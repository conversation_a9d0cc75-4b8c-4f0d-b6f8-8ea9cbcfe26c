# Maven构建输出
target/
*/target/
**/target/

# 编译文件
*.class
*.jar
*.war
*.ear

# 日志文件
*.log
logs/
*/logs/
**/logs/

# IDE文件
.idea/
*.iml
*.ipr
*.iws
.vscode/
.cursor/

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# Maven wrapper
.mvn/
mvnw
mvnw.cmd

# 依赖缓存
.m2/

# SonarQube
.sonar/
.scannerwork/

# 测试报告
surefire-reports/
*/surefire-reports/
**/surefire-reports/

# JaCoCo
*.exec

# Spring Boot
*.orig

# 数据库文件
*.db
*.sqlite

# 环境变量文件
.env
.env.local
.env.*.local

# 备份文件
*.bak
*.backup 

# 其他
gemini-plugins/02-plugins-biz
gemini-plugins/03-plugins-project
gemini-plugins/05-plugins-product/g03-cloud-huawei
gemini-plugins/05-plugins-product/g04-cloud-aws
gemini-plugins/05-plugins-product/s01-cloud-cloudos-e5
gemini-plugins/05-plugins-product/s01-cloud-cloudos-e7
gemini-plugins/05-plugins-product/s01-cloud-cloudos-e7111
gemini-plugins/05-plugins-product/s02-cloud-ziguang-e7
gemini-plugins/05-plugins-product/s03-cloud-scp-68
gemini-plugins/05-plugins-product/s03-cloud-scp-610
gemini-plugins/05-plugins-product/s04-cloud-openstack-d
gemini-plugins/05-plugins-product/s06-cloud-UCloud
gemini-plugins/05-plugins-product/v01-cloud-vmware
gemini-plugins/05-plugins-product/v02-cloud-cas-e7
gemini-plugins/05-plugins-product/v03-cloud-fusionCompute-v8.0
gemini-plugins/05-plugins-product/v03-cloud-fusionCompute-v8.5

