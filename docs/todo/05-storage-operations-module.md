# 存储操作模块开发任务

存储操作模块负责实现对云平台存储资源的管理，包括磁盘、快照和镜像等。本文档详细说明了开发云插件存储操作模块的具体任务。

## 1. 存储服务类开发

### 1.1 StorageService 类开发

- 创建 `StorageService` 类，封装存储操作：
  - 定义存储操作的统一接口
  - 实现统一的请求参数处理
  - 实现统一的响应处理
  - 实现错误处理和日志记录

```java
@Slf4j
public class StorageService {
    
    // 通用操作方法
    private static BaseResponse executeStorageOperation(JSONObject params, Function<CloudClient, JSONObject> operation) {
        try {
            String accountId = params.getString("accountId");
            
            // 获取客户端
            CloudClient client = ClientFactory.getClient(accountId);
            
            // 执行操作
            JSONObject result = operation.apply(client);
            
            return BaseResponse.SUCCESS.of(result);
        } catch (Exception e) {
            log.error("存储操作失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("存储操作失败: " + e.getMessage());
        }
    }
}
```

## 2. 磁盘管理操作实现

### 2.1 获取磁盘列表

- 实现获取磁盘列表功能：
  - 支持分页和过滤
  - 支持按状态、类型等条件筛选
  - 统一格式化返回数据

```java
public static BaseResponse listDisks(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        int pageSize = params.getIntValue("pageSize", 50);
        int pageNum = params.getIntValue("pageNum", 1);
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("limit", String.valueOf(pageSize));
        queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
        
        // 添加筛选条件
        if (params.containsKey("status")) {
            queryParams.put("status", params.getString("status"));
        }
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject result = client.listDisks(regionId, queryParams);
        
        // 转换数据格式
        List<JSONObject> disks = convertDisks(result);
        
        JSONObject response = new JSONObject();
        response.put("list", disks);
        response.put("total", result.getIntValue("total"));
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("获取磁盘列表失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取磁盘列表失败: " + e.getMessage());
    }
}
```

### 2.2 获取磁盘详情

- 实现获取磁盘详情功能：
  - 获取单个磁盘的详细信息
  - 获取磁盘的挂载状态
  - 获取磁盘的性能参数

```java
public static BaseResponse getDisk(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String diskId = params.getString("diskId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject disk = client.getDisk(regionId, diskId);
        
        // 转换数据格式
        JSONObject formattedDisk = convertDiskDetail(disk);
        
        return BaseResponse.SUCCESS.of(formattedDisk);
    } catch (Exception e) {
        log.error("获取磁盘详情失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取磁盘详情失败: " + e.getMessage());
    }
}
```

### 2.3 创建磁盘

- 实现创建磁盘功能：
  - 验证创建参数
  - 支持不同的磁盘类型
  - 调用云平台API创建磁盘

```java
public static BaseResponse createDisk(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String name = params.getString("name");
        int sizeGB = params.getIntValue("sizeGB");
        String diskType = params.getString("diskType", "SSD"); // 默认SSD类型
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建创建参数
        JSONObject createParams = new JSONObject();
        createParams.put("name", name);
        createParams.put("size", sizeGB);
        createParams.put("volume_type", diskType);
        
        // 调用API创建磁盘
        JSONObject result = client.createDisk(regionId, createParams);
        
        // 解析返回结果
        String diskId = result.getString("id");
        
        JSONObject response = new JSONObject();
        response.put("diskId", diskId);
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("创建磁盘失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建磁盘失败: " + e.getMessage());
    }
}
```

### 2.4 挂载磁盘

- 实现挂载磁盘功能：
  - 验证磁盘和虚拟机状态
  - 设置挂载参数（设备名等）
  - 调用云平台API挂载磁盘

```java
public static BaseResponse attachDisk(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String diskId = params.getString("diskId");
        String instanceId = params.getString("instanceId");
        String device = params.getString("device", null); // 可选参数
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建挂载参数
        JSONObject attachParams = new JSONObject();
        attachParams.put("volumeId", diskId);
        if (device != null) {
            attachParams.put("device", device);
        }
        
        // 调用API挂载磁盘
        client.attachDisk(regionId, instanceId, attachParams);
        
        return BaseResponse.SUCCESS.of("磁盘挂载请求已提交");
    } catch (Exception e) {
        log.error("挂载磁盘失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("挂载磁盘失败: " + e.getMessage());
    }
}
```

### 2.5 卸载磁盘

- 实现卸载磁盘功能：
  - 验证磁盘挂载状态
  - 调用云平台API卸载磁盘

```java
public static BaseResponse detachDisk(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String diskId = params.getString("diskId");
        String instanceId = params.getString("instanceId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 调用API卸载磁盘
        client.detachDisk(regionId, instanceId, diskId);
        
        return BaseResponse.SUCCESS.of("磁盘卸载请求已提交");
    } catch (Exception e) {
        log.error("卸载磁盘失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("卸载磁盘失败: " + e.getMessage());
    }
}
```

### 2.6 删除磁盘

- 实现删除磁盘功能：
  - 验证磁盘状态（确保未挂载）
  - 调用云平台API删除磁盘

```java
public static BaseResponse deleteDisk(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String diskId = params.getString("diskId");
        boolean force = params.getBooleanValue("force", false);
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 调用API删除磁盘
        client.deleteDisk(regionId, diskId, force);
        
        return BaseResponse.SUCCESS.of("磁盘删除请求已提交");
    } catch (Exception e) {
        log.error("删除磁盘失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("删除磁盘失败: " + e.getMessage());
    }
}
```

## 3. 快照管理操作实现

### 3.1 创建快照

- 实现创建磁盘快照功能：
  - 验证磁盘状态
  - 调用云平台API创建快照

```java
public static BaseResponse createSnapshot(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String diskId = params.getString("diskId");
        String name = params.getString("name");
        String description = params.getString("description", "");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建创建参数
        JSONObject createParams = new JSONObject();
        createParams.put("name", name);
        createParams.put("description", description);
        createParams.put("volume_id", diskId);
        
        // 调用API创建快照
        JSONObject result = client.createSnapshot(regionId, createParams);
        
        // 解析返回结果
        String snapshotId = result.getString("id");
        
        JSONObject response = new JSONObject();
        response.put("snapshotId", snapshotId);
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("创建快照失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建快照失败: " + e.getMessage());
    }
}
```

### 3.2 获取快照列表

- 实现获取快照列表功能：
  - 支持分页和过滤
  - 支持按磁盘ID筛选
  - 统一格式化返回数据

```java
public static BaseResponse listSnapshots(JSONObject params) {
    // 实现获取快照列表
}
```

### 3.3 删除快照

- 实现删除快照功能：
  - 验证快照状态
  - 调用云平台API删除快照

```java
public static BaseResponse deleteSnapshot(JSONObject params) {
    // 实现删除快照
}
```

### 3.4 从快照创建磁盘

- 实现从快照创建磁盘功能：
  - 验证快照状态
  - 调用云平台API从快照创建磁盘

```java
public static BaseResponse createDiskFromSnapshot(JSONObject params) {
    // 实现从快照创建磁盘
}
```

## 4. 镜像管理操作实现

### 4.1 获取镜像列表

- 实现获取镜像列表功能：
  - 支持分页和过滤
  - 支持按可见性、状态等条件筛选
  - 统一格式化返回数据

```java
public static BaseResponse listImages(JSONObject params) {
    // 实现获取镜像列表
}
```

### 4.2 获取镜像详情

- 实现获取镜像详情功能：
  - 获取单个镜像的详细信息
  - 获取镜像的配置参数

```java
public static BaseResponse getImage(JSONObject params) {
    // 实现获取镜像详情
}
```

### 4.3 创建自定义镜像

- 实现创建自定义镜像功能：
  - 从虚拟机创建镜像
  - 设置镜像参数和描述
  - 调用云平台API创建镜像

```java
public static BaseResponse createImage(JSONObject params) {
    // 实现创建自定义镜像
}
```

### 4.4 删除自定义镜像

- 实现删除自定义镜像功能：
  - 验证镜像状态
  - 调用云平台API删除镜像

```java
public static BaseResponse deleteImage(JSONObject params) {
    // 实现删除自定义镜像
}
```

## 任务验收标准

1. 能成功创建、挂载、卸载和删除磁盘
2. 能成功创建和管理快照
3. 能成功获取和管理镜像
4. 能正确处理存储资源之间的关联关系
5. 错误处理完善，能提供有意义的错误信息

## 相关文件

- `StorageService.java`
- `DiskOperations.java`
- `SnapshotOperations.java`
- `ImageOperations.java` 