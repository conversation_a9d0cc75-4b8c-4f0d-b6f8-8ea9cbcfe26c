# 网络操作模块开发任务

网络操作模块负责实现对云平台网络资源的管理，包括VPC、子网、安全组和弹性IP等。本文档详细说明了开发云插件网络操作模块的具体任务。

## 1. 网络服务类开发

### 1.1 NetworkService 类开发

- 创建 `NetworkService` 类，封装网络操作：
  - 定义网络操作的统一接口
  - 实现统一的请求参数处理
  - 实现统一的响应处理
  - 实现错误处理和日志记录

```java
@Slf4j
public class NetworkService {
    
    // 通用操作方法
    private static BaseResponse executeNetworkOperation(JSONObject params, Function<CloudClient, JSONObject> operation) {
        try {
            String accountId = params.getString("accountId");
            
            // 获取客户端
            CloudClient client = ClientFactory.getClient(accountId);
            
            // 执行操作
            JSONObject result = operation.apply(client);
            
            return BaseResponse.SUCCESS.of(result);
        } catch (Exception e) {
            log.error("网络操作失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("网络操作失败: " + e.getMessage());
        }
    }
}
```

## 2. VPC管理操作实现

### 2.1 获取VPC列表

- 实现获取VPC列表功能：
  - 支持分页和过滤
  - 支持按状态、名称等条件筛选
  - 统一格式化返回数据

```java
public static BaseResponse listVpcs(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        int pageSize = params.getIntValue("pageSize", 50);
        int pageNum = params.getIntValue("pageNum", 1);
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("limit", String.valueOf(pageSize));
        queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
        
        // 添加筛选条件
        if (params.containsKey("status")) {
            queryParams.put("status", params.getString("status"));
        }
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject result = client.listVpcs(regionId, queryParams);
        
        // 转换数据格式
        List<JSONObject> vpcs = convertVpcs(result);
        
        JSONObject response = new JSONObject();
        response.put("list", vpcs);
        response.put("total", result.getIntValue("total"));
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("获取VPC列表失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取VPC列表失败: " + e.getMessage());
    }
}
```

### 2.2 创建VPC

- 实现创建VPC功能：
  - 设置VPC名称、CIDR等参数
  - 调用云平台API创建VPC
  - 处理创建过程中的异常

```java
public static BaseResponse createVpc(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String name = params.getString("name");
        String cidr = params.getString("cidr");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建创建参数
        JSONObject createParams = new JSONObject();
        createParams.put("name", name);
        createParams.put("cidr", cidr);
        
        // 调用API创建VPC
        JSONObject result = client.createVpc(regionId, createParams);
        
        // 解析返回结果
        String vpcId = result.getString("id");
        
        JSONObject response = new JSONObject();
        response.put("vpcId", vpcId);
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("创建VPC失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建VPC失败: " + e.getMessage());
    }
}
```

### 2.3 删除VPC

- 实现删除VPC功能：
  - 验证VPC状态（确保没有关联资源）
  - 调用云平台API删除VPC

```java
public static BaseResponse deleteVpc(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String vpcId = params.getString("vpcId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 调用API删除VPC
        client.deleteVpc(regionId, vpcId);
        
        return BaseResponse.SUCCESS.of("VPC删除请求已提交");
    } catch (Exception e) {
        log.error("删除VPC失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("删除VPC失败: " + e.getMessage());
    }
}
```

## 3. 子网管理操作实现

### 3.1 获取子网列表

- 实现获取子网列表功能：
  - 支持分页和过滤
  - 支持按VPC ID筛选
  - 统一格式化返回数据

```java
public static BaseResponse listSubnets(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        int pageSize = params.getIntValue("pageSize", 50);
        int pageNum = params.getIntValue("pageNum", 1);
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("limit", String.valueOf(pageSize));
        queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
        
        // 添加筛选条件
        if (params.containsKey("vpcId")) {
            queryParams.put("vpc_id", params.getString("vpcId"));
        }
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject result = client.listSubnets(regionId, queryParams);
        
        // 转换数据格式
        List<JSONObject> subnets = convertSubnets(result);
        
        JSONObject response = new JSONObject();
        response.put("list", subnets);
        response.put("total", result.getIntValue("total"));
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("获取子网列表失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取子网列表失败: " + e.getMessage());
    }
}
```

### 3.2 创建子网

- 实现创建子网功能：
  - 验证VPC ID
  - 设置子网名称、CIDR等参数
  - 调用云平台API创建子网

```java
public static BaseResponse createSubnet(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String vpcId = params.getString("vpcId");
        String name = params.getString("name");
        String cidr = params.getString("cidr");
        String gatewayIp = params.getString("gatewayIp");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建创建参数
        JSONObject createParams = new JSONObject();
        createParams.put("name", name);
        createParams.put("cidr", cidr);
        createParams.put("vpc_id", vpcId);
        createParams.put("gateway_ip", gatewayIp);
        
        // 调用API创建子网
        JSONObject result = client.createSubnet(regionId, createParams);
        
        // 解析返回结果
        String subnetId = result.getString("id");
        
        JSONObject response = new JSONObject();
        response.put("subnetId", subnetId);
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("创建子网失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建子网失败: " + e.getMessage());
    }
}
```

### 3.3 删除子网

- 实现删除子网功能：
  - 验证子网状态（确保没有关联资源）
  - 调用云平台API删除子网

```java
public static BaseResponse deleteSubnet(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String subnetId = params.getString("subnetId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 调用API删除子网
        client.deleteSubnet(regionId, subnetId);
        
        return BaseResponse.SUCCESS.of("子网删除请求已提交");
    } catch (Exception e) {
        log.error("删除子网失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("删除子网失败: " + e.getMessage());
    }
}
```

## 4. 安全组管理操作实现

### 4.1 获取安全组列表

- 实现获取安全组列表功能：
  - 支持分页和过滤
  - 支持按VPC ID筛选
  - 统一格式化返回数据

```java
public static BaseResponse listSecurityGroups(JSONObject params) {
    // 实现获取安全组列表
}
```

### 4.2 创建安全组

- 实现创建安全组功能：
  - 设置安全组名称、描述等参数
  - 调用云平台API创建安全组

```java
public static BaseResponse createSecurityGroup(JSONObject params) {
    // 实现创建安全组
}
```

### 4.3 安全组规则管理

- 实现安全组规则管理功能：
  - 添加安全组规则
  - 删除安全组规则
  - 获取安全组规则列表

```java
public static BaseResponse addSecurityGroupRule(JSONObject params) {
    // 实现添加安全组规则
}

public static BaseResponse deleteSecurityGroupRule(JSONObject params) {
    // 实现删除安全组规则
}
```

### 4.4 删除安全组

- 实现删除安全组功能：
  - 验证安全组状态（确保没有关联资源）
  - 调用云平台API删除安全组

```java
public static BaseResponse deleteSecurityGroup(JSONObject params) {
    // 实现删除安全组
}
```

## 5. 弹性IP管理操作实现

### 5.1 获取弹性IP列表

- 实现获取弹性IP列表功能：
  - 支持分页和过滤
  - 支持按状态筛选
  - 统一格式化返回数据

```java
public static BaseResponse listEips(JSONObject params) {
    // 实现获取弹性IP列表
}
```

### 5.2 创建弹性IP

- 实现创建弹性IP功能：
  - 设置带宽等参数
  - 调用云平台API创建弹性IP

```java
public static BaseResponse createEip(JSONObject params) {
    // 实现创建弹性IP
}
```

### 5.3 绑定弹性IP

- 实现绑定弹性IP功能：
  - 验证弹性IP和实例状态
  - 调用云平台API绑定弹性IP到实例

```java
public static BaseResponse bindEip(JSONObject params) {
    // 实现绑定弹性IP
}
```

### 5.4 解绑弹性IP

- 实现解绑弹性IP功能：
  - 验证绑定状态
  - 调用云平台API解绑弹性IP

```java
public static BaseResponse unbindEip(JSONObject params) {
    // 实现解绑弹性IP
}
```

### 5.5 释放弹性IP

- 实现释放弹性IP功能：
  - 验证弹性IP状态（确保未绑定）
  - 调用云平台API释放弹性IP

```java
public static BaseResponse releaseEip(JSONObject params) {
    // 实现释放弹性IP
}
```

## 任务验收标准

1. 能成功创建和管理VPC
2. 能成功创建和管理子网
3. 能成功创建和管理安全组及其规则
4. 能成功创建、绑定、解绑和释放弹性IP
5. 能正确处理网络资源之间的关联关系
6. 错误处理完善，能提供有意义的错误信息

## 相关文件

- `NetworkService.java`
- `VpcOperations.java`
- `SubnetOperations.java`
- `SecurityGroupOperations.java`
- `EipOperations.java` 