# 资源同步模块开发任务

资源同步模块负责从云平台获取各类资源信息并同步到系统中。本文档详细说明了开发云插件资源同步模块的具体任务。

## 1. 同步服务框架开发

### 1.1 FetchService 类开发

- 创建 `FetchService` 类，实现以下功能：
  - 通用同步方法封装
  - 分页处理
  - 数据格式转换
  - 同步结果处理

```java
public class FetchService {
    // 同步资源的通用方法
    protected static BaseResponse fetchResources(String accountId, String action, 
                                                Function<CloudClient, JSONObject> apiCall,
                                                Function<JSONObject, List<JSONObject>> converter) {
        try {
            // 获取客户端
            CloudClient client = ClientFactory.getClient(accountId);
            
            // 调用API获取资源
            JSONObject result = apiCall.apply(client);
            
            // 转换数据格式
            List<JSONObject> resources = converter.apply(result);
            
            // 返回结果
            JSONObject response = new JSONObject();
            response.put("list", resources);
            response.put("total", resources.size());
            return BaseResponse.SUCCESS.of(response);
        } catch (Exception e) {
            log.error("同步资源失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("同步资源失败: " + e.getMessage());
        }
    }
}
```

### 1.2 数据转换工具类开发

- 创建 `DataConverter` 类，用于标准化资源数据：
  - 资源状态标准化
  - 资源类型标准化
  - 属性名称标准化
  - 单位转换

```java
public class DataConverter {
    // 状态标准化映射
    public static final Map<String, String> STATUS_MAP = new HashMap<>();
    static {
        STATUS_MAP.put("ACTIVE", "Running");
        STATUS_MAP.put("STOPPED", "Stopped");
        // 其他状态映射...
    }
    
    // 状态转换方法
    public static String convertStatus(String originalStatus) {
        return STATUS_MAP.getOrDefault(originalStatus.toUpperCase(), "Unknown");
    }
    
    // 单位转换方法
    public static long convertMemoryToMB(String memory) {
        // 实现单位转换逻辑
    }
}
```

## 2. 虚拟机资源同步开发

### 2.1 虚拟机列表同步

- 实现虚拟机列表同步功能：
  - 调用云平台API获取虚拟机列表
  - 支持分页查询
  - 支持过滤条件
  - 数据格式标准化转换

```java
public static BaseResponse fetchVm(JSONObject params) {
    String accountId = params.getString("accountId");
    int pageSize = params.getIntValue("pageSize", 50);
    int pageNum = params.getIntValue("pageNum", 1);
    
    // 构建API调用参数
    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("limit", String.valueOf(pageSize));
    queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
    
    // 调用通用同步方法
    return fetchResources(accountId, "FetchComputeInstance", 
        client -> client.listInstances(queryParams),
        result -> convertInstances(result)
    );
}

// 虚拟机数据转换
private static List<JSONObject> convertInstances(JSONObject result) {
    List<JSONObject> instances = new ArrayList<>();
    JSONArray items = result.getJSONArray("servers"); // 根据云平台API响应调整
    
    for (int i = 0; i < items.size(); i++) {
        JSONObject item = items.getJSONObject(i);
        JSONObject instance = new JSONObject();
        
        // 标准化字段
        instance.put("id", item.getString("id"));
        instance.put("name", item.getString("name"));
        instance.put("status", DataConverter.convertStatus(item.getString("status")));
        instance.put("createTime", item.getString("created"));
        instance.put("flavor", getFlavorInfo(item));
        instance.put("image", getImageInfo(item));
        instance.put("networks", getNetworkInfo(item));
        
        instances.add(instance);
    }
    
    return instances;
}
```

### 2.2 虚拟机详情同步

- 实现虚拟机详情同步功能：
  - 获取单个虚拟机的详细信息
  - 包含更全面的配置数据
  - 获取关联资源信息

```java
public static BaseResponse fetchVmDetail(JSONObject params) {
    // 虚拟机详情同步实现
}
```

## 3. 存储资源同步开发

### 3.1 磁盘资源同步

- 实现磁盘资源同步功能：
  - 获取磁盘列表
  - 标准化磁盘数据
  - 关联虚拟机信息

```java
public static BaseResponse fetchDisks(JSONObject params) {
    // 磁盘列表同步实现
}

private static List<JSONObject> convertDisks(JSONObject result) {
    // 磁盘数据转换
}
```

### 3.2 镜像资源同步

- 实现镜像资源同步功能：
  - 获取可用镜像列表
  - 标准化镜像数据

```java
public static BaseResponse fetchImages(JSONObject params) {
    // 镜像列表同步实现
}
```

## 4. 网络资源同步开发

### 4.1 VPC同步

- 实现VPC资源同步功能：
  - 获取VPC列表
  - 标准化VPC数据

```java
public static BaseResponse fetchVpcs(JSONObject params) {
    // VPC列表同步实现
}
```

### 4.2 子网同步

- 实现子网资源同步功能：
  - 获取子网列表
  - 标准化子网数据
  - 关联VPC信息

```java
public static BaseResponse fetchSubnets(JSONObject params) {
    // 子网列表同步实现
}
```

### 4.3 安全组同步

- 实现安全组资源同步功能：
  - 获取安全组列表
  - 获取安全组规则
  - 标准化安全组数据

```java
public static BaseResponse fetchSecurityGroups(JSONObject params) {
    // 安全组列表同步实现
}
```

## 5. 其他资源同步开发

### 5.1 账户配额同步

- 实现账户配额同步功能：
  - 获取账户资源限额
  - 获取资源使用情况

```java
public static BaseResponse fetchQuotas(JSONObject params) {
    // 配额同步实现
}
```

### 5.2 可用区同步

- 实现可用区同步功能：
  - 获取可用区列表
  - 获取可用区状态

```java
public static BaseResponse fetchAvailabilityZones(JSONObject params) {
    // 可用区同步实现
}
```

## 任务验收标准

1. 成功获取并转换云平台各类资源数据
2. 数据格式符合系统标准
3. 支持分页查询
4. 能够处理异常情况（如API限流、网络故障等）
5. 日志记录完善，便于问题排查

## 相关文件

- `FetchService.java`
- `DataConverter.java`
- `VmSyncService.java`
- `DiskSyncService.java`
- `NetworkSyncService.java`
- `QuotaSyncService.java` 