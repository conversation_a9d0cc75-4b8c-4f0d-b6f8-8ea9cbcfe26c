# 虚拟机操作模块开发任务

虚拟机操作模块负责实现对云平台虚拟机资源的各种操作，包括创建、启动、停止、重启和删除等。本文档详细说明了开发云插件虚拟机操作模块的具体任务。

## 1. 虚拟机服务类开发

### 1.1 ComputeService 类开发

- 创建 `ComputeService` 类，封装虚拟机操作：
  - 定义虚拟机操作的统一接口
  - 实现统一的请求参数处理
  - 实现统一的响应处理
  - 实现错误处理和日志记录

```java
@Slf4j
public class ComputeService {
    
    // 通用操作方法
    private static BaseResponse executeVmOperation(JSONObject params, Function<CloudClient, JSONObject> operation) {
        try {
            String accountId = params.getString("accountId");
            String instanceId = params.getString("instanceId");
            
            // 获取客户端
            CloudClient client = ClientFactory.getClient(accountId);
            
            // 执行操作
            operation.apply(client);
            
            return BaseResponse.SUCCESS.of("操作成功");
        } catch (Exception e) {
            log.error("虚拟机操作失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("操作失败: " + e.getMessage());
        }
    }
}
```

## 2. 基本虚拟机操作实现

### 2.1 获取虚拟机列表

- 实现获取虚拟机列表功能：
  - 支持分页和过滤
  - 支持按状态、名称等条件筛选
  - 统一格式化返回数据

```java
public static BaseResponse listInstances(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        int pageSize = params.getIntValue("pageSize", 50);
        int pageNum = params.getIntValue("pageNum", 1);
        
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("limit", String.valueOf(pageSize));
        queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
        
        // 添加筛选条件
        if (params.containsKey("status")) {
            queryParams.put("status", params.getString("status"));
        }
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject result = client.listInstances(regionId, queryParams);
        
        // 转换数据格式
        List<JSONObject> instances = convertInstances(result);
        
        JSONObject response = new JSONObject();
        response.put("list", instances);
        response.put("total", result.getIntValue("total"));
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("获取虚拟机列表失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机列表失败: " + e.getMessage());
    }
}
```

### 2.2 获取虚拟机详情

- 实现获取虚拟机详情功能：
  - 获取单个虚拟机的详细配置
  - 获取虚拟机的状态信息
  - 获取虚拟机的网络配置
  - 获取虚拟机的存储配置

```java
public static BaseResponse getInstance(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject instance = client.getInstance(regionId, instanceId);
        
        // 转换数据格式
        JSONObject formattedInstance = convertInstanceDetail(instance);
        
        return BaseResponse.SUCCESS.of(formattedInstance);
    } catch (Exception e) {
        log.error("获取虚拟机详情失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机详情失败: " + e.getMessage());
    }
}
```

### 2.3 启动虚拟机

- 实现启动虚拟机功能：
  - 验证虚拟机状态
  - 调用云平台API启动虚拟机
  - 处理启动过程中的异常

```java
public static BaseResponse startInstance(JSONObject params) {
    return executeVmOperation(params, client -> {
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        return client.startInstance(regionId, instanceId);
    });
}
```

### 2.4 停止虚拟机

- 实现停止虚拟机功能：
  - 验证虚拟机状态
  - 支持强制停止选项
  - 调用云平台API停止虚拟机

```java
public static BaseResponse stopInstance(JSONObject params) {
    return executeVmOperation(params, client -> {
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        boolean forceStop = params.getBooleanValue("forceStop", false);
        return client.stopInstance(regionId, instanceId, forceStop);
    });
}
```

### 2.5 重启虚拟机

- 实现重启虚拟机功能：
  - 验证虚拟机状态
  - 支持软重启和硬重启
  - 调用云平台API重启虚拟机

```java
public static BaseResponse rebootInstance(JSONObject params) {
    return executeVmOperation(params, client -> {
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        boolean hardReboot = params.getBooleanValue("hardReboot", false);
        return client.rebootInstance(regionId, instanceId, hardReboot);
    });
}
```

### 2.6 删除虚拟机

- 实现删除虚拟机功能：
  - 验证虚拟机状态
  - 支持强制删除选项
  - 调用云平台API删除虚拟机

```java
public static BaseResponse deleteInstance(JSONObject params) {
    return executeVmOperation(params, client -> {
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        boolean forceDelete = params.getBooleanValue("forceDelete", false);
        return client.deleteInstance(regionId, instanceId, forceDelete);
    });
}
```

## 3. 高级虚拟机操作实现

### 3.1 创建虚拟机

- 实现创建虚拟机功能：
  - 验证创建参数
  - 获取镜像、规格、网络等资源
  - 调用云平台API创建虚拟机
  - 处理创建过程中的异常

```java
public static BaseResponse createInstance(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        
        // 解析创建参数
        String name = params.getString("name");
        String imageId = params.getString("imageId");
        String flavorId = params.getString("flavorId");
        String networkId = params.getString("networkId");
        String securityGroupId = params.getString("securityGroupId");
        
        // 获取客户端
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 构建创建参数
        JSONObject createParams = new JSONObject();
        createParams.put("name", name);
        createParams.put("imageRef", imageId);
        createParams.put("flavorRef", flavorId);
        
        JSONObject network = new JSONObject();
        network.put("uuid", networkId);
        createParams.put("networks", Collections.singletonList(network));
        
        if (securityGroupId != null) {
            createParams.put("security_groups", Collections.singletonList(
                new JSONObject().fluentPut("name", securityGroupId)));
        }
        
        // 调用API创建虚拟机
        JSONObject result = client.createInstance(regionId, createParams);
        
        // 解析返回结果
        String instanceId = result.getJSONObject("server").getString("id");
        
        JSONObject response = new JSONObject();
        response.put("instanceId", instanceId);
        
        return BaseResponse.SUCCESS.of(response);
    } catch (Exception e) {
        log.error("创建虚拟机失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建虚拟机失败: " + e.getMessage());
    }
}
```

### 3.2 调整虚拟机规格

- 实现调整虚拟机规格功能：
  - 验证虚拟机状态
  - 验证目标规格
  - 调用云平台API调整规格

```java
public static BaseResponse resizeInstance(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        String flavorId = params.getString("flavorId");
        
        CloudClient client = ClientFactory.getClient(accountId);
        
        // 调用API调整规格
        client.resizeInstance(regionId, instanceId, flavorId);
        
        return BaseResponse.SUCCESS.of("虚拟机规格调整请求已提交");
    } catch (Exception e) {
        log.error("调整虚拟机规格失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("调整虚拟机规格失败: " + e.getMessage());
    }
}
```

### 3.3 虚拟机快照管理

- 实现虚拟机快照功能：
  - 创建虚拟机快照
  - 列出虚拟机快照
  - 从快照恢复虚拟机
  - 删除虚拟机快照

```java
public static BaseResponse createSnapshot(JSONObject params) {
    // 实现创建快照功能
}

public static BaseResponse listSnapshots(JSONObject params) {
    // 实现列出快照功能
}

public static BaseResponse restoreSnapshot(JSONObject params) {
    // 实现从快照恢复功能
}

public static BaseResponse deleteSnapshot(JSONObject params) {
    // 实现删除快照功能
}
```

## 4. 虚拟机监控数据采集

### 4.1 CPU使用率采集

- 实现CPU使用率采集功能：
  - 调用云平台监控API获取CPU使用率
  - 数据格式标准化
  - 支持时间范围和粒度参数

```java
public static BaseResponse getCpuUsage(JSONObject params) {
    try {
        String accountId = params.getString("accountId");
        String regionId = params.getString("regionId");
        String instanceId = params.getString("instanceId");
        String startTime = params.getString("startTime");
        String endTime = params.getString("endTime");
        String period = params.getString("period", "300"); // 默认300秒
        
        CloudClient client = ClientFactory.getClient(accountId);
        JSONObject result = client.getInstanceMetric(regionId, instanceId, "cpu_utilization", startTime, endTime, period);
        
        // 转换数据格式
        JSONObject formattedData = formatMetricData(result);
        
        return BaseResponse.SUCCESS.of(formattedData);
    } catch (Exception e) {
        log.error("获取CPU使用率失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取CPU使用率失败: " + e.getMessage());
    }
}
```

### 4.2 内存使用率采集

- 实现内存使用率采集功能：
  - 调用云平台监控API获取内存使用率
  - 数据格式标准化

```java
public static BaseResponse getMemoryUsage(JSONObject params) {
    // 实现内存使用率采集
}
```

### 4.3 磁盘IO采集

- 实现磁盘IO采集功能：
  - 调用云平台监控API获取磁盘读写速率
  - 数据格式标准化

```java
public static BaseResponse getDiskIO(JSONObject params) {
    // 实现磁盘IO采集
}
```

### 4.4 网络IO采集

- 实现网络IO采集功能：
  - 调用云平台监控API获取网络进出流量
  - 数据格式标准化

```java
public static BaseResponse getNetworkIO(JSONObject params) {
    // 实现网络IO采集
}
```

## 任务验收标准

1. 能成功执行虚拟机的基本操作（启动、停止、重启、删除）
2. 能成功创建新的虚拟机
3. 能调整虚拟机规格并生效
4. 能获取虚拟机监控数据
5. 错误处理完善，能提供有意义的错误信息
6. 支持虚拟机快照功能（如云平台支持）

## 相关文件

- `ComputeService.java`
- `InstanceOperations.java`
- `SnapshotOperations.java`
- `MetricsCollector.java` 