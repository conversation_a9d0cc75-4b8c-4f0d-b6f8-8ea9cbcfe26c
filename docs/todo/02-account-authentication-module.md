# 账号和认证模块开发任务

账号和认证模块是云插件的基础，负责管理云平台的认证信息和账号验证。本文档详细说明了开发云插件账号和认证模块的具体任务。

## 1. 认证机制开发

### 1.1 连接配置类开发

- 创建 `ConnectionConfig` 类，包含以下功能：
  - 云平台连接信息（endpoint、域名、端口等）
  - 认证信息（AccessKey、SecretKey、用户名、密码等）
  - 连接超时、读取超时等配置
  - 代理设置（如需要）

```java
public class ConnectionConfig {
    private String endpoint;
    private AuthConfig authConfig;
    private int connectTimeout = 10000; // 默认10秒
    private int readTimeout = 30000; // 默认30秒
    // getter/setter...
}
```

### 1.2 认证配置类开发

- 创建 `AuthConfig` 类，根据云平台的认证要求设计：
  - 基础认证信息（用户名、密码）
  - 令牌认证信息（AccessKey、SecretKey）
  - 域、项目、租户等特殊认证参数

```java
public class AuthConfig {
    private String username;
    private String password;
    private String accessKey;
    private String secretKey;
    private String domain;
    private String projectId;
    // getter/setter...
}
```

### 1.3 Token管理实现

- 开发Token获取和缓存机制：
  - 实现Token请求逻辑
  - 使用Guava Cache等机制缓存Token
  - 处理Token过期和刷新

```java
public String getToken() {
    // 从缓存获取token
    Object token = GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, cacheKey);
    if (token == null) {
        // 调用API获取token
        token = doGetToken();
        // 存入缓存
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, cacheKey, token, 3600); // 缓存1小时
    }
    return token.toString();
}
```

## 2. API客户端开发

### 2.1 HTTP客户端封装

- 创建 HTTP 客户端类，支持：
  - 连接池管理
  - 请求重试机制
  - 错误处理
  - 日志记录
  - SSL配置

```java
public class HttpClient {
    private final CloseableHttpClient client;
    private final int maxRetries;
    
    // 创建HTTP客户端，配置SSL和超时
    // 实现重试机制
    // 实现请求方法（GET、POST、PUT、DELETE等）
}
```

### 2.2 API调用封装

- 创建云平台API调用类：
  - 封装认证逻辑
  - 实现请求构建
  - 处理响应解析
  - 错误转换

```java
public class CloudClient {
    private final HttpClient httpClient;
    private final ConnectionConfig config;
    
    // API方法封装
    // 错误处理和转换
}
```

## 3. 账号表单配置

### 3.1 表单配置文件创建

- 创建账号表单配置文件：
  - `resources/account/add_form_zh-CN.json`: 中文表单配置
  - `resources/account/add_form_en-US.json`: 英文表单配置
  - 确保字段名与云账号信息表字段一致

### 3.2 调度任务配置

- 创建调度任务配置文件：
  - `resources/account/add_dispatch.json`
  - 配置资源同步的调度任务

## 4. 账号验证接口实现

### 4.1 账号验证服务

- 创建 `PlatformAccountService` 类，实现账号验证功能：
  - 验证连接参数
  - 测试认证信息
  - 获取账号基本信息

```java
public static BaseResponse authAccount(JSONObject params) {
    try {
        // 解析连接参数
        // 创建连接配置
        // 测试API调用
        return BaseResponse.SUCCESS.of("认证成功");
    } catch (Exception e) {
        log.error("账号验证失败", e);
        return BaseResponse.FAIL_AUTH.of("认证失败: " + e.getMessage());
    }
}
```

### 4.2 账号表单获取接口

- 实现获取账号表单的接口：
  - 根据语言返回对应的表单配置
  - 处理表单字段的默认值

```java
public static BaseResponse getAccountAddForm(JSONObject params) {
    // 获取语言参数
    // 返回对应语言的表单配置
}
```

## 5. 区域信息管理

### 5.1 区域列表获取

- 实现获取云平台区域列表的功能：
  - 调用云平台API获取区域信息
  - 转换为标准格式

### 5.2 区域信息同步

- 实现区域信息同步功能：
  - 定时更新区域信息
  - 存储区域元数据

## 任务验收标准

1. 能成功验证云平台账号信息
2. 能获取并展示正确的表单配置
3. 能获取云平台的区域列表
4. Token缓存和刷新机制正常工作
5. 错误处理机制完善，能提供有意义的错误信息

## 相关文件

- `ConnectionConfig.java`
- `AuthConfig.java`
- `HttpClient.java`
- `CloudClient.java`
- `PlatformAccountService.java`
- `add_form_zh-CN.json`
- `add_form_en-US.json`
- `add_dispatch.json` 