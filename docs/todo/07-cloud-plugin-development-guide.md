# 云插件开发步骤指南

本文档提供了开发一个新的云插件的完整步骤指南，从项目创建到功能实现的每一步。

## 1. 环境准备

### 1.1 开发环境配置

- 安装JDK 8或以上版本
- 安装Maven 3.6或以上版本
- 安装Git客户端
- 准备开发IDE (如IntelliJ IDEA)
- 配置Maven镜像和仓库

### 1.2 获取源代码

- Clone项目代码库
```bash
git clone <repository-url>
git checkout dev-branch
```

### 1.3 导入项目

- 使用IDE导入Maven项目
- 确保所有依赖能够正确下载

## 2. 创建插件基础结构

### 2.1 创建插件模块

在`gemini-plugins/05-plugins-cloud`目录下，创建新的云插件模块：

```bash
# 示例：创建AWS云插件
mkdir -p g04-cloud-aws/src/main/java/com/futong/gemini/plugin/cloud/aws
mkdir -p g04-cloud-aws/src/main/resources/account
mkdir -p g04-cloud-aws/src/main/resources/META-INF/services
```

### 2.2 配置POM文件

创建`pom.xml`文件，添加必要的依赖和构建配置。

### 2.3 添加到父POM

在`gemini-plugins/05-plugins-cloud/pom.xml`中添加新模块。

### 2.4 创建SPI注册文件

创建`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`文件。

## 3. 实现插件基本类

### 3.1 创建插件主类

创建继承自`BaseCloudPluginTemplate`的插件主类，实现初始化方法和获取注册类的方法。

### 3.2 创建注册类

创建继承自`BaseCloudRegister`的注册类，实现各种功能的注册方法。

## 4. 实现连接配置和认证机制

### 4.1 创建连接配置类

创建包含连接信息、超时设置等参数的配置类。

### 4.2 创建认证配置类

创建用于存储认证信息（如AccessKey、SecretKey等）的配置类。

## 5. 实现API客户端

### 5.1 创建客户端类

创建与云平台交互的API客户端类，封装HTTP请求和响应处理。

### 5.2 创建客户端工厂类

创建用于管理和缓存云平台客户端实例的工厂类。

## 6. 实现账号服务

### 6.1 创建基础服务类

实现账号表单获取、调度任务创建等基础功能。

### 6.2 创建平台账号服务类

实现账号验证等与云平台账号相关的功能。

## 7. 配置表单文件

### 7.1 创建中文表单配置文件

创建`resources/account/add_form_zh-CN.json`文件，配置中文表单字段。

### 7.2 创建英文表单配置文件

创建`resources/account/add_form_en-US.json`文件，配置英文表单字段。

### 7.3 创建调度任务配置文件

创建`resources/account/add_dispatch.json`文件，配置资源同步调度任务。

## 8. 实现资源同步服务

### 8.1 创建FetchService类

实现区域、虚拟机、磁盘等资源的同步方法。

### 8.2 实现数据转换方法

实现将云平台特定格式数据转换为系统标准格式的方法。

## 9. 实现虚拟机操作服务

### 9.1 创建ComputeService类

实现虚拟机的启动、停止、重启、删除等操作。

### 9.2 实现虚拟机创建方法

实现创建虚拟机的方法，包括参数验证和处理。

## 10. 实现存储操作服务

### 10.1 创建StorageService类

实现磁盘的创建、挂载、卸载、删除等操作。

### 10.2 实现快照和镜像管理方法

实现快照的创建、删除等操作，以及镜像的管理方法。

## 11. 实现网络操作服务

### 11.1 创建NetworkService类

实现VPC、子网、安全组等网络资源的管理方法。

### 11.2 实现弹性IP管理方法

实现弹性IP的创建、绑定、解绑、释放等操作。

## 12. 构建和测试插件

### 12.1 构建插件

使用Maven构建插件JAR包：

```bash
cd gemini-plugins/05-plugins-cloud/g04-cloud-aws
mvn clean package
```

### 12.2 部署插件

将构建好的JAR包上传到系统中或放置到指定目录。

### 12.3 测试功能

测试账号验证、资源同步、虚拟机操作等功能。

## 13. 问题排查和调试

### 13.1 查看日志

监控插件日志以排查问题。

### 13.2 启用调试日志

根据需要调整日志级别以获取更详细的信息。

### 13.3 常见问题和解决方法

总结开发过程中可能遇到的常见问题和解决方案。

## 14. 插件发布

### 14.1 版本管理

遵循版本号规范，记录版本变更。

### 14.2 文档编写

编写插件使用文档和API参考文档。

### 14.3 提交代码和发布

将代码提交到代码库，并发布插件。 