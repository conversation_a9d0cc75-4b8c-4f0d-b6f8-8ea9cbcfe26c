# 双子星(Gemini)系统架构概览

## 1. 系统整体架构

双子星(Gemini)是一个基于插件机制的云资源管理系统，作为整个CMP(云管平台)系统中的核心基础服务组件。系统采用模块化设计，核心组件包括：

- **gemini-server**: 核心服务器模块，负责接收请求并调度插件
- **gemini-model**: 数据模型和ORM映射
- **gemini-sdk**: 提供开发SDK和工具类
- **gemini-plugins**: 插件集合，包含多种云平台接入和业务功能实现

系统采用插件化架构，通过动态加载机制实现不同云平台和业务功能的扩展。双子星不直接处理业务逻辑，而是作为统一的云服务接入层，为上层业务服务提供标准化的云资源操作接口。

### 1.1 系统定位与职责

双子星在整个CMP系统中的主要职责包括：

1. **统一接入层**：屏蔽各云厂商API差异，提供统一的接口规范
2. **资源同步**：定时从各云平台获取资源信息，通过MQ发送到其他服务进行持久化
3. **资源操作**：提供云资源（如虚拟机、存储、网络等）的标准化操作接口
4. **插件管理**：支持云平台插件的动态加载、更新和卸载

## 2. 主要模块

### 2.1 gemini-server

作为系统核心，负责：
- 请求处理与路由
- 插件管理与调度
- 权限认证
- 系统配置
- 插件的动态加载与卸载

gemini-server通过核心类`GeminiFramework`管理所有插件的生命周期，负责插件的加载、初始化、调用和卸载。

### 2.2 gemini-model

定义系统数据模型，包括：
- 实体类定义
- 数据映射
- 查询封装

### 2.3 gemini-sdk

为插件开发者提供公共SDK，包含：
- 插件接口定义（`PluginInterface`）
- 工具类库
- 通信协议
- 统一的错误处理机制

### 2.4 gemini-plugins

提供各类插件实现，按照功能分类：
- 01-plugin-sdk: 插件开发SDK
- 02-plugins-biz: 业务插件
- 03-plugins-project: 项目定制插件
- 04-plugins-product: 产品插件
- 05-plugins-cloud: 云平台接入插件，包括：
  - 公有云：阿里云、腾讯云、华为云、AWS等
  - 私有云：VMware、OpenStack等
  - 专有云：阿里专有云、华为专有云等

## 3. 插件架构

### 3.1 插件机制

系统通过以下机制实现插件的加载和管理：

1. **插件注册**: 通过Java的SPI(Service Provider Interface)机制实现插件注册
   - 在插件JAR包的`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`文件中定义实现类
   - 系统通过`ServiceLoader.load(PluginInterface.class)`加载所有实现

2. **动态加载**: 使用自定义`URLClassLoader`在运行时加载插件JAR包
   - 每个插件使用独立的`ClassLoader`加载
   - 支持热更新，无需重启服务即可更新插件

3. **插件隔离**: 每个插件有独立的类加载器，实现插件间隔离
   - 一个插件的异常不会影响其他插件的运行
   - 插件之间的依赖版本可以不同，避免冲突

4. **统一接口**: 所有插件实现统一的`PluginInterface`接口
   - 主要方法包括`init()`、`execute()`和`destroy()`
   - 通过JSON格式进行数据交换，保证灵活性

### 3.2 插件标识与路由

每个插件通过两个关键标识符唯一区分：

- **realm**: 表示云平台类型或业务领域，如`public_ali`代表阿里公有云
- **version**: 表示插件版本，通常对应云厂商的API版本

系统通过`realm:version`格式的key在插件映射表中查找并路由到相应的插件实例。

### 3.3 插件类型

系统支持多种类型插件：

1. **云平台插件**: 对接各种公有云、私有云平台，负责资源操作和数据同步
2. **业务插件**: 实现特定业务逻辑，如自动化运维、成本分析等
3. **项目插件**: 针对特定项目的定制功能
4. **产品插件**: 对接其他产品或服务

### 3.4 插件配置

插件配置主要包括：

- **账号配置文件**:
  - `add_dispatch.json`: 定义同步任务的配置
  - `add_form_zh-CN.json`/`add_form_en-US.json`: 定义添加云账号时的表单字段
- **SPI注册文件**:
  - `META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`: 定义插件实现类

## 4. 数据流

### 4.1 请求处理流程

1. 客户端请求 → gemini-server
2. gemini-server根据请求中的`realm`和`version`确定需要调用的插件
3. 如果插件未加载，动态加载相应插件
4. 调用插件的`execute()`方法处理请求
5. 插件处理业务逻辑，可能与外部云平台API交互
6. 返回处理结果给gemini-server
7. gemini-server响应客户端请求

### 4.2 数据同步流程

1. 定时任务或手动触发同步请求
2. gemini-server调用相应插件的同步方法
3. 插件从云平台获取资源数据
4. 插件将数据通过MQ发送到其他服务
5. 其他服务负责数据的持久化和处理

## 5. 部署架构

系统支持多种部署方式：

- **容器化部署**: 通过Docker容器进行部署，是推荐的方式
  - 服务打包为Docker镜像
  - 插件以JAR包形式存储在MinIO中
  - 通过管理界面加载插件

- **集群部署**: 通过配置中心和服务注册实现高可用
  - 多实例部署，负载均衡
  - 共享插件存储，保证一致性

- **单机部署**: 适合小规模使用和开发测试环境

### 5.1 插件部署流程

1. 开发并构建插件JAR包
2. 将JAR包上传到MinIO存储
3. 通过管理界面注册插件信息
4. 系统自动或手动触发插件加载
5. 加载成功后可立即使用，无需重启服务

## 6. 技术栈

- **后端**: Java, Spring Boot, MyBatis
- **数据库**: MySQL/PostgreSQL
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **服务发现**: Spring Cloud
- **存储**: MinIO (用于存储插件JAR包)
- **构建工具**: Maven

## 7. 安全架构

- **身份认证**: JWT令牌
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据加密**: 敏感配置使用Jasypt加密
- **安全通信**: HTTPS/TLS
- **插件隔离**: 通过独立ClassLoader实现插件间的隔离

## 8. 系统优势

- **统一接口**: 屏蔽各云厂商API差异，提供统一接口
- **灵活扩展**: 无需修改核心代码，即可支持新的云平台
- **动态加载**: 支持插件的热更新，无需重启服务
- **隔离机制**: 插件间相互隔离，提高系统稳定性
- **标准化操作**: 统一的资源操作模型，简化上层应用开发 