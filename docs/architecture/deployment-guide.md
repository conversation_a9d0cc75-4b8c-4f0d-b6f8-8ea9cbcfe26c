# 双子星(Gemini)系统部署指南

本文档提供了双子星(Gemini)系统的部署流程和配置说明。

## 1. 系统要求

### 1.1 硬件要求

- CPU: 4核及以上
- 内存: 8GB及以上
- 磁盘: 50GB及以上
- 网络: 千兆网络

### 1.2 软件要求

- Docker: 19.03及以上
- Docker Compose: 1.25及以上
- MinIO: 用于存储插件JAR包
- MySQL: 5.7及以上
- RabbitMQ: 3.8及以上
- Redis: 5.0及以上

## 2. 部署方式

双子星系统支持多种部署方式，推荐使用Docker容器化部署。

### 2.1 容器化部署

#### 2.1.1 准备Docker环境

确保服务器已安装Docker和Docker Compose。

#### 2.1.2 获取Docker镜像

```bash
docker pull futong/gemini-server:v4.0.4
```

#### 2.1.3 创建docker-compose.yml文件

```yaml
version: '3'
services:
  gemini-server:
    image: futong/gemini-server:v4.0.4
    container_name: gemini-server
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./config:/etc/ftcloud/public/database_config
      - ./logs:/var/log/ftcloud
      - ./plugins:/cloudJars
    environment:
      - JAVA_OPTS=-Xms1g -Xmx2g
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - minio

  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: always
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=ftcloud
      - MYSQL_DATABASE=gemini

  redis:
    image: redis:5.0
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data

  rabbitmq:
    image: rabbitmq:3.8-management
    container_name: rabbitmq
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq

  minio:
    image: minio/minio
    container_name: minio
    restart: always
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio-data:/data
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"

volumes:
  mysql-data:
  redis-data:
  rabbitmq-data:
  minio-data:
```

#### 2.1.4 创建配置文件

在`./config`目录下创建`database.properties`文件:

```properties
spring.datasource.url=***************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=ftcloud

spring.redis.host=redis
spring.redis.port=6379
spring.redis.database=0

spring.rabbitmq.host=rabbitmq
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest

minio.endpoint=http://minio:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.bucketName=plugins
```

#### 2.1.5 启动服务

```bash
docker-compose up -d
```

#### 2.1.6 初始化数据库

```bash
docker exec -i mysql mysql -uroot -pftcloud gemini < init.sql
```

### 2.2 集群部署

对于生产环境，推荐使用集群部署方式，以提高系统的可用性和性能。

#### 2.2.1 组件高可用

- MySQL: 主从复制或集群
- Redis: 哨兵模式或集群
- RabbitMQ: 集群
- MinIO: 分布式部署

#### 2.2.2 服务水平扩展

- 使用负载均衡器(如Nginx)分发请求
- 部署多个Gemini服务实例
- 配置服务发现和注册(如Eureka、Nacos)

## 3. 插件部署

### 3.1 插件构建

使用Maven构建插件JAR包:

```bash
mvn -pl gemini-plugins/05-plugins-cloud/cloud-provider-name -am clean package
```

### 3.2 插件上传

#### 3.2.1 通过管理界面上传

1. 登录双子星管理界面
2. 进入"插件管理"页面
3. 点击"上传插件"按钮
4. 选择插件JAR包并上传
5. 填写插件信息(名称、版本、realm、version)
6. 点击"保存"按钮

#### 3.2.2 直接上传到MinIO

1. 登录MinIO控制台(http://your-server:9001)
2. 进入plugins存储桶
3. 上传插件JAR包
4. 通过API注册插件信息

```bash
curl -X POST http://your-server:8080/api/plugins/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "阿里云插件",
    "version": "1.0",
    "realm": "aliyun",
    "pluginVersion": "1.0",
    "path": "plugins/cloud-aliyun-1.0.jar"
  }'
```

### 3.3 插件加载

#### 3.3.1 通过管理界面加载

1. 登录双子星管理界面
2. 进入"插件管理"页面
3. 找到要加载的插件
4. 点击"加载"按钮

#### 3.3.2 通过API加载

```bash
curl -X POST http://your-server:8080/api/plugins/load \
  -H "Content-Type: application/json" \
  -d '{
    "id": "plugin-id"
  }'
```

## 4. 系统配置

### 4.1 服务配置

编辑`bootstrap.yml`文件或环境变量配置服务参数:

```yaml
server:
  port: 8080

spring:
  application:
    name: gemini-server
  profiles:
    active: prod
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:localhost:8848}
      config:
        server-addr: ${NACOS_SERVER:localhost:8848}
        file-extension: yaml
```

### 4.2 日志配置

编辑`logback-spring.xml`文件配置日志:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOG_PATH" value="/var/log/ftcloud" />
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n" />

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gemini-server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gemini-server.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

## 5. 健康检查与监控

### 5.1 健康检查

可以通过以下API检查服务健康状态:

```bash
curl http://your-server:8080/actuator/health
```

正常响应:

```json
{
  "status": "UP"
}
```

### 5.2 监控配置

双子星系统支持Spring Boot Actuator监控，可以通过以下配置启用:

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

## 6. 故障排查

### 6.1 服务无法启动

- 检查配置文件是否正确
- 检查数据库连接是否正常
- 查看日志文件排查错误

### 6.2 插件加载失败

- 检查插件JAR包是否完整
- 确认插件实现了PluginInterface接口
- 检查SPI注册文件是否正确
- 查看日志文件中的详细错误信息

### 6.3 资源同步失败

- 检查云账号配置是否正确
- 确认网络连接是否正常
- 检查MQ服务是否正常
- 查看插件日志文件

## 7. 升级指南

### 7.1 服务升级

1. 备份配置文件和数据
2. 拉取新版本镜像
3. 停止旧版本服务
4. 启动新版本服务
5. 执行数据库升级脚本

### 7.2 插件升级

1. 构建新版本插件JAR包
2. 上传新版本插件
3. 卸载旧版本插件
4. 加载新版本插件 