# 双子星(Gemini)插件开发规范

本文档定义了双子星系统插件开发的标准和规范，所有插件开发人员必须遵循这些规范以确保插件的质量和一致性。

## 1. 插件开发通用规范

### 1.1 命名规范

#### 插件项目命名

插件项目应按照以下格式命名：

- 云平台插件: `gemini-plugins/05-plugins-cloud/[g|s|v]xx-cloud-{provider}[-{version}]`
  - g: 公有云，如阿里云、腾讯云
  - s: 专有云，如OpenStack、UCloud
  - v: 虚拟化平台，如VMware
  - provider: 云平台名称
  - version: 可选，云平台版本

- 业务插件: `gemini-plugins/02-plugins-biz/xx-biz-{name}`
- 项目插件: `gemini-plugins/03-plugins-project/xx-project-{name}`
- 产品插件: `gemini-plugins/04-plugins-product/xx-product-{name}`

#### 插件类命名

- 插件主类应以`Plugin`结尾，如`AliCloudPlugin`
- 客户端类应以`Client`结尾，如`AliCloudClient`
- 服务类应以`Service`结尾，如`VmService`
- 模型类应以实体名称命名，如`VirtualMachine`

### 1.2 包结构规范

插件应按照以下包结构组织代码：

```
com.futong.gemini.plugin.[type].[provider]
  |- client      # API客户端
  |- model       # 数据模型
  |- service     # 业务服务
  |- util        # 工具类
  |- [Provider]Plugin.java  # 插件主类
```

其中：
- type: 插件类型，如cloud、biz、project、product
- provider: 提供商或名称

### 1.3 配置文件规范

#### SPI注册文件

必须在`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`文件中注册插件实现类的全限定名：

```
com.futong.gemini.plugin.cloud.provider.ProviderPlugin
```

#### 账号配置文件

云平台插件必须提供以下配置文件：

- `add_dispatch.json`: 定义表单结构和分组
- `add_form_zh-CN.json`: 中文表单配置
- `add_form_en-US.json`: 英文表单配置

## 2. 插件实现规范

### 2.1 插件接口实现

所有插件必须实现`PluginInterface`接口，推荐继承相应的抽象类：

- 云平台插件: 继承`AbstractCloudPlugin`
- 业务插件: 继承`AbstractBizPlugin`
- 项目插件: 继承`AbstractProjectPlugin`
- 产品插件: 继承`AbstractProductPlugin`

```java
public class ProviderPlugin extends AbstractCloudPlugin<JSONObject> {
    
    @Override
    public void init(String key) {
        super.init(key);
        // 配置日志输出
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.plugin.cloud.provider", "/cloud/provider");
        log.info("插件{}初始化完成", key);
    }
    
    @Override
    public void destroy() {
        // 清理资源
        log.info("插件{}销毁", getKey());
    }
    
    @Override
    public BaseResponse execute(JSONObject params) {
        // 实现请求处理逻辑
    }
}
```

### 2.2 错误处理规范

所有插件必须使用统一的错误处理机制：

- 使用`BaseException`封装异常
- 使用`BaseResponse`返回统一格式的响应
- 必须记录详细的错误日志
- 不允许抛出未捕获的异常

```java
try {
    // 业务逻辑
} catch (IOException e) {
    log.error("网络请求失败", e);
    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("网络请求失败: " + e.getMessage()), e);
} catch (Exception e) {
    log.error("未知错误", e);
    throw new BaseException(BaseResponse.ERROR_SYS.of("系统错误: " + e.getMessage()), e);
}
```

常用的错误码：

- `BaseResponse.SUCCESS`: 操作成功
- `BaseResponse.FAIL_PARAM`: 参数错误
- `BaseResponse.FAIL_AUTH`: 认证失败
- `BaseResponse.FAIL_OP_CLOUD`: 云操作失败
- `BaseResponse.ERROR_SYS`: 系统错误

### 2.3 日志规范

插件必须实现完善的日志记录：

- 使用SLF4J API进行日志记录
- 在插件初始化时配置独立的日志文件
- 必须记录关键操作和错误信息
- 不得在日志中记录敏感信息（如密钥）

```java
// 配置日志
DynamicLoggerConfigurator.addLogger("com.futong.gemini.plugin.cloud.provider", "/cloud/provider");

// 日志记录
log.debug("准备调用API: {}", apiName);
log.info("成功创建资源: {}", resourceId);
log.warn("API响应超时，将重试: {}", apiName);
log.error("API调用失败", exception);
```

### 2.4 客户端实现规范

API客户端类应遵循以下规范：

- 封装所有API调用
- 实现认证、请求构建和响应解析
- 提供合理的超时设置
- 实现重试机制
- 使用连接池管理连接

```java
public class ProviderClient {
    
    private final String endpoint;
    private final String accessKey;
    private final String secretKey;
    private final int connectTimeout;
    private final int readTimeout;
    private final int maxRetries;
    
    // 构造函数、初始化HTTP客户端等
    
    // 封装API调用
    public JSONObject callApi(String action, JSONObject params) {
        // 实现认证、请求构建、发送、响应解析等
    }
    
    // 提供具体的API方法
    public JSONObject getInstances(String region, JSONObject filters) {
        // 调用通用API方法
    }
}
```

## 3. 插件功能规范

### 3.1 账号验证

所有云平台插件必须实现账号验证功能：

- 验证云平台认证信息是否有效
- 获取账号基本信息
- 检查账号权限是否满足最低要求

### 3.2 资源操作

云平台插件应实现以下基本资源操作：

#### 3.2.1 虚拟机操作

- 获取虚拟机列表
- 获取虚拟机详情
- 创建虚拟机
- 启动/停止/重启虚拟机
- 删除虚拟机

#### 3.2.2 存储操作

- 获取磁盘列表
- 创建/删除磁盘
- 挂载/卸载磁盘

#### 3.2.3 网络操作

- 获取VPC/子网列表
- 获取安全组列表
- 创建/修改/删除网络资源

### 3.3 资源同步

云平台插件应实现资源同步功能：

- 同步虚拟机数据
- 同步存储数据
- 同步网络数据
- 同步监控数据

### 3.4 数据格式标准化

插件必须将云平台特定的数据格式转换为系统标准格式：

- 资源状态标准化（如Running、Stopped等）
- 资源属性名称标准化
- 计量单位标准化（如CPU、内存、存储等）

## 4. 插件性能规范

### 4.1 响应时间

- API调用应设置合理的超时时间，通常为：
  - 连接超时：5-10秒
  - 读取超时：30-60秒
- 耗时操作应考虑异步处理

### 4.2 资源控制

- 合理使用连接池，避免资源泄漏
- 控制内存使用，避免大对象常驻内存
- 注意释放资源，如关闭流、连接等

### 4.3 并发处理

- 插件应是线程安全的
- 避免使用静态可变状态
- 使用线程安全的集合和工具类

## 5. 插件安全规范

### 5.1 敏感信息处理

- 不得在代码中硬编码敏感信息
- 不得在日志中记录敏感信息
- 传输中的敏感信息应加密处理

### 5.2 输入验证

- 必须验证所有外部输入
- 防止SQL注入、命令注入等安全问题

### 5.3 权限控制

- 遵循最小权限原则
- 验证操作权限

## 6. 插件测试规范

### 6.1 单元测试

- 核心功能必须有单元测试
- 覆盖正常和异常情况
- 模拟外部依赖

### 6.2 集成测试

- 测试与系统其他组件的集成
- 测试插件的加载和卸载
- 测试MQ消息发送和接收

### 6.3 性能测试

- 测试大量数据处理能力
- 测试并发处理能力
- 测试长时间运行的稳定性

## 7. 插件构建与部署规范

### 7.1 构建规范

- 使用Maven构建插件
- 遵循标准的版本号规范：`主版本.次版本.修订版本`
- 构建产物应是单一的JAR包，包含所有必要的类和资源

### 7.2 部署规范

- 插件JAR包应上传到指定的MinIO存储桶
- 通过管理界面或API注册插件信息
- 通过管理界面或API加载插件

## 8. 插件文档规范

### 8.1 代码注释

- 类、接口、方法应有JavaDoc注释
- 复杂逻辑应有行内注释
- 必须说明参数含义和返回值

### 8.2 接口文档

- 必须提供插件支持的API接口文档
- 详细说明请求参数和响应格式
- 提供示例请求和响应

### 8.3 使用说明

- 提供插件的安装和配置说明
- 说明插件的功能和限制
- 提供常见问题解答 