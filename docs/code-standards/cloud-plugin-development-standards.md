# 双子星(Gemini)云插件开发规范

本文档定义了双子星系统云平台插件开发的标准和规范，所有云插件开发人员必须遵循这些规范以确保插件的质量和一致性。本规范参考了已开发的ManageOne插件实现。

## 1. 云插件命名规范

### 1.1 插件项目命名

云平台插件应按照以下格式命名：

`gemini-plugins/05-plugins-cloud/[g|s|v|h|c]xx-cloud-{provider}[-{version}]`
  - g: 公有云(PUBLIC_)，如阿里云、腾讯云、华为云
  - s: 私有云/专有云(PRIVATE_)，如OpenStack、UCloud、ManageOne
  - v: 虚拟化平台(VM_)，如VMware、华为FusionCompute、华三CAS
  - h: 超融合平台(HYPER_)，如华三UIS
  - c: 容器云平台(CONTAINER_)，目前未实现，如Kubernetes、CCE
  - xx: 序号，用于同类型插件的排序
  - provider: 云平台名称
  - version: 可选，云平台版本

例如：
- `g01-cloud-ali`: 阿里云插件
- `s04-cloud-openstack-d`: OpenStack私有云插件
- `s05-cloud-manageOne`: 华为ManageOne私有云插件
- `v01-cloud-vmware`: VMware虚拟化插件
- `v03-cloud-fusionCompute-v8.0`: 华为FusionCompute虚拟化插件
- `v05-cloud-uis`: 华三UIS超融合插件（注意：当前目录命名为v05，但应为h01，在POM文件中已正确标记为hyper_uis）
- `c01-cloud-k8s`: Kubernetes容器云插件 (规划中)

字母前缀设计目的是为了在文件系统中能够按类型对插件进行分组排序，数字序号确保同种类型云的不同插件能够在目录中有序显示。插件目录命名应与CloudType枚举中的前缀保持一致（PUBLIC_、PRIVATE_、VM_、HYPER_、CONTAINER_）。

### 1.2 云插件类命名

- 插件主类应以`Plugin`或`PluginTemplate`结尾，如`AliCloudPlugin`或`ManageOnePluginTemplate`
- 注册类应以`Register`结尾，如`ManageOneRegister` 
- 客户端类应以`Client`结尾，如`ManageOneClient`
- 服务类应以`Service`结尾，如`BaseService`、`PlatformService`
- 同步服务类可命名为`FetchService`
- 配置类应以`Config`结尾，如`ConnectionConfig`、`AuthConfig`
- 模型类应以实体名称命名，如`VirtualMachine`

## 2. 云插件包结构规范

云插件应按照以下包结构组织代码：

```
com.futong.gemini.plugin.cloud.[provider]
  |- client      # API客户端
  |- service     # 业务服务
  |- common      # 通用工具
  |- sampler     # 资源同步相关
  |- [Provider]Plugin.java     # 插件主类
  |- [Provider]Register.java   # 插件注册类
```

其中：
- provider: 云平台提供商名称

## 3. 云插件配置规范

### 3.1 SPI注册文件

所有云插件必须在`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`文件中注册插件实现类的全限定名：

```
com.futong.gemini.plugin.cloud.provider.ProviderPlugin
```

### 3.2 账号配置文件

云平台插件必须提供以下配置文件，位于`resources/account/`目录下：

- `add_dispatch.json`: 定义调度任务信息
- `add_form_zh-CN.json`: 中文表单配置
- `add_form_en-US.json`: 英文表单配置

#### 3.2.1 表单字段规范

表单中的`field`字段必须与云账号信息表的字段保持一致，而`label`可以根据需要自定义。云账号信息表包含以下字段：

| 字段名 | 描述 |
|-------|------|
| cmpId | 云账号ID |
| accountName | 账号名称 |
| cloudType | 云类型 |
| serverIp | 服务器IP |
| serverPort | 服务器端口 |
| username | 用户名 |
| password | 密码 |
| protocol | 协议 |
| localIp | 本地IP |
| localPort | 本地端口 |
| status | 状态 |
| domain | 域 |
| projectId | 项目ID |
| version | 版本 |
| multiRegion | 是否多区域 |
| scvmmPort | SCVMM端口 |
| scvmmUser | SCVMM用户 |
| scvmmPassword | SCVMM密码 |
| scvmmRole | SCVMM角色 |
| jsonStr | JSON字符串(用于存储额外配置) |
| accountType | 账号类型 |
| isAddSubAccount | 是否添加子账号 |
| parentCmpId | 父账号ID |

#### 3.2.2 表单配置示例

##### add_form_zh-CN.json

`add_form_zh-CN.json`用于定义添加云账号的中文表单信息。由于不同云平台API调用所需的信息各不相同，这个文件定义了表单的结构和字段，会在插件加载时缓存，当用户需要添加云账号时通过接口获取并绘制成表单页面。

```json
{
  "model": {
    "form": {
      "items": [
        {
          "field": "accountName",
          "label": "账号名称",
          "component": "input",
          "required": true
        },
        {
          "field": "serverIp",
          "label": "服务器IP",
          "component": "input",
          "required": true
        },
        {
          "field": "username",
          "label": "用户名",
          "component": "input",
          "required": true
        },
        {
          "field": "password",
          "label": "密码",
          "component": "password",
          "required": true
        }
      ]
    }
  }
}
```

##### add_form_en-US.json

`add_form_en-US.json`是表单的英文版本，结构与中文版相同，只是标签使用英文：

```json
{
  "model": {
    "form": {
      "items": [
        {
          "field": "accountName",
          "label": "Account Name",
          "component": "input",
          "required": true
        },
        {
          "field": "serverIp",
          "label": "Server IP",
          "component": "input",
          "required": true
        },
        {
          "field": "username",
          "label": "Username",
          "component": "input",
          "required": true
        },
        {
          "field": "password",
          "label": "Password",
          "component": "password",
          "required": true
        }
      ]
    }
  }
}
```

##### add_dispatch.json

`add_dispatch.json`用于生成调度任务信息。当用户添加了某个云账号后，系统会根据这个JSON配置自动生成该账号的定时同步任务，这些任务信息会通过接口记录到调度器服务中。

```json
{
  "data": [
    {
      "required": false,
      "unique": false,
      "form": [],
      "dispatcher_info": {
        "jobName": "获取云主机列表",
        "jobDrive": "timer",
        "jobLevel": "${cmpId}",
        "jobPriority": "高",
        "triggerType": "cron",
        "triggerTime": "0 0/5 * * * ?",
        "triggerStart": null,
        "triggerEnd": null,
        "geminiRealm": "#{plugin_realm}",
        "geminiVersion": "#{plugin_version}",
        "mqExchange": null,
        "mqRoutingKey": "cmp_resource,cmp_resource_relation",
        "description": "获取云主机列表",
        "jobInfo": "{\"action\": \"FetchComputeInstance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}"
      }
    },
    {
      "required": false,
      "unique": false,
      "form": [],
      "dispatcher_info": {
        "jobName": "获取云磁盘列表",
        "jobDrive": "timer",
        "jobLevel": "${cmpId}",
        "jobPriority": "高",
        "triggerType": "cron",
        "triggerTime": "0 0/5 * * * ?",
        "triggerStart": null,
        "triggerEnd": null,
        "geminiRealm": "#{plugin_realm}",
        "geminiVersion": "#{plugin_version}",
        "mqExchange": null,
        "mqRoutingKey": "cmp_resource,cmp_resource_relation",
        "description": "获取云磁盘列表",
        "jobInfo": "{\"action\": \"FetchStorageDisk\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}"
      }
    }
  ]
}
```

#### 3.2.3 配置文件说明

1. **add_form_zh-CN.json 和 add_form_en-US.json**
   - 定义了云账号添加表单的字段和显示
   - 表单字段的`field`必须与云账号信息表字段一致
   - 字段属性包括：
     - field: 字段名称
     - label: 显示标签
     - component: 控件类型（input、password、select等）
     - required: 是否必填
     - items: 下拉选项（对于select类型）

2. **add_dispatch.json**
   - 用于生成云资源同步的调度任务
   - 配置包含多个任务项，每个任务项包括：
     - required: 是否必须创建该任务
     - unique: 是否唯一任务
     - form: 关联的表单字段
     - dispatcher_info: 调度任务详细配置
       - jobName: 任务名称
       - jobDrive: 驱动类型，通常为timer
       - jobLevel: 任务级别，通常为${cmpId}（云账号ID）
       - jobPriority: 优先级（高/中/低）
       - triggerType: 触发类型（cron）
       - triggerTime: 执行频率，使用cron表达式
       - geminiRealm: 插件域
       - geminiVersion: 插件版本
       - mqRoutingKey: 消息队列路由键
       - description: 任务描述
       - jobInfo: 任务信息，包含action和参数

## 4. 云插件实现规范

### 4.1 插件主类实现

云平台插件应继承`BaseCloudPluginTemplate`并实现必要的方法：

```java
@Slf4j
public class ProviderPluginTemplate extends BaseCloudPluginTemplate {

    @Override
    public void init(String key) {
        super.init(key);
        // 加载表单配置
        loadAccountForm();
        loadAccountDispatch();
        // 配置日志输出
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.plugin.cloud.provider", "/cloud/provider");
        log.info("插件{}初始化完成", key);
    }

    @Override
    public BaseCloudRegister getRegister() {
        return new ProviderRegister();
    }
    
    // 加载账号表单配置
    public void loadAccountForm() {
        try {
            // 加载中英文表单配置
            String[] files = {"add_form_en-US.json", "add_form_zh-CN.json"};
            for (String file : files) {
                InputStream is = getClass().getClassLoader().getResourceAsStream("account/" + file);
                String text = IoUtil.readUtf8(is);
                JSONObject json = JSON.parseObject(text);
                String lang = extractLocaleFromFilename(file);
                Locale locale = Locale.forLanguageTag(lang);
                BaseService.accountForm.put(locale, json);
            }
        } catch (Exception e) {
            log.error("加载" + key + "插件账号表单信息失败!", e);
        }
    }
    
    // 加载调度配置
    public void loadAccountDispatch() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("account/add_dispatch.json");
            String text = IoUtil.readUtf8(is);
            // 替换作用域和版本参数
            String[] split = key.split(":");
            text = StrUtil.replace(text, "#{plugin_realm}", split[0]);
            text = StrUtil.replace(text, "#{plugin_version}", split[1]);
            BaseService.accountDispatch = text;
        } catch (Exception e) {
            log.error("加载" + key + "插件账号调度任务信息失败!", e);
        }
    }
}
```

### 4.2 插件注册类实现

云平台插件应创建专门的注册类来注册各种操作处理器：

```java
public class ProviderRegister extends BaseCloudRegister {
    @Override
    public void load() {
        // 加载云平台基础操作
        onAfterLoadPlatform();
        // 加载同步资源操作
        onAfterLoadFetch();
        // 加载虚拟机操作
        onAfterLoadCompute();
        // 加载网络操作
        onAfterLoadNetwork();
        // 加载存储操作
        onAfterLoadStorage();
        // ... 其他操作
    }

    public void onAfterLoadPlatform() {
        // 账号验证
        register(ActionType.API_TEST, PlatformService::apiTest);
        // 获取账号表单
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);
        // 创建调度任务
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);
        // 认证云账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatformAccountService::authAccount);
    }
    
    // 同步资源相关的注册
    public void onAfterLoadFetch() {
        // 同步区域信息
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion)
                .addBefore(DefaultUtils::defaultPage100); // 默认分页参数
                
        // 同步虚拟机信息
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchVm)
                .addBefore(DefaultUtils::defaultPage50);
                
        // ... 其他同步操作
    }
    
    // ... 其他操作类型注册
}
```

### 4.3 客户端实现规范

API客户端类应实现以下功能：

```java
@Slf4j
public class ProviderClient {
    
    private final CloseableHttpClient httpClient;
    private final ConnectionConfig config;
    private final int connectTimeout; // 默认5-10秒
    private final int readTimeout;    // 默认30-60秒
    private final int maxRetries;     // 默认3次
    
    public ProviderClient(ConnectionConfig config) {
        this.config = config;
        this.connectTimeout = config.getConnectTimeout();
        this.readTimeout = config.getReadTimeout();
        this.maxRetries = config.getMaxRetries();
        this.httpClient = createHttpClient();
    }
    
    // 创建HTTP客户端，配置SSL和超时
    private CloseableHttpClient createHttpClient() {
        // 实现HTTP客户端创建，包括SSL配置、超时设置等
    }
    
    // 获取认证Token
    public String getToken() {
        // 从缓存获取token
        Object token = GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, cacheKey);
        if (token == null) {
            // 调用API获取token
            token = doToken();
            // 存入缓存
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, cacheKey, token);
        }
        return token.toString();
    }
    
    // 执行请求，包含重试机制
    private String executeRequest(HttpRequestBase request) throws Exception {
        // 添加认证头
        String token = getToken();
        request.setHeader("X-Auth-Token", token);
        // 设置请求头
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        
        // 执行请求并处理响应
        for (int retry = 0; retry <= maxRetries; retry++) {
            try {
                CloseableHttpResponse response = httpClient.execute(request);
                // 处理响应...
                return responseBody;
            } catch (Exception e) {
                if (retry == maxRetries) {
                    throw e;
                }
                log.warn("API请求失败，将重试({}/{}): {}", retry+1, maxRetries, e.getMessage());
                Thread.sleep(1000 * (retry + 1)); // 退避策略
            }
        }
        throw new RuntimeException("请求失败，已达最大重试次数");
    }
    
    // HTTP方法封装
    public String doGet(String path, Map<String, String> params) { /* 实现 */ }
    public String doPost(String path, Object body) { /* 实现 */ }
    public String doPut(String path, Object body) { /* 实现 */ }
    public String doDelete(String path) { /* 实现 */ }
    
    // 业务方法封装
    public JSONObject listInstances(Map<String, String> params) { /* 实现 */ }
    public JSONObject getInstance(String instanceId) { /* 实现 */ }
    // ... 其他业务方法
}
```

### 4.3.1 ClientFactory实现规范

云插件应提供一个ClientFactory类，用于创建和获取客户端实例：

```java
@Slf4j
public class ClientUtils {
    
    // 客户端缓存，使用账号ID和客户端类型作为键
    private static final Map<String, Object> clientCache = new ConcurrentHashMap<>();
    
    /**
     * 获取客户端实例，支持多种客户端类型
     * 
     * @param clazz 客户端类型
     * @param body 请求体，包含云账号信息
     * @param <C> 客户端类型泛型
     * @return 客户端实例
     */
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        // 加载认证对象信息
        body.loadAccess();
        try {
            // 请求Client对象配置信息
            ConnectionConfig config = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .domain(body.getAccess().getDomain())
                            .username(body.getAccess().getUsername())
                            .password(body.getAccess().getPassword())
                            .projectId(body.getAccess().getProjectId())
                            .build()
                    )
                    .build();
            return clazz.getConstructor(ConnectionConfig.class).newInstance(config);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
    
    /**
     * 获取或创建客户端实例，带缓存支持
     * 
     * @param clazz 客户端类型
     * @param body 请求体，包含云账号信息
     * @param useCache 是否使用缓存
     * @param <C> 客户端类型泛型
     * @return 客户端实例
     */
    @SuppressWarnings("unchecked")
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body, boolean useCache) {
        if (!useCache) {
            return client(clazz, body);
        }
        
        String accountId = body.getAuth().getCmpId();
        String cacheKey = accountId + "_" + clazz.getName();
        
        // 从缓存获取
        Object client = clientCache.get(cacheKey);
        if (client != null && clazz.isInstance(client)) {
            return (C) client;
        }
        
        // 创建新客户端
        synchronized (clientCache) {
            client = clientCache.get(cacheKey);
            if (client != null && clazz.isInstance(client)) {
                return (C) client;
            }
            
            C newClient = client(clazz, body);
            clientCache.put(cacheKey, newClient);
            return newClient;
        }
    }
    
    /**
     * 清除指定账号的客户端缓存
     * 
     * @param accountId 云账号ID
     */
    public static void removeClient(String accountId) {
        clientCache.entrySet().removeIf(entry -> entry.getKey().startsWith(accountId + "_"));
    }
    
    /**
     * 清除所有客户端缓存
     */
    public static void clearClients() {
        clientCache.clear();
    }
    
    /**
     * 通过类型选择客户端示例
     */
    public static BaseResponse apiRequest(CloudPluginRequest request) {
        // 根据客户端类型参数选择不同的客户端实现
        String clientType = request.getBody().getString("clientType");
        try {
            if ("type1".equals(clientType)) {
                ClientType1 client = client(ClientType1.class, request.getBody(), true);
                return processRequest(client, request);
            } else if ("type2".equals(clientType)) {
                ClientType2 client = client(ClientType2.class, request.getBody(), true);
                return processRequest(client, request);
            } else {
                // 默认客户端类型
                DefaultClient client = client(DefaultClient.class, request.getBody(), true);
                return processRequest(client, request);
            }
        } catch (Exception e) {
            log.error("API调用失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e);
        }
    }
    
    private static BaseResponse processRequest(BaseClient client, CloudPluginRequest request) throws Exception {
        // 执行API请求...
        return BaseResponse.SUCCESS;
    }
}
```

ConnectionConfig应该使用构建器模式实现：

```java
public class ConnectionConfig {
    private final String protocol;
    private final String host;
    private final String port;
    private final String endpoint;
    private final int connectTimeout;
    private final int socketTimeout;
    private final AuthConfig authConfig;

    private ConnectionConfig(Builder builder) {
        this.protocol = builder.protocol;
        this.host = builder.host;
        this.port = builder.port;
        this.endpoint = builder.endpoint;
        this.connectTimeout = builder.connectTimeout;
        this.socketTimeout = builder.socketTimeout;
        this.authConfig = builder.authConfig;
    }

    public String getProtocol() { return protocol; }
    public String getHost() { return host; }
    public String getPort() { return port; }
    public int getConnectTimeout() { return connectTimeout; }
    public int getSocketTimeout() { return socketTimeout; }
    public AuthConfig getAuthConfig() { return authConfig; }

    // 获取完整端点URL
    public String getEndpoint() {
        if (StrUtil.isNotEmpty(endpoint)) {
            return endpoint;
        }
        return protocol + "://" + host + ":" + port;
    }

    public static class Builder {
        private String protocol = "https";
        private String host;
        private String port;
        private String endpoint;
        private int connectTimeout = 30000; // 30秒
        private int socketTimeout = 30000;  // 30秒
        private AuthConfig authConfig;

        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }

        public Builder host(String host) {
            this.host = host;
            return this;
        }

        public Builder port(String port) {
            this.port = port;
            return this;
        }

        public Builder endpoint(String endpoint) {
            this.endpoint = endpoint;
            return this;
        }

        public Builder connectTimeout(int timeout) {
            this.connectTimeout = timeout;
            return this;
        }

        public Builder socketTimeout(int timeout) {
            this.socketTimeout = timeout;
            return this;
        }

        public Builder authConfig(AuthConfig config) {
            this.authConfig = config;
            return this;
        }

        public ConnectionConfig build() {
            validate();
            return new ConnectionConfig(this);
        }

        private void validate() {
            if (StrUtil.isEmpty(host) && StrUtil.isEmpty(endpoint)) {
                throw new IllegalArgumentException("Missing required host or endpoint");
            }
        }
    }
}
```

AuthConfig类也应采用构建器模式：

```java
public class AuthConfig {
    private final String username;
    private final String password;
    private final String domain;
    private final String projectId;
    // 其他认证相关字段...

    private AuthConfig(Builder builder) {
        this.username = builder.username;
        this.password = builder.password;
        this.domain = builder.domain;
        this.projectId = builder.projectId;
    }

    public String getUsername() { return username; }
    public String getPassword() { return password; }
    public String getDomain() { return domain; }
    public String getProjectId() { return projectId; }

    public static class Builder {
        private String username;
        private String password;
        private String domain;
        private String projectId;

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder domain(String domain) {
            this.domain = domain;
            return this;
        }

        public Builder projectId(String projectId) {
            this.projectId = projectId;
            return this;
        }

        public AuthConfig build() {
            return new AuthConfig(this);
        }
    }
}
```

应用示例：

```java
// 在服务类中使用ClientUtils获取客户端实例
public class ComputeService {

    public BaseResponse startVm(JSONObject params) {
        try {
            // 构造请求体
            CloudPluginRequest request = new CloudPluginRequest();
            request.setAction(ActionType.COMPUTE_START_VM);
            
            BaseCloudRequestBody body = new BaseCloudRequestBody();
            body.setAuth(params.getJSONObject("auth"));
            body.setCloud(params.getJSONObject("cloud"));
            request.setBody(body);
            
            // 获取客户端类型（可选）
            String clientType = body.getString("clientType");
            
            // 根据客户端类型选择不同的客户端实现
            if ("sc".equals(clientType)) {
                // 使用SC客户端，不使用缓存
                SCClient client = ClientUtils.client(SCClient.class, body, false);
                String instanceId = body.getCloud().getString("instanceId");
                client.startInstance(instanceId);
            } else {
                // 默认使用OC客户端，使用缓存
                OCClient client = ClientUtils.client(OCClient.class, body, true);
                String instanceId = body.getCloud().getString("instanceId");
                client.startInstance(instanceId);
            }
            
            return BaseResponse.SUCCESS.of("启动虚拟机成功");
        } catch (Exception e) {
            log.error("启动虚拟机失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("启动虚拟机失败: " + e.getMessage());
        }
    }
}
```

### 4.4 错误处理规范

云插件必须使用统一的错误处理机制：

```java
try {
    // 业务逻辑
} catch (IOException e) {
    log.error("网络请求失败: {}", e.getMessage(), e);
    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("网络请求失败: " + e.getMessage()), e);
} catch (Exception e) {
    log.error("未知错误: {}", e.getMessage(), e);
    throw new BaseException(BaseResponse.ERROR_SYS.of("系统错误: " + e.getMessage()), e);
}
```

#### 4.4.1 响应码体系

系统采用分层级的响应码体系，主响应码由`BaseResponse`类定义：

```
- SUCCESS                     # 成功
- FAIL                        # 失败
  - FAIL_PARAM                # 参数错误
    - FAIL_PARAM_EMPTY        # 参数为空
    - FAIL_PARAM_TYPE         # 参数类型错误
    - FAIL_PARAM_EXISTS       # 参数信息已存在
  - FAIL_OP                   # 操作失败
    - FAIL_OP_QUERY           # 执行查询失败 
    - FAIL_OP_CREATE          # 执行添加失败
    - FAIL_OP_UPDATE          # 执行修改失败
    - FAIL_OP_DELETE          # 执行删除失败
    - FAIL_OP_UPLOAD          # 执行上传失败
    - FAIL_OP_DOWNLOAD        # 执行下载失败
    - FAIL_OP_CLOUD           # 云操作失败
      - FAIL_OP_CLOUD_ACCOUNT # 云账号操作失败
        - FAIL_OP_CLOUD_ACCOUNT_AUTH # 云账号认证失败
  - FAIL_LOGIN                # 登录失败
    - FAIL_LOGIN_AUTH         # 认证失败
    - FAIL_LOGIN_VC           # 验证码错误
    - FAIL_LOGIN_EXP          # 登录失效
    - FAIL_LOGIN_DIS          # 账户已禁用
    - FAIL_LOGIN_TMT          # 登录超时
  - FAIL_TOKEN                # Token异常
    - FAIL_TOKEN_FI           # Token强制失效
    - FAIL_TOKEN_EXP          # Token过期
    - FAIL_TOKEN_UM           # Token被非法篡改
  - FAIL_LICENSE              # License异常
- ERROR                       # 异常
  - ERROR_SYS                 # 系统异常
    - ERROR_SYS_NET           # 网络异常
    - ERROR_SYS_IO            # 文件异常
    - ERROR_SYS_REDIS         # Redis异常
    - ERROR_SYS_MQ            # MQ异常
    - ERROR_SYS_MINIO         # Minio异常
  - ERROR_BIZ                 # 业务异常
    - ERROR_BIZ_DATA          # 业务数据异常
      - ERROR_BIZ_DATA_EMPTY  # 数据为空
      - ERROR_BIZ_DATA_PARSE  # 数据转换失败
      - ERROR_BIZ_DATA_REPEAT # 数据重复
      - ERROR_BIZ_DATA_PEAK   # 数据极值异常
      - ERROR_BIZ_DATA_RE     # 正则表达式异常
```

云插件常用的错误码：

- `BaseResponse.SUCCESS`: 操作成功
- `BaseResponse.FAIL_PARAM`: 参数错误
- `BaseResponse.FAIL_PARAM_EMPTY`: 参数为空
- `BaseResponse.FAIL_AUTH`: 认证失败 
- `BaseResponse.FAIL_OP_CLOUD`: 云操作失败
- `BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH`: 云账号认证失败
- `BaseResponse.ERROR_SYS`: 系统错误
- `BaseResponse.ERROR_BIZ`: 业务错误
- `BaseResponse.ERROR_BIZ_DATA_EMPTY`: 业务数据为空

#### 4.4.2 响应处理方式

`BaseResponse`提供了多种方法用于构建响应：

```java
// 使用已定义的响应码
BaseResponse response = BaseResponse.SUCCESS;

// 添加自定义消息
response = BaseResponse.SUCCESS.of("操作成功");

// 添加异常信息
response = BaseResponse.FAIL_OP_CLOUD.of(exception);

// 添加消息和异常信息
response = BaseResponse.FAIL_OP_CLOUD.of("调用云API失败", exception);

// 基于另一个响应创建新响应
response = BaseResponse.of(otherResponse);

// 扩展响应码
BaseResponse customResponse = BaseResponse.ext(BaseResponse.FAIL_OP_CLOUD, "CUSTOM");
```

响应结果判断：

```java
// 判断响应是否成功
if (BaseResponse.SUCCESS.isEquals(response)) {
    // 处理成功情况
}

// 判断响应是否不成功
if (BaseResponse.SUCCESS.isNotEquals(response)) {
    // 处理失败情况
}

// 判断响应是否属于某一类响应的子类
if (BaseResponse.FAIL_OP.isSub(response)) {
    // 响应是操作失败类型
}

// 判断响应是否属于某一响应的扩展
if (response.isExt(BaseResponse.FAIL_OP_CLOUD)) {
    // 响应是云操作失败的扩展
}
```

#### 4.4.3 带数据的响应

使用`BaseDataResponse`返回带数据的响应：

```java
// 创建带数据的成功响应
BaseDataResponse<JSONObject> response = new BaseDataResponse<>(jsonData);

// 创建带数据和消息的响应
BaseDataResponse<List<JSONObject>> response = new BaseDataResponse<>(list, "查询成功");

// 创建自定义响应码的带数据响应
BaseDataResponse<JSONObject> response = new BaseDataResponse<>(BaseResponse.FAIL, data);

// 链式调用方式
BaseDataResponse<JSONObject> response = new BaseDataResponse<>()
    .withData(jsonData)
    .withResponse(BaseResponse.SUCCESS);

// 获取响应中的数据
if (response instanceof BaseDataResponse) {
    BaseDataResponse<?> dataResponse = (BaseDataResponse<?>) response;
    Object data = dataResponse.getData();
    // 处理数据
}
```

#### 4.4.4 异常处理

`BaseException`是系统的基础异常类，提供了多种构造方法：

```java
// 基本异常，使用标准错误响应
throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY);

// 带自定义消息的异常
throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数ID不能为空");

// 带原始异常的异常
throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e);

// 带自定义消息和原始异常的异常
throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "调用API失败");
```

异常捕获与转换：

```java
try {
    // 业务逻辑
} catch (BaseException e) {
    // 已经是BaseException，直接处理
    log.error("操作失败: {}", e.getMessage());
    return e.response;
} catch (Exception e) {
    // 其他异常转换为BaseException
    log.error("未知错误", e);
    return BaseResponse.ERROR_SYS.of(e);
}
```

#### 4.4.5 国际化消息

系统使用Spring的MessageSource进行国际化消息管理。`BaseResponse`提供了`ofI18n`方法用于使用国际化消息：

```java
// 使用国际化消息键
BaseResponse response = BaseResponse.FAIL_AUTH.ofI18n("gemini.public.token.fail");

// 带参数的国际化消息
response = BaseResponse.SUCCESS.ofI18n("gemini.load.success", key, jarFilePath);
```

国际化消息文件位于：
- 公共消息: `futong-public/2-common/src/main/resources/i18n/messages_*.properties`
- 应用特定消息: `gemini-server/src/main/resources/i18n/messages_additional_*.properties`

常用的国际化消息键：

```
# 核心错误消息
ERROR=[异常]
ERROR_SYS=[系统异常]
ERROR_BIZ=[业务异常]
SUCCESS=[成功]
FAIL=[失败]
FAIL_PARAM=[参数错误]
FAIL_PARAM_EMPTY=[参数不能为空]
FAIL_OP_CLOUD=[云操作]
FAIL_OP_CLOUD_ACCOUNT_AUTH=认证失败!

# 云平台特定消息
gemini.public.http.fail=HttpClient构建失败
gemini.public.token.fail=获取token失败
gemini.public.auth.fail=账号认证失败
gemini.public.auth.success=账号认证通过.

# 资源操作相关消息
gemini.public.fetch.host.fail=同步宿主机资源数据失败
gemini.public.fetch.instance.fail=同步云主机资源数据失败
gemini.public.fetch.disk.fail=同步磁盘资源数据失败
gemini.public.fetch.storagePool.fail=同步存储池资源数据失败
gemini.public.fetch.snapshot.fail=同步快照资源数据失败
gemini.public.fetch.template.fail=同步模版资源数据失败

# 云插件管理消息
gemini.load.success=加载云类型{0},新的JAR包{1}已装载成功!
gemini.unload.success=卸载云类型{0},JAR包:{1}已卸载成功!
```

插件开发时，应遵循以下国际化消息原则：

1. 使用已定义的消息键，避免重复定义相同含义的消息
2. 插件特定的错误消息键应使用`gemini.{provider}.xxx`格式
3. 通用云操作相关的错误消息应使用`gemini.public.xxx`格式
4. 错误消息应简洁明了，指出错误原因

### 4.5 日志规范

云插件必须实现完善的日志记录：

```java
// 配置日志
DynamicLoggerConfigurator.addLogger("com.futong.gemini.plugin.cloud.provider", "/cloud/provider");

// 日志记录
log.debug("准备调用API: {}", apiName);
log.info("资源操作成功: {}", resourceId);
log.warn("API响应超时，将重试: {}", apiName);
log.error("API调用失败: {}", exception.getMessage(), exception);

// API请求日志
log.info("POST请求URL: {}", fullUrl);
log.debug("请求体: {}", requestBody);
log.debug("响应体: {}", responseBody);
```

## 5. 云插件功能规范

### 5.1 账号验证

所有云平台插件必须实现账号验证功能：

```java
public class PlatformAccountService {
    
    public static BaseResponse authAccount(JSONObject params) {
        try {
            // 解析参数
            String username = params.getString("username");
            String password = params.getString("password");
            String endpoint = buildEndpoint(params);
            
            // 创建连接配置
            ConnectionConfig config = new ConnectionConfig();
            config.setEndpoint(endpoint);
            AuthConfig authConfig = new AuthConfig();
            authConfig.setUsername(username);
            authConfig.setPassword(password);
            // 设置其他认证信息...
            config.setAuthConfig(authConfig);
            
            // 创建客户端并验证连接
            ProviderClient client = new ProviderClient(config);
            String token = client.getToken(); // 尝试获取token
            
            // 返回成功结果
            return BaseResponse.SUCCESS.of("认证成功");
        } catch (Exception e) {
            log.error("账号验证失败", e);
            return BaseResponse.FAIL_AUTH.of("认证失败: " + e.getMessage());
        }
    }
}
```

### 5.2 资源同步

云平台插件应通过FetchService实现资源同步功能：

```java
public class FetchService {
    
    // 同步虚拟机列表
    public static BaseResponse fetchVm(JSONObject params) {
        try {
            // 解析参数
            String accountId = params.getString("accountId");
            int pageSize = params.getIntValue("pageSize");
            int pageNum = params.getIntValue("pageNum");
            
            // 获取客户端
            ProviderClient client = ClientFactory.getClient(accountId);
            
            // 调用API获取资源
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("limit", String.valueOf(pageSize));
            queryParams.put("offset", String.valueOf((pageNum - 1) * pageSize));
            JSONObject result = client.listInstances(queryParams);
            
            // 转换数据格式
            List<JSONObject> instances = convertInstances(result);
            
            // 返回结果
            JSONObject response = new JSONObject();
            response.put("list", instances);
            response.put("total", result.getIntValue("total"));
            return BaseResponse.SUCCESS.of(response);
        } catch (Exception e) {
            log.error("同步虚拟机失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("同步虚拟机失败: " + e.getMessage());
        }
    }
    
    // 格式转换，将云平台特定格式转为统一格式
    private static List<JSONObject> convertInstances(JSONObject result) {
        List<JSONObject> instances = new ArrayList<>();
        JSONArray items = result.getJSONArray("items");
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            JSONObject instance = new JSONObject();
            
            // 标准化字段
            instance.put("id", item.getString("id"));
            instance.put("name", item.getString("name"));
            instance.put("status", standardizeStatus(item.getString("status")));
            instance.put("createTime", formatTime(item.getString("created")));
            // 转换其他字段...
            
            instances.add(instance);
        }
        return instances;
    }
    
    // 状态标准化
    private static String standardizeStatus(String status) {
        switch (status.toLowerCase()) {
            case "active":
            case "running":
                return "Running";
            case "stopped":
            case "shutoff":
                return "Stopped";
            // 其他状态映射...
            default:
                return "Unknown";
        }
    }
}
```

### 5.3 资源操作

云平台插件应实现资源的CRUD操作：

```java
public class ComputeService {
    
    // 启动虚拟机
    public static BaseResponse startVm(JSONObject params) {
        String accountId = params.getString("accountId");
        String instanceId = params.getString("instanceId");
        
        try {
            ProviderClient client = ClientFactory.getClient(accountId);
            client.startInstance(instanceId);
            return BaseResponse.SUCCESS.of("启动虚拟机成功");
        } catch (Exception e) {
            log.error("启动虚拟机失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("启动虚拟机失败: " + e.getMessage());
        }
    }
    
    // 其他操作方法...
}
```

### 5.4 数据格式标准化

插件必须将云平台特定的数据格式转换为系统标准格式：

```java
// 状态码映射
public static final Map<String, String> STATUS_MAP = new HashMap<>();
static {
    STATUS_MAP.put("ACTIVE", "Running");
    STATUS_MAP.put("STOPPED", "Stopped");
    STATUS_MAP.put("SUSPENDED", "Suspended");
    // 其他状态映射...
}

// 资源类型映射
public static final Map<String, String> RESOURCE_TYPE_MAP = new HashMap<>();
static {
    RESOURCE_TYPE_MAP.put("ecs", "VM");
    RESOURCE_TYPE_MAP.put("disk", "DISK");
    // 其他类型映射...
}

// 单位转换
public static long convertMemoryToMB(String memory) {
    if (memory.endsWith("GB")) {
        return Long.parseLong(memory.substring(0, memory.length() - 2)) * 1024;
    } else if (memory.endsWith("MB")) {
        return Long.parseLong(memory.substring(0, memory.length() - 2));
    }
    // 其他单位转换...
    return 0;
}
```

## 6. 云插件性能与安全规范

### 6.1 性能优化

- 使用连接池管理HTTP连接
- 实现令牌缓存机制，避免频繁认证
- 批量处理API请求，减少网络往返
- 使用异步处理大量数据同步

```java
// 缓存配置
GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, cacheKey, token, 3600); // 缓存1小时

// 批量处理示例
public List<JSONObject> batchGetResources(List<String> resourceIds, int batchSize) {
    List<JSONObject> results = new ArrayList<>();
    for (int i = 0; i < resourceIds.size(); i += batchSize) {
        List<String> batch = resourceIds.subList(i, Math.min(i + batchSize, resourceIds.size()));
        // 批量处理batch
        results.addAll(processBatch(batch));
    }
    return results;
}
```

### 6.2 安全处理

- 敏感信息处理
  - 不在日志中记录密码等敏感信息
  - 密码等敏感信息使用加密传输
  - 不在代码中硬编码认证信息

```java
// 密码日志处理
log.info("使用用户 {} 进行认证", username);
// 错误示范: log.info("使用用户 {} 密码 {} 进行认证", username, password);

// 密码参数处理
if (log.isDebugEnabled()) {
    JSONObject safeParams = new JSONObject(params);
    safeParams.put("password", "******");
    log.debug("认证请求参数: {}", safeParams);
}
```

## 7. 云插件测试规范

### 7.1 单元测试要点

- 模拟HTTP响应测试客户端
- 测试异常处理
- 测试数据格式转换

```java
@Test
public void testClientRetry() {
    // 模拟HTTP客户端，前两次请求失败，第三次成功
    MockHttpClient mockClient = new MockHttpClient()
        .addResponse(500, "服务器错误")
        .addResponse(500, "服务器错误")
        .addResponse(200, "{\"success\":true}");
    
    ProviderClient client = new ProviderClient(config, mockClient);
    JSONObject result = client.callApi("test");
    
    assertTrue(result.getBooleanValue("success"));
    assertEquals(3, mockClient.getRequestCount());
}
```

### 7.2 集成测试清单

- 账号验证
- 资源列表获取
- 资源详情获取
- 资源操作（创建、修改、删除）
- 异常情况测试

## 8. 云插件文档规范

### 8.1 JavaDoc规范

```java
/**
 * 华为云ManageOne客户端，封装对ManageOne API的调用
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ManageOneClient {
    
    /**
     * 获取虚拟机列表
     *
     * @param params 查询参数，支持以下字段:
     *               - limit: 每页数量
     *               - offset: 偏移量
     *               - status: 虚拟机状态
     * @return 虚拟机列表JSON对象
     * @throws Exception 如果API调用失败
     */
    public JSONObject listInstances(Map<String, String> params) throws Exception {
        // 实现...
    }
}
```

### 8.2 API参考文档

插件应提供API参考文档，包括：

- 支持的操作列表
- 每个操作的参数说明
- 返回值格式
- 错误码说明
- 示例请求和响应

## 9. 依赖管理

- 优先使用系统提供的公共依赖
- 避免引入冲突的依赖版本
- 如需使用特定版本依赖，应使用shade插件重定位包名

```xml
<build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <configuration>
                <relocations>
                    <relocation>
                        <pattern>com.dependency</pattern>
                        <shadedPattern>com.futong.gemini.plugin.shaded.dependency</shadedPattern>
                    </relocation>
                </relocations>
            </configuration>
        </plugin>
    </plugins>
</build>
```

## 10. 版本兼容性

- 版本命名应包含云平台版本信息
- 清晰记录API兼容性信息
- 通过配置处理不同版本之间的差异

```java
// 版本兼容性处理
private String getApiPath(String basePath) {
    if (isVersion8Plus()) {
        return "/api/v3" + basePath;
    } else {
        return "/api/v2" + basePath;
    }
}

private boolean isVersion8Plus() {
    return versionCompare(config.getVersion(), "8.0") >= 0;
}
``` 