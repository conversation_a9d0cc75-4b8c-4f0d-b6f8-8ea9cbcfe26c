# Java编码规范

本文档定义了双子星(Gemini)项目的Java代码编写标准和最佳实践。所有开发人员应遵循这些规范以确保代码质量和一致性。

## 1. 命名约定

### 1.1 包命名

- 统一使用小写字母
- 使用公司域名反转形式: `com.futong.gemini`
- 按照功能模块组织包结构

### 1.2 类命名

- 使用PascalCase(大驼峰)命名法
- 名词或名词短语
- 避免缩写，除非是广泛接受的缩写(如HTTP, URL等)

例如:
```java
public class UCloudClient { }
public class PluginInterface { }
```

### 1.3 方法命名

- 使用camelCase(小驼峰)命名法
- 动词或动词短语开头
- 名称应表明方法的功能

例如:
```java
public void loadPluginInterface(String key, ClassLoader classLoader) { }
public String getPluginKey(JSONObject plugin) { }
```

### 1.4 变量命名

- 使用camelCase(小驼峰)命名法
- 有意义的名称，避免单字母(循环计数器除外)
- 布尔变量应以"is"、"has"、"can"等开头

例如:
```java
private final CloseableHttpClient httpClient;
private boolean isConnected;
```

### 1.5 常量命名

- 全部大写，单词间用下划线分隔
- 定义为static final

例如:
```java
public static final String CONJUNCTION = ":";
```

## 2. 代码风格

### 2.1 缩进与格式化

- 使用4个空格进行缩进，不使用tab
- 每行不超过120个字符
- 花括号使用K&R风格(左花括号不换行)
- `if`, `for`, `while`等关键字与左括号间添加空格

### 2.2 注释规范

- 类头部添加Javadoc注释，说明用途
- 复杂方法添加方法注释
- 复杂逻辑添加行内注释
- 使用中文/英文注释均可，但在同一个文件中保持一致

例如:
```java
/**
 * 插件接口定义
 * 所有插件必须实现此接口
 */
public interface PluginInterface<T> {
    
    /**
     * 初始化插件
     * @param key 插件键值
     */
    void init(String key);
    
    // 其他方法...
}
```

### 2.3 异常处理

- 不要捕获Exception后不处理
- 使用finally块释放资源，或使用try-with-resources
- 异常日志应包含详细错误信息和堆栈跟踪

例如:
```java
try {
    // 业务逻辑
} catch (IOException e) {
    log.error("读取配置文件失败", e);
    throw new BaseException(BaseResponse.ERROR_SYS.of("无法加载配置"), e);
} finally {
    // 释放资源
}
```

## 3. 编码实践

### 3.1 日志规范

- 使用SLF4J+Logback框架
- 合理使用日志级别:
  - ERROR: 系统错误，需要立即处理
  - WARN: 警告信息，可能影响系统
  - INFO: 重要业务事件，系统状态变化
  - DEBUG: 调试信息
  - TRACE: 详细调试信息
- 使用占位符形式而非字符串拼接

例如:
```java
log.info("云类型({}),加载JAR:{}", key, jarFile.getPath());
```

### 3.2 空值处理

- 使用Optional处理可能为null的返回值
- 使用工具类如Hutool的StrUtil, CollUtil等进行空判断
- 不要返回null集合，返回空集合

例如:
```java
// 推荐
if (CollUtil.isEmpty(data)) {
    return Collections.emptyList();
}

// 不推荐
if (data == null || data.size() == 0) {
    return null;
}
```

### 3.3 性能考虑

- 避免在循环中创建对象
- 使用StringBuilder而非字符串拼接
- 合理使用缓存
- 避免不必要的大对象加载

## 4. 代码质量

### 4.1 单元测试

- 新功能必须有单元测试
- 测试覆盖率目标不低于80%
- 测试应包含正常情况和异常情况
- 使用JUnit和Mockito框架

### 4.2 代码审查

- 提交前进行自审
- 提交代码需经过至少一名其他开发者审查
- 解决所有代码审查中提出的问题

### 4.3 重构原则

- 发现重复代码，及时抽取共通方法
- 保持类的单一职责
- 方法不超过50行，类不超过500行

## 5. 安全编码

- 敏感信息不硬编码，使用配置文件，并加密存储
- 用户输入必须进行验证和转义
- 避免SQL注入和XSS攻击
- 不在日志中记录敏感信息

## 6. 插件开发特定规范

- 插件必须实现`PluginInterface`接口
- 使用SPI机制注册插件服务
- 在`META-INF/services`目录下添加服务发现文件
- 所有的插件方法应是线程安全的 