# 云平台插件API文档

本文档描述了双子星(Gemini)系统云平台插件的API规范和接口定义。

## 1. API概述

双子星系统云平台插件API采用基于JSON的请求/响应模式。所有请求和响应都使用JSON格式，遵循RESTful API设计原则。

### 1.1 基本请求格式

```json
{
  "action": "操作名称",
  "plugin": {
    "realm": "云平台类型",
    "version": "版本号"
  },
  "auth": {
    "id": "账号ID",
    "name": "账号名称"
  },
  "params": {
    // 操作特定参数
  }
}
```

其中:
- `action`: 表示要执行的操作，如"getVms"、"createVm"等
- `plugin`: 指定要调用的插件类型和版本
  - `realm`: 云平台类型，如"aliyun"、"tencent"等
  - `version`: 插件版本，通常与云平台API版本对应
- `auth`: 云账号信息
  - `id`: 云账号在系统中的唯一标识
  - `name`: 云账号名称
- `params`: 操作特定的参数，根据不同操作有不同的结构

### 1.2 基本响应格式

所有API响应都使用统一的格式:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

其中:
- `code`: 响应状态码，200表示成功，其他值表示失败
- `message`: 响应消息，成功或错误描述
- `data`: 响应数据，根据不同操作返回不同的数据结构

### 1.3 错误响应格式

当操作失败时，响应格式如下:

```json
{
  "code": 400,
  "message": "参数错误: 缺少必要参数'region'",
  "data": null
}
```

常见错误码:
- 400: 参数错误
- 401: 认证失败
- 403: 权限不足
- 404: 资源不存在
- 500: 系统错误
- 503: 服务不可用
- 504: 网关超时

## 2. 通用API

### 2.1 账号验证

验证云平台账号的有效性。

#### 请求

```json
{
  "action": "verifyAccount",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "params": {
    "endpoint": "https://ecs.aliyuncs.com",
    "accessKey": "YOUR_ACCESS_KEY",
    "secretKey": "YOUR_SECRET_KEY",
    "region": "cn-hangzhou"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "accountId": "*********",
    "accountName": "阿里云账号",
    "regions": [
      {
        "id": "cn-hangzhou",
        "name": "杭州"
      },
      {
        "id": "cn-beijing",
        "name": "北京"
      }
    ]
  }
}
```

### 2.2 获取区域列表

获取云平台支持的区域列表。

#### 请求

```json
{
  "action": "getRegions",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "regions": [
      {
        "id": "cn-hangzhou",
        "name": "杭州",
        "status": "Available",
        "zones": [
          {
            "id": "cn-hangzhou-a",
            "name": "杭州可用区A"
          },
          {
            "id": "cn-hangzhou-b",
            "name": "杭州可用区B"
          }
        ]
      },
      {
        "id": "cn-beijing",
        "name": "北京",
        "status": "Available",
        "zones": [
          {
            "id": "cn-beijing-a",
            "name": "北京可用区A"
          },
          {
            "id": "cn-beijing-b",
            "name": "北京可用区B"
          }
        ]
      }
    ]
  }
}
```

### 2.3 资源查询

通用的资源查询接口，可以查询各种类型的资源。

#### 请求

```json
{
  "action": "getResources",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "type": "vm",
    "region": "cn-hangzhou",
    "page": 1,
    "size": 10,
    "filters": {
      "status": ["Running", "Stopped"],
      "name": "web-server"
    }
  }
}
```

参数说明:
- `type`: 资源类型，如"vm"、"disk"、"vpc"等
- `region`: 区域ID
- `page`: 页码，从1开始
- `size`: 每页记录数
- `filters`: 过滤条件，根据资源类型不同支持不同的过滤字段

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 100,
    "page": 1,
    "size": 10,
    "items": [
      {
        "id": "i-bp1e9fngvkl2mmmx****",
        "name": "test-vm-001",
        "status": "Running",
        "type": "ecs.g6.large",
        "region": "cn-hangzhou",
        "zone": "cn-hangzhou-h",
        "createTime": "2023-01-01T12:00:00Z",
        "properties": {
          // 资源特定属性
        }
      }
      // 更多资源项...
    ]
  }
}
```

## 3. 虚拟机API

### 3.1 获取虚拟机列表

#### 请求

```json
{
  "action": "getVms",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "page": 1,
    "size": 10,
    "filters": {
      "status": ["Running", "Stopped"],
      "name": "web-server"
    }
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "items": [
      {
        "id": "i-bp1e9fngvkl2mmmx****",
        "name": "web-server-001",
        "status": "Running",
        "ip": {
          "private": ["*************"],
          "public": ["47.98.234.XXX"]
        },
        "type": "ecs.g6.large",
        "region": "cn-hangzhou",
        "zone": "cn-hangzhou-h",
        "createTime": "2023-01-01T12:00:00Z",
        "cpu": 2,
        "memory": 8,
        "disk": [
          {
            "id": "d-bp18ulljcto9no9q****",
            "type": "system",
            "size": 40,
            "device": "/dev/xvda"
          },
          {
            "id": "d-bp1e7nohcto9noiw****",
            "type": "data",
            "size": 100,
            "device": "/dev/xvdb"
          }
        ],
        "network": {
          "vpcId": "vpc-bp1opxu1zkhn00gzv****",
          "subnetId": "vsw-bp1pnbn9p7cy67wyx****",
          "securityGroups": ["sg-bp67acfmxazb4p****"]
        },
        "tags": {
          "environment": "production",
          "department": "IT"
        }
      }
      // 更多虚拟机...
    ]
  }
}
```

### 3.2 获取虚拟机详情

#### 请求

```json
{
  "action": "getVmDetail",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "id": "i-bp1e9fngvkl2mmmx****"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "i-bp1e9fngvkl2mmmx****",
    "name": "web-server-001",
    "status": "Running",
    "ip": {
      "private": ["*************"],
      "public": ["47.98.234.XXX"]
    },
    "type": "ecs.g6.large",
    "region": "cn-hangzhou",
    "zone": "cn-hangzhou-h",
    "createTime": "2023-01-01T12:00:00Z",
    "cpu": 2,
    "memory": 8,
    "os": {
      "type": "Linux",
      "distribution": "CentOS",
      "version": "7.9",
      "architecture": "x86_64"
    },
    "disk": [
      {
        "id": "d-bp18ulljcto9no9q****",
        "type": "system",
        "size": 40,
        "device": "/dev/xvda",
        "iops": 1800,
        "throughput": 140
      },
      {
        "id": "d-bp1e7nohcto9noiw****",
        "type": "data",
        "size": 100,
        "device": "/dev/xvdb",
        "iops": 3000,
        "throughput": 200
      }
    ],
    "network": {
      "vpcId": "vpc-bp1opxu1zkhn00gzv****",
      "subnetId": "vsw-bp1pnbn9p7cy67wyx****",
      "securityGroups": ["sg-bp67acfmxazb4p****"],
      "interfaces": [
        {
          "id": "eni-bp16s2iwm8k1xnrm****",
          "mac": "00:16:3e:12:34:56",
          "privateIps": ["*************"],
          "publicIps": ["47.98.234.XXX"]
        }
      ]
    },
    "tags": {
      "environment": "production",
      "department": "IT"
    },
    "billing": {
      "type": "PostPaid",
      "expireTime": "2023-12-31T23:59:59Z"
    }
  }
}
```

### 3.3 创建虚拟机

#### 请求

```json
{
  "action": "createVm",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "zone": "cn-hangzhou-h",
    "name": "web-server-002",
    "instanceType": "ecs.g6.large",
    "imageId": "centos_7_9_x64_20G_alibase_20220727.vhd",
    "systemDisk": {
      "size": 40,
      "category": "cloud_efficiency"
    },
    "dataDisk": [
      {
        "size": 100,
        "category": "cloud_ssd"
      }
    ],
    "network": {
      "vpcId": "vpc-bp1opxu1zkhn00gzv****",
      "subnetId": "vsw-bp1pnbn9p7cy67wyx****",
      "securityGroupId": "sg-bp67acfmxazb4p****",
      "assignPublicIp": true
    },
    "password": "P@ssw0rd123",
    "tags": {
      "environment": "production",
      "department": "IT"
    }
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "i-bp1g6zv0ce8og******",
    "name": "web-server-002",
    "orderId": "****************"
  }
}
```

### 3.4 操作虚拟机

#### 请求

```json
{
  "action": "operateVm",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "id": "i-bp1e9fngvkl2mmmx****",
    "operation": "restart"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "requestId": "473469C7-AA6F-4DC5-B3DB-A3DC0DE3****"
  }
}
```

## 4. 存储API

### 4.1 获取磁盘列表

#### 请求

```json
{
  "action": "getDisks",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "page": 1,
    "size": 10
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "items": [
      {
        "id": "d-bp18ulljcto9no9q****",
        "name": "system-disk-001",
        "status": "In_use",
        "type": "system",
        "category": "cloud_efficiency",
        "size": 40,
        "region": "cn-hangzhou",
        "zone": "cn-hangzhou-h",
        "createTime": "2023-01-01T12:00:00Z",
        "attachments": [
          {
            "instanceId": "i-bp1e9fngvkl2mmmx****",
            "device": "/dev/xvda"
          }
        ]
      }
      // 更多磁盘...
    ]
  }
}
```

## 5. 网络API

### 5.1 获取VPC列表

#### 请求

```json
{
  "action": "getVpcs",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "page": 1,
    "size": 10
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 5,
    "page": 1,
    "size": 10,
    "items": [
      {
        "id": "vpc-bp1opxu1zkhn00gzv****",
        "name": "production-vpc",
        "status": "Available",
        "cidrBlock": "***********/16",
        "region": "cn-hangzhou",
        "createTime": "2023-01-01T12:00:00Z",
        "description": "Production VPC"
      }
      // 更多VPC...
    ]
  }
}
```

## 6. 监控API

### 6.1 获取虚拟机监控数据

#### 请求

```json
{
  "action": "getVmMetrics",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "id": "i-bp1e9fngvkl2mmmx****",
    "metrics": ["CPUUtilization", "MemoryUsage", "DiskReadIOPS", "DiskWriteIOPS"],
    "startTime": "2023-06-01T00:00:00Z",
    "endTime": "2023-06-01T23:59:59Z",
    "period": 300
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "i-bp1e9fngvkl2mmmx****",
    "metrics": {
      "CPUUtilization": [
        {
          "timestamp": "2023-06-01T00:00:00Z",
          "value": 45.2
        },
        {
          "timestamp": "2023-06-01T00:05:00Z",
          "value": 32.7
        }
        // 更多数据点...
      ],
      "MemoryUsage": [
        {
          "timestamp": "2023-06-01T00:00:00Z",
          "value": 62.5
        },
        {
          "timestamp": "2023-06-01T00:05:00Z",
          "value": 64.3
        }
        // 更多数据点...
      ]
      // 更多指标...
    }
  }
}
```

## 7. 标签API

### 7.1 获取资源标签

#### 请求

```json
{
  "action": "getResourceTags",
  "plugin": {
    "realm": "aliyun",
    "version": "1.0"
  },
  "auth": {
    "id": "account-001",
    "name": "阿里云账号"
  },
  "params": {
    "region": "cn-hangzhou",
    "resourceType": "vm",
    "resourceId": "i-bp1e9fngvkl2mmmx****"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "resourceId": "i-bp1e9fngvkl2mmmx****",
    "resourceType": "vm",
    "tags": {
      "environment": "production",
      "department": "IT",
      "owner": "zhang.san",
      "project": "ecommerce"
    }
  }
}
```

## 8. 错误码

| 错误码 | 描述 | 处理建议 |
|-------|------|---------|
| 200 | 成功 | 无 |
| 400 | 参数错误 | 检查请求参数是否正确 |
| 401 | 认证失败 | 检查认证信息是否正确 |
| 403 | 权限不足 | 检查账号权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 429 | 请求过于频繁 | 降低API调用频率 |
| 500 | 系统错误 | 联系系统管理员 |
| 504 | 网关超时 | 检查网络连接或重试 | 