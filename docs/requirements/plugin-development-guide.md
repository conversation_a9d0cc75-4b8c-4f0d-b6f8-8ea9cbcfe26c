# 云平台插件开发指南

本文档提供了在双子星(Gemini)系统中开发新的云平台插件的详细步骤和最佳实践。

## 1. 插件开发准备

### 1.1 开发环境准备

- JDK 1.8+
- Maven 3.6+
- IDE (推荐IntelliJ IDEA)
- Git客户端
- 云平台账号和API密钥
- 对目标云平台API的了解

### 1.2 相关依赖

开发一个新的云平台插件需要以下核心依赖:

```xml
<dependencies>
    <!-- 双子星插件SDK -->
    <dependency>
        <groupId>com.futong.gemini</groupId>
        <artifactId>plugin-sdk</artifactId>
        <version>${project.version}</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- 通用工具类库 -->
    <dependency>
        <groupId>com.futong</groupId>
        <artifactId>public-common</artifactId>
        <version>1.0-SNAPSHOT</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- 如果需要，可以添加特定云平台的SDK -->
    <dependency>
        <groupId>com.example.cloud</groupId>
        <artifactId>cloud-sdk</artifactId>
        <version>x.y.z</version>
    </dependency>
    
    <!-- 日志依赖 -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.30</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- JSON处理 -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.75</version>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

## 2. 插件项目结构

按照以下结构组织项目文件:

```
cloud-provider-name/
├── pom.xml
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── futong/
│       │           └── gemini/
│       │               └── plugin/
│       │                   └── cloud/
│       │                       └── provider/
│       │                           ├── client/        # API客户端
│       │                           │   └── CloudClient.java
│       │                           ├── model/         # 数据模型
│       │                           │   └── CloudModel.java
│       │                           ├── service/       # 业务服务
│       │                           │   └── CloudService.java
│       │                           └── CloudPlugin.java  # 插件入口类
│       └── resources/
│           ├── account/            # 账号配置
│           │   ├── add_dispatch.json
│           │   ├── add_form_en-US.json
│           │   └── add_form_zh-CN.json
│           └── META-INF/
│               └── services/
│                   └── com.futong.gemini.plugin.sdk.PluginInterface  # SPI注册文件
```

## 3. 插件开发步骤

### 3.1 创建插件主类

实现`PluginInterface`接口，这是插件的入口点。推荐继承`AbstractCloudPlugin`抽象类，它已经实现了一些通用功能:

```java
package com.futong.gemini.plugin.cloud.provider;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.AbstractCloudPlugin;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CloudPlugin extends AbstractCloudPlugin<JSONObject> {
    
    @Override
    public void init(String key) {
        // 初始化插件，设置默认值等
        super.init(key);
        // 配置日志输出
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.provider", "/cloud/provider");
        log.info("插件{}初始化完成", key);
    }
    
    @Override
    public void destroy() {
        // 资源清理
        log.info("插件销毁");
    }
    
    @Override
    public BaseResponse execute(JSONObject params) {
        // 根据请求参数分发到不同的处理方法
        try {
            String action = params.getString("action");
            switch (action) {
                case "verifyAccount":
                    return verifyAccount(params);
                case "getVms":
                    return getVirtualMachines(params);
                case "createVm":
                    return createVirtualMachine(params);
                // 添加更多操作...
                default:
                    return BaseResponse.FAIL_PARAM.of("不支持的操作: " + action);
            }
        } catch (Exception e) {
            log.error("处理请求时出错", e);
            return BaseResponse.ERROR_SYS.of("系统错误: " + e.getMessage());
        }
    }
    
    private BaseResponse verifyAccount(JSONObject params) {
        // 实现账号验证逻辑
        return BaseResponse.SUCCESS.of("账号验证成功");
    }
    
    private BaseResponse getVirtualMachines(JSONObject params) {
        // 实现获取虚拟机列表的逻辑
        return BaseResponse.SUCCESS.of("获取虚拟机列表成功");
    }
    
    private BaseResponse createVirtualMachine(JSONObject params) {
        // 实现创建虚拟机的逻辑
        return BaseResponse.SUCCESS.of("创建虚拟机成功");
    }
}
```

### 3.2 创建云平台API客户端

封装对特定云平台API的调用，处理认证、请求构建和响应解析等通用逻辑:

```java
package com.futong.gemini.plugin.cloud.provider.client;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CloudClient {
    
    private final String apiEndpoint;
    private final String accessKey;
    private final String secretKey;
    private final int connectTimeout;
    private final int readTimeout;
    
    public CloudClient(String apiEndpoint, String accessKey, String secretKey) {
        this(apiEndpoint, accessKey, secretKey, 10000, 30000);
    }
    
    public CloudClient(String apiEndpoint, String accessKey, String secretKey, 
                      int connectTimeout, int readTimeout) {
        this.apiEndpoint = apiEndpoint;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        // 初始化HTTP客户端等
    }
    
    // 封装API调用方法
    public JSONObject callApi(String action, JSONObject params) {
        try {
            // 构建请求
            // 添加认证信息
            // 发送请求
            // 解析响应
            return new JSONObject();
        } catch (Exception e) {
            log.error("API调用失败: " + action, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("API调用失败: " + e.getMessage()), e);
        }
    }
    
    // 封装特定API的调用方法
    public JSONObject getInstances(String region, JSONObject filters) {
        JSONObject params = new JSONObject();
        params.put("Region", region);
        if (filters != null) {
            params.put("Filters", filters);
        }
        return callApi("DescribeInstances", params);
    }
    
    // 更多API方法...
}
```

### 3.3 注册SPI服务

在`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`文件中添加插件实现类的全限定名:

```
com.futong.gemini.plugin.cloud.provider.CloudPlugin
```

这个文件是Java SPI机制的关键，系统通过它发现并加载插件实现。

### 3.4 创建账号配置文件

账号配置文件用于定义云平台账号的表单字段和配置项，位于`resources/account/`目录下。

#### 3.4.1 add_dispatch.json

定义插件需要的配置参数及其分组:

```json
{
  "baseInfo": {
    "icon": "cloud-provider",
    "name": "云平台名称",
    "i18n": "cloud.provider.name"
  },
  "formConfig": {
    "tabs": [
      {
        "key": "base",
        "name": "基本信息",
        "i18n": "cloud.base"
      }
    ],
    "groups": [
      {
        "tab": "base",
        "key": "baseConfig",
        "name": "基础配置",
        "i18n": "cloud.base.config",
        "items": [
          "name",
          "type",
          "endpoint",
          "accessKey",
          "secretKey",
          "region"
        ]
      }
    ]
  }
}
```

#### 3.4.2 add_form_zh-CN.json

中文表单配置:

```json
{
  "name": {
    "label": "账号名称",
    "placeholder": "请输入账号名称",
    "type": "input",
    "rules": [
      {
        "required": true,
        "message": "账号名称不能为空"
      }
    ]
  },
  "type": {
    "label": "账号类型",
    "type": "hidden",
    "initialValue": "provider"
  },
  "endpoint": {
    "label": "API地址",
    "placeholder": "请输入API地址",
    "type": "input",
    "rules": [
      {
        "required": true,
        "message": "API地址不能为空"
      }
    ]
  },
  "region": {
    "label": "默认区域",
    "placeholder": "请选择默认区域",
    "type": "select",
    "options": [
      {
        "label": "区域1",
        "value": "region1"
      },
      {
        "label": "区域2",
        "value": "region2"
      }
    ],
    "rules": [
      {
        "required": true,
        "message": "默认区域不能为空"
      }
    ]
  },
  "accessKey": {
    "label": "访问密钥ID",
    "placeholder": "请输入访问密钥ID",
    "type": "input",
    "rules": [
      {
        "required": true,
        "message": "访问密钥ID不能为空"
      }
    ]
  },
  "secretKey": {
    "label": "访问密钥密码",
    "placeholder": "请输入访问密钥密码",
    "type": "password",
    "rules": [
      {
        "required": true,
        "message": "访问密钥密码不能为空"
      }
    ]
  }
}
```

#### 3.4.3 add_form_en-US.json

英文表单配置，内容结构与中文版相同，只是文本内容为英文。

### 3.5 实现核心业务逻辑

以下是需要实现的主要功能:

#### 3.5.1 账号验证

验证云平台账号的有效性:

```java
private BaseResponse verifyAccount(JSONObject params) {
    try {
        String endpoint = params.getString("endpoint");
        String accessKey = params.getString("accessKey");
        String secretKey = params.getString("secretKey");
        
        // 创建客户端
        CloudClient client = new CloudClient(endpoint, accessKey, secretKey);
        
        // 调用一个简单API进行验证
        JSONObject result = client.callApi("DescribeRegions", new JSONObject());
        
        // 返回成功
        JSONObject data = new JSONObject();
        data.put("accountId", "云平台账号ID");
        data.put("accountName", "云平台账号名称");
        return BaseResponse.SUCCESS.ofData(data);
    } catch (Exception e) {
        log.error("账号验证失败", e);
        return BaseResponse.FAIL_AUTH.of("账号验证失败: " + e.getMessage());
    }
}
```

#### 3.5.2 资源查询

实现云资源查询功能:

```java
private BaseResponse getVirtualMachines(JSONObject params) {
    try {
        // 从参数中获取认证信息
        JSONObject auth = params.getJSONObject("auth");
        String accountId = auth.getString("id");
        
        // 获取其他参数
        String region = params.getJSONObject("params").getString("region");
        JSONObject filters = params.getJSONObject("params").getJSONObject("filters");
        
        // 从配置中获取账号信息
        JSONObject accountInfo = getAccountInfo(accountId);
        String endpoint = accountInfo.getString("endpoint");
        String accessKey = accountInfo.getString("accessKey");
        String secretKey = accountInfo.getString("secretKey");
        
        // 创建客户端
        CloudClient client = new CloudClient(endpoint, accessKey, secretKey);
        
        // 调用API获取资源列表
        JSONObject result = client.getInstances(region, filters);
        
        // 处理结果并返回
        return BaseResponse.SUCCESS.ofData(processVmList(result));
    } catch (Exception e) {
        log.error("获取虚拟机列表失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机列表失败: " + e.getMessage());
    }
}

// 处理API返回的原始数据，转换为标准格式
private JSONObject processVmList(JSONObject rawData) {
    // 转换逻辑
    // ...
    return processedData;
}
```

#### 3.5.3 资源操作

实现资源创建、修改、删除等操作:

```java
private BaseResponse createVirtualMachine(JSONObject params) {
    try {
        // 从参数中获取认证信息
        JSONObject auth = params.getJSONObject("auth");
        String accountId = auth.getString("id");
        
        // 获取创建参数
        JSONObject createParams = params.getJSONObject("params");
        String region = createParams.getString("region");
        String name = createParams.getString("name");
        String instanceType = createParams.getString("instanceType");
        // 更多参数...
        
        // 从配置中获取账号信息
        JSONObject accountInfo = getAccountInfo(accountId);
        String endpoint = accountInfo.getString("endpoint");
        String accessKey = accountInfo.getString("accessKey");
        String secretKey = accountInfo.getString("secretKey");
        
        // 创建客户端
        CloudClient client = new CloudClient(endpoint, accessKey, secretKey);
        
        // 调用API创建资源
        JSONObject result = client.createInstance(region, name, instanceType, /* 其他参数 */);
        
        // 处理结果并返回
        JSONObject data = new JSONObject();
        data.put("id", result.getString("InstanceId"));
        data.put("name", name);
        return BaseResponse.SUCCESS.ofData(data);
    } catch (Exception e) {
        log.error("创建虚拟机失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("创建虚拟机失败: " + e.getMessage());
    }
}
```

#### 3.5.4 资源同步

实现资源数据同步，通过MQ发送数据到其他服务:

```java
private BaseResponse syncResources(JSONObject params) {
    try {
        // 从参数中获取认证信息
        JSONObject auth = params.getJSONObject("auth");
        String accountId = auth.getString("id");
        
        // 获取同步参数
        JSONObject syncParams = params.getJSONObject("params");
        String region = syncParams.getString("region");
        String resourceType = syncParams.getString("resourceType");
        
        // 从配置中获取账号信息
        JSONObject accountInfo = getAccountInfo(accountId);
        String endpoint = accountInfo.getString("endpoint");
        String accessKey = accountInfo.getString("accessKey");
        String secretKey = accountInfo.getString("secretKey");
        
        // 创建客户端
        CloudClient client = new CloudClient(endpoint, accessKey, secretKey);
        
        // 获取资源数据
        JSONObject resources = null;
        switch (resourceType) {
            case "vm":
                resources = client.getInstances(region, null);
                break;
            case "disk":
                resources = client.getDisks(region, null);
                break;
            // 更多资源类型...
        }
        
        // 处理数据
        JSONObject processedData = processResourceData(resources, resourceType);
        
        // 发送MQ消息
        sendMQMessage(processedData, resourceType, accountId);
        
        return BaseResponse.SUCCESS.of("同步成功");
    } catch (Exception e) {
        log.error("资源同步失败", e);
        return BaseResponse.FAIL_OP_CLOUD.of("资源同步失败: " + e.getMessage());
    }
}

// 发送MQ消息
private void sendMQMessage(JSONObject data, String resourceType, String accountId) {
    // MQ发送逻辑
    // ...
}
```

## 4. 插件构建与部署

### 4.1 构建命令

```bash
mvn -pl gemini-plugins/05-plugins-cloud/cloud-provider-name -am clean package
```

这个命令会构建指定的插件模块及其依赖。

### 4.2 插件包结构

构建后的插件包是一个JAR文件，包含:

- 已编译的类文件
- META-INF/services/目录下的SPI注册文件
- 配置文件和资源

### 4.3 部署步骤

1. 将构建好的插件JAR包上传到MinIO存储
2. 通过管理界面注册插件信息，指定以下内容:
   - 插件名称
   - 插件版本
   - JAR包在MinIO中的路径
   - 插件类型(cloud/biz/project/product)
   - realm值
   - version值
3. 点击"加载"按钮，系统将自动加载插件
4. 加载成功后，可以在"云账号"管理页面添加相应类型的云账号

## 5. 插件测试

### 5.1 单元测试

为插件的核心功能编写单元测试，确保功能正确性:

```java
@Test
public void testVerifyAccount() {
    CloudPlugin plugin = new CloudPlugin();
    plugin.init("test:1.0");
    
    JSONObject params = new JSONObject();
    params.put("endpoint", "https://api.example.com");
    params.put("accessKey", "test-key");
    params.put("secretKey", "test-secret");
    
    BaseResponse response = plugin.execute(createRequest("verifyAccount", params));
    Assert.assertEquals(BaseResponse.SUCCESS.getCode(), response.getCode());
}

private JSONObject createRequest(String action, JSONObject params) {
    JSONObject request = new JSONObject();
    request.put("action", action);
    request.put("params", params);
    return request;
}
```

### 5.2 集成测试

在集成环境中测试插件功能，验证与系统的集成是否正常:

1. 部署双子星系统和测试插件
2. 通过API或界面添加云账号
3. 测试各项功能是否正常工作

### 5.3 功能测试

测试插件的各项功能是否符合需求，包括:
- 账号验证
- 资源查询
- 资源操作
- 资源同步
- 错误处理

## 6. 错误处理与日志

### 6.1 错误处理

插件应使用统一的错误处理机制:

1. 使用`BaseException`封装异常
2. 使用`BaseResponse`定义标准响应码
3. 避免在插件中抛出未捕获的异常

示例:

```java
try {
    // 业务逻辑
} catch (IOException e) {
    log.error("API调用失败", e);
    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("API调用失败: " + e.getMessage()), e);
} catch (Exception e) {
    log.error("未知错误", e);
    throw new BaseException(BaseResponse.ERROR_SYS.of("系统错误: " + e.getMessage()), e);
}
```

### 6.2 日志记录

插件应实现完善的日志记录:

1. 使用SLF4J API进行日志记录
2. 根据重要性选择合适的日志级别
3. 在插件初始化时配置独立的日志文件
4. 记录关键操作和错误信息，但避免记录敏感数据

示例:

```java
// 在插件初始化时配置日志
DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.provider", "/cloud/provider");

// 日志记录
log.debug("准备调用API: {}", apiName);
log.info("成功创建资源: {}", resourceId);
log.warn("API响应超时，将重试: {}", apiName);
log.error("API调用失败", exception);
```

## 7. 常见问题

### 7.1 插件加载失败

**问题**: 插件无法被系统加载。

**解决方案**:
- 确认SPI注册文件路径和内容正确: `META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`
- 确认插件实现类正确实现了`PluginInterface`接口
- 检查插件JAR包是否包含所有必要的类和资源
- 查看系统日志获取详细错误信息

### 7.2 API调用失败

**问题**: 调用云平台API失败。

**解决方案**:
- 确认API地址和认证信息正确
- 检查网络连接是否正常
- 查看请求和响应日志，分析失败原因
- 检查API参数格式是否符合云平台要求
- 考虑实现重试机制

### 7.3 功能不正确

**问题**: 插件功能实现与预期不符。

**解决方案**:
- 检查代码逻辑是否符合云平台API文档要求
- 验证请求参数格式是否正确
- 确认响应数据解析是否正确
- 编写更详细的单元测试

## 8. 最佳实践

- **统一错误处理**: 使用`BaseException`和`BaseResponse`进行统一的错误处理
- **完善日志记录**: 记录关键操作和错误信息，方便问题排查
- **参数验证**: 对所有输入参数进行验证，避免无效请求发送到云平台
- **API封装**: 将云平台API调用封装在专门的客户端类中，便于复用和维护
- **数据转换**: 将云平台特定的数据格式转换为系统标准格式，保持一致性
- **连接池管理**: 使用连接池管理API客户端连接，提高性能
- **重试机制**: 对可重试的错误实现自动重试机制
- **超时控制**: 设置合理的连接和读取超时时间
- **异步处理**: 对耗时操作考虑使用异步处理
- **资源释放**: 正确释放资源，避免内存泄漏
- **版本兼容**: 考虑云平台API版本变化，设计兼容机制
- **敏感信息保护**: 不要在日志中记录敏感信息，如密钥 