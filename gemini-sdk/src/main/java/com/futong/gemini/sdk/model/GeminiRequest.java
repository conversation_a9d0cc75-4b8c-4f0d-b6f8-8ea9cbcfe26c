package com.futong.gemini.sdk.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

@Data
public abstract class GeminiRequest<T extends JSONObject> extends JSONObject {

    //请求信息
    private String action;
    private T body;

    public GeminiRequest(JSONObject json) {
        super(json);
        this.action = json.getString("action");
        builderBody(json.getJSONObject("body"));
    }

    public abstract T builderBody(JSONObject json);


    public void setAction(String action) {
        put("action", action);
        this.action = action;
    }

    public JSONObject cloneJSONObject() {
        return JSON.parseObject(this.toJSONString());
    }
}
