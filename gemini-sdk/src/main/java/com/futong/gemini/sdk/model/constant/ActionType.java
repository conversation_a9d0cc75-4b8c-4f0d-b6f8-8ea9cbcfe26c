package com.futong.gemini.sdk.model.constant;

import cn.hutool.core.util.StrUtil;

/**
 * 名字规则 OperationType_Module_ResourceType
 */
public enum ActionType {
    /**********************云平台*************************/
    API_TEST("API测试", ModuleType.PLATFORM, ResourceType.API, OperationType.QUERY),
    //账号相关
    AUTH_PLATFORM_ACCOUNT("平台账号校验", ModuleType.PLATFORM, ResourceType.ACCOUNT, OperationType.QUERY),
    GET_PLATFORM_ACCOUNT_ADD_FORM("查询平台账号添加表单", ModuleType.PLATFORM, ResourceType.ACCOUNT, OperationType.GET),
    GET_PLATFORM_FETCH_DISPATCH_MODEL("获取平台调度任务模型", ModuleType.PLATFORM, ResourceType.DISPATCH, OperationType.GET),
    CREATE_PLATFORM_FETCH_DISPATCH("添加默认平台调度任务", ModuleType.PLATFORM, ResourceType.DISPATCH, OperationType.CREATE),
    QUERY_PLATFORM_BILL_BALANCE("查群云平台账单余额", ModuleType.PLATFORM, ResourceType.CMP_BILL_RES, OperationType.QUERY),
    /**********************资源同步*************************/
    FETCH_COMPUTE_HOST("同步物理机", ModuleType.COMPUTE, ResourceType.CMDB_HOST_RES, OperationType.FETCH),
    FETCH_COMPUTE_HOST_PERF("同步物理机性能", ModuleType.COMPUTE, ResourceType.CMDB_HOST_RES, OperationType.FETCH),
    FETCH_COMPUTE_INSTANCE("同步云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    FETCH_COMPUTE_INSTANCE_RELATION("同步云主机关联关系", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    FETCH_COMPUTE_INSTANCE_PERF("同步云主机性能", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    FETCH_COMPUTE_TEMPLATE("同步模版", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    FETCH_COMPUTE_FLAVOR("同步规格", ModuleType.COMPUTE, ResourceType.CMDB_FLAVOR, OperationType.FETCH),
    FETCH_COMPUTE_NODE("同步云主机性能", ModuleType.COMPUTE, ResourceType.CMDB_HOST_RES, OperationType.FETCH),
    FETCH_COMPUTE_SECURITYGROUP("同步安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.FETCH),
    FETCH_COMPUTE_SECURITYGROUP_RULE("同步安全组规则", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RULE, OperationType.FETCH),
    FETCH_STORAGE_POOL("同步存储池", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.FETCH),
    FETCH_STORAGE_IMAGE("同步镜像", ModuleType.STORAGE, ResourceType.CMDB_IMAGE_RES, OperationType.FETCH),
    FETCH_STORAGE_DISK("同步磁盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.FETCH),
    FETCH_STORAGE_SNAPSHOT("同步快照", ModuleType.STORAGE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.FETCH),
    FETCH_STORAGE_BUCKET("同步桶", ModuleType.STORAGE, ResourceType.CMDB_BUCKET_RES, OperationType.FETCH),
    FETCH_STORAGE_BUCKET_FILE("同步桶文件", ModuleType.STORAGE, ResourceType.CMDB_BUCKET_RES, OperationType.FETCH),
    FETCH_NEUTRON_SUBNET("同步子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.FETCH),
    FETCH_NEUTRON_PORT("同步子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.FETCH),
    FETCH_NEUTRON_SWITCH("同步交换机", ModuleType.NEUTRON, ResourceType.CMDB_VSWITCH_RES, OperationType.FETCH),
    FETCH_NEUTRON_VPC("同步VPC", ModuleType.NEUTRON, ResourceType.CMDB_VPC_RES, OperationType.FETCH),
    FETCH_NEUTRON_NIC("同步网卡", ModuleType.NEUTRON, ResourceType.CMDB_NETCARD_RES, OperationType.FETCH),
    FETCH_NEUTRON_ROUTE("同步路由", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.FETCH),
    FETCH_NEUTRON_EIP("同步EIP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.FETCH),
    FETCH_NEUTRON_NAT("同步NAT网络", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.FETCH),
    FETCH_NEUTRON_VLAN("同步虚拟局域网", ModuleType.NEUTRON, ResourceType.CMDB_VLAN_RES, OperationType.FETCH),
    FETCH_NEUTRON_NAT_ENTRY("同步NAT网络规则条目", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.FETCH),
    FETCH_NEUTRON_DNAT_ENTRY("同步DNAT网络规则条目", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.FETCH),
    FETCH_NEUTRON_SNAT_ENTRY("同步SNAT网络规则条目", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.FETCH),
    FETCH_NEUTRON_LOADBALANCE("同步负载均衡", ModuleType.NEUTRON, ResourceType.CMDB_LOADBALANCE_RES, OperationType.FETCH),
    FETCH_PLATFORM_DATACENTER("同步数据中心", ModuleType.PLATFORM, ResourceType.CMDB_VPC_RES, OperationType.FETCH),
    FETCH_PLATFORM_CLUSTER("同步集群", ModuleType.PLATFORM, ResourceType.CMDB_VPC_RES, OperationType.FETCH),
    FETCH_PLATFORM_FOLDER("同步文件夹", ModuleType.PLATFORM, ResourceType.CMDB_VPC_RES, OperationType.FETCH),
    FETCH_PLATFORM_RESOURCE_POOL("同步资源池", ModuleType.PLATFORM, ResourceType.CMDB_VPC_RES, OperationType.FETCH),
    FETCH_PLATFORM_KEYPAIR("同步密钥对", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_PROJECT("同步组织", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_USER("同步用户", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_AZONE("同步可用区", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_VDC("同步VDC", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_VDC_PROJECT("同步VDC下资源空间", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_TENANT("同步租户", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_HOSTPOOL("同步主机池", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_REGION("同步Region", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_ALARM("同步告警", ModuleType.PLATFORM, ResourceType.CMP_ALARM_RES, OperationType.FETCH),
    FETCH_PLATFORM_BILL("同步告警", ModuleType.PLATFORM, ResourceType.CMP_BILL_RES, OperationType.FETCH),
    FETCH_PLATFORM_BILL_DAY("按天同步告警", ModuleType.PLATFORM, ResourceType.CMP_BILL_RES, OperationType.FETCH),
    FETCH_PLATFORM_EVENT("同步事件", ModuleType.PLATFORM, ResourceType.CMP_EVENT_RES, OperationType.FETCH),
    FETCH_DATABASE_MONGO("同步MongoDB", ModuleType.DATABASE, ResourceType.CMDB_MONGO, OperationType.FETCH),
    FETCH_DATABASE_REDIS("同步Redis", ModuleType.DATABASE, ResourceType.CMDB_REDIS, OperationType.FETCH),
    FETCH_DATABASE_RDS("同步Rds", ModuleType.DATABASE, ResourceType.CMDB_RDS, OperationType.FETCH),
    FETCH_MESSAGE_KAFKA("同步Kafka", ModuleType.MESSAGE, ResourceType.CMDB_KAFKA, OperationType.FETCH),
    FETCH_MESSAGE_ROCKETMQ("同步RocketMQ", ModuleType.MESSAGE, ResourceType.CMDB_ROCKETMQ, OperationType.FETCH),
    FETCH_CONTAINER_KUBERNETES("同步Kubernetes", ModuleType.CONTAINER, ResourceType.CMDB_KUBERNETES, OperationType.FETCH),
    FETCH_STORAGE_UNIT("同步存储设备", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_UNIT_RES, OperationType.FETCH),
    FETCH_STORAGE_RESOURCE("同步存储资源", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_RESOURCE_RES, OperationType.FETCH),
    //???????为什么用块存储，不适用云盘记录
    FETCH_BLOCK_STORAGE("同步块存储", ModuleType.STORAGE, ResourceType.CMDB_BLOCK_STORAGE_RES, OperationType.FETCH),
    //???????看来同步是同步物理机信息，为什么搞了个同步概要???
    FETCH_COMPUTE_HOST_OVERVIEW_INFO("同步物理机概要信息", ModuleType.STORAGE, ResourceType.CMDB_HOST_RES, OperationType.FETCH),
    FETCH_NEUTRON_UPLINK_PORTS("同步上线链路组", ModuleType.NEUTRON, ResourceType.CMDB_UPLINK_PORT_RES, OperationType.FETCH),
    //???????应使用REFRESH_COMPUTE_INSTANCE，此ACTION后续要废弃
    SYNC_COMPUTE_INSTANCE("同步虚拟机", ModuleType.STORAGE, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    //???????这个只在uis中before方法做了默认，并没有对应处理,待删除
    FETCH_COMPUTE_INSTANCE_NETWORK("同步虚拟机网络", ModuleType.NEUTRON, ResourceType.CMDB_INSTANCE_RES, OperationType.FETCH),
    //???????????是否同规格????????,如果是OS，不用加后缀VERSION
    FETCH_COMPUTE_OS_VERSION("同步操作操作系统", ModuleType.COMPUTE, ResourceType.CMDB_OS_RES, OperationType.FETCH),
    /**********************计算-云主机*************************/
    CREATE_COMPUTE_INSTANCE("创建云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.CREATE),
    START_COMPUTE_INSTANCE("启动云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.START),
    STOP_COMPUTE_INSTANCE("停止云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.STOP),
    REBOOT_COMPUTE_INSTANCE("重启云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.REBOOT),
    DELETE_COMPUTE_INSTANCE("删除云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    UPDATE_COMPUTE_INSTANCE("修改云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.UPDATE),
    QUERY_UPDATE_COMPUTE_INSTANCE_FLAVOR("查询可修改云主机规格", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    UPDATE_COMPUTE_INSTANCE_FLAVOR("修改云主机规格", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.UPDATE),
    SHUTDOWN_COMPUTE_INSTANCE("安全关机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.STOP),
    PAUSE_COMPUTE_INSTANCE("暂停云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.STOP),
    RESUME_COMPUTE_INSTANCE("恢复云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.START),
    SUSPEND_COMPUTE_INSTANCE("挂起云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.START),
    HIBERNATE_COMPUTE_INSTANCE("休眠云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.STOP),
    CONSOLE_COMPUTE_INSTANCE("控制台", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.CONSOLE),
    UPDATE_COMPUTE_INSTANCE_VNC("云主机VNC登录密码", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.UPDATE),
    QUERY_COMPUTE_INSTANCE_VNC("云主机VNC登陆地址", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    QUERY_COMPUTE_INSTANCE_TOTAL("云主机统计信息", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    QUERY_COMPUTE_PRODUCT("查询弹性云服务器产品列表", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.QUERY),
    QUERY_COMPUTE_TAG("查询标签列表", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.QUERY),
    REFRESH_COMPUTE_INSTANCE("刷新云主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.REFRESH),
    /**********************计算-云主机-快照*************************/
    CREATE_COMPUTE_SNAPSHOT("创建云主机快照", ModuleType.COMPUTE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.CREATE),
    DELETE_COMPUTE_SNAPSHOT("删除云主机快照", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    UPDATE_COMPUTE_SNAPSHOT("修改云主机快照", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.UPDATE),
    RESUME_COMPUTE_SNAPSHOT("恢复云主机快照", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.UPDATE),
    /**********************计算-云主机-安全组*************************/
    CREATE_COMPUTE_SECURITYGROUP("创建云主机安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.CREATE),
    DELETE_COMPUTE_SECURITYGROUP("删除云主机安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.DELETE),
    UPDATE_COMPUTE_SECURITYGROUP("修改云主机安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.UPDATE),
    BIND_COMPUTE_SECURITYGROUP("绑定云主机安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.UPDATE),
    UNBIND_COMPUTE_SECURITYGROUP("解绑云主机安全组", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RES, OperationType.UPDATE),
    CREATE_COMPUTE_SECURITYGROUP_RULE("创建云主机安全组规则", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RULE, OperationType.CREATE),
    DELETE_COMPUTE_SECURITYGROUP_RULE("删除云主机安全组规则", ModuleType.COMPUTE, ResourceType.CMDB_SECURITYGROUP_RULE, OperationType.DELETE),
    /**********************计算-云主机-密钥对*************************/
    CREATE_COMPUTE_KEYPAIR("创建云主机密钥对", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.CREATE),
    DELETE_COMPUTE_KEYPAIR("删除云主机密钥对", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.DELETE),
    DETACH_COMPUTE_KEYPAIR("卸载云主机密钥对", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.DETACH),
    ATTACH_COMPUTE_KEYPAIR("挂载云主机密钥对", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.ATTACH),
    /**********************存储-云硬盘*************************/
    CREATE_STORAGE_DISK("创建云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.CREATE),
    UPDATE_STORAGE_DISK("修改云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.UPDATE),
    DELETE_STORAGE_DISK("删除云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.DELETE),
    RESIZE_STORAGE_DISK("扩容云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.UPDATE),
    ATTACH_STORAGE_DISK("挂载云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.ATTACH),
    DETACH_STORAGE_DISK("卸载云硬盘", ModuleType.STORAGE, ResourceType.CMDB_DISK_RES, OperationType.DETACH),
    /**********************存储-快照*************************/
    CREATE_STORAGE_SNAPSHOT("创建云硬盘快照", ModuleType.STORAGE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.CREATE),
    UPDATE_STORAGE_SNAPSHOT("修改云硬盘快照", ModuleType.STORAGE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.UPDATE),
    DELETE_STORAGE_SNAPSHOT("删除云硬盘快照", ModuleType.STORAGE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.DELETE),
    RESET_STORAGE_SNAPSHOT("回滚云硬盘快照", ModuleType.STORAGE, ResourceType.CMDB_SNAPSHOT_RES, OperationType.RESET),
    /**********************网络-VPC*************************/
    CREATE_NEUTRON_VPC("创建网络VPC", ModuleType.NEUTRON, ResourceType.CMDB_VPC_RES, OperationType.CREATE),
    UPDATE_NEUTRON_VPC("修改网络VPC", ModuleType.NEUTRON, ResourceType.CMDB_VPC_RES, OperationType.UPDATE),
    DELETE_NEUTRON_VPC("删除网络VPC", ModuleType.NEUTRON, ResourceType.CMDB_VPC_RES, OperationType.DELETE),
    REFRESH_NEUTRON_VPC("刷新网络VPC", ModuleType.NEUTRON, ResourceType.CMDB_VPC_RES, OperationType.REFRESH),
    /**********************网络-子网*************************/
    CREATE_NEUTRON_SUBNET("创建网络子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.CREATE),
    UPDATE_NEUTRON_SUBNET("修改网络子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.UPDATE),
    DELETE_NEUTRON_SUBNET("删除网络子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.DELETE),
    REFRESH_NEUTRON_SUBNET("刷新网络子网", ModuleType.NEUTRON, ResourceType.CMDB_SUBNET_RES, OperationType.REFRESH),
    CREATE_NEUTRON_VSWITCH("创建交换机", ModuleType.NEUTRON, ResourceType.CMDB_VSWITCH_RES, OperationType.CREATE),
    UPDATE_NEUTRON_VSWITCH("修改交换机", ModuleType.NEUTRON, ResourceType.CMDB_VSWITCH_RES, OperationType.UPDATE),
    DELETE_NEUTRON_VSWITCH("删除交换机", ModuleType.NEUTRON, ResourceType.CMDB_VSWITCH_RES, OperationType.DELETE),
    /**********************网络-弹性IP*************************/
    CREATE_NEUTRON_EIP("创建弹性IP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.CREATE),
    UPDATE_NEUTRON_EIP("修改弹性IP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.UPDATE),
    DELETE_NEUTRON_EIP("删除弹性IP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.DELETE),
    BIND_NEUTRON_EIP("绑定弹性IP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.BIND),
    UNBIND_NEUTRON_EIP("解绑弹性IP", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.UNBIND),
    QUERY_NEUTRON_SEGMENT("获取网段", ModuleType.NEUTRON, ResourceType.CMDB_EIP_RES, OperationType.QUERY),
    /**********************网络-NAT网关*************************/
    CREATE_NEUTRON_NAT("创建NAT网关", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.CREATE),
    UPDATE_NEUTRON_NAT("修改NAT网关", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.UPDATE),
    DELETE_NEUTRON_NAT("删除NAT网关", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.DELETE),
    DELETE_NEUTRON_NAT_ENTRY("删除SNAT/DNAT网关", ModuleType.NEUTRON, ResourceType.CMDB_NAT_RES, OperationType.DELETE),
    /**********************网络-路由表*************************/
    CREATE_NEUTRON_ROUTE("创建路由表", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.CREATE),
    UPDATE_NEUTRON_ROUTE("修改路由表", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.UPDATE),
    DELETE_NEUTRON_ROUTE("删除路由表", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.DELETE),
    BIND_NEUTRON_ROUTE("绑定路由表", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.BIND),
    UNBIND_NEUTRON_ROUTE("解绑路由表", ModuleType.NEUTRON, ResourceType.CMDB_ROUTE_RES, OperationType.UNBIND),
    /**********************计算-物理机*************************/
    ADD_COMPUTE_HOST("添加主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.CREATE),
    STOP_COMPUTE_HOST("关闭主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.STOP),
    CONNECT_COMPUTE_HOST("连接主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.CONNECT),
    DISCONNECT_COMPUTE_HOST("断开主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DISCONNECT),
    ENTER_COMPUTE_HOST("进入维护模式", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.ENTER),
    EXIT_COMPUTE_HOST("退出维护模式", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.LEAVE),
    REMOVE_COMPUTE_HOST("移除主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.LEAVE),
    REBOOT_COMPUTE_HOST("重启主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.LEAVE),
    QUERY_COMPUTE_HOST_TOTAL("主机统计信息", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    QUERY_COMPUTE_INSTANCE_PASSWORD("获取云主机密码", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    QUERY_COMPUTE_TEMPLATE("主机模版信息", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    /**********************镜像操作*************************/
    DELETE_COMPUTE_TEMPLATE("删除镜像", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    DELETE_COMPUTE_IMAGE("删除镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.DELETE),
    CREATE_COMPUTE_IMAGE("创建镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.CREATE),
    UPDATE_COMPUTE_IMAGE("修改镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.UPDATE),
    COPY_COMPUTE_IMAGE("复制镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.UPDATE),
    EXPORT_COMPUTE_IMAGE("导出镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.UPDATE),
    UPDATE_COMPUTE_IMAGE_PERMISSION("管理镜像共享权限", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.UPDATE),
    QUERY_COMPUTE_IMAGE("查询镜像", ModuleType.COMPUTE, ResourceType.CMDB_IMAGE_RES, OperationType.QUERY),
    /**********************存储操作*************************/
    START_STORAGE_POOL("启动存储池", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.START),
    STOP_STORAGE_POOL("停止存储池", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.STOP),
    DELETE_STORAGE_POOL("删除存储池", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.DELETE),
    CREATE_STORAGE_FILE("创建存储文件", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.CREATE),
    DELETE_VM_TEMPLATE("删除虚拟机模板", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.DELETE),
    CREATE_VM_BLOCK_DEVICE("创建块设备", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.DELETE),
    DELETE_VM_BLOCK_DEVICE("删除块设备", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.DELETE),
    UPDATE_VM_BLOCK_DEVICE("扩容块设备", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.DELETE),
    QUERY_DATA_POOL("查询数据池列表", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.QUERY),
    QUERY_HOST_GROUP("查询主机组列表", ModuleType.STORAGE, ResourceType.CMDB_STORAGE_POOL_RES, OperationType.QUERY),
    QUERY_NETWORK_PORT_PROFILE("查询网络策略模板列表", ModuleType.NEUTRON, ResourceType.CMDB_VSWITCH_RES, OperationType.QUERY),
    CREATE_VM_VIRTUAL_SWITCH("创建虚拟交换机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    DELETE_VM_VIRTUAL_SWITCH("删除虚拟交换机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    QUERY_NETWORK_OUTPUT("获取网络出口", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.QUERY),
    MOVE_HOST("移入主机", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),
    SCAN_HOST_DATASTORE("扫描数据存储", ModuleType.COMPUTE, ResourceType.CMDB_INSTANCE_RES, OperationType.DELETE),


    /**********************算力平台*************************/
    FETCH_COMPUTE_CAPACITY_RESOURCE("同步全部计算容量信息", ModuleType.CA, ResourceType.CA_CLOUD_POOL_PERFORMANCE, OperationType.FETCH),
    FETCH_COMPUTE_CAPACITY_RESOURCE_GPU("同步全部计算容量信息-GPU", ModuleType.CA, ResourceType.CA_CLOUD_POOL_PERFORMANCE_GPU, OperationType.FETCH),
    FETCH_IMAGE("同步镜像", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.FETCH),
    FETCH_TAG("同步标签", ModuleType.CA, ResourceType.CA_TASK_TAG, OperationType.FETCH),
    FETCH_ALGO("同步算法", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.FETCH),
    FETCH_MODEL("同步模型", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.FETCH),
    FETCH_DATASET("同步数据集", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.FETCH),
    FETCH_RESOURCE_GROUP("同步资源组", ModuleType.CA, ResourceType.CA_RESOURCE_GROUP, OperationType.FETCH),
    FETCH_DEV_ENV("同步开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.FETCH),
    FETCH_DEV_ENV_PERF("同步开发环境性能", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.FETCH),
    FETCH_TASK("同步推理任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.FETCH),
    FETCH_TASK_PERF("同步推理任务性能", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.FETCH),
    FETCH_FINE_TASK("同步调优任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.FETCH),
    FETCH_FINE_TASK_PERF("同步调优任务性能", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.FETCH),
    FETCH_File("同步文件", ModuleType.CA, ResourceType.CA_KEY_PAIR, OperationType.FETCH),
    FETCH_KEYPAIR("同步密钥对", ModuleType.CA, ResourceType.CA_KEY_PAIR, OperationType.FETCH),
    FETCH_FOLDER("获取文件夹下文件", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.FETCH),
    FETCH_PROPERTY_FOLDER("获取资产下文件夹下文件", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.FETCH),
    /***********开发机操作接口************************/
    CREATE_DEV_ENV("创建开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.CREATE),
    START_DEV_ENV("启动开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.START),
    OPEN_DEV_ENV("打开开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.START),
    GET_LOG("查看日志", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.START),
    GET_POD("查看容器", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.START),
    STOP_DEV_ENV("停止开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.STOP),
    DELETE_DEV_ENV("删除开发环境", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.DELETE),
    COMPLETION("对话", ModuleType.CA, ResourceType.CA_DEV_ENV, OperationType.CREATE),
    /***********模型操作接口************************/
    CREATE_MODEL("创建模型", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.CREATE),
    DELETE_MODEL("删除模型", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.DELETE),
    CREATE_MODEL_VERSION("创建模型版本", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.CREATE),
    UPDATE_MODEL_VERSION("修改模型版本", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.UPDATE),
    DELETE_MODEL_VERSION("删除模型版本", ModuleType.CA, ResourceType.CA_PROPERTY_MODEL_V, OperationType.DELETE),
    /***********数据集操作接口************************/
    CREATE_DATASET("创建数据集", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.CREATE),
    DELETE_DATASET("删除数据集", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.DELETE),
    CREATE_DATASET_VERSION("创建数据集版本", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.CREATE),
    UPDATE_DATASET_VERSION("修改数据集版本", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.UPDATE),
    DELETE_DATASET_VERSION("删除数据集版本", ModuleType.CA, ResourceType.CA_PROPERTY_DATASET_V, OperationType.DELETE),
    /***********镜像操作接口************************/
    CREATE_IMAGE("创建镜像", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.CREATE),
    DELETE_IMAGE("删除镜像", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.DELETE),
    CREATE_IMAGE_VERSION("创建镜像版本", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.CREATE),
    UPDATE_IMAGE_VERSION("修改镜像版本", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.UPDATE),
    DELETE_IMAGE_VERSION("删除镜像版本", ModuleType.CA, ResourceType.CA_PROPERTY_IMAGE_V, OperationType.DELETE),
    /***********算法操作接口************************/
    CREATE_ALGORITHM("创建算法", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.CREATE),
    DELETE_ALGORITHM("删除算法", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.DELETE),
    CREATE_ALGORITHM_VERSION("创建算法版本", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.CREATE),
    UPDATE_ALGORITHM_VERSION("修改算法版本", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.UPDATE),
    DELETE_ALGORITHM_VERSION("删除算法版本", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.DELETE),
    CREATE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.CREATE),
    UPDATE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.UPDATE),
    DELETE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.CA, ResourceType.CA_PROPERTY_ALGO_V, OperationType.DELETE),
    /***********资产文件操作接口************************/
    CREATE_PROPERTY_FOLDER("创建文件夹", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.CREATE),
    DELETE_PROPERTY_FILE("删除文件", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.DELETE),
    READ_PROPERTY_FILE("读取文件内容", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.QUERY),
    UPDATE_PROPERTY_FILE("修改文件内容", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPDATE),
    QUERY_PROPERTY_FILE_URL("查询文件URL", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.QUERY),
    DOWNLOAD_PROPERTY_FILE("下载文件", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.GET),
    UPLOAD_PROPERTY_FILE("上传文件（夹）", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.CREATE),
    CHECK_FILE_EXIST("文件（夹）是否存在", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.QUERY),
    /***********文件操作接口************************/
    CREATE_FOLDER("创建文件夹", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.CREATE),
    DELETE_FILE("删除文件（夹）", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.DELETE),
    FIND_SHARE_FILE("文件（夹）是否被共享", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.QUERY),
    RENAME_FILE("重命名文件（夹）", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPDATE),
    DOWNLOAD_FILE("下载文件", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.DOWNLOAD),
    DOWNLOAD_FOLDER("下载文件夹", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.DOWNLOAD),
    UPLOAD_SMALL_FILE("小文件上传", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPLOAD),
    UPLOAD_LARGE_FILE("大文件上传", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPLOAD),
    MERGE_PROPERTY_FILE("资产文件上传合并", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPLOAD),
    MERGE_LARGE_FILE("大文件上传合并", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.UPLOAD),
    DOWNLOAD_PUBLIC_FILE("下载文件（公共文件）", ModuleType.CA, ResourceType.CA_PROPERTY_FILE_V, OperationType.DOWNLOAD),
    /***********大模型调优任务************************/
    CREATE_FINE_TUNE_TASK("创建大模型调优任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.CREATE),
    STOP_FINE_TUNE_TASK("停止大模型调优任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.STOP),
    START_FINE_TUNE_TASK("启动大模型调优任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.START),
    DELETE_MODEL_TASK("删除大模型调优任务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_FINE_TUNE_TASK, OperationType.DELETE),
    /***********推理服务************************/
    STOP_REASON_TASK("停止推理服务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.STOP),
    START_REASON_TASK("启动推理服务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.START),
    CREATE_REASON_TASK("创建推理服务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.CREATE),
    DELETE_REASON_TASK("删除推理服务", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.DELETE),
    GET_FINE_TUNE_TASK_POD_LOG("获取大模型调优任务日志", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.QUERY),
    GET_REASON_TASK_POD_LOG("获取推理服务日志", ModuleType.CA, ResourceType.CA_LARGE_MODEL_REASONING_TASK, OperationType.QUERY);

    public final String cname;
    public final ModuleType moduleType;
    public final ResourceType resourceType;
    public final OperationType operationType;

    ActionType(String cname, ModuleType moduleType, ResourceType resourceType, OperationType operationType) {
        this.cname = cname;
        this.moduleType = moduleType;
        this.resourceType = resourceType;
        this.operationType = operationType;
    }

    public String value() {
        return StrUtil.upperFirst(StrUtil.toCamelCase(this.name()));
    }


    public String getType() {
        return moduleType.cname + "/" + resourceType.cname;
    }

    public static ActionType fromValue(String value) {
        for (ActionType type : values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }

    /**
     * 根据驼峰命名转换成枚举值
     *
     * @param value 传入的驼峰常量字符串
     * @return ActionType
     */
    public static ActionType fromCamelValue(String value) {
        value = StrUtil.toUnderlineCase(value);
        try {
            return fromValue(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

}
