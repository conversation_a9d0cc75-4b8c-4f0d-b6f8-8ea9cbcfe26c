package com.futong.gemini.sdk.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

@Data
public abstract class GeminiRequestBody<T extends JSONObject> extends JSONObject {

    //gourd调度采集器信息
    private GourdInfo gourd = new GourdInfo();

    public GeminiRequestBody(JSONObject json) {
        super(json);
        if (json.containsKey("gourd")) {
            this.gourd = json.getJSONObject("gourd").toJavaObject(GourdInfo.class);
        }
    }
}
