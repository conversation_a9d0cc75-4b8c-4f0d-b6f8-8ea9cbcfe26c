package com.futong.gemini.sdk.model.constant;

/**
 * 资源类型
 */
public enum ResourceType {
    //物理机
    CMDB_HOST_RES("物理机"),
    //云主机
    CMDB_INSTANCE_RES("云主机"),
    //磁盘
    CMDB_DISK_RES("磁盘"),
    //安全组
    CMDB_SECURITYGROUP_RES("安全组"),
    //专有网络
    CMDB_VPC_RES("专有网络"),
    //弹性网络
    CMDB_EIP_RES("弹性网络"),
    //块存储
    CMDB_BLOCK_STORAGE_RES("块存储"),
    //快照
    CMDB_SNAPSHOT_RES("快照"),
    //规格
    CMDB_FLAVOR("规格"),
    //子网
    CMDB_SUBNET_RES("子网"),
    //镜像
    CMDB_IMAGE_RES("镜像"),
    //IP
    CMDB_IP_RES("IP"),
    //负载均衡
    CMDB_LOADBALANCE_RES("负载均衡"),
    //秘钥对
    CMDB_KEYPAIR_RES("秘钥对"),
    //网卡
    CMDB_NETCARD_RES("网卡"),
    //NAT网关
    CMDB_NAT_RES("NAT网关"),
    //存储池
    CMDB_STORAGE_POOL_RES("存储池"),
    //虚拟局域网
    CMDB_VLAN_RES("虚拟局域网"),
    //虚拟交换机
    CMDB_VSWITCH_RES("虚拟交换机"),
    //操作系统
    CMDB_OS_RES("操作系统"),
    //路由
    CMDB_ROUTE_RES("路由"),
    //对象存储
    CMDB_BUCKET_RES("对象存储"),
    //监控告警
    CMP_ALARM_RES("监控告警"),
    //安全组规则
    CMDB_SECURITYGROUP_RULE("安全组规则"),
    //Kafka
    CMDB_KAFKA("Kafka"),
    //Kubernetes
    CMDB_KUBERNETES("Kubernetes"),
    //PolarDB-X
    CMDB_POLARDBX("PolarDB-X"),
    //MongoDB
    CMDB_MONGO("MongoDB"),
    //Rds
    CMDB_RDS("Rds"),
    //Redis
    CMDB_REDIS("Redis"),
    //RocketMQ
    CMDB_ROCKETMQ("RocketMQ"),
    //账单
    CMP_BILL_RES("账单"),
    //事件
    CMP_EVENT_RES("事件"),
    //NAT网关规则条目
    CMDB_NAT_ENTRY_RES("NAT网关规则条目"),
    //存储设备
    CMDB_STORAGE_UNIT_RES("存储设备"),
    //存储资源
    CMDB_STORAGE_RESOURCE_RES("存储资源"),
    //存储容器
    CMDB_STORAGE_CONTAINER_RES("存储容器"),
    //上行链路组
    CMDB_UPLINK_PORT_RES("上行链路组"),
    //GPU模型信息
    CA_CLOUD_POOL_PERFORMANCE_GPU("GPU模型信息"),
    //资源池
    CA_CLOUD_POOL_PERFORMANCE("资源池"),
    //开发环境
    CA_DEV_ENV("开发环境"),
    //资源组
    CA_RESOURCE_GROUP("资源组"),
    //密钥对
    CA_KEY_PAIR("密钥对"),
    //资产标签
    CA_TASK_TAG("资产标签"),
    //资产数据集
    CA_PROPERTY_DATASET_V("资产数据集"),
    //资产模型
    CA_PROPERTY_MODEL_V("资产模型"),
    //资产镜像
    CA_PROPERTY_IMAGE_V("资产镜像"),
    //资产算法
    CA_PROPERTY_ALGO_V("资产算法"),
    //资产文件
    CA_PROPERTY_FILE_V("资产文件"),
    //推理任务
    CA_LARGE_MODEL_REASONING_TASK("推理任务"),
    //调优任务
    CA_LARGE_MODEL_FINE_TUNE_TASK("调优任务"),
    //账号
    ACCOUNT("账号"),
    //调度
    DISPATCH("调度"),
    //在线API
    API("在线API");

    public final String cname;

    ResourceType(String cname) {
        this.cname = cname;
    }

    public String value() {
        return name().toLowerCase();
    }

    public static ResourceType fromValue(String value) {
        for (ResourceType type : values()) {
            if (type.value().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
}
