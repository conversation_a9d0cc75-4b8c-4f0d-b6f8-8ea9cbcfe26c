<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>public-parent</artifactId>
        <groupId>com.futong</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <groupId>com.futong.gemini</groupId>
    <artifactId>gemini</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <yunjing.version>v4.0.6</yunjing.version>
        <yuntu.version>v4.0.6.atlas</yuntu.version>
    </properties>
    <modules>
        <module>gemini-sdk</module>
        <module>gemini-server</module>
        <module>gemini-plugins</module>
        <module>gemini-model</module>
    </modules>

</project>