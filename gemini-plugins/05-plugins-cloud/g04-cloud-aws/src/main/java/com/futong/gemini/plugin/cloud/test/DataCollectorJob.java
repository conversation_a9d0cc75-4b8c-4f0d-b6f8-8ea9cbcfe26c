package com.futong.gemini.plugin.cloud.test;

import com.alibaba.fastjson.JSON;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.DescribeInstancesRequest;
import software.amazon.awssdk.services.ec2.model.DescribeInstancesResponse;

public class DataCollectorJob {


    private void collectAwsData() {
        Ec2Client ec2 = Ec2Client.builder()
                .region(Region.US_EAST_1)
                .credentialsProvider(() -> AwsBasicCredentials.create("sdf", "fsdf"))
                .build();
        DescribeInstancesRequest request = DescribeInstancesRequest.builder().build();
        DescribeInstancesResponse response = ec2.describeInstances(request);
        response.reservations().forEach(reservation -> {
            reservation.instances().forEach(instance -> {
                System.out.println(JSON.toJSONString(instance));
            });
        });
    }

    private void collectH3CData() {
        System.out.println("collectH3CData" + System.currentTimeMillis());
        // Implement H3C data collection logic
    }

    private void collectAliData() {
        // Implement Ali data collection logic
    }
}
