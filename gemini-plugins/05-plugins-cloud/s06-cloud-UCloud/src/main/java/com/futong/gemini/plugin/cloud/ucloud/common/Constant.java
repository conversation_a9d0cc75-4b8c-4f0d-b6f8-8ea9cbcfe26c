package com.futong.gemini.plugin.cloud.ucloud.common;

import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.futong.common.utils.Entry.E2;
import static com.futong.common.utils.Entry.E3;

public class Constant {

    public static Map<String, E3<String, E2<String, String>[], String>> metrics = new HashMap<>();
    private static E2<String, String>[] metricType = new E2[]{
            new E2("Average", "average"),
            new E2("Maximum", "max"),
            new E2("Minimum", "min"),
            new E2("Value", "value"),
            new E2("Sum", "sum"),
    };

    static {
        metrics.put("vm_cpu_utilization", new E3("cpuUsage", metricType, "%"));
        metrics.put("vm_memory_utilization", new E3("memUsage", metricType, "%"));
        metrics.put("user_vm_disk_utilization", new E3("diskUsage", metricType, "%"));
        metrics.put("vm_disk_iops_read_total", new E3("diskRead", metricType, "次/s"));
        metrics.put("vm_disk_iops_write_total", new E3("diskWrite", metricType, "次/s"));
        metrics.put("vm_nic_in_bytes_total", new E3("netIn", metricType, "Bytes/s"));
        metrics.put("vm_nic_out_bytes_total", new E3("netOut", metricType, "Bytes/s"));
//        metrics.put("VPC_PublicIP_InternetInRate", new E3("ipInRate", metricType, "bit/s"));
//        metrics.put("VPC_PublicIP_InternetOutRate", new E3("ipOutRate", metricType, "bit/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMapping = new HashMap<>();

    static {
        perfMapping.put("vm_cpu_utilization", PerfInfoBean::setCpuUsage);
        perfMapping.put("vm_memory_utilization", PerfInfoBean::setMemUsage);
        perfMapping.put("user_vm_disk_utilization", PerfInfoBean::setDiskUsage);
        perfMapping.put("vm_disk_iops_read_total", PerfInfoBean::setDiskRead);
        perfMapping.put("vm_disk_iops_write_total", PerfInfoBean::setDiskWrite);
        perfMapping.put("vm_nic_in_bytes_total", PerfInfoBean::setNetIn);
        perfMapping.put("vm_nic_out_bytes_total", PerfInfoBean::setNetOut);
    }

}
