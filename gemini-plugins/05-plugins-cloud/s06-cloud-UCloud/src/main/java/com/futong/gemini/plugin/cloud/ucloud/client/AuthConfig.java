package com.futong.gemini.plugin.cloud.ucloud.client;

import lombok.Getter;

// 认证信息封装
@Getter
public class AuthConfig {
    private final String publicKey;
    private final String privateKey;

    private AuthConfig(Builder builder) {
        this.publicKey = builder.publicKey;
        this.privateKey = builder.privateKey;
    }

    public static class Builder {
        private String publicKey;
        private String privateKey;

        public Builder publicKey(String publicKey) {
            this.publicKey = publicKey;
            return this;
        }

        public Builder privateKey(String privateKey) {
            this.privateKey = privateKey;
            return this;
        }


        public AuthConfig build() {
            validate();
            return new AuthConfig(this);
        }

        private void validate() {
            if (publicKey == null || privateKey == null ) {
                throw new IllegalArgumentException("Missing auth parameters publicKey privateKey");
            }
        }
    }

}