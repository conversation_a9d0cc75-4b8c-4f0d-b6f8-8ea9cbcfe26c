package com.futong.gemini.plugin.cloud.ucloud.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class FileStorageService {

    /**
     * 创建文件存储
     * @param request
     * @return
     */
    public static BaseResponse createFs(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject storage = request.getBody().getCloud().getJSONObject("storageClusters");
            request.getBody().getCloud().remove("storageClusters");
            request.getBody().getCloud().put("DiskSetType",storage.getString("DiskSetType"));
            request.getBody().getCloud().put("DiskSpace",storage.getInteger("DiskSpace"));
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateFS", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建文件存储失败!"), e);
        }
    }

    /**
     * 删除文件存储
     * @param request
     * @return
     */
    public static BaseResponse deleteFs(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DeleteFS", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件存储失败!"), e);
        }
    }

    /**
     * 扩容文件存储
     * @param request
     * @return
     */
    public static BaseResponse resizeFs(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("UpgradeFS", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("扩容文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("扩容文件存储失败!"), e);
        }
    }

    /**
     * 创建文件存储文件目录
     * @param request
     * @return
     */
    public static BaseResponse createFsFolder(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateFSDir", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建文件存储文件目录失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建文件存储文件目录失败!"), e);
        }
    }

    /**
     * 删除文件存储目录文件
     * @param request
     * @return
     */
    public static BaseResponse deleteFsFile(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("DeleteFSFile", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除文件存储目录文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件存储目录文件失败!"), e);
        }
    }
}
