package com.futong.gemini.plugin.cloud.ucloud.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class SecurityGroupService {

    /**
     * 创建安全组
     * @param request
     * @return
     */
    public static BaseResponse createSecurityGroup(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray rules = request.getBody().getCloud().getJSONArray("rules");
            request.getBody().getCloud().remove("rules");
            List<String> ruleList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(rules)){
                for (int i = 0; i < rules.size(); i++) {
                    JSONObject item = rules.getJSONObject(i);
                    //direction:0 表示出站，1 表示入站
                    String rule = item.getString("protocol")+"|"+item.getString("port")+"|"+item.getString("ip")+"|"+item.getString("movement")+"|"+item.getString("priority")+"|"+item.getString("direction")+"|";
                    if(StrUtil.isNotEmpty(item.getString("remark"))){
                        rule += item.getString("remark");
                    }
                    ruleList.add(rule);
                }
                request.getBody().getCloud().put("Rule", ruleList);
            }
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query0(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateSecurityGroup", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建安全组失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组失败!"), e);
        }
    }

    /**
     * 删除安全组
     * @param request
     * @return
     */
    public static BaseResponse deleteSecurityGroup(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DeleteSecurityGroup", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除安全组失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组失败!"), e);
        }
    }

    /**
     * 绑定安全组
     * @param request
     * @return
     */
    public static BaseResponse bindSecurityGroup(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("BindSecurityGroup", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("绑定安全组失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定安全组失败!"), e);
        }
    }

    /**
     * 获取安全组资源
     * @param request
     * @return
     */
    public static BaseResponse querySecurityGroupResource(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("DescribeSecurityGroupResource", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("解绑安全组失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑安全组失败!"), e);
        }
    }

    /**
     * 解绑安全组
     * @param request
     * @return
     */
    public static BaseResponse unBindSecurityGroup(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("UnBindSecurityGroup", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("解绑安全组失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑安全组失败!"), e);
        }
    }

    /**
     * 创建安全组规则
     * @param request
     * @return
     */
    public static BaseResponse createSecurityGroupRule(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray rules = request.getBody().getCloud().getJSONArray("rules");
            request.getBody().getCloud().remove("rules");
            List<String> ruleList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(rules)){
                for (int i = 0; i < rules.size(); i++) {
                    JSONObject item = rules.getJSONObject(i);
                    //direction:0 表示出站，1 表示入站
                    String rule = item.getString("protocol")+"|"+item.getString("port")+"|"+item.getString("ip")+"|"+item.getString("movement")+"|"+item.getString("priority")+"|"+item.getString("direction")+"|";
                    if(StrUtil.isNotEmpty(item.getString("remark"))){
                        rule += item.getString("remark");
                    }
                    ruleList.add(rule);
                }
                request.getBody().getCloud().put("Rules", ruleList);
            }
            Map<String, String> query = FTHttpUtils.query0(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("CreateSecurityGroupRule", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建安全组规则失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组规则失败!"), e);
        }
    }

    /**
     * 创建安全组规则
     * @param request
     * @return
     */
    public static BaseResponse updateSecurityGroupRule(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray rules = request.getBody().getCloud().getJSONArray("rules");
            String ruleId = request.getBody().getCloud().getString("ruleId");
            request.getBody().getCloud().remove("ruleId");
            request.getBody().getCloud().remove("rules");
            List<String> ruleList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(rules)){
                for (int i = 0; i < rules.size(); i++) {
                    JSONObject item = rules.getJSONObject(i);
                    //direction:0 表示出站，1 表示入站
                    String rule = item.getString("protocol")+"|"+item.getString("port")+"|"+item.getString("ip")+"|"+item.getString("movement")+"|"+item.getString("priority")+"|"+item.getString("direction")+"|"+ruleId+"|";
                    if(StrUtil.isNotEmpty(item.getString("remark"))){
                        rule += item.getString("remark");
                    }
                    ruleList.add(rule);
                }
                request.getBody().getCloud().put("Rules", ruleList);
            }
            Map<String, String> query = FTHttpUtils.query0(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("UpdateSecurityGroupRule", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建安全组规则失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组规则失败!"), e);
        }
    }

    /**
     * 删除安全组规则
     * @param request
     * @return
     */
    public static BaseResponse deleteSecurityGroupRule(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DeleteSecurityGroupRule", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除安全组规则失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组规则失败!"), e);
        }
    }
}
