package com.futong.gemini.plugin.cloud.ucloud.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class StorageService {

    /**
     * 创建云硬盘
     * @param request
     * @return
     */
    public static BaseResponse createDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateDisk", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建云硬盘失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云硬盘失败!"), e);
        }
    }

    /**
     * 删除云硬盘
     * @param request
     * @return
     */
    public static BaseResponse deleteDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            List<String> diskIds = (List)request.getBody().getCloud().get("DiskIDs");
            request.getBody().getCloud().remove("DiskIDs");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            diskIds.forEach(diskId->{
                query.put("DiskID", diskId);
                client.doActionJSON("DeleteDisk", query);
            });
            return BaseResponse.SUCCESS.of("删除成功");
        }catch (Exception e){
            log.error("创建云硬盘失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云硬盘失败!"), e);
        }
    }

    /**
     * 挂载云硬盘
     * @param request
     * @return
     */
    public static BaseResponse ecsAttachDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("AttachDisk", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("挂载云硬盘失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("挂载云硬盘失败!"), e);
        }
    }

    /**
     * 卸载云硬盘
     * @param request
     * @return
     */
    public static BaseResponse ecsDetachDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("DetachDisk", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("卸载云硬盘失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("卸载云硬盘失败!"), e);
        }
    }

    /**
     * 扩容云硬盘
     * @param request
     * @return
     */
    public static BaseResponse resizeDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("UpgradeDisk", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("扩容云硬盘失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("扩容云硬盘失败!"), e);
        }
    }


    /**
     * 创建快照
     * @param request
     * @return
     */
    public static BaseResponse createSnapshot(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateSnapshot", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建云硬盘快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云硬盘快照失败!"), e);
        }
    }

    /**
     * 删除快照
     * @param request
     * @return
     */
    public static BaseResponse deleteSnapshot(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("DeleteSnapshot", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除快照失败!"), e);
        }
    }

    /**
     * 快照回滚
     * @param request
     * @return
     */
    public static BaseResponse resetDisk(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("RollbackSnapshot", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("回滚快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("回滚快照失败!"), e);
        }
    }
}
