package com.futong.gemini.plugin.cloud.ucloud.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.util.Arrays;
import java.util.Map;

@Slf4j
public class UCloudClient {
    private final CloseableHttpClient httpClient;
    private final ConnectionConfig config;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public UCloudClient(ConnectionConfig config) {
        this.config = config;
        this.httpClient = createHttpClient(config);
    }

    private CloseableHttpClient createHttpClient(ConnectionConfig config) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(config.getConnectTimeout())
                .setSocketTimeout(config.getSocketTimeout())
                .build();

        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .build();
    }


    // 通用请求执行方法
    private String executeRequest(HttpRequestBase request)  {
//        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity());
//            if (response.getStatusLine().getStatusCode() == 401) {
//                refreshToken();
//                return executeRequest(request); // 重试请求
//            }
            if (response.getStatusLine().getStatusCode() < 200 ||
                    response.getStatusLine().getStatusCode() >= 300) {
                throw new RuntimeException("API request failed: " + responseBody);
            }
            return responseBody;
        }catch (Exception e){
            log.error("网络请求失败:{}", e.getMessage());
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of(e.getMessage()),e);
        }
    }

    public static String getSignature(Map<String, String> query, AuthConfig auth) {
        query.put("PublicKey", auth.getPublicKey());
        String[] keys = query.keySet().toArray(new String[0]);
        Arrays.sort(keys);//排序
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key).append(query.get(key));
        }
        sb.append(auth.getPrivateKey());
        //使用SHA1加密
        return DigestUtil.sha1Hex(sb.toString());
    }

    // GET请求
    public String doGet(String apiPath, Map<String, String> query) {
        String signature = getSignature(query, config.getAuthConfig());
        query.put("Signature", signature);
        String url = config.getEndpoint() + apiPath;
        if (CollUtil.isNotEmpty(query)) {
            url += "?" + FTHttpUtils.toFormString(query);
        }
        return executeRequest(new HttpGet(url));
    }

    // POST JSON请求
    public String doPostJson(String apiPath, Object requestBody) throws Exception {
        HttpPost request = new HttpPost(config.getEndpoint() + apiPath);
        String json = objectMapper.writeValueAsString(requestBody);
        request.setEntity(new StringEntity(json));
        return executeRequest(request);
    }

    // 响应解析工具方法
    public <T> T parseResponse(String json, Class<T> clazz) throws Exception {
        return objectMapper.readValue(json, clazz);
    }

    public String doAction(String action, Map<String, ?> params) {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM,  e);
        }
        query.put("Action", action);
        String result = doGet("/api", query);
        Integer retCode = JSONPath.read(result, "$.RetCode", Integer.class);
        if (retCode != null && retCode != 0) {
            log.error("请求失败:{}", result);
            String message = JSONPath.read(result, "$.Message", String.class);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of(message));
        }
        return result;
    }

    public JSONObject doActionJSON(String action, Map<String, ?> params) {
        String result = doAction(action, params);
        return JSONObject.parseObject(result);
    }

    public JSONArray doActionJSONInfos(String action, Map<String, ?> params) {
        JSONObject result = doActionJSON(action, params);
        return result.getJSONArray("Infos");
    }

}
