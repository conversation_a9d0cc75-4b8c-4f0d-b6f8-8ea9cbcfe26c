package com.futong.gemini.plugin.cloud.ucloud.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PlatformAccountService {

    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            client.doAction("DescribeRegion",null);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    public static void main(String[] args) {
        JSONObject json= JSONObject.parseObject("{\n" +
                "\t\"action\": \"AuthPlatformAccount\",\n" +
                "\t\"body\": {\n" +
                "\t\t\"auth\": {\n" +
                "\t\t\t\"cmpId\": \"6756a21f92e953e3debdbefd58509a06\",\n" +
                "\t\t\t\"username\": \"P0EtRyUqFjQSgZwW0AIJwmFgd1EuWv1u4abKvPS4O9QHnYv_RqtduoqG\",\n" +
                "\t\t\t\"password\": \"z8Oq2m7bEyPq07JNJ8ShHqLkygr1cJnZoCACI1k8OsQWzRJVaboW3MYRS96XY4x8\",\n" +
                "\t\t\t\"protocol\": \"https\",\n" +
                "\t\t\t\"serverIp\": \"console.poc.ucloudstack.com\",\n" +
                "\t\t\t\"serverPort\": 443\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}");
        BaseCloudRequest  request = new BaseCloudRequest(json);
        BaseResponse response = authAccount(request);
        System.out.println(JSONObject.toJSONString(response));
    }

}
