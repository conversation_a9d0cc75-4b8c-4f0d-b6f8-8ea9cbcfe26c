package com.futong.gemini.plugin.cloud.ucloud.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class NatService {

    public static BaseResponse createNatGateway(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateNATGW", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建NAT网关失败!", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建NAT网关失败!"), e);
        }
    }

    public static BaseResponse deleteNatGateway(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("NATGWID", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除NAT网关失败!", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除NAT网关失败!"), e);
        }
    }
}
