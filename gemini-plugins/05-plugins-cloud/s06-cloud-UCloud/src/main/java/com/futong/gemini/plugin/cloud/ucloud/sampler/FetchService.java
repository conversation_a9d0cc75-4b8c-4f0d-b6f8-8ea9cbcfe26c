package com.futong.gemini.plugin.cloud.ucloud.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.common.utils.PageUtils;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.entity.CmdbBucketStorage;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.ucloud.common.Constant;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class FetchService {
    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) return response;
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) return response;
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("Offset", (t - 1) * 50);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }
    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONArray regions = client.doActionJSONInfos("DescribeRegion", query);
            Map<Class, List> data = Convert.convertRegion(request, regions);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取地域失败!"), e);
        }
    }

    public static BaseResponse fetchTenant(BaseCloudRequest request) {
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject tenant = client.doActionJSON("DescribeUser", query);
            Map<Class, List> data = Convert.convertTenant(request, tenant);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    tenant.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取地域失败!"), e);
        }
    }

    /**
     * 同步云主机
     * @param request
     * @return
     */
    public static BaseResponse fetchEcs(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject instance = client.doActionJSON("DescribeVMInstance", query);
            Map<Class, List> data = Convert.convertEcs(request, instance);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    instance.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机失败!"), e);
        }
    }

    /**
     * 同步镜像
     * @param request
     * @return
     */
    public static BaseResponse fetchImage(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject image = client.doActionJSON("DescribeImage", query);
            Map<Class, List> data = Convert.convertImage(request, image);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    image.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取镜像失败!"), e);
        }
    }

    /**
     * 同步VPC
     * @param request
     * @return
     */
    public static BaseResponse fetchVpc(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject vpc = client.doActionJSON("DescribeVPC", query);
            Map<Class, List> data = Convert.convertVpc(request, vpc);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    vpc.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取Vpc失败!"), e);
        }
    }

    /**
     * 同步Subnet
     * @param request
     * @return
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject subnet = client.doActionJSON("DescribeSubnet", query);
            Map<Class, List> data = Convert.convertSubnet(request, subnet);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    subnet.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取Subnet失败!"), e);
        }
    }

    /**
     * 同步网卡
     * @param request
     * @return
     */
    public static BaseResponse fetchNetcard(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject netCard = client.doActionJSON("DescribeNIC", query);
            Map<Class, List> data = Convert.convertNetcard(request, netCard);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    netCard.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取网卡失败!"), e);
        }
    }

    /**
     * 同步EIP
     * @param request
     * @return
     */
    public static BaseResponse fetchEip(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject eip = client.doActionJSON("DescribeEIP", query);
            Map<Class, List> data = Convert.convertEip(request, eip);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    eip.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取Eip失败!"), e);
        }
    }

    /**
     * 同步安全组
     * @param request
     * @return
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject securityGroup = client.doActionJSON("DescribeSecurityGroup", query);
            Map<Class, List> data = Convert.convertSecurityGroup(request, securityGroup);
            if(ObjectUtil.isNotEmpty(data.get(CmdbSecuritygroupRes.class))){
                for (Object obj : data.get(CmdbSecuritygroupRes.class)) {
                    CmdbSecuritygroupRes res = (CmdbSecuritygroupRes) obj;
                    query.put("SGID",res.getOpen_id());
                    try {
                        JSONObject securityGroupResource = client.doActionJSON("DescribeSecurityGroupResource", query);
                        Map<Class, List> ruleMap = Convert.convertSecurityGroupResource(request, securityGroupResource);
                        BaseCloudService.fetchSend(request, ruleMap);
                    }catch (Exception e) {
                        continue;
                    }
                }
            }
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    securityGroup.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取安全组失败!"), e);
        }
    }

    /**
     * 同步磁盘
     * @param request
     * @return
     */
    public static BaseResponse fetchDisk(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject disk = client.doActionJSON("DescribeDisk", query);
            Map<Class, List> data = Convert.convertDisk(request, disk);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    disk.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取磁盘失败!"), e);
        }
    }

    /**
     * 同步快照
     * @param request
     * @return
     */
    public static BaseResponse fetchSnapshot(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject snapshot = client.doActionJSON("DescribeSnapshot", query);
            Map<Class, List> data = Convert.convertSnapshot(request, snapshot);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    snapshot.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取快照失败!"), e);
        }
    }

    /**
     * 同步NAT网关
     * @param request
     * @return
     */
    public static BaseResponse fetchNat(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject nat = client.doActionJSON("DescribeNATGW", query);
            Map<Class, List> data = Convert.convertNat(request, nat);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    nat.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取快照失败!"), e);
        }
    }

    /**
     * 同步NAT网关-
     * @param request
     * @return
     */
    public static BaseResponse fetchNatEntry(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String action = "";
            if("DNAT".equals(request.getBody().getCi().getString("type"))){
                action = "DescribeNATGWPolicy";
            }
            if("SNAT".equals(request.getBody().getCi().getString("type"))){
                action = "DescribeNATGWRule";
            }
            JSONObject natEntry = client.doActionJSON(action, request.getBody());
            Map<Class, List> data = Convert.convertNatEntry(request, natEntry);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    natEntry.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取Nat规则失败!"), e);
        }
    }

    /**
     * 同步负载均衡
     * @param request
     * @return
     */
    public static BaseResponse fetchLoadBalance(BaseCloudRequest request){
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(request.getBody().getCloud());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        try {
            JSONObject elb = client.doActionJSON("DescribeLB", query);
            Map<Class, List> data = Convert.convertLoadBalance(request, elb);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    elb.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取快照失败!"), e);
        }
    }

    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject alert = client.doActionJSON("DescribeAlert", query);
            if (ObjectUtil.isEmpty(alert) || CollUtil.isEmpty(alert.getJSONArray("Infos"))) {
                return BaseResponse.SUCCESS.of("查询告警信息为空!");
            }
            //将获取到的云主机信息转换为CI模型
            List<AlarmInfoBean> listCI = Convert.convertAlarm(request, alert);
            BaseCloudService.toAetMessageAndSend(listCI, "alarm");
            String message = StrUtil.format("本次获取告警信息,页码：{},条数：{},本次获取条数：{}",Integer.valueOf(query.get("Offset"))+1, query.get("Limit"), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && alert.getInteger("TotalCount") .equals(Integer.valueOf(query.get("Limit")))) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("Offset", (Integer.valueOf(query.get("Offset")+1))*50);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }

    }

    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject event = client.doActionJSON("DescribeResourceEvent", query);
            if (ObjectUtil.isEmpty(event) || CollUtil.isEmpty(event.getJSONArray("Infos"))) {
                return BaseResponse.SUCCESS.of("查询事件信息为空!");
            }
            List<EventInfoBean> listCI = Convert.convertEvent(request, event);
            listCI.removeIf(Objects::isNull);
            BaseCloudService.toAetMessageAndSend(listCI, "event");

            String message = StrUtil.format("本次获取事件信息,页码：{},条数：{},本次获取条数：{}", Integer.valueOf(query.get("Offset"))+1, query.get("Limit"), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && event.getInteger("TotalCount") .equals(Integer.valueOf(query.get("Limit")))) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("Offset", (Integer.valueOf(query.get("Offset")+1))*50);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步事件数据失败");
        }
    }

    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("Region")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数Region不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("startTime")) {
            //当前时间-监控数据得统计周期（秒）*1000（毫秒）
            long startTime = Instant.now().getEpochSecond() - NumberUtil.parseLong("300");
            request.getBody().getCloud().put("Start", Long.toString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("End", Instant.now().getEpochSecond() + "");
        }
        if (!request.getBody().getCloud().containsKey("Step")) {
            request.getBody().getCloud().put("Step", "30");
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        if(!searchJsonRequest.containsKey("size")){
            searchJsonRequest.put("size", 50);
        }
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                request.put("async",true);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);
        //设置采集监控数据云主机ID
        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        String instanceIds = instanceMap.keySet().stream().collect(Collectors.joining(","));
        request.getBody().getCloud().put("instanceIds", instanceIds);
        return true;
    }

    public static BaseResponse fetchEcsPerf(BaseCloudRequest request) {
        try {
            //监控请求指标集合
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String instanceIdArr[] = request.getBody().getCloud().getString("instanceIds").split(",");
            request.getBody().getCloud().remove("instanceIds");
            Map<String,String> query = null;
            Map<String,List<JSONArray>> metricMap = new HashMap<>();
            for (String vmId : instanceIdArr) {
                query = new HashMap<>();
                query.put("TargetTypes.0","VM");
                query.put("TargetID",vmId);
                JSONObject result = client.doActionJSON("DescribeMetric", query);
                JSONObject metrics = result.getJSONArray("Metrics").getJSONObject(0);
                JSONArray metricInfos = metrics.getJSONArray("Infos");
                for (int i = 0; i < metricInfos.size(); i++) {
                    JSONObject info = metricInfos.getJSONObject(i);
                    String metricId = info.getString("MetricID");
                    String metric = info.getString("Metric");
                    if(Constant.metrics.containsKey(metricId)){
                        request.getBody().getCloud().put("Query",metric);
                        Map<String, String> metricQuery = FTHttpUtils.query(request.getBody().getCloud());
                        JSONObject perfResult = client.doActionJSON("PrometheusQueryRange", metricQuery);
                        log.info("Prometheus监控数据为:{}", JSON.toJSONString(perfResult));
                        if(perfResult.getJSONObject("data").getJSONArray("result").size() > 0 ){
                            JSONObject dataResult = perfResult.getJSONObject("data").getJSONArray("result").getJSONObject(0);
                            String keyName = vmId+"-"+metricId;
                            List<JSONArray> top3Values = dataResult.getJSONArray("values")
                                    .toJavaList(JSONArray.class)
                                    .stream()
                                    .sorted((a, b) -> Long.compare(b.getLong(0), a.getLong(0)))
                                    .limit(3)
                                    .collect(Collectors.toList());

                            metricMap.put(keyName,top3Values);
                        }
                    }
                }
            }
            Map<String, PerfInfoBean> perfMap = Convert.convertPerf(request, metricMap);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("获取云主机监控失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取云主机监控失败");
        }
    }

    /**
     * 获取对象存储
     * @param request
     * @return
     */
    public static BaseResponse fetchOss(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject bucket = client.doActionJSON("DescribeOSS", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertOss(request, bucket);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            if(ObjectUtil.isNotEmpty(data.get(CmdbBucketStorage.class))){
                for (Object obj : data.get(CmdbBucketStorage.class)) {
                    CmdbBucketStorage res = (CmdbBucketStorage) obj;
                    request.getBody().getCloud().put("OSSID",res.getOpen_id());
                    try {
                        JSONObject ossBucket = client.doActionJSON("DescribeBuckets", request.getBody().getCloud());
                        Map<Class, List> bucketMap = Convert.convertOssBucket(request, ossBucket);
                        BaseCloudService.fetchSend(request, bucketMap);
                    }catch (Exception e) {
                        continue;
                    }
                }
                request.getBody().getCloud().remove("OSSID");
            }
            return toPageGourdResponse(request, baseResponse,
                    bucket.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        }catch (Exception e){
            throw new BaseException(BaseResponse.FAIL_OP,"获取对象存储失败");
        }
    }

    /**
     * 获取文件存储
     * @param request
     * @return
     */
    public static BaseResponse fetchFs(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject fs = client.doActionJSON("DescribeFS", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertFile(request, fs);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, baseResponse,
                    fs.getInteger("TotalCount"),
                    request.getBody().getCloud().getInteger("Limit"));
        }catch (Exception e){
            throw new BaseException(BaseResponse.FAIL_OP,"获取对象存储失败");
        }
    }

//    public static BaseResponse fetchZone(BaseCloudRequest request) {
//        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
//        Map<String, String> query;
//        try {
//            query = FTHttpUtils.query(request.getBody().getCloud());
//        } catch (Exception e) {
//            throw new BaseException(BaseResponse.FAIL_PARAM, e);
//        }
//        try {
//            JSONObject zones = client.doNorthApiInstances("SYS_Azone", query);
//            Map<Class, List> data = Convert.convertZone(request, zones);
//            BaseResponse response = BaseCloudService.fetchSend(request, data);
//            return toPageGourdResponse(request, response,
//                    zones.getInteger("totalNum"),
//                    zones.getInteger("pageSize"));
//        } catch (Exception e) {
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取可用区失败!"), e);
//        }
//    }
//    public static BaseResponse fetchVDC(BaseCloudRequest request) {
//        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
//        Map<String, String> query;
//        try {
//            query = FTHttpUtils.query(request.getBody().getCloud());
//        } catch (Exception e) {
//            throw new BaseException(BaseResponse.FAIL_PARAM, e);
//        }
//        try {
//            JSONObject vdcs = client.doResources("tenant_vdc", query);
//            Map<Class, List> data = Convert.convertVDC(request, vdcs);
//            BaseResponse response = BaseCloudService.fetchSend(request, data);
//            return toLimitGourdResponse(request, response,
//                    vdcs.getInteger("totalNum"),
//                    vdcs.getInteger("pageSize"));
//        } catch (Exception e) {
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取可用区失败!"), e);
//        }
//    }
}