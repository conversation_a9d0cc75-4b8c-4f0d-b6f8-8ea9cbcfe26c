package com.futong.gemini.plugin.cloud.ucloud.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

@Slf4j
public class BaseService {
    public static String accountDispatch;
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
        JSONArray regions = client.doActionJSONInfos("DescribeRegion", null);
        List<JSONObject> dispatchers = listAllDispatcher(result, regions);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, JSONArray regions) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                for (Object regionObject : regions) {
                    JSONObject region = (JSONObject) regionObject;
                    String itemStrRegion = StrUtil.replace(itemStr, "${region.label}", region.getString("regionAlias"));
                    itemStrRegion = StrUtil.replace(itemStrRegion, "${region.value}", region.getString("region"));
                    dispatchers.add(JSON.parseObject(itemStrRegion));
                }
            }
        });
        return dispatchers;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("BeginTime")) {
            request.getBody().getCloud().put("BeginTime", Instant.now().minus(Duration.ofDays(1)).getEpochSecond());
        }
        if (!request.getBody().getCloud().containsKey("EndTime")) {
            request.getBody().getCloud().put("EndTime", Instant.now().getEpochSecond());
        }
        return true;
    }

    /**
     * 修改地域下资源名称和备注 - ModifyNameAndRemark
     * @param request
     * @return
     */
    public static BaseResponse modifyNameAndRemark(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("ModifyNameAndRemark", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("修改地域下资源名称和备注失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改地域下资源名称和备注失败!"), e);
        }
    }

    /**
     * 获取平台项目
     * @param request
     * @return
     */
    public static BaseResponse queryPlatformProjects(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray projects = client.doActionJSONInfos("ListProjects", request.getBody().getCloud());
            return new BaseDataResponse<>(projects);
        }catch (Exception e){
            log.error("获取平台项目失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取平台项目失败!"), e);
        }
    }

    /**
     * 查询网段
     * @param request
     * @return
     */
    public static BaseResponse queryNeutronSegment(BaseCloudRequest request){
        try {
            JSONArray result = new JSONArray();
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String ipVersion = request.getBody().getCloud().getString("IPVersion");
            JSONArray segment = client.doActionJSONInfos("DescribeSegment", request.getBody().getCloud());
            if(ObjectUtil.isNotEmpty(segment)){
                for (Object segmentInfo : segment) {
                    JSONObject seg = (JSONObject)segmentInfo;
                    String IPVersion = seg.getString("IPVersion");
                    if(ipVersion.equals(IPVersion)){
                        result.add(seg);
                    }
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("查询网段失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询网段失败!"), e);
        }
    }

    /**
     * 查询规格
     * @param request
     * @return
     */
    public static BaseResponse queryPlatformFlavor(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String setType = request.getBody().getCloud().getString("setType");
            String productType = request.getBody().getCloud().getString("productTypes");
            String specificationName = request.getBody().getCloud().getString("specificationName");
            Map<String, String> query = new HashMap<>();
            query.put("Region",request.getBody().getCloud().getString("region"));
            query.put("ProductTypes.0",productType);
            query.put("SpecificationName",specificationName);
            JSONArray flavors = client.doActionJSONInfos("DescribeProductSpecification", query);
            JSONArray result = new JSONArray();
            if(ObjectUtil.isNotEmpty(flavors)){
                for (Object flavor : flavors) {
                    JSONObject object = new JSONObject();
                    JSONObject json = (JSONObject)flavor;
                    if(setType.equals(json.getString("SetType"))){
                        String value = json.getString("Value");
                        object.put("flavorValue",value);
                        if(value.contains("_")){
                            String valueArr[] = value.split("_");
                            object.put("cpuSize",valueArr[0]);
                            object.put("memSize",valueArr[1]);
                            object.put("flavorName",value.replace("_","核")+"G");
                        }else{
                            object.put("cpuSize",value);
                            object.put("flavorName",value+"核");
                        }
                        result.add(object);
                    }
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("查询规格失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询规格失败!"), e);
        }
    }

    /**
     * 获取平台日志
     * @param request
     * @return
     */
    public static BaseResponse queryPlatformLog(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject log = client.doActionJSON("DescribeOPLogs", MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud())));
            return new BaseDataResponse<>(log);
        }catch (Exception e){
            throw new BaseException(BaseResponse.FAIL_OP,"获取对象存储失败");
        }
    }
}
