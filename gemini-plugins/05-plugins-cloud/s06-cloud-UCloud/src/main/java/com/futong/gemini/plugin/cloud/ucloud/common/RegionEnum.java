package com.futong.gemini.plugin.cloud.ucloud.common;

/**
 * @author: zxr
 * @date: 2025/07/14
 * @description: 临时用，后续会删除
 */
public enum RegionEnum {

    CN_ChanganPOC("ChanganPOC", "poc"),
    CN_CAGDC("CAGDC", "gdc"),
    CN_CAGDC11("manager01", "manager01"),
    CN_CAGDC22("manager02", "manager02");

    private String code;

    private String value;

    RegionEnum(String changanPOC, String poc) {
        this.code = changanPOC;
        this.value = poc;
    }

    public static String fromCode(String code) {
        for (RegionEnum region : RegionEnum.values()) {
            if (region.code .equals( code)) {
                return region.value;
            }
        }
        throw new IllegalArgumentException("No enum constant with code " + code);
    }

    public static void main(String[] args) {
        System.out.println("&lt;script&gt;alert(&apos;XSS&apos;);&lt;/script&gt;");
    }
}
