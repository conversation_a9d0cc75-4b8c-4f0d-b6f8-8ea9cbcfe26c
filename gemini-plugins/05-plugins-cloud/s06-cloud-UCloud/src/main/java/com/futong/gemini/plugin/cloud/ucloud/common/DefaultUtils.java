package com.futong.gemini.plugin.cloud.ucloud.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import org.apache.commons.lang3.StringUtils;

public class DefaultUtils {
    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("Offset")) {
            request.getBody().getCloud().put("Offset", 0);//默认1
        }
        if (!request.getBody().getCloud().containsKey("Limit")) {
            request.getBody().getCloud().put("Limit", 50);//默认20
        }
        return true;
    }

    public static boolean toBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        if (resNum == null || resNum == 0) return true;
        JSONObject cloud = request.getBody().getCloud();
        JSONArray networkInfos = new JSONArray();
        if(cloud.containsKey("networkInfos")){
            JSONObject networkInfo = cloud.getJSONArray("networkInfos").getJSONObject(resNum-1);
            networkInfos.add(networkInfo);
            cloud.put("networkInfos", networkInfos);
        }
        cloud.put("resNum", request.getBody().getBiz().getInteger("resNum"));
        cloud.put("Quantity", 1);
        return true;
    }

    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) {
            return;
        }
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
            if (data instanceof JSONObject) {
                JSONObject runInstancesResponse = (JSONObject) data;
                if (ObjectUtil.isEmpty(runInstancesResponse)) {
                    return;
                }
                JSONObject biz = request.getBody().getBiz();
                String vmId = StringUtils.stripEnd(runInstancesResponse.getString("VMID"), ",");
                biz.put("resId", IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vmId));
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            }
        }
    }
}
