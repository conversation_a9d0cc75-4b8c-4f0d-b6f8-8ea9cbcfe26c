package com.futong.gemini.plugin.cloud.ucloud.sampler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class RefreshService {

    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("Starting");//启动中
            add("Restarting");//重启中
            add("Stopping");//关机中
            add("Terminating");//销毁中
            add("Migrating");//迁移中
            add("Reinstalling");//重装中
            add("Poweroffing");//断电中
            add("Deleting");//删除中
        }
    };

    public static BaseResponse refreshEcs(BaseCloudRequest request){
        try {
            Integer refreshCount = request.getBody().getGourd().getCount();
            Integer refreshMaxCount = 5;
            Integer refreshInterval = 5000;
            JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
            if (refreshConfig != null) {
                if (refreshConfig.containsKey("refreshMaxCount")) {
                    refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
                }
                if (refreshConfig.containsKey("refreshInterval")) {
                    refreshInterval = refreshConfig.getInteger("refreshInterval");
                }
            }
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            query.put("VMIDs.0",query.get("VMIDs"));
            query.remove("VMIDs");
            log.info("云主机实时刷新请求参数为：{}", query);
            JSONObject instance = client.doActionJSON("DescribeVMInstance", query);
            log.info("云主机实时刷新结果为：{}", instance.toString());
            Map<Class, List> result = Convert.convertEcs(request, instance);
            List<CmdbInstanceRes> res = result.get(CmdbInstanceRes.class);
            if (CollUtil.isEmpty(res)) {
                //发送已删除
                String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
                CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
                cmdbInstanceRes.setRes_id(resId);
                List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
                BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
            } else {
                //发送同步更新任务
                BaseCloudService.refreshUpdateSend(request, result);
                CmdbInstanceRes cmdbInstanceRes = res.get(0);
                if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                        && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                        && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
                ) {
                    JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
                }
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("实时刷新云主机异常",e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("实时刷新云主机失败!"), e);
        }
    }
}
