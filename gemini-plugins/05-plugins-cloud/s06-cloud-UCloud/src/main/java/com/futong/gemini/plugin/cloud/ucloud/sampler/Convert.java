package com.futong.gemini.plugin.cloud.ucloud.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.*;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.common.Constant;

import java.text.DecimalFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

public class Convert {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }
    public static Map<Class, List> convertRegion(BaseCloudRequest request, JSONArray response) {
        Map<Class, List> result = new HashMap<>();
        if (CollUtil.isEmpty(response)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的规格信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                response,
                (Object t)->((JSONObject)t).getString("RegionAlias"),
                (Object t)->((JSONObject)t).getString("Region")
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * CI模型租户转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(TmdbTenant.class, null);
            return result;
        }
        List<TmdbTenant> data = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject) {
                JSONObject res = (JSONObject) infos;
                TmdbTenant ci = new TmdbTenant();
                ci.setBiz_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("CompanyID")));
                ci.setOpen_id(res.getString("CompanyID"));
                ci.setFull_name(res.getString("Name"));
                ci.setDict_code("tenant_tenant");
                ci.setSimpl_name(res.getString("Name"));
                ci.setInfo_json(JSON.toJSONString(res));
                ci.setCreate_time(res.getLong("CreateTime"));
                ci.setUpdate_time(res.getLong("UpdateTime"));
                ci.setAccount_id(request.getBody().getAccess().getCmpId());
                ci.setCloud_type(request.getPlugin().getRealm());
                ci.setStatus("AVAILABLE".equals(res.getString("Status"))?1:2);
                data.add(ci);
                TmdbTenantLink link = new TmdbTenantLink();
                link.setBiz_id(IdUtils.encryptId(ci.getBiz_id(),  "tmdb_tenant_link"));
                link.setTenant_id(ci.getBiz_id());
                link.setCreate_time(ci.getCreate_time());
                link.setUpdate_time(ci.getUpdate_time());
                links.add(link);
            }
        }
        result.put(TmdbTenant.class, data);
        result.put(TmdbTenantLink.class, links);
        return result;
    }

    /**
     * CI模型云主机转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertEcs(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_INSTANCE_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject) {
                JSONObject res = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbInstanceRes ci = new CmdbInstanceRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VMID")));
                ci.setOpen_id(res.getString("VMID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setMem_size(res.getInteger("Memory"));
                ci.setCpu_size(res.getInteger("CPU"));
                ci.setOpen_status(res.getString("State"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                ci.setSet_type(res.getString("VMType"));
                toCiResCloud(request, ci);
                String status = res.getString("State");
                switch (status) {
                    case "Starting":
                        ci.setStatus(InstanceStatus.BUILDING.value());
                        break;
                    case "Running":
                        ci.setStatus(InstanceStatus.RUNNING.value());
                        break;
                    case "Restarting":
                        ci.setStatus(InstanceStatus.RESTARTING.value());
                        break;
                    case "Stopping":
                        ci.setStatus(InstanceStatus.STOPPING.value());
                        break;
                    case "Stopped":
                        ci.setStatus(InstanceStatus.STOPPED.value());
                        break;
                    case "Failed":
                        ci.setStatus(InstanceStatus.ERROR.value());
                        break;
                    case "Migrating":
                        ci.setStatus(InstanceStatus.MIGRATING.value());
                        break;
                    default:
                        ci.setStatus(InstanceStatus.UNKNOWN.value());
                        break;
                }
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
                //关联镜像
                Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ImageID")));
                associations.add(image);
                //关联镜像OS
                Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getString("ImageID")));
                associations.add(os);
                //关联网络VPC
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                associations.add(vpc);
                //关联网络VPC子网
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                associations.add(subnet);
                //添加公网弹性IP,关联弹性IP
                JSONArray  ipInfos = res.getJSONArray("IPInfos");
                if (CollUtil.isNotEmpty(ipInfos)){
                    for (Object ipOb : ipInfos) {
                        JSONObject ipInfo = (JSONObject) ipOb;
                        if(ObjectUtil.isNotEmpty(ipInfo.getString("IPID"))){
                            CmdbIpRes ip = new CmdbIpRes();
                            ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipInfo.getString("IPID")));
                            String type = ipInfo.getString("Type");
                            ip.setAddress(ipInfo.getString("IP"));
                            ip.setOpen_id(ipInfo.getString("IPID"));
                            ip.setOpen_name(ipInfo.getString("InterfaceName"));
                            toCiResCloud(request, ip);
                            if(type.equals("Public")){
                                ip.setType(IpType.PUBLIC_IP.value());
                            }
                            if(type.equals("Private")){
                                ip.setType(IpType.PRIVATE_IP.value());
                                Association subnetIp = AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                                associations.add(subnetIp);
                            }
                            ips.add(ip);
                            //IP关联云主机
                            associations.add(AssociationUtils.toAssociation(ip, ci));
                        }
                    }
                }
                //关联磁盘
                JSONArray disks = res.getJSONArray("DiskInfos");
                if(CollUtil.isNotEmpty(disks)){
                    for (Object diskOb : disks) {
                        JSONObject disk = (JSONObject) diskOb;
                        Association diskRelation = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), disk.getString("DiskID")));
                        associations.add(diskRelation);
                    }
                }
                data.add(ci);
            }
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型镜像转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertImage(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_IMAGE_RES.value()
                );
        BuilderResourceSet builderResourceSetOs = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_OS_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbImageRes ci = new CmdbImageRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ImageID")));
                ci.setType(res.getString("OSType").toLowerCase());
                ci.setSize(res.getInteger("ImageSize").floatValue());
                ci.setStatus(res.getString("ImageStatus"));
                ci.setOpen_id(res.getString("ImageID"));
                ci.setOpen_name(res.getString("ImageName"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                //区分基础镜像、自定义镜像、iso镜像
                if("Base".equals(res.getString("ImageType"))){
                    ci.setVisibility(res.getString("ImageType"));
                    ci.setImage_source("system");
                }
                if("Custom".equals(res.getString("ImageType")) && "iso".equals(res.getString("ImageFormat"))){
                    ci.setVisibility("ISO");
                    ci.setImage_source("system");
                }
                if("Custom".equals(res.getString("ImageType")) && "qcow2".equals(res.getString("ImageFormat"))){
                    ci.setVisibility("Custom");
                    ci.setImage_source("self");
                }
                toCiResCloud(request, ci);
                dataImage.add(ci);
                //关联地域与租户
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
                //操作系统OS对象
                CmdbOsRes os = new CmdbOsRes();
                os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getString("ImageID")));
                os.setType(res.getString("OSType"));
                os.setVersion(res.getString("OSName"));
                os.setFull_name(res.getString("OSName"));
                os.setOpen_name(res.getString("OSName"));
                os.setOpen_id(res.getString("ImageID"));
                os.setCpu_arch(res.getString("SetArch"));
                toCiResCloud(request, os);
                dataOs.add(os);
                Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
                dataAss.add(osa);
                //关联地域与租户
                builderResourceSetOs.withData(os.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            }
        }
        List<TmdbResourceSet> dataSet = new ArrayList<>();
        dataSet.addAll(builderResourceSet.getData());
        dataSet.addAll(builderResourceSetOs.getData());
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, dataSet);
        return result;
    }

    /**
     * CI模型VPC转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertVpc(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbVpcRes> data = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_VPC_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject) {
                JSONObject res = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(), res.getString("CompanyID"));
                CmdbVpcRes ci = new CmdbVpcRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                ci.setOpen_id(res.getString("VPCID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setCidr(res.getString("Network"));
                ci.setStatus(res.getString("State"));
                ci.setOpen_status(res.getString("State"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    /**
     * CI模型子网转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertSubnet(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject res  = (JSONObject) infos;
                CmdbSubnetRes ci = new CmdbSubnetRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                ci.setOpen_id(res.getString("SubnetID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setCidr_ipv4(res.getString("Network"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_status(res.getString("State"));
                ci.setStatus(res.getString("State"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //关联VPC
                Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                associations.add(rule);
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SUBNET_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("Region"))
                .getData();
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型网卡转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertNetcard(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_NETCARD_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbNetcardRes ci = new CmdbNetcardRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("NICID")));
                ci.setOpen_id(res.getString("NICID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setType(res.getString("NICType"));
                ci.setStatus(res.getString("NICStatus"));
                ci.setOpen_status(res.getString("NICStatus"));
                ci.setMac_address(res.getString("MAC"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                ci.setIpv4_address(res.getString("IP"));
                toCiResCloud(request, ci);
                data.add(ci);
                //关联VPC
                if (StrUtil.isNotEmpty(res.getString("VPCID"))) {
                    Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                    associations.add(vpc);
                }
                //关联子网
                if (StrUtil.isNotEmpty(res.getString("SubnetID"))) {
                    Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                    associations.add(subnet);
                }
                //关联安全组
                if (StrUtil.isNotEmpty(res.getString("SGID"))) {
                    Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SGID")));
                    associations.add(securityGroup);
                }
                //关联云主机
                if (StrUtil.isNotEmpty(res.getString("BindResourceID")) && StrUtil.equals(res.getString("BindResourceType"), "VM")) {
                    Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("BindResourceID")));
                    associations.add(instance);
                }
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型弹性公网IP转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertEip(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbEipRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_EIP_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                CmdbEipRes ci = new CmdbEipRes();
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("EIPID")));
                ci.setBandwidth_speed(res.getString("Bandwidth"));
                ci.setElastic_ip(res.getString("IP"));
                if("Bound".equalsIgnoreCase(res.getString("Status"))){
                    ci.setStatus("inuse");
                }else if("Free".equalsIgnoreCase(res.getString("Status"))){
                    ci.setStatus("available");
                }else if("Failed".equalsIgnoreCase(res.getString("Status"))){
                    ci.setStatus("error");
                }else {
                    ci.setStatus("other");
                }
                ci.setOpen_status(res.getString("Status"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_id(res.getString("EIPID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //关联云主机
                if (StrUtil.isNotEmpty(res.getString("BindResourceID")) && StrUtil.equals(res.getString("BindResourceType"), "VM")) {
                    Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("BindResourceID")));
                    associations.add(instance);
                }
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型安全组转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<CmdbSecuritygroupRule> ruleList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SECURITYGROUP_RES.value()
                );
        BuilderResourceSet builderResourceSetRule = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SECURITYGROUP_RULE.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SGID")));
                ci.setOpen_id(res.getString("SGID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //安全组规则
                if(CollUtil.isNotEmpty(res.getJSONArray("Rule"))){
                    for (Object ruleObject : res.getJSONArray("Rule")) {
                        JSONObject  securityGroupRule = (JSONObject) ruleObject;
                        CmdbSecuritygroupRule rule = new CmdbSecuritygroupRule();
                        rule.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroupRule.getString("RuleID")));
                        rule.setOpen_id(securityGroupRule.getString("RuleID"));
                        rule.setOpen_name(securityGroupRule.getString("RuleID"));
                        rule.setDirection(securityGroupRule.getString("IsIn").equals("0")?"egress":"ingress");
                        if("ingress".equals(rule.getDirection())){
                            rule.setSource_cidr(securityGroupRule.getString("SrcIP"));
                        }
                        if("egress".equals(rule.getDirection())){
                            rule.setDest_cidr(securityGroupRule.getString("SrcIP"));
                        }
                        rule.setPort_range(securityGroupRule.getString("DstPort"));
                        rule.setPolicy(securityGroupRule.getString("RuleAction").equals("ACCEPT")?"accept":"refuse");
                        rule.setIp_protocol(securityGroupRule.getString("ProtocolType"));
                        rule.setPriority(securityGroupRule.getString("Priority"));
                        rule.setOpen_direction(securityGroupRule.getString("IsIn"));
                        rule.setOpen_policy(securityGroupRule.getString("RuleAction"));
                        toCiResCloud(request, rule);
                        ruleList.add(rule);
                        Association securityGroup = AssociationUtils.toAssociation(rule, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroupRule.getString("SGID")));
                        associations.add(securityGroup);
                        builderResourceSetRule.withData(rule.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                    }
                }
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        List<TmdbResourceSet> dataSet = new ArrayList<>();
        dataSet.addAll(builderResourceSet.getData());
        dataSet.addAll(builderResourceSetRule.getData());
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(CmdbSecuritygroupRule.class, ruleList);
        result.put(TmdbResourceSet.class, dataSet);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSecurityGroupResource(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(Association.class, null);
            return result;
        }
        List<Association> associations = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject) {
                JSONObject res = (JSONObject) infos;
                CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SGID")));
                toCiResCloud(request, ci);
                Association association = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ResourceID")), res.getString("NICType"), res.toJSONString());
                associations.add(association);
            }
        }
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型磁盘转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertDisk(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_DISK_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbDiskRes ci = new CmdbDiskRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("DiskID")));
                ci.setOpen_id(res.getString("DiskID"));
                ci.setOpen_name(res.getString("Name"));
                if("Boot".equals(res.getString("DiskType"))){
                    ci.setCategory("system");
                }
                if("Data".equals(res.getString("DiskType"))){
                    ci.setCategory("data");
                }
                ci.setSize(res.getFloat("Size"));
                ci.setOpen_status(res.getString("DiskStatus"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                ci.setShare_disk(Boolean.toString(res.getBoolean("ShareAble")));
                switch (res.getString("DiskStatus")) {
                    case "Bound":
                        ci.setStatus(DiskStatus.IN_USE.value());
                        break;
                    case "Unbound":
                        ci.setStatus(DiskStatus.AVAILABLE.value());
                        break;
                    case "Failed":
                        ci.setStatus("error");
                        break;
                    default:
                        ci.setStatus(res.getString("DiskStatus"));
                }
                data.add(ci);
                toCiResCloud(request, ci);
                //关联云主机
                if (StrUtil.isNotEmpty(res.getString("AttachResourceID")) && StrUtil.equals(res.getString("AttachResourceType"), "VM")) {
                    Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("AttachResourceID")));
                    associations.add(instance);
                }
                //关联地域与租户
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型快照转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SNAPSHOT_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbSnapshotRes ci = new CmdbSnapshotRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SnapshotID")));
                ci.setOpen_id(res.getString("SnapshotID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setStatus(res.getString("SnapshotStatus").toLowerCase());
                ci.setOpen_status(res.getString("SnapshotID"));
                ci.setSize(res.getFloat("SnapshotSize"));
                ci.setType(res.getString("Type"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //关联磁盘
                Association disk = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("DiskID")));
                associations.add(disk);
                //关联地域云主机
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbSnapshotRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型NAT转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertNat(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbNatRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_NAT_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbNatRes ci = new CmdbNatRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("NATGWID")));
                ci.setOpen_id(res.getString("NATGWID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setStatus(res.getString("NATGWStatus"));
                ci.setOpen_status(res.getString("NATGWStatus"));
                ci.setDesc(res.getString("Remark"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //关联VPC
                if(ObjectUtil.isNotEmpty(res.getString("VPCID"))){
                    Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                    associations.add(vpc);
                }
                //关联子网
                if(ObjectUtil.isNotEmpty(res.getString("SubnetID"))){
                    Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                    associations.add(subnet);
                }
                //关联安全组
                if(ObjectUtil.isNotEmpty(res.getString("SGID"))){
                    Association sg = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SGID")));
                    associations.add(sg);
                }
                //关联EIP
                if(CollUtil.isNotEmpty(res.getJSONArray("EIPInfos"))){
                    for (Object eipInfos : res.getJSONArray("EIPInfos")) {
                        if (eipInfos instanceof JSONObject){
                            JSONObject  eip  = (JSONObject) eipInfos;
                            Association eipAssociation = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getString("IPID")));
                            associations.add(eipAssociation);
                        }
                    }
                }
                //关联地域与租户
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbNatRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertNatEntry(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            JSONObject res  = (JSONObject) infos;
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            if("DNAT".equals(request.getBody().getCi().getString("type"))){
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("PolicyID")));
                ci.setOpen_id(res.getString("PolicyID"));
                ci.setOpen_name(res.getString("PolicyID"));
                ci.setProtocol(res.getString("Protocol"));
                ci.setPrivate_port(res.getString("DstIP"));
                ci.setPrivate_ip(res.getString("DstPort"));
                ci.setPublic_port(res.getString("SrcIP"));
                ci.setPublic_ip(res.getString("SrcPort"));
                ci.setType("DNAT");
                //关联虚拟机
                Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, res.getString("DstResourceID"));
                associations.add(instance);
            }
            if("SNAT".equals(request.getBody().getCi().getString("type"))){
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("RuleID")));
                ci.setOpen_id(res.getString("RuleID"));
                ci.setOpen_name(res.getString("RuleID"));
                ci.setCidr(res.getString("Address"));
                ci.setPublic_ip(res.getString("EIP"));
                ci.setType("SNAT");
                String bindResourceType = res.getString("BindResourceType");
                if("VPC".equals(bindResourceType)){
                    Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, res.getString("BindResourceID"));
                    associations.add(vpc);
                }
                if("Subnet".equals(bindResourceType)){
                    Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, res.getString("BindResourceID"));
                    associations.add(subnet);
                }
            }
            toCiResCloud(request, ci);
            //关联NAT
            Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, res.getString("NATGWID"));
            associations.add(nat);
            data.add(ci);
        }
        result.put(CmdbNatEntryRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * CI模型负载均衡转换
     * @param request
     * @param response
     * @return
     */
    public static Map<Class, List> convertLoadBalance(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbLoadbalanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbLoadbalanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_LOADBALANCE_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),res.getString("CompanyID"));
                CmdbLoadbalanceRes ci = new CmdbLoadbalanceRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("LBID")));
                ci.setOpen_id(res.getString("LBID"));
                ci.setOpen_name(res.getString("Name"));
                ci.setAddress(StrUtil.isNotEmpty(res.getString("PrivateIP"))?res.getString("PrivateIP"):res.getString("PublicIP"));
                //负载均衡实例状态。取值：
                //inactive：实例已停止，此状态的实例监听不会再转发流量。
                //active：实例运行中，实例创建后，默认状态为 active。
                //locked：实例已锁定，实例已经被锁定。
                ci.setOpen_status(res.getString("LBStatus"));
                ci.setStatus(res.getString("LBStatus"));
                ci.setOpen_create_time(res.getLong("CreateTime")*1000);
                toCiResCloud(request, ci);
                data.add(ci);
                //关联vpc
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("VPCID")));
                associations.add(vpc);
                //关联子网
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("SubnetID")));
                associations.add(subnet);
                //关联地域与租户
                builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
                builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            }
        }
        result.put(CmdbLoadbalanceRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static List<AlarmInfoBean>  convertAlarm(BaseCloudRequest request, JSONObject response) {
        List<AlarmInfoBean> alarmList = new ArrayList<>();
        for (Object infos : response.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                AlarmInfoBean alarm = new AlarmInfoBean();
                alarm.setAccountId(request.getBody().getAccess().getCmpId());
                alarm.setCloudType(request.getPlugin().getRealm());
                alarm.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ActiveAt"),res.getString("TargetID")));
                alarm.setOpenId(res.getString("TargetID"));
//                alarm.setOpenLevel(alert.getAlarmRuleLevel());
//                alarm.setAlarmId(alert.getAlarmRuleId());
                alarm.setAlarmName(res.getString("Metric"));
                alarm.setDetail(res.getString("Summary"));
                alarm.setClosedStatus(false);
                alarm.setJsonInfo(JSON.toJSONString(res));
                alarm.setCount(1);
                if (ObjectUtil.isNotEmpty(res.getString("ActiveAt"))) {
                    alarm.setFirstTime(String.valueOf(res.getInteger("ActiveAt")*1000));
                    alarm.setCreateTime(alarm.getFirstTime());
                }
                String alarmLevel = res.getString("Severity");
                switch (alarmLevel) {
                    case "critical":
                        alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                        break;
                    case "error":
                        alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                        break;
                    case "warning":
                        alarm.setAlarmLevel(AlarmLevel.MINOR.value());
                        break;
                }
                alarmList.add(alarm);
            }
        }
        return alarmList;
    }


    public static List<EventInfoBean> convertEvent(BaseCloudRequest request, JSONObject event) {
        List<EventInfoBean> result = new ArrayList<>();
        for (Object infos : event.getJSONArray("Infos")) {
            if (infos instanceof JSONObject){
                JSONObject  res  = (JSONObject) infos;
                EventInfoBean bean = new EventInfoBean();
                bean.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ResourceEventID")));
                bean.setAccountId(request.getBody().getAccess().getCmpId());
                bean.setCloudType(request.getPlugin().getRealm());
                bean.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ResourceID")));
                bean.setOpenId(res.getString("ResourceID"));
                bean.setOpenName(res.getString("ResourceName"));
                bean.setOpenLevel(res.getString("Level"));
                switch (res.getString("Level")) {
                    case "Error":
                        bean.setEventLevel(AlarmLevel.CRITICAL.value());
                        break;
                    case "Warning":
                        bean.setEventLevel(AlarmLevel.MAJOR.value());
                        break;
                    case "Info":
                        bean.setEventLevel(AlarmLevel.INFORMATION.value());
                        break;
                }
                bean.setEventType(res.getString("Type"));
                bean.setEventName(res.getString("ResourceName"));
                bean.setDetail(res.getString("Content"));
                if (ObjectUtil.isNotEmpty(res.getString("StartTime"))) {
                    bean.setBeginTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(res.getString("StartTime"))*1000)));
                }
                if (ObjectUtil.isNotEmpty(res.getString("UpdateTime"))) {
                    bean.setEndTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(res.getString("UpdateTime"))*1000)));
                }
                bean.setJsonInfo(JSON.toJSONString(event));
                String resourceType = res.getString("ResourceType");
                if (StrUtil.isNotEmpty(resourceType)) {
                    if ("VM".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                    } else if ("DISK".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_DISK_RES.value());
                    } else if ("DISKSNAPSHOT".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_SNAPSHOT_RES.value());
                    } else if ("IMAGE".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_IMAGE_RES.value());
                    } else if ("SG".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_SECURITYGROUP_RES.value());
                    } else if ("SG_RULE".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_SECURITYGROUP_RULE.value());
                    } else if ("VPC".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_VPC_RES.value());
                    } else if ("SUBNET".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_SUBNET_RES.value());
                    } else if ("NIC".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
                    } else if ("NATGW".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_NAT_RES.value());
                    } else if ("EIP".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_EIP_RES.value());
                    } else if ("LB".equalsIgnoreCase(resourceType)) {
                        bean.setResourceType(ResourceType.CMDB_LOADBALANCE_RES.value());
                    }
                }
                result.add(bean);
            }
        }
        return result;
    }

    public static Map<String, PerfInfoBean> convertPerf(BaseCloudRequest request, Map<String,List<JSONArray>> metricMap) {
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        if(CollUtil.isEmpty(metricMap)){
            return perfMap;
        }
        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        DecimalFormat format = new DecimalFormat("#.00");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").withZone(ZoneId.systemDefault());
        for (String key : metricMap.keySet()) {
            String instanceId = key.substring(0, key.lastIndexOf("-"));
            String metricName = key.substring(key.lastIndexOf("-") + 1, key.length());
            List<JSONArray> metricList = metricMap.get(key);
            for (JSONArray metric : metricList) {
                long timestamp = metric.getLong(0);
                Double value = metric.getDouble(1);
                Double average = Double.valueOf(format.format(value));
                String createTime = formatter.format(Instant.ofEpochSecond(timestamp))+":00";
                String id = instanceId + "_" + timestamp;
                PerfInfoBean perf = perfMap.get( id);
                if (perf == null) {
                    //生成指标对象
                    ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                    perf = new PerfInfoBean();//指标对应得资源CI信息
                    perf.setAccountId(instanceDisk.getAccount_id());
                    perf.setCloudType(instanceDisk.getCloud_type());
                    perf.setResId(instanceDisk.getRes_id());
                    perf.setOpenId(instanceDisk.getOpen_id());
                    perf.setOpenName(instanceDisk.getOpen_name());
                    perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                    perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                    perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                    perf.setCreateTime(createTime);
                    if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                        Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                        perf.setDiskSize(sum);
                    }
                    perf.setId(id);
                }
                BiConsumer<PerfInfoBean, Double> setValue = Constant.perfMapping.get(metricName);
                setValue.accept(perf, average);//设置监控指标值
                perfMap.put(perf.getId(), perf);
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    public static Map<Class, List> convertOss(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbBucketStorage.class, null);
            return result;
        }
        JSONArray infos = response.getJSONArray("Infos");
        List<CmdbBucketStorage> data = new ArrayList<>();
        List< Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        "cmdb_bucket_storage"
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object info : infos) {
            JSONObject oss = (JSONObject) info;
            String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),oss.getString("CompanyID"));
            CmdbBucketStorage ci = new CmdbBucketStorage();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("OSSID")));
            ci.setOpen_id(oss.getString("OSSID"));
            ci.setOpen_name(oss.getString("Name"));
            ci.setCpu_size(oss.getInteger("CPU"));
            ci.setSize(oss.getFloat("DiskSpace"));
            ci.setStatus(oss.getString("Status"));
            ci.setOpen_status(oss.getString("Status"));
            ci.setStorage_cluster(oss.getString("StorageSetType"));
            ci.setCompute_cluster(oss.getString("VMTypeAlias"));
            ci.setDesc(oss.getString("Description"));
            JSONArray endpoints = oss.getJSONArray("Endpoints");
            if(ObjectUtil.isNotEmpty(endpoints)){
                for (Object endpointInfo : endpoints) {
                    JSONObject  endpoint = (JSONObject) endpointInfo;
                    if("LAN".equals(endpoint.getString("NicType"))){
                        ci.setLan_endpoint(endpoint.getString("Endpoint"));
                    }
                    if("WAN".equals(endpoint.getString("NicType"))){
                        ci.setWan_endpoint(endpoint.getString("Endpoint"));
                    }
                }
            }
            toCiResCloud(request,ci);
            data.add(ci);
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            //关联vpc
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("VPCID")));
            associations.add(vpc);
            //关联子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("SubnetID")));
            associations.add(subnet);
        }
        result.put(CmdbBucketStorage.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertOssBucket(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbBucketRes.class, null);
            return result;
        }
        JSONArray infos = response.getJSONArray("Infos");
        List<CmdbBucketRes> data = new ArrayList<>();
        List< Association> associations = new ArrayList<>();
        for (Object info : infos) {
            JSONObject bucket = (JSONObject) info;
            CmdbBucketRes ci = new CmdbBucketRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), bucket.getString("Name")));
            ci.setOpen_id(bucket.getString("Name"));
            ci.setOpen_name(bucket.getString("Name"));
            ci.setAcl(bucket.getString("AccessType"));
            ci.setVersioning_status(bucket.getString("VersionStatus"));
            ci.setOpen_create_time(bucket.getLong("CreateTime")*1000);
            toCiResCloud(request,ci);
            data.add(ci);
            //关联对象存储
            Association oss = AssociationUtils.toAssociation(ci, CmdbBucketStorage.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), request.getBody().getCloud().getString("OSSID")));
            associations.add(oss);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_BUCKET_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("Region"))
                .getData();
        result.put(CmdbBucketRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    public static Map<Class, List> convertFile(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (ObjectUtil.isEmpty(response) || CollUtil.isEmpty(response.getJSONArray("Infos"))) {
            result.put(CmdbFileStorage.class, null);
            return result;
        }
        JSONArray infos = response.getJSONArray("Infos");
        List<CmdbFileStorage> data = new ArrayList<>();
        List< Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        "cmdb_file_storage"
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("Region"));
        for (Object info : infos) {
            JSONObject oss = (JSONObject) info;
            String companyResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),oss.getString("CompanyID"));
            CmdbFileStorage ci = new CmdbFileStorage();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("OSSID")));
            ci.setOpen_id(oss.getString("FSID"));
            ci.setOpen_name(oss.getString("Name"));
            ci.setSize(oss.getFloat("DiskSpace"));
            ci.setStatus(oss.getString("Status"));
            ci.setOpen_status(oss.getString("Status"));
            ci.setStorage_cluster(oss.getString("StorageSetType"));
            ci.setCompute_cluster(oss.getString("VMTypeAlias"));
            JSONArray endpoints = oss.getJSONArray("Endpoints");
            if(ObjectUtil.isNotEmpty(endpoints)){
                for (Object endpointInfo : endpoints) {
                    JSONObject  endpoint = (JSONObject) endpointInfo;
                    if("LAN".equals(endpoint.getString("NicType"))){
                        ci.setLan_endpoint(endpoint.getString("Endpoint"));
                    }
                    if("WAN".equals(endpoint.getString("NicType"))){
                        ci.setWan_endpoint(endpoint.getString("Endpoint"));
                    }
                }
            }
            toCiResCloud(request,ci);
            data.add(ci);
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withData(ci.getRes_id(), "tenant_tenant", companyResId,"tmdb_tenant");
            //关联vpc
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("VPCID")));
            associations.add(vpc);
            //关联子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), oss.getString("SubnetID")));
            associations.add(subnet);
        }
        result.put(CmdbFileStorage.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertVDC(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        return result;
    }
}
