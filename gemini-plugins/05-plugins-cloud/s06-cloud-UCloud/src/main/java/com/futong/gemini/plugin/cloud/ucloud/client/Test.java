package com.futong.gemini.plugin.cloud.ucloud.client;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class Test {
    public static void main(String[] args) throws Exception {
        UCloudClient client = new UCloudClient(new ConnectionConfig.Builder()
                .endpoint("https://console.poc.ucloudstack.com")
                .connectTimeout(5000)
                .socketTimeout(5000)
                .authConfig(new AuthConfig.Builder()
                        .publicKey("P0EtRyUqFjQSgZwW0AIJwmFgd1EuWv1u4abKvPS4O9QHnYv_RqtduoqG")
                        .privateKey("z8Oq2m7bEyPq07JNJ8ShHqLkygr1cJnZoCACI1k8OsQWzRJVaboW3MYRS96XY4x8")
                        .build())
                .build());
        Map<String, String> query = new HashMap<>();
//        query.put("Limit", "20");
//        query.put("Offset", "0");
        JSONObject result = client.doActionJSON("DescribeRegion", query);
        System.out.println(result.toString());
    }
}
