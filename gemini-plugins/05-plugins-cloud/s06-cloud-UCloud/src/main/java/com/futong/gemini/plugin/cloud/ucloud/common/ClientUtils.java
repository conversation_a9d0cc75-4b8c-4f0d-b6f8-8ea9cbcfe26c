package com.futong.gemini.plugin.cloud.ucloud.common;

import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ucloud.client.AuthConfig;
import com.futong.gemini.plugin.cloud.ucloud.client.ConnectionConfig;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClientUtils {

    //获取Client对象
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            C client = null;
            //请求Client对象配置信息
            ConnectionConfig config = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .publicKey(body.getAccess().getUsername())
                            .privateKey(body.getAccess().getPassword())
                            .build()
                    )
                    .build();
            client = clazz.getConstructor(ConnectionConfig.class).newInstance(config);
            return client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    public static <C> C S3Client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            C client = null;
            //请求Client对象配置信息
            ConnectionConfig config = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .publicKey(body.getAccess().getUsername())
                            .privateKey(body.getAccess().getPassword())
                            .build()
                    )
                    .build();
            client = clazz.getConstructor(ConnectionConfig.class).newInstance(config);
            return client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
}

