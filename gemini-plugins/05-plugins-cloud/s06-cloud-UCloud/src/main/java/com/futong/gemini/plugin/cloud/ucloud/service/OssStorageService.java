package com.futong.gemini.plugin.cloud.ucloud.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.client.US3BucketLister;
import com.futong.gemini.plugin.cloud.ucloud.client.US3ClientBuilder;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.s3.S3Client;

import java.util.Map;

@Slf4j
public class OssStorageService {

    /**
     * 创建对象存储
     * @param request
     * @return
     */
    public static BaseResponse createOss(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject storage = request.getBody().getCloud().getJSONObject("storageClusters");
            request.getBody().getCloud().remove("storageClusters");
            request.getBody().getCloud().put("DiskSetType",storage.getString("DiskSetType"));
            request.getBody().getCloud().put("DiskSpace",storage.getInteger("DiskSpace"));
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateOSS", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建对象存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建对象存储失败!"), e);
        }
    }

    /**
     * 删除对象存储
     * @param request
     * @return
     */
    public static BaseResponse deleteOss(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("DeleteOSS", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除对象存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除对象存储失败!"), e);
        }
    }

    /**
     * 扩容对象存储
     * @param request
     * @return
     */
    public static BaseResponse resizeOss(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("UpgradeOSS", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除对象存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除对象存储失败!"), e);
        }
    }

    /**
     * 创建对象存储桶
     * @param request
     * @return
     */
    public static BaseResponse createOssBucket(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateBucket", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建对象存储桶失败!"), e);
        }
    }

    /**
     * 修改对象存储桶-桶访问类型
     * @param request
     * @return
     */
    public static BaseResponse updateOssBucket(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("UpdateBucketAccessType", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("修改对象存储桶访问类型失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改对象存储桶访问类型失败!"), e);
        }
    }

    /**
     * 删除对象存储桶
     * @param request
     * @return
     */
    public static BaseResponse deleteOssBucket(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("DeleteBucket", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除对象存储桶失败!"), e);
        }
    }

    /**
     * 清空对象存储桶
     * @param request
     * @return
     */
    public static BaseResponse flushOssBucket(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("FlushBucket", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("清空对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("清空对象存储桶失败!"), e);
        }
    }

    /**
     * 创建对象存储桶文件目录
     * @param request
     * @return
     */
    public static BaseResponse createOssBucketFolder(BaseCloudRequest request){
        try {
            S3Client s3Client = US3ClientBuilder.build(request);
            String bucketName = request.getBody().getCloud().getString("bucketName");
            String key = request.getBody().getCloud().getString("key");
            US3BucketLister lister = new US3BucketLister(s3Client, bucketName);
            lister.createFolder(key);
            return BaseResponse.SUCCESS.of("创建对象存储桶文件目录成功");
        }catch (Exception e){
            log.error("创建对象存储桶文件目录失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建对象存储桶文件目录失败!"), e);
        }
    }

    /**
     * 获取文件列表
     * @param request
     * @return
     */
    public static BaseResponse listOssBucketFile(BaseCloudRequest request){
        try {
            S3Client s3Client = US3ClientBuilder.build(request);
            String bucketName = request.getBody().getCloud().getString("bucketName");
            String key = request.getBody().getCloud().getString("key");
            US3BucketLister lister = new US3BucketLister(s3Client, bucketName);
            JSONArray list = lister.listObjects(key);
            return new BaseDataResponse<>(list);
        }catch (Exception e){
            log.error("清空对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("清空对象存储桶失败!"), e);
        }
    }

    /**
     * 删除文件或文件夹
     * @param request
     * @return
     */
    public static BaseResponse deleteOssBucketFile(BaseCloudRequest request){
        try {
            S3Client s3Client = US3ClientBuilder.build(request);
            String bucketName = request.getBody().getCloud().getString("bucketName");
            String key = request.getBody().getCloud().getString("key");
            US3BucketLister lister = new US3BucketLister(s3Client, bucketName);
            lister.deleteObject(key);
            return BaseResponse.SUCCESS.of("删除成功");
        }catch (Exception e){
            log.error("删除对象存储桶文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除对象存储桶文件失败!"), e);
        }
    }

    /**
     * 删除文件或文件夹
     * @param request
     * @return
     */
    public static BaseResponse uploadOssBucketFile(BaseCloudRequest request){
        try {
            S3Client s3Client = US3ClientBuilder.build(request);
            String bucketName = request.getBody().getCloud().getString("bucketName");
            String key = request.getBody().getCloud().getString("key");
            US3BucketLister lister = new US3BucketLister(s3Client, bucketName);
            lister.uploadFile(key);
            return BaseResponse.SUCCESS.of("上传对象存储桶文件成功");
        }catch (Exception e){
            log.error("上传对象存储桶文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("上传对象存储桶文件失败!"), e);
        }
    }

    /**
     * 下载文件
     * @param request
     * @return
     */
    public static BaseResponse downloadOssBucketFile(BaseCloudRequest request){
        try {
            S3Client s3Client = US3ClientBuilder.build(request);
            String bucketName = request.getBody().getCloud().getString("bucketName");
            String key = request.getBody().getCloud().getString("key");
            US3BucketLister lister = new US3BucketLister(s3Client, bucketName);
            lister.downloadFile(key);
            return BaseResponse.SUCCESS.of("下载成功");
        }catch (Exception e){
            log.error("下载对象存储桶文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("下载对象存储桶文件失败!"), e);
        }
    }

    /**
     * 获取对象存储桶生命周期列表
     * @param request
     * @return
     */
    public static BaseResponse describeBucketLifecycleRules(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray lifecycleRules = client.doActionJSONInfos("DescribeBucketLifecycleRules", request.getBody().getCloud());
            return new BaseDataResponse<>(lifecycleRules);
        }catch (Exception e){
            log.error("获取对象存储桶生命周期列表失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取对象存储桶生命周期列表失败!"), e);
        }
    }

    /**
     * 创建对象存储桶生命周期
     * @param request
     * @return
     */
    public static BaseResponse createBucketLifecycleRules(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONArray lifecycleRules = client.doActionJSONInfos("CreateBucketLifecycleRule", query);
            return new BaseDataResponse<>(lifecycleRules);
        }catch (Exception e){
            log.error("创建对象存储桶生命周期失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建对象存储桶生命周期失败!"), e);
        }
    }

    /**
     * 修改对象存储桶生命周期列表
     * @param request
     * @return
     */
    public static BaseResponse updateBucketLifecycleRules(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONArray lifecycleRules = client.doActionJSONInfos("UpdateBucketLifecycleRule", query);
            return new BaseDataResponse<>(lifecycleRules);
        }catch (Exception e){
            log.error("修改对象存储桶生命周期失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改对象存储桶生命周期失败!"), e);
        }
    }

    /**
     * 删除对象存储桶生命周期列表
     * @param request
     * @return
     */
    public static BaseResponse deleteBucketLifecycleRules(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONArray lifecycleRules = client.doActionJSONInfos("DeleteBucketLifecycleRule", request.getBody().getCloud());
            return new BaseDataResponse<>(lifecycleRules);
        }catch (Exception e){
            log.error("修改对象存储桶生命周期失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改对象存储桶生命周期失败!"), e);
        }
    }
}
