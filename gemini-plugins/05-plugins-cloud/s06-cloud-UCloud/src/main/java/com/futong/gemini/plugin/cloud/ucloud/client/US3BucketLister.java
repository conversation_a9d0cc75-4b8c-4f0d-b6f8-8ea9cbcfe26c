package com.futong.gemini.plugin.cloud.ucloud.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.utils.FTServletUtils;
import com.futong.common.utils.TimeUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.sdk.service.FileService;
import org.apache.http.Consts;
import org.apache.http.HttpResponse;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.springframework.core.io.InputStreamSource;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.paginators.ListObjectsV2Iterable;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class US3BucketLister {

    private final S3Client s3Client;
    private final String bucketName;

    public US3BucketLister(S3Client s3Client, String bucketName) {
        this.s3Client = s3Client;
        this.bucketName = bucketName;
    }

    /**
     * 简单列出存储桶内容（单次请求）
     * @param maxKeys 最大返回数量
     * @param delimiter 目录分隔符（通常为"/"）
     */
    public ListObjectsV2Response listObjects(int maxKeys, String delimiter) throws Exception{
        ListObjectsV2Request request = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .delimiter(delimiter)
                .build();
        return s3Client.listObjectsV2(request);
    }

    /**
     * 分页列出所有对象（自动处理分页）
     */
    public void listAllObjects() {

        ListObjectsV2Request request = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .build();

        ListObjectsV2Iterable response = s3Client.listObjectsV2Paginator(request);
        response.contents().forEach(object -> {
            System.out.printf("Key: %s, Size: %d, LastModified: %s%n",
                    object.key(),
                    object.size(),
                    object.lastModified());
        });
    }

    /**
     * 带目录结构的列表
     */
    public void listWithDirectories(String dir) {
        ListObjectsV2Request.Builder builder  = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .delimiter("/");
        if(StrUtil.isNotEmpty(dir)){
            builder.prefix(dir);
        }
        ListObjectsV2Response response = s3Client.listObjectsV2(builder.build());

        // 打印目录
        System.out.println("Directories:");
        response.commonPrefixes().forEach(prefix -> {
            System.out.println(" - " + prefix.prefix());
        });

        // 打印文件
        System.out.println("Files:");
        response.contents().forEach(object -> {
            System.out.println(" - " + object.key());
        });

        // 处理文件夹(CommonPrefixes)
        System.out.println("Subdirectories:");
        List<CommonPrefix> commonPrefixes = response.commonPrefixes();
        System.out.println("commonPrefixes的size："+commonPrefixes.size());
        response.commonPrefixes().forEach(prefix ->
                System.out.println(" - " + prefix));

        // 处理文件(Contents)
        System.out.println("Files:");
        response.contents().stream()
                .filter(obj -> !obj.key().endsWith("/")) // 排除文件夹标记
                .forEach(obj ->
                        System.out.printf(" - %s (size: %d)%n", obj.key(), obj.size()));
    }

    public void deleteObject(String key,String bucketName){
        s3Client.deleteObject(builder -> builder.bucket(bucketName).key(key));
    }


    public void uploadFile(String key) throws Exception{
        String filePath = "";
        MultipartFile multipartFile = (MultipartFile) FileService.get()[0];
        if(StrUtil.isEmpty( key)){
            filePath = multipartFile.getOriginalFilename() ;
        }else{
            filePath = key+multipartFile.getOriginalFilename();
        }
        s3Client.putObject(
                PutObjectRequest.builder().bucket(bucketName).key(filePath).build(),
                RequestBody.fromBytes(multipartFile.getBytes())
        );
    }


    public void createFolder(String folderName) {
        // 确保目录路径以/结尾
        String[] parts = folderName.split("/");
        StringBuilder currentPath = new StringBuilder();
        for (String part : parts) {
            if (!part.isEmpty()) {
                currentPath.append(part).append("/");
                createDirectory( currentPath.toString());
            }
        }
    }
    public void createDirectory(String folderName){
        String normalizedPath = folderName.endsWith("/") ?
                folderName : folderName + "/";
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(normalizedPath)
                .contentLength(0L)
                .build();
        s3Client.putObject(request,RequestBody.empty());
    }

    /**
     * 获取文件列表
     * @param prefix 文件前缀
     * @return
     */
    public JSONArray listObjects(String prefix){
        ListObjectsV2Request.Builder builder  = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .delimiter("/");
        if(StrUtil.isNotEmpty(prefix)){
            builder.prefix(prefix);
        }
        JSONArray jsonArray = new JSONArray();
        ListObjectsV2Response response = s3Client.listObjectsV2(builder.build());
        List<CommonPrefix> commonPrefixes = response.commonPrefixes();
        if(CollectionUtil.isNotEmpty(commonPrefixes)){
            for (CommonPrefix commonPrefix : commonPrefixes) {
                JSONObject object = new JSONObject();
                object.put("fileName", commonPrefix.prefix().substring(prefix.length()));
                object.put("fileSize", "-");
                object.put("updateTime", "-");
                object.put("isDir", true);
                jsonArray.add(object);
            }

        }
        List<S3Object> contents =response.contents();
        if(CollectionUtil.isNotEmpty(contents)){
            for (S3Object content : contents) {
                if(!content.key().endsWith("/")){
                    JSONObject object = new JSONObject();
                    object.put("fileName", content.key().substring(prefix.length()));
                    object.put("fileSize", content.size());
                    object.put("updateTime", TimeUtils.instantToString(content.lastModified()));
                    object.put("isDir", false);
                    jsonArray.add(object);
                }
            }
        }
        return jsonArray;
    }

    public void deleteObject(String objectKey) {
        if (objectKey.endsWith("/")) {
            deleteFolder(objectKey);
        } else {
            deleteFile(objectKey);
        }
    }

    /**
     * 删除文件夹
     * @param folderPath 文件夹路径
     */
    public void deleteFolder(String folderPath) {
        // 确保文件夹路径以/结尾
        String prefix = folderPath.endsWith("/") ? folderPath : folderPath + "/";
        System.out.println("Deleting folder: " + prefix);
        // 列出所有要删除的对象
        List<String> objectsToDelete = new ArrayList<>();
        ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .prefix(prefix)
                .build();

        // 使用分页器获取所有对象
        s3Client.listObjectsV2Paginator(listRequest)
                .stream()
                .flatMap(response -> response.contents().stream())
                .forEach(s3Object -> objectsToDelete.add(s3Object.key()));

        // 批量删除对象
        if (!objectsToDelete.isEmpty()) {
            deleteObjectsBatch(objectsToDelete);
            System.out.println("Deleted " + objectsToDelete.size() + " objects");
        } else {
            System.out.println("No objects found in folder");
        }
    }

    /**
     * 批量删除对象（S3限制每次最多1000个）
     */
    private void deleteObjectsBatch( List<String> objectKeys) {
        // 分批处理（每批1000个）
        int batchSize = 1000;
        for (int i = 0; i < objectKeys.size(); i += batchSize) {
            List<String> batch = objectKeys.subList(i, Math.min(i + batchSize, objectKeys.size()));
            // 构建删除请求
            List<ObjectIdentifier> objects = batch.stream()
                    .map(key -> ObjectIdentifier.builder().key(key).build())
                    .collect(Collectors.toList());
            DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(Delete.builder().objects(objects).build())
                    .build();

            // 执行批量删除
            s3Client.deleteObjects(deleteRequest);
        }
    }

    /**
     * 删除单个文件
     * @param objectKey 对象键（包含路径的文件名）
     */
    public void deleteFile(String objectKey) {
        DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .build();
        s3Client.deleteObject(deleteRequest);
        System.out.println("Deleted object: " + objectKey);
    }

    /**
     * 下载文件
     * @param downFileName 文件名
     */
    public void downloadFile(String downFileName) throws Exception{
        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(downFileName)
                .build();
        byte[] bytes = s3Client.getObject(request, ResponseTransformer.toBytes()).asByteArray();
        InputStream inputStream = new ByteArrayInputStream(bytes);
        FTServletUtils.download(inputStream, URLEncoder.encode(downFileName, StandardCharsets.UTF_8.toString()));
    }
}
