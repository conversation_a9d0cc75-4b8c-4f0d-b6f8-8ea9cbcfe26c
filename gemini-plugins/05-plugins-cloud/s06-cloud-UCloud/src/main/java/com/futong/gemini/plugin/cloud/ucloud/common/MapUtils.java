package com.futong.gemini.plugin.cloud.ucloud.common;

import java.util.Map;
import java.util.stream.Collectors;

public class MapUtils {

    public static Map<String, String> filterEmpty(Map<String, String> map){
        Map<String, String> filteredMap = map.entrySet()
                .stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return filteredMap;
    }
}
