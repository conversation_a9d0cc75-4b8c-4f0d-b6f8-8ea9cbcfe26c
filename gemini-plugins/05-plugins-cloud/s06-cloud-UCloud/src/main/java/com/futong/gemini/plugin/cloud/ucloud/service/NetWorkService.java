package com.futong.gemini.plugin.cloud.ucloud.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class NetWorkService {

    /**
     * 创建VPC
     * @param request
     * @return
     */
    public static BaseResponse createVpc(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateVPC", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建VPC失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建VPC失败!"), e);
        }
    }

    /**
     * 修改VPC
     * @param request
     * @return
     */
    public static BaseResponse updateVpc(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateVPC", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建VPC失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建VPC失败!"), e);
        }
    }

    /**
     * 删除VPC
     * @param request
     * @return
     */
    public static BaseResponse deleteVpc(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DeleteVPC", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除VPC失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除VPC失败!"), e);
        }
    }

    /**
     * 创建子网
     * @param request
     * @return
     */
    public static BaseResponse createSubnet(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateSubnet", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建子网失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建子网失败!"), e);
        }
    }

    /**
     * 创建子网
     * @param request
     * @return
     */
    public static BaseResponse deleteSubnet(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DeleteSubnet", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除子网失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除子网失败!"), e);
        }
    }

    /**
     * 创建EIP
     * @param request
     * @return
     */
    public static BaseResponse allocateEipAddress(BaseCloudRequest request){
        try {
            JSONObject result = new JSONObject();
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String ips = cloud.getString("ips");
            cloud.remove("ips");
            String ipArr[] = new String[]{};
            if(StrUtil.isNotEmpty(ips)){
                ipArr = ips.split(",");
            }
            StringBuffer buffer = new StringBuffer();
            Integer quantity = cloud.getInteger("Quantity");
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(cloud));
            if(quantity > 1){
                for(int i = 0; i < quantity; i++){
                    if(ipArr.length >= i+1){
                        query.put("IP", ipArr[i]);
                    }
                    query.put("Name",cloud.getString("Name")+"-"+(i+1));
                    JSONObject instance = client.doActionJSON("AllocateEIP", query);
                    buffer.append(instance.getString("EIPID")).append(",");
                }
                result.put("EIPID",buffer.toString());
                return new BaseDataResponse<>(result);
            }else{
                if(ipArr.length > 0){
                    query.put("IP",ipArr[0]);
                }
                JSONObject instance = client.doActionJSON("AllocateEIP", query);
                return new BaseDataResponse<>(instance);
            }
        }catch (Exception e){
            log.error("创建EIP失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建EIP失败!"), e);
        }
    }

    /**
     * 删除EIP
     * @param request
     * @return
     */
    public static BaseResponse releaseEipAddress(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("ReleaseEIP", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("删除EIP失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除EIP失败!"), e);
        }
    }

    /**
     * 绑定EIP
     * @param request
     * @return
     */
    public static BaseResponse associateEipAddress(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            request.getBody().getCloud().put("ResourceType","VM");
            JSONObject instance = client.doActionJSON("BindEIP", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("绑定EIP失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定EIP失败!"), e);
        }
    }

    /**
     * 解绑EIP
     * @param request
     * @return
     */
    public static BaseResponse unAssociateEipAddress(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            request.getBody().getCloud().put("ResourceType","VM");
            JSONObject instance = client.doActionJSON("UnBindEIP", request.getBody().getCloud());
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("解绑EIP失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑EIP失败!"), e);
        }
    }

    /**
     * 查询EIP模式
     * @param request
     * @return
     */
    public static BaseResponse queryEipMode(BaseCloudRequest request){
        try {
            JSONObject result = new JSONObject();
            result.put("mode","");
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject instance = client.doActionJSON("DescribeVMInstance", request.getBody().getCloud());
            if(instance != null && ObjectUtils.isNotEmpty(instance.getJSONArray("Infos"))){
                JSONObject info = instance.getJSONArray("Infos").getJSONObject(0);
                JSONArray ipInfos = info.getJSONArray("IPInfos");
                for (int i = 0; i < ipInfos.size(); i++) {
                    JSONObject ipInfo = ipInfos.getJSONObject(i);
                    if("Public".equals(ipInfo.getString("Type")) && StrUtil.isNotEmpty(ipInfo.getString("Mode"))){
                        result.put("mode",ipInfo.getString("Mode"));
                        break;
                    }
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("查询EIP模式失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询EIP模式失败!"), e);
        }
    }
}
