package com.futong.gemini.plugin.cloud.ucloud.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.client.UCloudClient;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.MapUtils;
import com.futong.gemini.plugin.cloud.ucloud.common.RegionEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ComputeInstanceService {

    public static BaseResponse describeVMSet(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONArray cluster = client.doActionJSONInfos("DescribeVMSet", query);
            return new BaseDataResponse<>(cluster);
        }catch (Exception e){
            log.error("查询集群失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群失败!"), e);
        }
    }

    public static BaseResponse describeStorageSet(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONArray cluster = client.doActionJSONInfos("DescribeStorageSet", query);
            return new BaseDataResponse<>(cluster);
        }catch (Exception e){
            log.error("查询存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询存储失败!"), e);
        }
    }

    /**
     * 创建云主机
     * @param request
     * @return
     */
    public static BaseResponse createInstance(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            Integer memory = cloud.getInteger("Memory")*1024;
            cloud.put("Memory",memory);
            JSONObject sysDisk = request.getBody().getCloud().getJSONObject("sysDisk");
            cloud.remove("sysDisk");
            JSONObject dataDisk =  cloud.getJSONObject("dataDisk");
            String BootDiskSetType = sysDisk.getString("BootDiskSetType");
            Integer BootDiskSpace = sysDisk.getInteger("BootDiskSpace");
            cloud.put("BootDiskSetType",BootDiskSetType);
            cloud.put("BootDiskSpace",BootDiskSpace);
            if(ObjectUtil.isNotEmpty(dataDisk) && ObjectUtil.isNotEmpty(dataDisk.getString("DataDiskSetType"))){
                String DataDiskSetType = dataDisk.getString("DataDiskSetType");
                Integer DataDiskSpace = dataDisk.getInteger("DataDiskSpace");
                cloud.put("DataDiskSetType",DataDiskSetType);
                cloud.put("DataDiskSpace",DataDiskSpace);
                cloud.put("BindDataDisk",true);
                cloud.remove("dataDisk");
            }
            JSONObject result = new JSONObject();
            Integer quantity = cloud.getInteger("Quantity");
            StringBuffer buffer = new StringBuffer();
            JSONArray networkInfos = new JSONArray();
            if(cloud.containsKey("networkInfos")){
                networkInfos = cloud.getJSONArray("networkInfos");
                cloud.remove("networkInfos");
            }
            JSONObject nameRule = new JSONObject();
            if(cloud.containsKey("nameRule")){
                nameRule = cloud.getJSONObject("nameRule");
                cloud.remove("nameRule");
            }
            //判断是否resNum，因为resNum是从工单那边传的，云资源运维是没有resNum的，所以要判断一下
            int resNum = cloud.containsKey("resNum") ? cloud.getIntValue("resNum") : 0;
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(cloud));
            for(int i = 0; i < quantity; i++){
                if(networkInfos.size() >= i+1){
                    JSONObject networkInfo = networkInfos.getJSONObject(i);
                    String privateIp = networkInfo.getString("private_ip");
                    query.put("InternalIP", privateIp);
                    query.put("InternetIP", networkInfo.getString("public_ip"));
                    query.put("Bandwidth", networkInfo.getString("bandwidth"));
                    if(ObjectUtil.isNotEmpty(nameRule)){
                        String regionValue = RegionEnum.fromCode(cloud.getString("Region"));
                        String name = privateIp+"-ecs-"+regionValue+"-"+nameRule.getString("dept")+"-"+nameRule.getString("businessSystem");
                        if(StrUtil.isNotEmpty(nameRule.getString("applicationType"))){
                            name = name+"-"+nameRule.getString("applicationType");
                        }
                        if(resNum == 0){
                            name =name+"-"+nameRule.getString("systemType")+"-"+(i+1);
                        }else{
                            name =name+"-"+nameRule.getString("systemType")+"-"+resNum;
                        }
                        query.put("Name",name);
                    }
                }
                log.info("创建实例参数为：{}"+ JSON.toJSONString(query));
                JSONObject instance = client.doActionJSON("CreateVMInstance", query);
                buffer.append(instance.getString("VMID")).append(",");
            }
            result.put("VMID",buffer.toString());
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("创建云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云主机失败!"), e);
        }
    }

    /**
     * 创建云主机
     * @param request
     * @return
     */
    public static BaseResponse cloneInstance(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            Integer memory = cloud.getInteger("Memory")*1024;
            cloud.put("Memory",memory);
            JSONObject result = new JSONObject();
            Integer quantity = cloud.getInteger("Quantity");
            StringBuffer buffer = new StringBuffer();
            JSONArray networkInfos = new JSONArray();
            if(cloud.containsKey("networkInfos")){
                networkInfos = cloud.getJSONArray("networkInfos");
                cloud.remove("networkInfos");
            }
            JSONObject nameRule = new JSONObject();
            if(cloud.containsKey("nameRule")){
                nameRule = cloud.getJSONObject("nameRule");
                cloud.remove("nameRule");
            }
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(cloud));
            for(int i = 0; i < quantity; i++){
                if(networkInfos.size() >= i+1){
                    JSONObject networkInfo = networkInfos.getJSONObject(i);
                    String privateIp = networkInfo.getString("private_ip");
                    query.put("InternalIP", privateIp);
                    query.put("InternetIP", networkInfo.getString("public_ip"));
                    query.put("Bandwidth", networkInfo.getString("bandwidth"));
                    if(ObjectUtil.isNotEmpty(nameRule)){
                        String regionValue = RegionEnum.fromCode(cloud.getString("Region"));
                        String name = privateIp+"-ecs-"+regionValue+"-"+nameRule.getString("dept")+"-"+nameRule.getString("businessSystem");
                        if(StrUtil.isNotEmpty(nameRule.getString("applicationType"))){
                            name = name+"-"+nameRule.getString("applicationType");
                        }
                        name =name+"-"+nameRule.getString("systemType")+"-"+(i+1);
                        query.put("Name",name);
                    }
                }
                log.info("克隆实例参数为：{}"+ JSON.toJSONString(query));
                JSONObject instance = client.doActionJSON("CloneVMInstance", query);
                buffer.append(instance.getString("VMID")).append(",");
            }
            result.put("VMID",buffer.toString());
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("克隆云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("克隆云主机失败!"), e);
        }
    }

    /**
     * 修改云主机配置
     * @param request
     * @return
     */
    public static BaseResponse updateEcsConfig(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String flavor = cloud.getString("flavor");
            cloud.remove("flavor");
            String flavorArr[] = flavor.split("_");
            Integer cpu = Integer.valueOf(flavorArr[0]);
            Integer memory = Integer.valueOf(flavorArr[1])*1024;
            cloud.put("CPU",cpu);
            cloud.put("Memory",memory);
            Map<String, String> query = FTHttpUtils.query(cloud);
            JSONObject instance = client.doActionJSON("ResizeVMConfig", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("修改云主机配置失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改云主机配置失败!"), e);
        }
    }

    /**
     * 删除云主机
     * @param request
     * @return
     */
    public static BaseResponse deleteInstance(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            List<String> vmIds = (List)request.getBody().getCloud().get("VMIDs");
            request.getBody().getCloud().remove("VMIDs");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            for(int i = 0; i < vmIds.size(); i++){
                query.put("VMID",vmIds.get(i));
                client.doActionJSON("DeleteVMInstance", query);
            }
            request.getBody().getCloud().put("VMIDs",vmIds);
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("删除云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除云主机失败!"), e);
        }
    }

    /**
     * 开启云主机
     * @param request
     * @return
     */
    public static BaseResponse startInstance(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            List<String> vmIds = (List)request.getBody().getCloud().get("VMIDs");
            request.getBody().getCloud().remove("VMIDs");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            for(int i = 0; i < vmIds.size(); i++){
                query.put("VMID",vmIds.get(i));
                client.doActionJSON("StartVMInstance", query);
            }
            request.getBody().getCloud().put("VMIDs",vmIds);
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("启动云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("启动云主机失败!"), e);
        }
    }

    /**
     * 关闭云主机
     * @param request
     * @return
     */
    public static BaseResponse stopInstance(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            List<String> vmIds = (List)request.getBody().getCloud().get("VMIDs");
            request.getBody().getCloud().remove("VMIDs");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            for(int i = 0; i < vmIds.size(); i++){
                query.put("VMID",vmIds.get(i));
                client.doActionJSON("StopVMInstance", query);
            }
            request.getBody().getCloud().put("VMIDs",vmIds);
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("关闭云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("关闭云主机失败!"), e);
        }
    }

    /**
     * 重启云主机
     * @param request
     * @return
     */
    public static BaseResponse rebootInstance(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            List<String> vmIds = (List)request.getBody().getCloud().get("VMIDs");
            request.getBody().getCloud().remove("VMIDs");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            for(int i = 0; i < vmIds.size(); i++){
                query.put("VMID",vmIds.get(i));
                JSONObject instance = client.doActionJSON("RestartVMInstance", query);
            }
            request.getBody().getCloud().put("VMIDs",vmIds);
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("重启云主机失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("重启云主机失败!"), e);
        }
    }

    /**
     * 获取云主机的VNC远程登录地址
     * @param request
     * @return
     */
    public static BaseResponse describeInstanceVncUrl(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject session = client.doActionJSON("AllocateVMVNCSession", query);
            String SessionID = session.getString("SessionID");
            JSONObject vnc = new JSONObject();
            CloudAccessBean accessBean = request.getBody().getAccess();
            vnc.put("domain",accessBean.getProtocol() + "://" +accessBean.getServerIp()+ ":" + accessBean.getServerPort()+"/vncproxy/vnc.html");
            vnc.put("token",SessionID);
            return new BaseDataResponse<>(vnc);
        }catch (Exception e){
            log.error("获取云主机的VNC远程登录地址失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机的VNC远程登录地址失败!"), e);
        }
    }

    /**
     * 重置密码
     * @param request
     * @return
     */
    public static BaseResponse resetEcsPassword(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            JSONObject instance = client.doActionJSON("ResetVMInstancePassword", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("云主机重置密码失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("云主机重置密码失败!"), e);
        }
    }

    /**
     * 查询规格
     * @param request
     * @return
     */
    public static BaseResponse queryUpdateComputeInstanceFlavor(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String setType = request.getBody().getCloud().getString("setType");
            String vmId = request.getBody().getCloud().getString("vmId");
            String productType = request.getBody().getCloud().getString("productTypes");
            String specificationName = request.getBody().getCloud().getString("specificationName");
            Map<String, String> query = new HashMap<>();
            query.put("Region",request.getBody().getCloud().getString("region"));
            if(StrUtil.isNotEmpty(vmId)){
                query.put("VMIDs.0",vmId);
                JSONArray vmArray = client.doActionJSONInfos("DescribeVMInstance",query);
                JSONObject vm = vmArray.getJSONObject(0);
                setType = vm.getString("VMType");
                query.remove("VMIDs.0");
            }
            query.put("ProductTypes.0",productType);
            query.put("SpecificationName",specificationName);
            JSONArray flavors = client.doActionJSONInfos("DescribeProductSpecification", query);
            JSONArray result = new JSONArray();
            if(ObjectUtil.isNotEmpty(flavors)){
                for (Object flavor : flavors) {
                    JSONObject object = new JSONObject();
                    JSONObject json = (JSONObject)flavor;
                    if(setType.equals(json.getString("SetType"))){
                        String value = json.getString("Value");
                        object.put("flavorValue",value);
                        if(value.contains("_")){
                            String valueArr[] = value.split("_");
                            object.put("cpuSize",valueArr[0]);
                            object.put("memSize",valueArr[1]);
                            object.put("flavorName",value.replace("_","核")+"G");
                        }else{
                            object.put("cpuSize",value);
                            object.put("flavorName",value+"核");
                        }
                        result.add(object);
                    }
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("查询规格失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询规格失败!"), e);
        }
    }



    /**
     * 创建主机镜像
     * @param request
     * @return
     */
    public static BaseResponse createImage(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String, String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instance = client.doActionJSON("CreateCustomImage", query);
            return new BaseDataResponse<>(instance);
        }catch (Exception e){
            log.error("创建私有镜像失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建私有镜像失败!"), e);
        }
    }

    /**
     * 删除主机镜像
     * @param request
     * @return
     */
    public static BaseResponse deleteImage(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String imageSource = request.getBody().getCloud().getString("imageSource");
            request.getBody().getCloud().remove("imageSource");
            List<String> imageIds = (List)request.getBody().getCloud().get("ImageIds");
            request.getBody().getCloud().remove("ImageIds");
            Map<String, String> query = FTHttpUtils.query(request.getBody().getCloud());
            String action = "DeleteCustomImage";
            for (String imageId : imageIds) {
                query.put("ImageID", imageId);
                if ("system".equals(imageSource)) {
                    action = "DeleteBaseImage";
                }
                client.doActionJSON(action, query);
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("删除主机镜像失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除主机镜像失败!"), e);
        }
    }

    /**
     * 获取已使用IP和外网线路网段
     * @param request
     * @return
     */
    public static BaseResponse queryUsedIP(BaseCloudRequest request){
        try {
            JSONObject result = new JSONObject();
            Map<String,Object> queryParam = request.getBody().getCloud().toJavaObject(Map.class);
            JSONObject ipResult = ApiFactory.Api.res.listUsedSubnetIp(queryParam);
            JSONObject data = ipResult.getJSONArray("data").getJSONObject(0);
            JSONArray ipList = data.getJSONArray("cmdb_ip_res");
            List<String> ipUsedList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(ipList)){
                for (Object ip : ipList) {
                    Map<String,Object> ipInfo = ( Map<String,Object>)ip;
                    ipUsedList.add(ipInfo.get("address").toString());
                }
            }
            result.put("ipUsedList",ipUsedList);
            result.put("internalSegment",data.getString("cidr_ipv4"));
            result.put("externalSegment",request.getBody().getCloud().getString("segment"));
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("获取已使用IP失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取已使用IP失败!"), e);
        }
    }

    /**
     * 获取云主机绑定的安全组
     * @param request
     * @return
     */
    public static BaseResponse queryComputeSecurityGroup(BaseCloudRequest request){
        try {
            JSONArray result = new JSONArray();
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = FTHttpUtils.query0(request.getBody().getCloud());
            JSONArray instanceInfos = client.doActionJSONInfos("DescribeVMInstance", query);
            if(ObjectUtil.isNotEmpty(instanceInfos)){
                JSONObject instance = instanceInfos.getJSONObject(0);
                JSONArray ipInfos = instance.getJSONArray("IPInfos");
                if(ObjectUtil.isNotEmpty(ipInfos)){
                    for (Object ipInfo : ipInfos) {
                        JSONObject  ip = (JSONObject) ipInfo;
                        if(StrUtil.isNotEmpty(ip.getString("SGID"))){
                            result.add( ip);
                        }
                    }
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("获取云主机绑定的安全组信息失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机绑定的安全组信息失败!"), e);
        }
    }

    /**
     * 获取云主机快照
     * @param request
     * @return
     */
    public static BaseResponse describeComputeSnapshot(BaseCloudRequest request){
        try {
            JSONArray snapshotList = new JSONArray();
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = FTHttpUtils.query0(request.getBody().getCloud());
            JSONArray instanceInfos = client.doActionJSONInfos("DescribeVMInstance", query);
            if(ObjectUtil.isNotEmpty(instanceInfos)){
                JSONObject instance = instanceInfos.getJSONObject(0);
                snapshotList = instance.getJSONArray("VMSPInfos");
            }
            return new BaseDataResponse<>(snapshotList);
        }catch (Exception e){
            log.error("获取云主机快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机快照失败!"), e);
        }
    }

    /**
     * 创建云主机快照
     * @param request
     * @return
     */
    public static BaseResponse createComputeSnapshot(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instanceSnapshot = client.doActionJSON("SaveVMInstance", query);
            return new BaseDataResponse<>(instanceSnapshot);
        }catch (Exception e){
            log.error("创建云主机快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云主机快照失败!"), e);
        }
    }

    /**
     * 删除云主机快照
     * @param request
     * @return
     */
    public static BaseResponse deleteComputeSnapshot(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instanceSnapshot = client.doActionJSON("DeleteVMSnapshot", query);
            return new BaseDataResponse<>(instanceSnapshot);
        }catch (Exception e){
            log.error("删除云主机快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除云主机快照失败!"), e);
        }
    }

    /**
     * 恢复云主机快照
     * @param request
     * @return
     */
    public static BaseResponse resumeComputeSnapshot(BaseCloudRequest request){
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            Map<String,String> query = MapUtils.filterEmpty(FTHttpUtils.query(request.getBody().getCloud()));
            JSONObject instanceSnapshot = client.doActionJSON("RestoreVMInstance", query);
            return new BaseDataResponse<>(instanceSnapshot);
        }catch (Exception e){
            log.error("恢复云主机快照失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("恢复云主机快照失败!"), e);
        }
    }
}
