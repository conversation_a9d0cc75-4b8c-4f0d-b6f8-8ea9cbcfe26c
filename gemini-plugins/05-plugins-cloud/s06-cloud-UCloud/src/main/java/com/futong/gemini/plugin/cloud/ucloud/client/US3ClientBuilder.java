package com.futong.gemini.plugin.cloud.ucloud.client;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.ucloud.common.ClientUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class US3ClientBuilder {
    /**
     * 创建专有云S3客户端
     * @param endpoint 专有云Endpoint，例如：https://s3.internal.ucloudstack.com
     * @param accessKey UCloud AccessKey
     * @param secretKey UCloud SecretKey
     * @param region 区域标识（专有云通常为"us3"）
     */
    public static S3Client build(String endpoint, String accessKey, String secretKey, String region) {
        S3ClientBuilder builder = S3Client.builder()
                .endpointOverride(URI.create(endpoint))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKey, secretKey)))
                .region(Region.of(region));

        // 专有云通常需要以下特殊配置
//        builder.serviceConfiguration(b -> b
//                .pathStyleAccessEnabled(true)  // 强制使用路径样式
//                .chunkedEncodingEnabled(false)); // 某些专有云版本需要关闭分块编码

        return builder.build();
    }

    public static S3Client build(BaseCloudRequest request) {
        JSONObject osTokenInfo = getToken(request);
        String accessKey = osTokenInfo.getString("AccessKey");
        String secretKey = osTokenInfo.getString("SecretKey");
        String endpoint = request.getBody().getCloud().getString("wanEndpoint");
        S3ClientBuilder builder = S3Client.builder()
                .endpointOverride(URI.create(endpoint))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKey, secretKey)))
                .region(Region.of("us-east-1"));

        // 专有云通常需要以下特殊配置
//        builder.serviceConfiguration(b -> b
//                .pathStyleAccessEnabled(true)  // 强制使用路径样式
//                .chunkedEncodingEnabled(false)); // 某些专有云版本需要关闭分块编码

        return builder.build();
    }

    /***
     * 获取token
     * @return
     */
    public static JSONObject getToken(BaseCloudRequest request){
        JSONObject token =null;
        try {
            token = (JSONObject) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, request.getBody().getCloud().getString("OSSID"));
        } catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, request.getBody().getCloud().getString("OSSID"));
        }
        if(ObjectUtil.isNull(token)){
            token = fetchToken(request);
            log.info("调用云上接口获取token并存入缓存内  = {}", JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, request.getBody().getCloud().getString("OSSID"),token);
        }
        return token;
    }
    public  static JSONObject fetchToken(BaseCloudRequest request) {
        try {
            UCloudClient client = ClientUtils.client(UCloudClient.class, request.getBody());
            String region = request.getBody().getCloud().getString("Region");
            String ossId = request.getBody().getCloud().getString("OSSID");
            Map<String,String> query = new HashMap<>();
            query.put("Region",region);
            query.put("OSSID",ossId);
            JSONArray osTokenInfos = client.doActionJSONInfos("DescribeDOSToken", query);
            if(ObjectUtil.isEmpty(osTokenInfos)){
                throw new BaseException(BaseResponse.FAIL_OP,"该存储桶没有访问令牌！");
            }
            JSONObject osTokenInfo = osTokenInfos.getJSONObject(0);
            return osTokenInfo;
        } catch (Exception e) {
            log.error("获取令牌失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
    }
}
