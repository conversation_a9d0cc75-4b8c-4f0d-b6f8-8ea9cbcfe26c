{"description": "The initial management account only supports the main account for management", "model": [{"type": "main", "name": "Cloud platform operation master account", "description": "Cloud platform operation master account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your <PERSON> Account."}, {"field": "username", "label": "Access Key", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key"}, {"field": "password", "label": "Access Key Secret", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key Secret"}, {"field": "serverIp", "label": "Server Host", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Host"}, {"field": "serverPort", "label": "Server Port", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Port"}, {"field": "protocol", "label": "protocol", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}], "required": true, "isUpdate": true, "tips": "Please select your Protocol"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}, {"scope": "resource", "type": "sub", "name": "Cloud platform operation sub account", "description": "Cloud platform operation sub account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your <PERSON> Account."}, {"field": "username", "label": "Access Key", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key"}, {"field": "password", "label": "Access Key Secret", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key Secret"}, {"field": "jsonStr.proxyAddr", "label": "Proxy Address", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Proxy Address"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}]}