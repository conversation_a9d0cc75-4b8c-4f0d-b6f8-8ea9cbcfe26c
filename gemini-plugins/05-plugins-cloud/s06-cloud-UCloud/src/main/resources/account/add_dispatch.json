{"form": {"region": {"field": "region", "label": "地域", "type": "select", "value": "", "items": [], "required": true, "isUpdate": true, "tips": "请选择地域"}}, "data": [{"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud主机-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud主机-${region.label}", "jobInfo": "{\"action\": \"FetchComputeInstance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud磁盘-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud磁盘-${region.label}", "jobInfo": "{\"action\": \"FetchStorageDisk\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud规格-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud规格-${region.label}", "jobInfo": "{\"action\": \"FetchComputeFlavor\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud镜像-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud镜像-${region.label}", "jobInfo": "{\"action\": \"FetchStorageImage\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud安全组-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud安全组-${region.label}", "jobInfo": "{\"action\": \"FetchComputeSecuritygroup\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloudVPC-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloudVPC-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronVpc\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud密钥对-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud密钥对-${region.label}", "jobInfo": "{\"action\": \"FetchPlatformKeypair\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud弹性IP-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud弹性IP-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronEip\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud子网-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud子网-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronSubnet\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloudNAT网关-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloudNAT网关-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronNat\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud磁盘快照-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud磁盘快照-${region.label}", "jobInfo": "{\"action\": \"FetchStorageSnapshot\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud负载均衡-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud负载均衡-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronLoadbalance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud路由表-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud路由表-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronRoute\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取UCloud对象存储-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud对象存储-${region.label}", "jobInfo": "{\"action\": \"FetchStorageBucket\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud地域可用区", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "获取UCloud地域可用区", "jobInfo": "{\"action\": \"FetchPlatformRegion\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"cn-beijing\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud主机监控", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取UCloud主机监控", "jobInfo": "{\"action\":\"FetchComputeInstancePerf\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"cloud\":{\"namespace\":\"acs_ecs_dashboard\",\"Region\":\"cn-beijing\"},\"BasePageSortSearchRequest\":{\"searchList\":[{\"key\":\"status\",\"searchClassiy\":\"0\",\"value\":\"running\"},{\"key\":\"account_id\",\"searchClassiy\":\"0\",\"value\":\"${cmpId}\"}]}},\"async\":false}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud告警信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformAlarm\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud账单", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformBill\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"Region\": \"cn-beijing\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud事件", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformEvent\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取UCloud租户", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformTenant\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}