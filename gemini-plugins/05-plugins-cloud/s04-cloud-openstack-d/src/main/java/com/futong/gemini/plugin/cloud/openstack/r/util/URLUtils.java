package com.futong.gemini.plugin.cloud.openstack.r.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;

public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取token信息*/
    private String[] tokenUrl = { "/v3/auth/tokens" };

    public String[] getTokenUrl() {
        return tokenUrl.clone();
    }

    /** 获取计算资源URL */
    private String[] computeUrl = { "/servers/detail" };

    public String[] getComputeUrl() {
        return computeUrl.clone();
    }

    private String[] serverUrl = { "/servers/detail" };

    public String[] getServerUrl() {
        return serverUrl.clone();
    }

    private String[] keypairUrl = { "/os-keypairs" };

    public String[] getKeypairUrl() {
        return keypairUrl.clone();
    }
    /** 获取镜像资源URL */
    private String[] imageUrl = { "/v2/images" };

    public String[] getImageUrl() {
        return imageUrl.clone();
    }

    private String[] flavorUrl = { "/flavors/detail" };

    public String[] getFlavorUrl() {
        return flavorUrl.clone();
    }

    /** 获取计算节点URL */
    private String[] computeNodeUrl = {"/os/compute/v1/h3cloudos/computenode"};

    public String[] getComputeNodeUrl() {
        return computeNodeUrl.clone();
    }

    /** 获取主机URL */
    private String[] hostUrl = {"/os-hypervisors"};

    public String[] getHostUrl() {
        return hostUrl.clone();
    }


    /** 获取存储资源接口URL */
    private String[] storageUrl = {"/os/storage/v1/v2"};

    public String[] getStorageUrl() {
        return storageUrl.clone();
    }
    /** 获取经典网络资源接口URL */
    private String[] networkUrl = {"/v2.0/networks"};

    public String[] getNetworkUrl() {
        return networkUrl.clone();
    }

    /** 获取vpc资源接口URL */
    private String[] vpcUrl = { "/os/netsecurity/v1/v1.0/vpcs" };

    public String[] getVpcUrl() {
        return vpcUrl.clone();
    }
    /** 获取子网资源接口URL */
    private String[] subnetUrl = { "/v2.0/subnets" };

    public String[] getSubnetUrl() {
        return subnetUrl.clone();
    }

    private String[] volumeUrl = { "/volumes/detail" };

    public String[] getVolumeUrl() {
        return volumeUrl.clone();
    }

    private String[] volumeTypeUrl = { "/types" };
    public String[] getVolumeTypeUrl() {
        return volumeTypeUrl.clone();
    }

    private String[] snapshotUrl = { "/snapshots/detail" };
    public String[] getSnapshotUrl() {
        return snapshotUrl.clone();
    }
    /** 获取网卡资源接口URL */
    private String[] portUrl = { "/os/netsecurity/v1/v2.0/ports" };

    public String[] getPortUrl() {
        return portUrl.clone();
    }

    private String[] netsecurityUrl = {"/os/netsecurity/v1/v2.1"};

    public String[] getNetsecurityUrl() {
        return netsecurityUrl.clone();
    }


    // 安全组
    private String[] securityGroupListUrl = new String[]{"/v2.0/security-groups"};

    public String[] getSecurityGroupListUrl() {
        return this.securityGroupListUrl.clone();
    }

    // 安全组规则
    private String[] securityGroupRuleListUrl = new String[]{"/os/netsecurity/v1/v2.0/security-group-rules"};

    public String[] getSecurityGroupRuleListUrl() {
        return this.securityGroupRuleListUrl.clone();
    }

    //获取组织资源URL
    private String[] projectUrl = { "/sys/oapi/v1/getProjects" };
    public String[] getProjectUrl() {
        return projectUrl.clone();
    }

    private String[] userUrl = { "/sys/oapi/v1/users" };
    public String[] getUserUrl() {
        return userUrl.clone();
    }

    private String[] azoneUrl = { "/os/compute/v1/cloudos/azones" };
    public String[] getAzoneUrl() {
        return azoneUrl.clone();
    }

    private String[] instancePerfUrl = { "/os/compute/v1/v1/resource/generic" };

    public String[] getInstancePerfUrl() {
        return instancePerfUrl.clone();
    }

    private String[] routerUrl = { "/v2.0/routers" };

    public String[] getRouterUrl() {
        return routerUrl.clone();
    }

    private String[] alarmUrl = { "/sys/alertmgt/api/v1/alert","page","pagesize" };

	public String[] getAlarmUrl() {
		return alarmUrl.clone();
	}

    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

    public static String makeOsUrl(String url, String[] paths, String[] args,String version){
        if (paths.length > 1)
        {
            return configArgs(url, paths, args);
        }
        else if (null != args && args.length > 0 && null != args[0])
        {
            return url + args[0] + paths[0];
        }
        else
        {
            return url + paths[0];
        }
    }

}
