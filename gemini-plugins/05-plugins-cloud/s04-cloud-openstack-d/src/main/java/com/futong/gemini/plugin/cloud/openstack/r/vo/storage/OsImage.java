package com.futong.gemini.plugin.cloud.openstack.r.vo.storage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsImage {

    private String container_format;

    private Integer min_ram;

    private String updated_at;

    private String cas_ostype;

    private String file;

    private String owner;

    private String id;

    private Long size;

    private String self;

    private String disk_format;

    private String user_name;

    private String hw_disk_bus;

    private String schema;

    private String status;

    private String description;

    private String virt_type;

    private String frameworkType;

    private String visibility;

    private String image_flag;

    private Integer min_disk;

    private String virtual_size;

    private String name;

    private String min_cpu;

    private String checksum;

    private String created_at;

    @JsonProperty("protected")
    private Boolean is_protected;

    private String image_type;

    private String ostype;

    private String imageType;
}

