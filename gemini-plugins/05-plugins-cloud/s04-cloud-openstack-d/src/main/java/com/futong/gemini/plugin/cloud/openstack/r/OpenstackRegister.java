package com.futong.gemini.plugin.cloud.openstack.r;

import com.futong.gemini.plugin.cloud.openstack.r.sampler.FetchSampler;
import com.futong.gemini.plugin.cloud.openstack.r.service.AccountService;
import com.futong.gemini.plugin.cloud.openstack.r.service.CloudService;
import com.futong.gemini.plugin.cloud.openstack.r.service.ComputeInstanceService;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class OpenstackRegister extends BaseCloudRegister {

    @Override
    public void load() {
        //加载云平台操作
        onAfterLoadAccount();
        //加载同步调度信息
        onAfterLoadFetch();
    }

    public void onAfterLoadAccount() {
        //云账号验证
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount);
        //获取云账号表单信息
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);
        //添加默认调度任务
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);
    }


    public void onAfterLoadFetch() {

//        registerBefore(CloudService::defaultPage100,
//                ActionType.FETCH_COMPUTE_INSTANCE);

        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchSampler::fetchInstance);//同步云主机
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchSampler::fetchFlavor);//同步规格
        register(ActionType.FETCH_STORAGE_IMAGE, FetchSampler::fetchImage);//同步镜像

        register(ActionType.FETCH_COMPUTE_HOST, FetchSampler::fetchComputenode);//同步计算节点信息
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchSampler::fetchKeypair);//同步密钥对
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchSampler::fetchRouter);//同步路由
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchSampler::fetchSecurityGroup);//同步安全组

        register(ActionType.FETCH_NEUTRON_SWITCH, FetchSampler::fetchNetwork);//同步经典网络
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchSampler::fetchSubnet);//同步子网
        register(ActionType.FETCH_STORAGE_DISK, FetchSampler::fetchDisk);//同步云盘

        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchSampler::fetchSnapshot);//同步快照

        register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance);//创建云主机
        register(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::startInstance);//启动云主机
        register(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::stopInstance);//关闭云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::rebootInstance);//重启云主机
        register(ActionType.RESUME_COMPUTE_INSTANCE, ComputeInstanceService::resumeInstance);//恢复云主机
        register(ActionType.SUSPEND_COMPUTE_INSTANCE, ComputeInstanceService::suspendInstance);//挂起云主机
        register(ActionType.PAUSE_COMPUTE_INSTANCE, ComputeInstanceService::pauseInstance);//暂停云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance);//删除云主机
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::webConsole);//web控制台

    }
}
