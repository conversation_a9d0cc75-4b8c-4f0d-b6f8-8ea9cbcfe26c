
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbImageRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbKeypairRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsKeypair;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PlatKeypairService {

    public static final PlatKeypairService bean = new PlatKeypairService();

    public void fetchKeypair(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getKeypairUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.KEYPAIR.getValue());
        List<CmdbKeypairRes> keypairList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                /** 密钥对数据模型转换*/
                OsKeypair osKey = Converts.parseAndConvert(d.toString(), new TypeReference<OsKeypair>() {});
                CmdbKeypairRes keypair = Converts.toNxcKeypair(arguments.getBody().getAccess(), osKey);
                keypairList.add(keypair);
            });
        }
        Map<Class,List> result = new HashMap<>();
        result.put(CmdbKeypairRes.class,keypairList);
        BaseCloudService.fetchSend(arguments, result);
    }

    /**
     * 获取秘钥对
     * @param arguments
     * @return
     */
    private List<OsKeypair> fetchCloudosKeypair(BaseCloudRequest arguments){
        List<OsKeypair> keypairs = new ArrayList<>();
        DescribeOpenstackRequest request =   arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(),new String[]{request.getAuthToken().getToken().getProject().getId(),"os-keypairs"}),
                ResourceEnum.KEYPAIR.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                OsKeypair keypair = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<OsKeypair>() {});
                keypairs.add(keypair);
            });
        }
        return keypairs;
    }

//    public static BaseResponse createKeyPair(JSONObject arguments) {
//        String message = "操作成功.";
//        log.info("cloudos创建秘钥对接受参数={}", arguments.toString());
//        CreateKeypairRequest request = BaseClient.bodys.get().toJavaObject(CreateKeypairRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs"});
//        try {
//            JSONObject keypair = new JSONObject();
//            keypair.put("keypair",request.getCloud());
//            HttpClientUtil.post(url, keypair.toString(), config);
//        } catch (Exception e) {
//            log.error("创建秘钥对异常{}", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建秘钥对异常");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse deleteKeyPair(JSONObject arguments) {
//        String message = "操作成功.";
//        log.info("cloudos删除秘钥对接受参数={}", arguments.toString());
//        CreateKeypairRequest request = BaseClient.bodys.get().toJavaObject(CreateKeypairRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        JSONObject body = BaseClient.bodys.get();
//        List<String> keypairIds = getInstanceList(body);
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        for (String keyId : keypairIds) {
//            try {
//                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs",keyId});
//                HttpClientUtil.delete(url, config);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//
//        return BaseResponse.SUCCESS.of(message);
//    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
