package com.futong.gemini.plugin.cloud.openstack.r.vo;

import lombok.Data;

@Data
public class OPEndpoint {
    private String regionId;
    /**
     * 服务地址
     */
    private String url;
    /**
     * 区域名称
     */
    private String region;

    private String serviceId;

    private String endpointId;
    /**
     * openstack 地址
     */
    private String serverIp;

    /**
     * 服务类型: nova、neutron
     */
    private String type;

    private String name;

    private long updateTime;
}
