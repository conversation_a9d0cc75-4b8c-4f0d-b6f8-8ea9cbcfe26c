package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.model.otc.bxc.entity.TmdbUser;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbImageRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsFlavor;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsImage;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存储服务类，负责从云资源提供商获取镜像信息并转换、推送数据队列
 */
@Slf4j
public class StorageImageService {

    // 存储镜像服务实例
    public static final StorageImageService bean = new StorageImageService();

    /**
     * 获取并推送镜像信息
     *
     * @param arguments 请求参数，包含获取镜像信息所需的信息
     */
    public void fetchImage(BaseCloudRequest arguments) {
        List<CmdbImageRes> imageList = new ArrayList<>();
        List<CmdbSnapshotRes> snapshotList = new ArrayList<>();
        List<OsImage> images = fetchCloudosImage(arguments);
        Map<String, String> userMap = fetchCloudosUser(arguments);
        List<Association> associations = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(images)) {
            images.forEach(image -> {
                // 镜像资源数据模型转换
                CmdbImageRes imageRes = Converts.toNxImage(arguments.getBody().getAccess(), image);
                if (imageRes != null){
                    String userId = userMap.get(image.getUser_name()) == null ? "" : userMap.get(image.getUser_name());
                    associations.add(AssociationUtils.toAssociation(imageRes, TmdbUser.class, userId));
                    if (ObjectUtil.isNotNull(imageRes))
                        imageList.add(imageRes);

                    // 快照资源数据模型转换
                    CmdbSnapshotRes snapshot = Converts.toNxSnapshot(arguments.getBody().getAccess(), image);
                    if (ObjectUtil.isNotNull(snapshot))
                        snapshotList.add(snapshot);
                }
            });
        }

        Map<Class,List> result = new HashMap<>();
        result.put(CmdbImageRes.class,imageList);
        result.put(CmdbSnapshotRes.class,snapshotList);
        result.put(Association.class,associations);
        BaseCloudService.fetchSend(arguments, result);
    }

    /**
     * 从云资源提供商获取镜像信息
     *
     * @param arguments 请求参数，包含获取镜像信息所需的信息
     * @return 镜像信息列表
     */
    public List<OsImage> fetchCloudosImage(BaseCloudRequest arguments) {
        List<OsImage> images = new ArrayList<>();
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.GLANCE.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getImageUrl(),null,null);
        url = url + "?limit=99999";
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.IMAGE.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                OsImage image = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<OsImage>() {});
                images.add(image);
            });
        }
        return images;
    }

    private Map<String, String> fetchCloudosUser(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        //获取用户列表
        String response = HttpClientUtil.get(URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getUserUrl(), null), config);
        if (ObjectUtil.isNotEmpty(response)
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"))) {
            return fetchUser(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"), arguments.getBody().getAccess());
        }
        return new HashMap<>();
    }

    private Map<String, String> fetchUser(JSONArray array, CloudAccessBean bean) {
        Map<String, String> map = new HashMap<>();
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                map.put(tempObj.getString("name"),tempObj.getString("id"));
            });
        }
        return map;
    }
    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
