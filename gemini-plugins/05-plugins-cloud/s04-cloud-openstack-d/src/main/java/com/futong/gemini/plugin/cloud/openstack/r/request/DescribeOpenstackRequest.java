package com.futong.gemini.plugin.cloud.openstack.r.request;

import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.openstack.r.vo.OPEndpoint;
import com.futong.gemini.plugin.cloud.openstack.r.vo.OsToken;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class DescribeOpenstackRequest {

    /**
     * 待同步的资源ID
     */
    private List<String> ids;

    /**
     * 每次同步的条数
     */
    private Integer fecthSize;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * cloudos token信息
     */
    private OsToken authToken;

    private Map<String, OPEndpoint> endpointMap;

    /** 计算节点名称 */
    private String computeNode;

    private CloudAccessBean access;

}
