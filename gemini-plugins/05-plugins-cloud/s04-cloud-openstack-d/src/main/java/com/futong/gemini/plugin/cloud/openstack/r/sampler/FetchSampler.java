package com.futong.gemini.plugin.cloud.openstack.r.sampler;

import cn.hutool.core.util.ObjectUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.service.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FetchSampler {


    public static BaseResponse fetchInstance(BaseCloudRequest request) {
        String message = "成功获取云主机资源信息.";
        try{
            ComputeInstanceService.bean.fetchInstance(request);
        }  catch (Exception e) {
            log.error("同步云主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云主机资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    //获取组织资源数据并推送到北新仓下
//    public static BaseResponse fetchProject(JSONObject arguments) {
//        String message = "成功获取组织资源信息.";
//        try{
//            PlatProjectService.bean.fetchProject(arguments);
//        }  catch (Exception e) {
//            log.error("同步组织资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步组织资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse fetchUser(JSONObject arguments) {
//        String message = "成功获取用户资源信息.";
//        try{
//            PlatProjectService.bean.fetchUser(arguments);
//        }  catch (Exception e) {
//            log.error("同步用户资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步用户资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//    public static BaseResponse fetchAzone(JSONObject arguments) {
//        String message = "成功获取可用域资源信息.";
//        try{
//            PlatAZoneService.bean.fetchAZone(arguments);
//            //PlatAZoneService.bean.fetchRegionAndZone(arguments);
//        }  catch (Exception e) {
//            log.error("同步可用域资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步可用域资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse fetchAlarm(JSONObject arguments) {
//        String message = "成功获取告警资源信息.";
//        try{
//            AlarmService.bean.fetchAlarm(arguments);
//        }  catch (Exception e) {
//            log.error("同步告警资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步告警资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
    public static BaseResponse fetchSnapshot(BaseCloudRequest arguments) {
        String message = "成功获取磁盘快照资源信息.";
        try{
            StorageSnapshotService.bean.fetchSnapshot(arguments);
        }  catch (Exception e) {
            log.error("同步磁盘快照资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步磁盘快照资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

//    public static BaseResponse fetchVpc(JSONObject arguments) {
//        String message = "成功获取vpc资源信息.";
//        try{
//            NetworkVpcService.bean.fetchVpc(arguments);
//        }  catch (Exception e) {
//            log.error("同步vpc资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步vpc资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//


//
//    public static BaseResponse fetchCard(JSONObject arguments) {
//        String message = "成功获取网卡资源信息.";
//        try{
//            NetworkNicService.bean.fetchNic(arguments);
//        }  catch (Exception e) {
//            log.error("同步网卡资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步网卡资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
    public static BaseResponse fetchNetwork(BaseCloudRequest arguments) {
        String message = "成功获取经典网络资源信息.";
        try{
            NetworkVswitchService.bean.fetchNetwork(arguments);
        }  catch (Exception e) {
            log.error("同步经典网络资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步经典网络资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchSubnet(BaseCloudRequest arguments) {
        String message = "成功获取子网资源信息.";
        try{
            NetworkSubnetService.bean.fetchSubnet(arguments);
        }  catch (Exception e) {
            log.error("同步子网资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步子网资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDisk(BaseCloudRequest arguments) {
        String message = "成功获取云盘资源信息.";
        try{
            StorageDiskService.bean.fetchDisk(arguments);
        }  catch (Exception e) {
            log.error("同步云盘资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云盘资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchFlavor(BaseCloudRequest request) {
        String message = "成功获取规格资源信息.";
        try{
            ComputeFlavorService.bean.fetchFlavor(request);
        }  catch (Exception e) {
            log.error("同步规格资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步规格资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }
    public static BaseResponse fetchImage(BaseCloudRequest request) {
        String message = "成功获取镜像资源信息.";
        try{
            StorageImageService.bean.fetchImage(request);
        }  catch (Exception e) {
            log.error("同步镜像资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步镜像资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse fetchComputenode(BaseCloudRequest request) {
        String message = "成功获取主机资源信息.";
        try{
            ComputeHostService.bean.fetchHost(request);
        }  catch (Exception e) {
            log.error("同步主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步主机资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    /**
     * 获取路由器数据
     * @param arguments
     * @return
     */
    public static BaseResponse fetchRouter(BaseCloudRequest arguments) {
        String message = "成功获取路由器信息.";
        try{
            NetworkRouterService.bean.fetchRouter(arguments);
        }  catch (Exception e) {
            log.error("同步路由器数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步路由器数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    /**
     * 获取密钥对数据
     * @param arguments
     * @return
     */
    public static BaseResponse fetchKeypair(BaseCloudRequest arguments) {
        String message = "成功获取秘钥对信息.";
        try{
            PlatKeypairService.bean.fetchKeypair(arguments);
        }  catch (Exception e) {
            log.error("同步秘钥对数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步秘钥对数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    /**
     * 获取安全组及规则
     * @param arguments
     * @return
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest arguments) {
        String message = "成功获取获取安全组及规则信息.";
        try {
            NetworkSecurityGroupService.bean.fetchSecurityGroup(arguments);
        } catch (Exception e) {
            log.error("同步安全组资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步安全组资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

//    public static BaseResponse queryInstanceTotal(JSONObject arguments) {
//        String message = "同步虚拟机数量成功";
//        try {
//            DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//            JSONObject data = new JSONObject();
//            int count = ComputeInstanceService.bean.queryInstanceTotal(request);
//            data.put("count", count);
//            return new BaseDataResponse<>(data);
//        } catch (Exception e) {
//            log.error("同步虚拟机数量失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP, e, "同步虚拟机数量失败");
//        }
//    }
//
//    public static BaseResponse queryHostTotal(JSONObject arguments) {
//        String message = "同步主机数量成功";
//        try {
//            log.info("开始获取主机total"+System.currentTimeMillis());
//            JSONObject data = ComputeNodeService.bean.queryHostTotal();
//            log.info("结束获取主机total"+System.currentTimeMillis());
//            return new BaseDataResponse<>(data);
//        } catch (Exception e) {
//            log.error("同步主机数量失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP, e, "同步主机数量失败");
//        }
//    }
//
//    public static BaseResponse queryComputePassword(JSONObject arguments) {
//        String message = "同步云主机密码成功";
//        try {
//            CloudosInstancePassRequest request = BaseClient.bodys.get().toJavaObject(CloudosInstancePassRequest.class);
//            String pass = ComputeInstanceService.bean.queryInstancePass(request);
//            return new BaseDataResponse<>(pass);
//        } catch (Exception e) {
//            log.error("同步云主机密码失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP, e, "同步云主机密码失败");
//        }
//    }
//
//    public static BaseResponse queryNetworkOutput(JSONObject arguments) {
//        String message = "获取网络出口";
//        try {
//            CloudosInstancePassRequest request = BaseClient.bodys.get().toJavaObject(CloudosInstancePassRequest.class);
//            List<JSONObject> result = ComputeInstanceService.bean.queryNetworkOutput(request);
//            return new BaseDataResponse<>(result);
//        } catch (Exception e) {
//            log.error("获取网络出口失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP, e, "获取网络出口失败");
//        }
//    }
//
//    public static BaseResponse queryAzone(JSONObject arguments) {
//        String message = "获取区域";
//        try {
//            CloudosInstancePassRequest request = BaseClient.bodys.get().toJavaObject(CloudosInstancePassRequest.class);
//            List<JSONObject> result = ComputeInstanceService.bean.queryAzone(request);
//            return new BaseDataResponse<>(result);
//        } catch (Exception e) {
//            log.error("获取区域失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP, e, "获取区域失败");
//        }
//    }

}
