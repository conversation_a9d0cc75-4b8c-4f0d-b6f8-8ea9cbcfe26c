package com.futong.gemini.plugin.cloud.openstack.r.service;

import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CloudService {

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", "0");
        }
        if (!request.getBody().getCloud().containsKey("limit")) {
            request.getBody().getCloud().put("limit", "100");
        }
        return true;
    }
}
