package com.futong.gemini.plugin.cloud.openstack.r.convert;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DiskStatus;
import com.futong.constant.dict.InstanceStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.constant.dict.TenantSide;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbUser;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.DateUtils;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.OsToken;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsFlavor;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsHost;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsInstance;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsKeypair;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsNetwork;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsRouter;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsSecurityGroup;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsSubnet;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsDisk;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsImage;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsSnapshot;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.DiskCategory;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class Converts {

//    public static BasePageSortSearchRequest toBasePageSortSearchRequest(PageSortSearchRequest request) {
//        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
//        if (ObjectUtil.isNotNull(request)) {
//            searchRequest.setCurrent(request.getCurrent() == null ? 1 : request.getCurrent());
//            searchRequest.setSize(request.getSize() == null ? 50 : request.getSize());
//            searchRequest.setSortField(request.getSortField() == null ? BaseConstant.RES_ID : request.getSortField());
//            searchRequest.setSort(request.getSort() == null ? 0 : request.getSort());
//        } else {
//            searchRequest.setCurrent(1);
//            searchRequest.setSize(50);
//            searchRequest.setSortField(BaseConstant.RES_ID);
//            searchRequest.setSort(0);
//        }
//        List<BaseSearchApiModel> searchList = new ArrayList<>();
//        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
//        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
//        searchApiModel.setValue(BaseClient.auths.get().getCmpId());
//        searchApiModel.setSearchClassiy("0");
//        searchList.add(searchApiModel);
//        searchRequest.setSearchList(searchList);
//        searchRequest.setSearchList(searchList);
//        return searchRequest;
//    }

    public static <T> T parseAndConvert(String jsonString, TypeReference<T> typeRef) {
        return JSON.parseObject(jsonString, typeRef);
    }

//    public static CmdbStoragePoolRes toNxcStoragePool(CloudAccessBean bean, CloudosStoragePool info, String hostId) {
//        CmdbStoragePoolRes storage = new CmdbStoragePoolRes();
//        if ("dir".equals(info.getType())) {
//            storage.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), hostId, info.getPath()));
//        } else {
//            storage.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), info.getPath()));
//        }
//        storage.setType(info.getType());
//        storage.setOpen_name(info.getName());
//        storage.setCloud_type(bean.getCloudType());
//        storage.setAccount_id(bean.getCmpId());
//        storage.setTotal_size(info.getTotalSize() == null ? 0 : Float.valueOf(String.format("%.2f", info.getTotalSize() / 1024.0)));
//        if (ObjectUtil.isNotNull(info.getFreeSize()) && info.getFreeSize() / 1024.0 <= storage.getTotal_size()) {
//            storage.setUsed_size(storage.getTotal_size() - Float.valueOf(String.format("%.2f", info.getFreeSize() / 1024.0)));
//        } else {
//            storage.setUsed_size(0f);
//        }
//        storage.setAllocation_size(info.getAllocation() == null ? 0 : Float.valueOf(String.format("%.2f", info.getAllocation() / 1024.0)));
//        storage.setStatus(info.getStatus());
//        storage.setDesc(info.getTitle());
//        return storage;
//    }
//
    public static CmdbHostRes toNxcHost(CloudAccessBean bean, OsHost info) {
        CmdbHostRes nxcHostRes = new CmdbHostRes();
        /**
         * 封装资源唯一ID
         */
        nxcHostRes.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), info.getId()));
        nxcHostRes.setOpen_name(info.getName());
        nxcHostRes.setOpen_id(info.getId());
        nxcHostRes.setIp(info.getIp());
        nxcHostRes.setModel(info.getModel());
        nxcHostRes.setCpu_size(info.getCpuCount());
        nxcHostRes.setMem_size(info.getMemorySize() != null ? info.getMemorySize() : 0);
        nxcHostRes.setCloud_type(bean.getCloudType());
        nxcHostRes.setAccount_id(bean.getCmpId());
        nxcHostRes.setManufacturer(info.getVendor());
        nxcHostRes.setSn(info.getSerialNumber());
        nxcHostRes.setTotal_size(Float.parseFloat(NumberUtil.roundStr(Float.parseFloat(info.getDiskSize()) / 1024.0, 2)));
        nxcHostRes.setUsed_size(Float.parseFloat(NumberUtil.roundStr(nxcHostRes.getTotal_size()*info.getDiskRate()/100,2)));

        switch (info.getStatus().toString()) {
            case "1":
                nxcHostRes.setStatus(InstanceStatus.RUNNING.value());
                break;
            case "0":
                nxcHostRes.setStatus(InstanceStatus.STOPPED.value());
                break;
            default:
                nxcHostRes.setStatus(InstanceStatus.UNKNOWN.value());
                break;
        }
        nxcHostRes.setOpen_status(info.getStatus());
        nxcHostRes.setMaintain_mode(info.getMaintainMode());
        return nxcHostRes;
    }

    public static <T> TmdbResourceSet toTmdbResourceSet(CloudAccessBean bean, T info) {
        TmdbResourceSet tag = new TmdbResourceSet();
        tag.setSet_table(AssociationUtils.otcTableName(TmdbDevops.class));
        /**
         * 封装资源唯一ID
         */
        if (info instanceof OsInstance) {
            OsInstance instance = (OsInstance) info;
            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getId()));
            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), instance.getTenant_id()));
            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
            tag.setResource_type(ResourceType.CMDB_INSTANCE_RES.value());
            //tag.setDevops_level01(IdUtils.encryptId(bean.getCmpId(),bean.getCloudType(), DevopsSide.DEVOPS_ZONE.getValue(),instance.getMetadata().getZone_uuid()));
            tag.setCloud_type(bean.getCloudType());
        }
//        else if (info instanceof CloudosDisk) {
//            CloudosDisk disk = (CloudosDisk) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), disk.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), disk.getTenant_id()));
//            tag.setResource_type(ResourceType.CMDB_DISK_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosNetwork) {
//            CloudosNetwork network = (CloudosNetwork) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), network.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), network.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_VSWITCH_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosPort) {
//            CloudosPort port = (CloudosPort) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), port.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), port.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_NETCARD_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosSubnet) {
//            CloudosSubnet subnet = (CloudosSubnet) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), subnet.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), subnet.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_SUBNET_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosVpc) {
//            CloudosVpc vpc = (CloudosVpc) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_VPC_RES.value(), vpc.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), vpc.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_VPC_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosSnapshot) {
//            CloudosSnapshot snapshot = (CloudosSnapshot) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), snapshot.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), snapshot.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_SNAPSHOT_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        } else if (info instanceof CloudosSecurityGroup) {
//            CloudosSecurityGroup group = (CloudosSecurityGroup) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), group.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), group.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_SECURITYGROUP_RES.value());
//            tag.setCloud_type(bean.getCloudType());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//        } else if (info instanceof CloudosRouter) {
//            CloudosRouter router = (CloudosRouter) info;
//            tag.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_ROUTE_RES.value(), router.getId()));
//            tag.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), router.getProject_id()));
//            tag.setResource_type(ResourceType.CMDB_ROUTE_RES.value());
//            tag.setSet_type(TenantSide.TENANT_PROJECT.value());
//            tag.setCloud_type(bean.getCloudType());
//        }
        else {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "Unsupported type for info: " + info.getClass().getName());
        }
        tag.setAccount_id(bean.getCmpId());
        tag.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), tag.getSet_id(), tag.getResource_id()}));
        return tag;
    }

    public static CmdbDiskRes toNxcDisk(CloudAccessBean bean, OsFlavor info) {
        CmdbDiskRes disk = new CmdbDiskRes();
        disk.setCloud_type(bean.getCloudType());
        disk.setAccount_id(bean.getCmpId());
        disk.setStatus(DiskStatus.IN_USE.value());
        disk.setCategory(DiskCategory.SYSTEM.getValue());
        disk.setSize(info.getDisk());
        return disk;
    }

    public static CmdbOsRes toNxcOsRes(CloudAccessBean bean, OsImage info) {
        CmdbOsRes os = new CmdbOsRes();
        if (ObjectUtil.isNotNull(info.getCreated_at()))
            os.setCreate_time(DateUtils.utcToTimestamp(info.getCreated_at()));
        if (ObjectUtil.isNotNull(info.getOstype())) {
            os.setType(info.getOstype());
            os.setFull_name(info.getImageType());
            os.setName(info.getImageType());
        } else if (ObjectUtil.isNotEmpty(info.getCas_ostype()) && info.getCas_ostype().split("\\|").length > 1) {
            os.setType(info.getCas_ostype().split("\\|")[0]);
            os.setFull_name(info.getCas_ostype().split("\\|")[1]);
            os.setName(info.getCas_ostype().split("\\|")[1]);
        }
        os.setOpen_id(info.getId());
        os.setCloud_type(bean.getCloudType());
        os.setAccount_id(bean.getCmpId());
        os.setCpu_arch(info.getFrameworkType());
        os.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_OS_RES.value(), info.getId()));
        return os;
    }

    public static CmdbInstanceRes toNxcInstance(CloudAccessBean bean, OsInstance info) {
        CmdbInstanceRes instance = new CmdbInstanceRes();
        /**
         * 封装资源唯一ID
         */
        instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), info.getId()));
        instance.setOpen_id(info.getId());
        instance.setOpen_name(info.getName());
        switch (info.getStatus().toUpperCase()) {
            case "ACTIVE":
                instance.setStatus(InstanceStatus.RUNNING.value());
                break;
            case "ERROR":
                instance.setStatus(InstanceStatus.ERROR.value());
                break;
            case "SUSPENDED":
                instance.setStatus(InstanceStatus.SUSPENDED.value());
                break;
            case "STOPPED":
                instance.setStatus(InstanceStatus.STOPPED.value());
                break;
            case "SHUTOFF":
                instance.setStatus(InstanceStatus.STOPPED.value());
                break;
            default:
                instance.setStatus(InstanceStatus.UNKNOWN.value());
                break;
        }
        instance.setOpen_status(info.getStatus());
        instance.setDesc(info.getMetadata().getDescription());
        instance.setCloud_type(bean.getCloudType());
        instance.setAccount_id(bean.getCmpId());
        instance.setIs_template(0);
        return instance;
    }

    public static CmdbFlavor toNxcFlavor(CloudAccessBean bean, OsFlavor info) {
        CmdbFlavor flavor = new CmdbFlavor();
        /**
         * 封装资源唯一ID
         */
        flavor.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_FLAVOR.value(), info.getId()));
        flavor.setOpen_id(info.getId());
        flavor.setOpen_name(info.getName());
        flavor.setCloud_type(bean.getCloudType());
        flavor.setAccount_id(bean.getCmpId());
        flavor.setCpu_size(info.getVcpus());
        flavor.setMem_size(info.getRam());
        flavor.setCategory("cpu");
        flavor.setMax_sysdisk_size(info.getDisk());
        flavor.setMin_sysdisk_size(info.getDisk());
        flavor.setDefault_sysdisk_size(info.getDisk());
        flavor.setExtend1(info.getIs_public() != null ? info.getIs_public().toString() : null);
        flavor.setExtend2(info.getDisabled() != null ? info.getDisabled().toString() : null);
        return flavor;
    }

    public static CmdbDiskRes toNxcDisk(CloudAccessBean bean, OsDisk info) {
        CmdbDiskRes disk = new CmdbDiskRes();
        /**
         * 封装资源唯一ID
         */
        disk.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), info.getId()));
        disk.setOpen_id(info.getId());
        disk.setOpen_name(info.getName());
        disk.setCloud_type(bean.getCloudType());
        disk.setAccount_id(bean.getCmpId());
        disk.setSize(info.getSize());
        disk.setType(info.getVolume_type());
        disk.setCategory(DiskCategory.DATA.getValue());
        disk.setOpen_status(info.getStatus());
        disk.setStatus("in-use".equals(info.getStatus())? DiskStatus.IN_USE.value() :DiskStatus.AVAILABLE.value());
        return disk;
    }
//
//    public static CmdbVpcRes toNxcVpc(CloudAccessBean bean, CloudosVpc info) {
//        CmdbVpcRes res = new CmdbVpcRes();
//        /**
//         * 封装资源唯一ID
//         */
//        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_VPC_RES.value(), info.getId()));
//        res.setOpen_id(info.getId());
//        res.setOpen_name(info.getName());
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        res.setCidr(info.getIpv4_cidr());
////        res.setVpc_status(info.getStatus());
//        res.setDesc(info.getDescription());
//        return res;
//    }
//
    public static CmdbRouteRes toNxcRoute(CloudAccessBean bean, OsRouter info) {
        CmdbRouteRes res = new CmdbRouteRes();
        /**
         * 封装资源唯一ID
         */
        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_ROUTE_RES.value(), info.getId()));
        res.setOpen_id(info.getId());
        res.setOpen_name(info.getName());
        res.setCloud_type(bean.getCloudType());
        res.setAccount_id(bean.getCmpId());
        res.setDesc(info.getDescription());
        res.setStatus(info.getStatus());
        if (ObjectUtil.isNotNull(info.getCreated_at()))
            res.setCreate_time(DateUtils.utcToTimestamp(info.getCreated_at()));
        return res;
    }

    public static CmdbSubnetRes toNxcSubnet(CloudAccessBean bean, OsSubnet info) {
        CmdbSubnetRes res = new CmdbSubnetRes();
        /**
         * 封装资源唯一ID
         */
        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), info.getId()));
        res.setOpen_id(info.getId());
        res.setOpen_name(info.getName());
        res.setCloud_type(bean.getCloudType());
        res.setAccount_id(bean.getCmpId());
        res.setDesc(info.getDescription());
        if (ObjectUtil.isNotNull(info.getCreated_at()))
            res.setCreate_time(DateUtils.utcToTimestamp(info.getCreated_at()));
        if (ObjectUtil.isNotEmpty(info.getUpdated_at()))
            res.setUpdate_time(DateUtils.utcToTimestamp(info.getUpdated_at()));
        res.setCidr_ipv4(info.getCidr());
        res.setGateway_ipv4(info.getGateway_ip());
        res.setDhcp_enable(info.getEnable_dhcp() ? 1 : 0);
        return res;
    }
//
//    public static CmdbNetcardRes toNxcNetcard(CloudAccessBean bean, CloudosPort info) {
//        CmdbNetcardRes res = new CmdbNetcardRes();
//        /**
//         * 封装资源唯一ID
//         */
//        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), info.getId()));
//        res.setOpen_id(info.getId());
//        res.setOpen_name(info.getName());
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        res.setDesc(info.getDescription());
//        if (ObjectUtil.isNotNull(info.getCreated_at()))
//            res.setCreate_time(DateUtils.utcToTimestamp(info.getCreated_at()));
//        if (ObjectUtil.isNotEmpty(info.getUpdated_at()))
//            res.setUpdate_time(DateUtils.utcToTimestamp(info.getUpdated_at()));
//        if (ObjectUtil.isNotEmpty(info.getFixed_ips())) {
//            res.setIpv4_address(info.getFixed_ips().get(0).getIp_address());
//        }
//        res.setMac_address(info.getMac_address());
//        res.setType(info.getVif_type());
//        res.setDesc(info.getDescription());
//        res.setStatus(info.getStatus());
//        return res;
//    }
//
//    //转换网卡关联的子网信息
//    public static CmdbSubnetRes toNxcSubnet(CloudAccessBean bean, CloudosPort info) {
//        CmdbSubnetRes res = new CmdbSubnetRes();
//        /**
//         * 封装资源唯一ID
//         */
//        if (ObjectUtil.isNotEmpty(info.getFixed_ips())) {
//            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), info.getFixed_ips().get(0).getSubnet_id()));
//            res.setOpen_id(info.getFixed_ips().get(0).getSubnet_id());
//        } else {
//            return null;
//        }
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        return res;
//    }
//
//    //转换网卡关联的经典网络信息
//    public static CmdbVswitchRes toNxcVswitch(CloudAccessBean bean, CloudosPort info) {
//        CmdbVswitchRes res = new CmdbVswitchRes();
//        /**
//         * 封装资源唯一ID
//         */
//        if (ObjectUtil.isNotEmpty(info.getNetwork_id())) {
//            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), info.getNetwork_id()));
//            res.setOpen_id(info.getNetwork_id());
//        } else {
//            return null;
//        }
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        return res;
//    }
//
//    //转换网卡关联的云主机信息
//    public static CmdbInstanceRes toNxcInstance(CloudAccessBean bean, CloudosPort info) {
//        CmdbInstanceRes res = new CmdbInstanceRes();
//        /**
//         * 封装资源唯一ID
//         */
//        if (ObjectUtil.isNotEmpty(info.getDevice_id())) {
//            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), info.getDevice_id()));
//            res.setOpen_id(info.getDevice_id());
//        } else {
//            return null;
//        }
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        return res;
//    }
//
//    //转换网卡关联的安全组信息
//    public static void toNxcSecuritygroup(List<Association> associations, CloudAccessBean bean, CloudosPort info, CmdbInstanceRes instance, CmdbNetcardRes card) {
//        if (ObjectUtil.isNotEmpty(info.getSecurity_groups())) {
//            info.getSecurity_groups().forEach(sg -> {
//                CmdbSecuritygroupRes res = new CmdbSecuritygroupRes();
//                /**
//                 * 封装资源唯一ID
//                 */
//                res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), sg));
//                res.setOpen_id(sg);
//                res.setCloud_type(bean.getCloudType());
//                res.setAccount_id(bean.getCmpId());
//                if (ObjectUtil.isNotNull(instance)) {
//                    associations.add(AssociationUtils.toAssociation(res, instance));
//                }
//                associations.add(AssociationUtils.toAssociation(res, card));
//            });
//        }
//    }
//
//    //转换网卡关联的云主机信息
//    public static CmdbIpRes toNxcIp(CloudAccessBean bean, CloudosPort info) {
//        CmdbIpRes res = new CmdbIpRes();
//        /**
//         * 封装资源唯一ID
//         */
//        if (ObjectUtil.isNotEmpty(info.getFixed_ips()) && ObjectUtil.isNotEmpty(info.getFixed_ips().get(0).getIp_address())) {
//            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_IP_RES.value(), info.getFixed_ips().get(0).getIp_address()));
//            res.setAddress(info.getFixed_ips().get(0).getIp_address());
//        } else {
//            return null;
//        }
//        res.setType(IpType.PRIVATE_IP.getValue());
//        res.setMac(info.getMac_address());
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        return res;
//    }
//
//
    //转换经典网络模型
    public static CmdbVswitchRes toNxcVswitch(CloudAccessBean bean, OsNetwork info) {
        CmdbVswitchRes res = new CmdbVswitchRes();
        /**
         * 封装资源唯一ID
         */
        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), info.getId()));
        res.setOpen_id(info.getId());
        res.setOpen_name(info.getName());
        res.setCloud_type(bean.getCloudType());
        res.setAccount_id(bean.getCmpId());
        res.setMtu(info.getMtu() == null ? "" : info.getMtu().toString());
        res.setType(info.getNetwork_type());
        if (ObjectUtil.isNotNull(info.getCreated_at()))
            res.setCreate_time(DateUtils.utcToTimestamp(info.getCreated_at()));
        if (ObjectUtil.isNotEmpty(info.getUpdated_at()))
            res.setUpdate_time(DateUtils.utcToTimestamp(info.getUpdated_at()));
        return res;
    }

    public static <T, P> List<Association> toAssociation(CloudAccessBean bean, T info, P res) {
        List<Association> associations = new ArrayList<>();
//        if (res instanceof CmdbDiskRes && info instanceof CloudosDisk) {//处理云主机和云盘关系
//            CmdbDiskRes diskRes = (CmdbDiskRes) res;
//            CloudosDisk disk = (CloudosDisk) info;
//            String userId = disk.getUser_id();
//            associations.add(AssociationUtils.toAssociation(diskRes, TmdbUser.class,userId));
//            if (ObjectUtil.isNotEmpty(disk.getAttachments())) {
//                disk.getAttachments().forEach(attachment -> {
//                    CmdbInstanceRes instance = new CmdbInstanceRes();
//                    instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), attachment.getServer_id()));
//                    instance.setOpen_id(attachment.getServer_id());
//                    instance.setAccount_id(bean.getCmpId());
//                    instance.setCloud_type(bean.getCloudType());
//                    associations.add(AssociationUtils.toAssociation(instance, diskRes));
//                });
//            }
//        } else
            if (res instanceof CmdbVswitchRes && info instanceof OsNetwork) {//交换机(经典网络)与子网关系
            CmdbVswitchRes vswitchRes = (CmdbVswitchRes) res;
            OsNetwork net = (OsNetwork) info;
            if (ObjectUtil.isNotEmpty(net.getSubnets())) {
                net.getSubnets().forEach(s -> {
                    CmdbSubnetRes subnet = new CmdbSubnetRes();
                    subnet.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), s));
                    subnet.setOpen_id(s);
                    subnet.setAccount_id(bean.getCmpId());
                    subnet.setCloud_type(bean.getCloudType());
                    associations.add(AssociationUtils.toAssociation(vswitchRes, subnet));
                });
            }
        }
        return associations;
    }

    public static CmdbImageRes toNxImage(CloudAccessBean bean, OsImage info) {
        CmdbImageRes image = new CmdbImageRes();
        /**
         * 封装资源唯一ID
         */
        image.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_IMAGE_RES.value(), info.getId()));
        image.setOpen_id(info.getId());
        image.setOpen_name(info.getName());
        image.setCloud_type(bean.getCloudType());
        image.setAccount_id(bean.getCmpId());
        image.setDisk_format(info.getDisk_format());
        image.setStatus(info.getStatus());
        image.setOpen_status(info.getStatus());
        image.setVisibility(info.getVisibility());
        image.setImage_source("system");
        image.setSize(info.getSize() == null ? 0f : Float.valueOf(String.format("%.2f", info.getSize() / 1024.0 / 1024 / 1024.0)));
        image.setMin_disk(info.getMin_disk() == null ? 0f : info.getMin_disk());
        if (ObjectUtil.isNull(info.getImage_type()) || "image".equals(info.getImage_type())) {
            image.setType("image");
        } else {
            return null;
        }
        image.setDesc(info.getDescription());
        return image;
    }

    public static CmdbSnapshotRes toNxSnapshot(CloudAccessBean bean, OsSnapshot info) {
        CmdbSnapshotRes snapshot = new CmdbSnapshotRes();
        /**
         * 封装资源唯一ID
         */
        snapshot.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), info.getId()));
        snapshot.setOpen_id(info.getId());
        snapshot.setOpen_name(info.getName());
        snapshot.setCloud_type(bean.getCloudType());
        snapshot.setSize(info.getSize() == null ? 0f : info.getSize().floatValue());
        snapshot.setStatus(info.getStatus());
        snapshot.setType(ResourceType.CMDB_DISK_RES.value());
        snapshot.setAccount_id(bean.getCmpId());
        if (ObjectUtil.isNotEmpty(info.getCreated_at()))
            snapshot.setCreate_time(DateUtils.utcToTimestampSSS(info.getCreated_at()));
        snapshot.setDesc(info.getDescription());
        return snapshot;
    }

    public static CmdbDiskRes toNxDisk(CloudAccessBean bean, OsSnapshot info) {
        CmdbDiskRes disk = new CmdbDiskRes();
        /**
         * 封装资源唯一ID
         */
        disk.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), info.getVolume_id()));
        disk.setOpen_id(info.getVolume_id());
        disk.setAccount_id(bean.getCmpId());
        disk.setCloud_type(bean.getCloudType());
        return disk;
    }

    public static CmdbSnapshotRes toNxSnapshot(CloudAccessBean bean, OsImage info) {
        CmdbSnapshotRes snapshot = new CmdbSnapshotRes();
        /**
         * 封装资源唯一ID
         */
        if (ObjectUtil.isNotNull(info.getImage_type()) && "snapshot".equals(info.getImage_type())) {
            snapshot.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), info.getId()));
            snapshot.setOpen_id(info.getId());
            snapshot.setOpen_name(info.getName());
            snapshot.setCloud_type(bean.getCloudType());
            snapshot.setSize(info.getSize() == null ? 0f : Float.valueOf(String.format("%.2f", info.getSize() / 1024.0 / 1024 / 1024.0)));
            snapshot.setStatus(info.getStatus());
            snapshot.setType(ResourceType.CMDB_INSTANCE_RES.value());
            snapshot.setDesc(info.getDescription());
            snapshot.setAccount_id(bean.getCmpId());
            return snapshot;
        } else
            return null;
    }
//
//
    public static JSONArray fetchResourceToJsonArray(DescribeOpenstackRequest request, String url, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.get(url, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (ObjectUtil.isEmpty(key)) {
            array = JSONArray.parseArray(responseJson);
        } else if (HttpClientUtil.isJsonarray(JSONObject.parseObject(responseJson).get(key).toString())) {
            array = JSONObject.parseObject(responseJson).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(responseJson).getJSONObject(key));
        }
        return array;
    }
//
//    public static JSONArray covertJsonArray(String json, String key) {
//        JSONArray array = new JSONArray();
//        if (ObjectUtil.isEmpty(json) || "null".equals(json))
//            return array;
//        if (HttpClientUtil.isJsonarray(JSONObject.parseObject(json).get(key).toString())) {
//            array = JSONObject.parseObject(json).getJSONArray(key);
//        } else {
//            array.add(JSONObject.parseObject(json).getJSONObject(key));
//        }
//        return array;
//    }
//
    public static CmdbKeypairRes toNxcKeypair(CloudAccessBean bean, OsInstance info) {

        if (ObjectUtil.isNotNull(info.getKey_name())) {
            CmdbKeypairRes res = new CmdbKeypairRes();
            /**
             * 封装资源唯一ID
             */
            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_KEYPAIR_RES.value(), info.getKey_name()));
            res.setOpen_id(info.getKey_name());
            res.setOpen_name(info.getKey_name());
            res.setCloud_type(bean.getCloudType());
            res.setAccount_id(bean.getCmpId());
            return res;
        } else {
            return null;
        }
    }
    public static CmdbKeypairRes toNxcKeypair(CloudAccessBean bean, OsKeypair info) {
        CmdbKeypairRes res = new CmdbKeypairRes();
        /**
         * 封装资源唯一ID
         */
        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_KEYPAIR_RES.value(), info.getKeypair().getName()));
        res.setOpen_id(info.getKeypair().getName());
        res.setOpen_name(info.getKeypair().getName());
        res.setCloud_type(bean.getCloudType());
        res.setAccount_id(bean.getCmpId());
        res.setDesc(info.getKeypair().getName());
        res.setPublic_key(info.getKeypair().getPublic_key());
        res.setFingerprint(info.getKeypair().getFingerprint());
        return res;
    }

    public static CmdbSecuritygroupRes toNxcSecurityGroup(CloudAccessBean bean, OsSecurityGroup info) {
        CmdbSecuritygroupRes securitygroup = new CmdbSecuritygroupRes();
        securitygroup.setAccount_id(bean.getCmpId());
        securitygroup.setCloud_type(bean.getCloudType());
        securitygroup.setDesc(info.getDescription());
        securitygroup.setOpen_id(info.getId());
        securitygroup.setOpen_name(info.getName());
        securitygroup.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
        return securitygroup;
    }

    public static void toNxcSecurityGroupRule(List<CmdbSecuritygroupRule> rules, CloudAccessBean bean, OsSecurityGroup info) {
        if (ObjectUtil.isNotEmpty(info.getSecurity_group_rules())) {
            info.getSecurity_group_rules().forEach(r -> {
                CmdbSecuritygroupRule rule = new CmdbSecuritygroupRule();
                rule.setAccount_id(bean.getCmpId());
                rule.setCloud_type(bean.getCloudType());
                rule.setOpen_id(r.getId());
                rule.setDirection(r.getDirection());
                rule.setDesc(r.getDescription());
                rule.setCreate_time(DateUtils.utcToTimestamp(r.getCreated_at()));
                rule.setUpdate_time(DateUtils.utcToTimestamp(r.getUpdated_at()));
                rule.setSource_cidr(r.getRemote_ip_prefix());
                rule.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), r.getId()));
                rules.add(rule);
            });
        }
    }

}
