
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsSnapshot;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class StorageSnapshotService {

    public static final StorageSnapshotService bean = new StorageSnapshotService();


    public void fetchSnapshot(BaseCloudRequest arguments) {
        List<CmdbSnapshotRes> snapshotList = new ArrayList<>();
        List<Association> associationList = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();

        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.CINDERV3.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getSnapshotUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.SNAPSHOT.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                OsSnapshot osSnapshot = Converts.parseAndConvert(d.toString(), new TypeReference<OsSnapshot>() {});
                /** 磁盘快照资源数据模型转换*/
                CmdbSnapshotRes snapshot = Converts.toNxSnapshot(arguments.getBody().getAccess(), osSnapshot);
                snapshotList.add(snapshot);
                //快照和磁盘的关系数据
                CmdbDiskRes disk =Converts.toNxDisk(arguments.getBody().getAccess(), osSnapshot);
                associationList.add(AssociationUtils.toAssociation(disk, snapshot));
                // 快照
                tags.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), d));
            });
        }

        Map<Class,List> result = new HashMap<>();
        result.put(CmdbSnapshotRes.class,snapshotList);
        result.put(Association.class,associationList);
        result.put(TmdbResourceSet.class,tags);
        BaseCloudService.fetchSend(arguments, result);
    }

}
