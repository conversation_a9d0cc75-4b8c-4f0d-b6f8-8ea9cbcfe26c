package com.futong.gemini.plugin.cloud.openstack.r.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsNetwork {
    @JsonProperty("provider:physical_network")
    private String physical_network;

    private String updated_at;

    private Integer revision_number;

    private Integer mtu;

    private String id;

    @JsonProperty("router:external")
    private Boolean external;

    private List<Object> availability_zone_hints;

    private List<Object> availability_zones;
    @JsonProperty("provider:segmentation_id")
    private Integer segmentation_id;

    private String ipv4_address_scope;

    private Boolean shared;

    private String project_id;

    private Boolean l2_adjacency;

    private String status;

    private List<String> subnets;

    private String description;

    private List<Object> tags;

    private String ipv6_address_scope;

    private String qos_policy_id;

    private String name;

    private Boolean admin_state_up;

    private String tenant_id;

    private String created_at;

    @JsonProperty("provider:network_type")
    private String network_type;

    private List<String> alias_zone_ids;

}
