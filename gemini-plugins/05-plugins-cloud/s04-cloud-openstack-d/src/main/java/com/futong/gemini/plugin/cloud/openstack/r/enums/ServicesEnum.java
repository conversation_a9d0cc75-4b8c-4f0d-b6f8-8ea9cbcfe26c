package com.futong.gemini.plugin.cloud.openstack.r.enums;

public enum ServicesEnum {
    NEUTRON("network", "neutron"),
    CINDERV2("volumev2", "cinderv2"),
    CINDERV3("volumev3", "cinderv3"),
    PANKO("event", "panko"),
    CINDER("volume","cinder"),
    NOVA("compute","nova"),
    GLANCE("image","glance"),
    SWIFT("object-store","swift"),
    GNOCCHI("metric","gnocchi"),
    PLACEMENT("placement","placement"),
    CEILOMETER("metering", "ceilometer"),
    AODH("alarming","aodh"),
    KEYSTONE("identity","keystone"),
    PLUTO("pluto","pluto"),
    EMLA("emla","emla");
    private String name;
    private String type;
    private ServicesEnum(String type,String name ) {
        this.name = name;
        this.type = type;
    }
    public String getName() {
        return name;
    }
    public String getType() {
        return type;
    }
}
