package com.futong.gemini.plugin.cloud.openstack.r.common;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;

public class FetchConverts {

    public static void createInstanceEventJob(JSONObject arguments, String instanceId, String type) {
        try {
            PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
            JSONObject req = new JSONObject();
            JSONObject instance = new JSONObject();
            instance.put("ids", new String[]{instanceId});
            instance.put("resourceType", type);
            instance.put("auth", arguments.getJSONObject("auth"));
            req.put("auth", arguments.getJSONObject("auth"));
            req.put("body", instance);
            req.put("action", ActionType.REFRESH_COMPUTE_INSTANCE.value());
            toJobInfo(plugin.getRealm(), plugin.getVersion(), req);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void toJobInfo(String realm, String version, JSONObject jobRequest) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(realm);
        jobInfo.setVersion(version);
        jobInfo.setCount(1);
        jobInfo.setRequest(jobRequest);
        jobInfo.setTriggerTime(System.currentTimeMillis() + 30000);
        SpringUtil.getBean(GourdProxy.class).createTempEventJob(jobInfo);
    }
}
