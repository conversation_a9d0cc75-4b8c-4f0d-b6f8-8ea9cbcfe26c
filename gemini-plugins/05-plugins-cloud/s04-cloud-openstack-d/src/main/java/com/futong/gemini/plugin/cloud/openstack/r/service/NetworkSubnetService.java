
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRule;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsSubnet;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.TypeReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class NetworkSubnetService {

    public static final NetworkSubnetService bean = new NetworkSubnetService();

    public void fetchSubnet(BaseCloudRequest arguments) {
        List<CmdbSubnetRes> subnetList = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();

        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NEUTRON.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getSubnetUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.SUBNET.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                OsSubnet osSubnet = Converts.parseAndConvert(d.toString(), new TypeReference<OsSubnet>() {});
                /** 子网资源数据模型转换*/
                CmdbSubnetRes subnet = Converts.toNxcSubnet(arguments.getBody().getAccess(), osSubnet);
                subnetList.add(subnet);
                // 子网标签数据
                sets.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), d));
            });
        }

        Map<Class,List> result = new HashMap<>();
        result.put(CmdbSubnetRes.class,subnetList);
        result.put(TmdbResourceSet.class,sets);
        BaseCloudService.fetchSend(arguments, result);

    }
}
