
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.JsonKeyEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.JobUtils;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsCluster;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsComputenode;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsHost;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ComputeNodeService {

    public static final ComputeNodeService bean = new ComputeNodeService();
    public List<JobInfo> splitInstanceDataJob(DescribeOpenstackRequest request, JSONObject arguments){
        JSONArray hostArray = fetchComputenode(arguments);
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        log.info("hostArray ={}",hostArray);
        /** 拆分宿主机dataJob */
        jobs = Stream.of(jobs,
                        JobUtils.splitDataJob(request,hostArray,
                                arguments,
                                new String[] {ResourceEnum.HOST.getValue(),ResourceEnum.STORAGEPOOL.getValue(),ResourceEnum.HOST_VM.getValue()},
                                JsonKeyEnum.ID.getValue()))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        return jobs;
    }

    private JSONArray  fetchComputenode(JSONObject arguments){
        JSONArray hosts = new JSONArray();
        DescribeOpenstackRequest request =   BaseClient.bodys.get().toJavaObject(DescribeOpenstackRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeNodeUrl(),null),
                null);
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(n -> {
                JSONObject nodeObj = (JSONObject) n;
                OsComputenode node = Converts.parseAndConvert(nodeObj.toJSONString(), new TypeReference<OsComputenode>() {});
                if(node.getVmType() ==1)//排除虚拟化类型为1(vmware)的计算节点
                    return;
                try {
                    JSONArray hostArray = Converts.fetchResourceToJsonArray(request,
                            URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(),new String[]{"crm",request.getAuthToken().getToken().getProject().getId(),node.getHostName(),"clusters-hosts"}),
                            ResourceEnum.CLUSTER.getValue());
                    if(ObjectUtil.isNotEmpty(hostArray)){
                        hostArray.forEach(host ->{
                            JSONObject hostObj = (JSONObject) host;
                            OsCluster cluster = Converts.parseAndConvert(hostObj.toJSONString(), new TypeReference<OsCluster>() {});
                            if(ObjectUtil.isNotEmpty(cluster.getChildren())){
                                cluster.getChildren().forEach(child->{
                                    JSONObject childObj = new JSONObject();
                                    childObj.put("name",child.getName());
                                    childObj.put("id",child.getMorId());
                                    childObj.put("computeNode",node.getHostName());
                                    hosts.add(childObj);
                                });
                            }
                        });
                    }
                }catch (Exception e){
                    log.error("获取计算节点下宿主机异常,{}",e.getMessage());
                }
            });
        }
        return hosts;
    }

    public JSONObject queryHostTotal() {
        JSONObject data = new JSONObject();
        DescribeOpenstackRequest request =   BaseClient.bodys.get().toJavaObject(DescribeOpenstackRequest.class);
        JSONArray hostArray = fetchComputenode(null);
        int count = 0;
        int cpuCount = 0;
        int memoryCount = 0;
        if(hostArray!=null) {
            count = hostArray.size();
            for(int i=0;i<hostArray.size();i++) {
                JSONObject host = hostArray.getJSONObject(i);
                String json = HttpClientUtil.get(BaseClient.auths.get(), request, URLUtils.bean.getHostUrl(), new String[]{"crm", request.getAuthToken().getToken().getProject().getId(), host.getString("computeNode"), "hostDetail", host.getString("id")});
                OsHost cloudHost = Converts.parseAndConvert(json, new TypeReference<OsHost>() {
                });
                cpuCount += cloudHost.getCpuCount();
                memoryCount += cloudHost.getMemorySize() / 1024;
            }
        }

        data.put("count",count);
        data.put("cpuCount",cpuCount);
        data.put("memoryCount",memoryCount);
        return data;
    }

}
