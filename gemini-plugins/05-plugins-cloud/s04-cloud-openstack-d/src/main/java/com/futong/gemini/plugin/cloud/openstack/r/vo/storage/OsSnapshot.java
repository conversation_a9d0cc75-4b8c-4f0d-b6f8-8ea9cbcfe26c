package com.futong.gemini.plugin.cloud.openstack.r.vo.storage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsSnapshot {
    private String status;

    private String description;

    private String updated_at;

    private String volume_id;

    private String id;

    private Integer size;
    @JsonProperty("os-extended-snapshot-attributes:progress")
    private String progress;

    private String name;
    @JsonProperty("os-extended-snapshot-attributes:project_id")
    private String project_id;

    private String created_at;

    private Metadata metadata;

    @Data
    public static class Metadata {

        private String user_id;

        private String user_name;

    }
}
