package com.futong.gemini.plugin.cloud.openstack.r.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsSubnet {

    private String updated_at;

    private String ipv6_ra_mode;

    private List<Allocation_pools> allocation_pools;

    private List<Object> host_routes;

    private Integer revision_number;

    private String ipv6_address_mode;

    private String id;

    private List<Object> dns_nameservers;

    private String gateway_ip;

    private String project_id;

    private String description;

    private List<Object> tags;

    private String cidr;

    private String subnetpool_id;

    private List<Object> service_types;

    private String name;

    private Boolean enable_dhcp;

    private String segment_id;

    private String network_id;

    private String tenant_id;

    private String created_at;

    private Integer ip_version;

    private String vpc_id;

    @Data
    public static class Allocation_pools {

        private String start;

        private String end;

    }
}
