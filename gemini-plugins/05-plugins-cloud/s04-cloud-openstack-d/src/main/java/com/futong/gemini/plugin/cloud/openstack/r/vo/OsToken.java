package com.futong.gemini.plugin.cloud.openstack.r.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OsToken implements Serializable {

    private final  long serialVersionUID = 1L;

    private String authToken;

    private Token token;
    @Data
    public static class Roles {
        private String id;
        private String name;
    }
    @Data
    public static class ProjectDomain {
        private String id;
        private String name;
    }
    @Data
    public static class Project {
        private ProjectDomain domain;
        private String id;
        private String name;
    }
    @Data
    public static class Endpoints {

        private String url;
        @J<PERSON>NField(name = "interface")
        private String name;
        private String region;

        private String region_id;

        private String id;
    }

    @Data
    public static class Catalog {

        private List<Endpoints> endpoints;

        private String type;

        private String id;

        private String name;

    }

    @Data
    public static class UserDomain {

        private String id;

        private String name;

    }

    @Data
    public static class User {

        private String password_expires_at;

        private UserDomain domain;

        private String id;

        private String name;

    }

    @Data
    public static class Token {

        private Boolean is_domain;

        private List<String> methods;

        private List<Roles> roles;

        private String expires_at;

        private Project project;

        private List<Catalog> catalog;

        private User user;

        private List<String> audit_ids;

        private String issued_at;

    }
}
