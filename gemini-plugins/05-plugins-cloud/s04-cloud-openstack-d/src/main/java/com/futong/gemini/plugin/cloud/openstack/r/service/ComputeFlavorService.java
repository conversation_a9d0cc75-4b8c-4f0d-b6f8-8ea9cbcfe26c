package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbFlavor;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbOsRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsFlavor;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ComputeFlavorService类用于从云资源提供商获取计算规格信息，并转换为内部使用的数据格式
 */
public class ComputeFlavorService {

    // 单例实例
    public static final ComputeFlavorService bean = new ComputeFlavorService();

    /**
     * 根据提供的参数获取规格信息，并将其转换后发送消息
     *
     * @param request 请求参数，包含获取规格信息所需的必要信息
     */
    public void fetchFlavor(BaseCloudRequest request) {
        List<CmdbFlavor> flavorList = new ArrayList<>();
        List<OsFlavor> flavors = fetchCloudosFlavor(request);
        if(ObjectUtil.isNotEmpty(flavors)){
            flavors.forEach(flavor ->{
                // 规格资源数据模型转换
                flavorList.add(Converts.toNxcFlavor(BaseClient.auths.get(), flavor));
            });
        }
        Map<Class,List> result = new HashMap<>();
        result.put(OsFlavor.class,flavorList);
        BaseCloudService.fetchSend(request, result);
    }

    /**
     * 获取规格资源信息，并将其转换为CloudosFlavor格式
     *
     * @param arguments 请求参数，用于构建获取规格信息的请求
     * @return 返回CloudosFlavor格式的规格信息列表
     */
    public List<OsFlavor> fetchCloudosFlavor(BaseCloudRequest arguments){
        List<OsFlavor> flavors =  new ArrayList<>();
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getFlavorUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.FLAVOR.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 解析并转换JSON对象为CloudosFlavor对象
                OsFlavor flavor = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<OsFlavor>() {});
                flavors.add(flavor);
            });
        }
        return flavors;
    }
}
