
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbOsRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsHost;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ComputeHostService {

    public static final ComputeHostService bean = new ComputeHostService();

    public void fetchHost(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getHostUrl(),null,null);
        List<CmdbHostRes> hosts = new ArrayList<>();
        url = url + "/detail";
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.HYPERVISOR.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(inst -> {
                JSONObject instObj = (JSONObject) inst;
                OsHost host = Converts.parseAndConvert(instObj.toJSONString(), new TypeReference<OsHost>() {});
                CmdbHostRes hostRes = Converts.toNxcHost(BaseClient.auths.get(), host);
                //缓存宿主机所属计算节点名称，为后续性能数据同步时使用
                hostRes.setExtend1(request.getComputeNode());
                hosts.add(hostRes);
            });
        }

        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        Map<Class,List> result = new HashMap<>();
        result.put(CmdbHostRes.class,hosts);
        BaseCloudService.fetchSend(arguments, result);
    }

    public void fetchHostVmRelation(DescribeOpenstackRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(hostId -> {
                JSONArray array = Converts.fetchResourceToJsonArray(request,
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), new String[]{"crm", request.getAuthToken().getToken().getProject().getId(), request.getComputeNode(), "vms-host-simple", hostId}),
                        ResourceEnum.HOST_VM.getValue());
                List<CmdbInstanceRes> instanceResList = new ArrayList<>();
                array.forEach(pool -> {
                    JSONObject json = (JSONObject) pool;
                    CmdbInstanceRes instance = new CmdbInstanceRes();
                    instance.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), BaseClient.auths.get().getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), json.getString("uuid")));
                    instance.setAccount_id(BaseClient.auths.get().getCmpId());
                    instance.setOpen_id(json.getString("uuid"));
                    instance.setCloud_type(BaseClient.auths.get().getCloudType());
                    instanceResList.add(instance);
                });
                if (ObjectUtil.isNotEmpty(instanceResList)) {
                    /**宿主机与云主机关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), hostId, new CmdbHostRes()), instanceResList.stream().collect(Collectors.toList())));
                }
            });
        } catch (Exception e) {
            log.error("同步宿主机与云主机资源关系数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.h3c.cloudos.fetch.host.instance.relation.fail"), e);
        }
        /**
         * 推送宿主机与云主机关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }
}
