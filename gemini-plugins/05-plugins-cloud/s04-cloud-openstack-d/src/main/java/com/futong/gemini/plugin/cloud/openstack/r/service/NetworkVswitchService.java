
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbVswitchRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsNetwork;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class NetworkVswitchService {

    public static final NetworkVswitchService bean = new NetworkVswitchService();

    public void fetchNetwork(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getNetworkUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.NETWORRK.getValue());
        List<CmdbVswitchRes> vswitchList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                /** 密钥对数据模型转换*/
                OsNetwork net = Converts.parseAndConvert(d.toString(), new TypeReference<OsNetwork>() {});
                CmdbVswitchRes vswitch = Converts.toNxcVswitch(arguments.getBody().getAccess(), net);
                List<Association> associationList = Converts.toAssociation(BaseClient.auths.get(), d, vswitch);
                if(ObjectUtil.isNotEmpty(associationList))
                    associations.addAll(associationList);
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
                vswitchList.add(vswitch);
            });
        }
        Map<Class,List> result = new HashMap<>();
        result.put(CmdbVswitchRes.class,vswitchList);
        result.put(Association.class,associations);
        result.put(TmdbResourceSet.class,tags);
        BaseCloudService.fetchSend(arguments, result);

    }

//    public static BaseResponse createNetwork(JSONObject arguments) {
//        String message = "操作成功.";
//        log.info("cloudos创建经典网络接受参数={}", arguments.toString());
//        CreateNetworkRequest request = BaseClient.bodys.get().toJavaObject(CreateNetworkRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetworkUrl(), null);
//        try {
//            JSONObject network = new JSONObject();
//            JSONObject cloud = request.getCloud();
//            cloud.put("tenant_id",request.getAuthToken().getToken().getProject().getId());
//            cloud.put("provider:segmentation_id", cloud.getString("segmentation_id"));
//            cloud.remove("segmentation_id");
//            network.put("network",request.getCloud());
//            log.info("创建经典网络url={}", url);
//            log.info("创建经典网络参数={}", network.toString());
//            HttpClientUtil.post(url, network.toString(), config);
//        } catch (Exception e) {
//            log.error("创建经典网络异常{}", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建经典网络异常");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse deleteNetwork(JSONObject arguments) {
//        String message = "操作成功.";
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        JSONObject body = BaseClient.bodys.get();
//        List<String> instanceIds = getInstanceList(body);
//        for (String instanceId : instanceIds) {
//            try {
//                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetworkUrl(), new String[]{ "/"+instanceId});
//                HttpClientUtil.delete(url, config);
//            } catch (Exception e) {
//                log.error("删除经典网络异常{}", e);
//                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除经典网络异常");
//            }
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static List<String> getInstanceList(JSONObject body) {
//        List<String> instanceIds = new ArrayList<>();
//        if(body.containsKey("cis")) {
//            JSONArray cis = body.getJSONArray("cis");
//            for (int i = 0; i < cis.size(); i++) {
//                JSONObject ci = cis.getJSONObject(i);
//                String instanceId = ci.getString("openId");
//                instanceIds.add(instanceId);
//            }
//        }else if(body.containsKey("ci")){
//            JSONObject ci = body.getJSONObject("ci");
//            String instanceId = ci.getString("openId");
//            instanceIds.add(instanceId);
//        }
//        return instanceIds;
//    }
}
