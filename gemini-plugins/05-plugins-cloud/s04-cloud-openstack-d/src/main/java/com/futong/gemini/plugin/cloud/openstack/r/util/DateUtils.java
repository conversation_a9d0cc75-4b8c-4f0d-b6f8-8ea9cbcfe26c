package com.futong.gemini.plugin.cloud.openstack.r.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class DateUtils {

    public static  SimpleDateFormat utc_sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    public static  SimpleDateFormat utc_sdfSSS = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");

    public static  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static SimpleDateFormat utc_sdf_z = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
    public static String timestampToStr(long timestamp) {
        return simpleDateFormat.format(new Date(timestamp));
    }

    public static String utcToStr(String utcTime) {
        utc_sdf_z.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = utc_sdf_z.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        utc_sdf_z.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = utc_sdf_z.format(utcDate.getTime());
        try {
            locatlDate = utc_sdf_z.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return simpleDateFormat.format(locatlDate);
    }
    public static String longToUtc(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat();
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        sdf.applyPattern("yyyy-MM-dd'T'HH:mm:ss");
        return sdf.format(new Date(time));
    }

    public static void main(String[] args) {
        System.out.println(utcToTimestampSSS("2024-10-08T05:58:27.000000"));
    }

    public static Long utcToTimestampSSS(String utcTime) {
        utc_sdfSSS.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = utc_sdfSSS.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        utc_sdfSSS.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = utc_sdfSSS.format(utcDate.getTime());
        try {
            locatlDate = utc_sdfSSS.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return  locatlDate.getTime();
    }

    public static Long utcToTimestamp(String utcTime) {
        utc_sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = utc_sdf.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        utc_sdf.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = utc_sdf.format(utcDate.getTime());
        try {
            locatlDate = utc_sdf.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return  locatlDate.getTime();
    }
}
