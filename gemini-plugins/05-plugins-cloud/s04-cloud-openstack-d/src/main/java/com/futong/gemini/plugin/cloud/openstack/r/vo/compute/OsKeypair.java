package com.futong.gemini.plugin.cloud.openstack.r.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsKeypair {

    private Keypair keypair;

    @Data
    public static class Keypair {

        private String public_key;

        private String name;

        private String fingerprint;
    }
}
