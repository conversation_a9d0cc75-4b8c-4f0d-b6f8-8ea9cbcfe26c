
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbRouteRes;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsRouter;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class NetworkRouterService {

    public static final NetworkRouterService bean = new NetworkRouterService();

    public void fetchRouter(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NEUTRON.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getRouterUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.ROUTER.getValue());
        List<CmdbRouteRes> routeList = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                /** 密钥对数据模型转换*/
                OsRouter router = Converts.parseAndConvert(d.toString(), new TypeReference<OsRouter>() {});
                CmdbRouteRes route = Converts.toNxcRoute(arguments.getBody().getAccess(), router);
                routeList.add(route);
                tags.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), d));
            });
        }
        Map<Class,List> result = new HashMap<>();
        result.put(CmdbRouteRes.class,routeList);
        result.put(TmdbResourceSet.class,tags);
        BaseCloudService.fetchSend(arguments, result);
    }

}
