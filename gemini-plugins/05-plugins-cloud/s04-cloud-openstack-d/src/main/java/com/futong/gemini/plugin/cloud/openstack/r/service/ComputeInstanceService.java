package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.openstack.r.common.FetchConverts;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.OsToken;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsFlavor;
import com.futong.gemini.plugin.cloud.openstack.r.vo.compute.OsInstance;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsImage;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBodyCI;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ComputeInstanceService {

    public static final ComputeInstanceService bean = new ComputeInstanceService();

    public void fetchInstance(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getComputeUrl(),null,null);
        url = url + "?all_tenants=1&limit=2";
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.SERVER.getValue());
        List<CmdbInstanceRes> instanceList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbDiskRes> diskList = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(array)) {
            Map<String, OsImage> imageMap = StorageImageService.bean.fetchCloudosImage(arguments).stream().collect(Collectors.toMap(OsImage::getId, Function.identity(), (key1, key2) -> key1));
            Map<String, OsFlavor> flavorMap = ComputeFlavorService.bean.fetchCloudosFlavor(arguments).stream().collect(Collectors.toMap(OsFlavor::getId, Function.identity(), (key1, key2) -> key1));
            array.forEach(inst -> {
                JSONObject instObj = (JSONObject) inst;
                /**转换json对象为 CloudosInstance*/
                OsInstance osInstance = Converts.parseAndConvert(instObj.toJSONString(), new TypeReference<OsInstance>() {
                });
                /**云主机资源数据模型转换*/
                CmdbInstanceRes instance = Converts.toNxcInstance(arguments.getBody().getAccess(), osInstance);
                /**从规格内获取CPU、内存信息补全模型*/
                if (ObjectUtil.isNotNull(osInstance.getFlavor()) && ObjectUtil.isNotNull(flavorMap.get(osInstance.getFlavor().getId()))) {
                    instance.setCpu_size(flavorMap.get(osInstance.getFlavor().getId()).getVcpus());
                    instance.setMem_size(flavorMap.get(osInstance.getFlavor().getId()).getRam());
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(arguments.getBody().getAccess(), osInstance.getFlavor().getId(), new CmdbFlavor()), instance));
                    // 获取系统盘信息、云主机与磁盘关系
                    CmdbDiskRes disk = Converts.toNxcDisk(arguments.getBody().getAccess(), flavorMap.get(osInstance.getFlavor().getId()));
                    if (ObjectUtil.isNotNull(disk)) {
                        disk.setOpen_id("fake" + IdUtils.encryptId(instance.getOpen_id(), ResourceType.CMDB_DISK_RES.value()));
                        disk.setOpen_name(instance.getOpen_name() + "_system_disk");
                        disk.setRes_id(IdUtils.encryptId(disk.getAccount_id(), disk.getCloud_type(), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));
                        associations.add(AssociationUtils.toAssociation(instance, disk));
                        diskList.add(disk);
                    }
                }
                /**云主机与镜像关系*/
                if (ObjectUtil.isNotNull(osInstance.getImage()) && ObjectUtil.isNotNull(imageMap.get(osInstance.getImage().getId()))) {
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(arguments.getBody().getAccess(), osInstance.getImage().getId(), new CmdbImageRes()), instance));
                    // 云主机与操作系统关系
                    CmdbOsRes os = Converts.toNxcOsRes(arguments.getBody().getAccess(), imageMap.get(osInstance.getImage().getId()));
                    osList.add(os);
                    associations.add(AssociationUtils.toAssociation(instance, os));
                }
                // 获取云主机下密钥对，并绑定云主机与密钥关系
                CmdbKeypairRes keypairRes = Converts.toNxcKeypair(arguments.getBody().getAccess(), osInstance);
                if (ObjectUtil.isNotNull(keypairRes)) {
                    associations.add(AssociationUtils.toAssociation(keypairRes, instance));
                }
                //北新仓资源关系数据
                sets.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), osInstance));
                instanceList.add(instance);
            });
            imageMap.clear();
            flavorMap.clear();
        }

        Map<Class,List> result = new HashMap<>();
        result.put(CmdbInstanceRes.class,instanceList);
        result.put(Association.class,associations);
        result.put(CmdbOsRes.class,osList);
        result.put(CmdbDiskRes.class,diskList);
        result.put(TmdbResourceSet.class,sets);
        BaseCloudService.fetchSend(arguments, result);
    }

    public static BaseResponse pauseInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("os-stop", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse resumeInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("resume", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse suspendInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("suspend", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse rebootInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            JSONObject obj = new JSONObject();
            String type = "";
            if(type==null||"".equals(type))
                type = "SOFT";
            obj.put("type", type);
            params.put("reboot", obj);
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse startInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("os-start", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse stopInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("os-stop", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }



    /**
     * 创建云主机
     * @param arguments
     * @return
     */
    public static BaseResponse createInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("os-stop", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteInstance(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        List<BaseCloudRequestBodyCI> cis = arguments.getBody().getCis();
        for(BaseCloudRequestBodyCI ci : cis) {
            url+="/"+ci.getOpenId()+"/action";
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
            JSONObject params = new JSONObject();
            params.put("forceDelete", "");
            HttpClientUtil.post(url, params.toJSONString(), config);
            FetchConverts.createInstanceEventJob(arguments,ci.getOpenId(),"update");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse webConsole(BaseCloudRequest arguments) {
        String message = "操作成功.";
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NOVA.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getServerUrl(),null,null);

        BaseCloudRequestBodyCI ci = arguments.getBody().getCi();
        url+="/"+ci.getOpenId()+"/remote-consoles";
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject params = new JSONObject();
        JSONObject remoteConsole = new JSONObject();
        remoteConsole.put("protocol", "vnc");
        remoteConsole.put("type", "novnc");
        params.put("remote_console", remoteConsole);
        String result = HttpClientUtil.post(url, params.toJSONString(), config);
        return new BaseDataResponse(result);
    }
//
//    public static BaseResponse deleteKeyPair(JSONObject arguments) {
//        String message = "操作成功.";
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        JSONObject body = BaseClient.bodys.get();
//        List<String> instanceIds = getInstanceList(body);
//        for (String instanceId : instanceIds) {
//            try {
//                String url = URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs", instanceId});
//                HttpClientUtil.delete(url, config);
//            } catch (Exception e) {
//                log.error("删除秘钥对异常{}", e);
//                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除秘钥对异常");
//            }
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//
//    public static List<String> getInstanceList(JSONObject body) {
//        List<String> instanceIds = new ArrayList<>();
//        if(body.containsKey("cis")) {
//            JSONArray cis = body.getJSONArray("cis");
//            for (int i = 0; i < cis.size(); i++) {
//                JSONObject ci = cis.getJSONObject(i);
//                String instanceId = ci.getString("openId");
//                instanceIds.add(instanceId);
//            }
//        }else if(body.containsKey("ci")){
//            JSONObject ci = body.getJSONObject("ci");
//            String instanceId = ci.getString("openId");
//            instanceIds.add(instanceId);
//        }
//        return instanceIds;
//    }
//
//    public static BaseResponse updateInstance(JSONObject arguments) {
//        String message = "成功发起修改云主机请求.";
//        log.info("vmware修改虚拟机接受参数={}", arguments.toString());
//        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String url = URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers"});
//        try {
//        } catch (Exception e) {
//            log.error("创建云主机异常{}", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改云主机异常");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//
//    public static BaseResponse refleshInstance(JSONObject arguments) {
//        String message = "操作成功.";
//        log.info("触发更新虚拟机状态");
//        CloudAccessBean accessBean = arguments.getBody().getAccess();
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        JSONArray array = Converts.fetchResourceToJsonArray(request,
//                URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/detail"}),
//                ResourceEnum.SERVER.getValue());
//        List<CmdbInstanceRes> instanceList = new ArrayList<>();
//        boolean flag = false;
//        if("update".equals(request.getResourceType())) {
//            if (ObjectUtil.isNotEmpty(array)) {
//                for(int i = 0; i < array.size(); i++) {
//                    JSONObject instObj = array.getJSONObject(i);
//                    /**转换json对象为 CloudosInstance*/
//                    CloudosInstance cloudosInstance = Converts.parseAndConvert(instObj.toJSONString(), new TypeReference<CloudosInstance>() {
//                    });
//                    /**云主机资源数据模型转换*/
//                    CmdbInstanceRes instance = Converts.toNxcInstance(arguments.getBody().getAccess(), cloudosInstance);
//                    log.info("虚拟机状态={}", instance.getOpen_status());
//                    if("BUILD".equals(instance.getOpen_status())) {
//                        flag = true;
//                    }else {
//                        if(request.getIds().contains(instance.getOpen_id()))
//                            instanceList.add(instance);
//                    }
//                }
//                if(flag) {
//                    PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
//                    JobInfo jobInfo = toJobInfo(plugin.getRealm(), plugin.getVersion(), arguments);
//                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
//                }
//                BaseCloudService.toRefreshMessageAndSend("update", instanceList, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
//            }
//        }else {
//            List<CmdbInstanceRes> list = new ArrayList<>();
//            request.getIds().forEach(vmId -> {
//                CmdbInstanceRes ins = new CmdbInstanceRes();
//                String resId= IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0", vmId);
//                ins.setRes_id(resId);
//                list.add(ins);
//            });
//            BaseCloudService.toRefreshMessageAndSend("delete", list, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
//        }
//        return BaseResponse.SUCCESS;
//    }
//
//    public static JobInfo toJobInfo(String realm, String version, JSONObject jobRequest) {
//        JobInfo jobInfo = new JobInfo();
//        jobInfo.setRealm(realm);
//        jobInfo.setVersion(version);
//        jobInfo.setCount(1);
//        jobInfo.setRequest(jobRequest);
//        jobInfo.setTriggerTime(System.currentTimeMillis() + 10000);
//        return jobInfo;
//    }
//
//    public int queryInstanceTotal(DescribeCloudosRequest request) {
//        JSONArray array = Converts.fetchResourceToJsonArray(request,
//                URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/detail"}),
//                ResourceEnum.SERVER.getValue());
//        int count = 0;
//        if(array!=null) {
//            count = array.size();
//        }
//        return count;
//    }
//
//    public String queryInstancePass(CloudosInstancePassRequest request) {
//        HttpClientConfig config = new HttpClientConfig();
//        String pass = "";
//        String url = URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/",  request.getCi().getOpen_id()});
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String responseJson = HttpClientUtil.get(url, config);
//        if(ObjectUtil.isNotEmpty(responseJson)) {
//            JSONObject jsonObject = JSONObject.parseObject(responseJson);
//            if (jsonObject.get("server") != null) {
//                JSONObject server = jsonObject.getJSONObject("server");
//                pass = server.getString("adminPass");
//            }
//        }
//        return pass;
//    }
//
//    public List<JSONObject> queryNetworkOutput(CloudosInstancePassRequest request) {
//        HttpClientConfig config = new HttpClientConfig();
//        List<JSONObject> resultList = new ArrayList<>();
//        String url = URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getNetsecurityUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "h3c-phycial-mappings"});
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String responseJson = HttpClientUtil.get(url, config);
//        if(ObjectUtil.isNotEmpty(responseJson)) {
//            JSONObject obj = JSON.parseObject(responseJson);
//            if (null != obj){
//                JSONArray list = obj.getJSONArray("phycialmappinglist");
//                for (int i = 0; i < list.size(); i++){
//                    JSONObject jsonObject = list.getJSONObject(i);
//                    JSONArray phycialMappingList = jsonObject.getJSONArray("PhycialMapping_info");
//                    for (int y = 0; y < phycialMappingList.size(); y++){
//                        JSONObject phycialObj = phycialMappingList.getJSONObject(y);
//                        String phycialName = phycialObj.getString("phycial_name");
//                        JSONObject result = new JSONObject();
//                        result.put("azone",jsonObject.getString("azone"));
//                        result.put("encodeName",phycialName);
//                        result.put("decodeName",new String(Base64.getDecoder().decode(phycialName)));
//                        resultList.add(result);
//                    }
//                }
//            }
//        }
//        return resultList;
//    }
//
//    public List<JSONObject> queryAzone(CloudosInstancePassRequest request) {
//        HttpClientConfig config = new HttpClientConfig();
//        List<JSONObject> resultList = new ArrayList<>();
//        String url = URLUtils.bean.makeUrl(arguments.getBody().getAccess(), URLUtils.bean.getAzoneUrl(), null);
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String responseJson = HttpClientUtil.get(url, config);
//        String resourceType = request.getModel().getString("resourceType");
//        String azoneName = request.getModel().getString("azoneName");
//        String virtType = request.getModel().getString("virtType");
//        JSONArray obj = JSON.parseArray(responseJson);
//        if (null != obj){
//            for (int i = 0; i < obj.size(); i++){
//                JSONObject jsonObject = obj.getJSONObject(i);
//                String zone = jsonObject.getString("zone");
//                String type = jsonObject.getString("resourceType");
//                String vir = jsonObject.getString("virtType");
//                if (StringUtils.isNotEmpty(resourceType)){
//                    if (!type.equals(resourceType)){
//                        continue;
//                    }
//                }
//                if(!"CAS".equals(vir))
//                    continue;
//                if (StringUtils.isNotEmpty(azoneName) && azoneName.equals(zone)){
//                    resultList.add(jsonObject);
//                }else if (vir.equals(virtType)){
//                    resultList.add(jsonObject);
//                }else if(StringUtils.isEmpty(virtType) && StringUtils.isEmpty(azoneName)) {
//                    resultList.add(jsonObject);
//                }
//            }
//        }
//        return resultList;
//    }

}
