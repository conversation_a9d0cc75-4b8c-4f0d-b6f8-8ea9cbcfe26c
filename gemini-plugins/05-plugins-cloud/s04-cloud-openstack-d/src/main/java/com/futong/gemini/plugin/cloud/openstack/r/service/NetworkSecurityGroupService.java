
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbRouteRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRule;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsRouter;
import com.futong.gemini.plugin.cloud.openstack.r.vo.network.OsSecurityGroup;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class NetworkSecurityGroupService {

    public static final NetworkSecurityGroupService bean = new NetworkSecurityGroupService();

    public void fetchSecurityGroup(BaseCloudRequest arguments) {
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.NEUTRON.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getSecurityGroupListUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.SECURITY_GROUP.getValue());
        List<CmdbSecuritygroupRes> list = new ArrayList<>();
        List<CmdbSecuritygroupRule> rules = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                /** 密钥对数据模型转换*/
                OsSecurityGroup osGroup = Converts.parseAndConvert(d.toString(), new TypeReference<OsSecurityGroup>() {});
                CmdbSecuritygroupRes group = Converts.toNxcSecurityGroup(arguments.getBody().getAccess(), osGroup);
                list.add(group);
                List<CmdbSecuritygroupRule> ruleRes = new ArrayList<>();
                Converts.toNxcSecurityGroupRule(ruleRes,arguments.getBody().getAccess(), osGroup);
                if(ObjectUtil.isNotEmpty(ruleRes)){
                    rules.addAll(ruleRes);
                    /**安全组与安全组规则数据*/
                    associations.add(AssociationUtils.toAssociation(group,ruleRes.stream().collect(Collectors.toList())));
                }
                tags.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), osGroup));
            });
        }
        Map<Class,List> result = new HashMap<>();
        result.put(CmdbSecuritygroupRes.class,list);
        result.put(CmdbSecuritygroupRule.class,rules);
        result.put(TmdbResourceSet.class,tags);
        result.put(Association.class,associations);
        BaseCloudService.fetchSend(arguments, result);
    }

    private List<OsSecurityGroup> fetchCloudosSecurityGroup(BaseCloudRequest arguments){
        List<OsSecurityGroup> groups =  new ArrayList<>();
        DescribeOpenstackRequest request =   arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getSecurityGroupListUrl(),null),
                ResourceEnum.SECURITY_GROUP.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                OsSecurityGroup group = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<OsSecurityGroup>() {});
                groups.add(group);
            });
        }
        return groups;
    }
}
