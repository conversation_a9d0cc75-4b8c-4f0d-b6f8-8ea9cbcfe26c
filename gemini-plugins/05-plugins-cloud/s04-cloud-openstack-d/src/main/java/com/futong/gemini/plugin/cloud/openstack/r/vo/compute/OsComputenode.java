package com.futong.gemini.plugin.cloud.openstack.r.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsComputenode {

    private String allPass;

    private String resourceNode;

    private String useLocalStorage;

    private String linkClone;

    private String mode;

    private String ip;

    private String hostName;

    private Integer vmType;

    private String hostIp;

    private String userName;

    private String password;

    private String poolName;

    private String vxlanOverlayMode;

    private String storageZone;

    private String clusterName;

    private String clusterId;

    private String initMode;

    private List<Vswitch> vswitch;

    private Integer external;

    private String onestorRbdClusterName;

    private String cpuAllocate;

    private String ramAllocate;

    private String diskAllocate;

    private String cpuAllocateSwitch;

    private String ramAllocateSwitch;

    private String diskAllocateSwitch;

    private String frameworkType;

    private String protocal;

    private String port;

    private String transport_url;

    @Data
    public static class Vswitch {

        private String netName;

        private String device;

    }

}

