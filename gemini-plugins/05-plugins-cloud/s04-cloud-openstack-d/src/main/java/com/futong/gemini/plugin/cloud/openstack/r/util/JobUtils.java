package com.futong.gemini.plugin.cloud.openstack.r.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.request.PageSortSearchRequest;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class JobUtils {

    //拆分性能采集dataJob
    public static <T> List<JobInfo> splitPerfJob(BaseDataResponse<BaseResponseDataListModel<T>> response , BasePageSortSearchRequest searchRequest, JSONObject arguments){
        List<JobInfo> jobs = new ArrayList<>();
        log.info(StrUtil.format("同步性能数据，共有：{}台，分: {} 次同步.",response.getData().getCount(), (int)Math.ceil((float)response.getData().getCount()/searchRequest.getSize())));
        for (int i =2; i <= response.getData().getCount()/searchRequest.getSize(); i++) {
            PageSortSearchRequest request = new PageSortSearchRequest();
            request.setCurrent(i);
            request.setSize(searchRequest.getSize());
            request.setSort(searchRequest.getSort());
            request.setSortField(searchRequest.getSortField());
            JSONObject cloneArguments = arguments.clone();
            JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
            cloneBody.put("current", i);
            cloneBody.put("size", searchRequest.getSize());
            cloneBody.put("sort",searchRequest.getSort());
            cloneBody.put("sortField", searchRequest.getSortField());
            cloneArguments.put("body", cloneBody);
            JobInfo jobInfo = new JobInfo();
            jobInfo.setRequest(cloneArguments);
            jobs.add(jobInfo);
        }
        return jobs;
    }
    public static List<JobInfo> splitDataJob(JSONObject arguments, String resourceType, String resourceId){
        List<JobInfo> jobs = new ArrayList<>();
        JSONObject cloneArguments = arguments.clone();
        JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
        cloneBody.put("ids", new ArrayList<String>().add(resourceId));
        cloneBody.put("resourceType", resourceType);
        cloneArguments.put("body", cloneBody);
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRequest(cloneArguments);
        jobs.add(jobInfo);
        return jobs;
    }

    public static List<JobInfo> splitDataJob(DescribeOpenstackRequest request, JSONArray jsonArray, JSONObject arguments, String[] resourceTypes, String key){
        if(ObjectUtil.isNull(jsonArray) || ObjectUtil.isEmpty(jsonArray)){
            return null;
        }
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {
            List<String> ids = new ArrayList<>();

            Integer size = request.getFecthSize() ==null? 10: request.getFecthSize();
            log.info(StrUtil.format("共有{},条数：{},分: {} 次同步.",resourceType, jsonArray.size(), (int)Math.ceil((float)jsonArray.size()/size)));
            for (int i =1; i <= jsonArray.size(); i++) {
                ids.add(jsonArray.getJSONObject(i-1).getString(key));
                if(i % size == 0 || i == jsonArray.size()){
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("computeNode", jsonArray.getJSONObject(i-1).getString("computeNode"));
                    cloneBody.put("resourceType",resourceType);
                    cloneBody.put("fecthSize", size);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                    ids = new ArrayList<>();
                }
            }
        });
        return jobs;
    }
}
