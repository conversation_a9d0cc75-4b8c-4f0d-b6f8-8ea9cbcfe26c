package com.futong.gemini.plugin.cloud.openstack.r.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsInstance {

    private Image image;

    @JsonProperty("OS-EXT-STS:vm_state")
    private String status;

    @JsonProperty("OS-EXT-SRV-ATTR:instance_name")
    private String instanceName;

    @JsonProperty("OS-SRV-USG:launched_at")
    private String launchedAt;

    private Flavor flavor;

    private String id;

    private List<Security_groups> security_groups;

    private String user_id;

    private String accessIPv4;

    private String accessIPv6;

    @JsonProperty("OS-EXT-AZ:availability_zone")
    private String az;

    private String config_drive;

    private String updated;

    private String hostId;
    @JsonProperty("OS-EXT-SRV-ATTR:host")
    private String hostName;

    private String key_name;
    @JsonProperty("OS-EXT-SRV-ATTR:hypervisor_hostname")
    private String hypervisor;

    private String name;

    private String created;

    private String tenant_id;

    private Metadata metadata;

    @Data
    public static class Image {
        private String id;
    }
    @Data
    public static class Flavor {
        private String id;
    }
    @Data
    public static class Security_groups {
        private String name;
    }

    @Data
    public static class Metadata {

        private String zone_uuid;

        private String description;

        private String alias;

        private String storageTypeId;

        private String enableAdminPass;

        private String user_name;

        private String h3c_extend_api;

    }
}
