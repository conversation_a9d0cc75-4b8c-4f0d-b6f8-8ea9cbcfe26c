package com.futong.gemini.plugin.cloud.openstack.r.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OsHost {
    private String id;

    private String user;

    private String pwd;

    private String hostPoolId;

    private String clusterId;

    private String name;

    private String ip;

    private String mask;

    private String model;

    private String vendor;

    private Integer cpuCount;

    private String cpuModel;

    private String cpuFrequence;

    private String diskSize;

    private Integer memorySize;

    private String status;

    private String cpuSockets;

    private Integer cpuCores;

    private String enableStorNode;

    private String enableBackupNetwork;

    private String maintainMode;

    private String serialNumber;

    private String cvkMaintain;

    private Float cpuRate;

    private Float memRate;

    private String vmNum;

    private Float diskRate;

    private String localStorage;

    private String cpuSize;

    private String cpuHz;
}
