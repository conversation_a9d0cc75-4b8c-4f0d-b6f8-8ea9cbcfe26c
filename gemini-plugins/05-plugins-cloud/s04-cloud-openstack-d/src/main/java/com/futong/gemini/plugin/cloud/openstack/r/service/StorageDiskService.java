
package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRule;
import com.futong.gemini.plugin.cloud.openstack.r.convert.Converts;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.openstack.r.enums.ServicesEnum;
import com.futong.gemini.plugin.cloud.openstack.r.request.DescribeOpenstackRequest;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.openstack.r.vo.storage.OsDisk;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class StorageDiskService {

    public static final StorageDiskService bean = new StorageDiskService();


    public void fetchDisk(BaseCloudRequest arguments) {
        List<CmdbDiskRes> diskList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        DescribeOpenstackRequest request = arguments.getBody().toJavaObject(DescribeOpenstackRequest.class);
        request.setAccess(arguments.getBody().getAccess());
        String serviceUrl = request.getEndpointMap().get(SecureTool.encrypt(request.getAccess().getServerIp()+"_"+ ServicesEnum.CINDERV3.getType())).getUrl();
        String url = URLUtils.bean.makeOsUrl(serviceUrl, URLUtils.bean.getVolumeUrl(),null,null);
        JSONArray array = Converts.fetchResourceToJsonArray(request, url, ResourceEnum.VOLUME.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(d ->{
                /** 云盘资源数据模型转换*/
                OsDisk disk = Converts.parseAndConvert(d.toString(), new TypeReference<OsDisk>() {});
                CmdbDiskRes diskRes = Converts.toNxcDisk(arguments.getBody().getAccess(), disk);
                diskList.add(diskRes);
                List<Association> associationList = Converts.toAssociation(arguments.getBody().getAccess(), d, diskRes);
                if(ObjectUtil.isNotEmpty(associationList))
                    associations.addAll(associationList);

                tags.add(Converts.toTmdbResourceSet(arguments.getBody().getAccess(), d));
            });
        }

        Map<Class,List> result = new HashMap<>();
        result.put(CmdbDiskRes.class,diskList);
        result.put(Association.class,associations);
        result.put(TmdbResourceSet.class,tags);
        BaseCloudService.fetchSend(arguments, result);
    }


//
//
//    public static BaseResponse deleteStorageDisk(JSONObject arguments) {
//        String message = "操作成功.";
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//
//        JSONObject body = BaseClient.bodys.get();
//        List<String> diskIds = getInstanceList(body);
//        for (String diskId : diskIds) {
//            try {
//                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "volumes", diskId});
//                HttpClientUtil.delete(url,  config);
//            } catch (Exception e) {
//                log.error("删除云盘异常{}", e);
//                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除云盘异常");
//            }
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse attachStorageDisk(JSONObject arguments) {
//        String message = "操作成功.";
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//
//        JSONObject body = BaseClient.bodys.get();
//        String diskId = getCiId(body);
//        String instanceId = getInstance(body);
//        try {
//            JSONObject params = new JSONObject();
//            JSONObject disk = new JSONObject();
//            disk.put("volumeId",diskId);
//            params.put("volumeAttachment",disk);
//            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(),"servers",instanceId, "os-volume_attachments"});
//            HttpClientUtil.post(url,params.toJSONString(), config);
//        } catch (Exception e) {
//            log.error("删除云主机异常{}", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "绑定云盘异常");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse detachStorageDisk(JSONObject arguments) {
//        String message = "操作成功.";
//        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//        HttpClientConfig config = new HttpClientConfig();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//
//        JSONObject body = BaseClient.bodys.get();
//        String diskId = getCiId(body);
//        String instanceId = getDetachInstance(body);
//        try {
//            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(),"servers",instanceId, "os-volume_attachments", diskId});
//            HttpClientUtil.delete(url, config);
//        } catch (Exception e) {
//            log.error("解绑云盘异常{}", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "解绑云盘异常");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    public static String getCiId(JSONObject body) {
        JSONObject ci = body.getJSONObject("ci");
        String ciId = ci.getString("openId");
        return ciId;
    }

    public static String getInstance(JSONObject body) {
        JSONObject ci = body.getJSONObject("model");
        String instanceId = ci.getString("instanceId");
        return instanceId;
    }

    public static String getDetachInstance(JSONObject body) {
        JSONObject ci = body.getJSONObject("ci");
        JSONObject instance = ci.getJSONObject("relationInstance");
        String openId = instance.getString("relationOpenId");
        return openId;
    }
}
