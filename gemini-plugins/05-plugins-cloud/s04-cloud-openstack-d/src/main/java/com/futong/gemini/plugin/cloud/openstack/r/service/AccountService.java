package com.futong.gemini.plugin.cloud.openstack.r.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.openstack.r.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.openstack.r.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AccountService {

    public static final AccountService bean = new AccountService();
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();
    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            HttpClientUtil.getOpenstackToken(request.getBody().getAccess());
        } catch (Exception e) {
            log.error("账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.auth.success");
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        List<JSONObject> dispatchers = listAllDispatcher(result);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            }
        });
        return dispatchers;
    }
}
