{"description": "The initial management account only supports the main account for management", "model": [{"type": "main", "name": "Cloud platform operation master account", "description": "Cloud platform operation master account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your <PERSON> Account."}, {"field": "username", "label": "username", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Username"}, {"field": "password", "label": "password", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Password"}, {"field": "serverIp", "label": "Server Host", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Host"}, {"field": "serverPort", "label": "Server Port", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Port"}, {"field": "protocol", "label": "protocol", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}], "required": true, "isUpdate": true, "tips": "Please select your Protocol"}, {"field": "version", "label": "Api Version", "type": "select", "value": "", "items": [{"label": "V2", "value": "V2"}, {"label": "V3", "value": "V3"}], "required": true, "isUpdate": true, "tips": "Please select your Api Version"}, {"field": "multiRegion", "label": "Multiple Regions", "type": "select", "value": "", "items": [{"label": "YES", "value": true}, {"label": "NO", "value": false}], "required": true, "isUpdate": true, "tips": "Please choose if you have Multiple Regions"}, {"field": "projectId", "label": "Project ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Project ID"}, {"field": "domain", "label": "Domain", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Domain"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}]}