package com.futong.gemini.plugin.cloud.sdk.model.image;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建镜像请求参数
 */
@Setter
@Getter
public class CreateImageRequest implements Serializable {

    private String vcIp;
    private String vmName;
    private String vmfolder;
    private String templateId;
    private String datastoreId;
    private String hostId;
    private String dcId;
    private String clusterId;

}
