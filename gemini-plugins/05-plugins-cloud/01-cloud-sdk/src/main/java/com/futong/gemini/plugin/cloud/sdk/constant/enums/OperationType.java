package com.futong.gemini.plugin.cloud.sdk.constant.enums;

public enum OperationType {

    CREATE("新建"),
    DEPLOY("克隆"),
    STOP("停止"),
    START("启动"),
    REBOOT("重启"),
    DELETE("删除"),
    UPDATE("修改"),
    FETCH("获取"),
    ATTACH("挂载"),
    DETACH("卸载"),
    BIND("绑定"),
    UNBIND("解绑"),
    GET("获取"),
    QUERY("查询"),
    REFRESH("刷新"),
    RESET("回滚"),
    DISCONNECT("断开"),
    CONNECT("连接"),
    ENTER("进入维护模式"),
    LEAVE("离开维护模式"),
    CONSOLE("控制台"),
    VALID("校验");

    String cname;

    OperationType(String cname) {
        this.cname = cname;
    }

    public String cname() {
        return cname;
    }
}
