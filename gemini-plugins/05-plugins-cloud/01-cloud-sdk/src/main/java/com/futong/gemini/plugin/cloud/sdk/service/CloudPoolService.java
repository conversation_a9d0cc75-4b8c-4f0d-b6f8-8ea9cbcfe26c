package com.futong.gemini.plugin.cloud.sdk.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.feign.FTFeignUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.common.OperationProxy;
import com.futong.gemini.plugin.cloud.sdk.model.CloudPoolBean;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class CloudPoolService {

    public static List<CloudPoolBean> getCloudPoolList(String cloudType){
        try {
            OperationProxy proxy = FTFeignUtils.target(OperationProxy.class, "futong-public-operation:50012");
            OperationProxy.CloudPoolsRequest cloudPoolsRequest = new OperationProxy.CloudPoolsRequest();
            cloudPoolsRequest.setCloudType(cloudType);
            BaseDataResponse<List<CloudPoolBean>> cloudPoolList = proxy.getCloudPoolList(cloudPoolsRequest);
            if (CollUtil.isEmpty(cloudPoolList.getData())) {
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, StrUtil.format("云类型{}获取资源池信息为空", cloudType));
            }
            return cloudPoolList.getData();
        } catch (Exception e) {
            log.error(StrUtil.format("云类型{}无法获取资源池信息", cloudType), e);
            throw new BaseException(BaseResponse.FAIL_PARAM, e, StrUtil.format("云类型{}无法获取资源池信息", cloudType));
        }
    }

}
