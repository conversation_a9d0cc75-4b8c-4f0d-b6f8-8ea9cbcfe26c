package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyClient extends J<PERSON><PERSON>Object {
    //认证信息
    private String endpoint;

    public BaseCloudRequestBodyClient(JSONObject json) {
        super(json);
        if (json.containsKey(endpoint)) {
            this.endpoint = json.getString("endpoint");
        }
    }

    public void setEndpoint(String endpoint) {
        this.put("endpoint", endpoint);
        this.endpoint = endpoint;
    }
}
