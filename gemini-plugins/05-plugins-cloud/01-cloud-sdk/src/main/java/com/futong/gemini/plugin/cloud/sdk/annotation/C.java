package com.futong.gemini.plugin.cloud.sdk.annotation;

import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OperationType;

import java.lang.annotation.*;

@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface C {
    //云类型及版本
    CT[] type() default {};

    //请求action
    String action() default "";

    //资源类型
    ResourceType resourceType();

    //操作类型
    OperationType operationType();

    //描述
    String desc() default "";
}
