package com.futong.gemini.plugin.cloud.sdk.model.disk;

import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建快照请求参数
 */
@Setter
@Getter
public class CreateSnapshotRequest implements Serializable {

    //快照名称
    private String name;

    //快照描述
    private String description;

    //实例信息
    private CmdbInstanceRes ci;

    //是否内存快照,true是、false否
    private Boolean memory;

    //是否静默快照,true是、false否、vmware专用参数
    private Boolean quiesce;

    //快照类型
    private String type;

    //快照扩展参数
    private String extendJson;

}
