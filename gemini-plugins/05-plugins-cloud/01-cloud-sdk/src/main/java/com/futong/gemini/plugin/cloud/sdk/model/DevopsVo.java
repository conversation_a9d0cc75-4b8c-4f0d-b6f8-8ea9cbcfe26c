package com.futong.gemini.plugin.cloud.sdk.model;
import lombok.Data;

/**
 * <AUTHOR>  
 * @Description   TMDB运维侧
 * @Date 2024/10/16 10:37
 * @return  
 */
@Data
public class DevopsVo {
    /** 主键ID */
    private String bizId;
    /** 云账号ID */
    private String accountId;
    /** 云类型 */
    private String cloudType ;
    /** 空间名称 */
    private String devopsName ;
    /** 空间值 */
    private String devopsValue ;
    /** 对应数据字典 */
    private String dictCode ;
    /** 原始json */
    private String infoJson ;
    /** 状态 */
    private Integer status ;
    /** 创建时间 */
    private String createTime ;
    /** 修改时间 */
    private String updateTime ;
    /** 父级租户ID */
    private String parentTenantId ;
    /** 父级运维侧ID */
    private String parentBizId ;
}
