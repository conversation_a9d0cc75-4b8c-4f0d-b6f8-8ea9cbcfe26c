package com.futong.gemini.plugin.cloud.sdk.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyCloud extends JSONObject {
    //认证信息
    private String regionId;

    public BaseCloudRequestBodyCloud(JSONObject json) {
        super(json);
        if (json.containsKey("regionId")) {
            this.regionId = json.getString("regionId");
        }else if (json.containsKey("RegionId")) {
            this.regionId = json.getString("RegionId");
        }
    }
    public String getRegionId(){
        if(StrUtil.isNotEmpty(regionId))
            return regionId;
        if(containsKey("regionId")){
            return this.getString("regionId");
        }else if(containsKey("RegionId")){
            return this.getString("RegionId");
        }
        return null;
    }
    public void setRegionId(String regionId) {
        this.put("regionId", regionId);
        this.regionId = regionId;
    }

    public void setRegionIdZG(String regionId) {
        this.put("RegionId", regionId);
        this.regionId = regionId;
    }
    public <T> T toJava(Class<T> clazz) {
        return toJavaObject(clazz);
    }
}
