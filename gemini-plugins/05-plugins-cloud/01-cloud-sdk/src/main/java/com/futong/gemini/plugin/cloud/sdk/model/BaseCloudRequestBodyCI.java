package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyCI extends J<PERSON>NObject {
    private String openId;
    private String resId;

    public BaseCloudRequestBodyCI(JSONObject json) {
        super(json);
        if (json.containsKey("openId")) {
            this.openId = json.getString("openId");
        }
        if (json.containsKey("resId")) {
            this.resId = json.getString("resId");
        }
    }

    public void setOpenId(String openId) {
        this.openId = openId;
        this.put("openId", openId);
    }

    public void setResId(String resId) {
        this.resId = resId;
        this.put("resId", resId);
    }


}

