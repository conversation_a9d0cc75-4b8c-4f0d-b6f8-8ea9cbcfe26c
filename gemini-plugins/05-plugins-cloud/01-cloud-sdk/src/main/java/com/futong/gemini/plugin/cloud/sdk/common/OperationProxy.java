package com.futong.gemini.plugin.cloud.sdk.common;

import com.futong.bean.CloudAccessBean;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.plugin.cloud.sdk.model.CloudPoolBean;
import com.futong.gemini.plugin.cloud.sdk.model.NetCardBean;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface OperationProxy {
    @Data
    class AccessListRequest {
        private String cmpId;
    }

    @Data
    class CloudPoolsRequest {
        private String cloudType;
    }

    @PostMapping("/access/getAllAccessList")
    BaseDataResponse<List<CloudAccessBean>> getAllAccessList(@RequestBody AccessListRequest request);

    @PostMapping("/cloudPool/selCloudPoolsLevelOneList")
    BaseDataResponse<List<CloudPoolBean>> getCloudPoolList(@RequestBody CloudPoolsRequest request);

    @GetMapping("/operationCloud/resource/netCard/getNetCardByInstanceId/{instanceResId}")
    BaseDataResponse<List<NetCardBean>> getNetCardsByInstanceId(@PathVariable("instanceResId") String instanceResId);
}
