package com.futong.gemini.plugin.cloud.sdk.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTAfter;
import com.futong.common.function.FTBefore;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.sdk.template.PluginTemplateProcess;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class BaseCloudPluginTemplate extends PluginTemplateProcess<BaseCloudRequest> {
    public BaseCloudRegister register;

    public abstract BaseCloudRegister getRegister();

    @Override
    public void init(String key) {
        super.init(key);
        register = getRegister();
        register.load();
    }

    @Override
    public BaseCloudRequest toRequest(JSONObject arguments) {
        if (!arguments.containsKey("action")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.of("无action请求信息!"));
        }
        return new BaseCloudRequest(arguments);
    }

    @Override
    public boolean onBeforeProcess(BaseCloudRequest request, JSONObject context) {
        log.info("云插件服务[" + key + "]收到请求信息:" + request.toJSONString());
        //以下两个将在v4.0.2后废弃,请勿使用,替换为AUTH_PLATFORM_ACCOUNT,GET_PLATFORM_ACCOUNT_ADD_FORM
        if (ActionType.AUTH_CLOUD_ACCOUNT == request.getAction()) {
            request.setAction(ActionType.AUTH_PLATFORM_ACCOUNT);
            if (request.containsKey("auth")) {
                if (request.getBody().getAccess() == null) {
                    request.getBody().put("auth", request.getJSONObject("auth"));
                }
            }
        } else if (ActionType.QUERY_CLOUD_ACCOUNT_MODEL == request.getAction()) {
            request.setAction(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM);
        }
        List<FTBefore<BaseCloudRequest>> ftBefore = register.getBefore(request.getAction());
        if (null == ftBefore) return true;
        for (FTBefore<BaseCloudRequest> before : ftBefore) {
            if (!before.before(request)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public BaseResponse process(BaseCloudRequest request, JSONObject context) {
        if (register.isNotExists(request.getAction())) {
            return BaseResponse.FAIL_PARAM_EMPTY.of("未找Action:" + request.getAction() + "对应得处理方法");
        }
        FTAction<BaseCloudRequest> ftAction = register.getAction(request.getAction());
        return ftAction.doAction(request);
    }

    @Override
    public void onAfterProcess(BaseCloudRequest request, JSONObject context, BaseResponse response) {
        log.info("云插件服务[" + key + "]处理结束响应结果信息:" + JSON.toJSONString(response));
        List<FTAfter<BaseCloudRequest, BaseResponse>> ftAfters = register.getAfter(request.getAction());
        if (null == ftAfters) return;
        for (FTAfter<BaseCloudRequest, BaseResponse> after : ftAfters) {
            after.after(request, response);
        }
    }


}
