package com.futong.gemini.plugin.cloud.sdk.annotation;

import java.lang.annotation.*;

@Documented
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CP {
    //字段名称
    String value() default "";

    //云类型
    CT[] type() default {};

    //云上字段名称
    String cname() default "";

    //描述
    String desc() default "";

    //是否必填
    boolean required() default false;

    //默认值
    String defaultV() default "";

    //示例值
    String example() default "";

    //表单信息json形式{}
    String form() default "{}";
    //属性扩展
    CPE[] cpe() default {};

}
