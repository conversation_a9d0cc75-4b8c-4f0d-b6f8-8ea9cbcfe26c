package com.futong.gemini.plugin.cloud.sdk.client;

import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.function.FTExecuteNull;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;

public abstract class BaseCloudClient {
    /**
     * 核心方法：获取Client对象
     *
     * @param body  请求对象
     * @param clazz Class<Client>
     * @param <C>   Client对象类型
     * @return Client对象
     */
    public abstract <C> C client(Class<C> clazz, BaseCloudRequestBody body);

    /**
     * 核心方法：执行云上操作
     *
     * @param exec   云操作函数
     * @param client 云操作Client对象
     * @param <Q>    云操作函数入参类型
     * @param <R>    云操作函数出参类型
     * @param <C>    云操作函数所属的Client对象类型
     * @return 云操作函数出参对象
     */
    public <Q, R, C> R execute(C client, Q q, FTExecute<C, Q, R> exec) {
        try {
            return exec.apply(client, q);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    //辅助方法：执行调用函数
    //自动生成Client
    public <Q, R, C> R execute(BaseCloudRequestBody body, Q q, FTExecute<C, Q, R> exec) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        C client = client(clazz.v1, body);
        return execute(client, q, exec);
    }

    //辅助方法：执行调用函数
    //自动生成Client,请求对象
    public <Q, R, C> R execute(BaseCloudRequestBody body, FTExecute<C, Q, R> exec) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        Q q;
        try {
            if (body.getCloud() != null) {
                q = body.getCloud().toJavaObject(clazz.v2);
            } else {
                q = clazz.v2.newInstance();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, "生成云资源操作请求对象失败!");
        }
        C client = client(clazz.v1, body);
        return execute(client, q, exec);
    }

    //辅助方法：执行调用函数
    //自动生成Client,请求对象
    public <R, C> R executeNull(BaseCloudRequestBody body, FTExecuteNull<C, R> exec) {
        Entry.E2<Class<C>, Class<Void>> clazz = exec.clazz();
        C client = client(clazz.v1, body);
        return execute(client, null, exec);
    }


}
