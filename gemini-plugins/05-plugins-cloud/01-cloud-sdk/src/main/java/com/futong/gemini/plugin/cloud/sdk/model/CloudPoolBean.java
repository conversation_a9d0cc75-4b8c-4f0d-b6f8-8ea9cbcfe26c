package com.futong.gemini.plugin.cloud.sdk.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Auther: huangjin
 * @Date: 2021/11/19 10:01
 * @Description: 云资源池表
 */
@Data
public class CloudPoolBean implements Serializable {

    /**
     * 云资源池ID
     */
    private Integer id;

    /**
     * 父云资源池id
     */
    private Integer pId;

    /**
     * 代码格式（id:id:id）
     */
    private String code;

    /**
     * 云资源池名称
     */
    private String poolName;

    /**
     * 云类型
     */
    private String cloudType;

    /**
     * 云账户ID
     */
    private String keyId;

    /**
     * 0:独享；1:共享
     */
    private Integer isSharing;

    /**
     * 0不同步 1自动同步
     */
    private Integer isSync;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人员
     */
    private String createName;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 公司ID(租户) 0代表所有租户
     */
    private String companyIds;

    /**
     * 云账号状态 正常normal 关闭closed
     */
    private String status;

    /**
     * 资源组value和name [{"value":""}]
     */
    private String resourceGroups;

    /**
     * 网络类型 经典网络classical 专有网络private
     */
    private String networkType;

    /**
     * 专有网络vpc数组多选[{"instanceId":""}]
     */
    private String privateNetVpcs;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型名称
     */
    private String cloudTypeName;


    /**
     * 列表编辑的list
     */
    private List<String> editList;

    /**
     * 展示的结构
     */
    private String showStructure;

    /**
     * 云账户名称
     */
    private String accountName;

    /**
     * 业务资源池ID
     */
    private String businessPoolId;

    /**
     * 云资源池ID集合
     */
    private List<Integer> cloudPoolIdList;
    /**
     * 实例名称
     */
    private String instanceName;
    /**
     * 租户名称
     */
    private String companyName;
    @TableField(exist = false)
    private List<String> companyIdList = Lists.newArrayList();
    @TableField(exist = false)
    private List<Map<String, Object>> resourceGroupList = Lists.newArrayList();
    @TableField(exist = false)
    private List<Map<String, Object>> privateNetVpcList = Lists.newArrayList();
    @TableField(exist = false)
    private  List<DevopsVo> devopsVos;
}
