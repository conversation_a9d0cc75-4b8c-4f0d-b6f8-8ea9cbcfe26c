package com.futong.gemini.plugin.cloud.sdk.model.account;

import lombok.Data;

import java.util.List;

@Data
public class CloudAccessBeanExt {
    //地域信息,电子云使用
    private List<Region> regions;
    @Data
    public static class Region{
       private String regionId;
       private String regionName;
    }
    //默认地域
    private String defaultRegion;
    //运维账号AK
    private String ywUsername;
    //运维账号SK
    private String ywPassword;
    //安全账号AK
    private String secureUsername;
    //安全账号SK
    private String securePassword;
    //代理地址
    private String proxyAddr;
    //对象存储API地址
    private String ossEndpoint;
    //k8s API地址
    private String k8sEndpoint;
    //负载均衡API地址
    private String elbEndpoint;
    //阿里SOC API地址
    private String SOCEndpoint;
    //阿里流量监测 API地址
    private String SecspyBeaverEndpoint;
    //阿里封神凭证
    private String autograph;
    //阿里封神 API地址
    private String ServiceEndpoint;
    //阿里铜雀 API地址
    private String tongqueEndpoint;
    //阿里铜雀AK
    private String tongqueAccessKey;
    //阿里铜雀SK
    private String tongqueAccessKeySecret;
    //天翼云 WEB 应用防火墙AK
    private String SecurityWebUserName;
    //天翼云 WEB 应用防火墙SK
    private String SecurityWebPassword;
    //默认密码
    private String ecsDefaultPassword;
}
