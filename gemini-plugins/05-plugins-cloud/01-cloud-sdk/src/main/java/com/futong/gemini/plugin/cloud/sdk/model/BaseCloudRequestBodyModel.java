package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyModel extends JSONObject {
    //云主机ID
    private String instanceId;
    //云主机ResId
    private String resId;

    public BaseCloudRequestBodyModel(JSONObject json) {
        super(json);
        if (json.containsKey("instanceId")) {
            this.instanceId = json.getString("instanceId");
        }
        if (json.containsKey("resId")) {
            this.resId = json.getString("resId");
        }
    }

    public <T> T toJava(Class<T> clazz) {
        return toJavaObject(clazz);
    }

}
