package com.futong.gemini.plugin.cloud.sdk.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;

public class GourdUtils {
    public static BaseResponse addGourdLevel(BaseCloudRequest request){
        GourdProxy proxy = SpringUtil.getBean(GourdProxy.class);
        JSONObject level = new JSONObject();
        level.put("id", request.getBody().getAccess().getCmpId());
        level.put("parentId", request.getPlugin().getRealm());
        level.put("name", StrUtil.emptyToDefault(request.getBody().getAccess().getAccountName(), request.getBody().getAccess().getCmpId()));
        return proxy.createLevel(level);
    }
    public static BaseResponse addGourdLevel(String id, String parentId, String name) {
        GourdProxy proxy = SpringUtil.getBean(GourdProxy.class);
        JSONObject level = new JSONObject();
        level.put("id", id);
        if (StrUtil.isNotEmpty(parentId)) {
            level.put("parentId", parentId);
        }
        level.put("name", name);
        return proxy.createLevel(level);
    }
}
