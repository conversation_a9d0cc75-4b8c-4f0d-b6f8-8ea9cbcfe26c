package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyBiz extends JSONObject {
    private String resId;

    public BaseCloudRequestBodyBiz(JSONObject json) {
        super(json);
    }

    public void setResId(String resId) {
        put("resId", resId);
        this.resId = resId;
    }
}

