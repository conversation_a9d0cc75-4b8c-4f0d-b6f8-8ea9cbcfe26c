package com.futong.gemini.plugin.cloud.sdk.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONPath;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTAfter;
import com.futong.common.function.FTBefore;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public abstract class BaseCloudRegister {
    Map<ActionType, FTAction<BaseCloudRequest>> actionRegister = new HashMap<>();
    Map<Object, List<FTBefore<BaseCloudRequest>>> beforeRegister = new HashMap<>();
    Map<Object, List<FTAfter<BaseCloudRequest, BaseResponse>>> afterRegister = new HashMap<>();

    public enum InterceptorType {
        BEFORE,
        AFTER
    }

    public enum Type {
        REQUEST,
        RESPONSE
    }

    public abstract void load();

    public static class Builder {
        private BaseCloudRegister register;
        private ActionType actionType;

        public Builder(BaseCloudRegister register, ActionType actionType) {
            this.register = register;
            this.actionType = actionType;
        }


        public Builder addTransferCloud(String sourcePath, String targetPath) {
            return addTransferCloud(sourcePath, targetPath, true, null);
        }

        public Builder addTransferCloud(String sourcePath, String targetPath, boolean validNotEmpty) {
            return addTransferCloud(sourcePath, targetPath, validNotEmpty, null);
        }

        public Builder addTransferCloud(String sourcePath, String targetPath, Function<Object, Object> format) {
            return addTransferCloud(sourcePath, targetPath, true, format);
        }

        public Builder addTransferCloud(String sourcePath, String targetPath, boolean validNotEmpty, Function<Object, Object> format) {
            FTBefore<BaseCloudRequest> before = (request) -> {
                BaseUtils.transfer(request.getBody(), request.getBody().getCloud(), sourcePath, targetPath, validNotEmpty, format);
                return true;
            };
            register.registerBefore(actionType, before);
            return this;
        }

        public Builder addDefValue(String targetPath, Object value) {
            FTBefore<BaseCloudRequest> before = (request) -> {
                JSONPath.set(request, targetPath, value);
                return true;
            };
            register.registerBefore(actionType, before);
            return this;
        }

        public Builder addValidNull(String sourcePath, String message) {
            FTBefore<BaseCloudRequest> before = (request) -> {
                Object val = JSONPath.eval(request, sourcePath);
                if (ObjUtil.isEmpty(val)) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.of(message));
                }
                return true;
            };
            register.registerBefore(actionType, before);
            return this;
        }

        public Builder addTransfer(InterceptorType interceptorType, String sourcePath, String targetPath, boolean validNotEmpty, Function<Object, Object> format) {
            if (InterceptorType.BEFORE == interceptorType) {
                FTBefore<BaseCloudRequest> before = (request) -> {
                    BaseUtils.transfer(request, request, sourcePath, targetPath, validNotEmpty, format);
                    return true;
                };
                register.registerBefore(actionType, before);
            } else if (InterceptorType.AFTER == interceptorType) {
                FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                    Map<Object, Object> build = MapUtil.builder().put("request", request).put("response", response).build();
                    BaseUtils.transfer(build, build, sourcePath, targetPath, validNotEmpty, format);
                };
                register.registerAfter(actionType, after);
            }
            return this;
        }

        public Builder addBefore(FTBefore<BaseCloudRequest> before) {
            register.registerBefore(actionType, before);
            return this;
        }

        public Builder addAfter(FTAfter<BaseCloudRequest, BaseResponse> after) {
            register.registerAfter(actionType, after);
            return this;
        }


        public Builder addSetRefresh(ActionType action) {
            FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                BaseCloudService.setRefreshRequest(request, action);
            };
            register.registerAfter(actionType, after);
            return this;
        }

        public Builder addSetRefreshConfig(int refreshMaxCount, int refreshInterval, int firstRefreshLatency) {
            FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                BaseCloudService.setRefreshConfig(request, refreshMaxCount, refreshInterval, firstRefreshLatency);
            };
            register.registerAfter(actionType, after);
            return this;
        }

        public Builder addSetRefreshData(String type, String sourcePath) {
            FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                BaseCloudService.setRefreshData(request, response, type, sourcePath);
            };
            register.registerAfter(actionType, after);
            return this;
        }

        public Builder addSetRefreshCloud(String type, String sourcePath, String targetPath) {
            FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                BaseCloudService.setRefreshCloud(request, response, type, sourcePath, targetPath);
            };
            register.registerAfter(actionType, after);
            return this;
        }

        public Builder addSetRefreshSplitData(String type, String sourcePath) {
            FTAfter<BaseCloudRequest, BaseResponse> after = (request, response) -> {
                BaseCloudService.setSplitData(request, response, type, sourcePath);
            };
            register.registerAfter(actionType, after);
            return this;
        }

    }

    public <Q, R, C> Builder register(ActionType actionType, FTExecute<Q, R, C> execute, Function<FTExecute<Q, R, C>, FTAction<BaseCloudRequest>> function) {
        return register(actionType, function.apply(execute));
    }

    public Builder register(ActionType actionType, FTAction<BaseCloudRequest> ftAction) {
        actionRegister.put(actionType, ftAction);
        return new Builder(this, actionType);
    }

    public void registerBefore(FTBefore<BaseCloudRequest> ftBefore, ActionType... actionType) {
        for (ActionType type : actionType) {
            if (!beforeRegister.containsKey(type)) {
                beforeRegister.put(type, new ArrayList<>());
            }
            beforeRegister.get(type).add(ftBefore);
        }
    }

    public void registerBefore(ActionType actionType, FTBefore<BaseCloudRequest>... ftBefore) {
        if (!beforeRegister.containsKey(actionType)) {
            beforeRegister.put(actionType, new ArrayList<>());
        }
        CollUtil.addAll(beforeRegister.get(actionType), ftBefore);
    }

    public void registerAfter(ActionType actionType, FTAfter<BaseCloudRequest, BaseResponse>... ftAfter) {
        if (!afterRegister.containsKey(actionType)) {
            afterRegister.put(actionType, new ArrayList<>());
        }
        CollUtil.addAll(afterRegister.get(actionType), ftAfter);
    }

    public FTAction<BaseCloudRequest> getAction(ActionType action) {
        //以下两个将在v4.0.2后废弃,请勿使用,替换为AUTH_PLATFORM_ACCOUNT,GET_PLATFORM_ACCOUNT_ADD_FORM
        if (ActionType.AUTH_CLOUD_ACCOUNT == action) {
            action = ActionType.AUTH_PLATFORM_ACCOUNT;
        }
        if (ActionType.QUERY_CLOUD_ACCOUNT_MODEL == action) {
            action = ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM;
        }
        return actionRegister.get(action);
    }

    public List<FTBefore<BaseCloudRequest>> getBefore(ActionType action) {
        return beforeRegister.get(action);
    }

    public List<FTAfter<BaseCloudRequest, BaseResponse>> getAfter(ActionType action) {
        return afterRegister.get(action);
    }

    public boolean isNotExists(ActionType action) {
        return !isExists(action);
    }

    public boolean isExists(ActionType action) {
        return actionRegister.containsKey(action);
    }

}
