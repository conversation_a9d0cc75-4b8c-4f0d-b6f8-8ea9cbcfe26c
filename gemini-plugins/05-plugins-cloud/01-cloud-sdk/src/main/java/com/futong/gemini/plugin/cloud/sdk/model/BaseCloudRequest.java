package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import lombok.Data;

@Data
public class BaseCloudRequest extends JSONObject {

    //请求信息
    private ActionType action;
    private PluginInfo plugin;
    private BaseCloudRequestBody body;

    public BaseCloudRequest(JSONObject json) {
        super(json);
        this.action = ActionType.fromCamelValue(json.getString("action"));
        this.plugin = json.getObject("plugin", PluginInfo.class);
        if (json.containsKey("body")) {
            body = new BaseCloudRequestBody(json.getJSONObject("body"));
        } else {
            body = new BaseCloudRequestBody();
        }
    }

    public void setAction(ActionType action) {
        put("action", action.value());
        this.action = action;
    }

    public JSONObject cloneJSONObject() {
        return JSON.parseObject(this.toJSONString());
    }
}
