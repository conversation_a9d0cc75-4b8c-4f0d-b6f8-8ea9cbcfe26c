package com.futong.gemini.plugin.cloud.sdk.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.Data;
import lombok.NoArgsConstructor;
@Deprecated
@Data
@NoArgsConstructor
public class BaseCloudRequestBodyAuth extends JSONObject {
    //认证信息
    private CloudAccessBean access;

    private String proxyAddr;

    public BaseCloudRequestBodyAuth(JSONObject json) {
        super(json);
        this.access = json.toJavaObject(CloudAccessBean.class);
        loadExt();
    }

    public void loadExt() {
        if (StrUtil.isNotEmpty(access.getJsonStr())) {
            JSONObject accessExt = JSON.parseObject(access.getJsonStr());
            proxyAddr = accessExt.getString("proxyAddr");
        }
    }

    public void loadProxyAuthAccess() {
        CloudAccessBean bean = BaseCloudService.getAuthAccess(access);
        this.access = bean;
        loadExt();
    }
}

