package com.futong.gemini.plugin.cloud.sdk.common;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.utils.Entry;
import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.annotation.C;
import com.futong.gemini.plugin.cloud.sdk.annotation.CP;
import com.futong.gemini.plugin.cloud.sdk.annotation.CPE;
import com.futong.gemini.plugin.cloud.sdk.annotation.CT;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OperationType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AnnotationUtils {
    @Data
    public static class Info {
        String key;
        CloudType cloudType;
        String version;
        Class<?> clazz;
        List<CPInfo> cps = new ArrayList<>();//子属性

        public <T extends Info> T withKey() {
            this.key = cloudType.value() + ":" + version;
            return (T) this;
        }
    }

    @Data
    public static class CInfo extends Info {
        String action;
        ResourceType resourceType;
        OperationType operationType;
        String desc;
    }

    @Data
    public static class CPInfo extends Info {
        String value;
        String name;
        String cname;
        String desc;
        boolean required;
        String defaultV;//默认值
        String example;//示例
        String form;//表单信息
    }

    static Map<String, CInfo> cInfos = new HashMap<>();

    public static Map<String, CInfo> toCInfo(C c, Class clazz) {
        List<Entry.E2<CloudType, String>> cts = toCT(c.type());
        for (Entry.E2<CloudType, String> ct : cts) {
            CInfo cInfo = toCInfo(c, clazz, ct.v1, ct.v2).withKey();
            cInfos.put(cInfo.getKey(), cInfo);
        }
        return cInfos;
    }

    public static CInfo toCInfo(C c, Class clazz, CloudType cloudType, String version) {
        CInfo info = new CInfo();
        info.cloudType = cloudType;
        info.version = version;
        info.action = StrUtil.emptyToDefault(c.action(), clazz.getSimpleName());
        info.clazz = clazz;
        info.resourceType = c.resourceType();
        info.operationType = c.operationType();
        info.desc = c.desc();
        return info;
    }

    public static void putCPInfo(Class clazz, Map<String, Info> infos, List<Entry.E2<CloudType, String>> cts) {
        Field[] fields = ClassUtil.getDeclaredFields(clazz);
        for (Field field : fields) {
            CP cp = field.getAnnotation(CP.class);
            if (cp == null) {
                log.warn("{}类属性信息{}未注解@CP", clazz.getSimpleName(), field.getName());
                continue;
            }
            Map<String, CPInfo> cpInfos = toCPInfo(cp, field, cts);
            for (String key : cpInfos.keySet()) {
                Info info = infos.get(key);
                info.cps.add(cpInfos.get(key));
            }
        }
    }

    public static Map<String, CPInfo> toCPInfo(CP cp, Field field, List<Entry.E2<CloudType, String>> parentCts) {
        List<Entry.E2<CloudType, String>> cts = ArrayUtil.isEmpty(cp.type()) ? parentCts : toCT(cp.type());
        Map<String, CPInfo> infos = new HashMap<>();
        for (Entry.E2<CloudType, String> ct : cts) {
            CPInfo info = toCPInfo(cp, field, ct.v1, ct.v2).withKey();
//            if(info.clazz)
            infos.put(info.key, info);
        }
        //扩展属性信息
        if (ArrayUtil.isNotEmpty(cp.cpe())) {
            for (CPE cpe : cp.cpe()) {
                cts = toCT(cpe.type());
                for (Entry.E2<CloudType, String> ct : cts) {
                    //找到对应云类型及版本得属性对象
                    CPInfo info = infos.get(ct.v1 + ":" + ct.v2);
                    //merge该属性信息
                    mergeCPInfo(cpe, info);
                }
            }
        }
        return infos;
    }

    public static CPInfo toCPInfo(CP cp, Field field, CloudType cloudType, String version) {
        CPInfo info = new CPInfo();
        info.cloudType = cloudType;
        info.version = version;
        info.name = field.getName();
        info.clazz = field.getType();
        info.cname = cp.cname();
        info.desc = cp.desc();
        info.required = cp.required();
        info.defaultV = cp.defaultV();
        info.example = cp.example();
        info.form = cp.form();
        return info;
    }

    public static CPInfo mergeCPInfo(CPE cpe, CPInfo info) {
        info.cname = StrUtil.emptyToDefault(cpe.cname(), info.cname);
        info.desc = StrUtil.emptyToDefault(cpe.desc(), info.desc);
        info.required = StrUtil.isNotEmpty(cpe.required()) ? Boolean.valueOf(cpe.required()) : info.required;
        info.defaultV = StrUtil.emptyToDefault(cpe.defaultV(), info.defaultV);
        info.example = StrUtil.emptyToDefault(cpe.example(), info.example);
        if (!"{}".equals(cpe.form())) {//为空
            if ("{}".equals(info.form)) {
                info.form = cpe.form();
            } else {
                JSONObject json1 = JSON.parseObject(info.form);
                JSONObject json2 = JSON.parseObject(cpe.form());
                json1.putAll(json2);
                info.form = json1.toJSONString();
            }
        }
        return info;
    }

    public static List<Entry.E2<CloudType, String>> toCT(CT[] cts) {
        List<Entry.E2<CloudType, String>> infos = new ArrayList<>();
        if (ArrayUtil.isNotEmpty(cts)) {
            for (CT ct : cts) {
                CloudType[] value = ct.value();
                String version = ct.version();
                for (CloudType cloudType : value) {
                    infos.add(new Entry.E2<>(cloudType, version));
                }
            }
        } else {
            for (CloudType cloudType : CloudType.values()) {
                infos.add(new Entry.E2<>(cloudType, "ALL"));
            }
        }
        return infos;
    }

    public static void main(String[] args) {
//        List<CInfo> cInfos = toCInfo(CreateInstances.class.getAnnotation(C.class), CreateInstances.class);
//        CreateInstances.class.getField()
//        System.out.println(JSON.toJSONString(cInfos));
    }
}
