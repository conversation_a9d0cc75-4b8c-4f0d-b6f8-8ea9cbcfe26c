package com.futong.gemini.plugin.cloud.sdk.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.feign.FTFeignUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.common.OperationProxy;
import com.futong.gemini.plugin.cloud.sdk.model.NetCardBean;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class NetCardService {

    public static List<NetCardBean> getNetCardsByInstanceId(String resId){
        try {
            OperationProxy proxy = FTFeignUtils.target(OperationProxy.class, "futong-public-operation:50012");
            BaseDataResponse<List<NetCardBean>> cloudPoolList = proxy.getNetCardsByInstanceId(resId);
            if (CollUtil.isEmpty(cloudPoolList.getData())) {
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, StrUtil.format("云主机resId{}获取网卡信息为空", resId));
            }
            return cloudPoolList.getData();
        } catch (Exception e) {
            log.error(StrUtil.format("云主机resId{}无法网卡信息", resId), e);
            throw new BaseException(BaseResponse.FAIL_PARAM, e, StrUtil.format("云主机resId{}无法获取网卡信息", resId));
        }
    }
}
