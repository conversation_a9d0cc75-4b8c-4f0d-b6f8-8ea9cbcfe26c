package com.futong.gemini.plugin.cloud.sdk.annotation;

import java.lang.annotation.*;

@Documented
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CPE {
    //云类型 //必填
    CT[] type();

    //云上字段名称
    String cname() default "";

    String desc() default "";

    //是否必填
    String required() default "";

    //默认值
    String defaultV() default "";

    //示例值
    String example() default "";

    //表单信息json形式{}
    String form() default "{}";

}
