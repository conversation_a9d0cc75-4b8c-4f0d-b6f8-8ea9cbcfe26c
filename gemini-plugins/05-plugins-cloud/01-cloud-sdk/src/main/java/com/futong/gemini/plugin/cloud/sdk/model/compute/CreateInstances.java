package com.futong.gemini.plugin.cloud.sdk.model.compute;

import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.annotation.C;
import com.futong.gemini.plugin.cloud.sdk.annotation.CP;
import com.futong.gemini.plugin.cloud.sdk.annotation.CPE;
import com.futong.gemini.plugin.cloud.sdk.annotation.CT;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OperationType;

@Deprecated
@C(desc = "创建云主机实例", resourceType = ResourceType.CMDB_INSTANCE_RES, operationType = OperationType.CREATE)
public class CreateInstances  {
    @CP(value = "地域", type = {@CT({CloudType.PUBLIC_ALI})}, required = true)
    private String regionId;
    @CP(value = "可用区", type = {@CT(CloudType.PUBLIC_ALI)}, required = true)
    private String zoneId;

    @CP(value = "镜像ID",
            cpe = {
                    @CPE(type = {@CT(CloudType.PUBLIC_ALI)}, required = "true")
            }
    )
    private String imageId;
    @CP(value = "规格ID",
            cpe = {
                    @CPE(type = {@CT(CloudType.PUBLIC_ALI), @CT(CloudType.PUBLIC_TENXUN)}, required = "true"),
                    @CPE(type = {@CT(CloudType.PUBLIC_ALI)}, cname = "InstanceType")
            }
    )
    private String flavorId;
    @CP(value = "网络ID", required = true)
    private String vpcId;
    @CP(value = "子网ID", required = true,
            cpe = {
                    @CPE(type = {@CT(CloudType.PUBLIC_ALI)}, cname = "VSwitchId")
            }
    )
    private String subnetId;
    @CP(value = "实例名称", required = true)
    private String instanceName;
    @CP("实例的用户名")
    private String username;
    @CP("实例的密码")
    private String password;

}
