package com.futong.gemini.plugin.cloud.sdk.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class BaseCloudRequestBody extends JSONObject {
    //    //认证信息
//    private BaseCloudRequestBodyAuth auth = new BaseCloudRequestBodyAuth();
    //gourd调度采集器信息
    private GourdInfo gourd = new GourdInfo();
    //ci模型信息
    private BaseCloudRequestBodyCI ci = new BaseCloudRequestBodyCI();
    //cis模型信息
    private List<BaseCloudRequestBodyCI> cis = new ArrayList<>();
    //model统一资源模型信息
//    @Deprecated
//    private BaseCloudRequestBodyModel model = new BaseCloudRequestBodyModel();
    //cloud资源信息
//    @Deprecated
//    private BaseCloudRequestBodyCloud cloud = new BaseCloudRequestBodyCloud();
    //biz业务信息
//    private BaseCloudRequestBodyBiz biz = new BaseCloudRequestBodyBiz();
//    @Deprecated
    //client附加信息
//    private BaseCloudRequestBodyClient client = new BaseCloudRequestBodyClient();

    public BaseCloudRequestBody(JSONObject json) {
        super(json);
//        if (json.containsKey("auth")) {
//            this.auth = new BaseCloudRequestBodyAuth(json.getJSONObject("auth"));
//        }
        if (json.containsKey("gourd")) {
            this.gourd = json.getJSONObject("gourd").toJavaObject(GourdInfo.class);
        }
        if (json.containsKey("ci")) {
            this.ci = new BaseCloudRequestBodyCI(json.getJSONObject("ci"));
        } else {
            this.put("ci", this.ci);
        }
        if (json.containsKey("cis")) {
            JSONArray cisArray = json.getJSONArray("cis");
            for (int i = 0; i < cisArray.size(); i++) {
                cis.add(new BaseCloudRequestBodyCI(cisArray.getJSONObject(i)));
            }
        }
//        if (json.containsKey("model")) {
//            this.model = new BaseCloudRequestBodyModel(json.getJSONObject("model"));
//        }
//        if (json.containsKey("cloud")) {
//            this.cloud = new BaseCloudRequestBodyCloud(json.getJSONObject("cloud"));
//        }
//        if (json.containsKey("biz")) {
//            this.biz = new BaseCloudRequestBodyBiz(json.getJSONObject("biz"));
//        }
//        if (json.containsKey("client")) {
//            this.client = new BaseCloudRequestBodyClient(json.getJSONObject("client"));
//        }
    }

    public JSONObject getAuth() {
        JSONObject auth = getJSONObject("auth");
        if (auth == null) {
            auth = new JSONObject();
            put("auth", auth);
        }
        return auth;
    }

    public JSONObject getCloud() {
        JSONObject cloud = getJSONObject("cloud");
        if (cloud == null) {
            cloud = new JSONObject();
            put("cloud", cloud);
        }
        return cloud;
    }

    public JSONObject getModel() {
        JSONObject model = getJSONObject("model");
        if (model == null) {
            model = new JSONObject();
            put("model", model);
        }
        return model;
    }

    public JSONObject getBiz() {
        JSONObject biz = getJSONObject("biz");
        if (biz == null) {
            biz = new JSONObject();
            put("biz", biz);
        }
        return biz;
    }

    public JSONObject getApi() {
        JSONObject api = getJSONObject("api");
        if (api == null) {
            api = new JSONObject();
            put("api", api);
        }
        return api;
    }

    CloudAccessBean access;

    public CloudAccessBean getAccess() {
        if (access == null)
            this.access = BaseCloudService.getAuthAccess(getObject("auth", CloudAccessBean.class));
        return access;
    }

    public void loadAccess() {
        this.access = BaseCloudService.getAuthAccess(getAccess());
    }

}
