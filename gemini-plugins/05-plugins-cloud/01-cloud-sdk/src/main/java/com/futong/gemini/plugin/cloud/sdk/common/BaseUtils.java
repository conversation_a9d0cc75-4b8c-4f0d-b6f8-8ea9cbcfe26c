package com.futong.gemini.plugin.cloud.sdk.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.model.*;
import com.futong.gemini.model.route.RouteFactory;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

@Slf4j
public class BaseUtils {
    /**
     * 推送数据到消息队列
     *
     * @param infos
     * @param arguments
     * @param <T>
     */
    public static <T> void sendMessage(List<T> infos, JSONObject arguments) {
        if (ObjectUtil.isNotEmpty(infos)) {
            OtcMessageBatchCloud message = new OtcMessageBatchCloud<>();
            message.setCloudType(BaseClient.auths.get().getCloudType());
            message.setAccountBizId(BaseClient.auths.get().getCmpId());
            message.setDispatchId(arguments.getString("batch"));
            message.setBatchId(arguments.getString("batchId"));
            if (arguments.containsKey("tscTimeout")) {
                message.setTimeout(arguments.getInteger("tscTimeout"));
            }
            message.withBody(infos);
            BaseResponse baseResponse = RouteFactory.routeMessage(message);
            if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
                log.error("发送MQ消息到数据底座失败,{}", baseResponse.getMessage());
                throw new BaseException(baseResponse, "发送MQ消息到数据底座失败!");
            }
        }
    }

    public static <T> OtcPerfMessage<T> toPerfMessageAndSend(List<T> res, String source) {
        if (CollUtil.isEmpty(res)) {
            log.error("发送监控数据消息失败,本次获取监控数据为空");
            return null;
        }
        OtcPerfMessage<T> message = new OtcPerfMessage<>();
        message.setSource(source);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routePrefMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    public static <T> OtcPerfMessage<T> toDsPerfMessageAndSend(List<T> res, String source, String cloudType, String accountId, String createTime, String resourceType) {
        if (CollUtil.isEmpty(res)) {
            log.error("发送监控数据消息失败,本次获取监控数据为空");
            return null;
        }
        OtcDsPerfMessage<T> message = new OtcDsPerfMessage<>();
        message.setSource(source);
        message.setBody(res);
        message.setCloudType(cloudType);
        message.setAccountId(accountId);
        message.setCreateTime(createTime);
        message.setResourceType(resourceType);
        BaseResponse baseResponse = RouteFactory.routeDsPrefMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    public static <T> OtcPerfMessage<T> toNicPerfMessageAndSend(List<T> res, String source, String cloudType, String accountId, String createTime, String resourceType) {
        if (CollUtil.isEmpty(res)) {
            log.error("发送监控数据消息失败,本次获取监控数据为空");
            return null;
        }
        OtcDsPerfMessage<T> message = new OtcDsPerfMessage<>();
        message.setSource(source);
        message.setBody(res);
        message.setCloudType(cloudType);
        message.setAccountId(accountId);
        message.setCreateTime(createTime);
        message.setResourceType(resourceType);
        BaseResponse baseResponse = RouteFactory.routeNicPrefMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    public static <T> OtcMessage<T> sendMessage(List<T> res, JSONObject arguments, Class<T> clazz) {
        OtcMessage<T> message = toMessage(res, arguments, clazz);
        BaseResponse baseResponse = RouteFactory.routeMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    public static <T> OtcMessage<T> toMessage(List<T> res, JSONObject arguments, Class<T> clazz) {
        OtcMessageBatchCloud<T> message = new OtcMessageBatchCloud<>();
        message.setCloudType("public_ali");
        message.setAccountBizId(BaseClient.auths.get().getCmpId());
        message.setDispatchId(arguments.getString("batch"));
        message.setBatchId(arguments.getString("batchId"));
        if (arguments.containsKey("tscTimeout")) {
            message.setTimeout(arguments.getInteger("tscTimeout"));
        }
        message.withClass(clazz);
        message.withBody(res);
        return message;
    }

    public static <T> void sendAtlasMessage(List<T> infos, JSONObject arguments, String subCmpId, Class<T> clazz) {
        //if (ObjectUtil.isNotEmpty(infos)) {
        AtlasMessageBatchCloud message = new AtlasMessageBatchCloud<>();
        message.setCloudType(BaseClient.auths.get().getCloudType());
        message.setAccountBizId(BaseClient.auths.get().getCmpId());
        message.setSubAccountBizId(subCmpId);
        message.setDispatchId(arguments.getString("batch"));
        message.setBatchId(arguments.getString("batchId"));
        String type = arguments.getJSONObject("body").getString("condition") == null ? "" : arguments.getJSONObject("body").getString("condition");
        if (type.equals("model") || type.equals("image") || type.equals("algo") || type.equals("dataset"))
            message.setCondition("property_type='" + type + "'");
        if (arguments.containsKey("tscTimeout")) {
            message.setTimeout(arguments.getInteger("tscTimeout"));
        }
        message.withBody(infos, clazz);
        String jsonString = JSON.toJSONString(message);
        log.info("发送MQ消息到数据底座,{}", jsonString);
        BaseResponse baseResponse = RouteFactory.routeAtlasMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送MQ消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送MQ消息到数据底座失败!");
        }
        // }
    }

    public static <T> void sendAtlasPerfMessage(List<T> infos, JSONObject arguments, String subCmpId, Class<T> clazz) {
        if (ObjectUtil.isNotEmpty(infos)) {
            AtlasMessageBatchCloud message = new AtlasMessageBatchCloud<>();
            message.setCloudType(BaseClient.auths.get().getCloudType());
            message.setAccountBizId(BaseClient.auths.get().getCmpId());
            message.setSubAccountBizId(subCmpId);
            message.setDispatchId(arguments.getString("batch"));
            message.setBatchId(arguments.getString("batchId"));
            String type = arguments.getJSONObject("body").getString("condition") == null ? "" : arguments.getJSONObject("body").getString("condition");
            if (type.equals("model") || type.equals("image") || type.equals("algo") || type.equals("dataset"))
                message.setCondition("property_type='" + type + "'");
            if (arguments.containsKey("tscTimeout")) {
                message.setTimeout(arguments.getInteger("tscTimeout"));
            }
            message.withBody(infos, clazz);
            String jsonString = JSON.toJSONString(message);
            log.info("发送MQ消息到数据底座,{}", jsonString);
            BaseResponse baseResponse = RouteFactory.routeAtlasPerfMessage(message);
            if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
                log.error("发送MQ消息到数据底座失败,{}", baseResponse.getMessage());
                throw new BaseException(baseResponse, "发送MQ消息到数据底座失败!");
            }
        }
    }

    public static <T> void sendAtlasMessageCloud(List<T> infos, BaseCloudRequestBody body, String subCmpId, Class<T> clazz) {
//        if (ObjectUtil.isNotEmpty(infos)) {
        AtlasMessageBatchCloud message = new AtlasMessageBatchCloud<>();
        message.setCloudType(body.getAccess().getCloudType());
        message.setAccountBizId(body.getAccess().getCmpId());
        message.setSubAccountBizId(subCmpId);
        message.setDispatchId(body.getGourd().getDispatcherCode());
        message.setBatchId(body.getGourd().getZeroJobCode());
        message.setTimeout(body.getGourd().getTscTimeout());
        message.withBody(infos, clazz);
        log.info("消息队列message:{}", JSON.toJSONString(message));
        BaseResponse baseResponse = RouteFactory.routeAtlasMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送MQ消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送MQ消息到数据底座失败!");
        }
//        }
    }

    public static void transfer(Object source, Object target, String sourcePath, String targetPath, boolean validNotEmpty, Function<Object, ?> format) {
        if (JSONPath.contains(target, targetPath)) return;
        Object val = JSONPath.eval(source, sourcePath);
        Object setVal = JSONPath.eval(target, targetPath);
        if (ObjUtil.isEmpty(val) && ObjUtil.isEmpty(setVal) && validNotEmpty)
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作请求信息" + sourcePath + "为空!");
        if (ObjUtil.isEmpty(val)) return;
        JSONPath.set(target, targetPath, format == null ? val : format.apply(val));
    }


    public static Object formatStr(Object object) {
        if (object == null) return null;
        if (object instanceof List) {
            return String.join(",", (List) object);
        }
        return object;
    }

    public static Object formatSingle(Object object) {
        if (object == null) return null;
        if (object instanceof List) {
            return ((List) object).get(0);
        }
        return object;
    }

    public static String formatStrArray(Object object) {
        if (object == null) return null;
        if (object instanceof List) {
            return JSON.toJSONString(object);
        } else if (object instanceof String) {
            return JSON.toJSONString(object.toString().split(","));
        }
        return "[\"" + object.toString() + "\"]";
    }

    public static List formatList(Object object) {
        if (object == null) return null;
        if (object instanceof List) {
            return (List) object;
        } else if (object instanceof String) {
            if (JSONUtil.isTypeJSONArray(object.toString())) {
                return JSON.parseArray(object.toString());
            }
            if (JSONUtil.isTypeJSONObject(object.toString())) {
                return Arrays.asList(JSON.parseObject(object.toString()));
            }
            if (object.toString().contains(",")) {
                return Arrays.asList(object.toString().split(","));
            }
            return Arrays.asList(object.toString());
        } else {
            return Arrays.asList(object);
        }
    }

    public static void main(String[] args) {
        List list = formatList("\"aaaa\",\"bbb\",\"ccc\"");
        System.out.println(list);

    }


}
