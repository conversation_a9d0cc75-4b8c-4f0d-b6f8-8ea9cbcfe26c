package com.futong.gemini.plugin.cloud.sdk.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.feign.FTFeignUtils;
import com.futong.common.function.FTConvert;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.plugin.cloud.sdk.common.OperationProxy;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.account.CloudAccessBeanExt;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
public abstract class BaseClient {
    //请求信息
    public static ThreadLocal<JSONObject> arguments = new ThreadLocal<>();
    //认证对象
    public static ThreadLocal<CloudAccessBean> auths = new ThreadLocal<>();
    //认证对象扩展
    public static ThreadLocal<CloudAccessBeanExt> authExts = new ThreadLocal<>();
    //请求体信息
    public static ThreadLocal<JSONObject> bodys = new ThreadLocal<>();
    //地域信息
    public static ThreadLocal<String> regions = new ThreadLocal<>();

    public static ThreadLocal<BaseCloudRequest> requests = new ThreadLocal<>();

    /**
     * 获取Client对象
     *
     * @param body  请求对象
     * @param clazz Class<Client>
     * @param <C>   Client对象类型
     * @return Client对象
     */
    public abstract <C> C client(Class<C> clazz, JSONObject body);

    /**
     * 执行云上操作
     *
     * @param exec   云操作函数
     * @param client 云操作Client对象
     * @param <Q>    云操作函数入参类型
     * @param <R>    云操作函数出参类型
     * @param <C>    云操作函数所属的Client对象类型
     * @return 云操作函数出参对象
     */
    public abstract <Q, R, C> R execute(C client, Q q, FTExecute<C, Q, R> exec);

    //获取Client对象
    public <C> C client(Class<C> clazz) {
        return client(clazz, bodys.get());
    }

    //执行调用函数
    public <Q, R, C> R execute(FTExecute<C, Q, R> exec) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        Q q;
        if (bodys.get() != null) {
            q = bodys.get().toJavaObject(clazz.v2);
        } else {
            try {
                q = clazz.v2.newInstance();
            } catch (Exception e) {
                throw new BaseException(BaseResponse.ERROR_BIZ, "生成云资源操作请求对象失败!");
            }
        }
        C client = client(clazz.v1, bodys.get());
        return execute(client, q, exec);
    }

    //执行调用函数
    public <Q, R, C> R execute(Q q, FTExecute<C, Q, R> exec) {
        return execute(bodys.get(), q, exec);
    }

    //执行调用函数
    public <Q, R, C> R execute(JSONObject body, Q q, FTExecute<C, Q, R> exec) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        C client = client(clazz.v1, body);
        return execute(client, q, exec);
    }

    //执行调用函数
    public <Q, R, C> R execute(FTExecute<C, Q, R> exec, FTConvert<JSONObject, Q> convert) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        C client = client(clazz.v1, bodys.get());
        return execute(client, convert.convert(bodys.get()), exec);
    }

    //执行调用函数
    public <Q, R, C> R execute(JSONObject body, FTExecute<C, Q, R> exec, FTConvert<JSONObject, Q> convert) {
        Entry.E2<Class<C>, Class<Q>> clazz = exec.clazz();
        C client = client(clazz.v1, body);
        return execute(client, convert.convert(body), exec);
    }

    public static void load(JSONObject request) {
        arguments.set(request);
        CloudAccessBean client = loadAuth(request);
        auths.set(client);
        if (StrUtil.isNotEmpty(client.getJsonStr())) {
            CloudAccessBeanExt clientExt = JSON.parseObject(client.getJsonStr(), CloudAccessBeanExt.class);
            authExts.set(clientExt);
        } else {
            authExts.set(new CloudAccessBeanExt());
        }
        JSONObject body = loadBody(request);
        bodys.set(body);
        String regionId = body.getString("regionId");
        regions.set(regionId);
    }

    public static void unload(JSONObject request) {
        arguments.remove();
        auths.remove();
        authExts.remove();
        bodys.remove();
        regions.remove();
    }

    public static CloudAccessBean loadAuth(JSONObject request) {
        if (!request.containsKey("auth")) {
            request.put("auth", request.getJSONObject("body").getJSONObject("auth"));
        }
        CloudAccessBean bean = request.getJSONObject("auth").toJavaObject(CloudAccessBean.class);
        if (StrUtil.isNotEmpty(bean.getCmpId()) && StrUtil.isEmpty(bean.getUsername())) {
            //通过接口获取信息
            try {
                OperationProxy proxy = FTFeignUtils.target(OperationProxy.class,"http://futong-public-operation:50012");
                OperationProxy.AccessListRequest accessListRequest = new OperationProxy.AccessListRequest();
                accessListRequest.setCmpId(bean.getCmpId());
                BaseDataResponse<List<CloudAccessBean>> accessList = proxy.getAllAccessList(accessListRequest);
                if (CollUtil.isEmpty(accessList.getData())) {
                    throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, StrUtil.format("云账号{}无法获取账号信息", bean.getCmpId()));
                }
                return accessList.getData().get(0);
            } catch (Exception e) {
                log.error(StrUtil.format("云账号{}无法获取账号信息", bean.getCmpId()), e);
                throw new BaseException(BaseResponse.FAIL_PARAM, e, StrUtil.format("云账号{}无法获取账号信息", bean.getCmpId()));
            }
        }
        return bean;
    }

    public static JSONObject loadBody(JSONObject request) {
        if (request.containsKey("body")) {
            return request.getJSONObject("body");
        }
        return new JSONObject();
    }
}
