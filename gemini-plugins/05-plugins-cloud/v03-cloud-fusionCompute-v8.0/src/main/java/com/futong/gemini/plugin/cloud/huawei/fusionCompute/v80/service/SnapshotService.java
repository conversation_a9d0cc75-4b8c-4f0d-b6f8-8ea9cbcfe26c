package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SnapshotService {

    /**
     * 创建快照
     * @param arguments
     * @return
     */
    public static BaseResponse createSnapshot(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String vmUrn = arguments.getBody().getCloud().getString("vmUrn");
            String vmUri = UrnUtils.formatUrn(vmUrn);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmSnapshotCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            SnapshotCreateRequest request = arguments.getBody().getCloud().toJavaObject(SnapshotCreateRequest.class);
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建VM失败");
        }
    }

    /**
     * 修改快照
     * @param arguments
     * @return
     */
    public static BaseResponse updateSnapshot(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String snapshotUri = arguments.getBody().getCi().getString("openId");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmSnapshotUpdateUrl(cloudAccessBean.getScvmmRole()), new String[]{snapshotUri});
            SnapshotUpdateRequest request = arguments.getBody().getCloud().toJavaObject(SnapshotUpdateRequest.class);
            String json = HttpClientUtil.put(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建VM失败");
        }
    }

    /**
     * 删除快照
     * @param request
     * @return
     */
    public static BaseResponse deleteSnapshot(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String snapshotUri = request.getBody().getCi().getString("openId");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmSnapshotDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{snapshotUri});
            String json = HttpClientUtil.delete(url, request);
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("删除VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除VM失败");
        }
    }
}
