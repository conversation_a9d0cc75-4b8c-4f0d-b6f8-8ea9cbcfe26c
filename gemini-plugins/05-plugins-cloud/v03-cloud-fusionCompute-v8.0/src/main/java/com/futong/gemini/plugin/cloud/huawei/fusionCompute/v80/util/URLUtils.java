package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util;

import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;

/**
 * url规则
 * basic_uri为 /service
 */
public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取token信息*/
    private String[] authenticateUrl = { "/service/session" };
    public String[] getAuthenticateUrl(String prefix) {
        return getCommonUrl(prefix,authenticateUrl);
    }

    /**获取版本信息*/
    private String[] versionUrl = { "/service/versions" };
    public String[] getVersionUrl(String prefix) {
        return getCommonUrl(prefix,versionUrl);
    }

    /**获取站点信息*/
    private String[] querySitesUrl = { "/service/sites" };
    public String[] getQuerySitesUrl(String prefix) {
        return getCommonUrl(prefix,querySitesUrl);
    }

    /**获取集群信息*/
    private String[] queryClustersUrl = { "/clusters"};
    public String[] getQueryClustersUrl(String prefix) {
        return getCommonUrl(prefix,queryClustersUrl);
    }

    /**创建集群*/
    private String[] clusterCreateUrl = { "/clusters" };
    public String[] getClusterCreateUrl(String prefix) {
        return getCommonUrl(prefix,clusterCreateUrl);
    }

    /**修改集群*/
    private String[] clusterUpdateUrl = { "" };
    public String[] getClusterUpdateUrl(String prefix) {
        return getCommonUrl(prefix,clusterUpdateUrl);
    }

    /**删除集群*/
    private String[] clusterDeleteUrl = { "" };
    public String[] getClusterDeleteUrl(String prefix) {
        return getCommonUrl(prefix,clusterDeleteUrl);
    }

    /**获取host信息*/
    private String[] queryHostsUrl = { "/hosts?", "limit", "offset"};
    public String[] getQueryHostsUrl(String prefix) {
        return getCommonUrl(prefix,queryHostsUrl);
    }

    /**创建host*/
    private String[] hostCreateUrl = {"/hosts"};
    public String[] getHostCreateUrl(String prefix) {
        return getCommonUrl(prefix,hostCreateUrl);
    }

    /**修改host*/
    private String[] hostUpdateUrl = {""};
    public String[] getHostUpdateUrl(String prefix) {
        return getCommonUrl(prefix,hostUpdateUrl);
    }

    /**移除host*/
    private String[] hostRemoveUrl = {"?", "isForce"};
    public String[] getHostRemoveUrl(String prefix) {
        return getCommonUrl(prefix,hostRemoveUrl);
    }

    /**host上电*/
    private String[] hostPowerOnUrl = {"/action/poweron"};
    public String[] getHostPowerOnUrl(String prefix) {
        return getCommonUrl(prefix,hostPowerOnUrl);
    }

    /**host下电*/
    private String[] hostPowerOffUrl = {"/action/poweroff"};
    public String[] getHostPowerOffUrl(String prefix) {
        return getCommonUrl(prefix,hostPowerOffUrl);
    }

    /**Host重启*/
    private String[] hostRebootUrl = {"/action/reboot"};
    public String[] getHostRebootUrl(String prefix) {
        return getCommonUrl(prefix,hostRebootUrl);
    }

    /**获取vm信息*/
    private String[] queryVmsUrl = { "/vms?", "isTemplate","limit", "offset","detail" };
    public String[] getQueryVMsUrl(String prefix) {
        return getCommonUrl(prefix,queryVmsUrl);
    }

    /**获取模版的vm信息*/
    private String[] queryTemplateVmsUrl = { "/vms?", "detail","isTemplate" };
    public String[] getQueryTemplateVmsUrl(String prefix) {
        return getCommonUrl(prefix,queryTemplateVmsUrl);
    }

    /**vm详细信息*/
    private String[] queryVmDetailUrl = { "" };
    public String[] getQueryVmDetailUrl(String prefix) {
        return getCommonUrl(prefix,queryVmDetailUrl);
    }

    /**创建VM*/
    private String[] vmCreateUrl = { "/vms" };
    public String[] getVmCreateUrl(String prefix) {
        return getCommonUrl(prefix,vmCreateUrl);
    }
    /**创建VM*/
    private String[] vmCloneUrl = { "/action/clone" };
    public String[] getVmCloneUrl(String prefix) {
        return getCommonUrl(prefix,vmCloneUrl);
    }

    /**修改VM*/
    private String[] vmUpdateUrl = { "" };
    public String[] getVmUpdateUrl(String prefix) {
        return getCommonUrl(prefix,vmUpdateUrl);
    }

    /**启动VM*/
    private String[] vmStartUrl = { "/action/start" };
    public String[] getVmStartUrl(String prefix) {
        return getCommonUrl(prefix,vmStartUrl);
    }

    /**停止VM*/
    private String[] vmStopUrl = { "/action/stop" };
    public String[] getVmStopUrl(String prefix) {
        return getCommonUrl(prefix,vmStopUrl);
    }

    /**删除VM*/
    private String[] vmDeleteUrl = { "?","isReserveDisks", "isFormat", "holdTime" };
    public String[] getVmDeleteUrl(String prefix) {
        return getCommonUrl(prefix,vmDeleteUrl);
    }

    /**重启VM*/
    private String[] vmRebootUrl = { "/action/reboot" };
    public String[] getVmRebootUrl(String prefix) {
        return getCommonUrl(prefix,vmRebootUrl);
    }

    /**修改vnc密码*/
    private String[] vmResetVncUrl = { "/action/resetvnc" };
    public String[] getVmResetVncUrl(String prefix) {
        return getCommonUrl(prefix,vmResetVncUrl);
    }


    /**获取磁盘*/
    private String[] queryVolumesUrl = { "/volumes?", "limit", "offset"};
    public String[] getQueryVolumesUrl(String prefix) {
        return getCommonUrl(prefix,queryVolumesUrl);
    }
    /**创建磁盘*/
    private String[] volumeCreateUrl = { "/volumes" };
    public String[] getVolumeCreateUrl(String prefix) {
        return getCommonUrl(prefix,volumeCreateUrl);
    }

    /**修改磁盘*/
    private String[] volumeUpdateUrl = { "" };
    public String[] getVolumeUpdateUrl(String prefix) {
        return getCommonUrl(prefix,volumeUpdateUrl);
    }

    /**修改磁盘*/
    private String[] volumeDeleteUrl = { "?","isFormat" };
    public String[] getVolumeDeleteUrl(String prefix) {
        return getCommonUrl(prefix,volumeDeleteUrl);
    }

    /**挂载磁盘*/
    private String[] volumeAttachUrl = { "/action/attachvol" };
    public String[] getVolumeAttachUrl(String prefix) {
        return getCommonUrl(prefix,volumeAttachUrl);
    }

    /**卸载磁盘*/
    private String[] volumeDetachUrl = { "/action/detachvol" };
    public String[] getVolumeDetachUrl(String prefix) {
        return getCommonUrl(prefix,volumeDetachUrl);
    }

    /**扩容磁盘*/
    private String[] volumeResizeUrl = { "/action/expandvol" };
    public String[] getVolumeResizeUrl(String prefix) {
        return getCommonUrl(prefix,volumeResizeUrl);
    }

    /**获取虚拟交行机DVSwitch信息*/
    private String[] queryDVSwitchsUrl = { "/dvswitchs"};
    public String[] getQueryDVSwitchsUrl(String prefix) {
        return getCommonUrl(prefix,queryDVSwitchsUrl);
    }
    /**获取虚拟交行机DVSwitch详细信息*/
    private String[] queryDVSwitchsDetailUrl = { ""};
    public String[] getQueryDVSwitchsDetailUrl(String prefix) {
        return getCommonUrl(prefix,queryDVSwitchsDetailUrl);
    }

    /**创建虚拟交行机DVSwitch*/
    private String[] DVSwitchCreateUrl = { "/dvswitchs"};
    public String[] getDVSwitchCreateUrl(String prefix) {
        return getCommonUrl(prefix,DVSwitchCreateUrl);
    }

    /**修改虚拟交行机DVSwitch*/
    private String[] DVSwitchUpdateUrl = { ""};
    public String[] getDVSwitchUpdateUrl(String prefix) {
        return getCommonUrl(prefix,DVSwitchUpdateUrl);
    }

    /**创建虚拟交行机DVSwitch*/
    private String[] DVSwitchDeleteUrl = { ""};
    public String[] getDVSwitchDeleteUrl(String prefix) {
        return getCommonUrl(prefix,DVSwitchDeleteUrl);
    }

    /**获取端口组信息*/
    private String[] queryPortGroupsUrl = { "/portgroups?", "limit", "offset" };
    public String[] getQueryPortGroupsUrl(String prefix) {
        return getCommonUrl(prefix,queryPortGroupsUrl);
    }

    /**获取安全组信息*/
    private String[] querySecurityGroupsUrl = {"/securitygroups?", "limit", "offset"};
    public String[] getQuerySecurityGroupsUrl(String prefix) {
        return getCommonUrl(prefix,querySecurityGroupsUrl);
    }

    /**获取安全组规则信息*/
    private String[] querySecurityGroupRulesUrl = {"/action/rules?", "limit", "offset"};
    public String[] getQuerySecurityGroupRulesUrl(String prefix) {
        return getCommonUrl(prefix,querySecurityGroupRulesUrl);
    }

    /**获取快照信息*/
    private String[] queryVmSnapshotUrl = { "/snapshots" };
    public String[] getQueryVmSnapshotUrl(String prefix) {
        return getCommonUrl(prefix, queryVmSnapshotUrl);
    }

    /**创建快照*/
    private String[] vmSnapshotCreateUrl = { "/snapshots" };
    public String[] getVmSnapshotCreateUrl(String prefix) {
        return getCommonUrl(prefix, vmSnapshotCreateUrl);
    }

    /**修改快照*/
    private String[] vmSnapshotUpdateUrl = { "" };
    public String[] getVmSnapshotUpdateUrl(String prefix) {
        return getCommonUrl(prefix, vmSnapshotUpdateUrl);
    }

    /**删除快照*/
    private String[] vmSnapshotDeleteUrl = { "" };
    public String[] getVmSnapshotDeleteUrl(String prefix) {
        return getCommonUrl(prefix, vmSnapshotDeleteUrl);
    }

    /**数据存储*/
    private String[] queryDataStoresUrl = { "/datastores?", "limit", "offset", "scope"};
    public String[] getQueryDataStoresUrl(String prefix) {
        return getCommonUrl(prefix,queryDataStoresUrl);
    }

    /**存储设备*/
    private String[] queryStorageUnitsUrl = { "/storageunits/querybypage?", "limit", "offset", "deviceType"};
    public String[] getQueryStorageUnitsUrl(String prefix) {
        return getCommonUrl(prefix,queryStorageUnitsUrl);
    }

    /*** 存储资源*/
    private String[] queryStorageResourceUrl = { "/storageresources/queryallstorageresource?", "limit", "offset" };
    public String[] getQueryStorageResourceUrl(String prefix) {
        return getCommonUrl(prefix,queryStorageResourceUrl);
    }

    /**获取告警*/
    private String[] queryAlarmsUrl = {"/alarms/historyAlarms"};
    public String [] getQueryAlarmsUrl(String prefix){
        return getCommonUrl(prefix,queryAlarmsUrl);
    }

    /**获取事件*/
    private String[] queryEventsUrl = {"/alarms/events"};
    public String [] getQueryEventsUrl(String prefix){
        return getCommonUrl(prefix,queryEventsUrl);
    }

    /**获取历史监控数据*/
    private String[] queryObjectMetricCurveDataUrl = { "/monitors/objectmetric-curvedata" };
    public String[] getQueryObjectMetricCurveDataUrl(String prefix) {
        return getCommonUrl(prefix,queryObjectMetricCurveDataUrl);
    }

    /**获取实时监控数据*/
    private String[] queryObjectMetricRealTimeCurveDataUrl = { "/monitors/objectmetric-realtimedata" };
    public String[] getQueryObjectMetricRealTimeCurveDataUrl(String prefix) {
        return getCommonUrl(prefix,queryObjectMetricRealTimeCurveDataUrl);
    }

    /**获取操作系统*/
    private String[] osUrl = { "/vms/osversions" };
    public String[] getOsUrl(String prefix) {
        return getCommonUrl(prefix,osUrl);
    }

    /**获取操作系统*/
    private String[] osUrlByHost = { "/vms/osversions?","siteID","arch","cpuVendor","osVendor" };
    public String[] getOsUrlByHost(String prefix) {
        return getCommonUrl(prefix,osUrlByHost);
    }

    /**文件夹管理*/
    private String[] folderUrl = { "/folder?", "parentObjUrn", "type" };
    public String[] getFoldersUrl(String prefix) {
        return getCommonUrl(prefix,folderUrl);
    }

    /**同步上行链路组*/
    private String[] uplinkPortsUrl = {"/uplinkports"};
    public String[] getUplinkPortsUrl(String prefix) {
        return getCommonUrl(prefix,uplinkPortsUrl);
    }


    public  String makeUrl(CloudAccessBean accessBean, String[] paths, String[] args) {
//        String url = "https://***********:7443";
        String url = accessBean.getProtocol() +"://" + accessBean.getServerIp() + ":"+ accessBean.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url + args[0] + paths[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url){
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (StrUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")){
                    buf.append(paths[i]).append(args[i]);
                }else{
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            }else if (paths[i].contains("?") || paths[i].contains("/")){
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")){
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

    public String[] getCommonUrl(String prefix,String[] url) {
        if(prefix==null||"/".equals(prefix)) {
            return url.clone();
        }else {
            String s = url[0];
            if (!s.startsWith(prefix)) {
                if (prefix != null && prefix.length() > 0) {
                    s = prefix + s;
                }
                url[0] = s;
            }
        }
        return url.clone();
    }

}
