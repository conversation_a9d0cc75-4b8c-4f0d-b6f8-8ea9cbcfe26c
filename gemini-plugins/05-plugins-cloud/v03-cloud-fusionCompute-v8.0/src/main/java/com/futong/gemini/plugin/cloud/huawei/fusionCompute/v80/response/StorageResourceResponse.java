package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class StorageResourceResponse {

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 分页查询站点下所有存储资源信息列表
     */
    private List<StorageResourceResponseData> storeResInfoList;

    @Data
    public static class StorageResourceResponseData {
        /**
         * 设备类型：如果厂家是华为，取值范围为3900、V3、DSWARE和OTHER；
         * 如果是其他厂家，取值范围为OTHER。
         * 存储类型为advanceSan可以选择V3,代表使用OceanStor V3系列存储，3900代表使用OceanStor V2系列存储

         */
        private String deviceType;

        /**
         * 已关联主机数量
         */
        private Integer hostNum;

        /**
         * 管理端口
         */
        private String managementPort;

        /**
         *  存储资源名称
         */
        private String name;
        /**
         *
         * 存储设备类型，存储设备类型： LOCAL（本地硬盘） IPSAN FCSAN HCPSAN ADVANCESAN（XVE的存储池） NAS
         * DSWARE（DSWare的存储池） iotailor
         */
        private String storageType;


        /**
         * 唯一标识存储资源的urn。
         */
        private String urn;

        /**
         * 唯一标识存储资源的uri。
         */
        private String uri;

        /**
         * 厂家，有两种：HW(华为)和OTHER（其他厂家)
         */
        private String vender;

        /**
         * 关联的存储链路
         */
//        private List<StorageDataChannel> dataChannel;

        /**
         * 关联主机信息
         */
//        private List<StorageHostInfo> hostList;

        /**
         * 管理Ip
         */
        private List<String> managementIpList;
    }
}
