package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class SitesResponse {

    private List<SitesResponseData> sites;

    @Data
    public static class SitesResponseData {
        /**
         * 站点标识。
         */
        private String urn;

        /**
         * 站点URI。
         */
        private String uri;

        /**
         * 名称。
         */
        private String name;

        /**
         * 站点IP。
         */
        private String ip;

        /**
         * 是否是域控制器。
         */
        private Boolean isDC;

        /**
         * 是否是当前站点。
         */
        private Boolean isSelf;

        /**
         * VRM状态：<br>
         * joining，加入域中<br>
         * exiting， 退出域中<br>
         * normal，正常<br>
         * fault， 故障。
         */
        private String status;
    }
}
