package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host.HostCreateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host.HostPowerOffRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host.HostUpdateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HostService {

    /**
     * 创建Host
     * @param request
     * @return
     */
    public static BaseResponse createHost(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{"siteUri"});
            HostCreateRequest createRequest = request.getBody().getCloud().toJavaObject(HostCreateRequest.class);
            String json = HttpClientUtil.post(url, request, JSON.toJSONString(createRequest));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建Host失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建Host失败");
        }
    }

    /**
     * 修改Host
     * @param arguments
     * @return
     */
    public static BaseResponse updateHost(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostUpdateUrl(cloudAccessBean.getScvmmRole()), new String[]{"hostUri"});
            HostUpdateRequest request = arguments.getBody().getCloud().toJavaObject(HostUpdateRequest.class);
            String json = HttpClientUtil.put(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("修改Host失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改Host失败");
        }
    }

    /**
     * 移除Host
     * @param request
     * @return
     */
    public static BaseResponse removeHost(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String hostUri = request.getBody().getCi().getString("openId");
            String isForce = String.valueOf(request.getBody().getModel().getInteger("isForce"));
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostRemoveUrl(cloudAccessBean.getScvmmRole()), new String[]{hostUri,isForce});
            String json = HttpClientUtil.delete(url, request);
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("移除Host失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移除Host失败");
        }
    }

    /**
     * Host上电
     * @param arguments
     * @return
     */
    public static BaseResponse powerOnHost(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String hostUri = arguments.getBody().getCi().getString("openId");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostPowerOnUrl(cloudAccessBean.getScvmmRole()), new String[]{hostUri});
            String json = HttpClientUtil.post(url, arguments, "");
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("Host上电失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "Host上电失败");
        }
    }

    /**
     * Host下电
     * @param arguments
     * @return
     */
    public static BaseResponse powerOffHost(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String hostUri = arguments.getBody().getCi().getString("openId");
            String mode = arguments.getBody().getModel().getString("mode");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostPowerOffUrl(cloudAccessBean.getScvmmRole()), new String[]{hostUri});
            HostPowerOffRequest request = new HostPowerOffRequest();
            request.setMode(mode);
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("Host下电失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "Host下电失败");
        }
    }

    /**
     * Host重启
     * @param arguments
     * @return
     */
    public static BaseResponse rebootHost(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String hostUri = arguments.getBody().getCi().getString("openId");
            String mode = arguments.getBody().getModel().getString("mode");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostRebootUrl(cloudAccessBean.getScvmmRole()), new String[]{hostUri});
            HostPowerOffRequest request = new HostPowerOffRequest();
            request.setMode(mode);
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("Host重启失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "Host重启失败");
        }
    }
}
