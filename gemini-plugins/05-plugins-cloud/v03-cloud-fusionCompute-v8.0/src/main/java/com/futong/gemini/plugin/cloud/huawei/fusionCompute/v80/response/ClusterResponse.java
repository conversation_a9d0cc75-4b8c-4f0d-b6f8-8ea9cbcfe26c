/**
 * Copyright 2015 Huawei Technologies Co., Ltd. All rights reserved.
 * eSDK is licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *   
 * http://www.apache.org/licenses/LICENSE-2.0 
 *   
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 查询集群列表响应消息。
 * 
 */
@Data
public class ClusterResponse {

	private String siteUrn;
	/**
	 * 【可选】集群列表。
	 */
	private List<ClusterResponseData> clusters;

	@Data
	public static class ClusterResponseData{
		/**
		 * 集群标识。
		 */
		private String urn;

		/**
		 * 访问该集群的uri地址。
		 */
		private String uri;

		/**
		 * 集群名称。
		 */
		private String name;

		/**
		 * 父目录标识，若直接位于站点下则为空。
		 */
		private String parentObjUrn;

		/**
		 * 集群所在文件夹名称。
		 */
		private String parentObjName;

		/**
		 * 集群描述。
		 */
		private String description;

		/**
		 * 计算机CPU信息。
		 */
		private CpuResource cpuResource;

		/**
		 * 计算机内存信息。
		 */
		private MemoryResource memResource;

		/**
		 * 集群标签。
		 */
		private String tag;

		/**
		 * 预留，暂未使用。R5C10版本新增<br>
		 * 注： 该字段仅查询时返回，不可创建或修改。
		 */
		private Map<String,String> params;
	}
}
