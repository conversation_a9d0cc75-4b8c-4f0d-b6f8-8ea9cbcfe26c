package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.VRMTask;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.stream.Collectors;

@Slf4j
public class CloudService {

    public static boolean defaultSiteUri(BaseCloudRequest request) {
        String uri = UrnUtils.formatUrn(request.getBody().getCloud().getString("siteUrn"));
        request.getBody().getCloud().put("siteUri", uri);
        return true;
    }

    public static boolean toCiOpenIdUri(BaseCloudRequest request) {
        toCIStrOpenIdUri(request, "uri");
        return true;
    }

    public static boolean toCIStrOpenIdUri(BaseCloudRequest request, String key) {
        if (!request.getBody().getCi().isEmpty()) {
            String uri = UrnUtils.formatUrn(request.getBody().getCi().getString("openId"));
            request.getBody().getCloud().put(key, uri);
            return true;
        }
        if (CollUtil.isEmpty(request.getBody().getCis())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的CI信息为空!");
        }
        String uri = UrnUtils.formatUrn(request.getBody().getCis().get(0).getString("openId"));
        request.getBody().getCloud().put(key, uri);
        return true;
    }

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", "0");
        }
        if (!request.getBody().getCloud().containsKey("limit")) {
            request.getBody().getCloud().put("limit", "100");
        }
        return true;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("occurStartTime")) {
            request.getBody().getCloud().put("occurStartTime", Long.valueOf(DateUtil.offsetDay(new Date(), -1).getTime()));
        }
        if (!request.getBody().getCloud().containsKey("occurStopTime")) {
            request.getBody().getCloud().put("occurStopTime", Long.valueOf(System.currentTimeMillis()));
        }
        return true;
    }

    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) {
            return;
        }
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            String data = dataResponse.getData().toString();
            VRMTask vrmTask = JSON.parseObject(data, VRMTask.class);
            if (vrmTask == null || StrUtil.isEmpty(vrmTask.getUrn())) {
                return;
            }
            JSONObject biz = request.getBody().getBiz();
            biz.put("resId",IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vrmTask.getUrn()));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
        }
    }

    public static boolean defaultVmDelete(BaseCloudRequest request) {
        request.getBody().getCloud().put("isFormat","0");
        request.getBody().getCloud().put("holdTime","0");
        return true;
    }

    public static boolean toCloudForce(BaseCloudRequest request) {
        Integer force = request.getBody().getCloud().getInteger("force")==null?0:request.getBody().getCloud().getInteger("force");
        String mode = "safe";
        if(force == 1){
            mode = "force";
        }
    	request.getBody().getCloud().put("mode", mode);
    	return true;
    }
}
