package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.alarm;

import lombok.Data;

import java.util.List;

@Data
public class EventRequest  {

    /**
     * 【可选】事件对象类型，hosts：主机，vms：虚拟机，vrms：VRM节点。
     */
    private String MOC;

    /**
     * 【可选】事件ID。
     */
    private String eventId;

    /**
     * 【可选】事件名称。
     */
    private String eventName;

    /**
     * 【可选】是否集成部件(支持单选)，0：否，1：是。
     */
    private Integer objectType;

    /**
     * 【可选】告警产生起始时间，格式如下：1415169155039（即距离1970 年 1 月 1 日（00:00:00 GMT）以来的毫秒数）。
     */
    private String occurStartTime;

    /**
     * 【可选】告警产生结束时间，格式如下：1415169155039（即距离1970 年 1 月 1 日（00:00:00 GMT）以来的毫秒数）。
     */
    private String occurStopTime;

    /**
     * 【可选】偏移量，必须是数字，默认0。
     */
    private Integer offset;

    /**
     * 【可选】UI一页显示的记录数，默认100，最大值为500。
     */
    private Integer limit;

    /**
     * 【可选】排序条件<br>
     * ASC：升序；DESC降序<br>
     * 支持的排序条件：<br>
     * occurtime：事件产生时间<br>
     * eventId：事件ID<br>
     * 例如："order": ["occurtime DESC" ]表示按照产生时间为降序进行排序。
     */
    private List<String> order;
}
