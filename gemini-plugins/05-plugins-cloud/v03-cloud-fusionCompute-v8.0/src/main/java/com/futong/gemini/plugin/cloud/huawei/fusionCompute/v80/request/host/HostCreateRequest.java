package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host;

import lombok.Data;

@Data
public class HostCreateRequest {

    /**
     * 【可选】主机名称，长度[0, 256]。
     */
    private String name;

    /**
     * 【可选】描述 ， 长度[0,1024]。
     */
    private String description;

    /**
     * 【必选】主机IP。
     */
    private String ip;

    /**
     * 【可选】集群标识，默认添加至站点。
     */
    private String clusterUrn;

    /**
     * 【可选】BMC的IP地址，用于节点上下电操作。
     */
    private String bmcIp;

    /**
     * 【可选】BMC帐号，长度[0, 64]，不支持以下字符：&|;<>-/$。
     */
    private String bmcUserName;

    /**
     * 【可选】BMC密码，长度[0, 64]，不支持以下字符：&|;<>-/$。
     */
    private String bmcPassword;

    /**
     * 【可选】是否应用站点时钟源和时间配置信息，可选，即添加主机时，是否将站点的钟源和时间配置信息配置到该主机上<br>
     * 0-不应用（默认），1-应用<br>
     * 说明，当值为1（应用）时，添加主机之后，给该主机配置时钟源和时间，该主机管理业务进程会重启，管理业务会中断3~5分钟<br>
     */
    private Integer isConfDefNtpTimeZone;
}
