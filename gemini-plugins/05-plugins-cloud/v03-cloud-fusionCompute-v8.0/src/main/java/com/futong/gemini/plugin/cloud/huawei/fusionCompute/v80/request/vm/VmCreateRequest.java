package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm;

import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.OsOption;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.VmConfig;
import lombok.Data;

@Data
public class VmCreateRequest {

    /**
     * 【可选】虚拟机名称，长度为[0，256]。
     */
    private String name;

    /**
     * 【可选】虚拟机描述信息，长度[0，1024]。
     */
    private String description;

    /**
     * 【可选】虚拟机组名称，长度为[0，1024]<br>
     * 当客户端为FM或者VDI时，按如下规则使用：<br>
     * "FC_" – FC，
     * "FM_" – FM，
     * "VDI" – VDI<br>
     * 前三个字符保留以后扩展，其余字段供各部件自行定义使用规则<br>
     * 此字段对旧版本兼容说明：<br>
     * "VRMGroup"--R3C0 FM/VRM用来表示管理集群<br>
     * FM页面创建虚拟机模板时，使用group字段保存虚拟机模板的类型：<br>
     * 应用虚拟机模板("vapp_template")；<br>
     * 桌面模板("desktop_template")<br>
     * VDI支持将桌面模板("desktop_template")类型模板修改为如下四种类型：<br>
     * 桌面完整复制模板("fullcopy_template")；<br>
     * 桌面链接克隆模板("linkclone_template")；<br>
     * 桌面快速封装模板("quickprep_template")；<br>
     * 桌面PVD链接克隆模板("pvd_template")<br>
     * 后续扩展一律参照以上执行，不允许再进行此类扩展。
     */
    private String group;

    /**
     * 【必选】虚拟机所属，可以是集群或主机，clusterUrn或hostUrn；若为hostUrn则本次创建若需要启动的话将在该主机上启动。
     */
    private String location;

    /**
     * 【可选】是否与主机绑定；<br>
     * true：与主机绑定，<br>
     * false：不绑定主机；<br>
     * 注：当location为hostUrn时有效；<br>
     * 若指定主机不位于集群下时系统自动将此属性处理为true，若主机位于集群下时默认为false。
     */
    private Boolean isBindingHost;

    /**
     * 【必选】虚拟机配置。
     */
    private VmConfig vmConfig;

    /**
     * 【可选】是否自动启动，默认启动：true。
     */
    private Boolean autoBoot;

    /**
     * 【必选】虚拟机操作系统信息。
     */
    private OsOption osOptions;

    /**
     * 【可选】VNC设置，目前仅支持设置vncPassword。
     */
    private VncAccessInfo vncAccessInfo;

    /**
     * 【可选】是否为占位虚拟机，默认false。
     */
    private Boolean occupiedVm;

    /**
     * UUID，创建占位虚拟机时必选，其他情况下可选。
     */
    private String uuid;

    /**
     * 安全组信息（预留，不建议填写）。
     */
    private SecurityGroup securityGroupSet;

    /**
     * 【可选】是否开启磁盘加速，默认false。
     */
    private Boolean isMultiDiskSpeedup;

    /**
     * 【可选】外部虚拟机UUID标识
     */
    private String externalUuid;

    /**
     * 【可选】标父对象URN，站点或文件夹
     */
    private String parentObjUrn;
}
