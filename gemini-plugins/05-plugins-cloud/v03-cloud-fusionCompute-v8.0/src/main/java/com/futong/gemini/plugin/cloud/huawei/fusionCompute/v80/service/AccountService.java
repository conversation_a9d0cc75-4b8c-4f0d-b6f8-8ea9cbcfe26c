package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response.SitesResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.vo.LoginResponse;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AccountService {

    public static final AccountService bean = new AccountService();
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();
    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            log.info("---------华为账号认证开始------------");
            CloudAccessBean bean = request.getBody().getAuth().toJavaObject(CloudAccessBean.class);
            LoginResponse loginResponse = HttpClientUtil.getToken(bean);
            if(ObjectUtil.isNull(loginResponse)){
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, "云账号信息认证失败");
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("华为账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
    }

    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        //替换参数云账号ID
        String text = StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId());
        JSONObject result = JSON.parseObject(text);
        CloudAccessBean cloudAccessBean = request.getBody().getAccess();
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySitesUrl(cloudAccessBean.getScvmmRole()), null);
        String json = HttpClientUtil.get(url, request);
        SitesResponse response = JSONObject.parseObject(json,SitesResponse.class);
        if (request.getBody().containsKey("all")) {
            //根据地域生成全量调度任务
            return new BaseDataResponse<>(listAllDispatcher(result, response.getSites()));
        } else {
            //获取region信息
            List<HashMap<String, String>> formRegionItems = response.getSites().stream().map(t -> {
                HashMap<String, String> region = new HashMap<>();
                region.put("label", t.getName());
                region.put("value", t.getUrn());
                return region;
            }).collect(Collectors.toList());
            result.getJSONObject("form").getJSONObject("region").put("items", formRegionItems);
            return new BaseDataResponse<>(result);
        }
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, List<SitesResponse.SitesResponseData> sites) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                for (SitesResponse.SitesResponseData site : sites) {
                    String itemStrRegion = StrUtil.replace(itemStr, "${region.label}", site.getName());
                    itemStrRegion = StrUtil.replace(itemStrRegion, "${region.value}", site.getUrn());
                    dispatchers.add(JSON.parseObject(itemStrRegion));
                }
            }
        });
        return dispatchers;
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        CloudAccessBean cloudAccessBean = request.getBody().getAccess();
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySitesUrl(cloudAccessBean.getScvmmRole()), null);
        String json = HttpClientUtil.get(url, request);
        SitesResponse response = JSONObject.parseObject(json,SitesResponse.class);
        List<JSONObject> dispatchers = listAllDispatcher(result, response.getSites());
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }
}
