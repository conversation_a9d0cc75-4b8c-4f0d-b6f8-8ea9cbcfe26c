package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm;

import lombok.Data;

import java.util.List;

@Data
public class NicSpecification {
    /**
     * 【必选】自定义规范的网卡对应编号，1-12<br>
     * 不可重复，多个网卡时与虚拟机网卡的sequenceNum从小到大依次对应<br>
     * 例如：<br>
     * 网卡的sequenceNum为2 4 5<br>
     * 虚拟机自定义配置sequenceNum为1 2 3。
     */
    private Integer sequenceNum;

    /**
     * 【可选】虚拟机网卡IP地址，规则参考IP规范。
     */
    private String ip;

    /**
     * 【可选】虚拟机网卡子网掩码，规则参考子网掩码规范。
     */
    private String netmask;

    /**
     * 【可选】虚拟机网卡默认网关，规则参考网关规范。
     */
    private String gateway;

    /**
     * 【可选】虚拟机网卡首选DNS服务器，规则参考DNS规范。
     */
    private String setdns;

    /**
     * 【可选】虚拟机网卡备用DNS服务器，规则参考DNS规范。
     */
    private String adddns;

    /**
     * 【可选】
     * IP地址类型，值为：<br>
     * 4：表示网卡为IPv4地址；<br>
     * 6：表示网卡为IPv6地址<br>
     * 8：表示网卡为IPv4和IPv6地址<br>
     * 值为6、8，IPv6相关配置才生效<br>
     * 注：其它值为非法。
     */
    private Integer ipVersion;

    /**
     * 【可选】根据路由器通告自动分配IPv6地址开关 <br>
     * true： 开启根据路由器通告自动分配IPv6地址<br>
     * false：关闭开关（默认）。
     */
    private Boolean autoConfEnabled6;

    /**
     * 【可选】DHCP分配IPv6地址开关 <br>
     * true：开启DHCP分配IPv6地址<br>
     * false：关闭开关（默认）。
     */
    private Boolean dhcpEnabled6;

    /**
     * 【可选】虚拟机网卡信息。
     */
    private List<IpAddress6> ipAddress6;

    /**
     * 【可选】虚拟机网卡默认网关，规则参考网关规范。
     */
    private String gatewayIpAddr6;

    /**
     * 【可选】虚拟机网卡首选DNS服务器，规则参考DNS规范。
     */
    private String setdns6;

    /**
     * 【可选】虚拟机网卡备用DNS服务器，规则参考DNS规范 。
     */
    private String adddns6;

    /**
     * name，预留字段，暂不支持。
     */
    private String name;
}
