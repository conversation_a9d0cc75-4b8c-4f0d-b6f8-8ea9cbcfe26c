package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm;

import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.CPU;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.Memory;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.OsOption;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.Property;
import lombok.Data;

@Data
public class VmUpdateRequest {
    /**
     * 【可选】虚拟机名，长度[0,256]。
     */
    private String name;

    /**
     * 【可选】虚拟机描述信息，长度[0,1024]。
     */
    private String description;

    /**
     * 【可选】虚拟机组名称，长度为[0,1024]。
     */
    private String group;

    /**
     * 【可选】虚拟机归属。
     */
    private String location;

    /**
     * 【可选】是否为模板。
     */
    private Boolean isTemplate;

    /**
     * 【可选】CPU规格信息。
     */
    private CPU cpu;

    /**
     * 【可选】内存规格信息。
     */
    private Memory memory;

    /**
     * 【可选】虚拟机属性。
     */
    private Property properties;

    /**
     * 【可选】操作系统信息。
     */
    private OsOption osOptions;

    /**
     * 【可选】VNC信息设置，目前仅支持设置vncpassword。
     */
    private VncAccessInfo vncAccessInfo;

    /**
     * 【可选】是否开启磁盘加速，默认false。
     */
    private Boolean isMultiDiskSpeedup;

    /**
     * 【可选】虚拟机外部UUID标识
     */
    private String externalUuid;
}
