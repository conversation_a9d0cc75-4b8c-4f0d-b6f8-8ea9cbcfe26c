package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.storage;

import lombok.Data;

import java.util.Map;

@Data
public class VolumeCreateRequest {

    /**
     * 【可选】卷名称，长度[0,256]，可以重复<br>
     * 如果请求中无name，或者name为””，则会自动生成name。
     */
    private String name;

    /**
     * 【必选】卷大小， 范围分3种规格： <br>
     * advanceSan/local的数据存储，支持1G~2048G <br>
     * advanceSan 设备版本为v3时，支持1G~255T <br>
     * FusionStorage类型的数据存储，支持1G~16383G  <br>
     * LOCALPOME的数据存储，支持1G~2043G <br>
     * san类型的数据存储，支持1G~30T <br>
     * NAS，LUNPOME的数据存储，支持1G~64T <br>
     * LUN类型的数据存储，支持1G~30T <br>
     * iotailor 的数据存储，支持1G~511G。 <br>
     */
    private Integer quantityGB;

    /**
     * 【必选】数据存储的URN（主机容灾时此选项由映射关系指定）。
     */
    private String datastoreUrn;

    /**
     * 【可选】容灾场景创卷时必选。
     */
    private String uuid;

    /**
     * 【可选】是否瘦分配， 默认false。
     */
    private Boolean isThin;

    /**
     * 【必选】卷类型：普通normal，共享卷share。
     */
    private String type;

    /**
     * 【可选】是否为独立磁盘，默认为否(false)，表示卷受快照影响<br>
     * 块存储只支持独立磁盘(块存储会把此字段修改成默认值：true)
     */
    private Boolean indepDisk;

    /**
     * 【可选】是否持久化磁盘， 默认为是(true)，表示卷为持久化磁盘。
     */
    private Boolean persistentDisk;

    /**
     * 【可选】磁盘类型参数，默认为0，取值为：<br>
     *  0：普通卷 <br>
     *  1：延迟置零卷 <br>
     *  2：稀疏卷【预留，不可使用】<br>
     * 该字段在isThin参数为false时生效，在isthin参数为true时失效。
     */
    private Integer volType;

    /**
     * 【可选】当存储类型为advanceSan ，设备版本为V3系列时设置有效。取值范围[0,1]，默认为0；<br>
     *  0代表不支持裸设备映射，1代表支持裸设备映射
     */
    private Integer pvscsiSupport;

    /**
     * 每个卷的自定义属性数量不得超过10个，属性名称最多128个字符，属性值最多256个字符。可通过将指定自定义属性的值设置为空以删除相应自定义属性。
     */
    private Map<String, String> customProperties;

}
