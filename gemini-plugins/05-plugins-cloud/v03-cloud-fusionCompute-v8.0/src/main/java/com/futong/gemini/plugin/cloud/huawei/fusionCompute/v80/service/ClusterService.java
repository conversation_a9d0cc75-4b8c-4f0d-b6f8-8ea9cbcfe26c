package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.cluster.ClusterCreateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClusterService {

    /**
     * 创建集群
     * @param arguments
     * @return
     */
    public static BaseResponse createCluster(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getClusterCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{"siteUri"});
            ClusterCreateRequest request = arguments.getBody().getCloud().toJavaObject(ClusterCreateRequest.class);
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建集群失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建集群失败");
        }
    }

    /**
     * 修改集群
     * @param arguments
     * @return
     */
    public static BaseResponse updateCluster(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getClusterCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{"clusterUri"});
            ClusterCreateRequest request = arguments.getBody().getCloud().toJavaObject(ClusterCreateRequest.class);
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("修改集群失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改集群失败");
        }
    }

    /**
     * 删除集群
     * @param request
     * @return
     */
    public static BaseResponse deleteCluster(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String clusterUri = request.getBody().getCloud().getString("clusterUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getClusterDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{clusterUri});
            String json = HttpClientUtil.delete(url, request);
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("删除集群失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除集群失败");
        }
    }

}
