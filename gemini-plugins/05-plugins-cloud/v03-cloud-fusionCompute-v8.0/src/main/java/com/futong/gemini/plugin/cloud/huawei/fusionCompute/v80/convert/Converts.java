package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.convert;

import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

@Slf4j
public class Converts {

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> instanceMetrics = new HashMap<>();
    private static Entry.E2<String, String>[] metricType = new Entry.E2[]{
            new Entry.E2("Average", "average"),
            new Entry.E2("Maximum", "max"),
            new Entry.E2("Minimum", "min"),
            new Entry.E2("Value", "value"),
            new Entry.E2("Sum", "sum"),
    };

    static {
        instanceMetrics.put("cpu_usage", new Entry.E3("cpuUsage", "average", "%"));
        instanceMetrics.put("mem_usage", new Entry.E3("memUsage", "average", "%"));
        instanceMetrics.put("disk_usage", new Entry.E3("diskUsage", "average", "%"));
        instanceMetrics.put("disk_io_out", new Entry.E3("diskRead", "average", "kb/s"));
        instanceMetrics.put("disk_io_in", new Entry.E3("diskWrite", "average", "kb/s"));
        instanceMetrics.put("nic_byte_in", new Entry.E3("netIn", "average", "kb/s"));
        instanceMetrics.put("nic_byte_out", new Entry.E3("netOut", "average", "kb/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> instancePerfMapping = new HashMap<>();
    static {
        instancePerfMapping.put("cpu_usage", PerfInfoBean::setCpuUsage);
        instancePerfMapping.put("mem_usage", PerfInfoBean::setMemUsage);
        instancePerfMapping.put("disk_usage", PerfInfoBean::setDiskUsage);
        instancePerfMapping.put("disk_io_out", PerfInfoBean::setDiskRead);
        instancePerfMapping.put("disk_io_in", PerfInfoBean::setDiskWrite);
        instancePerfMapping.put("nic_byte_in", PerfInfoBean::setNetIn);
        instancePerfMapping.put("nic_byte_out", PerfInfoBean::setNetOut);
    }

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> hostMetrics = new HashMap<>();
    static {
        hostMetrics.put("cpu_usage", new Entry.E3("cpuUsage", "average", "%"));
        hostMetrics.put("mem_usage", new Entry.E3("memUsage", "average", "%"));
        hostMetrics.put("logic_disk_usage", new Entry.E3("diskUsage", "average", "%"));
        hostMetrics.put("disk_io_out", new Entry.E3("diskRead", "average", "kb/s"));
        hostMetrics.put("disk_io_in", new Entry.E3("diskWrite", "average", "kb/s"));
        hostMetrics.put("nic_byte_in", new Entry.E3("netIn", "average", "kb/s"));
        hostMetrics.put("nic_byte_out", new Entry.E3("netOut", "average", "kb/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> hostPerfMapping = new HashMap<>();
    static {
        hostPerfMapping.put("cpu_usage", PerfInfoBean::setCpuUsage);
        hostPerfMapping.put("mem_usage", PerfInfoBean::setMemUsage);
        hostPerfMapping.put("logic_disk_usage", PerfInfoBean::setDiskUsage);
        hostPerfMapping.put("disk_io_out", PerfInfoBean::setDiskRead);
        hostPerfMapping.put("disk_io_in", PerfInfoBean::setDiskWrite);
        hostPerfMapping.put("nic_byte_in", PerfInfoBean::setNetIn);
        hostPerfMapping.put("nic_byte_out", PerfInfoBean::setNetOut);
    }
}