package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class OsInfoResponse {

    private List<OsInfoResponseData> linux;

    private List<OsInfoResponseData> windows;

    private List<OsInfoResponseData> other;

    @Data
    public static class OsInfoResponseData {
        private int id;
        private String versionDes;
        private String osType;
        private int osBit;
        private int cpuQuantityLimit;
        private int cpuSocketLimit;
        private int memQuantityLimit;
        private boolean supportCpuHotPlug;
        private boolean supportMemHotPlug;
        private boolean supportHibernateAndWakeup;
        private boolean supportUEFIFirmware;
        private boolean supportDiskHotPlug;
        private boolean supportDiskHotUnPlug;
        private boolean supportNicHotPlug;
        private boolean supportNicHotUnplug;
        private boolean supportMigrate;
        private boolean supportHostAndStorageMigrate;
        private boolean supportFreeClock;
        private boolean supportMemorySnapshot;
        private boolean supportStorageSnapshot;
        private boolean supportVSS;
        private boolean supportUsbPassThrough;
        private boolean supportSriov;
        private boolean supportDiskExpand;
        private boolean supportDiskShrink;
        private boolean supportUpgrade;
        private boolean supportSSD;
        private boolean gpuPassThroughSupported;
        private boolean gpuVirtualizationSupported;
        private boolean supportIB;
        private boolean supportGuestNuma;
        private boolean supportVirtioBlk;
        private boolean supportVirtioScsi;
        private String floppyFile;
        private int defaultMemoryQuantity;
        private int defaultCPUQuantity;
        private int defaultSysVolQuantity;
        private boolean eos;
    }

}
