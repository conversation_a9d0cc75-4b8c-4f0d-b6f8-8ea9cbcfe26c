package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class SnapshotResponse {

    /**
     * 当前虚拟机快照信息。
     */
    private CurrentSnapshot currentSnapshot;

    /**
     * 虚拟机快照全集<br>
     * 注：可能会存在多个根节点，最少0个，最多32个快照。
     */
    private List<VmSnapshotInfo> rootSnapshots;

    @Data
    public static class CurrentSnapshot {
        /**
         * 快照标识。
         */
        private String urn;

        /**
         * 访问快照对应的uri。
         */
        private String uri;

        /**
         * 快照别名[1,256]。
         */
        private String name;
    }

    @Data
    public static class VmSnapshotInfo{
        /**
         * 快照标识。
         */
        private String urn;

        /**
         * 访问快照对应的uri。
         */
        private String uri;

        /**
         * 快照别名。
         */
        private String name;

        /**
         * 快照描述信息。
         */
        private String description;

        /**
         * 开始创建时间，UTC时间，格式如：“2012-08-27 20:29:19”。
         */
        private String createTime;

        /**
         * 快照类型: 普通快照normal（默认），备份点快照backup。
         */
        private String type;

        /**
         * 虚拟机快照状态：<br>
         * creating：创建中<br>
         * resuming：恢复虚拟机中<br>
         * ready：创建成功，可用于恢复虚拟机<br>
         * deleting：删除中。
         */
        private String status;

        /**
         * VmSnapshot列表。
         */
        private List<VmSnapshotInfo> childSnapshots;
    }
}
