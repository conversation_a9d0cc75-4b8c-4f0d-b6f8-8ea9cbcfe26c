package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class VlanPoolResponse {

    /**
     * Dvswitch 标识。
     */
    private String urn;

    /**
     * 访问Dvswitch 的uri地址。
     */
    private String uri;

    /**
     * Dvswitch名称。
     */
    private String name;

    /**
     * 描述。
     */
    private String description;

    /**
     * 使用的交换类型：<br>
     * 0: vSwitch 普通模式<br>
     * 1: eSwitch-VMDQ，直通模式<br>
     * 2: SR-IOV 直通模式。
     */
    private Integer type;

    /**
     * vlanPool设置。
     */
    private List<VlanPoolResponseData> vlanPoolSet;

    /**
     * 已用端口数。
     */
    private Integer usedVspNum;

    /**
     * 总端口数。
     */
    private Integer totalVspNum;

    /**
     * IGMP Snooping开关<br>
     * true：开启IGMP Snooping开关<br>
     * false：关闭IGMP Snooping开关<br>
     * null：不涉及
     */
    private Boolean isIgmpSnooping;


    @Data
    public static class VlanPoolResponseData {
        /**
         * 【必选】开始vlan号 ，范围：1 ~ 4094。
         */
        private Integer startVlanId;

        /**
         * 【必选】结束vlan号 ，范围：1 ~ 4094。
         */
        private Integer endVlanId;
    }
}
