package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class StorageUnitResponse {

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 分页查询站点/主机/集群下所有存储设备信息列表
     */
    private List<StorageUnitResponseData> storageUnits;

    @Data
    public static class StorageUnitResponseData {
        /**
         * 唯一标识存储设备的urn。
         */
        private String urn;

        /**
         * 唯一标识存储设备的uri。
         */
        private String uri;

        /**
         * 关联的存储资源URN。
         */
        private String sdUrn;

        /**
         * 存储资源的ID
         */
        private String sdId;

        /**
         * 关联的存储资源名称。
         */
        private String sdName;

        /**
         * 存储设备名称。
         */
        private String name;
        /**
         *
         * 存储设备类型，存储设备类型： LOCAL（本地硬盘） IPSAN FCSAN HCPSAN ADVANCESAN（XVE的存储池） NAS
         * DSWARE（DSWare的存储池） iotailor
         */
        private String type;

        private Integer capacityGB;
        /**
         * 存储设备状态:NORMAL, ABNORMAL。
         */
        private String status;

        /**
         * 上次扫描时间，UTC时间，格式如：“2012-08-27 20:29:19”。
         */
        private String refreshTime;
        /**
         * 是否支持 精简。
         */
        private Boolean isThin;

        /**
         * SAN设备的LUN对应的wwn号。
         */
        private String wwn;

        /**
         * LUN所属的HOST的ID
         */
        private String suHostID;
        /**
         * IPSAN阵列的SN
         */
        private String sanSN;

        /**
         * 存储设备对应的LUN在物理存储资源上的ID
         */
        private String suLunID;

        /**
         * 关联主机总数
         */
        private Integer hostNum;
        /**
         * 关联主机的hostId列表
         */
//        private List<StorageUnitHost> hostList;
    }
}
