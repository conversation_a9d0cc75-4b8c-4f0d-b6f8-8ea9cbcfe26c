package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net.DVSwitchCreateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net.DVSwitchUpdateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DVSwitchService {

    /**
     * 创建虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse createDVSwitch(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUrn = request.getBody().getCloud().getString("siteUrn");
            String siteUri = UrnUtils.formatUrn(siteUrn);
            DVSwitchCreateRequest createRequest = request.getBody().getCloud().toJavaObject(DVSwitchCreateRequest.class);
            createRequest.setIgmpSnooping(false);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDVSwitchCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.post(url, request, JSON.toJSONString(createRequest));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("删除交行机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除交换机失败");
        }
    }


    /**
     * 编辑虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse updateDVSwitch(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String uri = request.getBody().getCloud().getString("uri");
            DVSwitchUpdateRequest updateRequest = request.getBody().getCloud().toJavaObject(DVSwitchUpdateRequest.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDVSwitchUpdateUrl(cloudAccessBean.getScvmmRole()), new String[]{uri});
            String json = HttpClientUtil.put(url, request, JSON.toJSONString(updateRequest));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("删除交行机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除交换机失败");
        }
    }

    /**
     * 删除虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse deleteDVSwitch(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String dvSwitchUri = request.getBody().getCloud().getString("uri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDVSwitchDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{dvSwitchUri});
            String json = HttpClientUtil.delete(url,request);
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("删除磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除磁盘失败");
        }
    }
}
