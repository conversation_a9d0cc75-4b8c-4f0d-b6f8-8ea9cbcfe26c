package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DataStoresResponse {

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 分页查询站点/主机/集群下所有数据存储信息列表
     */
    private List<DataStoresResponseData> datastores;

    @Data
    public static class DataStoresResponseData {
        /**
         * 唯一标识数据存储的urn。
         */
        private String urn;

        /**
         * 唯一标识数据存储的uri。
         */
        private String uri;

        /**
         * datastore所关联的存储设备Urn。
         */
        private String suUrn;

        /**
         * datastore所关联的存储设备名称。
         */
        private String suName;

        /**
         * 存储类型<br>
         * 取值参考元数据
         */
        private String storageType;

        /**
         * 簇大小，虚拟化文件系统的簇大小，以KB为单位。
         */
        private Integer clusterSize;

        /**
         * 可读的数据存储的名字。
         */
        private String name;

        /**
         * 1.数据存储状态（NORMAL/ABNORMAL/CREATING/DELETING/READONLY/EXPANDING/RESTORING）；<br>
         * 2.查询主机下数据存储时，该字段表示的是 主机和数据存储的关联状态(关联中CONNECTING，已关联CONNECTED，解关联DISCONNECTING，只读READONLY)。
         */
        private String status;

        /**
         * 精简配置方案变更，不再使用该参数，但是保留；数据存储的最大可用容量，以G为单位
         */
        private Float capacityGB;

        /**
         * 数据存储的已经使用空间，以G为单位。
         */
        private Float usedSizeGB;

        /**
         * 精简配置方案变更，不再使用该参数，但是保留；数据存储的空闲空间，以G为单位。
         */
        private Float freeSizeGB;

        /**
         * 数据存储挂接的主机URN。
         */
        private List<String> hosts;

        /**
         * 是否支持精简配置。
         */
        private Boolean isThin;

        /**
         * 描述。
         */
        private String description;

        /**
         * 精简配置方案变更，不再使用该参数，但是保留；超分配比率，在100到300之间；如果不支持精简配置，则为100；只有支持精简配置，才能设置
         */
        private Integer thinRate;

        /**
         * 实际总空间，针对精简配置，非精简配置情况下和capacityGB相同。
         */
        private Float actualCapacityGB;

        /**
         * 实际剩余空间，针对精简配置，非精简配置情况下和freeSizeGB相同。
         */
        private Float actualFreeSizeGB;

        /**
         * 上次刷新时间，UTC时间， 格式如：“2012-08-27 20:29:19”。
         */
        private String refreshTime;

        /**
         * 数据存储对应的版本号，目前VIMS、advanceSan v3系列数据存储返回相应的版本号。
         */
        private String version;

        /**
         * 第一个代表存储池tier0的容量，第二个为存储池tier1的容量，第三个代表存储池tier2的容量
         */
        private long[] tierSize;

        /**
         * 预留字段（数据存储的io延时时间）。
         */
        private String ioDelay;

        /**
         * 扩容次数。
         */
        private Integer expandCount;

        /**
         * 数据存储对应的存储设备列表，其中第一个存储设备为主LUN。
         */
        private List<String> suIdList;

        /**
         * IO控制标识（预留字段），1启动SIOC，0关闭SIOC。
         */
        private Integer siocFlag;

        /**
         * 存储设备列表。
         */
//        private List<StorageUnit> storageUnits;

        /**
         * 预留，暂未使用；R5C10版本新增。
         */
        private Map<String,String> params;
    }
}
