package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net;

import lombok.Data;

@Data
public class DVSwitchCreateRequest {

    /**
     * Dvswitch名称。
     */
    private String name;

    /**
     * 描述。
     */
    private String description;

    /**
     * 使用的交换类型：<br>
     *0: vSwitch 普通模式；<br>
     *1: eSwitch-VMDQ，直通模式；<br>
     *2: SR-IOV 直通模式 （保留取值）。<br>
     */
    private Integer type;

    /**
     * mtu。
     */
    private Integer mtu;

    /**
     * isIgmpSnooping。
     */
    private boolean isIgmpSnooping;
}
