package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response.VmResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;

import java.util.*;

public class RefreshService {

    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        CloudAccessBean accessBean = request.getBody().getAccess();
        String uri = UrnUtils.formatUrn(request.getBody().getCloud().getString("urn"));
        String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getQueryVmDetailUrl(accessBean.getScvmmRole()), new String[]{uri});
        String json = HttpClientUtil.get(url, request);
        VmResponse.VmResponseData responseData = JSONObject.parseObject(json, VmResponse.VmResponseData.class);
        List<VmResponse.VmResponseData> vms = new ArrayList<>();
        vms.add(responseData);
        VmResponse vmResponse = new VmResponse();
        vmResponse.setVms(vms);
        Map<Class,List> result = FetchService.convertInstance(request, vmResponse);
        List<CmdbInstanceRes> res = result.get(CmdbInstanceRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
            cmdbInstanceRes.setRes_id(resId);
            List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, result);
            CmdbInstanceRes cmdbInstanceRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }

    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("creating");//创建中
            add("shutting_down");//删除中
            add("migrating");//迁移中
            add("fault-resuming");//故障恢复中
            add("starting");//启动中
            add("stopping");//停止中
            add("hibernating");//休眠中
            add("recycling");//回收中
        }
    };
}
