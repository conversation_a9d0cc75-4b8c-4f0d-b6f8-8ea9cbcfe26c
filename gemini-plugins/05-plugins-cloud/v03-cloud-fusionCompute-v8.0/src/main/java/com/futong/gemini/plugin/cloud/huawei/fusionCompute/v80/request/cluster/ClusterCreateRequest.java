package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.cluster;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ClusterCreateRequest {

    /**
     * 集群名称，长度为[1,256] ，全局唯一<br>
     * 注：创建时必选，修改时可选。
     */
    private String name;

    /**
     * 集群架构，取值“x86”、“arm”，不填充该值时，默认为“x86”。arm场景下必须填充该字段。
     */
    private String arch;

    /**
     * cluster标识<br>
     * 注：创建时生成，查询时返回。
     */
    private String urn;

    /**
     * 访问该cluster对应的uri地址<br>
     * 注：创建时生成，查询时返回。
     */
    private String uri;


    /**
     * 【可选】父目录标识，默认站点，若直接位于站点下则为空。
     */
    private String parentObjUrn;

    /**
     * 【可选】集群描述，长度为[0,1024]。
     */
    private String description;

    /**
     * 【可选】集群标签，长度[0,64]<br>
     * 内部不使用，用于接口对接中第三方扩展，不支持英文字符：[]。
     */
    private String tag;

    /**
     * 【可选】内存复用开关<br>
     * true: 开启，false: 关闭（默认）。
     */
    private Boolean isMemOvercommit;

    /**
     * 【可选】HA功能开关<br>
     * true: 开启（默认），false: 关闭。
     */
    private Boolean isEnableHa;

    /**
     * 【可选】集群HA设置，HA功能开启时需要设置。
     */
    private HAResSetting haResSetting;

    /**
     * 【可选】DRS开关<br>
     * true: 开启，false: 关闭（默认）。
     */
    private Boolean isEnableDrs;

    /**
     * 【可选】集群DRS设置，DRS功能开启时需要设置 。
     */
    private DRSSetting drsSetting;

    /**
     * DRS高级选项配置<br>
     * 注：系统保留字段，仅支持 查询、修改 。
     */
    private List<DrsExtensionConfig> drsExtensionConfig;

    /**
     * 查询cluster统计信息uri地址<br>
     * 格式为：< cluster_uri>/statistics，如：“/service/sites/3EB607A6/clusters/366/statistics”<br>
     * 注：该字段仅在查询时返回。
     */
    private String statistics;

    /**
     * 【可选】集群的资源分配策略<br>
     * loadBalance: 负载均衡 （默认）<br>
     * random: 随机。
     */
    private String resStrategy;

    /**
     * 【可选】异构迁移集群(IMC)模式开关<br>
     *  true: 开启<br>
     *  false: 关闭（默认）<br>
     * （修改时）为空：保持imcSetting相关配置，不做修改。
     */
    private Boolean isEnableImc;

    /**
     * 【可选】异构迁移集群(IMC)模式设置Baseline值，当参数isEnableImc为false或为空时，忽略此参数<br>
     * （创建或修改时）可设置Intel系列CPU 5个(不支持AMD系列CPU)<br>
     * （查询时）可返回Intel系列CPU 5个(不支持AMD系列CPU)：<br>
     * “Merom”，“Penryn”，“Nehalem”，“Westmere”，“Sandy Bridge”，后一个Baseline兼容前一个。
     */
    private String imcSetting;

    /**
     * 集群下所有主机（不包括故障、维护模式的主机）中最大的CPU核数<br>
     * 注： 该字段仅查询时返回，不可创建或修改。
     */
    private Integer maxCpuQuantity;

    /**
     * 【可选】是否开启虚拟机个别设置<br>
     * true：开启<br>
     * false：关闭（默认）。
     */
    private Boolean enableVmDrs;

    /**
     * 【可选】DRS虚拟机个别配置，只返回已设置个别配置的虚拟机，集群内其他虚拟机为默认与集群DRS相同，仅DRS功能开启时设置<br>
     * 注：此字段仅在修改时可用，创建集群时不可选，查询时返回。
     */
    private List<DrsVmConfig> drsVmConfig;

    /**
     * 【可选】集群非统一内存访问开关（NUMA）<br>
     * true ： 开启<br>
     * false ：关闭（默认）。
     */
    private Boolean enableGuestNuma;

    /**
     * 【可选】hostNumaDrs开关
     * true ： 开启<br>
     * false ：关闭（默认）。
     */
    private Boolean enableHostNumaDRS;

    /**
     * 【可选】集群本地内存盘开关<br>
     * true ： 开启<br>
     * false ：关闭。
     */
    private Boolean enableIOTailor;

    /**
     * 预留，暂未使用，R5C10版本新增；<br>
     * 注： 该字段仅查询时返回，不可创建或修改。
     */
    private Map<String,String> params;

    /**
     * 【可选】默认为0：不停止虚拟机，范围为[0,1],用于存储断链停止虚拟机
     */
    private Integer dsFaultStrategy;

    /**
     * 【可选】集群所在文件夹名称,仅作为查询结果返回
     */
    private String parentObjName;


}
