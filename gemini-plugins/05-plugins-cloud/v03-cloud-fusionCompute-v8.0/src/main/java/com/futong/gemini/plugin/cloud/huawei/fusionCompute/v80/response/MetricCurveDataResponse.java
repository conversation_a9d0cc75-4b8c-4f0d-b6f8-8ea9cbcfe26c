package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class MetricCurveDataResponse {

    /**
     * Items的个数。
     */
    private Integer total;

    /**
     * 预留。
     */
    private String result;

    /**
     * 指标返回值列表。
     */
    private List<CurveData> items;

    @Data
    public static class CurveData {
        /**
         * 单位。
         */
        private String unit;

        /**
         * 对象标识。
         */
        private String urn;

        /**
         * 对象实例名称。
         */
        private String objectName;

        /**
         * 指标标识。
         */
        private String metricId;

        /**
         * 指标名称。
         */
        private String metricName;

        /**
         * 指标值。
         */
        private List<MetricValue> metricValue;
    }

    @Data
    public static class MetricValue {
        /**
         * 时间距离。
         */
        private String time;

        /**
         * 对应时间的指标值。
         */
        private String value;
    }
}
