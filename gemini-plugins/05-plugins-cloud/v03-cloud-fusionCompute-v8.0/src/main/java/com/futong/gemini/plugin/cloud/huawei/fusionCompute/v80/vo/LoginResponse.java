package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.vo;

import lombok.Data;

import java.util.List;

@Data
public class LoginResponse {

    /**
     * Token有效期，为600000单位ms。
     */
    private Integer validity;

    /**
     * 用户没有权限的权限ID列表。
     */
    private List<String> privilegeIds;

    /**
     * 用户id。
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     *  角色列表
     */
    private List<String> roleList;

    /**
     * 账户安全类型<br>
     * 1：普通模式下的账户<br>
     * 2：高安全模式下系统管理员类账户<br>
     * 3：高安全模式下安全管理员类账户<br>
     * 4：高安全模式下安全审计员类账户。
     */
    private String rightType;

    /**
     * token
     */
    private String token;
}
