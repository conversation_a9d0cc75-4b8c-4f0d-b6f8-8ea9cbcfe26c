package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UrnUtils {

    public static String formatUrn(String urn) {
        String[] openIdStr = urn.split(":");
        StringBuffer buffer = new StringBuffer("/service/sites");
        for (int i = 2; i < openIdStr.length; i++) {
            buffer.append("/" + openIdStr[i]);
        }
        String uri = buffer.toString();
        return uri;
    }
}
