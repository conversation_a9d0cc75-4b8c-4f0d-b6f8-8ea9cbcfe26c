package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class FolderResponse {

    /**
     * 文件夹列表
     */
    private List<FolderResponseData> folders;

    @Data
    public static class FolderResponseData {
        /**
         * 文件夹标识
         */
        private String urn;

        /**
         * 访问文件夹的uri
         */
        private String uri;

        /**
         * 文件夹名
         */
        private String name;

        /**
         * 上级对象标识
         */
        private String parentObjUrn;
    }
}
