package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class VolumesResponse {

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 卷信息列表
     */
    private List<Volume> volumes;

    @Data
    public static class Volume{
        /**
         * 集群内唯一标识卷的urn。
         */
        private String urn;

        /**
         * 集群内唯一标识卷的uri。
         */
        private String uri;

        /**
         * 卷uuid。
         */
        private String uuid;

        /**
         * 卷名称。
         */
        private String name;

        /**
         * 卷的最大容量。
         */
        private Integer quantityGB;

        /**
         * 卷状态: <br>
         * CREATING：创建中；<br>
         * DELETING：删除中；<br>
         * USE：可用的；<br>
         * RESTORING：恢复中；<br>
         * RESTORED：已恢复；<br>
         * SNAPSHOTING：正在做快照；<br>
         * FORMATING：正在格式化；<br>
         * FORMATFAILED：格式化失败；<br>
         * COPYING：拷贝中；<br>
         * COPYFAILED：拷贝失败；<br>
         * MAGRATING：迁移中；<br>
         * RESIZING：扩容中；<br>
         * SHRINKING：磁盘容量回收中
         */
        private String status;

        /**
         * 存储类型，若携带datastoreUrn则此参数无效，取值参考数据存储元数据。
         */
        private String storageType;

        /**
         * 是否瘦分配。
         */
        private Boolean isThin;

        /**
         * 卷类型：<br>
         * 普通normal， 共享卷share。
         */
        private String type;

        /**
         * 数据存储标识。
         */
        private String datastoreUrn;

        /**
         * 数据存储名称。
         */
        private String datastoreName;

        /**
         * 是否为独立磁盘。
         */
        private Boolean indepDisk;

        /**
         * 是否持久化磁盘。
         */
        private Boolean persistentDisk;

        /**
         * 卷在存储设备上的标识。
         */
        private String volNameOnDev;

        /**
         * 表示卷当前占用的实际空间大小，单位为MB。
         */
        private Long volProvisionSize;

        /**
         * 表示虚拟机中用户已用空间大小，单位为MB
         */
        private Long userUsedSize;

        /**
         * 是否是差分卷。
         */
        private Boolean isDiffVol;

        /**
         * 磁盘类型参数，取值为：<br>
         * 0：普通卷 <br>
         * 1：延迟置零卷<br>
         * 注：该字段为可选字段，不携带时代表0，即普通卷<br>
         * 该字段在isThin参数为false时生效，在isthin参数为true时失效。
         */
        private Integer volType;

        /**
         * 每秒最大读字节数，单位为KB/s。
         */
        private Long maxReadBytes;

        /**
         * 每秒最大写字节数，单位为KB/s。
         */
        private Long maxWriteBytes;

        /**
         * 每秒最大读请求个数，单位为个/s。
         */
        private Long maxReadRequest;

        /**
         * 每秒最大写请求个数，单位为个/s。
         */
        private Long maxWriteRequest;

        /**
         * 卷的总线类型，当前只有SCSI和IDE两种。
         */
        private String pciType;

        /**
         * true表示为占位虚拟机的磁盘，false表示为非占位虚拟机磁盘。
         */
        private Boolean occupiedVolume;

        /**
         * 如果该卷绑定给虚拟机则返回值为3，否则返回值为2<br>
         * 注释：在查询指定卷的时候该字段不返回
         */
        private Integer bindToVm;

        /**
         * 原卷urn。
         */
        private String srcVolumeUrn;

        /**
         * 卷的使用类型，默认值0<br>
         * 0:一般卷<br>
         * 1:内存复用卷<br>
         * 2: 链接克隆卷母卷<br>
         * 3: 休眠卷<br>
         * 4: 有快照的卷<br>
         * 5: 备份卷<br>
         * 6: 临时卷<br>
         * 7: 链接克隆ID盘<br>
         * 8: 非持久化磁盘差分卷<br>
         * 9: iotailor的应急盘<br>
         * 10: 容灾配置卷。
         */
        private Integer volumeUseType;

        /**
         * 卷的io权重。
         */
        private String ioWeight;

        /**
         * 设置存储io控制。
         */
        private Integer siocFlag;

        /**
         * 卷的url。
         */
        private String volumeUrl;

        /**
         * 链接克隆母卷。
         */
        private Long linkCloneParent;

        /**
         * 卷的存储信息。
         */
        private String volInfoUrl;

        /**
         * 主机复制容灾扩展字段（json格式）dsMgntIp：dsware资源池管理IP；dsResourceId：dsware资源池IDeg：<br>
         * {“dsMgntIp”:”*******”,”dsResourceId”:”1”}
         */
        private String drExtParams;

        /**
         * 预留字段,R5C10版本新增。
         */
        private Map<String, String> params;

        /**
         * 0代表不支持裸设备映射，1代表支持裸设备映射
         */
        private Integer pvscsiSupport;

        /**
         * 存储的版本号
         */
        private String storageVersion;

        /**
         * 卷所在物理存储资源序列号
         */
        private String storageSN;

        /**
         * 卷对应的LUN在物理存储资源上的ID号
         */
        private String lunId;

        /**
         * 备份方案预留字段
         * 仅查询指定卷时返回
         */
        private String sdUrn;

        /**
         * 备份方案预留字段
         * 仅查询指定卷时返回
         */
        private String sdUri;

        /**
         * 备份方案预留字段
         * 仅查询指定卷时返回
         */
        private String iscsi;

        private String volumeFormat;
    }
}
