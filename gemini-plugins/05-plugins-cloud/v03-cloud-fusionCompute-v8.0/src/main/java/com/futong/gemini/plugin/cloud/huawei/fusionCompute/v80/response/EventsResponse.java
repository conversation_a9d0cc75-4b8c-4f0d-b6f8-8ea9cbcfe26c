package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class EventsResponse {

    /**
     * 事件总数
     */
    private Integer total;

    /**
     * 页数
     */
    private Integer pageno;

    /**
     * 页记录数
     */
    private Integer itemSize;

    /**
     * 事件列表
     */
    private List<EventsResponseData> items;

    @Data
    public static class EventsResponseData {
        /**
         * 事件名称。
         */
        private String eventName;

        /**
         * 事件发生的对象类型。
         */
        private String objectType;

        /**
         * 事件发生的对象标示。
         */
        private String objectUrn;

        /**
         * URN别名。
         */
        private String urnByName;

        /**
         * 事件ID。
         */
        private String eventID;

        /**
         * 事件产生时间（long型UTC字符串）。
         */
        private String occurTime;

        /**
         * 附加信息。
         */
        private String additionalInfo;

        /**
         * 事件产生原因。
         */
        private String isParse;

        /**
         * C10SPC700新增，预留字段。
         */
        private String locationInfo;
    }
}
