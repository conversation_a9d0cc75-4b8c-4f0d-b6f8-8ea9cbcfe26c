package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.storage;

import lombok.Data;

@Data
public class AttachVolumeRequest {
    /**
     * 【必选】卷标识，形如：urn:sites:1:volumes:78。
     */
    private String volUrn;

    /**
     * 【可选】磁盘挂载的总线类型，不选默认IDE，当前版本为：“IDE”，“SCSI”；
     * 只有在裸设备映射上创建的磁盘才可以挂载到SCSI总线上，其它的都是IDE总线上，如果是以前版本，都是默认IDE。
     */
    private String pciType;

    /**
     * 【可选】卷插槽号，最大为60，默认为系统选择第一个空闲插槽，若携带，不能与现有总线类型的重复，否则失败。
     */
    private Integer sequenceNum;

    private String ioMode;

    private Integer accessMode;
}
