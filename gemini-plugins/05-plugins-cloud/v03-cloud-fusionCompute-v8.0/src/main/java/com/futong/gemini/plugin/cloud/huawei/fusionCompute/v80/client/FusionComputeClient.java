package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.client;

import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host.HostCreateRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.VRMTask;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FusionComputeClient {

    public VRMTask createHost(HostCreateRequest request){
        return null;
    }

}
