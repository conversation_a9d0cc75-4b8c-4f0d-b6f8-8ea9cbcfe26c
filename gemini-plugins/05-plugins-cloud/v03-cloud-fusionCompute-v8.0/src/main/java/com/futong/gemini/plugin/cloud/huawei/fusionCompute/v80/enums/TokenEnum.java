package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.enums;

public enum TokenEnum {

    /**传递token的请求头参数名称*/
    AUTH_TOKEN("X-Auth-Token"),

    AUTHORIZATION("Authorization"),

    /**获取token的响应头参数名*/
    AUBJECT_TOKEN("X-Subject-Token"),

    AUTH_USER("X-Auth-User"),

    AUTH_KEY("X-Auth-Key");



    private String value;

    private TokenEnum(String value) {
        this.value = value;
    }
    public String getValue() {
        return value;
    }
}
