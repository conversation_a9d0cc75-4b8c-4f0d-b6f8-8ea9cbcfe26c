package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbIpRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbNetcardRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;

import java.util.List;

public class CiResCloudUtils {

    public static TmdbResourceSet toTmdbResourceSet(CloudAccessBean bean, String setType, Class c, String setId, String resourceType, String resourceId) {
        TmdbResourceSet set = new TmdbResourceSet();
        set.setAccount_id(bean.getCmpId());
        set.setCloud_type(bean.getCloudType());
        set.setResource_type(resourceType);
        if(ResourceType.CMDB_INSTANCE_RES.value().equals(resourceType)){
//            MorManager morManager = new MorManager();
//            morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, bean.getCmpId()));
//            VirtualMachineSummary summary = null;
//            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), resourceId);
//            try {
//                summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
//            } catch (Exception e) {
//                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
//            }
//            set.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), resourceType, summary.getConfig().isTemplate()?"1":"0",resourceId));
        }else {
            set.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), resourceType, resourceId));
        }
        set.setResource_type(resourceType);
        set.setSet_type(setType);
        set.setSet_table(AssociationUtils.otcTableName(c));
        set.setSet_id(IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), setType, setId}));
        set.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), set.getSet_id(), set.getResource_id()}));
        return set;
    }

    public static <T> void toTmdbResourceSet(List<TmdbResourceSet> sets, CloudAccessBean bean, String setType, String setId, List<T> infos) {

        if (ObjectUtil.isNotEmpty(infos)) {
            infos.forEach(info -> {
                TmdbResourceSet set = new TmdbResourceSet();
                set.setAccount_id(bean.getCmpId());
                set.setCloud_type(bean.getCloudType());
                set.setSet_type(setType);
                set.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), setType, setId));
                if (info instanceof CmdbSnapshotRes) {
                    CmdbSnapshotRes res = (CmdbSnapshotRes) info;
                    set.setResource_id(res.getRes_id());
                    set.setResource_type(ResourceType.CMDB_SNAPSHOT_RES.value());
                } else if (info instanceof CmdbDiskRes) {
                    CmdbDiskRes res = (CmdbDiskRes) info;
                    set.setResource_id(res.getRes_id());
                    set.setResource_type(ResourceType.CMDB_DISK_RES.value());
                } else if (info instanceof CmdbIpRes) {
                    CmdbIpRes res = (CmdbIpRes) info;
                    set.setResource_id(res.getRes_id());
                    set.setResource_type(ResourceType.CMDB_IP_RES.value());
                } else if (info instanceof CmdbNetcardRes) {
                    CmdbNetcardRes res = (CmdbNetcardRes) info;
                    set.setResource_id(res.getRes_id());
                    set.setResource_type(ResourceType.CMDB_NETCARD_RES.value());
                }
                set.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), set.getSet_id(), set.getResource_id()}));
                sets.add(set);
            });
        }
    }
}
