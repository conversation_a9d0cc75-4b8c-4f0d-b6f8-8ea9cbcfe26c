package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.PageUtils;
import com.futong.common.utils.TimeUtils;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.*;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.Disk;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.Nic;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.OsOption;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.VmConfig;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.convert.Converts;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.MetricRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.MetricRequestParam;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.alarm.AlarmRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.alarm.EventRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host.HostDescribeRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net.PortGroupsRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net.SecurityGroupDescribeRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.net.UplinkPortsRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.storage.DiskDescribeRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm.VmDescribeRequest;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
public class FetchService {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest arguments, T t) {
        t.setCloud_type(arguments.getPlugin().getRealm());
        t.setAccount_id(arguments.getBody().getAccess().getCmpId());
    }

    /**
     * 获取站点
     * @param request
     * @return
     */
    public static BaseResponse fetchSite(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySitesUrl(cloudAccessBean.getScvmmRole()), null);
            String json = HttpClientUtil.get(url, request);
            SitesResponse response = JSONObject.parseObject(json,SitesResponse.class);
            Map<Class, List> map = convertSite(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, map);
            if (CollUtil.isEmpty(map.get(TmdbDevops.class))) {
                return baseResponse;
            }
            return BaseCloudService.toGourdResponse(baseResponse, map.get(TmdbDevops.class), (TmdbDevops t) -> {
                JSONObject jsonObject = JSONObject.parseObject(t.getInfo_json());
                JobInfo jobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_PLATFORM_CLUSTER);
                request.getBody().getCloud().put("siteUri",jsonObject.getString("uri"));
                request.getBody().getCloud().put("siteUrn",jsonObject.getString("urn"));
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }catch (Exception e){
            log.error("获取站点失败失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取站点失败失败");
        }
    }
    public static Map<Class, List> convertSite(BaseCloudRequest request, SitesResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getSites())){
            result.put(TmdbDevopsLink.class,null);
            result.put(TmdbDevops.class,null);
            return result;
        }
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                response.getSites(),
                SitesResponse.SitesResponseData::getName,
                SitesResponse.SitesResponseData::getUrn
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 获取集群信息
     * @param request
     * @return
     */
    public static BaseResponse fetchCluster(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryClustersUrl(cloudAccessBean.getScvmmRole()), new String[] {siteUri});
            String json = HttpClientUtil.get(url, request);
            ClusterResponse response = JSONObject.parseObject(json,ClusterResponse.class);
            Map<Class,List> result = convertCluster(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return BaseCloudService.toGourdResponse(baseResponse, result.get(TmdbDevops.class), (TmdbDevops t) -> {
                JSONObject jsonObject = JSONObject.parseObject(t.getInfo_json());
                JobInfo jobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_STORAGE_POOL);
                request.getBody().getCloud().put("scope",jsonObject.getString("urn"));
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }catch (Exception e){
            log.error("获取集群失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取集群失败");
        }
    }
    public static Map<Class, List> convertCluster(BaseCloudRequest request, ClusterResponse response){
        Map<Class, List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getClusters())){
            result.put(TmdbDevops.class,null);
            result.put(TmdbDevopsLink.class,null);
            return result;
        }
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        request.getBody().getCloud().getString("siteUrn"),
                        DevopsSide.DEVOPS_RESOURCEPOOL.value());
        for (ClusterResponse.ClusterResponseData cluster : response.getClusters()) {
            BuilderDevops devopsCluster = new BuilderDevops()
                    .withDevops(devops.get(), cluster.getName(), cluster.getUrn(), DevopsSide.DEVOPS_CLUSTER.value())
                    .withJson(JSON.toJSONString(cluster));
            data.add(devopsCluster.get());
            links.add(devopsCluster.builderLink(devops.get()));
        }
        result.put(TmdbDevops.class,data);
        result.put(TmdbDevopsLink.class,links);
        return result;
    }

    /**
     * 同步物理机
     * @param arguments
     * @return
     */
    public static BaseResponse fetchHost(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            HostDescribeRequest request = arguments.getBody().getCloud().toJavaObject(HostDescribeRequest.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryHostsUrl(cloudAccessBean.getScvmmRole()), new String[]{request.getSiteUri(), request.getLimit(), request.getOffset()});
            String json = HttpClientUtil.get(url, arguments);
            HostResponse response = JSONObject.parseObject(json,HostResponse.class);
            Map<Class, List> result = convertHost(arguments,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(arguments, result);
            return BaseCloudService.toGourdResponse(baseResponse, response.getHosts(), (HostResponse.HostResponseData t) -> {
                JobInfo jobInfo = new JobInfo();
                arguments.setAction(ActionType.FETCH_COMPUTE_OS_VERSION);
                arguments.getBody().getCloud().put("arch",t.getArch());
                arguments.getBody().getCloud().put("cpuVendor",t.getCpuVendor());
                arguments.getBody().getCloud().put("osVendor",t.getOsVendor());
                arguments.getBody().getCloud().put("hostUrn",t.getUrn());
                jobInfo.setRequest(arguments.cloneJSONObject());
                return jobInfo;
            });
        }catch (Exception e){
            log.error("获取Host物理机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取Host物理机失败");
        }
    }
    public static Map<Class, List> convertHost(BaseCloudRequest request,HostResponse response){
        Map<Class, List> result = new HashMap<>();
        if(response == null  || CollUtil.isEmpty(response.getHosts())){
            result.put(CmdbHostRes.class,null);
            result.put(Association.class,null);
            result.put(TmdbResourceSet.class,null);
            result.put(CmdbIpRes.class,null);
            result.put(CmdbStoragePoolRes.class,null);
            return result;
        }
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbHostRes> data = new ArrayList<>();
        List<CmdbIpRes> ipList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbStoragePoolRes> storagePoolResList = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_HOST_RES
                );
        String siteResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                request.getBody().getCloud().getString("siteUrn"));
        for (HostResponse.HostResponseData res : response.getHosts()) {
            CmdbHostRes ci = new CmdbHostRes();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), res.getUrn()));
            ci.setOpen_id(res.getUrn());
            ci.setOpen_name(res.getName());
            ci.setModel(res.getModel());
            ci.setManufacturer(res.getVendor());
            switch (res.getStatus()) {
                case "rebooting":
                    ci.setStatus(InstanceStatus.RESTARTING.value());
                    break;
                case "normal":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "fault":
                    ci.setStatus(InstanceStatus.ERROR.value());
                    break;
                case "unknow":
                    ci.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
                case "poweroff":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
            }
            ci.setOpen_status(res.getStatus());
            ci.setIp(res.getIp());
            ci.setCpu_size(res.getCpuQuantity());
            ci.setMem_size(res.getMemQuantityMB());
            ci.setMaintain_mode(res.getIsMaintaining() ? "1" : "0");
            ci.setManufacturer(res.getOsVendor());
            ci.setExtend1(res.getArch());
            ci.setExtend2(res.getCpuVendor());
            toCiResCloud(request,ci);
            data.add(ci);
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, siteResId);
            if(StrUtil.isNotEmpty(res.getClusterUrn())){
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, res.getClusterUrn());
            }
            if(StrUtil.isNotEmpty(res.getIp())){
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),res.getIp()));
                ip.setOpen_id(res.getIp());
                ip.setOpen_name(res.getIp());
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(res.getIp());
                toCiResCloud(request,ip);
                ipList.add(ip);
                associations.add(AssociationUtils.toAssociation(ci, ip));
            }
            //关联查询数据存储
            Float total_size = 0F,used_size = 0F,allocation_size = 0F;
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryDataStoresUrl(cloudAccessBean.getScvmmRole()), new String[]{request.getBody().getCloud().getString("siteUri"), request.getBody().getCloud().getString("limit"),request.getBody().getCloud().getString("offset"),res.getUrn()});
            String json = HttpClientUtil.get(url, request);
            DataStoresResponse dataStoresResponse = JSONObject.parseObject(json,DataStoresResponse.class);
            if(dataStoresResponse != null && CollUtil.isNotEmpty(dataStoresResponse.getDatastores())) {
                for (DataStoresResponse.DataStoresResponseData datastore : dataStoresResponse.getDatastores()) {
                    allocation_size += datastore.getUsedSizeGB();
                    used_size += datastore.getActualCapacityGB() - datastore.getActualFreeSizeGB();
                    total_size += datastore.getActualCapacityGB();
                    CmdbStoragePoolRes storagePoolRes = new CmdbStoragePoolRes();
                    storagePoolRes.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),datastore.getUrn()));
                    storagePoolRes.setOpen_id(datastore.getUrn());
                    storagePoolRes.setOpen_name(datastore.getName());
                    storagePoolRes.setType(datastore.getStorageType());
                    storagePoolRes.setTotal_size(datastore.getActualCapacityGB());
                    storagePoolRes.setUsed_size(datastore.getActualCapacityGB() - datastore.getActualFreeSizeGB());
                    storagePoolRes.setAllocation_size(datastore.getUsedSizeGB());
                    storagePoolRes.setIs_thin(datastore.getIsThin().toString());
                    storagePoolRes.setStatus(datastore.getStatus().toLowerCase());
                    storagePoolRes.setOpen_status(datastore.getStatus());
                    storagePoolRes.setRefresh_time(TimeUtils.stringToLongTime(datastore.getRefreshTime()));
                    toCiResCloud(request,storagePoolRes);
                    storagePoolResList.add(storagePoolRes);
                    //与物理机关联
                    Association association = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getUrn()));
                    associations.add(association);
                }
            }
            ci.setTotal_size(total_size);
            ci.setAllocation_size(allocation_size);
            ci.setUsed_size(used_size);
        }
        result.put(CmdbHostRes.class,data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        result.put(CmdbIpRes.class, ipList);
        result.put(CmdbStoragePoolRes.class, storagePoolResList);
        return result;
    }

    /**
     * 同步虚拟机
     * @param request
     * @return
     */
    public static BaseResponse fetchInstance(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            VmDescribeRequest vmDescribeRequest = request.getBody().getCloud().toJavaObject(VmDescribeRequest.class);
            if(StrUtil.isEmpty(vmDescribeRequest.getIsTemplate())){
                vmDescribeRequest.setIsTemplate("false");
            }
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryVMsUrl(cloudAccessBean.getScvmmRole()), new String[]{vmDescribeRequest.getSiteUri(), vmDescribeRequest.getIsTemplate(), vmDescribeRequest.getLimit(), vmDescribeRequest.getOffset(),"2"});
            String json = HttpClientUtil.get(url, request);
            VmResponse response = JSONObject.parseObject(json, VmResponse.class);
            Map<Class, List> result = convertInstance(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
//            return BaseCloudService.toflatMapGourdResponse(baseResponse, response.getVms(), (VmResponse.VmResponseData t) -> {
//                List<JobInfo> jobInfoList = new ArrayList<>();
//                JobInfo snapshot = new JobInfo();
//                request.setAction(ActionType.FETCH_STORAGE_SNAPSHOT);
//                request.getBody().getCloud().put("vmUri",t.getUri());
//                request.getBody().getCloud().put("vmUrn",t.getUrn());
//                snapshot.setRequest(request.cloneJSONObject());
//                jobInfoList.add(snapshot);
//                return jobInfoList.stream();
//            });
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取VM虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取VM虚拟机失败");
        }
    }
    public static Map<Class, List> convertInstance(BaseCloudRequest request, VmResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getVms())){
            result.put(CmdbInstanceRes.class,null);
            result.put(Association.class,null);
            result.put(CmdbOsRes.class,null);
            result.put(CmdbDiskRes.class,null);
            result.put(CmdbNetcardRes.class,null);
            result.put(CmdbIpRes.class,null);
            result.put(TmdbResourceSet.class,null);
            result.put(CmdbSnapshotRes.class,null);
            return result;
        }
        CloudAccessBean cloudAccessBean = request.getBody().getAccess();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_INSTANCE_RES
                );
        String siteResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                request.getBody().getCloud().getString("siteUrn"));
        List<CmdbInstanceRes> instances = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbDiskRes> dkList = new ArrayList<>();
        List<CmdbNetcardRes> netcardResList = new ArrayList<>();
        List<CmdbIpRes> ipList = new ArrayList<>();
        List<CmdbSnapshotRes> snapshotList = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (VmResponse.VmResponseData vm : response.getVms()) {
            List<Disk> diskList = new ArrayList<>();
            List<Nic> nicList = new ArrayList<>();
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),vm.getUrn()));
            ci.setOpen_id(vm.getUrn());
            ci.setOpen_name(vm.getName());
            ci.setOpen_status(vm.getStatus());
            VmConfig config = vm.getVmConfig();
            if(config != null){
                if(config.getCpu() != null){
                    ci.setCpu_size(config.getCpu().getQuantity());
                }
                if(config.getMemory() != null){
                    ci.setMem_size(config.getMemory().getQuantityMB());
                }
                diskList = config.getDisks();
                nicList = config.getNics();
            }
            switch (vm.getStatus()) {
                case "creating":
                    ci.setStatus(InstanceStatus.BUILDING.value());
                    break;
                case "running":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "starting":
                    ci.setStatus(InstanceStatus.STARTING.value());
                    break;
                case "stopping":
                    ci.setStatus(InstanceStatus.STOPPING.value());
                    break;
                case "stopped":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "unknown":
                    ci.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
                case "migrating":
                    ci.setStatus(InstanceStatus.MIGRATING.value());
                    break;
                case "fault-resuming":
                    ci.setStatus(InstanceStatus.RECOVERING.value());
                    break;
                case "shutting-down":
                    ci.setStatus(InstanceStatus.DELETING.value());
                    break;
                default:
                    ci.setStatus(vm.getStatus().toLowerCase());
                    break;
            }
            ci.setOpen_status(vm.getStatus());
            ci.setIs_template(vm.getIsTemplate() ? 1 : 0);
            ci.setDesc(vm.getDescription());
            toCiResCloud(request,ci);
            OsOption osOption = vm.getOsOptions();
            if(ObjectUtil.isNotNull(osOption)){
                Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(accessBean.getCmpId(), osOption.getOsType().toLowerCase(),osOption.getOsVersion()+""));
                associations.add(os);
            }
            //关联集群
            if (StrUtil.isNotEmpty(vm.getClusterUrn())) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, vm.getClusterUrn());            }
            //关联HOST
            if (StrUtil.isNotEmpty(vm.getHostUrn())) {
                Association host = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(accessBean.getCmpId(), vm.getHostUrn()));
                associations.add(host);
            }
            //关联数据存储
            List<String> dataStoreUrns = vm.getDataStoreUrns();
            if(CollUtil.isNotEmpty(dataStoreUrns)){
                dataStoreUrns.forEach(dataStoreUrn -> {
                    Association dataStore = AssociationUtils.toAssociation(ci, CmdbStoragePoolRes.class, IdUtils.encryptId(accessBean.getCmpId(), dataStoreUrn));
                    associations.add(dataStore);
                });
            }
            //磁盘信息
            if(CollUtil.isNotEmpty(diskList)){
                diskList.forEach(disk -> {
                    Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(accessBean.getCmpId(), disk.getVolumeUrn()));
                    associations.add(diskAss);
//                    CmdbDiskRes dk = new CmdbDiskRes();
//                    dk.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), disk.getVolumeUrn()));
//                    dk.setOpen_id(disk.getVolumeUuid());
//                    dk.setOpen_name(disk.getDiskName());
//                    dk.setShare_disk(disk.getIndepDisk()+"");
//                    dk.setCategory(disk.getSystemVolume()?"system":"data");
//                    dk.setSize(Float.parseFloat(disk.getQuantityGB()+""));
//                    toCiResCloud(request,dk);
//                    dkList.add(dk);
//                    Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, dk.getRes_id());
//                    associations.add(diskAss);
                });
            }
            //网卡和IP信息
            if(CollUtil.isNotEmpty(nicList)){
                nicList.forEach(nic -> {
                    CmdbNetcardRes card = new CmdbNetcardRes();
                    card.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), nic.getUrn()));
                    card.setOpen_id(nic.getUrn());
                    card.setOpen_name(nic.getName());
                    card.setMac_address(nic.getMac());
                    card.setType(nic.getNicType()+"");
                    if(StrUtil.isNotEmpty(nic.getIp())){
                        CmdbIpRes ip = new CmdbIpRes();
                        ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), nic.getIp()));
                        ip.setType(IpType.PRIVATE_IP.value());
                        ip.setAddress(nic.getIp());
                        ip.setOpen_id(nic.getIp());
                        ip.setOpen_name(nic.getIp());
                        toCiResCloud(request, ip);
                        ipList.add(ip);
                        associations.add(AssociationUtils.toAssociation(card, ip));
                        //关联云主机
                        associations.add(AssociationUtils.toAssociation(ip, ci));
                    }
                    if(CollUtil.isNotEmpty(nic.getIps6())){
                        nic.getIps6().forEach(ip6->{
                            CmdbIpRes ip = new CmdbIpRes();
                            ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),ip6));
                            ip.setType(IpType.PRIVATE_IP.value());
                            ip.setAddress(ip6);
                            ip.setOpen_id(ip6);
                            ip.setOpen_name(ip6);
                            toCiResCloud(request, ip);
                            ipList.add(ip);
                            associations.add(AssociationUtils.toAssociation(card, ip));
                            //关联云主机
                            associations.add(AssociationUtils.toAssociation(ip, ci));
                        });
                    }
                    if(StrUtil.isNotEmpty(nic.getIpList())){
                        Arrays.stream(nic.getIpList().split(",")).forEach(ipAddress->{
                            CmdbIpRes ip = new CmdbIpRes();
                            ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), ipAddress));
                            ip.setType(IpType.PRIVATE_IP.value());
                            ip.setAddress(ipAddress);
                            ip.setOpen_id(ipAddress);
                            ip.setOpen_name(ipAddress);
                            toCiResCloud(request, ip);
                            ipList.add(ip);
                            associations.add(AssociationUtils.toAssociation(card, ip));
                            //关联云主机
                            associations.add(AssociationUtils.toAssociation(ip, ci));
                        });
                    }
                    toCiResCloud(request,card);
                    netcardResList.add(card);
                    //云主机与Nic关联
                    associations.add(AssociationUtils.toAssociation(ci, card));
                });
            }
            //关联资源池
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, siteResId);
            instances.add(ci);
            //查询云主机下的快照
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryVmSnapshotUrl(cloudAccessBean.getScvmmRole()), new String[]{vm.getUri()});
            String json = HttpClientUtil.get(url, request);
            SnapshotResponse snapshotResponse = JSONObject.parseObject(json,SnapshotResponse.class);
            if(snapshotResponse != null && CollUtil.isNotEmpty(snapshotResponse.getRootSnapshots())){
                snapshotResponse.getRootSnapshots().forEach(snapshot -> {
                    CmdbSnapshotRes snapshotRes = new CmdbSnapshotRes();
                    snapshotRes.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), snapshot.getUrn()));
                    snapshotRes.setOpen_id(snapshot.getUrn());
                    snapshotRes.setOpen_name(snapshot.getName());
                    snapshotRes.setStatus(snapshot.getStatus().toLowerCase());
                    snapshotRes.setOpen_status(snapshot.getStatus());
                    snapshotRes.setType(snapshot.getType());
                    snapshotRes.setDesc(snapshot.getDescription());
                    snapshotRes.setCreate_time(TimeUtils.stringToLongTime(snapshot.getCreateTime()));
                    toCiResCloud(request, snapshotRes);
                    snapshotList.add(snapshotRes);
                    //关联虚拟机
                    Association snapshotAss = AssociationUtils.toAssociation(snapshotRes, CmdbInstanceRes.class, ci.getRes_id());
                    associations.add(snapshotAss);
                });

            }
        }
        result.put(CmdbInstanceRes.class,instances);
        result.put(Association.class,associations);
        result.put(CmdbOsRes.class,osList);
        result.put(CmdbDiskRes.class,dkList);
        result.put(CmdbNetcardRes.class,netcardResList);
        result.put(CmdbIpRes.class,ipList);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(CmdbSnapshotRes.class, snapshotList);
        return result;
    }

    /**
     * 同步磁盘
     * @param request
     * @return
     */
    public static BaseResponse fetchDisk(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DiskDescribeRequest diskDescribeRequest = request.getBody().getCloud().toJavaObject(DiskDescribeRequest.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryVolumesUrl(cloudAccessBean.getScvmmRole()), new String[]{diskDescribeRequest.getSiteUri(), diskDescribeRequest.getLimit(), diskDescribeRequest.getOffset()});
            String json = HttpClientUtil.get(url, request);
            VolumesResponse response = JSONObject.parseObject(json,VolumesResponse.class);
            Map<Class, List> result = convertDisk(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取磁盘失败");
        }
    }
    public static Map<Class,List> convertDisk(BaseCloudRequest request,VolumesResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getVolumes())){
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        List<CmdbDiskRes> data = new ArrayList<>();
        for (VolumesResponse.Volume res : response.getVolumes()) {
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getUrn()));
            ci.setType(res.getType());
            ci.setSize(res.getQuantityGB().floatValue());
            ci.setStatus(res.getStatus().toLowerCase());
            ci.setOpen_status(res.getStatus());
            ci.setOpen_id(res.getUrn());
            ci.setOpen_name(res.getName());
            ci.setUsed_size(res.getVolProvisionSize()==null?0f:Float.parseFloat(String.format("%.2f", res.getVolProvisionSize().floatValue()/1024)));
            if(res.getVolType() == 0 && BooleanUtil.isTrue(res.getIsThin())){
                ci.setConfig_mode("2");
            }else{
                ci.setConfig_mode(res.getVolType()+"");
            }
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_DISK_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_RESOURCEPOOL,
                        request.getBody().getCloud().getString("siteUrn"))
                .getData();
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 获取快照
     * @param request
     * @return
     */
    public static BaseResponse fetchSnapshot(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String vmUri = request.getBody().getCloud().getString("vmUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryVmSnapshotUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            String json = HttpClientUtil.get(url, request);
            SnapshotResponse response = JSONObject.parseObject(json,SnapshotResponse.class);
            Map<Class,List> result = convertSnapshot(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return baseResponse;
        }catch (Exception e){
            log.error("获取快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取快照失败");
        }
    }
    public static Map<Class,List> convertSnapshot(BaseCloudRequest request,SnapshotResponse response){
        Map<Class, List> result = new HashMap<>();
        if (response == null || CollUtil.isEmpty(response.getRootSnapshots())) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        String vmUrn = request.getBody().getCloud().getString("vmUrn");
        for (SnapshotResponse.VmSnapshotInfo res : response.getRootSnapshots()) {
            CmdbSnapshotRes ci = new CmdbSnapshotRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getUrn()));
            ci.setOpen_id(res.getUrn());
            ci.setOpen_name(res.getName());
            ci.setStatus(res.getStatus().toLowerCase());
            ci.setOpen_status(res.getStatus());
//            ci.setSize(NumberUtil.parseFloat(res.getSourceDiskSize()));
            ci.setType(res.getType());
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreateTime()));
            toCiResCloud(request, ci);
            //关联虚拟机
            Association disk = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vmUrn));
            associations.add(disk);
            data.add(ci);
        }
        result.put(CmdbSnapshotRes.class, data);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 同步虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse fetchDVSwitch(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryDVSwitchsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.get(url, request);
            DVSwitchsResponse response = JSONObject.parseObject(json,DVSwitchsResponse.class);
            Map<Class, List> result = convertDVSwitch(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return BaseCloudService.toflatMapGourdResponse(baseResponse, response.getDvSwitchs(), (DVSwitchsResponse.DVSwitchsResponseData t) -> {
                List<JobInfo> jobInfoList = new ArrayList<>();
                //端口组
                JobInfo subnet = new JobInfo();
                request.setAction(ActionType.FETCH_NEUTRON_SUBNET);
                request.getBody().getCloud().put("DVSwitchUri",t.getUri());
                request.getBody().getCloud().put("DVSwitchUrn",t.getUrn());
                subnet.setRequest(request.cloneJSONObject());
                jobInfoList.add(subnet);

                //上行链路组
                JobInfo uplinkPorts = new JobInfo();
                List<String> DVSwitchUrns = new ArrayList<>();
                DVSwitchUrns.add(t.getUrn());
                request.setAction(ActionType.FETCH_NEUTRON_UPLINK_PORTS);
                request.getBody().getCloud().put("dvsurns",DVSwitchUrns);
                uplinkPorts.setRequest(request.cloneJSONObject());
                jobInfoList.add(uplinkPorts);

                //vlan池
                JobInfo vlan = new JobInfo();
                request.setAction(ActionType.FETCH_NEUTRON_VLAN);
                request.getBody().getCloud().put("DVSwitchUri",t.getUri());
                request.getBody().getCloud().put("DVSwitchUrn",t.getUrn());
                vlan.setRequest(request.cloneJSONObject());
                jobInfoList.add(vlan);
                return jobInfoList.stream();
            });
        }catch (Exception e){
            log.error("获取虚拟交换机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取虚拟交换机失败");
        }
    }
    public static Map<Class,List> convertDVSwitch(BaseCloudRequest request,DVSwitchsResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getDvSwitchs())){
            result.put(TmdbResourceSet.class,null);
            result.put(CmdbVswitchRes.class,null);
        }
        List<CmdbVswitchRes> vswitchList = new ArrayList<>();
        for (DVSwitchsResponse.DVSwitchsResponseData dvSwitch : response.getDvSwitchs()) {
            CmdbVswitchRes vswitch = new CmdbVswitchRes();
            vswitch.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), dvSwitch.getUrn()));
            vswitch.setOpen_id(dvSwitch.getUrn());
            vswitch.setOpen_name(dvSwitch.getName());
            vswitch.setDesc(dvSwitch.getDescription());
            vswitch.setType(dvSwitch.getType()+"");
            toCiResCloud(request,vswitch);
            vswitchList.add(vswitch);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_VSWITCH_RES
                ).withDataByDevopsValue(vswitchList,
                        DevopsSide.DEVOPS_RESOURCEPOOL,
                        request.getBody().getCloud().getString("siteUrn"))
                .getData();
        result.put(TmdbResourceSet.class,sets);
        result.put(CmdbVswitchRes.class,vswitchList);
        return result;
    }

    /**
     * 同步子网（端口组）
     * @param request
     * @return
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            PortGroupsRequest portGroupsRequest = request.getBody().getCloud().toJavaObject(PortGroupsRequest.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryPortGroupsUrl(cloudAccessBean.getScvmmRole()), new String[]{portGroupsRequest.getDVSwitchUri(), portGroupsRequest.getLimit(), portGroupsRequest.getOffset()});
            String json = HttpClientUtil.get(url, request);
            PortGroupsResponse response = JSONObject.parseObject(json,PortGroupsResponse.class);
            Map<Class,List> result = convertSubnet(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取端口组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取端口组失败");
        }
    }
    public static Map<Class,List> convertSubnet(BaseCloudRequest request, PortGroupsResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getPortGroups())) {
            result.put(CmdbSubnetRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (PortGroupsResponse.PortGroupsResponseData portGroup : response.getPortGroups()) {
            CmdbSubnetRes subnet = new CmdbSubnetRes();
            subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), portGroup.getUrn()));
            subnet.setOpen_id(portGroup.getUrn());
            subnet.setOpen_name(portGroup.getName());
            subnet.setType(portGroup.getPortType()==0?"Access":"Trunk");
            subnet.setVlan_id(portGroup.getVlanId()+"");
            portGroup.setDescription(portGroup.getDescription());
            toCiResCloud(request,subnet);
            data.add(subnet);
            //关联DVSwitch
            Association rule = AssociationUtils.toAssociation(subnet, CmdbVswitchRes.class, IdUtils.encryptId(accessBean.getCmpId(), request.getBody().getCloud().getString("DVSwitchUrn")));
            associations.add(rule);
        }
        result.put(CmdbSubnetRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 同步上行链路组
     * @param request
     * @return
     */
    public static BaseResponse fetchUplinkPorts(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            UplinkPortsRequest portsRequest = request.getBody().getCloud().toJavaObject(UplinkPortsRequest.class);
            String siteUri = UrnUtils.formatUrn(request.getBody().getCloud().getString("siteUrn"));
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getUplinkPortsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.post(url, request,JSON.toJSONString(portsRequest));
            UplinkPortsResponse response = JSONObject.parseObject(json,UplinkPortsResponse.class);
            Map<Class,List> result = convertUplinkPorts(request,response);
            return BaseCloudService.fetchSend(request, result);
        }catch (Exception e){
            log.error("获取上行链路组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取上行链路组失败");
        }
    }
    public static Map<Class,List> convertUplinkPorts(BaseCloudRequest request, UplinkPortsResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getUplinkPorts())) {
            result.put(CmdbSubnetRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbUplinkPortRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (UplinkPortsResponse.UplinkPortsResponseData uplinkPorts : response.getUplinkPorts()) {
            CmdbUplinkPortRes uplinkPortRes = new CmdbUplinkPortRes();
            uplinkPortRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), uplinkPorts.getPortUrn()));
            uplinkPortRes.setOpen_id(uplinkPorts.getPortUrn());
            uplinkPortRes.setOpen_name(uplinkPorts.getPortName());
            uplinkPortRes.setStatus(uplinkPorts.getStatus()+"");
            toCiResCloud(request,uplinkPortRes);
            data.add(uplinkPortRes);
            //关联DVSwitch
            Association rule = AssociationUtils.toAssociation(uplinkPortRes, CmdbVswitchRes.class, IdUtils.encryptId(accessBean.getCmpId(), request.getBody().getCloud().getString("DVSwitchUrn")));
            associations.add(rule);
            //关联Host
            Association host = AssociationUtils.toAssociation(uplinkPortRes, CmdbHostRes.class, IdUtils.encryptId(accessBean.getCmpId(), uplinkPorts.getHostUrn()));
            associations.add(host);
        }
        result.put(CmdbUplinkPortRes.class, data);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 同步vlan池
     * @param request
     * @return
     */
    public static BaseResponse fetchVlanPool(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String DVSwitchUri = request.getBody().getCloud().getString("DVSwitchUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryDVSwitchsDetailUrl(cloudAccessBean.getScvmmRole()), new String[]{DVSwitchUri});
            String json = HttpClientUtil.get(url, request);
            VlanPoolResponse response = JSONObject.parseObject(json,VlanPoolResponse.class);
            Map<Class,List> result = convertVlanPool(request,response);
            return BaseCloudService.fetchSend(request, result);
        }catch (Exception e){
            log.error("获取Vlan失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取Vlan组失败");
        }
    }
    public static Map<Class,List> convertVlanPool(BaseCloudRequest request, VlanPoolResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getVlanPoolSet())) {
            result.put(CmdbVlanRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbVlanRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (VlanPoolResponse.VlanPoolResponseData vlanPool : response.getVlanPoolSet()) {
            CmdbVlanRes vlan = new CmdbVlanRes();
            vlan.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), ResourceType.CMDB_VLAN_RES.value(), vlanPool.getEndVlanId()+"",vlanPool.getEndVlanId()+""));
            vlan.setStart_vlan_id(vlanPool.getStartVlanId()+"");
            vlan.setEnd_vlan_id(vlanPool.getEndVlanId()+"");
            toCiResCloud(request,vlan);
            data.add(vlan);
            //关联DVSwitch
            Association rule = AssociationUtils.toAssociation(vlan, CmdbVswitchRes.class, IdUtils.encryptId(accessBean.getCmpId(), request.getBody().getCloud().getString("DVSwitchUrn")));
            associations.add(rule);
        }
        result.put(CmdbVlanRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 同步安全组
     * @param request
     * @return
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            SecurityGroupDescribeRequest groupDescribeRequest = request.getBody().getCloud().toJavaObject(SecurityGroupDescribeRequest.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySecurityGroupsUrl(cloudAccessBean.getScvmmRole()), new String[]{groupDescribeRequest.getSiteUri(), groupDescribeRequest.getLimit(), groupDescribeRequest.getOffset()});
            String json = HttpClientUtil.get(url, request);
            SecurityGroupResponse response = JSONObject.parseObject(json,SecurityGroupResponse.class);
            Map<Class,List> result = convertSecurityGroup(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            BaseCloudService.toGourdResponse(baseResponse, result.get(CmdbSecuritygroupRes.class), (CmdbSecuritygroupRes t) -> {
                JobInfo jobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_COMPUTE_SECURITYGROUP_RULE);
                request.getBody().getCloud().put("securityRuleUri",groupDescribeRequest.getSiteUri()+"/"+t.getOpen_id());
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取安全组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取安全组失败");
        }
    }
    public static Map<Class,List> convertSecurityGroup(BaseCloudRequest request,SecurityGroupResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getSecurityGroups())){
            result.put(CmdbSecuritygroupRes.class,null);
            result.put(Association.class,null);
            return result;
        }
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (SecurityGroupResponse.SecurityGroupResponseData securityGroup : response.getSecurityGroups()) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(),ResourceType.CMDB_SECURITYGROUP_RES.value(), securityGroup.getSgId()));
            ci.setOpen_id(securityGroup.getSgId());
            ci.setOpen_name(securityGroup.getSgName());
            ci.setDesc(securityGroup.getSgDesc());
            //关联虚拟机
            if(CollUtil.isNotEmpty(securityGroup.getVmList())){
                securityGroup.getVmList().forEach(vm ->{
                    Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(),ResourceType.CMDB_INSTANCE_RES.value(), vm.getVmUrn()));
                    associations.add(instance);
                });
            }
            toCiResCloud(request, ci);
            data.add(ci);
        }
        result.put(CmdbSecuritygroupRes.class,data);
        result.put(Association.class,associations);
        return result;
    }

    /**
     * 同步安全组规则
     * @param request
     * @return
     */
    public static BaseResponse fetchSecurityGroupRule(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySecurityGroupRulesUrl(cloudAccessBean.getScvmmRole()), new String[]{map.get("securityRuleUri"), map.get("limit"), map.get("offset")});
            String json = HttpClientUtil.get(url,request);
            SecurityGroupRuleResponse response = JSONObject.parseObject(json,SecurityGroupRuleResponse.class);
            Map<Class,List> result  = convertSecurityRule(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取安全组规则失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取安全组规则失败");
        }
    }
    public static Map<Class,List> convertSecurityRule(BaseCloudRequest request, SecurityGroupRuleResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getSecurityGroupRules())){
            result.put(CmdbSecuritygroupRule.class,null);
            result.put(Association.class,null);
            return result;
        }
        List<CmdbSecuritygroupRule> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        response.getSecurityGroupRules().forEach(res->{
            CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), res.getRulesId()));
            ci.setOpen_id(res.getRulesId());
            ci.setOpen_name(res.getRulesId());
            ci.setDirection(res.getDirection());
            ci.setExtend1(res.getIpProtocol());
            ci.setPort_range(res.getIpRanges());
            ci.setSource_cidr(res.getFromPort());
            ci.setDest_cidr(res.getToPort());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联安全组
            Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(accessBean.getCmpId(), res.getSecurityGroupId()));
            associations.add(rule);
        });
        result.put(CmdbSecuritygroupRule.class,data);
        result.put(Association.class,associations);
        return result;
    }

    /**
     * 同步数据存储
     * @param request
     * @return
     */
    public static BaseResponse fetchDataStorage(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryDataStoresUrl(cloudAccessBean.getScvmmRole()), new String[]{map.get("siteUri"), map.get("limit"), map.get("offset"),map.get("scope")});
            String json = HttpClientUtil.get(url, request);
            DataStoresResponse response = JSONObject.parseObject(json,DataStoresResponse.class);
            Map<Class,List> result = convertDataStore(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取数据存储失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取数据存储失败");
        }
    }
    public static Map<Class,List> convertDataStore(BaseCloudRequest request,DataStoresResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getDatastores())){
            result.put(CmdbStoragePoolRes.class,null);
            result.put(Association.class,null);
            return result;
        }
        List<CmdbStoragePoolRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DataStoresResponse.DataStoresResponseData datastore : response.getDatastores()) {
            CmdbStoragePoolRes ci = new CmdbStoragePoolRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),datastore.getUrn()));
            ci.setOpen_id(datastore.getUrn());
            ci.setOpen_name(datastore.getName());
            ci.setType(datastore.getStorageType());
            ci.setTotal_size(datastore.getActualCapacityGB().floatValue());
            ci.setUsed_size(datastore.getUsedSizeGB().floatValue());
            ci.setAllocation_size(datastore.getUsedSizeGB().floatValue());
            ci.setIs_thin(datastore.getIsThin().toString());
            ci.setStatus(datastore.getStatus().toLowerCase());
            ci.setOpen_status(datastore.getStatus());
            ci.setRefresh_time(TimeUtils.stringToLongTime(datastore.getRefreshTime()));
            toCiResCloud(request,ci);
            data.add(ci);
            List<String> hostUrnList = datastore.getHosts();
            if(CollUtil.isNotEmpty(hostUrnList)){
                for (String hostUrn : hostUrnList) {
                    Association association = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), hostUrn));
                    associations.add(association);
                }
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_STORAGE_POOL_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_RESOURCEPOOL,
                        request.getBody().getCloud().getString("siteUrn"))
                .getData();
        result.put(TmdbResourceSet.class,sets);
        result.put(CmdbStoragePoolRes.class,data);
        result.put(Association.class,associations);
        return result;
    }

    /**
     * 同步存储设备
     * @param request
     * @return
     */
    public static BaseResponse fetchStorageUnit(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryStorageUnitsUrl(cloudAccessBean.getScvmmRole()), new String[]{map.get("siteUri"), map.get("limit"), map.get("offset"),map.get("deviceType")});
            String json = HttpClientUtil.get(url, request);
            StorageUnitResponse response = JSONObject.parseObject(json,StorageUnitResponse.class);
            Map<Class,List> result = convertStoreUnit(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取数据存储失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取数据存储失败");
        }
    }
    public static Map<Class,List> convertStoreUnit(BaseCloudRequest request,StorageUnitResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getStorageUnits())){
            result.put(CmdbStorageUnitRes.class,null);
            result.put(TmdbResourceSet.class,null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        "cmdb_storage_unit_res"
                );
        String siteResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                request.getBody().getCloud().getString("siteUrn"));
        List<CmdbStorageUnitRes> data = new ArrayList<>();
        for (StorageUnitResponse.StorageUnitResponseData storageUnit : response.getStorageUnits()) {
            CmdbStorageUnitRes ci = new CmdbStorageUnitRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),storageUnit.getUrn()));
            ci.setOpen_id(storageUnit.getUrn());
            ci.setOpen_name(storageUnit.getName());
            ci.setStatus(storageUnit.getStatus() == null?"":storageUnit.getStatus().toLowerCase());
            ci.setType(storageUnit.getType());
            ci.setTotal_size(storageUnit.getCapacityGB().floatValue());
            if(storageUnit.getIsThin() != null){
                ci.setIs_thin(storageUnit.getIsThin().toString());
            }
            if(StrUtil.isNotEmpty(storageUnit.getRefreshTime())){
                ci.setRefresh_time(TimeUtils.stringToLongTime(storageUnit.getRefreshTime()));
            }
            ci.setWwn(storageUnit.getWwn());
            ci.setPath(storageUnit.getUri());
            toCiResCloud(request,ci);
            data.add(ci);
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, siteResId);
        }
//        List<TmdbResourceSet> sets = BuilderResourceSet.of()
//                .withInfo(request.getBody().getAccess().getCmpId(),
//                        CloudType.fromValue(request.getPlugin().getRealm())),
//                        ResourceType.CMDB_STORAGE_UNIT_RES
//                ).withDataByDevopsValue(data,
//                        DevopsSide.DEVOPS_RESOURCEPOOL,
//                        request.getBody().getCloud().getString("siteUrn"))
//                .getData();
        result.put(TmdbResourceSet.class,builderResourceSet.getData());
        result.put(CmdbStorageUnitRes.class,data);
        return result;
    }

    /**
     * 获取存储资源
     * @param request
     * @return
     */
    public static BaseResponse fetchStorageResource(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryStorageResourceUrl(cloudAccessBean.getScvmmRole()), new String[]{map.get("siteUri"), map.get("limit"), map.get("offset")});
            String json = HttpClientUtil.get(url, request);
            StorageResourceResponse response = JSONObject.parseObject(json,StorageResourceResponse.class);
            Map<Class,List> result = convertStorageResource(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotal(),
                    request.getBody().getCloud().getInteger("limit"));
        }catch (Exception e){
            log.error("获取告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取告警数据失败");
        }
    }
    public static Map<Class,List> convertStorageResource(BaseCloudRequest request, StorageResourceResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response != null || CollUtil.isEmpty(response.getStoreResInfoList())){
            result.put(CmdbStorageResourceRes.class,null);
            result.put(TmdbResourceSet.class,null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        "cmdb_storage_resource_res"
                );
        String siteResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                request.getBody().getCloud().getString("siteUrn"));
        List<CmdbStorageResourceRes> data = new ArrayList<>();
        for (StorageResourceResponse.StorageResourceResponseData responseData : response.getStoreResInfoList()) {
            CmdbStorageResourceRes ci = new CmdbStorageResourceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),responseData.getUrn()));
            ci.setOpen_id(responseData.getUrn());
            ci.setOpen_name(responseData.getName());
            ci.setType(responseData.getStorageType());
            ci.setManagement_ip(responseData.getManagementIpList().stream().collect(Collectors.joining(",")));
            ci.setManagement_port(responseData.getManagementPort());
            toCiResCloud(request,ci);
            data.add(ci);
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, siteResId);
        }
        result.put(CmdbStorageResourceRes.class,data);
        return result;
    }

    public static BaseResponse fetchFolder(BaseCloudRequest request){
        try {
            CloudAccessBean accessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String parentObjUrn = map.get("parentObjUrn");
            if(StrUtil.isBlank(parentObjUrn)){
                parentObjUrn = map.get("siteUrn");
            }
            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getFoldersUrl(accessBean.getScvmmRole()), new String[]{map.get("siteUri"),parentObjUrn,"1"});
            String json = HttpClientUtil.get(url,request);
            FolderResponse response = JSONObject.parseObject(json,FolderResponse.class);
            Map<Class,List> result = convertFolder(request,response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return BaseCloudService.toGourdResponse(baseResponse, result.get(TmdbDevops.class), (TmdbDevops t) -> {
                JobInfo jobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_PLATFORM_FOLDER);
                request.getBody().getCloud().put("parentObjUrn",t.getDevops_value());
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }catch (Exception e){
            log.error("获取文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取文件夹失败");
        }
    }

//    public static void getAllFolder(BaseCloudRequest request,List<FolderResponse> folderResponseList){
//        try {
//            CloudAccessBean accessBean = request.getBody().getAccess();
//            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
//            String parentObjUrn = map.get("parentObjUrn");
//            if(StrUtil.isBlank(parentObjUrn)){
//                parentObjUrn = map.get("siteUrn");
//            }
//            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getFoldersUrl(accessBean.getScvmmRole()), new String[]{map.get("siteUri"),parentObjUrn,"1"});
//            String json = HttpClientUtil.get(url,request);
//            FolderResponse response = JSONObject.parseObject(json,FolderResponse.class);
//            if(response != null && CollUtil.isNotEmpty(response.getFolders())){
//                folderResponseList.add(response);
//                request.getBody().getCloud().put("parentObjUrn")
//                getAllFolder(request,folderResponseList);
//            }
//        }catch (Exception e){
//            log.error("获取文件夹失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取文件夹失败");
//        }
//    }

    public static Map<Class,List> convertFolder(BaseCloudRequest request, FolderResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getFolders())){
            result.put(TmdbDevops.class,null);
            result.put(TmdbDevopsLink.class,null);
            return result;
        }
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (FolderResponse.FolderResponseData folder : response.getFolders()) {
            TmdbDevops devops = new TmdbDevops();
            devops.setCloud_type(accessBean.getCloudType());
            devops.setDevops_name(folder.getName());
            devops.setDevops_value(folder.getUrn());
            devops.setAccount_id(accessBean.getCmpId());
            devops.setDict_code(DevopsSide.DEVOPS_VM_FOLDER.value());
            devops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(), DevopsSide.DEVOPS_VM_FOLDER.value(), folder.getUrn()));
            devops.setInfo_json(JSONObject.toJSONString(folder));
            devopsList.add(devops);

            TmdbDevopsLink link = new TmdbDevopsLink();
            link.setDevops_id(devops.getBiz_id());
//            link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(),DevopsSide.DEVOPS_VM_FOLDER.value(), folder.getParentObjUrn()));
            if(folder.getParentObjUrn().equals(request.getBody().getCloud().getString("siteUrn"))){
                link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(),DevopsSide.DEVOPS_RESOURCEPOOL.value(), folder.getParentObjUrn()));
            }else{
                link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(),accessBean.getCloudType(),DevopsSide.DEVOPS_VM_FOLDER.value(), folder.getParentObjUrn()));
            }
            link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
            links.add(link);
        }
        result.put(TmdbDevops.class,devopsList);
        result.put(TmdbDevopsLink.class,links);
        return result;
    }

    /**
     * 同步操作系统
     * @param request
     * @return
     */
    public static BaseResponse fetchOsVersion(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String siteUrn = map.get("siteUrn");
            String siteId = StringUtils.substringAfterLast(siteUrn, ":");
            String siteUri = UrnUtils.formatUrn(siteUrn);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            if(StrUtil.isNotEmpty(map.get("hostUrn"))){
                url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrlByHost(cloudAccessBean.getScvmmRole()), new String[]{siteUri,siteId,map.get("arch"),map.get("cpuVendor"),map.get("osVendor")});
            }
            String json = HttpClientUtil.get(url,request);
            OsInfoResponse osInfoResponse =  JSONObject.parseObject(json,OsInfoResponse.class);
            Map<Class,List> result = convertOsVersion(request,osInfoResponse);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return baseResponse;
        }catch (Exception e){
            log.error("获取操作系统失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取操作系统失败");
        }
    }

    public static BaseResponse fetchOsVersionInit(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.get(url,request);
            OsInfoResponse osInfoResponse =  JSONObject.parseObject(json,OsInfoResponse.class);
            Map<Class,List> result = convertOsVersion(request,osInfoResponse);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request,result);
            return baseResponse;
        }catch (Exception e){
            log.error("获取操作系统失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取操作系统失败");
        }
    }
    public static Map<Class,List> convertOsVersion(BaseCloudRequest request, OsInfoResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null){
            result.put(CmdbOsRes.class,null);
            return result;
        }
        String hostUrn = request.getBody().getCloud().getString("hostUrn");
        List<CmdbOsRes> osList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        if(CollUtil.isNotEmpty(response.getLinux())){
            for (OsInfoResponse.OsInfoResponseData linux : response.getLinux()) {
                CmdbOsRes ci = new CmdbOsRes();
                ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),linux.getOsType().toLowerCase(),linux.getId()+""));
                ci.setOpen_id(linux.getId()+"");
                ci.setOpen_name(linux.getVersionDes());
                ci.setType(linux.getOsType());
                ci.setVersion(linux.getVersionDes());
                ci.setFull_name(linux.getVersionDes());
                ci.setName(linux.getVersionDes());
                toCiResCloud(request, ci);
                osList.add(ci);
                if(StrUtil.isNotEmpty(hostUrn)){
                    //与物理机关联
                    Association association = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), hostUrn));
                    associations.add(association);
                }
            }
        }
        if(CollUtil.isNotEmpty(response.getWindows())){
            for (OsInfoResponse.OsInfoResponseData windows : response.getWindows()) {
                CmdbOsRes ci = new CmdbOsRes();
                ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),windows.getOsType().toLowerCase(),windows.getId()+""));
                ci.setOpen_id(windows.getId()+"");
                ci.setOpen_name(windows.getVersionDes());
                ci.setType(windows.getOsType());
                ci.setVersion(windows.getVersionDes());
                ci.setFull_name(windows.getVersionDes());
                ci.setName(windows.getVersionDes());
                toCiResCloud(request, ci);
                osList.add(ci);
                if(StrUtil.isNotEmpty(hostUrn)){
                    //与物理机关联
                    Association association = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), hostUrn));
                    associations.add(association);
                }
            }
        }
        if(CollUtil.isNotEmpty(response.getOther())){
            for (OsInfoResponse.OsInfoResponseData other : response.getOther()) {
                CmdbOsRes ci = new CmdbOsRes();
                ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),other.getOsType().toLowerCase(),other.getId()+""));
                ci.setOpen_id(other.getId()+"");
                ci.setOpen_name(other.getVersionDes());
                ci.setType(other.getOsType());
                ci.setVersion(other.getVersionDes());
                ci.setFull_name(other.getVersionDes());
                ci.setName(other.getVersionDes());
                toCiResCloud(request, ci);
                osList.add(ci);
                if(StrUtil.isNotEmpty(hostUrn)){
                    //与物理机关联
                    Association association = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), hostUrn));
                    associations.add(association);
                }
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_OS_RES
                ).withDataByDevopsValue(osList,
                        DevopsSide.DEVOPS_RESOURCEPOOL,
                        request.getBody().getCloud().getString("siteUrn"))
                .getData();
        result.put(TmdbResourceSet.class,sets);
        result.put(CmdbOsRes.class,osList);
        result.put(Association.class,associations);
        return result;
   }


    /**
     * 查询活动告警
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            AlarmRequest alarmRequest = request.getBody().getCloud().toJavaObject(AlarmRequest.class);
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryAlarmsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.post(url, request, JSON.toJSONString(alarmRequest));
            AlarmsResponse response = JSONObject.parseObject(json,AlarmsResponse.class);
            if (null == response || CollUtil.isEmpty(response.getItems())) {
                return BaseResponse.SUCCESS.of("查询告警信息为空!");
            }
            List<AlarmInfoBean> listCI = response.getItems().stream().map(t -> convertAlarm(request, t)).collect(Collectors.toList());
            BaseCloudService.toAetMessageAndSend(listCI, "alarm");
            String message = StrUtil.format("本次获取告警信息,页码：{},条数：{},本次获取条数：{}", alarmRequest.getOffset()/100+1, alarmRequest.getLimit(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == alarmRequest.getLimit().intValue()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("offset", (alarmRequest.getOffset()/100+1) * 100);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("获取告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取告警数据失败");
        }
    }
    public static AlarmInfoBean convertAlarm(BaseCloudRequest request, AlarmsResponse.AlarmResponseData alert) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        AlarmInfoBean alarm = new AlarmInfoBean();
        alarm.setId(IdUtils.encryptId(accessBean.getCmpId(), alert.getSvAlarmID(), alert.getDtOccurTime()));
        alarm.setAccountId(accessBean.getCmpId());
        alarm.setCloudType(accessBean.getCloudType());
        alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), alert.getSvAlarmID()));
        alarm.setOpenId(alert.getSvAlarmID());
        alarm.setOpenName(alert.getSvAlarmName());
        alarm.setOpenLevel(alert.getIAlarmLevel());
        alarm.setAlarmId(alert.getSvAlarmID());
        alarm.setAlarmName(alert.getSvAlarmName());
        alarm.setDetail(alert.getSvAlarmCause());
        alarm.setClosedStatus(false);
        alarm.setJsonInfo(JSON.toJSONString(alert));
        alarm.setCount(1);
        if (StrUtil.isNotEmpty(alert.getDtOccurTime())) {
            alarm.setFirstTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(alert.getDtOccurTime()))));
            alarm.setCreateTime(alarm.getFirstTime());
        }
        if ("vms".equalsIgnoreCase(alert.getSvMoc())) {
            alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        } else if("hosts".equalsIgnoreCase(alert.getSvMoc())){
            alarm.setResourceType(ResourceType.CMDB_HOST_RES.value());
        }
        switch (alert.getIAlarmLevel()) {
            case "1":
                alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                break;
            case "2":
                alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                break;
            case "3":
                alarm.setAlarmLevel(AlarmLevel.MINOR.value());
                break;
            case "4":
                alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                alarm.setClosedStatus(true);
                break;
        }
        return alarm;
    }

    /**
     * 同步事件
     * @param request
     * @return
     */
    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            EventRequest eventRequest = request.getBody().getCloud().toJavaObject(EventRequest.class);
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryEventsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.post(url, request, JSON.toJSONString(eventRequest));
            EventsResponse response = JSONObject.parseObject(json,EventsResponse.class);
            if(response == null || CollUtil.isEmpty(response.getItems())){
                return BaseResponse.SUCCESS.of("查询事件信息为空!");
            }
            List<EventInfoBean> listCI = response.getItems().stream().map(t -> convertEvent(request, t)).collect(Collectors.toList());
            listCI.removeIf(Objects::isNull);
            BaseCloudService.toAetMessageAndSend(listCI, "event");

            String message = StrUtil.format("本次获取事件信息,页码：{},条数：{},本次获取条数：{}", eventRequest.getOffset()/100+1, eventRequest.getLimit(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == eventRequest.getLimit().intValue()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("offset", (eventRequest.getOffset()/100+1) * 100);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("获取事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取事件数据失败");
        }
    }
    public static EventInfoBean convertEvent(BaseCloudRequest request, EventsResponse.EventsResponseData event) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        EventInfoBean bean = new EventInfoBean();
        bean.setId(IdUtils.encryptId(accessBean.getCmpId(), event.getEventID(), event.getEventName(), event.getOccurTime()));
        bean.setAccountId(accessBean.getCmpId());
        bean.setCloudType(accessBean.getCloudType());
        bean.setResId(IdUtils.encryptId(accessBean.getCmpId(), event.getEventID()));
        bean.setOpenId(event.getEventID());
        bean.setOpenName(event.getEventName());
        bean.setEventType(event.getObjectType());
        bean.setEventName(event.getEventName());
        bean.setDetail(event.getIsParse());
        if (event.getOccurTime() != null) {
            bean.setBeginTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(event.getOccurTime()))));
            bean.setEndTime(bean.getBeginTime());
        }
        bean.setJsonInfo(JSON.toJSONString(event));
        return bean;
    }


    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        // 获取当前时间
        Instant now = Instant.now();
        // 计算前5分钟和前35分钟的时间点
        Instant fiveMinutesAgo = now.minus(Duration.ofMinutes(5));
        Instant thirtyFiveMinutesAgo = now.minus(Duration.ofMinutes(35));
        if (!request.getBody().getCloud().containsKey("interval")) {
            request.getBody().getCloud().put("interval", "300");
        }
        if (!request.getBody().getCloud().containsKey("startTime")) {
            long startTime = thirtyFiveMinutesAgo.getEpochSecond();;
            request.getBody().getCloud().put("startTime", Long.toString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", fiveMinutesAgo.getEpochSecond() + "");
        }
        return true;
    }

    public static boolean defaultEcsMetricNames(BaseCloudRequest request) {
        if (!request.getBody().containsKey("MetricNames")) {
            request.getBody().put("MetricNames", Converts.instanceMetrics.keySet());
        }
        return true;
    }

    public static boolean defaultHostMetricNames(BaseCloudRequest request) {
        if (!request.getBody().containsKey("MetricNames")) {
            request.getBody().put("MetricNames", Converts.hostMetrics.keySet());
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 1000);
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 1000, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 1000);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);
        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        return true;
    }


    /**
     * 云主机监控
     * @param request
     * @return
     */
    public static BaseResponse fetchInstancePerf(BaseCloudRequest request){
        try {
            //监控请求指标集合
            Collection<String> metricNames = request.getBody().getJSONArray("MetricNames").toJavaList(String.class);
            Map<String, ResInstanceDiskApiModel> instanceMap = request.getBody().getJSONObject("instanceMap").toJavaObject(Map.class);
            MetricRequest metricRequest = request.getBody().getCloud().toJavaObject(MetricRequest.class);
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryObjectMetricCurveDataUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            List<MetricCurveDataResponse> responseList = new ArrayList<>();
            MetricRequestParam param = null;
            for (String urn : instanceMap.keySet()) {
                List<MetricRequestParam> list = new ArrayList<>();
                metricRequest.setUrn(urn);
                for (String metric : metricNames) {
                    metricRequest.setMetricId(metric);
                    param = new MetricRequestParam();
                    BeanUtils.copyProperties(metricRequest,param);
                    list.add(param);
                }
                String json = HttpClientUtil.post(url,request, JSONObject.toJSONString(list));
                MetricCurveDataResponse response = JSONObject.parseObject(json, MetricCurveDataResponse.class);
                if(response != null && CollUtil.isNotEmpty(response.getItems())){
                    responseList.add(response);
                }
            }
            Map<String, PerfInfoBean> perfMap = convertEcsPerf(request,responseList, instanceMap);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("获取云主机监控数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机监控数据失败");
        }
    }
    public static Map<String, PerfInfoBean> convertEcsPerf(BaseCloudRequest request, List<MetricCurveDataResponse> response, Map<String, ResInstanceDiskApiModel> instanceMap) {
        if(CollUtil.isEmpty(response)){
            return null;
        }
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (MetricCurveDataResponse curvedataResponse : response) {
            for (MetricCurveDataResponse.CurveData curvedata : curvedataResponse.getItems()) {
                if(CollUtil.isNotEmpty(curvedata.getMetricValue())){
                    String instanceId = curvedata.getUrn();;
                    String metricName = curvedata.getMetricId();
                    MetricCurveDataResponse.MetricValue metricValue = curvedata.getMetricValue().stream().collect(Collectors.collectingAndThen(Collectors.reducing((o1, o2) ->
                            o1.getTime().compareTo(o2.getTime()) > 0 ? o1 : o2), Optional::get));
                    Double average = Double.parseDouble(metricValue.getValue());
                    Long timestamp = Long.parseLong(metricValue.getTime());
                    String id = instanceId + "_" + timestamp;
                    PerfInfoBean perf = perfMap.get(id);
                    if (perf == null) {
                        //生成指标对象
                        ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                        perf = new PerfInfoBean();//指标对应得资源CI信息
                        perf.setAccountId(instanceDisk.getAccount_id());
                        perf.setCloudType(instanceDisk.getCloud_type());
                        perf.setResId(instanceDisk.getRes_id());
                        perf.setOpenId(instanceDisk.getOpen_id());
                        perf.setOpenName(instanceDisk.getOpen_name());
                        perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                        perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                        perf.setCreateTime(DateUtil.formatDateTime(DateUtil.date(timestamp*1000)));
                        perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                        if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                            Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                            perf.setDiskSize(sum);
                        }
                        perf.setId(id);
                    }
                    BiConsumer<PerfInfoBean, Double> setValue = Converts.instancePerfMapping.get(metricName);
                    setValue.accept(perf, average);//设置监控指标值
                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }


    public static boolean defaultHostMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 500);
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> result = ApiFactory.Api.res.listHostStoragePool(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 500, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 500);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);
        Map<String, ResHostStoragePoolApiModel> hostMap = CollStreamUtil.toMap(result.getData().getList(), ResHostStoragePoolApiModel::getOpen_id, t -> t);
        request.getBody().put("hostMap", hostMap);
        return true;
    }
    public static BaseResponse fetchHostPerf(BaseCloudRequest request){
        try {
            //监控请求指标集合
            Collection<String> metricNames = request.getBody().getJSONArray("MetricNames").toJavaList(String.class);
            Map<String, ResHostStoragePoolApiModel> hostMap = request.getBody().getJSONObject("hostMap").toJavaObject(Map.class);
            MetricRequest metricRequest = request.getBody().getCloud().toJavaObject(MetricRequest.class);
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String siteUri = request.getBody().getCloud().getString("siteUri");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryObjectMetricCurveDataUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            List<MetricCurveDataResponse> responseList = new ArrayList<>();
            MetricRequestParam param = null;
            for (String urn : hostMap.keySet()) {
                List<MetricRequestParam> list = new ArrayList<>();
                metricRequest.setUrn(urn);
                for (String metric : metricNames) {
                    metricRequest.setMetricId(metric);
                    param = new MetricRequestParam();
                    BeanUtils.copyProperties(metricRequest,param);
                    list.add(param);
                }
                String json = HttpClientUtil.post(url,request, JSONObject.toJSONString(list));
                MetricCurveDataResponse response = JSONObject.parseObject(json, MetricCurveDataResponse.class);
                if(response != null && CollUtil.isNotEmpty(response.getItems())){
                    responseList.add(response);
                }
            }
            Map<String, PerfInfoBean> perfMap = convertHostPerf(request,responseList, hostMap);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("获取物理机监控数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取物理机监控数据失败");
        }
    }
    public static Map<String, PerfInfoBean> convertHostPerf(BaseCloudRequest request, List<MetricCurveDataResponse> response, Map<String, ResHostStoragePoolApiModel> hostMap) {
        if(CollUtil.isEmpty(response)){
            return null;
        }
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (MetricCurveDataResponse curvedataResponse : response) {
            for (MetricCurveDataResponse.CurveData curvedata : curvedataResponse.getItems()) {
                if(CollUtil.isNotEmpty(curvedata.getMetricValue())){
                    String instanceId = curvedata.getUrn();;
                    String metricName = curvedata.getMetricId();
//                    MetricCurveDataResponse.MetricValue metricValue = curvedata.getMetricValue().stream().collect(Collectors.collectingAndThen(Collectors.reducing((o1, o2) ->
//                            o1.getTime().compareTo(o2.getTime()) > 0 ? o1 : o2), Optional::get));
                    List<MetricCurveDataResponse.MetricValue> metricValueList = curvedata.getMetricValue().stream()
                            .sorted((o1, o2) -> o2.getTime().compareTo(o1.getTime())) // 按时间降序排序
                            .limit(3) // 取前三条
                            .collect(Collectors.toList());
                    for (MetricCurveDataResponse.MetricValue metricValue : metricValueList) {
                        Double average = Double.parseDouble(metricValue.getValue());
                        Long timestamp = Long.parseLong(metricValue.getTime());
                        String id = instanceId + "_" + timestamp;
                        PerfInfoBean perf = perfMap.get(id);
                        if (perf == null) {
                            //生成指标对象
                            ResHostStoragePoolApiModel instanceDisk = hostMap.get(instanceId);
                            perf = new PerfInfoBean();
                            perf.setAccountId(instanceDisk.getAccount_id());
                            perf.setCloudType(instanceDisk.getCloud_type());
                            perf.setResId(instanceDisk.getRes_id());
                            perf.setOpenId(instanceDisk.getOpen_id());
                            perf.setOpenName(instanceDisk.getOpen_name());
                            perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                            perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                            perf.setCreateTime(DateUtil.formatDateTime(DateUtil.date(timestamp*1000)));
                            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                            perf.setDiskSize(instanceDisk.getTotal_size().doubleValue());
                            perf.setId(id);
                        }
                        BiConsumer<PerfInfoBean, Double> setValue = Converts.hostPerfMapping.get(metricName);
                        setValue.accept(perf, average);//设置监控指标值
                        perfMap.put(perf.getId(), perf);
                    }
                }
            }
        }
        request.getBody().remove("hostMap");
        return perfMap;
    }

    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("offset", (t-1)*100);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }
}

