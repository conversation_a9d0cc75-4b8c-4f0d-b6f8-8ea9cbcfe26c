package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm;

import lombok.Data;

@Data
public class VncAccessInfo {

    /**
     * 虚拟机所在主机IP地址，null为非法值，其他为合法值，仅在查询时有效。
     */
    private String hostIp;

    /**
     * 虚拟机VNC端口，-1为非法值，其他为合法值，仅在查询时有效。
     */
    private Integer vncPort;

    /**
     * 虚拟机VNC密码，当修改VNC密码时，需要满足以下要求：<br>
     * 1）长度为8位<br>
     * 2）至少包含如下两种：数字、大写字母、小写字母<br>
     * 3）至少包含如下特殊符号之一：~!@#$%^_+/.,;{}[]|":?
     */
    private String vncPassword;

    /**
     * 当前虚拟机VNC密码，修改VNC密码时有效。
     */
    private String vncOldPassword;

    private String vncUrl;
}
