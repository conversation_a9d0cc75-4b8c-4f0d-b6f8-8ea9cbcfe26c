package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm;

import lombok.Data;

@Data
public class SnapshotCreateRequest {

    /**
     * 【必选】虚拟机快照的名称，[1,256]。
     */
    private String name;

    /**
     * 【可选】虚拟机快照的描述，[0,1024]。
     */
    private String description;

    /**
     * 【可选】快照类型，枚举如下：普通快照：normal，备份点快照：backup，CBT备份：CBTbackup，默认为normal。
     */
    private String type;

    /**
     * 【可选】是否制作内存快照，默认false。
     */
    private Boolean needMemoryShot;

    /**
     * 【可选】默认false，是否一致性快照，预留参数(不建议填写)，携带参数表明该快照会保证虚拟机数据一致性，
     * 在虚拟机运行时会执行VSS功能，将缓存数据刷到磁盘上，当选择内存快照或虚拟机关机时，该参数不起作用。
     */
    private Boolean isConsistent;
}
