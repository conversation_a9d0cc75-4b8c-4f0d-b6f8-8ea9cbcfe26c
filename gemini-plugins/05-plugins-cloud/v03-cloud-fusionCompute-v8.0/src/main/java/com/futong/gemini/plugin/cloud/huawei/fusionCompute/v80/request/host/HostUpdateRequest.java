package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.host;

import lombok.Data;

@Data
public class HostUpdateRequest {
    /**
     * 【可选】主机名称，长度[0, 256]。
     */
    private String name;

    /**
     * 【可选】描述 ， 长度[0,1024]。
     */
    private String description;

    /**
     * 【可选】多路径类型属性：HW（华为），CURRENCY（通用）。
     */
    private String multiPathMode;

    /**
     * 【可选】bmc的IP地址。
     */
    private String bmcIp;

    /**
     * 【可选】bmc帐号，不支持以下字符：&|;<>-/$。
     */
    private String bmcUserName;

    /**
     * 【可选】bmc密码，不支持以下字符：&|;<>-/$。
     */
    private String bmcPassword;

    /**
     * 【可选】NTP服务器IP地址或域名1，长度为1~128个字符，IP地址或域名的有效性由调用者保证。
     */
    private String ntpIp1;

    /**
     * 【可选】NTP服务器IP地址或域名2，长度为0~128个字符，如果有ntpIp2（NTP服务器IP地址2）参数时，必须有ntpIp1 （NTP服务器IP地址1），IP地址或域名的有效性由调用者保证。
     */
    private String ntpIp2;

    /**
     * 【可选】NTP服务器第三备选IP地址或域名3，长度为0~128个字符，如果有ntpIp3（NTP服务器IP地址3）参数时，必须有ntpIp1 （NTP服务器选IP地址1），IP地址或域名的有效性由调用者保证。
     */
    private String ntpIp3;

    /**
     * 【可选】ntp同步周期，单位秒，取值为：16/32/64/128/256/512/1024/2048/4096。
     */
    private Integer ntpCycle;

    /**
     * 【可选】是否强制时间同步，0：不强制同步时间；1：强制同步时间。
     */
    private Integer isForceSynTime;

    /**
     * 【可选】GPU共享虚拟机数量，0默认GPU不共享，1-20代表GPU 共享虚拟机数量，其他非法。
     */
    private Integer gpuCapacity;

    /**
     * 【可选】图形桌面虚拟机用于发送的共享内存大小，单位为MB,必须为2的幂次方，取值范围为16~128，即取值只有16，32，64，128这四种。
     */
    private Integer gdvmMemory;

    /**
     * 【可选】图形桌面虚拟机用于接收的共享内存大小，单位为MB,,必须为2的幂次方，取值范围为16~128，即取值只有16，32，64，128这四种。
     */
    private Integer gsvmMemory;

    /**
     * OS主机名称，R5C10版本新增。
     */
    private String hostRealName;
}
