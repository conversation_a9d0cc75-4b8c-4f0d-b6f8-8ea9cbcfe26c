package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.cluster;

import lombok.Data;

import java.util.List;

@Data
public class DRSSetting {

    /**
     * drs自动化级别，可选： 1: 手动，系统给出虚拟机迁移建议； 3：自动，自动迁移虚拟机，默认自动。
     */
    private Integer drsLevel;

    /**
     * DRS分时阈值。
     */
    private List<FragmentLimen> drsFragmentLimen;

    /**
     * DRS调度周期设置。
     */
    private DrsCycle drsCycle;

    /**
     * drs规则，创建集群时不携带。
     */
    private List<DRSRule> drsRules;

    /**
     * 电源管理自动化级别，可选，（0：禁用，1：手动， 2：自动），默认禁用。
     */
    private Integer powerLevel;

    /**
     * 预留，暂未使用。
     */
    private Integer powerLimen;

    /**
     * Dpm分时阈值。
     */
    private List<FragmentLimen> powerFragmentLimen;

    /**
     * DPM调度周期设置。
     */
    private DrsCycle dpmCycle;

    /**
     * Drs策略考虑的因素：1:cpu，2:内存，3:cpu和内存。
     */
    private Integer factor;

    /**
     * Drs触发阈值设置，预留(不建议填写)，无实际意义。
     */
    private DpmThreshold drsThreshold;

    /**
     * Dpm阈值设置。
     */
    private List<DpmThresholds> dpmThresholds;

    /**
     * 电源管理策(预留字段)：1：下电，3：降频， 可选，默认1。
     */
    private Integer electricStrategy;
}
