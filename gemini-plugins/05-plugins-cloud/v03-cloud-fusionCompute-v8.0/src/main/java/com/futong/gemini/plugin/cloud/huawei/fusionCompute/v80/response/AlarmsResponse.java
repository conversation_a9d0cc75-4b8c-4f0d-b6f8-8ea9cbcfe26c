package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class AlarmsResponse {

    /**
     * 总告警条数
     */
    private Integer total;

    /**
     * 当前页告警条数
     */
    private Integer itemSize;

    /**
     * 告警列表
     */
    private List<AlarmResponseData> items;

    /**
     * 更新
     */
    private Integer updateFlag;

    /**
     * 当前页数
     */
    private Integer pageno;

    /**
     * 视图ID
     */
    private Integer viewId;

    @Data
    public static class AlarmResponseData {
        /**
         * 告警流水号。
         */
        private Integer iSerialNo;

        /**
         * 告警ID。
         */
        private String svAlarmID;

        /**
         * 告警对象类别：<br>
         * clusters：集群<br>
         * DATASTORE：数据存储<br>
         * hosts：主机<br>
         * sites：站点<br>
         * vms：虚拟机<br>
         * vrms：VRM节点。
         */
        private String objectType;

        /**
         * 对象标识。
         */
        private String objectUrn;

        /**
         * URN别名。
         */
        private String urnByName;

        /**
         * 告警名称。
         */
        private String svAlarmName;

        /**
         * 告警类别。
         */
        private String iAlarmCategory;

        /**
         * 告警级别。
         */
        private String iAlarmLevel;

        /**
         * 清除类型。
         */
        private String iClearType;

        /**
         * 告警产生时间（long型UTC字符串）。
         */
        private String dtOccurTime;

        /**
         * 告警更新时间（long型UTC字符串）。
         */
        private String dtUpdateTime;

        /**
         * 告警清除时间（long型UTC字符串）。
         */
        private String dtClearTime;

        /**
         * 告警手工清除用户标识。
         */
        private String svClearAlarmUserName;

        /**
         * 附加信息。
         */
        private String svAdditionalInfo;

        /**
         * 告警产生原因。
         */
        private String svAlarmCause;

        /**
         * 事件类型。
         */
        private String svEventType;

        /**
         * 是否可自动清除。
         */
        private String iAutoClear;

        /**
         * 影响业务标志。
         */
        private String iAffectOpFlag;

        /**
         * C10SPC700新增，预留字段。
         */
        private String dtArrivedTime;

        /**
         * C10SPC700新增，预留字段。
         */
        private String svLocationInfo;

        /**
         * C10SPC700新增，预留字段。
         */
        private String svMoc;

        /**
         * C10SPC700新增，预留字段。
         */
        private String iDisplay;

        /**
         * C10SPC700新增，预留字段。
         */
        private String iParse;

        /**
         * C10SPC700新增，预留字段。
         */
        private String iSyncNo;
    }
}
