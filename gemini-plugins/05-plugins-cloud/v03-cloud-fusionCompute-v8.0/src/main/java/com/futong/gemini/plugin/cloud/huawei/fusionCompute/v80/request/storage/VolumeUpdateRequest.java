package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.storage;

import lombok.Data;

@Data
public class VolumeUpdateRequest {

    /**
     * 【可选】卷名称，长度[1,256]。
     */
    private String name;

    /**
     * 【可选】是否为独立磁盘，不携带则不修改<br>
     * 注：块存储只支持独立磁盘(块存储会把此字段修改成默认值：true)。
     */
    private Boolean indepDisk;

    /**
     * 【可选】是否持久化磁盘,不携带则不修改<br>
     */
    private Boolean persistentDisk;

    /**
     * 【可选】卷类型：共享卷share，只修改该属性、且是从普通改为共享，磁盘将变为独立、持久化磁盘。
     */
    private String type;
}
