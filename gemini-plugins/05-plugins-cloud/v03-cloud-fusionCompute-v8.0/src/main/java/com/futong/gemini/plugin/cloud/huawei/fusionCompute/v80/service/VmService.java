package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.vm.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class VmService {

    public static BaseResponse queryOsInfo(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrl(cloudAccessBean.getScvmmRole()), new String[]{"siteUri"});
            String json =HttpClientUtil.get(url,request);
            JSONObject obj = JSON.parseObject(json);
            List<OsInfoResponse> osInfoResponseList = new ArrayList<>();
            if(obj!=null){
                JSONArray linux = obj.getJSONArray("linux");
                if(linux!=null&&linux.size()>0){
                    List<OsInfoResponse> list = linux.toJavaList(OsInfoResponse.class);
                    osInfoResponseList.addAll(list);
                }
                JSONArray window = obj.getJSONArray("windows");
                if(window!=null&&window.size()>0){
                    List<OsInfoResponse> list = window.toJavaList(OsInfoResponse.class);
                    osInfoResponseList.addAll(list);
                }
                JSONArray other = obj.getJSONArray("other");
                if(other!=null&&other.size()>0){
                    List<OsInfoResponse> list = other.toJavaList(OsInfoResponse.class);
                    osInfoResponseList.addAll(list);
                }
            }
            return new BaseDataResponse<>(osInfoResponseList);
        }catch (Exception e){
            log.error("查询所有操作系统失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询所有操作系统失败");
        }
    }

    /**
     * 创建虚拟机
     * @param arguments
     * @return
     */
    public static BaseResponse createVm(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String siteUrn = arguments.getBody().getCloud().getString("siteUrn");
            String vmUrn = arguments.getBody().getCloud().getString("vmUrn");
            String portGroupUrn = arguments.getBody().getCloud().getJSONObject("nicConfig").getString("portGroupUrn");
            List<Nic> nics = new ArrayList<>();
            Nic nic = new Nic();
            nic.setPortGroupUrn(portGroupUrn);
            nics.add(nic);
            String siteUri = UrnUtils.formatUrn(siteUrn);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            if(StrUtil.isNotEmpty(vmUrn)){
                String osTemplateVersion = arguments.getBody().getCloud().getString("osTemplateVersion");
                siteUri = UrnUtils.formatUrn(vmUrn);
                url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmCloneUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
                arguments.getBody().getCloud().getJSONObject("osOptions").put("osVersion",osTemplateVersion);
            }
            VmCloneTempRequest request = arguments.getBody().getCloud().toJavaObject(VmCloneTempRequest.class);
            List<Disk> disks = request.getVmConfig().getDisks();
            for (int i = 0; i < disks.size(); i++) {
                Disk disk = disks.get(i);
                disk.setSequenceNum(i+1);
                disk.setIndepDisk(false);
                disk.setPersistentDisk(true);
                disk.setIsThin(false);
                disk.setPciType("VIRTIO");
                disk.setVolType(1);
                disk.setBootOrder(-1);
            }
            VmConfig vmConfig = request.getVmConfig();
            vmConfig.setDisks(disks);
            vmConfig.setNics(nics);
            Memory memory = request.getVmConfig().getMemory();
            Integer quantityMB = memory.getQuantityMB()*1024;
            memory.setQuantityMB(quantityMB);
            PropertyFilter filter = (obj, fieldName, fieldValue) ->
                    fieldValue != null && !"".equals(fieldValue);
            String requestJSon = JSON.toJSONString(request,filter);
            String json = HttpClientUtil.post(url, arguments, requestJSon);
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建VM失败");
        }
    }

    /**
     * 修改虚拟机
     * @param arguments
     * @return
     */
    public static BaseResponse updateVm(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String vmUri = UrnUtils.formatUrn(arguments.getBody().getCloud().getString("urn"));
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmUpdateUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            VmUpdateRequest request = arguments.getBody().getCloud().toJavaObject(VmUpdateRequest.class);
            String json = HttpClientUtil.put(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("修改VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改VM失败");
        }
    }

    /**
     * 删除VM
     * @param request
     * @return
     */
    public static BaseResponse deleteVm(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            VmDeleteRequest deleteRequest = request.getBody().getCloud().toJavaObject(VmDeleteRequest.class);
            List<VRMTask> vmTasks = new ArrayList<>();
            List<String> urns = request.getBody().getCloud().getObject("urns",List.class);
            for (String urn : urns) {
                String vmUri = UrnUtils.formatUrn(urn);
                String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri,deleteRequest.getIsDeleteShareDisks(),deleteRequest.getIsFormat(), deleteRequest.getHoldTime()});
                String json = HttpClientUtil.delete(url, request);
                VRMTask vrmTask = JSONObject.parseObject(json, VRMTask.class);
                vmTasks.add(vrmTask);
            }
            return new BaseDataResponse<>(vmTasks);
        }catch (Exception e){
            log.error("删除VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除VM失败");
        }
    }


    /**
     * 启动VM
     * @param arguments
     * @return
     */
    public static BaseResponse startVm(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            List<VRMTask> vmTasks = new ArrayList<>();
            List<String> urns = arguments.getBody().getCloud().getObject("urns",List.class);
            for (String urn : urns) {
                String vmUri = UrnUtils.formatUrn(urn);
                String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmStartUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
                String json = HttpClientUtil.post(url, arguments, "{}");
                VRMTask vrmTask = JSONObject.parseObject(json, VRMTask.class);
                vmTasks.add(vrmTask);
            }
            return new BaseDataResponse<>(vmTasks);
        }catch (Exception e){
            log.error("启动VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动VM失败");
        }
    }

    /**
     * 停止VM
     * @param arguments
     * @return
     */
    public static BaseResponse stopVm(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            VmOperateRequest request = arguments.getBody().getCloud().toJavaObject(VmOperateRequest.class);
            List<VRMTask> vmTasks = new ArrayList<>();
            List<String> urns = arguments.getBody().getCloud().getObject("urns",List.class);
            for (String urn : urns) {
                String vmUri = UrnUtils.formatUrn(urn);
                String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmStopUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
                String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
                VRMTask vrmTask = JSONObject.parseObject(json, VRMTask.class);
                vmTasks.add(vrmTask);
            }
            return new BaseDataResponse<>(vmTasks);
        }catch (Exception e){
            log.error("停止VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "停止VM失败");
        }
    }

    /**
     * 重启VM
     * @param arguments
     * @return
     */
    public static BaseResponse rebootVm(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            VmOperateRequest request = arguments.getBody().getCloud().toJavaObject(VmOperateRequest.class);
            List<VRMTask> vmTasks = new ArrayList<>();
            List<String> urns = arguments.getBody().getCloud().getObject("urns",List.class);
            for (String urn : urns) {
                String vmUri = UrnUtils.formatUrn(urn);
                String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmRebootUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
                String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
                VRMTask vrmTask = JSONObject.parseObject(json, VRMTask.class);
                vmTasks.add(vrmTask);
            }
            return new BaseDataResponse<>(vmTasks);
        }catch (Exception e){
            log.error("停止VM失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重启VM失败");
        }
    }

    /**
     * 获取云主机的VNC远程登录地址
     * @param request
     * @return
     */
    public static BaseResponse describeInstanceVncUrl(BaseCloudRequest request){
        try {
            CloudAccessBean accessBean = request.getBody().getAccess();
            String ip = accessBean.getServerIp();
            String uri = request.getBody().getCloud().getString("uri");
            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getQueryVmDetailUrl(accessBean.getScvmmRole()), new String[]{uri});
            String json = HttpClientUtil.get(url, request);
            VmResponse.VmResponseData data = JSONObject.parseObject(json, VmResponse.VmResponseData.class);
            String siteId = StringUtils.substringBetween(data.getUrn(), ":sites:", ":vms:");
            String vmId = StringUtils.substringAfterLast(data.getUrn(), ":");
            VncAccessInfo vncAccessInfo = data.getVncAcessInfo();
            String arch = data.getArch();
            String osType = data.getOsOptions().getOsType();
            if(data != null && vncAccessInfo != null){
                String vncUrl = "https://"+ip+":8443/vnc/index.html#/client/c/"+vmId+"/params?siteId="+siteId+"&vmId="+vmId+"&language=zh_CN&keyLanguage=en&_ajaxTimeOut=12000&osType="+osType+"&arch="+arch+"&maxLength=2000";
                return new BaseDataResponse<>(vncUrl);
            }else{
                throw new BaseException(BaseResponse.FAIL, "获取云上vnc地址信息为空!");
            }
        }catch (Exception e) {
            log.info("获取云上vnc地址信息为空");
            throw new BaseException(BaseResponse.FAIL, "获取云上vnc地址信息为空!");
        }
    }

    /**
     * 获取文件夹
     * @param request
     * @return
     */
    public static BaseResponse describeFolders(BaseCloudRequest request){
        try {
            CloudAccessBean accessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getFoldersUrl(accessBean.getScvmmRole()), new String[]{map.get("parentObjUrn"),"1"});
            String json = HttpClientUtil.get(url,request);
            FolderResponse response = JSONObject.parseObject(json,FolderResponse.class);
            if(response != null && CollUtil.isNotEmpty(response.getFolders())){
                return new BaseDataResponse<>(response.getFolders());
            }
            return new BaseDataResponse<>();
        }catch (Exception e){
            log.info("获取文件夹为空");
            throw new BaseException(BaseResponse.FAIL, "获取文件夹为空!");
        }
    }

    /**
     * 查询云主机密码
     * @param request
     * @return
     */
    public static BaseResponse getEcsPassword(BaseCloudRequest request) {
        try {
            String password = "";
            CloudAccessBean accessBean = request.getBody().getAccess();
            String uri = request.getBody().getCloud().getString("uri");
            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getQueryVmDetailUrl(accessBean.getScvmmRole()), new String[]{uri});
            String json = HttpClientUtil.get(url, request);
            VmResponse.VmResponseData data = JSONObject.parseObject(json, VmResponse.VmResponseData.class);
            if(data != null && data.getVncAcessInfo() != null){
                password = data.getVncAcessInfo().getVncPassword();
            }
            return new BaseDataResponse<>(password);
        }catch (Exception e){
            log.error("查询云主机密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询云主机密码失败");
        }
    }

    /**
     * 重置VNC密码
     * @param request
     * @return
     */
    public static BaseResponse resetEcsPassword(BaseCloudRequest request) {
        try {
            CloudAccessBean accessBean = request.getBody().getAccess();
            String uri = request.getBody().getCloud().getString("uri");
            VncResetPasswordRequest passwordRequest = request.getBody().getCloud().toJavaObject(VncResetPasswordRequest.class);
            String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getVmResetVncUrl(accessBean.getScvmmRole()), new String[]{uri});
            String json = HttpClientUtil.post(url, request,JSON.toJSONString(passwordRequest));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("重置云主机密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重置云主机密码失败");
        }
    }

    /**
     * 获取虚拟机数量
     * @param request
     * @return
     */
    public static BaseResponse queryInstanceTotal(BaseCloudRequest request) {
        try {
            JSONObject data = new JSONObject();
            int count = 0;
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySitesUrl(cloudAccessBean.getScvmmRole()), null);
            String json = HttpClientUtil.get(url, request);
            SitesResponse response = JSONObject.parseObject(json,SitesResponse.class);
            if(response != null && CollUtil.isNotEmpty(response.getSites())){
                for (SitesResponse.SitesResponseData site : response.getSites()) {
                    String siteUri = site.getUri();
                    String vmUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryVMsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri, "100", "0","2"});
                    String vmJson = HttpClientUtil.get(vmUrl, request);
                    VmResponse vmResponse = JSONObject.parseObject(vmJson, VmResponse.class);
                    if(vmResponse != null){
                        count += vmResponse.getTotal();
                    }
                }
            }
            data.put("total", count);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("获取虚拟机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取虚拟机数量失败");
        }
    }

    /**
     * 获取物理机数量
     * @param request
     * @return
     */
    public static BaseResponse queryHostTotal(BaseCloudRequest request) {
        try {
            JSONObject data = new JSONObject();
            int count = 0, cpuCount = 0 ,memoryCount = 0;
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQuerySitesUrl(cloudAccessBean.getScvmmRole()), null);
            String json = HttpClientUtil.get(url, request);
            SitesResponse response = JSONObject.parseObject(json,SitesResponse.class);
            if(response != null && CollUtil.isNotEmpty(response.getSites())){
                for (SitesResponse.SitesResponseData site : response.getSites()) {
                    String siteUri = site.getUri();
                    String hostUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryHostsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri, "500", "0"});
                    String hostJson = HttpClientUtil.get(hostUrl, request);
                    HostResponse hostResponse = JSONObject.parseObject(hostJson,HostResponse.class);
                    if(hostResponse != null && CollUtil.isNotEmpty(hostResponse.getHosts())){
                        count += hostResponse.getTotal();
                        for (HostResponse.HostResponseData host : hostResponse.getHosts()) {
                            cpuCount += host.getCpuQuantity();
                            memoryCount += host.getMemQuantityMB();
                        }

                    }
                }
            }
            data.put("count",count);
            data.put("cpuCount",cpuCount);
            data.put("memoryCount",memoryCount);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("获取物理机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取物理机数量失败");
        }
    }

    public static BaseResponse describeTemplateInstance(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String siteUrn = map.get("siteUrn");
            String siteId = StringUtils.substringAfterLast(siteUrn, ":");
            String siteUri = UrnUtils.formatUrn(siteUrn);
            Boolean isTemplate = request.getBody().getCloud().getBoolean("isTemplate");
            String osType = map.get("osType");
            String customer = request.getBody().getCloud().getString("customer");
            List<JSONObject> result = new ArrayList<>();
            // isTemplate为true时，查询模板虚拟机,false时查询OS
            String url = "";
            if(isTemplate){
                url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getQueryTemplateVmsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri,"2","true"});
                String json = HttpClientUtil.get(url, request);
                VmResponse response = JSONObject.parseObject(json, VmResponse.class);
                if(response != null && CollUtil.isNotEmpty(response.getVms())){
                    for (VmResponse.VmResponseData vm : response.getVms()) {
                        if(osType.toLowerCase().equals(vm.getOsOptions().getOsType().toLowerCase())){
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("osId", vm.getUrn());
                            jsonObject.put("osName", vm.getName());
                            jsonObject.put("osTemplateVersion", vm.getOsOptions().getOsVersion());
                            jsonObject.put("osType", vm.getOsOptions().getOsType());
                            jsonObject.put("cpuSize",vm.getVmConfig().getCpu().getQuantity());
                            jsonObject.put("memorySize",vm.getVmConfig().getMemory().getQuantityMB()/1024);
                            jsonObject.put("vmUrn",vm.getUrn());
                            jsonObject.put("vmConfig",vm.getVmConfig());
                            result.add(jsonObject);
                        }
                    }
                }
                return new BaseDataResponse<>(result);
            }
            if("JC".equals( customer)){
                url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            }
            if("CA".equals( customer)){
                url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getOsUrlByHost(cloudAccessBean.getScvmmRole()), new String[]{siteUri,siteId,map.get("arch"),map.get("cpuVendor"),map.get("osVendor")});
            }
            String json = HttpClientUtil.get(url, request);
            JSONObject resultInfo = JSONObject.parseObject(json);
            if(ObjectUtil.isNotEmpty(resultInfo)){
                JSONArray  jsonArray = resultInfo.getJSONArray(osType.toLowerCase());
                for (Object object : jsonArray) {
                    JSONObject info = (JSONObject) object;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("osId", info.getInteger("id"));
                    jsonObject.put("osName", info.getString("versionDes"));
                    jsonObject.put("osTemplateVersion","");
                    jsonObject.put("osType", osType);
                    jsonObject.put("cpuSize",2);
                    jsonObject.put("memorySize",4);
                    jsonObject.put("vmUrn","");
                    jsonObject.put("vmConfig",new VmConfig());
                    result.add(jsonObject);
                }
            }
//            OsInfoResponse osInfoResponse =  JSONObject.parseObject(json,OsInfoResponse.class);
//            if("Linux".equals(osType)){
//                for (OsInfoResponse.OsInfoResponseData os : osInfoResponse.getLinux()) {
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("osId", os.getId());
//                    jsonObject.put("osName", os.getVersionDes());
//                    jsonObject.put("cpuSize",0);
//                    jsonObject.put("memorySize",0);
//                    jsonObject.put("vmUrn","");
//                    jsonObject.put("vmConfig",new VmConfig());
//                    result.add(jsonObject);
//                }
//            }
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("获取模板虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取模板虚拟机失败");
        }
    }

    public static List<String> getVmUriList(BaseCloudRequestBody body) {
        List<String> uriList = new ArrayList<>();
        if(body.containsKey("cis")) {
            for (JSONObject ci: body.getCis()) {
                String uri = ci.getString("openId");
                uriList.add(uri);
            }
        }else if(body.containsKey("ci")){
            String uri =   body.getCi().getString("openId");
            uriList.add(uri);
        }
        return uriList;
    }
}
