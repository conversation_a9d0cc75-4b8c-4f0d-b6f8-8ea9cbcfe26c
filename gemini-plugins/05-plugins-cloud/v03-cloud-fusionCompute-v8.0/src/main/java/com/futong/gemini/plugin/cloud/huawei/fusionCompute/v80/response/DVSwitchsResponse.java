package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DVSwitchsResponse {

    private List<DVSwitchsResponseData> dvSwitchs;

    @Data
    public static class DVSwitchsResponseData {
        /**
         * Dvswitch 标识。
         */
        private String urn;

        /**
         * 访问Dvswitch 的uri地址。
         */
        private String uri;

        /**
         * Dvswitch名称。
         */
        private String name;

        /**
         * 描述。
         */
        private String description;

        /**
         * 使用的交换类型:<br>
         * 0: vSwitch 普通模式<br>
         * 1: eSwitch-VMDQ，直通模式<br>
         * 2: SR-IOV 直通模式。
         */
        private Integer type;

        /**
         * 端口组个数。
         */
        private Integer portGroupNum;

        /**
         * IGMP Snooping开关<br>
         * true：开启IGMP Snooping开关， false：关闭IGMP Snooping开关，null：不涉及。
         */
        private Boolean isIgmpSnooping;

        /**
         * 预留，暂未使用，R5C10版本新增。
         */
        private Map<String, String> params;
    }
}
