package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.common.VRMTask;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.request.storage.*;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.UrnUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class StorageService {

    /**
     * 创建磁盘
     * @param arguments
     * @return
     */
    public static BaseResponse createVolume(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String diskMode = arguments.getBody().getCloud().getString("diskMode");
            String siteUri = UrnUtils.formatUrn(arguments.getBody().getCloud().getString("siteUrn"));
            VolumeCreateRequest request = arguments.getBody().getCloud().toJavaObject(VolumeCreateRequest.class);
            switch (diskMode){
                case "0":
                    request.setIndepDisk(false);
                    request.setPersistentDisk(true);
                    break;
                case "1":
                    request.setIndepDisk(true);
                    request.setPersistentDisk(true);
                    break;
                case "2":
                    request.setIndepDisk(true);
                    request.setPersistentDisk(false);
                    break;
                default:
                    request.setIndepDisk(false);
                    request.setPersistentDisk(true);
                    break;
            }
            if(request.getVolType() == 2){
                request.setVolType(0);
                request.setIsThin(true);
            }
            if(request.getVolType() == 0 || request.getVolType() == 1){
                request.setIsThin(false);
            }
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeCreateUrl(cloudAccessBean.getScvmmRole()), new String[]{siteUri});
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("创建磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建磁盘失败");
        }
    }

    /**
     * 修改磁盘
     * @param arguments
     * @return
     */
    public static BaseResponse updateVolume(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String uri  = arguments.getBody().getCloud().getString("uri");
            String diskMode = arguments.getBody().getCloud().getString("diskMode");
            VolumeUpdateRequest request = arguments.getBody().getCloud().toJavaObject(VolumeUpdateRequest.class);
            switch (diskMode){
                case "0":
                    request.setIndepDisk(false);
                    request.setPersistentDisk(true);
                    break;
                case "1":
                    request.setIndepDisk(true);
                    request.setPersistentDisk(true);
                    break;
                case "2":
                    request.setIndepDisk(true);
                    request.setPersistentDisk(false);
                    break;
            }
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeUpdateUrl(cloudAccessBean.getScvmmRole()), new String[]{uri});
            String json = HttpClientUtil.put(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(json);
        }catch (Exception e){
            log.error("创建磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建磁盘失败");
        }
    }

    /**
     * 删除磁盘
     * @param request
     * @return
     */
    public static BaseResponse deleteVolume(BaseCloudRequest request){
        try {
            List<VRMTask> result = new ArrayList<>();
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            List<String> volUrns = request.getBody().getCloud().getObject("volUrns",List.class);
            if(CollUtil.isNotEmpty(volUrns)){
                for (String volUrn : volUrns) {
                    String volumeUri = UrnUtils.formatUrn(volUrn);
                    String isFormat = request.getBody().getJSONObject("model").getString("isFormat") == null?"0":request.getBody().getJSONObject("model").getString("isFormat");
                    String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{volumeUri,isFormat});
                    String json = HttpClientUtil.delete(url, request);
                    VRMTask vrmTask = JSONObject.parseObject(json, VRMTask.class);
                    result.add(vrmTask);
                }
            }
            return new BaseDataResponse<>(result);
        }catch (Exception e){
            log.error("删除磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除磁盘失败");
        }
    }

    /**
     * 挂载磁盘
     * @param arguments
     * @return
     */
    public static BaseResponse attachVolume(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String vmUri = UrnUtils.formatUrn(arguments.getBody().getCloud().getString("instanceId"));
            AttachVolumeRequest request = arguments.getBody().getCloud().toJavaObject(AttachVolumeRequest.class);
            request.setAccessMode(0);
            request.setIoMode("dataplane");
            request.setPciType("VIRTIO");
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeAttachUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("挂载磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "挂载磁盘失败");
        }
    }


    /**
     * 卸载磁盘
     * @param arguments
     * @return
     */
    public static BaseResponse detachVolume(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String vmUri = UrnUtils.formatUrn(arguments.getBody().getCloud().getString("instanceId"));
            DetachVolumeRequest request = arguments.getBody().getCloud().toJavaObject(DetachVolumeRequest.class);
            request.setIsFormat(false);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeDetachUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("卸载磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "卸载磁盘失败");
        }
    }

    /**
     * 扩容磁盘
     * @param arguments
     * @return
     */
    public static BaseResponse resizeVolume(BaseCloudRequest arguments){
        try {
            CloudAccessBean cloudAccessBean = arguments.getBody().getAccess();
            String vmUri = UrnUtils.formatUrn(arguments.getBody().getCloud().getString("instanceId"));
            Long capacity = arguments.getBody().getCloud().getLong("capacity")*1024L;
            ResizeVolumeRequest request = arguments.getBody().getCloud().toJavaObject(ResizeVolumeRequest.class);
            request.setSize(capacity);
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVolumeResizeUrl(cloudAccessBean.getScvmmRole()), new String[]{vmUri});
            String json = HttpClientUtil.post(url, arguments, JSON.toJSONString(request));
            return new BaseDataResponse<>(JSONObject.parseObject(json, VRMTask.class));
        }catch (Exception e){
            log.error("磁盘扩容失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "磁盘扩容失败");
        }
    }
}
