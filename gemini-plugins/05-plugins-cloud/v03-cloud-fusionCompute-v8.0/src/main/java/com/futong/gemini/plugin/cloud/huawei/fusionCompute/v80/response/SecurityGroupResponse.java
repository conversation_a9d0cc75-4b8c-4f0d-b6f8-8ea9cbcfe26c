package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class SecurityGroupResponse {

    private Integer total;

    private List<SecurityGroupResponseData> securityGroups;

    @Data
    public static class SecurityGroupResponseData {
        private String sgId;
        private String sgName;
        private String sgDesc;
        private String rules;
        private String vmNum;
        private Integer sgType;
        private List<SecurityGroupResponseDataVm> vmList;
    }

    @Data
    public static class SecurityGroupResponseDataVm {
        private String vmId;
        private String vmName;
        private String vmUrn;
        private String vmUri;
    }
}
