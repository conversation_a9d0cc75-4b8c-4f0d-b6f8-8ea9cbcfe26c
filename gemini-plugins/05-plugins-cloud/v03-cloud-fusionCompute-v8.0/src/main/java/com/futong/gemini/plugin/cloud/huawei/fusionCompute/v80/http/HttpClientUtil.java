package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response.SitesResponse;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.EncryptUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.util.URLUtils;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.vo.LoginResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Map;

@Slf4j
public class HttpClientUtil {

    private static final String HTTPS = "https";

    private static final int HTTP_STATUS_CODE_OK = 200;

    /***
     * 获取token
     * @param bean
     * @return
     */
    public static LoginResponse getToken(CloudAccessBean bean){
        LoginResponse token =null;
        try {
            token = (LoginResponse) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        } catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }
        if(ObjectUtil.isNull(token)){
            token = fetchToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}",JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, bean.getCmpId(),token);
        }
        return token;
    }
    public  static LoginResponse fetchToken(CloudAccessBean bean) {
        /**拼接获取 token url */
        String url = URLUtils.bean.makeUrl(bean, URLUtils.bean.getAuthenticateUrl(bean.getScvmmRole()),null);
        CloseableHttpClient  httpClient = buildHttpClient(url);
        try {
            HttpPost httpPost = new HttpPost(url);
            HttpClientConfig config = new HttpClientConfig();
            httpPost.setConfig(config.buildRequestConfig());
            String version = bean.getVersion();
            httpPost.addHeader("Accept","application/json;version="+version+";charset=UTF-8");
            httpPost.addHeader("Content-Type","application/json;charset=UTF-8");
            httpPost.addHeader(TokenEnum.AUTH_USER.getValue(),bean.getUsername());
            httpPost.addHeader(TokenEnum.AUTH_KEY.getValue(), EncryptUtils.encodeToSHA256(bean.getPassword()));
            HttpResponse response = httpClient.execute(httpPost);
            String json = getResponseStr(response);
            if(response.getStatusLine().getStatusCode() != HTTP_STATUS_CODE_OK){
                throw new BaseException(BaseResponse.FAIL_PARAM.of("获取Token信息失败!"));
            }
            LoginResponse loginResponse = JSONObject.parseObject(json, LoginResponse.class);
            Header[] headers = response.getHeaders(TokenEnum.AUTH_TOKEN.getValue());
            if(ObjectUtil.isEmpty(headers)){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.of("获取Token信息失败!"));
            }
            for (Header header : headers) {
                loginResponse.setToken(header.getValue());
            }
            return loginResponse;
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * 设置站点缓存
     * @param info
     */
    public static void putSiteInfo(SitesResponse info){
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, IdUtils.encryptId(BaseClient.auths.get().getCmpId(),"site"),info);
    }

    /***
     * 获取站点缓存
     * @return
     */
    public static SitesResponse getSiteInfo(){
        SitesResponse sitesResponse = (SitesResponse) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, IdUtils.encryptId(BaseClient.auths.get().getCmpId(),"site"));
        return sitesResponse;
    }

    /**
     * Get http请求
     * @param url    请求地址
     * @return 响应结果字符串
     */
    public static String get(String url, BaseCloudRequest request) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpGet httpGet = new HttpGet(url);
        try {
            HttpClientConfig config = buildConfig(request);
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            HttpResponse response = httpClient.execute(httpGet);
            return getResponseStr(response);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, BaseCloudRequest request, String json) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        try {
            HttpClientConfig config = buildConfig(request);
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            EntityBuilder entityBuilder = EntityBuilder.create();
            if(StrUtil.isNotEmpty(json)){
                entityBuilder.setText(json);
            }
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }


    /**
     * Put请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String put(String url, BaseCloudRequest request, String json) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPut httpPut = new HttpPut(url);
        try {
            HttpClientConfig config = buildConfig(request);
            httpPut.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPut.addHeader(key, header.get(key));
            }
            EntityBuilder entityBuilder = EntityBuilder.create();
            if(StrUtil.isNotEmpty(json)){
                entityBuilder.setText(json);
            }
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPut.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPut);
            return getResponseStr(response);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * 删除
     * @param url
     * @param request
     * @return
     */
    public static String delete(String url,BaseCloudRequest request) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpDelete httpDelete = new HttpDelete(url);
        try {
            HttpClientConfig config = buildConfig(request);
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStr(response);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public static HttpClientConfig buildConfig(BaseCloudRequest request){
        try {
            HttpClientConfig httpClientConfig = new HttpClientConfig();
            LoginResponse loginResponse = request.getBody().getJSONObject("authToken").toJavaObject(LoginResponse.class);
            CloudAccessBean accessBean = request.getBody().getAccess();
            String version = accessBean.getVersion();
            httpClientConfig.addHeader("Content-Type","application/json");
            httpClientConfig.addHeader("Accept"," application/json;version="+version+";charset=UTF-8");
            httpClientConfig.addHeader(TokenEnum.AUTH_TOKEN.getValue(), loginResponse.getToken());
            return httpClientConfig;
        }catch (Exception e){
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }

    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @return
     */
    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            int connectTimeout = 50000;
            int socketTimeout = 50000;
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();
            if (url.toLowerCase().startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).setDefaultRequestConfig(requestConfig).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }

    private static String getResponseStr(HttpResponse response) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        String entity = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8).replace("@type", "type");

//        log.info("httpResult:{}",entity);
//        HttpResult httpResult = JSON.parseObject(entity,HttpResult.class);
//        httpResult.setCode(httpResult.getErrorCode()==null?httpResult.getCode():httpResult.getErrorCode());
//        if(httpResult.getCode() != 200){
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,httpResult.getMessage());
//        }
        return entity;
    }
}
