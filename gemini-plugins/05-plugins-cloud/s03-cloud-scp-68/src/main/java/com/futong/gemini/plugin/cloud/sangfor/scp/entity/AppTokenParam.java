package com.futong.gemini.plugin.cloud.sangfor.scp.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppTokenParam {

    public AppTokenParam(String username, String password) {
        this.auth = new Auth();
        this.auth.passwordCredentials = new PasswordCredentials();
        this.auth.passwordCredentials.username = username;
        this.auth.passwordCredentials.password = password;
    }

    private Auth auth;

    @Data
    public static class Auth {
        private PasswordCredentials passwordCredentials;
    }

    @Data
    public static class PasswordCredentials {
        private String username;
        private String password;
    }
}
