package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class NetClient extends ScpClient {

    public NetClient(Config config) throws Exception {
        super(config);
    }

    public DescribeEipsResponse describeEipPools(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/floatingippools", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeEipsResponse());
    }

    public DescribeEipsResponse describeEips(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/floatingips", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeEipsResponse());
    }

    public DescribeVpcsResponse describeVpcs(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/vpcs", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeVpcsResponse());
    }

    public DescribeSubnetsResponse describeSubnets(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/subnets", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeSubnetsResponse());
    }

    public DescribeRoutersResponse describeRouters(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/classic-routers", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeRoutersResponse());
    }

    public DescribeSwitchesResponse describeDVSwitches(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/classic-dvswitches", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeSwitchesResponse());
    }

    public DescribeVlansResponse describeBVSwitches(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/classic-bvswitches", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeVlansResponse());
    }

    public DescribePortsResponse describePorts(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/ports", "json", req, runtime);
        return TeaModel.toModel(map, new DescribePortsResponse());
    }
}
