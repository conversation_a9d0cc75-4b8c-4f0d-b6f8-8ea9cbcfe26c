package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeAZonesResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeAZonesResponseBody body;
    @Data
    public static class DescribeAZonesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeAZonesResponseBodyData data;
    }

    @Data
    public static class DescribeAZonesResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<AZones> data;
    }

    @Data
    public static class AZones extends TeaModel {
        @NameInMap("id")
        public String id;
        @NameInMap("name")
        public String name;
        @NameInMap("description")
        public String description;
        @NameInMap("status")
        public String status;
        @NameInMap("type")
        public String type;
        @NameInMap("tag")
        public String tag;
        @NameInMap("version")
        public String version;
    }
}
