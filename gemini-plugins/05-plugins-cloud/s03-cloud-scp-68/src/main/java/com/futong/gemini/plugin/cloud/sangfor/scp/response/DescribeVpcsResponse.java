package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeVpcsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeVpcResponseBody body;
    @Data
    public static class DescribeVpcResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeVpcResponseBodyData data;
    }

    @Data
    public static class DescribeVpcResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Vpc> data;
    }

    @Data
    public static class Vpc extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("type")
        public String type;

        @NameInMap("status")
        public String status;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("shared")
        public Integer shared;
    }

}
