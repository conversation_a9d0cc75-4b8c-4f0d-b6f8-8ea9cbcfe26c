package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeEipsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeEipResponseBody body;
    @Data
    public static class DescribeEipResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeEipResponseBodyData data;
    }

    @Data
    public static class DescribeEipResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Eip> data;
    }

    @Data
    public static class Eip extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("floating_ip")
        public String floatingIp;

        @NameInMap("floatingip_pool_id")
        public String floatingipPoolId;

        @NameInMap("floating_port_id")
        public String floatingPortId;

        @NameInMap("sharedbandwidth_id")
        public String sharedbandwidthId;

        @NameInMap("bandwidth")
        public Bandwidth bandwidth;

        @NameInMap("binding_info")
        public BindingInfo bindingInfo;
    }

    @Data
    public static class Bandwidth extends TeaModel {
        @NameInMap("qos_uplink_kbps")
        public Integer qosUplinkKbps;

        @NameInMap("qos_downlink_kbps")
        public Integer qosDownlinkKbps;
    }

    @Data
    public static class BindingInfo extends TeaModel {
        @NameInMap("router_id")
        public String routerId;

        @NameInMap("status")
        public String status;

        @NameInMap("bind_type")
        public String bindType;

        @NameInMap("ip_address")
        public String ipAddress;

        @NameInMap("port_id")
        public String portId;

        @NameInMap("vpc_id")
        public String vpcId;

        @NameInMap("bind_id")
        public String bindId;

        @NameInMap("az_id")
        public String azId;
    }

}
