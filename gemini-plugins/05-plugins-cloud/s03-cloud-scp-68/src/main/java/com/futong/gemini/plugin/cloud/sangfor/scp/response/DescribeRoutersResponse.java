package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeRoutersResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeRouterResponseBody body;
    @Data
    public static class DescribeRouterResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeRouterResponseBodyData data;
    }

    @Data
    public static class DescribeRouterResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Router> data;
    }

    @Data
    public static class Router extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("dh_id")
        public String dhId;

        @NameInMap("host_id")
        public String hostId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("device_type")
        public String deviceType;

        @NameInMap("ha_enable")
        public Integer haEnable;

        @NameInMap("vifs")
        public List<Vif> vifs;
    }

    @Data
    public static class Vif extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("enable")
        public Integer enable;

        @NameInMap("ipv4")
        public String ipv4;

        @NameInMap("ipv4_mask")
        public String ipv4Mask;

        @NameInMap("ipv6_prefix_len")
        public String ipv6PrefixLen;

        @NameInMap("ipv6")
        public String ipv6;

        @NameInMap("mac")
        public String mac;

        @NameInMap("peer_device_id")
        public String peerDeviceId;

        @NameInMap("peer_device_type")
        public String peerDeviceType;

        @NameInMap("peer_vlink_id")
        public String peerVlinkId;
    }

}
