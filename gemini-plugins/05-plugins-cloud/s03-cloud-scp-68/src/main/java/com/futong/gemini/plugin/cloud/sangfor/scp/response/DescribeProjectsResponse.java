package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeProjectsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeProjectResponseBody body;
    @Data
    public static class DescribeProjectResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeProjectResponseBodyData data;
    }

    @Data
    public static class DescribeProjectResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Project> data;
    }

    @Data
    public static class Project extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("user_name")
        public String userName;

        @NameInMap("role_type")
        public String roleType;

        @NameInMap("azs")
        public List<Az> azs;

        @NameInMap("dhs")
        public List<Dh> dhs;
    }

    @Data
    public static class Az extends TeaModel {
        @NameInMap("az_id")
        public String azId;

        @NameInMap("type")
        public String type;

        @NameInMap("az_name")
        public String azName;

        @NameInMap("az_tag")
        public String azTag;

        @NameInMap("dh_ids")
        public List<String> dhIds;

        @NameInMap("arch_type")
        public String archType;
    }

    @Data
    public static class Dh extends TeaModel {
        @NameInMap("az_id")
        public String azId;

        @NameInMap("dh_id")
        public String dhId;

        @NameInMap("dh_name")
        public String dhName;

        @NameInMap("tag")
        public String tag;
    }
}
