package com.futong.gemini.plugin.cloud.sangfor.scp.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.futong.common.redis.FTRedisUtils;
import com.futong.common.utils.Entry;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.*;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
public class Converts {

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> metrics = new HashMap<>();
    private static Entry.E2<String, String>[] metricType = new Entry.E2[]{
            new Entry.E2("Average", "average"),
            new Entry.E2("Maximum", "max"),
            new Entry.E2("Minimum", "min"),
            new Entry.E2("Value", "value"),
            new Entry.E2("Sum", "sum"),
    };

    static {
        metrics.put("cpu.util", new Entry.E3("cpuUsage", "average", "%"));
        metrics.put("memory.util", new Entry.E3("memUsage", "average", "%"));
        //metrics.put("disk.util", new Entry.E3("diskUsage", "average", "%"));
        metrics.put("io.read.speed", new Entry.E3("diskRead", "average", "Count/s"));
        metrics.put("io.write.speed", new Entry.E3("diskWrite", "average", "Count/s"));
        metrics.put("net.in.bps", new Entry.E3("netIn", "average", "bit/s"));
        metrics.put("net.out.bps", new Entry.E3("netOut", "average", "bit/s"));
    }

    public static final String SCP_DISK_USAGE = "scp_disk_usage:";

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMapping = new HashMap<>();
    static {
        perfMapping.put("cpuUtil", PerfInfoBean::setCpuUsage);
        perfMapping.put("memoryUtil", PerfInfoBean::setMemUsage);
        //perfMapping.put("diskUtil", PerfInfoBean::setDiskUsage);
        perfMapping.put("ioReadSpeed", PerfInfoBean::setDiskRead);
        perfMapping.put("ioWriteSpeed", PerfInfoBean::setDiskWrite);
        perfMapping.put("netInBps", PerfInfoBean::setNetIn);
        perfMapping.put("netOutBps", PerfInfoBean::setNetOut);
    }

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    public static Map<Class, List> convertProject(BaseCloudRequest request, DescribeProjectsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || ObjectUtil.isEmpty(response.body.data) || CollUtil.isEmpty(response.body.data.data)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的项目信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                TenantSide.TENANT_PROJECT.value(),
                response.body.data.data,
                DescribeProjectsResponse.Project::getName,
                DescribeProjectsResponse.Project::getId
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    public static Map<Class, List> convertZone(BaseCloudRequest request, DescribeAZonesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || ObjectUtil.isEmpty(response.body.data) || CollUtil.isEmpty(response.body.data.data)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的可用区信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_RESOURCEPOOL.value(),
                response.body.data.data,
                DescribeAZonesResponse.AZones::getName,
                DescribeAZonesResponse.AZones::getId
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    public static Map<Class, List> convertCluster(BaseCloudRequest request, DescribeClusterResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || ObjectUtil.isEmpty(response.body.data) || CollUtil.isEmpty(response.body.data.data)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        request.getBody().getCloud().getString("az_id"),
                        DevopsSide.DEVOPS_RESOURCEPOOL.value());
        for (DescribeClusterResponse.Cluster cluster : response.body.data.data) {
            BuilderDevops devopsZone = new BuilderDevops()
                    .withDevops(devops.get(), cluster.name, cluster.id, DevopsSide.DEVOPS_CLUSTER.value());
            data.add(devopsZone.get());
            links.add(devopsZone.builderLink(devops.get()));
        }
        result.put(TmdbDevops.class, data);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }

    public static Map<Class, List> convertImage(BaseCloudRequest request, DescribeImagesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbImageRes> dataImage = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_IMAGE_RES
                );
        for (DescribeImagesResponse.Image image : response.body.data.data) {
            CmdbImageRes ci = new CmdbImageRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), image.getId()));
            ci.setType(image.getOsType());
            ci.setSize(image.getSizeMb() / 1024);
            ci.setStatus(image.getStatus());
            ci.setOpen_id(image.getId());
            ci.setOpen_name(image.getName());
            ci.setVisibility("private");
            ci.setImage_source("public".equals(image.getImageType()) ? "system" : "self");
            toCiResCloud(request, ci);
            dataImage.add(ci);
            for (DescribeImagesResponse.Az az : image.getAzs()) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, az.getId());
            }
        }
        result.put(CmdbImageRes.class, dataImage);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertEip(BaseCloudRequest request, DescribeEipsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbEipRes> data = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_EIP_RES
                );
        for (DescribeEipsResponse.Eip eip : response.body.data.data) {
            CmdbEipRes ci = new CmdbEipRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getId()));
            ci.setBandwidth_speed(eip.getBandwidth() != null ? String.valueOf(eip.getBandwidth().getQosDownlinkKbps()) : "0");
            ci.setElastic_ip(eip.getFloatingIp());
            String status = "other"; // 默认状态
            if (eip.getBindingInfo() != null) {
                String bindingStatus = eip.getBindingInfo().getStatus();
                ci.setOpen_status(bindingStatus);
                if ("active".equals(bindingStatus)) {
                    status = "inuse";
                } else if ("errror".equals(bindingStatus)) {
                    status = "errror";
                }
            } else {
                status = "available";
            }
            ci.setStatus(status);
            ci.setOpen_id(eip.getId());
            ci.setOpen_name(eip.getFloatingIp());
            toCiResCloud(request, ci);
            data.add(ci);
            if (eip.getBindingInfo() != null) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, eip.getBindingInfo().getAzId());
            }
        }
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertStorage(BaseCloudRequest request, DescribeStoragesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbStoragePoolRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbStoragePoolRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_STORAGE_POOL_RES
                );
        for (DescribeStoragesResponse.Storage res : response.body.data.data) {
            CmdbStoragePoolRes ci = new CmdbStoragePoolRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setType(res.getType());
            ci.setTotal_size(Float.valueOf(res.getTotalMb()) / 1024);
            ci.setUsed_size(Float.valueOf(res.getUsedMb()) / 1024);
            ci.setStatus("normal".equals(res.getStatus()) && !"".equals(res.getStorageTagId()) ? "active" : "inactive");
            ci.setOpen_status(res.getStatus());
            //存储标签id
            ci.setSub_account_id(res.getStorageTagId());
            ci.setDesc(res.getDescription());
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        result.put(CmdbStoragePoolRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }
    public static Map<Class, List> convertVpc(BaseCloudRequest request, DescribeVpcsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_VPC_RES
                );
        List<CmdbVpcRes> data = new ArrayList<>();
        for (DescribeVpcsResponse.Vpc res : response.body.data.data) {
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setDesc(res.getDescription());
            ci.setStatus(res.getStatus());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
        }
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertSubnet(BaseCloudRequest request, DescribeSubnetsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_SUBNET_RES
                );
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSubnetsResponse.Subnet res : response.body.data.data) {
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setCidr_ipv4(res.getCidr());
            ci.setStatus(res.getStatus());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), request.getBody().getCloud().getString("vpc_id")));
            associations.add(rule);
        }
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertHost(BaseCloudRequest request, DescribeHostsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbHostRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_HOST_RES
                );
        List<CmdbHostRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeHostsResponse.Host res : response.body.data.data) {
            CmdbHostRes ci = new CmdbHostRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setStatus(res.getStatus());
            ci.setModel(res.getType());
            ci.setTotal_size(res.getStorage().getTotalMb() / 1024);
            switch (res.getStatus()) {
                case "running":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "offline":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
            }
            ci.setOpen_status(res.getStatus());
            ci.setIp(res.getIp());
            ci.setCpu_size(res.getCpu().coreCount);
            ci.setMem_size(res.getMemory().getTotalMb().intValue());
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
        }
        result.put(CmdbHostRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, DescribeInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_INSTANCE_RES
                );
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<CmdbNetcardRes> netcards = new ArrayList<>();
        List<CmdbDiskRes> disks = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbFlavor> flavors = new ArrayList<>();
        for (DescribeInstancesResponse.Instance res : response.body.data.data) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setCpu_size(res.getCores());
            ci.setMem_size(res.getMemoryMb());
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            if ("running".equals(res.getStatus()) && res.getStorageStatus() != null) {
                FTRedisUtils.setDouble(Converts.SCP_DISK_USAGE + res.getId(), NumberUtil.mul(res.getStorageStatus().getRatio().floatValue() ,100), 60 * 60 * 2);
            }
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            toCiResCloud(request, ci);
            setStatus(res.getStatus(), ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            if (CollUtil.isNotEmpty(res.getDisks())) {
                for (DescribeInstancesResponse.Disk diskRes : res.getDisks()) {
                    CmdbDiskRes disk = new CmdbDiskRes();
                    disk.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId(), diskRes.getId()));
                    disk.setType(diskRes.getPhysicalDiskType());
                    disk.setCategory("data");
                    disk.setSize(diskRes.getSizeMb() / 1024);
                    disk.setStatus("use");
                    disk.setStore_name(diskRes.getStorageFile());
                    disk.setLun_id(diskRes.getStorageId());
                    disk.setOpen_id(diskRes.getId());
                    disk.setOpen_name(diskRes.getStorageName());
                    toCiResCloud(request, disk);
                    disks.add(disk);

                    Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, disk.getRes_id());
                    associations.add(diskAss);
                }
            }
            if (ObjectUtil.isNotEmpty(res.getGpuConf())) {
                CmdbFlavor flavor = new CmdbFlavor();
                flavor.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbFlavor.class.getSimpleName(), res.getId()));
                flavor.setCpu_size(res.getCores());
                flavor.setMem_size(res.getMemoryMb());
                flavor.setCategory("GPU");
                flavor.setGpu_size(Float.valueOf(res.getGpuConf().getVgpuMemSize()));
                flavor.setGpu_num(res.getGpuConf().getGpuCores());
                flavor.setGpu_model(res.getGpuConf().getGpuName());
                flavor.setOpen_id(res.getId());
                flavor.setOpen_name(res.getId());
                toCiResCloud(request, flavor);
                flavors.add(flavor);

                Association flavorAss = AssociationUtils.toAssociation(ci, CmdbFlavor.class, flavor.getRes_id());
                associations.add(flavorAss);
            }
            //关联镜像
            if (StrUtil.isNotEmpty(res.getImageId())) {
                Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getImageId()));
                associations.add(image);
            }
            //操作新系统OS对象
            if (StrUtil.isNotEmpty(res.getOsName())) {
                //关联镜像OS
                String osName = res.getOsName();
                CmdbOsRes os = new CmdbOsRes();
                os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), osName));
                os.setType(osName);
                os.setCpu_arch(res.getOsType());
                os.setVersion(res.getOsType());
                os.setFull_name(osName);
                os.setOpen_name(osName);
                os.setOpen_id(osName);
                toCiResCloud(request, os);
                osList.add(os);
                Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
                associations.add(osa);
            }
            //关联主机
            associations.add(AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getHostId())));
            //关联网卡
            if (CollUtil.isNotEmpty(res.getNetworks())) {
                for (DescribeInstancesResponse.Network network : res.getNetworks()) {
                    CmdbNetcardRes netCard = new CmdbNetcardRes();
                    netCard.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getVifId()));
                    netCard.setModule(network.getModel());
                    netCard.setIpv4_address(network.getIpAddress());
                    netCard.setIpv6_address(network.getIpv6Address());
                    netCard.setMac_address(network.getMacAddress());
                    netCard.setDesc(network.getDeviceId());
                    netCard.setOpen_id(network.getVifId());
                    netCard.setOpen_name(network.getVifId());
                    netCard.setOpen_status(network.getConnect().toString());
                    toCiResCloud(request, netCard);
                    netcards.add(netCard);
                    //关联网络VPC
                    if (StrUtil.isNotEmpty(network.getVpcId())) {
                        associations.add(AssociationUtils.toAssociation(netCard, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getVpcId())));
                    }
                    //关联网络VPC子网
                    if (StrUtil.isNotEmpty(network.getSubnetId())) {
                        associations.add(AssociationUtils.toAssociation(netCard, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getSubnetId())));
                    }
                    //关联端口组
                    if (StrUtil.isNotEmpty(network.getPortId())) {
                        associations.add(AssociationUtils.toAssociation(netCard, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getPortId())));
                    }
                    //关联交换机(物理出口)
                    if (StrUtil.isNotEmpty(network.getDeviceId())) {
                        associations.add(AssociationUtils.toAssociation(netCard, CmdbVlanRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getDeviceId())));
                    }
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ci, netCard));
                }
            }
            //关联IP
            if (CollUtil.isNotEmpty(res.getIps())) {
                for (DescribeInstancesResponse.Network network : res.getNetworks()) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getIpAddress()));
                    ip.setType(IpType.PRIVATE_IP.value());
                    ip.setAddress(network.getIpAddress());
                    ip.setOpen_id(network.getIpAddress());
                    ip.setOpen_name(network.getIpAddress());
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ci, ip));
                }
            }
            //添加公网弹性IP,关联弹性IP
            if (ObjectUtil.isNotNull(res.getFloatingip()) && StrUtil.isNotEmpty(res.getFloatingip().getFloatingIpAddress())) {
                CmdbIpRes ip = new CmdbIpRes();
                DescribeInstancesResponse.FloatingIp eip = res.getFloatingip();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getFloatingipId()));
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(eip.getFloatingIpAddress());
                ip.setOpen_id(eip.getFloatingipId());
                ip.setOpen_name(eip.getFloatingIpAddress());
                toCiResCloud(request, ip);
                ips.add(ip);
                //IP关联云主机
                associations.add(AssociationUtils.toAssociation(ci, ip));
                //云主机关联EIP
                Association association = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getFloatingipId()));
                associations.add(association);
            }
            data.add(ci);
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbDiskRes.class, disks);
        result.put(CmdbIpRes.class, ips);
        result.put(CmdbNetcardRes.class, netcards);
        result.put(CmdbOsRes.class, osList);
        result.put(CmdbFlavor.class, flavors);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    private static void setStatus(String status, CmdbInstanceRes ci) {
        switch (status) {
            case "creating":
                ci.setStatus(InstanceStatus.BUILDING.value());
                break;
            case "running":
                ci.setStatus(InstanceStatus.RUNNING.value());
                break;
            case "starting":
                ci.setStatus(InstanceStatus.STARTING.value());
                break;
            case "stoping":
                ci.setStatus(InstanceStatus.STOPPING.value());
                break;
            case "resetting":
                ci.setStatus(InstanceStatus.RESTARTING.value());
                break;
            case "deleting":
                ci.setStatus(InstanceStatus.DELETING.value());
                break;
            case "migrate":
                ci.setStatus(InstanceStatus.MIGRATING.value());
                break;
            case "recovery":
                ci.setStatus(InstanceStatus.RECOVERING.value());
                break;
            case "stopped":
                ci.setStatus(InstanceStatus.STOPPED.value());
                break;
            case "suspend":
                ci.setStatus(InstanceStatus.SUSPENDED.value());
                break;
            default:
                ci.setStatus(InstanceStatus.UNKNOWN.value());
                break;
        }
    }

    public static Map<Class, List> convertRefreshEcs(BaseCloudRequest request, DescribeInstanceResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || ObjectUtil.isEmpty(response.body.data)) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_INSTANCE_RES
                );
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<CmdbDiskRes> disks = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        DescribeInstanceResponse.Instance res = response.body.data;
        CmdbInstanceRes ci = new CmdbInstanceRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
        ci.setCpu_size(res.getCores());
        ci.setMem_size(res.getMemoryMb().intValue());
        ci.setOpen_status(res.getStatus());
        ci.setDesc(res.getDescription());
        ci.setOpen_id(res.getId());
        ci.setOpen_name(res.getName());
        toCiResCloud(request, ci);
        setStatus(res.getStatus(), ci);
        builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
        if (CollUtil.isNotEmpty(res.getDisks())) {
            for (DescribeInstanceResponse.Disk diskRes : res.getDisks()) {
                CmdbDiskRes disk = new CmdbDiskRes();
                disk.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId(), diskRes.getId()));
                disk.setType(diskRes.getType());
                disk.setCategory("data");
                disk.setSize(diskRes.getSizeMb() / 1024);
                disk.setStatus("use");
                disk.setStore_name(diskRes.getStorageFile());
                disk.setLun_id(diskRes.getStorageId());
                disk.setOpen_id(diskRes.getId());
                disk.setOpen_name(diskRes.getStorageName());
                toCiResCloud(request, disk);
                disks.add(disk);

                Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, disk.getRes_id());
                associations.add(diskAss);
            }
        }
        data.add(ci);
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbDiskRes.class, disks);
        result.put(Association.class, associations);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertRouter(BaseCloudRequest request, DescribeRoutersResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbRouteRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbRouteRes> data = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_ROUTE_RES
                );
        for (DescribeRoutersResponse.Router res : response.body.data.data) {
            CmdbRouteRes ci = new CmdbRouteRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
        }
        result.put(CmdbRouteRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class,List> convertDVSwitch(BaseCloudRequest request, DescribeSwitchesResponse response){
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbVlanRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbVlanRes> data = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_VLAN_RES
                );
        for (DescribeSwitchesResponse.Switch res : response.body.data.data) {
            CmdbVlanRes ci = new CmdbVlanRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setCategory("交换机");
            ci.setLink_type("1");
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
        }
        result.put(CmdbVlanRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class,List> convertBVSwitch(BaseCloudRequest request, DescribeVlansResponse response){
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbVlanRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<Association> associations = new ArrayList<>();
        List<CmdbVlanRes> data = new ArrayList<>();
        List<CmdbSubnetRes> subnetRes = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_VLAN_RES
                );
        for (DescribeVlansResponse.Vlan res : response.body.data.data) {
            CmdbVlanRes ci = new CmdbVlanRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setCategory("物理出口");
            ci.setLink_type(String.valueOf(res.getEnable()));
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            List<DescribeVlansResponse.VlanGroup> vlanGroup = res.getVlanGroup();
            if (CollectionUtil.isNotEmpty(vlanGroup)){
                for (DescribeVlansResponse.VlanGroup vlanGroupRes : vlanGroup) {
                    CmdbSubnetRes subnet = new CmdbSubnetRes();
                    subnet.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vlanGroupRes.getId()));
                    subnet.setOpen_id(vlanGroupRes.getId());
                    subnet.setOpen_name(vlanGroupRes.getName());
                    subnet.setVlan_id(vlanGroupRes.getVlanRange());
                    subnet.setType(vlanGroupRes.getType());
                    toCiResCloud(request, subnet);
                    subnetRes.add(subnet);
                    //关联物理出口
                    associations.add(AssociationUtils.toAssociation(ci, subnet));
                }
            }
        }
        result.put(CmdbVlanRes.class, data);
        result.put(CmdbSubnetRes.class, subnetRes);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class,List> convertPort(BaseCloudRequest request, DescribePortsResponse response){
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_NETCARD_RES
                );
        for (DescribePortsResponse.Port res : response.body.data.data) {
            CmdbNetcardRes ci = new CmdbNetcardRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setStatus(String.valueOf(res.getAdminStatusUp()));
            ci.setOpen_status(String.valueOf(res.getAdminStatusUp()));
            ci.setMac_address(res.getMacAddress());
            if (CollUtil.isNotEmpty(res.getIpsInfo())) {
                ci.setIpv4_address(res.getIpsInfo().stream().map(DescribePortsResponse.IpsInfo::getIpAddress).collect(Collectors.joining(",")));
            }
            toCiResCloud(request, ci);
            data.add(ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            //关联VPC
            Association association = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(association);
            //关联子网
            association = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            associations.add(association);
        }
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<String, PerfInfoBean> convertPerf(BaseCloudRequest request, Map<String, DescribeMetricsResponse.DescribeMetricResponseBodyData> metricList) {
        if (CollUtil.isEmpty(metricList)) {
            return null;
        }
        Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (Map.Entry<String, DescribeMetricsResponse.DescribeMetricResponseBodyData> entry : metricList.entrySet()) {
            String instanceId = entry.getKey();
            DescribeMetricsResponse.DescribeMetricResponseBodyData metrics = entry.getValue();
            for (Map.Entry<String, Object> metricEntry : BeanUtil.beanToMap(metrics).entrySet()) {
                String metricName = metricEntry.getKey();
                DescribeMetricsResponse.Metric metric = (DescribeMetricsResponse.Metric)metricEntry.getValue();
                for (List<Object> datapoint : metric.getDatapoints()) {

                    DateTime dateTime = DateUtil.parse(String.valueOf(datapoint.get(0)), DatePattern.UTC_SIMPLE_PATTERN);
                    //对齐5分钟粒度数据
                    int minute = dateTime.getField(DateField.MINUTE);
                    int roundedMinute = (minute / 5) * 5; // 向下取整到最近的 5 的倍数
                    dateTime = dateTime.offsetNew(DateField.MINUTE, roundedMinute - minute);
                    Object o = datapoint.get(1);
                    Double average = o == null ? 0 :Double.parseDouble(String.valueOf(o));
                    String createTime = DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
                    String id = instanceId + "_" + dateTime.getTime();
                    PerfInfoBean perf = perfMap.get(id);
                    if (perf == null) {
                        //生成指标对象
                        perf = new PerfInfoBean();//指标对应得资源CI信息
                        Object info = instanceMap.get(instanceId);
                        if (info instanceof ResInstanceDiskApiModel) {
                            ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
                            perf.setResId(vminfo.getRes_id());
                            perf.setOpenId(vminfo.getOpen_id());
                            perf.setOpenName(vminfo.getOpen_name());
                            perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
                            perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
                            if (CollUtil.isNotEmpty(vminfo.getDisks())) {
                                Double sum = vminfo.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                                perf.setDiskSize(sum);
                            }
                            perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                            //设置存储使用率
                            perf.setDiskUsage(FTRedisUtils.getDouble(Converts.SCP_DISK_USAGE + vminfo.getOpen_id()));
                        } else if (info instanceof ResHostStoragePoolApiModel) {
                            ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
                            perf.setResId(host.getRes_id());
                            perf.setOpenId(host.getOpen_id());
                            perf.setOpenName(host.getOpen_name());
                            perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
                            perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
                            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                        }
                        perf.setCloudType(request.getPlugin().getRealm());
                        perf.setAccountId(request.getBody().getAccess().getCmpId());
                        perf.setCreateTime(createTime);
                        perf.setId(id);
                    }
                    BiConsumer<PerfInfoBean, Double> setValue = Converts.perfMapping.get(metricName);
                    setValue.accept(perf, average);//设置监控指标值
                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, Map<String, List<DescribeSnapshotsResponse.Snapshot>> snapshotList) {
        Map<Class, List> result = new HashMap<>();
        if (null == snapshotList || CollUtil.isEmpty(snapshotList)) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Map.Entry<String, List<DescribeSnapshotsResponse.Snapshot>> entry : snapshotList.entrySet()) {
            entry.getValue().forEach(res -> {
                CmdbSnapshotRes ci = new CmdbSnapshotRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), entry.getKey(), res.getId()));
                ci.setOpen_id(res.getId());
                ci.setOpen_name(res.getName());
                ci.setDesc(res.getDescription());
                ci.setP_snaps_id(res.getParentId());
                ci.setOpen_create_time(DateUtil.parse(res.getCreatedAt()).getTime());
                toCiResCloud(request, ci);
                data.add(ci);
                //关联云主机
                Association association = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), entry.getKey()));
                associations.add(association);
            });
        }
        result.put(CmdbSnapshotRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertAlarm(BaseCloudRequest request, DescribeAlarmsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(AlarmInfoBean.class, null);
            return result;
        }
        List<AlarmInfoBean> data = new ArrayList<>();
        for (DescribeAlarmsResponse.Alarm alarm : response.body.data.data) {
            AlarmInfoBean ci = new AlarmInfoBean();
            ci.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alarm.getId()));
            ci.setAccountId(request.getBody().getAccess().getCmpId());
            ci.setCloudType(request.getPlugin().getRealm());
            ci.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alarm.getId()));
            ci.setOpenId(alarm.getId());
            ci.setOpenName(alarm.getTitle());
            ci.setOpenLevel(alarm.getLevel());
            ci.setAlarmId(alarm.getId());
            ci.setAlarmName(alarm.getTitle());
            ci.setDetail(alarm.getDescription() + alarm.getObjectName());
            ci.setClosedStatus(alarm.getUnreadId() == -1);
            ci.setJsonInfo(JSON.toJSONString(alarm));
            ci.setCount(1);
            if (ObjectUtil.isNotEmpty(alarm.getStartTime())) {
                ci.setFirstTime(alarm.getStartTime());
                ci.setCreateTime(alarm.getStartTime());
            }
            ci.setResourceType(alarm.getObjectType());
            switch (alarm.getLevel()) {
                case "p1":
                    ci.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case "p2":
                    ci.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case "p3":
                    ci.setAlarmLevel(AlarmLevel.MINOR.value());
                    break;
            }
            data.add(ci);
        }
        result.put(AlarmInfoBean.class, data);
        return result;
    }
}