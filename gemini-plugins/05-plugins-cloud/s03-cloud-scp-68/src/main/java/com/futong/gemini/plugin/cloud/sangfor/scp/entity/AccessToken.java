package com.futong.gemini.plugin.cloud.sangfor.scp.entity;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccessToken implements Serializable {
    private Date expires;
    private String id;
    public boolean invalid() {
        return expires == null || DateUtil.date().offset(DateField.MINUTE, 20).isAfter(expires);
    }
}
