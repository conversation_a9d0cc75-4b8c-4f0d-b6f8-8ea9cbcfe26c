package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeInstanceResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesResponseBody body;

    @Data
    public static class DescribeInstancesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public Instance data;
    }

    @Data
    public static class Instance extends TeaModel {

        @NameInMap("id")
        public String id; // 云主机UUID，必返

        @NameInMap("status")
        public String status; // 云主机状态，必返

        @NameInMap("name")
        public String name; // 云主机名称，必返

        @NameInMap("vm_type")
        public String vmType; // 云主机类型，非必返

        @NameInMap("description")
        public String description; // 云主机描述，非必返

        @NameInMap("group_id")
        public String groupId; // 云主机分组ID，必返

        @NameInMap("group_id_path")
        public String groupIdPath; // 云主机分组ID路径，必返

        @NameInMap("group_name")
        public String groupName; // 分组名称，必返

        @NameInMap("storage_name")
        public String storageName; // 存储名称，非必返

        @NameInMap("host_name")
        public String hostName; // 主机名称，非必返

        @NameInMap("project_id")
        public String projectId; // 租户ID，必返

        @NameInMap("project_name")
        public String projectName; // 租户名称，必返

        @NameInMap("user_id")
        public String userId; // 用户ID，必返

        @NameInMap("cores")
        public Integer cores; // CPU总核数，必返

        @NameInMap("sockets")
        public Integer sockets; // socket数目，必返

        @NameInMap("memory_mb")
        public Integer memoryMb; // 内存大小(MB)，必返

        @NameInMap("location")
        public Location location; // 运行位置，必返（见Location类）

        @NameInMap("storage_type")
        public String storageType; // 存储类型，非必返

        @NameInMap("storage_location")
        public String storageLocation; // 存储位置，非必返

        @NameInMap("storage_tag_id")
        public String storageTagId; // 存储标签ID，必返

        @NameInMap("storage_tag_name")
        public String storageTagName; // 存储标签名称，必返

        @NameInMap("az_id")
        public String azId; // 资源池ID，非必返

        @NameInMap("az_name")
        public String azName; // 资源池名称，非必返

        @NameInMap("pool_tag")
        public String poolTag; // 资源池用途标签，非必返

        @NameInMap("dh_id")
        public String dhId; // 专属服务器组ID，非必返

        @NameInMap("dh_tag")
        public String dhTag; // 专属服务器组用途标签，非必返

        @NameInMap("disks")
        public List<Disk> disks; // 磁盘信息，必返（见Disk类）

        @NameInMap("networks")
        public List<Network> networks; // 网络信息，必返（见Network类）

        @NameInMap("cdroms")
        public List<Cdrom> cdroms; // 光驱信息，非必返（见Cdrom类）

        @NameInMap("usbs")
        public List<Usb> usbs; // USB信息，非必返（见Usb类）

        @NameInMap("os_type")
        public String osType; // 操作系统类型，非必返

        @NameInMap("os_name")
        public String osName; // 操作系统名称，非必返

        @NameInMap("image_name")
        public String imageName; // 镜像名称，非必返

        @NameInMap("os_installed")
        public Integer osInstalled; // 是否安装了操作系统，非必返

        @NameInMap("vtool_installed")
        public Integer vtoolInstalled; // 是否安装了优化工具，必返

        @NameInMap("ha")
        public Integer ha; // 是否是HA云主机，非必返

        @NameInMap("snap")
        public Integer snap; // 是否有快照，非必返

        @NameInMap("backup")
        public Integer backup; // 是否有备份，非必返

        @NameInMap("derived")
        public Integer derived; // 是否是派生云主机，非必返

        @NameInMap("hotplug_sup_info")
        public HotplugSupInfo hotplugSupInfo; // 热添加支持情况，非必返（见HotplugSupInfo类）

        @NameInMap("floatingip")
        public FloatingIp floatingIp; // 弹性IP信息，非必返（见FloatingIp类）

        @NameInMap("advance_param")
        public AdvanceParam advanceParam; // HCI特有的参数，非必返（见AdvanceParam类）

        @NameInMap("instant_vm")
        public String instantVm; // 是否为快速恢复云主机，非必返

        @NameInMap("encrypted")
        public Integer encrypted; // 是否加密，必返

        @NameInMap("cipher")
        public String cipher; // 加密算法，必返

        @NameInMap("gpu")
        public Gpu gpu; // GPU配置信息，非必返（见Gpu类）

        @NameInMap("machine_type")
        public String machineType; // 主板类型，非必返

        @NameInMap("arch_type")
        public String archType; // 集群架构类型，非必返

    }

    @Data
    public static class Location extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("type")
        public String type;

        @NameInMap("location")
        public Integer location;

        @NameInMap("policy_type")
        public String policyType;
    }

    @Data
    public static class Disk extends TeaModel {
        @NameInMap("size_mb")
        public Float sizeMb;

        @NameInMap("is_old_disk")
        public Integer isOldDisk;

        @NameInMap("preallocate")
        public String preallocate;

        @NameInMap("eagerly_scrub")
        public Integer eagerlyScrub;

        @NameInMap("id")
        public String id;

        @NameInMap("type")
        public String type;

        @NameInMap("storage_file")
        public String storageFile;

        @NameInMap("storage_id")
        public String storageId;

        @NameInMap("storage_name")
        public String storageName;

        @NameInMap("backing_file")
        public String backingFile;

        @NameInMap("label")
        public String label;

        @NameInMap("use_virtio")
        public Integer useVirtio;

        @NameInMap("storage_policy")
        public String storagePolicy;
    }

    @Data
    public static class Network extends TeaModel {
        @NameInMap("connect")
        public Integer connect;

        @NameInMap("device_id")
        public String deviceId;

        @NameInMap("model")
        public String model;

        @NameInMap("label")
        public String label;

        @NameInMap("ip_address")
        public String ipAddress;

        @NameInMap("ipv6_address")
        public String ipv6Address;

        @NameInMap("mac_address")
        public String macAddress;

        @NameInMap("vif_id")
        public String vifId;

        @NameInMap("port_id")
        public String portId;

        @NameInMap("host_tso")
        public Integer hostTso;

        @NameInMap("name")
        public String name;

        @NameInMap("vpc_id")
        public String vpcId;

        @NameInMap("vpc_name")
        public String vpcName;

        @NameInMap("network_type")
        public String networkType;

        @NameInMap("subnet_id")
        public String subnetId;

        @NameInMap("subnet_name")
        public String subnetName;

        @NameInMap("cidr")
        public String cidr;

        @NameInMap("subnet_gateway_ip")
        public String subnetGatewayIp;

        @NameInMap("custom_gateway_ip")
        public String customGatewayIp;

        @NameInMap("ip_info")
        public IpInfo ipInfo;

        @NameInMap("ipv6_info")
        public Ipv6Info ipv6Info;
    }

    @Data
    public static class IpInfo extends TeaModel {
        @NameInMap("ip_address")
        public String ipAddress;

        @NameInMap("netmask")
        public String netmask;

        @NameInMap("gateway")
        public String gateway;

        @NameInMap("dns")
        public List<String> dns;
    }

    @Data
    public static class Ipv6Info extends TeaModel {
        @NameInMap("ipv6_address")
        public String ipv6Address;

        @NameInMap("prefixlen")
        public String prefixlen;

        @NameInMap("gateway")
        public String gateway;

        @NameInMap("dns")
        public List<String> dns;
    }

    @Data
    public static class Cdrom extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("image_id")
        public String imageId;

        @NameInMap("location")
        public String location;

        @NameInMap("storage_name")
        public String storageName;

        @NameInMap("image_name")
        public String imageName;
    }

    @Data
    public static class Usb extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("usb_device")
        public String usbDevice;

        @NameInMap("controller_type")
        public String controllerType;

        @NameInMap("online")
        public Integer online;

        @NameInMap("description")
        public String description;
    }

    @Data
    public static class HotplugSupInfo extends TeaModel {
        @NameInMap("cpu")
        public Integer cpu;

        @NameInMap("memory")
        public Integer memory;
    }

    @Data
    public static class FloatingIp extends TeaModel {
        @NameInMap("floatingip_id")
        public String floatingipId;

        @NameInMap("floatingip_ip")
        public String floatingIpAddress;

        @NameInMap("line_type_name")
        public String lineTypeName;

        @NameInMap("line_type_id")
        public String lineTypeId;

        @NameInMap("bandwidth")
        public Integer bandwidth;

        @NameInMap("sharedbandwidth_name")
        public String sharedbandwidthName;

        @NameInMap("sharedbandwidth_id")
        public String sharedbandwidthId;
    }

    @Data
    public static class AdvanceParam extends TeaModel {
        @NameInMap("cpu_type")
        public String cpuType;

        @NameInMap("onboot")
        public Integer onboot;

        @NameInMap("use_uuid")
        public Integer useUuid;

        @NameInMap("uuid")
        public String uuid;

        @NameInMap("abnormal_recovery")
        public Integer abnormalRecovery;

        @NameInMap("hugepage_memory")
        public Integer hugepageMemory;

        @NameInMap("balloon_memory")
        public Integer balloonMemory;

        @NameInMap("schedopt")
        public Integer schedopt;

        @NameInMap("cpu_hotplug")
        public Integer cpuHotplug;

        @NameInMap("mem_hotplug")
        public Integer memHotplug;

        @NameInMap("boot_order")
        public String bootOrder;

        @NameInMap("boot_disk")
        public String bootDisk;

        @NameInMap("dir")
        public String dir;

        @NameInMap("use_vblk")
        public Integer useVblk;

        @NameInMap("real_use_vblk")
        public Integer realUseVblk;

        @NameInMap("storage_policy")
        public String storagePolicy;
    }

    @Data
    public static class Gpu extends TeaModel {
        @NameInMap("vendor")
        public String vendor;

        @NameInMap("conf")
        public GpuNvidia conf;
    }

    @Data
    public static class GpuNvidia extends TeaModel {
        @NameInMap("total_count")
        public Integer totalCount;

        @NameInMap("enable_count")
        public Integer enableCount;

        @NameInMap("gpu_type")
        public String gpuType;

        @NameInMap("slice_type")
        public String sliceType;

        @NameInMap("schedulers")
        public List<String> schedulers;

        @NameInMap("vgpu_sched_mode")
        public String vgpuSchedMode;

        @NameInMap("frame_rate")
        public Integer frameRate;
    }

}

