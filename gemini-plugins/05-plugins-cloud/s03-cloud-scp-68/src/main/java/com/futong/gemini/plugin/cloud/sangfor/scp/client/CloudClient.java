package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import cn.hutool.core.util.StrUtil;
import com.aliyun.teaopenapi.models.Config;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.sangfor.scp.entity.AccessToken;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;

public class CloudClient extends BaseCloudClient {

    public static final CloudClient client = new CloudClient();

    //获取Client对象
    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        String endpoint = body.getAuth().getString("endpoint");
//        String regionId = body.getCloud().getString("RegionId");
//        String cloudType = body.getAccess().getCloudType();
//        JSONObject authOther = new JSONObject();
        C client = null;
        try {
            //无需特殊处理的 Client 对象
            Config config = new Config();
            config.setAccessKeyId(body.getAccess().getUsername());
            config.setAccessKeySecret(body.getAccess().getPassword());
            //获取token
            AccessToken accessToken = HttpClientUtil.getToken(body.getAccess());
            config.setSecurityToken(accessToken.getId());
            config.setRegionId(body.getCloud().getString("regionId"));
            config.setProtocol(body.getAccess().getProtocol()==null?"https":body.getAccess().getProtocol().toLowerCase());
            if (StrUtil.isNotEmpty(endpoint)) {
                config.setEndpoint(endpoint);
            } else if (StrUtil.isNotEmpty(body.getAccess().getServerIp()) && StrUtil.isNotEmpty(body.getAccess().getServerPort())) {
                config.setEndpoint(body.getAccess().getServerIp()+":"+body.getAccess().getServerPort());
            } else {
                config.setEndpoint("**************:30990");
            }
            String proxyAddr;
            if (StrUtil.isNotEmpty(proxyAddr = body.getAuth().getString("proxyAddr"))) {
                config.setHttpProxy(proxyAddr);
            }
            client = clazz.getConstructor(Config.class).newInstance(config);
            return client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

}
