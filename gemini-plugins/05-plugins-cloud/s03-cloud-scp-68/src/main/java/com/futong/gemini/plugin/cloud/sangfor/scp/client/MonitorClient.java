package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class MonitorClient extends ScpClient {

    public MonitorClient(Config config) throws Exception {
        super(config);
    }

    public DescribeMetricsResponse describeMetrics(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/metrics/{object_id}", request);
        request.remove("object_id");
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", path, "json", req, runtime);
        return TeaModel.toModel(map, new DescribeMetricsResponse());
    }

}
