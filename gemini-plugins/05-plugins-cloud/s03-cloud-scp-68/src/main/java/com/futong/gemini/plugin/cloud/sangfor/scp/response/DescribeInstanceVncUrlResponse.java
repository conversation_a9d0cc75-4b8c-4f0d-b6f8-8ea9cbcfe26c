package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.Map;

@Data
public class DescribeInstanceVncUrlResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesResponseBody body;

    @Data
    public static class DescribeInstancesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeInstancesResponseBodyData data;
    }

    @Data
    public static class DescribeInstancesResponseBodyData extends BaseResponseData {
        @NameInMap("remote_console")
        public InstanceVncUrl data;
    }

    @Data
    public static class InstanceVncUrl extends TeaModel {
        @NameInMap("url")
        public String url;

        @NameInMap("type")
        public String type;

        @NameInMap("protocol")
        public String protocol;

    }

}

