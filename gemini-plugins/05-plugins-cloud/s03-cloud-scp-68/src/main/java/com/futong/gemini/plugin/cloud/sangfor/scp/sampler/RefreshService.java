package com.futong.gemini.plugin.cloud.sangfor.scp.sampler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.OperationClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.convert.Converts;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeInstanceResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class RefreshService {

    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("starting");//开机中
            add("stoping");//关机中
            add("clone");//克隆中
            add("backup");//备份中
            add("recovery");//恢复中
            add("run_backup");//备份中
            add("resetting");//重启中
            add("suspending");//挂起中
            add("updating");//更新中
            add("deleting");//删除中
            add("snapshotting");//快照中
            add("creating");//创建中
            add("migrate");//迁移中
            add("deriving");//部署中
            add("tpl_updating");//更新中
        }
    };

    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<DescribeInstanceResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                OperationClient::describeInstance,
                Converts::convertRefreshEcs);
        List<CmdbInstanceRes> res = mapE2.v2.get(CmdbInstanceRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
            cmdbInstanceRes.setRes_id(resId);
            List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbInstanceRes cmdbInstanceRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }
}
