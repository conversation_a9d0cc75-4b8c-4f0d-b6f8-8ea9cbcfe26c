package com.futong.gemini.plugin.cloud.sangfor.scp.sampler;

import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.EcsClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.OperationClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribePublicKeyResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.RunInstanceResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OperationService {

    public static BaseResponse updateInstancePassword(BaseCloudRequest request) {
        DescribePublicKeyResponse publicKeyResponse = CloudClient.client.execute(request.getBody(), EcsClient::describePublicKey);
        String publicKey = publicKeyResponse.getBody().getData().getPublicKey();
        // 将 publicKey 添加到请求参数中
        request.getBody().getCloud().put("public_key", publicKey);
        CloudClient.client.execute(request.getBody(), OperationClient::updateInstancePassword);
        return BaseResponse.SUCCESS.of("任务发起成功");
    }

    public static BaseResponse createSnapshot(BaseCloudRequest request) {
        CloudClient.client.execute(request.getBody(), OperationClient::createSnapshot);
        return BaseResponse.SUCCESS.of("任务发起成功");
    }

    public static BaseResponse cloneInstance(BaseCloudRequest request) {
        RunInstanceResponse response = CloudClient.client.execute(request.getBody(), OperationClient::cloneInstance);
        BaseDataResponse<RunInstanceResponse> runInstanceResponseBaseDataResponse = new BaseDataResponse<>();
        runInstanceResponseBaseDataResponse.setData(response);
        return runInstanceResponseBaseDataResponse;
    }

    public static BaseResponse migrateInstance(BaseCloudRequest request) {
        CloudClient.client.execute(request.getBody(), OperationClient::migrateInstance);
        return BaseResponse.SUCCESS.of("任务发起成功");
    }
}
