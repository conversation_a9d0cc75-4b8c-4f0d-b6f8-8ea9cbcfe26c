package com.futong.gemini.plugin.cloud.sangfor.scp.utils;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

public class RSAEncryptor {

    public static String encryptWithModulus(String content, String modulusHex) {
        try {
            BigInteger modulus = new BigInteger(modulusHex, 16);
            BigInteger exponent = new BigInteger("10001", 16);

            RSA rsa = new RSA(modulus, null, exponent);

            return HexUtil.encodeHexStr(rsa.encrypt(content.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey)).toLowerCase();
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    public static void main(String[] args) {
        String public_key = "B142B656B62BF2C4950FA46418A8EAEC25BD44726B8431AC9156641D211D502550344C03A1825AED8D13720E7C1DE4F63AA05FECAD2CABED115F32F8B1E650B1FB0DE03E67A3C3F7A0FBA3F6BF3C1813BAD3167C44486B74E2BA9B7DDA4583F38C0AB0EAA1ED0349B11219883612E5BDFF5BB689F73F1A3C521E02B54F19D43CABBA8E7DBE2833CF2C422CD6ABD1579F36DB8BDE034F894792D3937C8ECDE981E63C43448D28F3E327A7D16BBB3FF4CF346F71DBB367D44FC7A706A65B9A7557F6A7462E55366F14F1D806DB16AC46E8A344FEA3F4281A47D6E4CF91011DB485D9B8F259E0A52EE4D93CA21C91810EF0C4C459FEABFDAAF9B4746A79CBB5C81D\n";
        String encrypt = encryptWithModulus("123456", StrUtil.replace(public_key, "\n", ""));
        System.out.println(encrypt);
    }
}
