package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeEipPoolsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeEipPoolResponseBody body;
    @Data
    public static class DescribeEipPoolResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeEipPoolResponseBodyData data;
    }

    @Data
    public static class DescribeEipPoolResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<EipPool> data;
    }

    @Data
    public static class EipPool extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("line_type_id")
        public String lineTypeId;

        @NameInMap("description")
        public String description;

        @NameInMap("is_global")
        public Integer isGlobal;

        @NameInMap("az_ids")
        public List<String> azIds;

        @NameInMap("vlan")
        public Integer vlan;

        @NameInMap("ip_count")
        public Integer ipCount;

        @NameInMap("qos_downlink")
        public Integer qosDownlink;

        @NameInMap("qos_uplink")
        public Integer qosUplink;

        @NameInMap("allocation_pools")
        public List<AllocationPool> allocationPools;
    }

    @Data
    public static class AllocationPool extends TeaModel {
        @NameInMap("start")
        public String start;

        @NameInMap("end")
        public String end;
    }
}
