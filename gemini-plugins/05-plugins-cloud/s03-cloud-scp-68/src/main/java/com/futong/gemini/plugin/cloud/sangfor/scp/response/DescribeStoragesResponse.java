package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeStoragesResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeStorageResponseBody body;
    @Data
    public static class DescribeStorageResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeStorageResponseBodyData data;
    }

    @Data
    public static class DescribeStorageResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Storage> data;
    }

    @Data
    public static class Storage extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("type")
        public String type;

        @NameInMap("status")
        public String status;

        @NameInMap("storage_tag_id")
        public String storageTagId;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("target")
        public String target;

        @NameInMap("total_mb")
        public Integer totalMb;

        @NameInMap("used_mb")
        public Integer usedMb;

        @NameInMap("ratio")
        public Float ratio;

        @NameInMap("read_byteps")
        public Float readByteps;

        @NameInMap("write_byteps")
        public Float writeByteps;

        @NameInMap("max_read_byteps")
        public Float maxReadByteps;

        @NameInMap("max_write_byteps")
        public Float maxWriteByteps;
    }

}
