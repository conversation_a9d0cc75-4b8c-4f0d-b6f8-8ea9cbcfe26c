package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeClusterResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeClusterResponseBody body;
    @Data
    public static class DescribeClusterResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeClusterResponseBodyData data;
    }

    @Data
    public static class DescribeClusterResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Cluster> data;
    }

    @Data
    public static class Cluster extends TeaModel {
        @NameInMap("id")
        public String id;
        @NameInMap("name")
        public String name;
        @NameInMap("version")
        public String version;
        @NameInMap("type")
        public String type;
        @NameInMap("description")
        public String description;
        @NameInMap("status")
        public String status;
        @NameInMap("az_id")
        public String azId;
        @NameInMap("project_ids")
        public List<String> projectIds;
        @NameInMap("authorize_mode")
        public String authorizeMode;
        @NameInMap("cpu")
        public Cpu cpu;
        @NameInMap("memory")
        public Memory memory;
        @NameInMap("storage")
        public Storage storage;

    }

    @Data
    public static class Cpu extends TeaModel {
        @NameInMap("total_mhz")
        public Float totalMhz;
        @NameInMap("used_mhz")
        public Float usedMhz;
        @NameInMap("ratio")
        public Float ratio;
    }

    @Data
    public static class Memory extends TeaModel {
        @NameInMap("total_mb")
        public Float totalMb;
        @NameInMap("used_mb")
        public Float usedMb;
        @NameInMap("ratio")
        public Float ratio;
    }

    @Data
    public static class Storage extends TeaModel {
        @NameInMap("total_mb")
        public Float totalMb;
        @NameInMap("used_mb")
        public Float usedMb;
        @NameInMap("ratio")
        public Float ratio;
    }
}
