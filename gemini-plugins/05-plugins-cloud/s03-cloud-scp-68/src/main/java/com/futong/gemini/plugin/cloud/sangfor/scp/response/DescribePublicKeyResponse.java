package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.Map;

@Data
public class DescribePublicKeyResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public PublicKeyResponseBody body;

    @Data
    public static class PublicKeyResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public PublicKeyResponseBodyData data;
    }

    @Data
    public static class PublicKeyResponseBodyData  extends TeaModel {
        @NameInMap("public_key")
        public String publicKey;
    }
}
