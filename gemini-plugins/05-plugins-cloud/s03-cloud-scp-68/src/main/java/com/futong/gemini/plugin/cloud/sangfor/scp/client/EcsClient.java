package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import cn.hutool.core.util.StrUtil;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class EcsClient extends ScpClient {

    public EcsClient(Config config) throws Exception {
        super(config);
    }

    public DescribeProjectsResponse describeProject(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/projects", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeProjectsResponse());
    }

    public DescribeAZonesResponse describeAZone(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/azs", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeAZonesResponse());
    }

    public DescribeClusterResponse describeCluster(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/clusters", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeClusterResponse());
    }

    public DescribeImagesResponse describeImages(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/images", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeImagesResponse());
    }

    public DescribeStoragesResponse describeStorages(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/storages", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeStoragesResponse());
    }

    public DescribeHostsResponse describeHosts(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/hosts", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeHostsResponse());
    }

    public DescribeInstancesResponse describeInstances(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/servers", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeInstancesResponse());
    }

    public DescribeSnapshotsResponse describeSnapshots(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/snapshots", request);
        request.remove("server_id");
        request.remove("object_type");
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", path, "json", req, runtime);
        return TeaModel.toModel(map, new DescribeSnapshotsResponse());
    }

    public DescribeAlarmsResponse describeAlarms(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20180725/alarms", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeAlarmsResponse());
    }

    public DescribePublicKeyResponse describePublicKey(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/public-key", "json", req, runtime);
        return TeaModel.toModel(map, new DescribePublicKeyResponse());
    }

}
