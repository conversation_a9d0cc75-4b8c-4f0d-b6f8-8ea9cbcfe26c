package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RunInstanceResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesResponseBody body;

    @Data
    public static class DescribeInstancesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeInstancesResponseBodyData data;
    }

    @Data
    public static class DescribeInstancesResponseBodyData extends TeaModel {
        @NameInMap("task_id")
        public String taskId;

        @NameInMap("uuids")
        public List<String> uuids;
    }
}

