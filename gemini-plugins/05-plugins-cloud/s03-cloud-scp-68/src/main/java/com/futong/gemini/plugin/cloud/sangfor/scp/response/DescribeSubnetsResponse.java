package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeSubnetsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeSubnetResponseBody body;
    @Data
    public static class DescribeSubnetResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeSubnetResponseBodyData data;
    }

    @Data
    public static class DescribeSubnetResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Subnet> data;
    }

    @Data
    public static class Subnet extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("vpc_id")
        public String vpcId;

        @NameInMap("cidr")
        public String cidr;

        @NameInMap("status")
        public String status;

        @NameInMap("gateway_ip")
        public String gatewayIp;

        @NameInMap("is_visible")
        public Integer isVisible;

        @NameInMap("enable_dhcp")
        public Integer enableDhcp;

        @NameInMap("shared")
        public Integer shared;

        @NameInMap("allocation_pools")
        public List<AllocationPool> allocationPools;
    }

    @Data
    public static class AllocationPool extends TeaModel {
        @NameInMap("start")
        public String start;

        @NameInMap("end")
        public String end;
    }
}
