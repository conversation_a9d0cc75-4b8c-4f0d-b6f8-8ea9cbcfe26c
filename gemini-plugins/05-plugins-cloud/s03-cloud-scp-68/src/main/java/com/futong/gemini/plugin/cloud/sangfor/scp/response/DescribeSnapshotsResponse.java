package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeSnapshotsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeSnapshotsResponseBody body;

    @Data
    public static class DescribeSnapshotsResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public List<Snapshot> data;
    }

    @Data
    public static class Snapshot extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("parent_id")
        public String parentId;

        @NameInMap("created_at")
        public String createdAt;

        @NameInMap("is_latest")
        public Integer isLatest;
    }

}

