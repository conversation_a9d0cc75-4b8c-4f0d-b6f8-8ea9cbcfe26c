package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.Map;

@Data
public class OperationInstanceResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public OperationInstancesResponseBody body;

    @Data
    public static class OperationInstancesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public OperationInstancesResponseBodyData data;
    }

    @Data
    public static class OperationInstancesResponseBodyData extends TeaModel {
        @NameInMap("task_id")
        public String taskId;

    }
}

