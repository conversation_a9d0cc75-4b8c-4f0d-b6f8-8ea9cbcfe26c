package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeImagesResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeImageResponseBody body;
    @Data
    public static class DescribeImageResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeImageResponseBodyData data;
    }

    @Data
    public static class DescribeImageResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Image> data;
    }

    @Data
    public static class Image extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("image_type")
        public String imageType;

        @NameInMap("status")
        public String status;

        @NameInMap("os_type")
        public String osType;

        @NameInMap("visibility")
        public String visibility;

        @NameInMap("disk_format")
        public String diskFormat;

        @NameInMap("owner_id")
        public String ownerId;

        @NameInMap("size_mb")
        public Float sizeMb;

        @NameInMap("virtual_size_mb")
        public Float virtualSizeMb;

        @NameInMap("category")
        public String category;

        @NameInMap("is_vmtools_installed")
        public Integer isVmtoolsInstalled;

        @NameInMap("has_gpu")
        public Integer hasGpu;

        @NameInMap("message")
        public String message;

        @NameInMap("task_id")
        public String taskId;

        @NameInMap("support_versions")
        public List<String> supportVersions;

        @NameInMap("azs")
        public List<Az> azs;

        @NameInMap("disks")
        public List<Disk> disks;

        @NameInMap("image_members")
        public List<ImageMember> imageMembers;
    }

    @Data
    public static class Disk extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("storage_id")
        public String storageId;

        @NameInMap("size_mb")
        public Float sizeMb;

        @NameInMap("preallocate")
        public String preallocate;

        @NameInMap("tpl_config")
        public TplConfig tplConfig;
    }

    @Data
    public static class Az extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("is_available")
        public String isAvailable;

        @NameInMap("arch_type")
        public String archType;
    }

    @Data
    public static class TplConfig extends TeaModel {
        @NameInMap("mem_hotplug")
        public Integer memHotplug;

        @NameInMap("cpu_hotplug")
        public Integer cpuHotplug;

        @NameInMap("machine_type")
        public String machineType;
    }

    @Data
    public static class ImageMember extends TeaModel {
        @NameInMap("member_id")
        public String memberId;

        @NameInMap("status")
        public String status;
    }
}
