package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.futong.gemini.plugin.cloud.sangfor.scp.http.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ScpClient extends Client {

    public ScpClient(Config config) throws Exception {
        super(config);
    }

    public Map<String, ?> doRequest(String protocol, String method, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        if (CollUtil.isEmpty(request.getHeaders())) {
            request.setHeaders(new HashMap<>());
        }
        request.setHeaders(MapUtil.of("Authorization", "Token " + this.getSecurityToken()));
        return doRPCRequest(protocol, method, pathname, bodyType, request, runtime);
    }

    /**
     * 发起请求
     *
     * @param protocol 协议类型 HTTPS HTTP
     * @param method   请求方式 GET POST DELETE PUT
     * @param pathname 请求路径
     * @param bodyType 返回类型
     * @param request  请求数据
     * @param runtime
     * @return
     * @throws Exception
     */
    public Map<String, ?> doRPCRequest(String protocol, String method, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(60 * 1000, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            //处理 Headers 信息
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                Map<String, String> headers = this.getRpcHeaders();
                if (com.aliyun.teautil.Common.isUnset(headers)) {
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint)
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class, TeaConverter.buildMap(new TeaPair("host", _endpoint)), headers);
                }
                //优先使用自定义的 header
                if (CollUtil.isNotEmpty(request.getHeaders())) {
                    request_.headers.putAll(request.getHeaders());
                }
                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
                    //log.info("请求body：{}", JSONObject.toJSONString(request.body));
                    request_.body = Tea.toReadable(JSONObject.toJSONString(request.body));
                    request_.headers.put("content-type", "application/json;charset=UTF-8");
                }
                request_.headers.put("accept", "application/json,text/plain,*/*");
                //转换 query 参数格式计算签名使用 并用于拼接域名
                StringBuilder queryStr = new StringBuilder();
                if (CollUtil.isNotEmpty(request.getQuery())) {
                    for (Map.Entry<String, String> entry : request.getQuery().entrySet()) {
                        queryStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                    }
                    queryStr = new StringBuilder(queryStr.substring(0, queryStr.length() - 1));
                }
                if (!"".equals(queryStr.toString())) {
                    request_.pathname = pathname + "?" + queryStr;
                }
                //发起请求
                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                //log.info("请求参数：{}",JSONObject.toJSONString(request_));
                _lastResponse = response_;

                //解析返回结果
                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("code"), response_.statusCode)),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("message"), err.get("message")) + " request id: " + requestId),
                            new TeaPair("description", Client.defaultAny(err.get("description"), "")),
                            new TeaPair("data", err)
                    ));
                }
                if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    //log.info("接口返回：{}", StrUtil.sub(JSONObject.toJSONString(res), 0, 2000));
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

}
