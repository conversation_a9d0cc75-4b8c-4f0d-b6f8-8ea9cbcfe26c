package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeMetricsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeMetricsResponseBody body;
    @Data
    public static class DescribeMetricsResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeMetricResponseBodyData data;
    }


    @Data
    public static class DescribeMetricResponseBodyData extends TeaModel {
        @NameInMap("cpu.util")
        public Metric cpuUtil;

        @NameInMap("memory.util")
        public Metric memoryUtil;

        //@NameInMap("disk.util")
        //public Metric diskUtil;

        @NameInMap("io.read.speed")
        public Metric ioReadSpeed;

        @NameInMap("io.write.speed")
        public Metric ioWriteSpeed;

        @NameInMap("net.in.bps")
        public Metric netInBps;

        @NameInMap("net.out.bps")
        public Metric netOutBps;

    }

    @Data
    public static class Metric extends TeaModel {
        @NameInMap("datapoints")
        public List<List<Object>> datapoints;

        @NameInMap("unit")
        public String unit;

        @NameInMap("granularity")
        public Integer granularity;
    }
}
