package com.futong.gemini.plugin.cloud.sangfor.scp;

import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.OperationClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.sampler.FetchService;
import com.futong.gemini.plugin.cloud.sangfor.scp.sampler.OperationService;
import com.futong.gemini.plugin.cloud.sangfor.scp.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.sangfor.scp.service.AccountService;
import com.futong.gemini.plugin.cloud.sangfor.scp.service.CapacityResourceService;
import com.futong.gemini.plugin.cloud.sangfor.scp.service.CloudService;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class ScpRegister extends BaseCloudRegister {
    public <Q, R, C> Builder register(ActionType actionType, FTExecute<Q, R, C> execute) {
        return register(actionType, execute, CloudService::toFTAction);
    }

    @Override
    public void load() {
        onAfterLoadAccount();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadCompute();//加载云主机操作
    }

    public void onAfterLoadAccount() {
        //云账号验证
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authCloudAccount);
        //获取云账号表单信息
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);//获取调度添加模型
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);//添加默认调度任务
    }

    public void onAfterLoadFetch() {
        //默认查询页码及条数50
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_PLATFORM_PROJECT,
                ActionType.FETCH_PLATFORM_AZONE,
                ActionType.FETCH_PLATFORM_CLUSTER,
                ActionType.FETCH_STORAGE_IMAGE,
                ActionType.FETCH_NEUTRON_EIP,
                ActionType.FETCH_STORAGE_POOL,
                ActionType.FETCH_NEUTRON_VPC,
                ActionType.FETCH_NEUTRON_SUBNET,
                ActionType.FETCH_COMPUTE_HOST,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_NEUTRON_ROUTE,
                ActionType.FETCH_NEUTRON_SWITCH,
                ActionType.FETCH_PLATFORM_ALARM
        );
        //同步组织
        register(ActionType.FETCH_PLATFORM_PROJECT, FetchService::fetchProject);
        //同步可用区
        register(ActionType.FETCH_PLATFORM_RESOURCE_POOL, FetchService::fetchAZone);
        //同步集群
        register(ActionType.FETCH_PLATFORM_CLUSTER, FetchService::fetchCluster);
        //获取镜像
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage);
        //获取EIP
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip);
        //获取磁盘
        register(ActionType.FETCH_STORAGE_POOL, FetchService::fetchStorage);
        //获取VPC
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc);
        //获取Subnet
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);
        //同步物理机
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost);
        //同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);
        //同步路由器
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRouter);
        //同步交换机
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchDVSwitch);
        //同步物理出口
        register(ActionType.FETCH_NEUTRON_VLAN, FetchService::fetchBVSwitch);
        //同步端口
        register(ActionType.FETCH_NEUTRON_NIC, FetchService::fetchPort);
        //同步云主机数量
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL, CapacityResourceService::queryInstanceTotal);
        //同步物理机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL, CapacityResourceService::queryHostTotal);
        //获取云主机监控,预处理
        registerBefore(ActionType.FETCH_COMPUTE_INSTANCE_PERF,
                FetchService::defaultMetricRequest,
                FetchService::defaultMetricNames,
                FetchService::defaultEcsMetricDimensions);
        //获取云主机监控
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchPerf);
        //获取物理机监控,预处理
        registerBefore(ActionType.FETCH_COMPUTE_HOST_PERF,
                FetchService::defaultMetricRequest,
                FetchService::defaultMetricNames,
                FetchService::defaultHostMetricDimensions);
        //获取物理机监控
        register(ActionType.FETCH_COMPUTE_HOST_PERF, FetchService::fetchPerf);
        registerBefore(ActionType.FETCH_STORAGE_SNAPSHOT,
                FetchService::defaultEcsMetricDimensions);
        //获取云主机快照
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot);
        //获取告警信息
        registerBefore(ActionType.FETCH_PLATFORM_ALARM,
                CloudService::defaultStartEndTimeOneDay);
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);
    }

    //加载云主机操作
    public void onAfterLoadCompute() {
        //创建云主机
        register(ActionType.CREATE_COMPUTE_INSTANCE, OperationClient::runInstance)
                .addBefore(CloudService::toBeforeBiz)
                .addAfter(CloudService::toBizResId)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 10000, 10000)
                .addSetRefreshSplitData("response","$.data.body.data.uuids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改云主机
        register(ActionType.UPDATE_COMPUTE_INSTANCE, OperationClient::updateInstance)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 10000, 30000)
                .addSetRefreshSplitData("request","$.body.cloud.server_id")
                .addAfter(BaseCloudService::addRefreshGourdJob);;
        //获取云主机的VNC远程登录地址
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, OperationClient::instanceVncUrl)
                .addTransferCloud("$.ci.openId", "$.server_id");
        //批量删除云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, OperationClient::deleteInstance)
                .addTransferCloud("$.cis.openId", "$.server_ids")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 10000, 10000)
                .addSetRefreshSplitData("request","$.body.cloud.server_ids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量开启云主机
        register(ActionType.START_COMPUTE_INSTANCE, OperationClient::startInstance)
                .addTransferCloud("$.cis.openId", "$.server_ids")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 5000)
                .addSetRefreshSplitData("request","$.body.cloud.server_ids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量关闭云主机
        register(ActionType.STOP_COMPUTE_INSTANCE, OperationClient::stopInstance)
                .addTransferCloud("$.cis.openId", "$.server_ids")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 5000)
                .addSetRefreshSplitData("request","$.body.cloud.server_ids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量挂起云主机
        register(ActionType.SUSPEND_COMPUTE_INSTANCE, OperationClient::suspendInstance)
                .addTransferCloud("$.cis.openId", "$.server_ids")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 5000)
                .addSetRefreshSplitData("request","$.body.cloud.server_ids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量重启云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, OperationClient::rebootInstance)
                .addTransferCloud("$.cis.openId", "$.server_ids")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 5000)
                .addSetRefreshSplitData("request","$.body.cloud.server_ids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改云主机密码
        register(ActionType.UPDATE_COMPUTE_INSTANCE_PASSWORD, OperationService::updateInstancePassword)
                .addTransferCloud("$.ci.openId", "$.server_id")
                .addTransferCloud("$.model.password", "$.password");
        //创建云主机快照
        register(ActionType.CREATE_COMPUTE_SNAPSHOT, OperationService::createSnapshot)
                .addTransferCloud("$.ci.openId", "$.server_id")
                .addTransferCloud("$.model.name", "$.name")
                .addTransferCloud("$.model.description", "$.description",false);
        //克隆云主机
        register(ActionType.CLONE_COMPUTE_INSTANCE, OperationService::cloneInstance)
                .addBefore(CloudService::toBeforeBiz)
                .addAfter(CloudService::toBizResId)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 10000, 10000)
                .addSetRefreshSplitData("response","$.data.body.data.uuids")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //迁移云主机
        register(ActionType.MIGRATE_COMPUTE_INSTANCE, OperationService::migrateInstance)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 10000, 10000)
                .addSetRefreshSplitData("request","$.body.cloud.server_id")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //刷新云主机
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.server_id", BaseUtils::formatSingle);

    }
}
