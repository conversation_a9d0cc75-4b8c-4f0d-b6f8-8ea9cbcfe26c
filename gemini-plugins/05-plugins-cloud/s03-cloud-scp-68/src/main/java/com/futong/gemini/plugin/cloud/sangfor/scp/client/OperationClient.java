package com.futong.gemini.plugin.cloud.sangfor.scp.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeInstanceResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeInstanceVncUrlResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.OperationInstanceResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.RunInstanceResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.utils.RSAEncryptor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class OperationClient extends ScpClient {

    public OperationClient(Config config) throws Exception {
        super(config);
    }

    public RunInstanceResponse runInstance(Map<String, Object> request) throws Exception {
        Map<String, Object> body = convertMap(request);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", "/janus/20180725/servers", "json", req, runtime);
        return TeaModel.toModel(map, new RunInstanceResponse());
    }

    private Map<String, Object> convertMap(Map<String, Object> request) {
        Map<String, Object> body = new HashMap<>(request);
        body.put("sockets", getSockets((Integer)request.get("cores")));
        body.put("memory_mb", ((Integer)request.get("memory_mb")) * 1024);
        body.put("power_on", Boolean.TRUE.equals(request.get("power_on")) ? 1 : 0);
        Map<String, Object> disk = new HashMap<>();
        disk.put("size_mb", (Integer)((Map)request.get("disk")).get("disk_size") * 1024);
        disk.put("type", "new_disk");
        disk.put("id", "ide0");
        disk.put("preallocate", "metadata");
        disk.put("use_virtio", 1);
        body.put("disks", ListUtil.toList(disk));
        body.put("storage_tag_id", ((Map)request.get("disk")).get("disk_id"));
        List<Map<String, Object>> networks = new ArrayList<>();
        Boolean connect = (Boolean)request.get("connect");
        if (connect) {
            Map<String, Object> network = new HashMap<>();
            network.put("connect", 1);
            network.put("host_tso", 0);
            network.put("vif_id", "net0");
            network.put("model", "rtl8139");
            networks.add(network);
        }
        body.put("networks", networks);
        body.put("count", 1);
        body.remove("connect");
        body.remove("disk");
        return body;
    }


    public OperationInstanceResponse deleteInstance(Map<String, Object> request) throws Exception {
        OperationInstanceResponse responses = null;
        List<String> serverIds = (List<String>) request.get("server_ids");
        for (String serverId : serverIds) {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            String path = StrUtil.format("/janus/20180725/servers/{}", serverId);
            Map<String, ?> map = this.doRequest("HTTPS", "DELETE", path, "json", new OpenApiRequest(), runtime);
            OperationInstanceResponse response = TeaModel.toModel(map, new OperationInstanceResponse());
            if (responses == null) {
                responses = response;
            } else {
                responses.getBody().getData().setTaskId(responses.getBody().getData().getTaskId() + "," + response.getBody().getData().getTaskId());
            }
        }
        return responses;
    }

    public OperationInstanceResponse updateInstance(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}", request);
        Map<String, Object> body = convertUpdateMap(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "PUT", path, "json", req, runtime);
        return TeaModel.toModel(map, new OperationInstanceResponse());
    }

    private Map<String, Object> convertUpdateMap(Map<String, Object> request) throws Exception {
        DescribeInstanceResponse instanceResponse = describeInstance(MapUtil.of("server_id", request.get("server_id")));
        DescribeInstanceResponse.Instance instance = instanceResponse.body.data;
        Map<String, Object> body = new HashMap<>();
        body.put("name", request.get("name"));
        body.put("description", request.get("description"));
        //cpu
        if (!instance.getCores().equals(request.get("cores"))) {
            body.put("cores", request.get("cores"));
            body.put("sockets", getSockets((Integer)request.get("cores")));
        }
        //内存
        int memoryMb = ((Integer) request.get("memory_mb")) * 1024;
        if (instance.getMemoryMb() != memoryMb) {
            body.put("memory_mb", memoryMb);
        }
        //磁盘
        List<Map> disk = (List<Map>)request.get("disk");
        if (disk != null && !disk.isEmpty()) {
            List<String> diskIds = ListUtil.toList("ide0", "ide1", "ide3", "ide4", "ide5", "ide6", "ide7", "ide8", "ide9", "ide10", "ide11", "ide12", "ide13", "ide14", "ide15");
            Map<String, DescribeInstanceResponse.Disk> diskMap = instance.getDisks().stream()
                    .collect(Collectors.toMap(DescribeInstanceResponse.Disk::getId, i -> i));
            diskIds.removeAll(diskMap.keySet());
            List<Map<String, Object>> disks = new ArrayList<>();
            for (int i = 0; i < disk.size(); i++) {
                Map<String, Object> diskValue = (Map<String, Object>) disk.get(i);
                String disk_id = (String) diskValue.get("disk_id");
                if (StrUtil.isNotEmpty(disk_id) && diskMap.containsKey(disk_id)) {
                    DescribeInstanceResponse.Disk oldDisk = diskMap.get(disk_id);
                    Map<String, Object> editDisk = new HashMap<>();
                    editDisk.put("size_mb", (Integer)diskValue.get("disk_size") * 1024);
                    editDisk.put("type", oldDisk.getType());
                    editDisk.put("id", oldDisk.getId());
                    editDisk.put("preallocate", oldDisk.getPreallocate());
                    editDisk.put("storage_file", oldDisk.getStorageFile());
                    editDisk.put("is_old_disk", 1);
                    disks.add(editDisk);
                } else if (StrUtil.isEmpty(disk_id)) {
                    Map<String, Object> newDisk = new HashMap<>();
                    newDisk.put("size_mb", (Integer)diskValue.get("disk_size") * 1024);
                    newDisk.put("type", "new_disk");
                    newDisk.put("id", diskIds.get(i));
                    newDisk.put("preallocate", "metadata");
                    newDisk.put("use_virtio", 1);
                    disks.add(newDisk);
                }
            }
            body.put("disks", disks);
        }
        //网卡
        List<Map> network = (List<Map>)request.get("network");
        if (CollectionUtil.isNotEmpty(network)){
            List<String> netIds = ListUtil.toList("net0", "net1", "net2","net3", "net4", "net5", "net6", "net7", "net8", "net9");
            Map<String, DescribeInstanceResponse.Network> netMap = instance.getNetworks().stream()
                    .collect(Collectors.toMap(DescribeInstanceResponse.Network::getVifId, i -> i));
            netIds.removeAll(netMap.keySet());
            List<Map<String, Object>> nets = new ArrayList<>();
            for (int i = 0; i < network.size(); i++){
                Map<String, Object> networkValue = (Map<String, Object>) network.get(i);
                String vif_id = (String) networkValue.get("vif_id");//网卡id
                String name = (String) networkValue.get("name");//连接的网络名称
                String device_id = (String) networkValue.get("device_id");//连接的经典网络设备id
                String port_id = (String) networkValue.get("port_id");//端口id
                Object ip_info = networkValue.get("ip_info");//ip地址信息 {"netmask":"*************","ip_address":"**************","gateway":"","dns":[]}
                int connect = (int) networkValue.get("connect");//是否连接：1：是，0：否
                Map<String, Object> editNet = new HashMap<>();
                if (StrUtil.isNotEmpty(vif_id) && netMap.containsKey(vif_id)){
                    DescribeInstanceResponse.Network oldNet = netMap.get(vif_id);
                    editNet.put("vif_id", oldNet.getVifId());
                }else {
                    editNet.put("vif_id", netIds.get(i));
                }
                editNet.put("connect", connect);
                editNet.put("name", name);
                editNet.put("device_id", device_id);
                editNet.put("port_id", port_id);
                if (ip_info != null){
                    editNet.put("ip_info", ip_info);//ip_info对象中的ip_address信息不能为空
                }
                nets.add(editNet);
            }
            body.put("networks", nets);
        }
        return body;
    }

    private Integer getSockets(Integer cores) {
        int sockets = 1;
        if (cores >= 16 && cores % 4 == 0) {
            sockets = 2;
        }
        return sockets;
    }

    public OperationInstanceResponse startInstance(Map<String, Object> request) throws Exception {
        OperationInstanceResponse responses = null;
        List<String> serverIds = (List<String>) request.get("server_ids");
        for (String serverId : serverIds) {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            String path = StrUtil.format("/janus/20180725/servers/{}/start", serverId);
            Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", new OpenApiRequest(), runtime);
            OperationInstanceResponse response = TeaModel.toModel(map, new OperationInstanceResponse());
            if (responses == null) {
                responses = response;
            } else {
                responses.getBody().getData().setTaskId(responses.getBody().getData().getTaskId() + "," + response.getBody().getData().getTaskId());
            }
        }
        return responses;
    }

    public OperationInstanceResponse stopInstance(Map<String, Object> request) throws Exception {
        OperationInstanceResponse responses = null;
        List<String> serverIds = (List<String>) request.get("server_ids");
        for (String serverId : serverIds) {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            String path = StrUtil.format("/janus/20180725/servers/{}/stop", serverId);
            Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", new OpenApiRequest(), runtime);
            OperationInstanceResponse response = TeaModel.toModel(map, new OperationInstanceResponse());
            if (responses == null) {
                responses = response;
            } else {
                responses.getBody().getData().setTaskId(responses.getBody().getData().getTaskId() + "," + response.getBody().getData().getTaskId());
            }
        }
        return responses;
    }

    public OperationInstanceResponse rebootInstance(Map<String, Object> request) throws Exception {
        OperationInstanceResponse responses = null;
        List<String> serverIds = (List<String>) request.get("server_ids");
        for (String serverId : serverIds) {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            String path = StrUtil.format("/janus/20180725/servers/{}/reboot", serverId);
            Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", new OpenApiRequest(), runtime);
            OperationInstanceResponse response = TeaModel.toModel(map, new OperationInstanceResponse());
            if (responses == null) {
                responses = response;
            } else {
                responses.getBody().getData().setTaskId(responses.getBody().getData().getTaskId() + "," + response.getBody().getData().getTaskId());
            }
        }
        return responses;
    }

    public DescribeInstanceVncUrlResponse instanceVncUrl(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/remote-consoles", request);
        request.remove("server_id");
        request.put("remote_console", MapUtil.builder("protocol", "vnc").put("type", "novnc").build());
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", req, runtime);
        return TeaModel.toModel(map, new DescribeInstanceVncUrlResponse());
    }

    public DescribeInstanceResponse describeInstance(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}", request);
        request.remove("server_id");
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        try {
            Map<String, ?> map = this.doRequest("HTTPS", "query", path, "json", req, runtime);
            return TeaModel.toModel(map, new DescribeInstanceResponse());
        } catch (Exception e) {
            if (ReUtil.contains("云主机(.*?)不存在", e.getMessage())) {
                return new DescribeInstanceResponse();
            }
            throw e;
        }
    }

    public OperationInstanceResponse suspendInstance(Map<String, Object> request) throws Exception {
        OperationInstanceResponse responses = null;
        List<String> serverIds = (List<String>) request.get("server_ids");
        for (String serverId : serverIds) {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            String path = StrUtil.format("/janus/20180725/servers/{}/suspend", serverId);
            Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", new OpenApiRequest(), runtime);
            OperationInstanceResponse response = TeaModel.toModel(map, new OperationInstanceResponse());
            if (responses == null) {
                responses = response;
            } else {
                responses.getBody().getData().setTaskId(responses.getBody().getData().getTaskId() + "," + response.getBody().getData().getTaskId());
            }
        }
        return responses;
    }

    public OperationInstanceResponse updateInstancePassword(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/server-password", request);
        Map<String, Object> body = convertEncryptPasswordMap(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", req, runtime);
        return TeaModel.toModel(map, new OperationInstanceResponse());
    }

    public Map<String, Object> convertEncryptPasswordMap(Map<String, Object> request){
        String password = (String) request.get("password");
        String public_key = (String) request.get("public_key");
        String encrypt = RSAEncryptor.encryptWithModulus(password, StrUtil.replace(public_key, "\n", ""));
        return MapUtil.builder(new HashMap<String,Object>()).put("password", encrypt).build();
    }

    public OperationInstanceResponse createSnapshot(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/snapshots", request);
        Map<String, Object> body = MapUtil.builder(new HashMap<String, Object>())
                .put("name", request.get("name"))
                .put("description", request.get("description"))
                .build();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", req, runtime);
        return TeaModel.toModel(map, new OperationInstanceResponse());
    }

    public RunInstanceResponse cloneInstance(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/clone", request);
        Map<Object, Object> advanceParams = MapUtil.builder().put("return_uuids", 1).build();
        Map<String, Object> body = MapUtil.builder(new HashMap<String, Object>())
                .put("name", request.get("name"))
                .put("description", request.get("description"))
                .put("storage_tag_id", request.get("storage_tag_id"))
                .put("power_on", request.get("power_on"))//克隆后是否启动 0 关机 1 启动
                .put("count", request.get("count"))
                .put("advance_param", advanceParams)//是否需要返回克隆的实例ID
                .build();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", req, runtime);
        return TeaModel.toModel(map, new RunInstanceResponse());
    }

    public RunInstanceResponse migrateInstance(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String path = StrUtil.format("/janus/20180725/servers/{server_id}/migrate", request);
        Map<String, Object> body = MapUtil.builder(new HashMap<String, Object>())
                .put("az_id", request.get("az_id"))
                .put("storage_location", request.get("storage_location"))
                .put("compute_location", request.get("compute_location"))
                .put("auto_power_on", request.get("auto_power_on"))//迁移后是否启动 0 关机 1 启动
                .build();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", body)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "POST", path, "json", req, runtime);
        return TeaModel.toModel(map, new RunInstanceResponse());
    }
}
