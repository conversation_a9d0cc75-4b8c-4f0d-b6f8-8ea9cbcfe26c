package com.futong.gemini.plugin.cloud.sangfor.scp.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.common.utils.PageUtils;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.EcsClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.MonitorClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.NetClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.convert.Converts;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.*;
import com.futong.gemini.plugin.cloud.sangfor.scp.service.CloudService;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class FetchService {

    public static BaseResponse fetchProject(BaseCloudRequest request) {
        Entry.E2<DescribeProjectsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeProject,
                Converts::convertProject);
        return BaseCloudService.fetchSend(request, mapE2.v2);
    }

    public static BaseResponse fetchAZone(BaseCloudRequest request) {
         Entry.E2<DescribeAZonesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                 CloudClient.client,
                 EcsClient::describeAZone,
                 Converts::convertZone);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if (CollUtil.isEmpty(mapE2.v2.get(TmdbDevops.class))) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, mapE2.v2.get(TmdbDevops.class), (TmdbDevops t) -> {
            JobInfo jobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_PLATFORM_CLUSTER);
            request.getBody().getCloud().put("az_id", t.getDevops_value());
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static BaseResponse fetchCluster(BaseCloudRequest request) {
        Entry.E2<DescribeClusterResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeCluster,
                Converts::convertCluster);
        return BaseCloudService.fetchSend(request, mapE2.v2);
    }

    /**
     * 获取镜像
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchImage(BaseCloudRequest request) {
        Entry.E2<DescribeImagesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeImages,
                Converts::convertImage);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    public static BaseResponse fetchEipPool(BaseCloudRequest request) {
        Entry.E2<DescribeEipsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeEipPools,
                Converts::convertEip);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    public static BaseResponse fetchEip(BaseCloudRequest request) {
        Entry.E2<DescribeEipsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeEips,
                Converts::convertEip);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    public static BaseResponse fetchStorage(BaseCloudRequest request) {
        Entry.E2<DescribeStoragesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeStorages,
                Converts::convertStorage);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 获取VPC
     * @param request
     * @return
     */
    public static BaseResponse fetchVpc(BaseCloudRequest request) {
        Entry.E2<DescribeVpcsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeVpcs,
                Converts::convertVpc);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 获取Subnet
     * @param request
     * @return
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        Entry.E2<DescribeSubnetsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeSubnets,
                Converts::convertSubnet);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return response;
    }

    /**
     * 获取主机
     * @param request
     * @return
     */
    public static BaseResponse fetchHost(BaseCloudRequest request) {
        Entry.E2<DescribeHostsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeHosts,
                Converts::convertHost);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeInstances,
                Converts::convertEcs);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 获取路由
     * @param request
     * @return
     */
    public static BaseResponse fetchRouter(BaseCloudRequest request) {
        Entry.E2<DescribeRoutersResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeRouters,
                Converts::convertRouter);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 同步虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse fetchDVSwitch(BaseCloudRequest request) {
        Entry.E2<DescribeSwitchesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeDVSwitches,
                Converts::convertDVSwitch);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 同步物理出口
     * @param request
     * @return
     */
    public static BaseResponse fetchBVSwitch(BaseCloudRequest request) {
        Entry.E2<DescribeVlansResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describeBVSwitches,
                Converts::convertBVSwitch);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 同步端口
     * @param request
     * @return
     */
    public static BaseResponse fetchPort(BaseCloudRequest request) {
        Entry.E2<DescribePortsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                NetClient::describePorts,
                Converts::convertPort);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("page_num", t - 1);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("timegap")) {
            request.getBody().getCloud().put("timegap", "1h");
        }
        return true;
    }

    public static boolean defaultMetricNames(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("metric_names")) {
            request.getBody().getCloud().put("metric_names", StrUtil.join(",", Converts.metrics.keySet()));
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("object_type")) {
            request.getBody().getCloud().put("object_type", "server");
        }
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 50);//固定50条
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);

        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        //设置采集监控数据云主机ID
        Set<String> instanceIds = instanceMap.keySet();
        request.getBody().put("instanceIds", instanceIds);
        return true;
    }

    public static boolean defaultHostMetricDimensions(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("object_type")) {
            request.getBody().getCloud().put("object_type", "host");
        }
        //处理查询南新仓物理机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 50);//固定50条
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得物理机集合
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> result = ApiFactory.Api.res.listHostStoragePool(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得物理机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控物理机信息,页码：{},条数：{},本次获取条数：{},数据底座共有物理机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);

        Map<String, ResHostStoragePoolApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResHostStoragePoolApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        //设置采集监控数据物理机ID
        Set<String> instanceIds = instanceMap.keySet();
        request.getBody().put("instanceIds", instanceIds);
        return true;
    }

    public static BaseResponse fetchPerf(BaseCloudRequest request) {
        try {
            //监控请求指标集合
            Collection<String> instanceIds = request.getBody().getJSONArray("instanceIds").toJavaList(String.class);
            Map<String, DescribeMetricsResponse.DescribeMetricResponseBodyData> metricList = new HashMap<>();
            for (String instanceId : instanceIds) {
                request.getBody().getCloud().put("object_id", instanceId);
                DescribeMetricsResponse result = CloudClient.client.execute(request.getBody(), MonitorClient::describeMetrics);
                metricList.put(instanceId, result.getBody().getData());
            }
            request.getBody().remove("instanceIds");
            Map<String, PerfInfoBean> perfMap = Converts.convertPerf(request, metricList);
            log.info("转换监控信息为:{}", Objects.requireNonNull(perfMap).size());
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("获取监控失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取监控失败");
        }
    }

    public static BaseResponse fetchSnapshot(BaseCloudRequest request) {
        Collection<String> instanceIds = request.getBody().getJSONArray("instanceIds").toJavaList(String.class);
        Map<String, List<DescribeSnapshotsResponse.Snapshot>> snapshotList = new HashMap<>();
        for (String instanceId : instanceIds) {
            request.getBody().getCloud().put("server_id", instanceId);
            DescribeSnapshotsResponse result = CloudClient.client.execute(request.getBody(), EcsClient::describeSnapshots);
            if (CollUtil.isNotEmpty(result.getBody().getData())) {
                snapshotList.put(instanceId, result.getBody().getData());
            }
        }
        request.getBody().remove("instanceIds");
        Map<Class, List> v2 = Converts.convertSnapshot(request, snapshotList);
        return BaseCloudService.fetchSend(request, v2);
    }

    /**
     * 告警
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        Entry.E2<DescribeAlarmsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeAlarms,
                Converts::convertAlarm);
        BaseResponse response = CloudService.fetchAetSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

}
