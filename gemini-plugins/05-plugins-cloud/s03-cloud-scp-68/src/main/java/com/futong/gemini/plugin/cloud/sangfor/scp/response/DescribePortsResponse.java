package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribePortsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribePortResponseBody body;
    @Data
    public static class DescribePortResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribePortResponseBodyData data;
    }

    @Data
    public static class DescribePortResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Port> data;
    }

    @Data
    public static class Port extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("vpc_id")
        public String vpcId;

        @NameInMap("subnet_id")
        public String subnetId;

        @NameInMap("admin_status_up")
        public Boolean adminStatusUp;

        @NameInMap("device_owner")
        public String deviceOwner;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("device_id")
        public String deviceId;

        @NameInMap("device_type")
        public String deviceType;

        @NameInMap("mac_address")
        public String macAddress;

        @NameInMap("ips_info")
        public List<IpsInfo> ipsInfo;
    }

    @Data
    public static class IpsInfo extends TeaModel {
        @NameInMap("ip_address")
        public String ipAddress;
    }
}
