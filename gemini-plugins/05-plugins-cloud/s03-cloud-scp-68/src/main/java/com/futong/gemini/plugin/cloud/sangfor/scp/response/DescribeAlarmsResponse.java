package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeAlarmsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeAlarmsResponseBody body;

    @Data
    public static class DescribeAlarmsResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeAlarmsResponseBodyData data;
    }

    @Data
    public static class DescribeAlarmsResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Alarm> data;
    }

    @Data
    public static class Alarm extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("object_id")
        public String objectId;

        @NameInMap("object_name")
        public String objectName;

        @NameInMap("object_type")
        public String objectType;

        @NameInMap("level")
        public String level;

        @NameInMap("description")
        public String description;

        @NameInMap("title")
        public String title;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("unread_id")
        public Integer unreadId;

        @NameInMap("start_time")
        public String startTime;
    }

}

