package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeVlansResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeVlanResponseBody body;
    @Data
    public static class DescribeVlanResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeVlanResponseBodyData data;
    }

    @Data
    public static class DescribeVlanResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Vlan> data;
    }

    @Data
    public static class Vlan extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("enable")
        public Integer enable;

        @NameInMap("vlan_group")
        public List<VlanGroup> vlanGroup;

        @NameInMap("phy_if")
        public List<PhyIf> phyIf;
    }

    @Data
    public static class VlanGroup extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("count")
        public Integer count;

        @NameInMap("type")
        public String type;

        @NameInMap("vlan_id")
        public String vlanId;

        @NameInMap("pvid")
        public Integer pvid;

        @NameInMap("vlan_range")
        public String vlanRange;
    }

    @Data
    public static class PhyIf extends TeaModel {
        @NameInMap("port")
        public String port;

        @NameInMap("host_id")
        public String hostId;

        @NameInMap("status")
        public Integer status;

        @NameInMap("type")
        public String type;

        @NameInMap("vlan_id")
        public String vlanId;
    }
}
