package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeSwitchesResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeSwitchResponseBody body;
    @Data
    public static class DescribeSwitchResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeSwitchResponseBodyData data;
    }

    @Data
    public static class DescribeSwitchResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Switch> data;
    }

    @Data
    public static class Switch extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;
    }

}
