package com.futong.gemini.plugin.cloud.sangfor.scp.http;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.entity.AppTokenParam;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.AuthTokenResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.PublicKeyResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.utils.RSAEncryptor;
import com.futong.gemini.plugin.cloud.sangfor.scp.utils.URLUtils;
import com.futong.gemini.plugin.cloud.sangfor.scp.entity.AccessToken;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;

@Slf4j
public class HttpClientUtil {

    public static AccessToken getToken(CloudAccessBean bean) {
        AccessToken token =null;
        try {
            token = (AccessToken) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }
        if (ObjectUtil.isNull(token) || token.invalid()) {
            token = getAccessToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}", JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, bean.getCmpId(), token);
        }
        return token;
    }

    public static AccessToken getAccessToken(CloudAccessBean bean) {
        try {
            log.info("获取accessToken参数为：{}",JSON.toJSONString(bean));

            String publicKeyUrl = URLUtils.bean.makeUrl(bean, URLUtils.bean.getPublicKeyUrl(),null);
            String accessTokenUrl = URLUtils.bean.makeUrl(bean, URLUtils.bean.getTokenUrl(),null);
            PublicKeyResponse publicKey = fetchPublicKey(publicKeyUrl);
            String password = RSAEncryptor.encryptWithModulus(bean.getPassword(), StrUtil.replace(publicKey.getData().getPublicKey(), "\n", ""));
            AppTokenParam appTokenParam = new AppTokenParam(bean.getUsername(), password);
            return fetchToken(accessTokenUrl, JSON.toJSONString(appTokenParam));
        } catch (Exception e) {
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
    }

    public static PublicKeyResponse fetchPublicKey(String url) {
        String body = HttpUtil.get(url);
        return JSON.parseObject(body, PublicKeyResponse.class);
    }

    public static AccessToken fetchToken(String url, String json) {
        AccessToken accessToken = null;
        log.info("获取appToken的参数为：{}",json);
        String body = HttpUtil.post(url, json);
        log.info("获取appToken的返回结果为：{}",body);
        AuthTokenResponse response = JSON.parseObject(body, AuthTokenResponse.class);
        if (ObjectUtil.isNotNull(response.getData())) {
            accessToken = new AccessToken();
            accessToken.setId(response.getData().getAccess().getToken().getId());
            accessToken.setExpires(DateTime.from(Instant.parse(response.getData().getAccess().getToken().getExpires())));
        }
        return accessToken;
    }
}
