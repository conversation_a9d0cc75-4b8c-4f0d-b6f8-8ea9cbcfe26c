package com.futong.gemini.plugin.cloud.sangfor.scp.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.RunInstanceResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class CloudService {

    public static <Q, R, C> FTAction<BaseCloudRequest> toFTAction(FTExecute<C, Q, R> exec) {
        return (BaseCloudRequest body) -> doAction(body, exec);
    }

    public static <Q, R, C> BaseDataResponse<R> doAction(BaseCloudRequest request, FTExecute<C, Q, R> exec) {
        try {
            return new BaseDataResponse<>(CloudClient.client.execute(request.getBody(), exec));
        } catch (Exception e) {
            String message = request.getAction().operationType().cname() + request.getAction().resourceType().name() + "失败";
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("page_num")) {
            request.getBody().getCloud().put("page_num", 0);
        }
        if (!request.getBody().getCloud().containsKey("page_size")) {
            request.getBody().getCloud().put("page_size", 100);
        }
        return true;
    }

    public static void toBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) {
            return;
        }
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
            if (dataResponse.getData() instanceof RunInstanceResponse) {
                RunInstanceResponse runInstancesResponse = (RunInstanceResponse) data;
                if (runInstancesResponse.body == null
                        || runInstancesResponse.body.data == null
                        || CollUtil.isEmpty(runInstancesResponse.body.data.getUuids())) {
                    return;
                }
                JSONObject biz = request.getBody().getBiz();
                biz.put("resId",IdUtils.encryptId(request.getBody().getAccess().getCmpId(), runInstancesResponse.body.data.getUuids().get(0)));
                //dataResponse.withData(biz);
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            }
        }
    }

    public static boolean toBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        if (resNum == null || resNum == 0) return true;//为0非批量创建云主机,不做处理
        JSONObject cloud = request.getBody().getCloud();
        String instanceName = cloud.getString("name");
        cloud.put("name", instanceName + "-" + resNum);
        cloud.put("count", 1);//默认数量
        return true;
    }

    public static BaseResponse fetchAetSend(BaseCloudRequest request, Map<Class, List> map) {
        try {
            StringBuilder sb = new StringBuilder("本次获取告警数据:");
            for (Class clazz : map.keySet()) {
                List data = map.get(clazz);
                sb.append(CollUtil.size(data)).append("条");
                BaseCloudService.toAetMessageAndSend(data, "alarm");
            }
            return BaseResponse.SUCCESS.of(sb.toString());
        } catch (Exception e) {
            log.error("发送告警数据" + request.getAction().resourceType().cname() + "失败", e);
            throw new BaseException(BaseResponse.ERROR_SYS_MQ, e, "发送告警数据" + request.getAction().resourceType().cname() + "失败");
        }
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("begin_time")) {
            request.getBody().getCloud().put("begin_time", DateUtil.offsetDay(new Date(), -1).toString("yyyy-MM-dd HH:mm:ss"));//一天前
        }
        if (!request.getBody().getCloud().containsKey("end_time")) {
            request.getBody().getCloud().put("end_time", DateUtil.date().toString("yyyy-MM-dd HH:mm:ss"));//当前时间
        }
        return true;
    }
}
