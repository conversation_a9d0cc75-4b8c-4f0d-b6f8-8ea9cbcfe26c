package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import lombok.Data;

@Data
public class AuthTokenResponse extends BaseResponseBodyExt {

    private AuthTokenResponseBodyData data;

    @Data
    public static class AuthTokenResponseBodyData {
        public AuthTokenAccess access;
    }

    @Data
    public static class AuthTokenAccess {
        public AuthToken token;
    }

    @Data
    public static class AuthToken {
        private String issuedAt;
        private String expires;
        private String id;
    }
}
