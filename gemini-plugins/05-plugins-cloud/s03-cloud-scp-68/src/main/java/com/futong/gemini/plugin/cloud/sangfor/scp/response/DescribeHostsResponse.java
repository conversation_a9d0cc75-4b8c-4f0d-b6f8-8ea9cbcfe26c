package com.futong.gemini.plugin.cloud.sangfor.scp.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeHostsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeHostResponseBody body;
    @Data
    public static class DescribeHostResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeHostResponseBodyData data;
    }

    @Data
    public static class DescribeHostResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Host> data;
    }

    @Data
    public static class Host extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("description")
        public String description;

        @NameInMap("type")
        public String type;

        @NameInMap("status")
        public String status;

        @NameInMap("ip")
        public String ip;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("is_use_vs")
        public Integer isUseVs;

        @NameInMap("alarm_count")
        public Integer alarmCount;

        @NameInMap("reserves")
        public VirtualResources reserves;

        @NameInMap("cpu")
        public Cpu cpu;

        @NameInMap("memory")
        public Memory memory;

        @NameInMap("storage")
        public Storage storage;

        @NameInMap("gpu")
        public Gpu gpu;
    }

    @Data
    public static class VirtualResources extends TeaModel {
        @NameInMap("memory_mb")
        public Integer memoryMb;
    }

    @Data
    public static class Cpu extends TeaModel {
        @NameInMap("allocation_max")
        public Float allocationMax;

        @NameInMap("allocation_ratio")
        public Float allocationRatio;

        @NameInMap("total_mhz")
        public Float totalMhz;

        @NameInMap("used_mhz")
        public Float usedMhz;

        @NameInMap("ratio")
        public Float ratio;

        @NameInMap("socket_count")
        public Integer socketCount;

        @NameInMap("cores_per_socket")
        public Integer coresPerSocket;

        @NameInMap("core_count")
        public Integer coreCount;

        @NameInMap("type")
        public String type;
    }

    @Data
    public static class Memory extends TeaModel {
        @NameInMap("allocation_max")
        public Float allocationMax;

        @NameInMap("allocation_ratio")
        public Float allocationRatio;

        @NameInMap("total_mb")
        public Float totalMb;

        @NameInMap("used_mb")
        public Float usedMb;

        @NameInMap("ratio")
        public Float ratio;
    }

    @Data
    public static class Storage extends TeaModel {
        @NameInMap("total_mb")
        public Float totalMb;
    }

    @Data
    public static class Gpu extends TeaModel {
        @NameInMap("total")
        public Integer total;

        @NameInMap("used")
        public Float used;

        @NameInMap("ratio")
        public Float ratio;

        @NameInMap("memory_total_mb")
        public Float memoryTotalMb;

        @NameInMap("memory_used_mb")
        public Float memoryUsedMb;

        @NameInMap("memory_ratio")
        public Float memoryRatio;
    }
}
