package com.futong.gemini.plugin.cloud.sangfor.scp.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.EcsClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeHostsResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeInstancesResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CapacityResourceService {

    /**
     * 查询实例总数
     * @param request
     * @return
     */
    public static BaseResponse queryInstanceTotal(BaseCloudRequest request){
        try {
            JSONObject data = new JSONObject();
            DescribeInstancesResponse response = CloudClient.client.execute(request.getBody(), EcsClient::describeInstances);
            int count = 0;
            if(ObjectUtil.isNotNull(response) && CollUtil.isNotEmpty(response.getBody().getData().getData())){
                count = response.getBody().getData().getTotalSize();
            }
            data.put("count",count);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("查询实例总数失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询实例总数失败");
        }
    }

    /**
     * 查询物理机总数
     * @param request
     * @return
     */
    public static BaseResponse queryHostTotal(BaseCloudRequest request){
        try {
            JSONObject data = new JSONObject();
            DescribeHostsResponse response = CloudClient.client.execute(request.getBody(), EcsClient::describeHosts);
            int count = 0;
            if(ObjectUtil.isNotNull(response) && CollUtil.isNotEmpty(response.getBody().getData().getData())){
                count = response.getBody().getData().getTotalSize();
            }
            data.put("count",count);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("查询实例总数失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询实例总数失败");
        }
    }
}
