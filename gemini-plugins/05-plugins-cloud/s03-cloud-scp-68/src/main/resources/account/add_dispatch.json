{"data": [{"required": false, "unique": false, "dispatcher_info": {"jobName": "获取云主机", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云主机", "jobInfo": "{\"action\": \"FetchComputeInstance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":10,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取物理机", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理机", "jobInfo": "{\"action\": \"FetchComputeHost\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":10,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取磁盘", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取磁盘", "jobInfo": "{\"action\": \"FetchStoragePool\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":10,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取镜像", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取镜像", "jobInfo": "{\"action\": \"FetchStorageImage\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取VPC", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取VPC", "jobInfo": "{\"action\": \"FetchNeutronVpc\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取Subnet", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取Subnet", "jobInfo": "{\"action\": \"FetchNeutronSubnet\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取路由器", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取路由器", "jobInfo": "{\"action\": \"FetchNeutronRoute\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取交换机", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取交换机", "jobInfo": "{\"action\": \"FetchNeutronSwitch\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取物理出口", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理出口", "jobInfo": "{\"action\": \"FetchNeutronVlan\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取端口", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取端口", "jobInfo": "{\"action\": \"FetchNeutronNic\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取组织", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取组织", "jobInfo": "{\"action\": \"FetchPlatformProject\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取弹性IP", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取弹性IP", "jobInfo": "{\"action\": \"FetchNeutronEip\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取资源池", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "获取地域可用区", "jobInfo": "{\"action\": \"FetchPlatformResourcePool\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取云主机监控", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 0/1 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云主机监控", "jobInfo": "{\"action\":\"FetchComputeInstancePerf\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"cloud\":{},\"BasePageSortSearchRequest\":{\"searchList\":[{\"key\":\"status\",\"searchClassiy\":\"0\",\"value\":\"running\"},{\"key\":\"account_id\",\"searchClassiy\":\"0\",\"value\":\"${cmpId}\"}]}},\"async\":false}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":1,\"strategyPeriodUnit\":\"小时\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取物理机监控", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 0/1 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理机监控", "jobInfo": "{\"action\":\"FetchComputeHostPerf\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"cloud\":{},\"BasePageSortSearchRequest\":{\"searchList\":[{\"key\":\"status\",\"searchClassiy\":\"0\",\"value\":\"running\"},{\"key\":\"account_id\",\"searchClassiy\":\"0\",\"value\":\"${cmpId}\"}]}},\"async\":false}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":1,\"strategyPeriodUnit\":\"小时\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "获取快照", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取快照", "jobInfo": "{\"action\":\"FetchStorageSnapshot\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"cloud\":{},\"BasePageSortSearchRequest\":{\"searchList\":[{\"key\":\"status\",\"searchClassiy\":\"0\",\"value\":\"running\"},{\"key\":\"account_id\",\"searchClassiy\":\"0\",\"value\":\"${cmpId}\"}]}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取告警", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformAlarm\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}