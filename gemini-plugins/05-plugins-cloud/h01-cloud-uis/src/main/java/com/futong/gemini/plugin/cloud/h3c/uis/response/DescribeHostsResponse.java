package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeHostsResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeHostsData> data;

    @Data
    public static class DescribeHostsData {
        private String businessRole;

        private Integer clusterId;

        private String clusterName;

        private Integer clusterRole;

        private Integer cpu;

        private Double cpuRate;

        private String hostpoolId;

        private Integer id;

        private String ipAddr;

        private String maintainMode;

        private Integer memory;

        private Double memRate;

        private Integer mode;

        private String name;

        private String nodepoolName;

        private String onestorRole;

        private String platform;
        private Integer runningNum;
        private Integer shutoffNum;
        private Integer status;
        private Long storage;
        private Integer summary;

    }



}
