package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeClustersResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeClustersData> data;

    @Data
    public static class DescribeClustersData {


        private Integer id;

        private String name;

        private String desc;

        private Integer enableHA;

    }



}
