package com.futong.gemini.plugin.cloud.h3c.uis.common;

import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.futong.common.utils.Entry.E2;
import static com.futong.common.utils.Entry.E3;
public class Constant {

    public static Map<String, E3<String, E2<String, String>[], String>> metrics = new HashMap<>();
    private static E2<String, String>[] metricType = new E2[]{
            new E2("Average", "average"),
            new E2("Maximum", "max"),
            new E2("Minimum", "min"),
            new E2("Value", "value"),
            new E2("Sum", "sum"),
    };

    static {
        metrics.put("cpu_total", new E3("cpuUsage", metricType, "%"));
        metrics.put("memory_usedutilization", new E3("memUsage", metricType, "%"));
        metrics.put("diskusage_utilization", new E3("diskUsage", metricType, "%"));
        metrics.put("disk_readiops", new E3("diskRead", metricType, "Count/s"));
        metrics.put("disk_writeiops", new E3("diskWrite", metricType, "Count/s"));
        metrics.put("networkin_rate", new E3("netIn", metricType, "bit/s"));
        metrics.put("networkout_rate", new E3("netOut", metricType, "bit/s"));
//        metrics.put("VPC_PublicIP_InternetInRate", new E3("ipInRate", metricType, "bit/s"));
//        metrics.put("VPC_PublicIP_InternetOutRate", new E3("ipOutRate", metricType, "bit/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMapping = new HashMap<>();

    static {
        perfMapping.put("cpu", PerfInfoBean::setCpuUsage);
        perfMapping.put("memory", PerfInfoBean::setMemUsage);
        perfMapping.put("disk", PerfInfoBean::setDiskUsage);
        perfMapping.put("network", PerfInfoBean::setNetIo);
    }

}
