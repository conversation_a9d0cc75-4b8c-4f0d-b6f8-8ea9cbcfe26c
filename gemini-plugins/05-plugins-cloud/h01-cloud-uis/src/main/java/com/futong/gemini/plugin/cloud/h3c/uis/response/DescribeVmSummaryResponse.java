package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeVmSummaryResponse extends BaseResponseBodyData{

    @JsonProperty("data")
    private DescribeTrendData data;

    @Data
    public class DescribeTrendData {

        private String hostIp;
        private Integer vncport;
    }



}
