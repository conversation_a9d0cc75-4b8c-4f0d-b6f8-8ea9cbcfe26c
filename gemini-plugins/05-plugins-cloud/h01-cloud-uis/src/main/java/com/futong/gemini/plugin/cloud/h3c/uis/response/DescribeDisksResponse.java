package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeDisksResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeDisksData> data;


    @Data
    public static class DescribeDisksData {
        private String desc;
        private String diskpoolName;
        private String hostGroupId;

        private String hostGroupName;

        private String lunId;
        private List<String> lunList;
        private String lunName;
        private Integer lunSize;

        private String lunType;

        private String nodePoolName;

        private String oldHostGroupId;

        private Integer oldSize;

        private String poolName;
        private String poolStatus;
        private Integer stripeCount;
        private Integer stripeUnit;
        private Boolean tGT;
        private Integer hostLunId;
        private Integer useType;
        private String qosAssociateStatus;
        private String qosStatus;
        private String qosLimitType;
        private String qosName;
        private String qosProportion;
        private String qosLimitMetric;
        private String qosLimitIO;
        private String qosLimitValue;
        private String qosReservationMetric;
        private String qosReservationIO;
        private String qosReservationValue;

    }

}
