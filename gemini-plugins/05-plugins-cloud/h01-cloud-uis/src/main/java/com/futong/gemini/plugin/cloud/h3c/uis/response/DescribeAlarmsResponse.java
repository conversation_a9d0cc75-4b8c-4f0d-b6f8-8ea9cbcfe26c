package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeAlarmsResponse extends BaseResponseBodyData {

    @JsonProperty("data")
    private List<DescribeAlarmsData> data;

    @Data
    public static class DescribeAlarmsData {
        private Long catalogId;

        private Integer category;

        private String childTarget;

        private Long confirmTime;

        private Integer eventCount;

        private String eventDesc;

        private Integer eventLevel;

        private String eventName;

        private String eventSrc;

        private String eventTime;

        private Integer eventType;

        private String firstEventTime;

        private Long id;

        private Integer state;

        private Long targetId;

        private String uuid;
    }

}
