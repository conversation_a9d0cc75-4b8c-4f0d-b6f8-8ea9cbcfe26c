package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseResponseBodyData {


    @JsonProperty("state")
    private Integer state;
    @JsonProperty("errorCode")
    private Integer errorCode;

    private Integer offset;

    private String successMessage;

    private String failureMessage;

    private Integer totalLength;

    private Boolean success;



}
