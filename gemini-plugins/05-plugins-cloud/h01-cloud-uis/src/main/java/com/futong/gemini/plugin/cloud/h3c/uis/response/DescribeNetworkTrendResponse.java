package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeNetworkTrendResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeNetWorkTrendData> data;

    @Data
    public static class DescribeNetWorkTrendData {
        private String name;
        private String value;
        private List<DescribeNetWorkTrendList> list;
    }

    @Data
    public static class DescribeNetWorkTrendList{
        private Double rate;
        private Long time;
    }



}
