package com.futong.gemini.plugin.cloud.h3c.uis.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.PageUtils;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.plugin.cloud.h3c.uis.convert.Converts;
import com.futong.gemini.plugin.cloud.h3c.uis.request.*;
import com.futong.gemini.plugin.cloud.h3c.uis.response.*;
import com.futong.gemini.plugin.cloud.h3c.uis.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.futong.gemini.plugin.cloud.h3c.uis.convert.Converts.*;

@Slf4j
public class FetchService {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
//        t.setCloud_type(CloudType.PRIVATE_UIS.value());
        t.setCloud_type(request.getBody().getAccess().getCloudType());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }


    /**
     * 获取主机池
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHostPool(BaseCloudRequest request) {
        try {
            CloudAccessBean bean = request.getBody().getAccess();
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getHostPoolUrl(), new String[]{}),
                    "data");
            DescribeHostPoolsResponse response = JSONObject.parseObject(json, DescribeHostPoolsResponse.class);
            Map<Class, List> map = convertHostPool(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, map);
            if (CollUtil.isEmpty(map.get(TmdbDevops.class))) {
                return baseResponse;
            }
            return BaseCloudService.toGourdResponse(baseResponse, map.get(TmdbDevops.class), (TmdbDevops t) -> {
                JSONObject jsonObject = JSONObject.parseObject(t.getInfo_json());
                JobInfo jobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_PLATFORM_CLUSTER);
                request.getBody().getCloud().put("hostPoolId", jsonObject.getString("hpId"));
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        } catch (Exception e) {
            log.error("获取主机池失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取主机池失败");
        }
    }


    /**
     * 获取集群信息
     *
     * @param
     * @return
     */
    public static BaseResponse fetchCluster(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeClusterRequest arguments = request.getBody().getCloud().toJavaObject(DescribeClusterRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getClusterUrl(), new String[]{"?", arguments.getOffset(), arguments.getLimit(), arguments.getHostPoolId()}),
                    "data");
            DescribeClustersResponse response = JSONObject.parseObject(json, DescribeClustersResponse.class);
            Map<Class, List> result = convertCluster(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            //同步集群下的虚拟交换机
//            BaseCloudService.toGourdResponse(baseResponse, result.get(TmdbDevops.class), (TmdbDevops t) -> {
//                JSONObject jsonObject = JSONObject.parseObject(t.getInfo_json());
//                JobInfo jobInfo = new JobInfo();
//                request.setAction(ActionType.FETCH_NEUTRON_SWITCH);
//                request.getBody().getCloud().put("clusterId",jsonObject.getString("id"));
//                jobInfo.setRequest(request.cloneJSONObject());
//                return jobInfo;
//            });
            List<String> vmInstanceIds;
            if (response != null || CollUtil.isNotEmpty(response.getData())) {
                vmInstanceIds = response.getData().stream().map(t -> t.getId().toString()).collect(Collectors.toList());
                request.getBody().getCloud().put("clusterIds", vmInstanceIds);
                request.getBody().getCloud().put("hostPoolId", arguments.getHostPoolId());
//                response.getData().forEach(t->{
//                    request.getBody().getCloud().put("clusterId",t.getId());
//                });
                request.setAction(ActionType.FETCH_NEUTRON_SWITCH);
                fetchVirtualSwitch(request);
            }

            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("获取集群失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取集群失败");
        }
    }

    /**
     * 同步云主机
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchInstance(BaseCloudRequest request) {
        try {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            log.info("同步云主机开始时间：{}", LocalDateTime.now().format(dtf));
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeVmRequest arguments = request.getBody().getCloud().toJavaObject(DescribeVmRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getComputeUrl(), new String[]{"?", arguments.getOffset(), arguments.getLimit(), Boolean.TRUE.toString()}),
                    "data");
            log.info("同步云主机结束时间：{}", LocalDateTime.now().format(dtf));
            log.info("json:{}", json);
            DescribeInstancesResponse response = JSONObject.parseObject(json, DescribeInstancesResponse.class);
            Map<Class, List> result = convertInstance(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);

            //同步云盘
//            List<String> vmInstanceIds = new ArrayList<>();
//            if (response != null || CollUtil.isNotEmpty(response.getData())) {
//                vmInstanceIds = response.getData().stream().map(t -> t.getId().toString()).collect(Collectors.toList());
//                request.getBody().getCloud().put("vmInstanceIds", vmInstanceIds);
//                request.setAction(ActionType.FETCH_STORAGE_DISK);
//                fetchCloudDisk(request);
//            }
             BaseCloudService.toflatMapGourdResponse(baseResponse, response.getData(), (DescribeInstancesResponse.DescribeInstancesData t) -> {
                List<JobInfo> resultdisk = new ArrayList<>();
                JobInfo subnetJobInfo = new JobInfo();
                request.setAction(ActionType.FETCH_STORAGE_DISK);
                request.getBody().getCloud().put("vmInstanceId", t.getId()+"");
//                request.getBody().getCloud().put("VpcId", t.getInstanceId());
                subnetJobInfo.setRequest(request.cloneJSONObject());
                resultdisk.add(subnetJobInfo);
                return resultdisk.stream();
            });
//            if (response != null || CollUtil.isNotEmpty(response.getData())) {
//                for (DescribeInstancesResponse.DescribeInstancesData instancesData : response.getData()) {
//                    String instanceId = instancesData.getId().toString();
//                    try {
//                        log.info("同步云主机详情开始时间：{}", LocalDateTime.now().format(dtf));
//                        String detailJson = Converts.fetchResourceToJsonString(request,
//                                URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getCloudDiskUrl(instanceId), new String[]{}),
//                                "data");
//                        log.info("同步云主机详情结束时间：{}", LocalDateTime.now().format(dtf));
//                        DescribeInstancesDomainDetailResponse detailResponse = JSONObject.parseObject(detailJson, DescribeInstancesDomainDetailResponse.class);
//                        if (!response.getSuccess()) {
//                            break;
//                        }
//                        Map<Class, List> detailResult = convertCloudDisk(request, detailResponse);
//                        BaseCloudService.fetchSend(request, detailResult);
//                    } catch (Exception e) {
//                        continue;
//                    }
//                }
//            }
            return toPageGourdResponse25(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步云主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云主机资源数据失败");
        }
    }

    /**
     * 同步主机
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHost(BaseCloudRequest request) {
        List<CmdbHostRes> hosts = new ArrayList<>();
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeHostRequest arguments = request.getBody().getCloud().toJavaObject(DescribeHostRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostUrl(), new String[]{"?", arguments.getOffset(), arguments.getLimit()}),
                    "data");
            DescribeHostsResponse response = JSONObject.parseObject(json, DescribeHostsResponse.class);
            Map<Class, List> result = convertHost(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步宿主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.host.fail"), e);
        }
    }

    /**
     * 同步云盘
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchBlockDevice(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeDiskRequest arguments = request.getBody().getCloud().toJavaObject(DescribeDiskRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getStorageUrl(0L), new String[]{"?", arguments.getOffset(), arguments.getLimit()}),
                    "data");
            log.info("同步云盘获取数据:{}", json);
            DescribeDisksResponse response = JSONObject.parseObject(json, DescribeDisksResponse.class);
            log.info("同步云盘数据组装:{}", JSONObject.toJSONString(response));
            Map<Class, List> result = convertBlockDevice(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步云盘资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云盘资源数据失败");
        }
    }

    /**
     * 同步模版
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchTemplate(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeTemplateRequest arguments = request.getBody().getCloud().toJavaObject(DescribeTemplateRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTemplateUrl(), new String[]{"?", arguments.getOffset(), arguments.getLimit()}),
                    "data");
            DescribeTemplateResponse response = JSONObject.parseObject(json, DescribeTemplateResponse.class);
            Map<Class, List> result = convertTemplate(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步虚拟机模版数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步虚拟机模版数据失败");
        }
    }

    /**
     * 同步虚拟交换机
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchVirtualSwitch(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
//            String clusterId = request.getBody().getCloud().getString("clusterIds");
            Collection<String> clusterIds = request.getBody().getCloud().getJSONArray("clusterIds").toJavaList(String.class);
            String hostPoolId = request.getBody().getCloud().getString("hostPoolId");
            log.info("同步虚拟交换机集群id:{}", clusterIds);
            log.info("同步虚拟交换机主机池id:{}" + hostPoolId);
//            Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("vmInstanceMap");
            for (String clusterId : clusterIds) {
                String json = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVirtualSwitchUrl(), new String[]{"?", clusterId}),
                        "data");
                DescribeVirtualSwitchResponse response = JSONObject.parseObject(json, DescribeVirtualSwitchResponse.class);
                Map<Class, List> result = convertVirtualSwitch(request, response, clusterId, hostPoolId);
                BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
                if (baseResponse.getCode() == BaseResponse.ERROR.getCode()) {
                    throw new BaseException(baseResponse);
                }
            }

            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("同步虚拟交换机数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步虚拟交换机数据失败");
        }
    }


    /**
     * 同步虚拟防火墙
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchVirtualFirewall(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeVirtualFirewallRequest arguments = request.getBody().getCloud().toJavaObject(DescribeVirtualFirewallRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVirtualFirewallUrl(), new String[]{"?", arguments.getOffset(), arguments.getLimit()}),
                    "data");
            DescribeVirtualFirewallResponse response = JSONObject.parseObject(json, DescribeVirtualFirewallResponse.class);
            Map<Class, List> result = convertVirtualFirewall(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步虚拟防火墙数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步虚拟防火墙数据失败");
        }
    }

    /**
     * 同步云盘
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchCloudDisk(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
//            Collection<String> instanceIds = request.getBody().getCloud().getJSONArray("vmInstanceIds").toJavaList(String.class);
            String vmInstanceId = request.getBody().getCloud().getString("vmInstanceId");
            log.info("同步云盘的云主机id:{}", vmInstanceId);
//            Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("vmInstanceMap");
//            for (String instanceId : instanceIds) {
                String json = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getCloudDiskUrl(vmInstanceId), new String[]{}),
                        "data");
            log.info("同步云盘的数据结果为:{}", json);
                DescribeInstancesDomainDetailResponse response = JSONObject.parseObject(json, DescribeInstancesDomainDetailResponse.class);
                Map<Class, List> result = convertCloudDisk(request, response);
                BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
                if (baseResponse.getCode() == BaseResponse.ERROR.getCode()) {
                    throw new BaseException(baseResponse);
                }
//            }

            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("同步云盘数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云盘数据失败");
        }
    }

    /**
     * 同步主机概要信息
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHostOverviewInfo(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            Collection<String> instanceIds = request.getBody().getJSONArray("instanceIds").toJavaList(String.class);
            Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
            for (String instanceId : instanceIds) {
                String json = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostOverviewInfoUrl(), new String[]{"?", instanceId}),
                        "data");
                DescribeHostOverviewInfoResponse response = JSONObject.parseObject(json, DescribeHostOverviewInfoResponse.class);
                Map<Class, List> result = convertHostOverviewInfo(request, response, instanceId, instanceMap);
                BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
                if (baseResponse.getCode() == BaseResponse.ERROR.getCode()) {
                    throw new BaseException(baseResponse);
                }
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("同步主机概要信息数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步主机概要信息数据失败");
        }
    }

    /**
     * 同步主机性能
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHostPerf(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            //监控请求指标集合
            Collection<String> instanceIds = request.getBody().getJSONArray("instanceIds").toJavaList(String.class);
            Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
            List<JSONObject> metricList = new ArrayList<>();
            for (String instanceId : instanceIds) {
//                //CPU使用率
                String cpu = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getCPUTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeTrendResponse cpuResponse = JSONObject.parseObject(cpu, DescribeTrendResponse.class);
//                List<PerfInfoBean> cpuPerfMap = convertPerf(cpuResponse,instanceId,"cpu",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(cpuPerfMap, "API");
                List<JSONObject> cpuMetricResult = JSON.parseObject(cpu).getJSONArray("data").toJavaList(JSONObject.class);

                // 计算最后 10 条数据的 rate 平均值

                if (CollUtil.isNotEmpty(cpuMetricResult)) {
                    JSONObject cpu1 = calculateOptimized(cpuMetricResult, instanceId, "cpu");
                    metricList.add(cpu1);
                }
                //内存使用率
                String memory = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getMemoryTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeNetworkTrendResponse memoryResponse = JSONObject.parseObject(memory, DescribeNetworkTrendResponse.class);
//                List<PerfInfoBean> memoryPerfMap = convertNetWorkPerf(memoryResponse,instanceId,"memory",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(memoryPerfMap, "API");
                List<JSONObject> memoryMetricResult = JSON.parseObject(memory).getJSONArray("data").toJavaList(JSONObject.class);
                if (CollUtil.isNotEmpty(memoryMetricResult)) {
//                    List<JSONObject> memoryMetricResult = memoryArray.getJSONObject(0).getJSONArray("list").toJavaList(JSONObject.class);
                    // 收集结果到新列表
                    JSONObject memory1 = calculateOptimized(memoryMetricResult, instanceId, "memory");
                    metricList.add(memory1);
                }
                // 收集结果到新列表

                //磁盘使用率
                String disk = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDiskTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeNetworkTrendResponse diskResponse = JSONObject.parseObject(disk, DescribeNetworkTrendResponse.class);
//                List<PerfInfoBean> diskPerfMap = convertNetWorkPerf(diskResponse,instanceId,"disk",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(diskPerfMap, "API");
                JSONArray diskArray = JSON.parseObject(disk).getJSONArray("data");
                if (CollUtil.isNotEmpty(diskArray)) {
                    List<JSONObject> diskMetricResult = diskArray.getJSONObject(0).getJSONArray("list").toJavaList(JSONObject.class);
                    // 收集结果到新列表
                    JSONObject disk1 = calculateOptimized(diskMetricResult, instanceId, "disk");
                    metricList.add(disk1);
                }


//                int diskSize = diskMetricResult.size();
//                int diskStartIndex = Math.max(0, diskSize - 10);
//                diskMetricResult = new ArrayList<>(diskMetricResult.subList(diskStartIndex, diskSize));

                // 收集结果到新列表

                //网络吞吐量
                String network = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getNetWorkTrendTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeNetworkTrendResponse networkResponse = JSONObject.parseObject(network, DescribeNetworkTrendResponse.class);
//                List<PerfInfoBean> networkPerfMap = convertNetWorkPerf(networkResponse,instanceId,"network",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(networkPerfMap, "API");
                JSONArray networkArray = JSON.parseObject(network).getJSONArray("data");
                if (CollUtil.isNotEmpty(networkArray)) {
                    List<JSONObject> networkMetricResult = networkArray.getJSONObject(0).getJSONArray("list").toJavaList(JSONObject.class);
                    // 收集结果到新列表
                    JSONObject network1 = calculateOptimized(networkMetricResult, instanceId, "network");
                    metricList.add(network1);
                }

            }

            Map<String, PerfInfoBean> perfMap = Converts.convertEcsPerf(instanceMap, metricList);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("同步主机性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步主机性能数据失败");
        }
    }


    /**
     * 同步云主机性能
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchInstancePerf(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            //监控请求指标集合
            Collection<String> instanceIds = request.getBody().getCloud().getJSONArray("vmInstanceIds").toJavaList(String.class);
            Map<String, ?> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().getCloud().get("vmInstanceMap");
            List<JSONObject> metricList = new ArrayList<>();
            for (String instanceId : instanceIds) {
                //CPU使用率
                String cpu = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmCPUTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
                List<JSONObject> cpuMetricResult = JSON.parseObject(cpu).getJSONArray("data").toJavaList(JSONObject.class);

                if (CollUtil.isNotEmpty(cpuMetricResult)) {
                    JSONObject cpu1 = calculateOptimized(cpuMetricResult, instanceId, "cpu");
                    metricList.add(cpu1);
                }

//                List<PerfInfoBean> cpuPerfMap = convertPerf(cpuResponse,instanceId,"cpu",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(cpuPerfMap, "API");
                //内存使用率
                String memory = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmMemoryTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeTrendResponse memoryResponse = JSONObject.parseObject(memory, DescribeTrendResponse.class);
//                List<PerfInfoBean> memoryPerfMap = convertPerf(memoryResponse,instanceId,"memory",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(memoryPerfMap, "API");
                List<JSONObject> memoryMetricResult = JSON.parseObject(memory).getJSONArray("data").toJavaList(JSONObject.class);

                // 计算最后 10 条数据的 rate 平均值
                if (CollUtil.isNotEmpty(memoryMetricResult)) {
                    JSONObject memory1 = calculateOptimized(memoryMetricResult, instanceId, "memory");
                    metricList.add(memory1);
                }

//                metricList.addAll(memoryMetricResult);


                //磁盘使用率
                String disk = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmDiskTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeNetworkTrendResponse diskResponse = JSONObject.parseObject(disk, DescribeNetworkTrendResponse.class);
//                List<PerfInfoBean> diskPerfMap = convertNetWorkPerf(diskResponse,instanceId,"disk",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(diskPerfMap, "API");
                JSONArray diskArray = JSON.parseObject(disk).getJSONArray("data");
                if (CollUtil.isNotEmpty(diskArray)) {
                    List<JSONObject> diskMetricResult = diskArray.getJSONObject(0).getJSONArray("list").toJavaList(JSONObject.class);
                    // 收集结果到新列表
                    JSONObject disk1 = calculateOptimized(diskMetricResult, instanceId, "disk");
                    metricList.add(disk1);
                }

                //网络吞吐量
                String network = Converts.fetchResourceToJsonString(request,
                        URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getVmNetWorkTrendTrendUrl(Integer.valueOf(instanceId)), new String[]{"?", "30"}),
                        "data");
//                DescribeNetworkTrendResponse networkResponse = JSONObject.parseObject(network, DescribeNetworkTrendResponse.class);
//                List<PerfInfoBean> networkPerfMap = convertNetWorkPerf(networkResponse,instanceId,"network",instanceMap);
//                BaseCloudService.toPerfMessageAndSend(networkPerfMap, "API");
                JSONArray networkArray = JSON.parseObject(network).getJSONArray("data");
                if (CollUtil.isNotEmpty(networkArray)) {
                    List<JSONObject> networkMetricResult = networkArray.getJSONObject(0).getJSONArray("list").toJavaList(JSONObject.class);
                    // 收集结果到新列表
                    JSONObject network1 = calculateOptimized(networkMetricResult, instanceId, "network");
                    metricList.add(network1);
                }


//                networkMetricResult = networkMetricResult.stream()
//                        .map(t -> {
//                            t.put("metricName", "disk");
//                            t.put("instanceId", instanceId);
//                            return t; // 直接返回修改后的t
//                        }).collect(Collectors.toList());
//                // 收集结果到新列表
//                metricList.addAll(networkMetricResult);

            }
            Map<String, PerfInfoBean> perfMap = Converts.convertEcsPerf(instanceMap, metricList);
            log.info("UIS转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("同步云主机性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云主机性能数据失败");
        }
    }


    public static boolean defaultBeforeBasicInfo(BaseCloudRequest request) {
        //转换分页获取物理机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        searchRequest.setSize(50);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得主机集合
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> result = ApiFactory.Api.res.listHostStoragePool(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchRequest.setCurrent(t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);

        Map<String, ResHostStoragePoolApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResHostStoragePoolApiModel::getOpen_id, t -> t);
        //设置采集监控数据主机ID
        request.getBody().put("instanceMap", instanceMap);
        Set<String> instanceIds = instanceMap.keySet();
        request.getBody().put("instanceIds", instanceIds);
        return true;
    }

    public static boolean defaultVmBeforeBasicInfo(BaseCloudRequest request) {
        //转换分页获取云主机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        searchRequest.setSize(50);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchRequest.setCurrent(t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().getCloud().put("vmResponse", baseResponse);

        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().getCloud().put("vmInstanceMap", instanceMap);
        //设置采集监控数据主机ID
        Set<String> instanceIds = instanceMap.keySet();
        request.getBody().getCloud().put("vmInstanceIds", instanceIds);
        return true;
    }

//    //获取组织资源数据并推送到北新仓下
//    public static BaseResponse fetchProject(JSONObject arguments) {
//        String message = "成功获取组织资源信息.";
//        try{
//            PlatProjectService.bean.fetchProject(arguments);
//        }  catch (Exception e) {
//            log.error("同步组织资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步组织资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

//    public static BaseResponse fetchUser(JSONObject arguments) {
//        String message = "成功获取用户资源信息.";
//        try{
//            PlatProjectService.bean.fetchUser(arguments);
//        }  catch (Exception e) {
//            log.error("同步用户资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步用户资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//    public static BaseResponse fetchAzone(JSONObject arguments) {
//        String message = "成功获取可用域资源信息.";
//        try{
//            PlatAZoneService.bean.fetchAZone(arguments);
//            //PlatAZoneService.bean.fetchRegionAndZone(arguments);
//        }  catch (Exception e) {
//            log.error("同步可用域资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步可用域资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

//    public static BaseResponse fetchAlarm(JSONObject arguments) {
//        String message = "成功获取告警资源信息.";
//        try{
//            AlarmService.bean.fetchAlarm(arguments);
//        }  catch (Exception e) {
//            log.error("同步告警资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步告警资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

//    public static BaseResponse fetchSnapshot(JSONObject arguments) {
//        String message = "成功获取磁盘快照资源信息.";
//        try{
//            StorageSnapshotService.bean.fetchSnapshot(arguments);
//        }  catch (Exception e) {
//            log.error("同步磁盘快照资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步磁盘快照资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//    public static BaseResponse fetchVpc(JSONObject arguments) {
//        String message = "成功获取vpc资源信息.";
//        try{
//            NetworkVpcService.bean.fetchVpc(arguments);
//        }  catch (Exception e) {
//            log.error("同步vpc资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步vpc资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

//    public static BaseResponse fetchSubnet(JSONObject arguments) {
//        String message = "成功获取子网资源信息.";
//        try{
//            NetworkSubnetService.bean.fetchSubnet(arguments);
//        }  catch (Exception e) {
//            log.error("同步子网资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步子网资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//
//    public static BaseResponse fetchCard(JSONObject arguments) {
//        String message = "成功获取网卡资源信息.";
//        try{
//            NetworkNicService.bean.fetchNic(arguments);
//        }  catch (Exception e) {
//            log.error("同步网卡资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步网卡资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse fetchNetwork(JSONObject arguments) {
//        String message = "成功获取经典网络资源信息.";
//        try{
//            NetworkVswitchService.bean.fetchNetwork(arguments);
//        }  catch (Exception e) {
//            log.error("同步经典网络资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步经典网络资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//

//
//
//
//
//    public static BaseResponse fetchFlavor(JSONObject arguments) {
//        String message = "成功获取规格资源信息.";
//        try{
//            ComputeFlavorService.bean.fetchFlavor(arguments);
//        }  catch (Exception e) {
//            log.error("同步规格资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步规格资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//    public static BaseResponse fetchImage(JSONObject arguments) {
//        String message = "成功获取镜像资源信息.";
//        try{
//            StorageImageService.bean.fetchImage(arguments);
//        }  catch (Exception e) {
//            log.error("同步镜像资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步镜像资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    public static BaseResponse fetchComputenode(JSONObject arguments) {
//        String message = "成功获取计算节点资源信息.";
//        try{
//            DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
//            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取宿主机列表并拆分dataJob
//                switch (request.getResourceType()){
//                    case "host"://同步宿主机详情
//                        ComputeHostService.bean.fetchHost(request,arguments);
//                        break;
//                    case "storagePool"://存储池
//                        StoragePoolService.bean.fetchStoragePool(request,arguments);
//                        break;
//                    case "vms"://同步宿主机与云主机的关系数据
//                        ComputeHostService.bean.fetchHostVmRelation(request,arguments);
//                        break;
//                    default:
//                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
//                }
//            }else{
//                return new GourdJobResponse(ComputeNodeService.bean.splitInstanceDataJob(request, arguments),message);
//            }
//            return BaseResponse.SUCCESS.of(message);
//        }  catch (Exception e) {
//            log.error("同步计算节点资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步计算节点资源数据失败");
//        }
//    }
//
//    /**
//     * 获取路由器数据
//     * @param arguments
//     * @return
//     */
//    public static BaseResponse fetchRouter(JSONObject arguments) {
//        String message = "成功获取路由器信息.";
//        try{
//            NetworkRouterService.bean.fetchRouter(arguments);
//        }  catch (Exception e) {
//            log.error("同步路由器数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步路由器数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//
//    /**
//     * 获取秘钥对数据
//     * @param arguments
//     * @return
//     */
//    public static BaseResponse fetchKeypair(JSONObject arguments) {
//        String message = "成功获取秘钥对信息.";
//        try{
//            PlatKeypairService.bean.fetchKeypair(arguments);
//        }  catch (Exception e) {
//            log.error("同步秘钥对数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步秘钥对数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }
//
//    /**
//     * 获取安全组及规则
//     * @param arguments
//     * @return
//     */
//    public static BaseResponse fetchSecurityGroup(JSONObject arguments) {
//        String message = "成功获取获取安全组及规则信息.";
//        try {
//            NetworkSecurityGroupService.bean.fetchSecurityGroup(arguments);
//        } catch (Exception e) {
//            log.error("同步安全组资源数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步安全组资源数据失败");
//        }
//        return BaseResponse.SUCCESS.of(message);
//    }

    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("offset", (t - 1) * 100);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static BaseResponse toPageGourdResponse25(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("offset", (t - 1) * 15);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    /**
     * 监控数据最近10条求平均值
     *
     * @return
     */
    public static JSONObject getMetricAverageRateData(List<JSONObject> metricResult, String instanceId, String metricName) {
        JSONObject jsonObject = new JSONObject();
        // 步骤 1：找到最近的 5 分钟倍数的时间节点（从后往前找）
        long time1 = 0;
        for (int i = metricResult.size() - 1; i >= 0; i--) {
            JSONObject obj = metricResult.get(i);
            long time = obj.getLongValue("time");
            Date date = new Date(time);
            // 将时间戳转换为分钟和秒
            int seconds = date.getSeconds();
            int minutes = date.getMinutes();
            // 判断分钟是否是5的倍数且秒为0
            if (minutes % 5 == 0 && seconds == 0) {
                time1 = time;
                break;
            }
        }
        if (time1 == 0) {
            System.out.println("未找到符合条件的时间节点,metricName:" + metricName);

        }
// 步骤 2：从该时间节点开始往前取 10 条数据（包含该节点）
        int index = -1;
        // 找到目标时间戳在数据列表中的索引
        for (int i = 0; i < metricResult.size(); i++) {
            if (metricResult.get(i).getLongValue("time") == time1) {
                index = i;
                break;
            }
        }
        if (index == -1) {
            return jsonObject;
        }
        // 往前取 count 条数据（注意索引范围，避免越界）
        int startIndex = Math.max(0, index - 10 + 1);
        List<JSONObject> dataList = metricResult.subList(startIndex, index + 1);
        // 步骤 3：计算这 10 条数据 rate 字段的平均值
        double sum = dataList.stream()
                .mapToDouble(obj -> obj.getDoubleValue("rate"))
                .sum();
        double averageRate = sum / dataList.size();


        jsonObject.put("metricName", metricName);
        jsonObject.put("instanceId", instanceId);
        jsonObject.put("rate", averageRate);
        jsonObject.put("time", time1);
        return jsonObject;
    }

    // 5分钟的毫秒数
    private static final long FIVE_MINUTES_MS = 5 * 60 * 1000;

    /**
     * 计算每5分钟的平均值 - 完全使用JSONObject
     * 输入和输出都是JSON对象
     */
    public static JSONObject calculateOptimized(List<JSONObject> jsonArray, String instanceId, String metricName) {
        // 使用ConcurrentHashMap存储中间计算结果
        ConcurrentHashMap<Long, JSONObject> statsMap = new ConcurrentHashMap<>(
                Math.max(16, jsonArray.size() / 8));

        // 使用并行流处理数据
        IntStream.range(0, jsonArray.size())
                .parallel()
                .forEach(i -> {
                    JSONObject jsonObj = jsonArray.get(i);

                    // 从JSONObject中直接获取rate和time
                    double rate = jsonObj.getDouble("rate");
                    long time = jsonObj.getLong("time");

                    // 计算时间键 - 直接使用毫秒计算
                    long timeKey = (time / FIVE_MINUTES_MS) * FIVE_MINUTES_MS;

                    // 使用computeIfAbsent避免多次查找
                    statsMap.computeIfAbsent(timeKey, k -> {
                        JSONObject stats = new JSONObject();
                        stats.put("sum", 0.0);
                        stats.put("count", 0);
                        return stats;
                    });

                    // 使用同步块更新统计数据
                    synchronized (statsMap.get(timeKey)) {
                        JSONObject stats = statsMap.get(timeKey);
                        stats.put("sum", stats.getDouble("sum") + rate);
                        stats.put("count", stats.getInteger("count") + 1);
                    }
                });

        // 创建结果JSONArray
        JSONArray resultArray = new JSONArray();

        // 转换结果并排序
        statsMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    long timeKey = entry.getKey();
                    JSONObject stats = entry.getValue();

                    // 计算平均值
                    double averageRate = stats.getDouble("sum") / stats.getInteger("count");

                    // 创建结果JSONObject
                    JSONObject resultObj = new JSONObject();
                    resultObj.put("time", timeKey);
                    resultObj.put("rate", averageRate);
                    resultObj.put("formattedTime", formatTime(timeKey));
                    resultObj.put("metricName", metricName);
                    resultObj.put("instanceId", instanceId);
                    // 添加到结果数组
                    resultArray.add(resultObj);
                });

        return resultArray.getJSONObject(resultArray.size() - 1);
    }

    // 格式化时间戳以便显示
    private static String formatTime(long timestamp) {
        Date date = new Date(timestamp);
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * 同步告警
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            DescribeAlarmRequest arguments = request.getBody().getCloud().toJavaObject(DescribeAlarmRequest.class);
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getAlarmUrl(), new String[]{"?", arguments.getOffset(),
                            arguments.getLimit(), URLUtil.encode(arguments.getEventTimeFrom()), URLUtil.encode(arguments.getEventTimeTo())}),
                    "data");
            log.info("json:{}", json);
            DescribeAlarmsResponse response = JSONObject.parseObject(json, DescribeAlarmsResponse.class);
            List<AlarmInfoBean> result = convertAlarm(request, response);
            BaseCloudService.toAetMessageAndSend(result, "alarm");
            BaseResponse baseResponse = BaseResponse.SUCCESS.of("本次获取告警数据:" + result.size() + "条");
            return toPageGourdResponse(request, baseResponse,
                    response.getTotalLength(),
                    request.getBody().getCloud().getInteger("limit"));
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步告警数据失败");
        }
    }
}
