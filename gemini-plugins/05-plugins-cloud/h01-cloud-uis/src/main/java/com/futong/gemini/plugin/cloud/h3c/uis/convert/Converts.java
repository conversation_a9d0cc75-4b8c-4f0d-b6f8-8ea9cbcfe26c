package com.futong.gemini.plugin.cloud.h3c.uis.convert;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.BaseSearchApiModel;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.h3c.uis.common.Constant;
import com.futong.gemini.plugin.cloud.h3c.uis.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.h3c.uis.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.h3c.uis.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.h3c.uis.response.*;
import com.futong.gemini.plugin.cloud.h3c.uis.vo.UisToken;
import com.futong.gemini.plugin.cloud.sdk.constant.BaseConstant;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OsType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;

import static com.futong.gemini.plugin.cloud.h3c.uis.sampler.FetchService.toCiResCloud;
@Slf4j
public class Converts {
    public static <T> T parseAndConvert(String jsonString, TypeReference<T> typeRef) {
        return JSON.parseObject(jsonString, typeRef);
    }

    public static Map<Class, List> convertHostPool(BaseCloudRequest request, DescribeHostPoolsResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getData())){
            result.put(TmdbDevopsLink.class,null);
            result.put(TmdbDevops.class,null);
            return result;
        }
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_HOST_POOL.value(),
                response.getData(),
                DescribeHostPoolsResponse.DescribeHostPoolsData::getHpName,
                data -> String.valueOf(data.getHpId())
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }
        /**
         * 封装资源唯一ID
         */
        public static Map<Class, List> convertCluster(BaseCloudRequest request, DescribeClustersResponse response){
            Map<Class, List> result = new HashMap<>();
            if(response == null || CollUtil.isEmpty(response.getData())){
                result.put(TmdbDevops.class,null);
                result.put(TmdbDevopsLink.class,null);
                return result;
            }
            List<TmdbDevops> data = new ArrayList<>();
            List<TmdbDevopsLink> links = new ArrayList<>();
            BuilderDevops devops = new BuilderDevops()
                    .withInfo(request.getBody().getAccess().getCmpId(),
                            request.getPlugin().getRealm(),
                            request.getBody().getCloud().getString("hostPoolId"),
                            DevopsSide.DEVOPS_HOST_POOL.value());
            for (DescribeClustersResponse.DescribeClustersData cluster : response.getData()) {
                BuilderDevops devopsCluster = new BuilderDevops()
                        .withDevops(devops.get(), cluster.getName(), cluster.getId().toString(), DevopsSide.DEVOPS_CLUSTER.value())
                        .withJson(JSON.toJSONString(cluster));
                data.add(devopsCluster.get());
                links.add(devopsCluster.builderLink(devops.get()));
            }
            result.put(TmdbDevops.class,data);
            result.put(TmdbDevopsLink.class,links);
            return result;
        }

    public static Map<Class, List> convertInstance(BaseCloudRequest request, DescribeInstancesResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || ObjUtil.isEmpty(response)){
            result.put(CmdbInstanceRes.class,null);
            result.put(Association.class,null);
            result.put(CmdbOsRes.class,null);
            result.put(CmdbDiskRes.class,null);
            result.put(CmdbNetcardRes.class,null);
            result.put(CmdbIpRes.class,null);
            result.put(TmdbResourceSet.class,null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_INSTANCE_RES.value()
                );
        List<CmdbInstanceRes> instances = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbDiskRes> dkList = new ArrayList<>();
        List<CmdbNetcardRes> netcardResList = new ArrayList<>();
        List<CmdbIpRes> ipList = new ArrayList<>();
        List<CmdbBaseMetainfo> metainfos = new ArrayList<>();
        CloudAccessBean accessBean = request.getBody().getAccess();
        for (DescribeInstancesResponse.DescribeInstancesData vm : response.getData()) {
//            List<Disk> diskList = new ArrayList<>();
//            List<Nic> nicList = new ArrayList<>();
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),CmdbInstanceRes.class.getSimpleName(),vm.getId().toString()));
            ci.setOpen_id(vm.getId().toString());
            ci.setOpen_name(vm.getTitle());
            ci.setCpu_size(vm.getCpu());
            ci.setMem_size(vm.getMemory());
//            ci.setOpen_status(vm.getStatus().toString());
//            VmConfig config = vm.getVmConfig();
//            if(config != null){
//                if(config.getCpu() != null){
//                    ci.setCpu_size(config.getCpu().getQuantity());
//                }
//                if(config.getMemory() != null){
//                    ci.setMem_size(config.getMemory().getQuantityMB());
//                }
//                diskList = config.getDisks();
//                nicList = config.getNics();
//            }
//            虚拟机状态。1:未知 2:运行 3:关闭 4 暂停
            switch (vm.getStatus().toString()) {
                case "2":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "3":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "1":
                    ci.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
                case "shutOff":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
                default:
                    ci.setStatus(vm.getStatus().toString());
                    break;
            }
            ci.setOpen_status(vm.getStatus().toString());
//            ci.setIs_template(vm.getIsTemplate() ? 1 : 0);
            ci.setDesc(vm.getDescription());
            toCiResCloud(request,ci);
//            OsOption osOption = vm.getOsOptions();
//            if(ObjectUtil.isNotNull(osOption)){
//                CmdbOsRes os = new CmdbOsRes();
//                os.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), vm.getUrn()));
//                os.setOpen_name(osOption.getGuestOSName());
//                os.setType(osOption.getOsType());
//                os.setVersion(osOption.getOsType());
//                toCiResCloud(request,os);
//                osList.add(os);
//                associations.add(AssociationUtils.toAssociation(ci, os));
//            }
//            //查询关联磁盘
//            String json = Converts.fetchResourceToJsonString(request,
//                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getCloudDiskUrl(ci.getOpen_id()), new String[]{}),
//                    "data");
//            DescribeInstancesDomainDetailResponse diskresp = JSONObject.parseObject(json, DescribeInstancesDomainDetailResponse.class);
//
//            if(!response.getSuccess()){
//                break;
//            }
            //关联集群
            if (ObjUtil.isNotEmpty(vm.getClusterId())) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, vm.getClusterId().toString());}
            //关联HOST
            if (StrUtil.isNotEmpty(vm.getHostId().toString())) {
                Association host = AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(accessBean.getCmpId(),CmdbHostRes.class.getSimpleName(), vm.getHostId().toString()));
                associations.add(host);
            }
            if(ObjectUtil.isNotEmpty(vm.getSystem())){
                CmdbOsRes osRes = new CmdbOsRes();
                osRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),CmdbOsRes.class.getSimpleName(),vm.getId().toString()));
                osRes.setOpen_id(vm.getId().toString());
                osRes.setOpen_name(vm.getTitle());
                if(ObjectUtil.isNotEmpty(vm.getSystem())){
                    switch (vm.getSystem()){
                        case 0:
                            osRes.setType(OsType.WINDOWS.getValue());
                            break;
                        case 1:
                            osRes.setType(OsType.LINUX.getValue());
                            break;
                        case 2:
                            osRes.setType("BSD");
                            break;
                    }
                }
                osRes.setFull_name(vm.getOsBit());
                osRes.setVersion(vm.getOsBit());
                toCiResCloud(request, osRes);
                osList.add(osRes);
                associations.add(AssociationUtils.toAssociation(ci, osRes));
            }
            //磁盘信息
//            if(CollUtil.isNotEmpty(diskList)){
//                diskList.forEach(disk -> {
//                    CmdbDiskRes dk = new CmdbDiskRes();
//                    dk.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), disk.getVolumeUrn()));
//                    dk.setOpen_id(disk.getVolumeUuid());
//                    dk.setOpen_name(disk.getDiskName());
//                    dk.setShare_disk(disk.getIndepDisk()+"");
//                    dk.setCategory(disk.getSystemVolume()?"system":"data");
//                    dk.setSize(Float.parseFloat(disk.getQuantityGB()+""));
//                    toCiResCloud(request,dk);
//                    dkList.add(dk);
//                    Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, dk.getRes_id());
//                    associations.add(diskAss);
//                });
//            }
            //网卡和IP信息
            if(CollUtil.isNotEmpty(vm.getDomainIps())){
//                vm.getDomainIps().stream().filter(!"::")
                vm.getDomainIps().forEach(iprs -> {
//                    CmdbNetcardRes card = new CmdbNetcardRes();
//                    card.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), nic.getUrn()));
//                    card.setOpen_id(nic.getUrn());
//                    card.setOpen_name(nic.getName());
//                    card.setMac_address(nic.getMac());
//                    card.setType(nic.getNicType()+"");
                    if(StrUtil.isNotEmpty(iprs)){
                        CmdbIpRes ip = new CmdbIpRes();
                        ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), iprs));
                        ip.setType(IpType.PRIVATE_IP.value());
                        ip.setAddress(iprs);
                        ip.setOpen_id(iprs);
                        ip.setOpen_name(iprs);
                        toCiResCloud(request, ip);
                        ipList.add(ip);
                        associations.add(AssociationUtils.toAssociation(ci, ip));
                    }
//                    if(CollUtil.isNotEmpty(nic.getIps6())){
//                        nic.getIps6().forEach(ip6->{
//                            CmdbIpRes ip = new CmdbIpRes();
//                            ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),ip6));
//                            ip.setType(IpType.PRIVATE_IP.value());
//                            ip.setAddress(ip6);
//                            ip.setOpen_id(ip6);
//                            ip.setOpen_name(ip6);
//                            toCiResCloud(request, ip);
//                            ipList.add(ip);
//                            associations.add(AssociationUtils.toAssociation(card, ip));
//                        });
//                    }
//                    if(StrUtil.isNotEmpty(nic.getIpList())){
//                        Arrays.stream(nic.getIpList().split(",")).forEach(ipAddress->{
//                            CmdbIpRes ip = new CmdbIpRes();
//                            ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), ipAddress));
//                            ip.setType(IpType.PRIVATE_IP.value());
//                            ip.setAddress(ipAddress);
//                            ip.setOpen_id(ipAddress);
//                            ip.setOpen_name(ipAddress);
//                            toCiResCloud(request, ip);
//                            ipList.add(ip);
//                            associations.add(AssociationUtils.toAssociation(card, ip));
//                        });
//                    }
//                    toCiResCloud(request,card);
//                    netcardResList.add(card);
                    //云主机与Nic关联
//                    associations.add(AssociationUtils.toAssociation(ci, card));
                });
            }
            //关联资源池
//            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, siteResId);

            CmdbBaseMetainfo metainfo = new CmdbBaseMetainfo();
            metainfo.setRes_id(ci.getRes_id());
            metainfo.setTable("cmdb_instance_res");
            metainfo.setMetainfo(JSON.toJSONString(vm));
            toCiResCloud(request, metainfo);
            metainfos.add(metainfo);

            instances.add(ci);
        }
        result.put(CmdbInstanceRes.class,instances);
        result.put(CmdbBaseMetainfo.class,metainfos);
        result.put(Association.class,associations);
        result.put(CmdbOsRes.class,osList);
        result.put(CmdbDiskRes.class,dkList);
        result.put(CmdbNetcardRes.class,netcardResList);
        result.put(CmdbIpRes.class,ipList);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertHost(BaseCloudRequest request,DescribeHostsResponse response){
        Map<Class, List> result = new HashMap<>();
        if(response == null  || CollUtil.isEmpty(response.getData())){
            result.put(CmdbHostRes.class,null);
            result.put(Association.class,null);
            result.put(TmdbResourceSet.class,null);
            result.put(CmdbIpRes.class,null);
//            result.put(CmdbStoragePoolRes.class,null);
            return result;
        }
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbHostRes> data = new ArrayList<>();
        List<CmdbIpRes> ipList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
//        List<CmdbStoragePoolRes> storagePoolResList = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
//                        CloudType.PRIVATE_UIS,
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_HOST_RES.value()
                );
        for (DescribeHostsResponse.DescribeHostsData res : response.getData()) {
            CmdbHostRes ci = new CmdbHostRes();
            ci.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),CmdbHostRes.class.getSimpleName(), res.getId().toString()));
            ci.setOpen_id(res.getId().toString());
            ci.setOpen_name(res.getName());
            //主机状态 0为不正常，1为正常
            switch (res.getStatus()) {
                case 0:
                    ci.setStatus(InstanceStatus.ERROR.value());
                    break;
                case 1:
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
            }
            ci.setOpen_status(res.getStatus().toString());
            ci.setIp(res.getIpAddr());
            ci.setCpu_size(res.getCpu());
            ci.setMem_size(res.getMemory());
            ci.setMaintain_mode("on".equals(res.getMaintainMode()) ? "1" : "0");
            toCiResCloud(request,ci);

            //存储总量
            if(ObjectUtil.isNotEmpty(res.getStorage())){
                float total = Float.valueOf(ObjectUtil.isNotEmpty(res.getStorage()) ? res.getStorage() : 0) * 1024;
                ci.setTotal_size(total);
//                CmdbStoragePoolRes storagePoolRes = new CmdbStoragePoolRes();
//                storagePoolRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), res.getId().toString()));
//                storagePoolRes.setOpen_id(res.getId().toString());
//                storagePoolRes.setOpen_name(res.getName());
//                storagePoolRes.setTotal_size(total);
//                toCiResCloud(request,storagePoolRes);
//                associations.add(AssociationUtils.toAssociation(ci, storagePoolRes));
//                storagePoolResList.add(storagePoolRes);
            }
            data.add(ci);
            //关联集群
            if(ObjectUtil.isNotEmpty(res.getClusterId())){
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, res.getClusterId().toString());
            }
            //关联主机池
            if(StrUtil.isNotEmpty(res.getHostpoolId())){
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_HOST_POOL, res.getHostpoolId());
            }
            if(StrUtil.isNotEmpty(res.getIpAddr())){
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),res.getIpAddr()));
                ip.setOpen_id(res.getIpAddr());
                ip.setOpen_name(res.getIpAddr());
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(res.getIpAddr());
                toCiResCloud(request,ip);
                ipList.add(ip);
                associations.add(AssociationUtils.toAssociation(ci, ip));
            }
        }
        result.put(CmdbHostRes.class,data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        result.put(CmdbIpRes.class, ipList);
//        result.put(CmdbStoragePoolRes.class, storagePoolResList);
        return result;
    }

    public static Map<Class,List> convertBlockDevice(BaseCloudRequest request, DescribeDisksResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getData())){
            result.put(CmdbBlockDeviceRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        List<CmdbBlockDeviceRes> data = new ArrayList<>();
        List<CmdbBaseMetainfo> metainfos = new ArrayList<>();
        for (DescribeDisksResponse.DescribeDisksData res : response.getData()) {
            CmdbBlockDeviceRes ci = new CmdbBlockDeviceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbBlockDeviceRes.class.getSimpleName(), String.valueOf(res.getLunId())));
            ci.setDesc(ObjectUtil.isNotEmpty(res.getDesc())?res.getDesc():null);
            ci.setOpen_id(StrUtil.isNotEmpty(res.getLunId())?res.getLunId():null);
            ci.setOpen_name(ObjectUtil.isNotEmpty(res.getLunName())?res.getLunName():null);
            ci.setLun_size(Math.toIntExact(ObjectUtil.isNotEmpty(res.getLunSize()) ? res.getLunSize() : 0));
            ci.setLun_type(ObjectUtil.isNotEmpty(res.getLunType())?res.getLunType():null);
            ci.setDiskpool_name(ObjectUtil.isNotEmpty(res.getDiskpoolName())?res.getDiskpoolName():null);
            ci.setHost_group_id(ObjectUtil.isNotEmpty(res.getHostGroupId())?res.getHostGroupId():null);
            ci.setHost_group_name(ObjectUtil.isNotEmpty(res.getHostGroupName())?res.getHostGroupName():null);
            ci.setHost_lun_id(String.valueOf(ObjectUtil.isNotEmpty(res.getHostLunId())?res.getHostLunId():null));
            ci.setQos_name(ObjectUtil.isNotEmpty(res.getQosName())?res.getQosName():null);
//            ci.setNode_pool_name(ObjectUtil.isNotEmpty(res.getNodePoolName())?res.getNodePoolName():null);
//            ci.setPool_name(ObjectUtil.isNotEmpty(res.getPoolName())?res.getPoolName():null);
//            ci.setOld_host_group_id(ObjectUtil.isNotEmpty(res.getOldHostGroupId())?res.getOldHostGroupId():null);
//            ci.setOld_size(ObjectUtil.isNotEmpty(res.getOldSize())?res.getOldSize():null);
            toCiResCloud(request, ci);
            data.add(ci);


            CmdbBaseMetainfo metainfo = new CmdbBaseMetainfo();
            metainfo.setRes_id(ci.getRes_id());
            metainfo.setTable("cmdb_block_device_res");
            metainfo.setMetainfo(JSON.toJSONString(res));
            toCiResCloud(request, metainfo);
            metainfos.add(metainfo);


        }
        result.put(CmdbBlockDeviceRes.class, data);
        //目前暂无关联的资源 暂传null
        result.put(TmdbResourceSet.class, null);
        result.put(CmdbBaseMetainfo.class,metainfos);
        return result;
    }


    public static Map<Class,List> convertTemplate(BaseCloudRequest request,DescribeTemplateResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || CollUtil.isEmpty(response.getData())){
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        List<CmdbInstanceRes> data = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_INSTANCE_RES.value()
                );
        for (DescribeTemplateResponse.DescribeTemplateData res : response.getData()) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbInstanceRes.class.getSimpleName(),"vmTemplate", String.valueOf(res.getId())));
            ci.setOpen_id(String.valueOf(ObjectUtil.isNotEmpty(res.getId())?res.getId():null));
            ci.setOpen_name(ObjectUtil.isNotEmpty(res.getDomainName())?res.getDomainName():null);
            ci.setCpu_size(ObjectUtil.isNotEmpty(res.getCpu())?res.getCpu():null);
            ci.setMem_size(ObjectUtil.isNotEmpty(res.getMemory())?res.getMemory():null);
            ci.setDesc(ObjectUtil.isNotEmpty(res.getDescription())?res.getDescription():null);
            ci.setIs_template(1);
            ci.setOpen_create_time(ObjectUtil.isNotEmpty(res.getCreateDate())?res.getCreateDate():null);
            toCiResCloud(request, ci);
            data.add(ci);
            //关联集群
            if(ObjectUtil.isNotEmpty(res.getClusterId())){
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getClusterId());
            }
        }
        result.put(CmdbInstanceRes.class, data);
        //目前暂无关联的资源 暂传null
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class,List> convertVirtualSwitch(BaseCloudRequest request,DescribeVirtualSwitchResponse response,String clusterId ,String hostPoolId){
        Map<Class,List> result = new HashMap<>();
        if(response == null ||ObjectUtil.isEmpty(response.getData())){
            result.put(CmdbVswitchRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_VSWITCH_RES.value()
                );
        List<CmdbVswitchRes> data = new ArrayList<>();
        for (DescribeVirtualSwitchResponse.DescribeVirtualSwitchData res : response.getData()) {
            CmdbVswitchRes ci = new CmdbVswitchRes();
            ci.setOpen_id(String.valueOf(ObjectUtil.isNotEmpty(res.getId())?res.getId():res.getName()));
            ci.setOpen_name(ObjectUtil.isNotEmpty(res.getName())?res.getName():null);
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbVswitchRes.class.getSimpleName(), String.valueOf(ci.getOpen_id())));
//            ci.setBond_mode(ObjectUtil.isNotEmpty(res.getBondMode())?res.getBondMode():null);
            ci.setMtu(ObjectUtil.isNotEmpty(res.getMtu())? String.valueOf(res.getMtu()):null);
            ci.setDesc(ObjectUtil.isNotEmpty(res.getDescription())?res.getDescription():null);
            ci.setType(ObjectUtil.isNotEmpty(res.getNetworkType())?String.valueOf(res.getNetworkType()):null);
            toCiResCloud(request, ci);

            //关联集群
            if (StrUtil.isNotEmpty(clusterId)) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, clusterId);
            }
            //关联主机池
            if (StrUtil.isNotEmpty(hostPoolId)) {
                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_HOST_POOL, hostPoolId);
            }
            data.add(ci);
        }
        result.put(CmdbVswitchRes.class, data);

        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }


    public static Map<Class,List> convertVirtualFirewall(BaseCloudRequest request,DescribeVirtualFirewallResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || ObjectUtil.isEmpty(response.getData())){
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        for (DescribeVirtualFirewallResponse.DescribeVirtualFirewallData res : response.getData()) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbSecuritygroupRes.class.getSimpleName(), String.valueOf(res.getId())));
            ci.setOpen_id(String.valueOf(ObjectUtil.isNotEmpty(res.getId())?res.getId():null));
            ci.setOpen_name(ObjectUtil.isNotEmpty(res.getName())?res.getName():null);
            ci.setTitle(ObjectUtil.isNotEmpty(res.getTitle())?res.getTitle():null)  ;
            ci.setDesc(ObjectUtil.isNotEmpty(res.getDescription())?res.getDescription():null);
            ci.setRule_action(ObjectUtil.isNotEmpty(res.getRuleAction())?res.getRuleAction():null);
            toCiResCloud(request, ci);
            data.add(ci);
        }
        result.put(CmdbSecuritygroupRes.class, data);
        //目前暂无关联的资源 暂传null
        result.put(TmdbResourceSet.class, null);
        return result;
    }

    public static List<AlarmInfoBean> convertAlarm(BaseCloudRequest request, DescribeAlarmsResponse response) {
        List<AlarmInfoBean> result = new ArrayList<>();
        for (DescribeAlarmsResponse.DescribeAlarmsData alarm : response.getData()) {
            AlarmInfoBean ci = new AlarmInfoBean();
            ci.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), AlarmInfoBean.class.getSimpleName(), String.valueOf(alarm.getId())));
            ci.setAccountId(request.getBody().getAccess().getCmpId());
            ci.setCloudType(request.getPlugin().getRealm());
            ci.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), AlarmInfoBean.class.getSimpleName(), String.valueOf(alarm.getId())));
            ci.setOpenId(String.valueOf(alarm.getId()));
            ci.setOpenName(alarm.getEventName());
            ci.setOpenLevel(String.valueOf(alarm.getEventLevel()));
            ci.setAlarmId(String.valueOf(alarm.getId()));
            ci.setAlarmName(alarm.getEventName());
            ci.setDetail(alarm.getEventDesc() + alarm.getEventSrc());
            ci.setClosedStatus(alarm.getState() == 1);
            ci.setJsonInfo(JSON.toJSONString(alarm));
            ci.setCount(1);
            if (ObjectUtil.isNotEmpty(alarm.getEventTime())) {
                String alarmTime = DateUtil.date(Long.parseLong(alarm.getEventTime())).toString("yyyy-MM-dd HH:mm:ss");
                ci.setFirstTime(alarmTime);
                ci.setCreateTime(alarmTime);
            }
            ci.setResourceType(String.valueOf(alarm.getEventType()));
            switch (alarm.getEventLevel()) {
                case 1:
                    ci.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case 2:
                    ci.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case 3:
                    ci.setAlarmLevel(AlarmLevel.MINOR.value());
                    break;
                case 4:
                    ci.setAlarmLevel(AlarmLevel.INFORMATION.value());
                    break;
            }
            result.add(ci);
        }
        return result;
    }

    public static Map<Class,List> convertCloudDisk(BaseCloudRequest request,DescribeInstancesDomainDetailResponse response){
        Map<Class,List> result = new HashMap<>();
        if(response == null || ObjectUtil.isEmpty(response.getData())){
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_DISK_RES.value()
                );
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CmdbInstanceRes instanceRes = new CmdbInstanceRes();
        CmdbHostRes cmdbHostRes = new CmdbHostRes();
        toCiResCloud(request, instanceRes);
        toCiResCloud(request, cmdbHostRes);
//        Object info = instanceMap.get(instanceId);
//        ResInstanceDiskApiModel host = (ResInstanceDiskApiModel) info;
//        instanceRes.setOpen_id(host.getOpen_id());
//        instanceRes.setOpen_name(host.getOpen_name());
//        instanceRes.setRes_id(host.getRes_id());
//        instanceRes.setCpu_size(ObjectUtil.isNotEmpty(host.getCpu_size())?host.getCpu_size():null);
//        instanceRes.setMem_size(ObjectUtil.isNotEmpty(host.getMem_size())?host.getMem_size():null);
//        instanceRes.setStatus(ObjectUtil.isNotEmpty(host.getStatus())?host.getStatus():null);
//        instanceRes.setOpen_status(ObjectUtil.isNotEmpty(host.getOpen_status())?host.getOpen_status():null);
//        instanceRes.setDesc(ObjectUtil.isNotEmpty(host.getDesc())?host.getDesc():null);
//        instanceRes.setIs_template(ObjectUtil.isNotEmpty(host.getIs_template())?host.getIs_template():null);
//        for (DescribeInstancesDomainDetailResponse.DescribeDomainDetailData res : response.getData()) {
//            if(ObjectUtil.isEmpty(response.getData().getDiskList())){
//                break;
//            }
        if(ObjectUtil.isNotEmpty(response.getData()) && ObjectUtil.isNotEmpty(response.getData().getDiskList())){
            DescribeInstancesDomainDetailResponse.DescribeDomainDetailData res = response.getData();
            List<DescribeInstancesDomainDetailResponse.Disk> diskList = res.getDiskList();
            for (DescribeInstancesDomainDetailResponse.Disk disk : diskList) {
                if(ObjectUtil.isEmpty(disk.getDetail())){
                    break;
                }
                DescribeInstancesDomainDetailResponse.StorageDetail detail = disk.getDetail();
                CmdbDiskRes ci = new CmdbDiskRes();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),res.getId().toString(),CmdbDiskRes.class.getSimpleName(),disk.getDispName()));
                ci.setOpen_id(disk.getTabIndex().toString());
                ci.setOpen_name(disk.getDispName());
                //磁盘类型
                ci.setType("system");
                //磁盘大小
                ci.setSize(ObjectUtil.isNotEmpty(detail.getShowSize())?detail.getShowSize().floatValue():0);
                //磁盘路径
                ci.setPath(ObjectUtil.isNotEmpty(detail.getSrcPathAll())?detail.getSrcPathAll():null);
                toCiResCloud(request, ci);
                data.add(ci);
//                associations.add(AssociationUtils.toAssociation(instanceRes, ci));

                //关联集群
                if (ObjUtil.isNotEmpty(res.getClusterId())) {
                    builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_CLUSTER, res.getClusterId().toString());}
                //关联HOST
                if (ObjUtil.isNotEmpty(res.getHostId())) {
                    cmdbHostRes.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbHostRes.class.getSimpleName(), res.getHostId().toString()));
                    Association host = AssociationUtils.toAssociation(cmdbHostRes, CmdbDiskRes.class, ci.getRes_id());
                    associations.add(host);
                }

                //关联云主机
                if (ObjUtil.isNotEmpty(res.getId())) {
                    instanceRes.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbInstanceRes.class.getSimpleName(), res.getId().toString()));
//                    Association vm = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(),CmdbInstanceRes.class.getSimpleName(), res.getId().toString()));
                    Association vm = AssociationUtils.toAssociation(instanceRes, CmdbDiskRes.class, ci.getRes_id());
                    associations.add(vm);
                }
//                //关联资源池
//                builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, String.valueOf(instanceId));
            }
        }
//        }
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class,List> convertHostOverviewInfo(BaseCloudRequest request,DescribeHostOverviewInfoResponse response, String instanceId,Map<String, ?> instanceMap){
        Map<Class,List> result = new HashMap<>();
        if(response == null || ObjectUtil.isEmpty(response.getData())){
            result.put(CmdbHostRes.class, null);
            result.put(TmdbResourceSet.class, null);
        }
        List<CmdbHostRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CmdbHostRes ci = new CmdbHostRes();
        Object info = instanceMap.get(instanceId);
        ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
        toCiResCloud(request, ci);
        ci.setRes_id(host.getRes_id());
        ci.setOpen_id(host.getOpen_id());
        ci.setOpen_name(host.getOpen_name());
        ci.setAsset_num(ObjectUtil.isNotEmpty(host.getAsset_num())?host.getAsset_num():null);
        ci.setSn(ObjectUtil.isNotEmpty(host.getSn())?host.getSn():null);
        ci.setManufacturer(ObjectUtil.isNotEmpty(host.getManufacturer())?host.getManufacturer():null);
        ci.setWarranty_period(ObjectUtil.isNotEmpty(host.getWarranty_period())?host.getWarranty_period():null);
        ci.setService_code(ObjectUtil.isNotEmpty(host.getService_code())?host.getService_code():null);
        ci.setStatus(ObjectUtil.isNotEmpty(host.getStatus())?host.getStatus():null);
        ci.setIp(ObjectUtil.isNotEmpty(host.getIp())?host.getIp():null);
        ci.setAlarm_status(ObjectUtil.isNotEmpty(host.getAlarm_status())?host.getAlarm_status():null);
        ci.setCpu_size(ObjectUtil.isNotEmpty(host.getCpu_size())?host.getCpu_size():null);
        ci.setMem_size(ObjectUtil.isNotEmpty(host.getMem_size())?host.getMem_size():null);
        ci.setOn_line(ObjectUtil.isNotEmpty(host.getOn_line())?host.getOn_line():null);
        ci.setMaintain_mode(ObjectUtil.isNotEmpty(host.getMaintain_mode())?host.getMaintain_mode():null);
        ci.setOpen_status(ObjectUtil.isNotEmpty(host.getOpen_status())?host.getOpen_status():null);
        for (DescribeHostOverviewInfoResponse.DescribeHostOverviewInfoData res : response.getData()) {
             if("主机本地存储".equals(res.getName())){
                String freeStorage = ObjectUtil.isNotEmpty(res.getFreeStorage()) ? res.getFreeStorage() : "";
                 String storage = ObjectUtil.isNotEmpty(res.getData()) ? res.getData() : "";
                if(freeStorage.contains("GB")){
                    String gb = freeStorage.substring(0, freeStorage.indexOf("GB"));
                    String total = storage.substring(0, storage.indexOf("GB"));
                    ci.setTotal_size(Float.valueOf(total));
                    ci.setUsed_size(Float.valueOf(gb));
                }else if(freeStorage.contains("TB")){
                    String tb = freeStorage.substring(0, freeStorage.indexOf("TB"));
                    String total = storage.substring(0, storage.indexOf("TB"));
                    ci.setTotal_size(Float.valueOf(BigDecimal.valueOf(Float.valueOf(total)).multiply(BigDecimal.valueOf(1024)).floatValue()));
                    ci.setUsed_size(Float.valueOf(BigDecimal.valueOf(Float.valueOf(tb)).multiply(BigDecimal.valueOf(1024)).floatValue()));;
                }else if(freeStorage.contains("MB")){
                    String mb = freeStorage.substring(0, freeStorage.indexOf("MB"));
                    String total = storage.substring(0, storage.indexOf("MB"));
                    ci.setTotal_size(Float.valueOf(BigDecimal.valueOf(Float.valueOf(total)).divide(BigDecimal.valueOf(1024)).floatValue()));
                    ci.setUsed_size(Float.valueOf(BigDecimal.valueOf(Float.valueOf(mb)).divide(BigDecimal.valueOf(1024)).floatValue()));
                }
            }else if("主机型号".equals(res.getName())){
                ci.setModel(res.getData());
            }
        }
        data.add(ci);
        result.put(CmdbHostRes.class, data);
        result.put(Association.class, associations);
        //目前暂无关联的资源 暂传null
        result.put(TmdbResourceSet.class, null);
        return result;
    }

    public static List<PerfInfoBean> convertPerf(DescribeTrendResponse response, String instanceId,String type,Map<String, ?> instanceMap){
        if (ObjectUtil.isEmpty(response)||ObjectUtil.isEmpty(response.getData())) {
            return null;
        }
        List<PerfInfoBean> data = new ArrayList<>();
        response.getData().stream().forEach(e->{
            if(ObjectUtil.isNotEmpty(e)){
                PerfInfoBean perf = new PerfInfoBean();
                Object info = instanceMap.get(instanceId);
//                DateTime dateTime = DateUtil.parse(String.valueOf(datapoint.get(0)), DatePattern.UTC_SIMPLE_PATTERN);
                String createTime = DateUtil.format(new Date(e.getTime()), DatePattern.NORM_DATETIME_PATTERN);
                DateTime dateTime = DateUtil.parse(String.valueOf(e.getTime()), DatePattern.NORM_DATETIME_PATTERN);
                String id = instanceId + "_" + dateTime.getTime();
                if (info instanceof ResInstanceDiskApiModel) {
                    ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
                    perf.setResId(ObjectUtil.isNotEmpty(vminfo.getRes_id())?vminfo.getRes_id():null);
                    perf.setOpenId(ObjectUtil.isNotEmpty(vminfo.getOpen_id())?vminfo.getOpen_id():null);
                    perf.setOpenName(ObjectUtil.isNotEmpty(vminfo.getOpen_name())?vminfo.getOpen_name():null);
                    perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
                    perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
                    if (CollUtil.isNotEmpty(vminfo.getDisks())) {
                        Double sum = vminfo.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                        perf.setDiskSize(sum);
                    }
                    perf.setAccountId(vminfo.getAccount_id());
                    perf.setCloudType(vminfo.getCloud_type());
                    perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                } else if (info instanceof ResHostStoragePoolApiModel) {
                    ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
                    perf.setResId(host.getRes_id());
                    perf.setOpenId(host.getOpen_id());
                    perf.setOpenName(host.getOpen_name());
                    perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
                    perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
                    perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                    perf.setAccountId(host.getAccount_id());
                    perf.setCloudType(host.getCloud_type());
                }
                if("cpu".equals(type)){
                    perf.setCpuUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                }else if("memory".equals(type)){
                    perf.setMemUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                }else if("disk".equals(type)){
                    perf.setDiskUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                }else if("network".equals(type)){
                    perf.setNetIo(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                }

                perf.setCreateTime(createTime);
                perf.setId(id);

                data.add(perf);
            }
        });
        return data;
    }


    public static List<PerfInfoBean> convertNetWorkPerf( DescribeNetworkTrendResponse response, String instanceId,String type,Map<String, ?> instanceMap){
        if (ObjectUtil.isEmpty(response)||ObjectUtil.isEmpty(response.getData())) {
            return null;
        }
        List<PerfInfoBean> data = new ArrayList<>();
        for (DescribeNetworkTrendResponse.DescribeNetWorkTrendData res : response.getData()) {
            List<DescribeNetworkTrendResponse.DescribeNetWorkTrendList> list = res.getList();
            if(ObjectUtil.isEmpty(list)){
                return null;
            }
            list.stream().forEach(e->{
                if(ObjectUtil.isNotEmpty(e)){
                    PerfInfoBean perf = new PerfInfoBean();
                    Object info = instanceMap.get(instanceId);
                    String createTime = DateUtil.format(new Date(e.getTime()), DatePattern.NORM_DATETIME_PATTERN);
                    DateTime dateTime = DateUtil.parse(String.valueOf(e.getTime()), DatePattern.NORM_DATETIME_PATTERN);
                    String id = instanceId + "_" + dateTime.getTime();
                    if (info instanceof ResInstanceDiskApiModel) {
                        ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
                        perf.setResId(ObjectUtil.isNotEmpty(vminfo.getRes_id())?vminfo.getRes_id():null);
                        perf.setOpenId(ObjectUtil.isNotEmpty(vminfo.getOpen_id())?vminfo.getOpen_id():null);
                        perf.setOpenName(ObjectUtil.isNotEmpty(vminfo.getOpen_name())?vminfo.getOpen_name():null);
                        perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
                        perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
                        if (CollUtil.isNotEmpty(vminfo.getDisks())) {
                            Double sum = vminfo.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                            perf.setDiskSize(sum);
                        }
                        perf.setAccountId(vminfo.getAccount_id());
                        perf.setCloudType(vminfo.getCloud_type());
                        perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                    } else if (info instanceof ResHostStoragePoolApiModel) {
                        ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
                        perf.setResId(host.getRes_id());
                        perf.setOpenId(host.getOpen_id());
                        perf.setOpenName(host.getOpen_name());
                        perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
                        perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
                        perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                        perf.setAccountId(host.getAccount_id());
                        perf.setCloudType(host.getCloud_type());
                    }
                    if("cpu".equals(type)){
                        perf.setCpuUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                    }else if("memory".equals(type)){
                        perf.setMemUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                    }else if("disk".equals(type)){
                        perf.setDiskUsage(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                    }else if("network".equals(type)){
                        perf.setNetIo(ObjectUtil.isNotEmpty(e.getRate())?e.getRate():0);
                    }
                    perf.setCreateTime(createTime);
                    perf.setId(id);
                    data.add(perf);
                }
            });

        }
        return data;
    }
    public static Map<String, PerfInfoBean> convertEcsPerf(Map<String, ?> instanceMap, List<JSONObject> response) {
        if (CollUtil.isEmpty(response)) {
            return null;
        }
//        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (JSONObject metric : response) {

            String instanceId = metric.getString("instanceId");//资源ID
            String metricName = metric.getString("metricName");//指标名称
            Double average = metric.getDouble("rate");//指标平均值
            Long timestamp = metric.getLong("time");//时间戳

//            long timestamp = System.currentTimeMillis();
            String createTime = DateUtil.format(new Date(timestamp), DatePattern.NORM_DATETIME_PATTERN);
//            DateTime dateTime = DateUtil.parse(String.valueOf(timestamp), DatePattern.NORM_DATETIME_PATTERN);
//            String id = instanceId + "_" + dateTime.getTime();
            Object o = instanceMap.get(instanceId);
            JSONObject jsonObject = (JSONObject)JSON.toJSON(o);
            String resId=jsonObject.getString("res_id");
            String id = instanceId + "_" + resId+ "_" + timestamp;

            PerfInfoBean perf = perfMap.get(id);
            if (perf == null) {
                //生成指标对象
                Object info = instanceMap.get(instanceId);
                perf = new PerfInfoBean();//指标对应得资源CI信息
                if (info instanceof ResInstanceDiskApiModel) {
                    ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
                    perf.setResId(ObjectUtil.isNotEmpty(vminfo.getRes_id())?vminfo.getRes_id():null);
                    perf.setOpenId(ObjectUtil.isNotEmpty(vminfo.getOpen_id())?vminfo.getOpen_id():null);
                    perf.setOpenName(ObjectUtil.isNotEmpty(vminfo.getOpen_name())?vminfo.getOpen_name():null);
                    perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
                    perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
                    if (CollUtil.isNotEmpty(vminfo.getDisks())) {
                        Double sum = vminfo.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                        perf.setDiskSize(sum);
                    }
                    perf.setAccountId(vminfo.getAccount_id());
                    perf.setCloudType(vminfo.getCloud_type());
                    perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                    perf.setCreateTime(createTime);
                    perf.setId(id);
                }
                else if (info instanceof ResHostStoragePoolApiModel) {
                    ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
                    perf.setResId(host.getRes_id());
                    perf.setOpenId(host.getOpen_id());
                    perf.setOpenName(host.getOpen_name());
                    perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
                    perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
                    perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                    perf.setAccountId(host.getAccount_id());
                    perf.setCloudType(host.getCloud_type());
                    perf.setCreateTime(createTime);
                    perf.setId(id);
                }
            }
            BiConsumer<PerfInfoBean, Double> setValue = Constant.perfMapping.get(metricName);
            setValue.accept(perf, average);//设置监控指标值
            perfMap.put(perf.getId(), perf);
        }
//        request.getBody().remove("instanceMap");


        return perfMap;
    }
//
//
    public static JSONArray fetchResourceToJsonArray(BaseCloudRequest request, String url, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.get(url, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (ObjectUtil.isEmpty(key)) {
            array = JSONArray.parseArray(responseJson);
        } else if (HttpClientUtil.isJsonarray(JSONObject.parseObject(responseJson).get(key).toString())) {
            array = JSONObject.parseObject(responseJson).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(responseJson).getJSONObject(key));
        }
        return array;
    }

    public static String fetchResourceToJsonString(BaseCloudRequest request, String url, String key) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOiJUNnB5VjJudiIsInJvbGUiOiJcdTAwMDBcdTAwMDBcdTAwMDBcdTAwMDEiLCJpc3MiOiJIM0MgQXV0aCIsInVzZXJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiJ9.L4e9uS6Y2I2sVIurM-Uoet3Zbtjx9yzBX8B9B_4FDxY";
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.get(url, config);
        return responseJson;
    }

    public static String operateResourceToJsonString(BaseCloudRequest request, String url, String key) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String json = "";
        if(ObjectUtil.isNotEmpty(request.getBody().get("info"))){
            json = JSONObject.toJSONString(request.getBody().get("info"));
        }
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.put(url, config,json);
        return responseJson;
    }

    public static String operateDeleteResourceToJsonString(BaseCloudRequest request, String url, String key) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.delete(url, config);
        return responseJson;
    }

    public static String operateAddResourceToJsonString(BaseCloudRequest request, String url, String key) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOiJYVDB3cWVzeSIsInJvbGUiOiJcdTAwMDBcdTAwMDBcdTAwMDBcdTAwMDEiLCJpc3MiOiJIM0MgQXV0aCIsInVzZXJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiJ9.iTWz9guxk_7hrMYPNGW2M3vN_QdQcXRKF2QzYN0T6mc";
        String instanceInfo = JSONObject.toJSONString(request.getBody().get("info"));
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.post(url,instanceInfo, config);
        return responseJson;
    }

    public static String operateAddResourceToJsonString(BaseCloudRequest request, String url, String key,String respone) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOiJYVDB3cWVzeSIsInJvbGUiOiJcdTAwMDBcdTAwMDBcdTAwMDBcdTAwMDEiLCJpc3MiOiJIM0MgQXV0aCIsInVzZXJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiJ9.iTWz9guxk_7hrMYPNGW2M3vN_QdQcXRKF2QzYN0T6mc";
        String instanceInfo = JSONObject.toJSONString(request.getBody().get("key"));
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.post(url,instanceInfo, config);
        return responseJson;
    }

    public static BasePageSortSearchRequest toBasePageSortSearchRequest(BaseCloudRequest request) {
        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
        CloudAccessBean access = request.getBody().getAccess();
        searchRequest.setCurrent(1);
        searchRequest.setSize(50);
        searchRequest.setSortField(BaseConstant.RES_ID);
        searchRequest.setSort(0);
        List<BaseSearchApiModel> searchList = new ArrayList<>();
        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
        searchApiModel.setValue(access.getCmpId());
        searchApiModel.setSearchClassiy("0");
        searchList.add(searchApiModel);
        BaseSearchApiModel searchApiModel2 = new BaseSearchApiModel();
        searchApiModel2.setKey("status");
        searchApiModel2.setValue("running");
        searchApiModel2.setSearchClassiy("0");
        searchList.add(searchApiModel2);
        searchRequest.setSearchList(searchList);
        return searchRequest;
    }

    public static String operateResourceToPostByObject(BaseCloudRequest request, String url, Object object) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOiJNZldHenF6VSIsInJvbGUiOiJcdTAwMDBcdTAwMDBcdTAwMDBcdTAwMDEiLCJpc3MiOiJIM0MgQXV0aCIsInVzZXJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiJ9.esNABjNrKWOnJdoSZsxX8PUlyDbH-nrrnloVcQFsszY";
        String json = JSONObject.toJSONString(object);
        log.info("获取POST入参为：{}",json);
        config.addHeader("Cookie", cookieHeaderValue);
        String responseJson = HttpClientUtil.post(url,json, config);
        return responseJson;
    }

    public static String operateResourceToPutByObject(BaseCloudRequest request, String url, Object object) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOiJNZldHenF6VSIsInJvbGUiOiJcdTAwMDBcdTAwMDBcdTAwMDBcdTAwMDEiLCJpc3MiOiJIM0MgQXV0aCIsInVzZXJpZCI6MiwidXNlcm5hbWUiOiJhZG1pbiJ9.esNABjNrKWOnJdoSZsxX8PUlyDbH-nrrnloVcQFsszY";
        String json = JSONObject.toJSONString(object);
        config.addHeader("Cookie", cookieHeaderValue);
        log.info("获取PUT入参为：{}",json);

//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.put(url, config,json);
        return responseJson;
    }

    public static String operateResourceToDeleteByObject(BaseCloudRequest request, String url, Object object) {
        HttpClientConfig config = new HttpClientConfig();
        UisToken uisToken = request.getBody().getJSONObject("authToken").toJavaObject(UisToken.class);
        String cookieHeaderValue = TokenEnum.AUTH_TOKEN.getValue()+"="+uisToken.getAuthToken();
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
//        String instanceInfo = JSONObject.toJSONString(object);
        config.addHeader("Cookie", cookieHeaderValue);
//        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.delete(url, config);
        return responseJson;
    }
//
//    public static JSONArray covertJsonArray(String json, String key) {
//        JSONArray array = new JSONArray();
//        if (ObjectUtil.isEmpty(json) || "null".equals(json))
//            return array;
//        if (HttpClientUtil.isJsonarray(JSONObject.parseObject(json).get(key).toString())) {
//            array = JSONObject.parseObject(json).getJSONArray(key);
//        } else {
//            array.add(JSONObject.parseObject(json).getJSONObject(key));
//        }
//        return array;
//    }
//
//    public static CmdbKeypairRes toNxcKeypair(CloudAccessBean bean, CloudosInstance info) {
//
//        if (ObjectUtil.isNotNull(info.getKey_name())) {
//            CmdbKeypairRes res = new CmdbKeypairRes();
//            /**
//             * 封装资源唯一ID
//             */
//            res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_KEYPAIR_RES.value(), info.getKey_name()));
//            res.setOpen_id(info.getKey_name());
//            res.setOpen_name(info.getKey_name());
//            res.setCloud_type(bean.getCloudType());
//            res.setAccount_id(bean.getCmpId());
//            return res;
//        } else {
//            return null;
//        }
//    }
//
//    public static CmdbKeypairRes toNxcKeypair(CloudAccessBean bean, CloudosKeypair info) {
//        CmdbKeypairRes res = new CmdbKeypairRes();
//        /**
//         * 封装资源唯一ID
//         */
//        res.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_KEYPAIR_RES.value(), info.getKeypair().getName()));
//        res.setOpen_id(info.getKeypair().getName());
//        res.setOpen_name(info.getKeypair().getName());
//        res.setCloud_type(bean.getCloudType());
//        res.setAccount_id(bean.getCmpId());
//        res.setDesc(info.getKeypair().getName());
//        res.setPublic_key(info.getKeypair().getPublic_key());
//        res.setFingerprint(info.getKeypair().getFingerprint());
//        return res;
//    }
//
//    public static CmdbSecuritygroupRes toNxcSecurityGroup(CloudAccessBean bean, CloudosSecurityGroup info) {
//        CmdbSecuritygroupRes securitygroup = new CmdbSecuritygroupRes();
//        securitygroup.setAccount_id(bean.getCmpId());
//        securitygroup.setCloud_type(bean.getCloudType());
//        securitygroup.setDesc(info.getDescription());
//        securitygroup.setOpen_id(info.getId());
//        securitygroup.setOpen_name(info.getName());
//        securitygroup.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
//        return securitygroup;
//    }
//
//    public static void toNxcSecurityGroupRule(List<CmdbSecuritygroupRule> rules, CloudAccessBean bean, CloudosSecurityGroup info) {
//        if (ObjectUtil.isNotEmpty(info.getSecurity_group_rules())) {
//            info.getSecurity_group_rules().forEach(r -> {
//                CmdbSecuritygroupRule rule = new CmdbSecuritygroupRule();
//                rule.setAccount_id(bean.getCmpId());
//                rule.setCloud_type(bean.getCloudType());
//                rule.setOpen_id(r.getId());
//                rule.setDirection(r.getDirection());
//                rule.setDesc(r.getDescription());
//                rule.setCreate_time(DateUtils.utcToTimestamp(r.getCreated_at()));
//                rule.setUpdate_time(DateUtils.utcToTimestamp(r.getUpdated_at()));
//                rule.setSource_cidr(r.getRemote_ip_prefix());
//                rule.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), r.getId()));
//                rules.add(rule);
//            });
//        }
//    }

}
