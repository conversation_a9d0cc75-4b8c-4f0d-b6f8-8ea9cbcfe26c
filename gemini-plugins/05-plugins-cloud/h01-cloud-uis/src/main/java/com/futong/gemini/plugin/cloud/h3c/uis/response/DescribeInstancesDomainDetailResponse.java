package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeInstancesDomainDetailResponse extends BaseResponseBodyData{

    @JsonProperty("data")
    private DescribeDomainDetailData data;



    @Data
    public static class DescribeDomainDetailData {
        //虚拟机id
        private Integer id;
        //虚拟机名称
        private String domainName;
        //虚拟机显示名称（展示用，可修改）
        private String title;
        //主机id
        private Integer hostId;
        //集群id
        private Integer clusterId;
        //当前修改的页签
        private String currentTabId;
        //来源
        private String source;
        //虚拟机图标
        private String icon;
        //虚拟机快速配置
        private String quickConfig;
        //主机状态（数字）：维护模式时（3-维护模式，4-本地存储故障维护模式）；非维护模式时（2-主机非异常且主机在HA集群但主机未启用HA）；其他（0-异常，1-正常）
        private Integer hostStatus;
        //虚拟机存储设备
        private List<Disk> diskList;
    }

    @Data
    public static class Disk{
        //用于页面显示
        private String dispName;
        //索引，用于页面
        private Integer tabIndex;
        //存储设备详细
        private StorageDetail detail;
    }

    @Data
    public static class StorageDetail{
        //已使用大小 MB
        private Integer allocation;
        //缓存方式（directsync-直接独写、
        //writethrough-一级物理缓存、
        //writeback-二级虚拟缓存、none-一
        //级虚拟缓存）
        private String cacheModel;
        //簇大小（单位B） 64K，128K，
        //256K，512K，1M，2M
        private String clustersize;
        //设备（disk, cdrom, floppy）
        private String dev;
        //设备总线
        private String deviceBus;
        //设备名称
        private String deviceName;
        //设备对象
        private String devObj;
        //是否是磁盘
        private Boolean disk;
        //磁盘模式(subordinate:从
        //属;independent-persistent:独立-持
        //久)
        private String diskMode;
        //是否存在磁盘有加密磁盘
        private Boolean diskSecret;
        //已分配空间，带单位，单位范围：
        //MB、GB、TB
        private String distribute;
        //是否转换
        private Boolean enableConverFmt;
        //是否可修改磁盘模式
        private Boolean enableEditDiskMode;
        //是否可修改
        private Boolean enableModify;
        //是否rbd存储
        private Boolean enableRbd;
        //密码（加密磁盘）
        private String encryptPw;
        //存储格式（raw-高速，qcow2-智
        //能）
        private String format;
        //cbt增量备份
        private Boolean hasCbtIncBack;
        //是否允许热插拔 （true-是，false-
        //否）
        private Boolean hotPluggable;
        //最大值：MB
        private Double maxValue;
        //最小值：MB
        private Double minValue;
        //预分配（0-延迟置零，1-置零，2-精
        //简）
        private Integer mode;
        //限制I/O速率（读）KB/S
        private Long readBytesSec;
        //限制IOPS(读) 读/S
        private Long readIopsSec;
        //是否只读
        private Boolean readonly;
        //serial号
        private String serial;
        //是否共享
        private Boolean share;
        //存储显示大小
        private Double showSize;
        //存储显示大小的单位,单位范围：
        //MB、GB、TB
        private String showUnit;
        //存储大小（MB）
        private Double size;
        //文件路径
        private String srcPath;
        //文件全路径
        private String srcPathAll;
        //磁盘类型（IDE硬盘，SCSI硬盘，
        //USB硬盘，Virtio硬盘，Virtio SCSI硬
        //盘，IDE光驱，软驱）
        private String type;
        //磁盘利用率
        private Double usage;
        //限制I/O速率（写）KB/S
        private Long writeBytesSec;
        //限制IOPS(写) 写/S
        private Long writeIopsSec;
    }

}
