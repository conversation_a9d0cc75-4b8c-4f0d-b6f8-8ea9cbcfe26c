package com.futong.gemini.plugin.cloud.h3c.uis.util;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbFlavor;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbImageRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;

import java.util.*;
import java.util.stream.Collectors;
public class CiResCloudUtils {
    public static <T> T toCiResCloud(CloudAccessBean bean, String id, T classBean) {
        if (classBean instanceof CmdbHostRes) {
            CmdbHostRes host = (CmdbHostRes) classBean;
            host.setCloud_type(bean.getCloudType());
            host.setAccount_id(bean.getCmpId());
            host.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), id));
            return (T) host;
        } else if (classBean instanceof CmdbInstanceRes) {
            CmdbInstanceRes instance = (CmdbInstanceRes) classBean;
            instance.setCloud_type(bean.getCloudType());
            instance.setAccount_id(bean.getCmpId());
            instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), id));
            return (T) instance;
        } else if (classBean instanceof CmdbFlavor) {
            CmdbFlavor flavor = (CmdbFlavor) classBean;
            flavor.setCloud_type(bean.getCloudType());
            flavor.setAccount_id(bean.getCmpId());
            flavor.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_FLAVOR.value(), id));
            return (T) flavor;
        } else if (classBean instanceof CmdbImageRes) {
            CmdbImageRes image = (CmdbImageRes) classBean;
            image.setCloud_type(bean.getCloudType());
            image.setAccount_id(bean.getCmpId());
            image.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_IMAGE_RES.value(), id));
            return (T) image;
        }
        return classBean;
    }

    public static Map<Class, List> mergeMaps(Map<Class, List>... maps) {
        return Arrays.stream(maps)
                .filter(Objects::nonNull)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (list1, list2) -> {
                            List merged = new ArrayList<>(list1);
                            merged.addAll(list2);
                            return merged;
                        }
                ));
    }

    public static <T> Map<Class<T>, List<T>> mergeTypedMaps(Map<Class<T>, List<T>>... maps) {
        Map<Class<T>, List<T>> result = new HashMap<>();

        for (Map<Class<T>, List<T>> map : maps) {
            if (map == null) continue;

            for (Map.Entry<Class<T>, List<T>> entry : map.entrySet()) {
                Class<T> key = entry.getKey();
                List<T> value = entry.getValue();

                result.computeIfAbsent(key, k -> new ArrayList<>())
                        .addAll(value);
            }
        }

        return result;
    }


}
