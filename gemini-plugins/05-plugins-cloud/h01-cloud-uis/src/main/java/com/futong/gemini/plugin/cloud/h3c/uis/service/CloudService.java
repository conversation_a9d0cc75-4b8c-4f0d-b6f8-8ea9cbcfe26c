package com.futong.gemini.plugin.cloud.h3c.uis.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

import java.util.Date;

/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2025年04月15日
 */
public class CloudService {
    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", "0");
        }
        if (!request.getBody().getCloud().containsKey("limit")) {
            request.getBody().getCloud().put("limit", "100");
        }
        return true;
    }

    public static boolean defaultPage15(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", "0");
        }
        if (!request.getBody().getCloud().containsKey("limit")) {
            request.getBody().getCloud().put("limit", "15");
        }
        return true;
    }

    public static boolean toCiInstanceId(BaseCloudRequest request) {
        toCIOpenId(request, "server_id");
        return true;
    }

    public static boolean toCIOpenId(BaseCloudRequest request, String key) {
        if (!request.getBody().getCi().isEmpty()) {
            request.getBody().put(key, request.getBody().getCi().get("openId"));
            return true;
        }
        if (CollUtil.isEmpty(request.getBody().getCis())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的CI信息为空!");
        }
//        List<String> ids = request.getBody().getCis().stream().map(BaseCloudRequestBodyCI::getOpenId).collect(Collectors.toList());
        String instanceId = request.getBody().getCis().get(0).getString("openId");
        request.getBody().put(key, instanceId);
        return true;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("eventTimeFrom")) {
            request.getBody().getCloud().put("eventTimeFrom", DateUtil.offsetDay(new Date(), -1).toString("yyyy-MM-dd HH:mm:ss"));//一天前
        }
        if (!request.getBody().getCloud().containsKey("eventTimeTo")) {
            request.getBody().getCloud().put("eventTimeTo", DateUtil.date().toString("yyyy-MM-dd HH:mm:ss"));//当前时间
        }
        return true;
    }
}
