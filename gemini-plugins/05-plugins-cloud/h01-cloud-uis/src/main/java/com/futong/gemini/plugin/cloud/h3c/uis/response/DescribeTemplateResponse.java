package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.sql.Date;
import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeTemplateResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeTemplateData> data;


    @Data
    public static class DescribeTemplateData {
        /**
         * 集群id
         */
        private String clusterId;
        /**
         * 虚拟机模版cpu个数
         */
        private Integer cpu;
        /**
         * 虚拟机模板创建日志(时间戳，ms)
         */
        private Long createDate;
        /**
         * 虚拟机模版描述
         */
        private String description;
        /**
         * 虚拟机模板名称
         */
        private String domainName;
        /**
         * 主机id
         */
        private Integer hostId;
        /**
         * 虚拟机模版id
         */
        private Integer id;
        /**
         * 虚拟机模版内存：MB
         */
        private Integer memory;
        /**
         * 虚拟机模版内存值
         */
        private Double memoryInit;
        /**
         * 虚拟机模版内存单位
         */
        private String memoryUnit;
        /**
         * 虚拟机安装的操作系统版本
         */
        private String osVersion;
        /**
         * 虚拟机模版存储大小
         */
        private Integer storage;
        /**
         * 虚拟机安装的操作系统，取值：0：windows;1:linux；2：BSD
         */
        private Integer system;
        /**
         * 虚拟机模版存放目录
         */
        private String templetStoragePath;
        /**
         * 虚拟机模版显示名称(展示用，可修改)
         */
        private String title;
        /**
         * 虚拟机模版外部云资源类型(通常=2，即cvm)
         */
        private String type;

    }

}
