package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OperateInstanceStatusResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private Long data;

    private String taskId;


}
