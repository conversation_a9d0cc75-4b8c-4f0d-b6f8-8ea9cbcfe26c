package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeVirtualSwitchResponse extends BaseResponseBodyData{
//    @JsonProperty("entity")
//    private DescribeVirtualSwitchEntity entity;
@JsonProperty("data")
private List<DescribeVirtualSwitchData> data;


//    @Data
//    public static class DescribeVirtualSwitchEntity{
//        @JsonProperty("data")
//        private List<DescribeVirtualSwitchData> data;
//    }


    @Data
    public static class DescribeVirtualSwitchData {
        /**
         * 虚拟交换机id
         */
        private Long id;
//        /**
//         * 主机id
//         */
//        private Long hostId;
//        /**
//         * 主机名称
//         */
//        private String hostName;
        /**
         * 虚拟交换机名称
         */
        private String name;
        /**
         * 虚拟交换机描述
         */
        private String description;
        /**
         * 虚拟交换机端口数量
         */
        private Integer portNum;
        /**
         * 集群id
         */
        private Integer clusterId;
        /**
         * 转发模式(0：veb;1:vepa;2：多通道)
         */
        private Integer mode;
        /**
         * 虚拟交换机VLANID
         */
        private Integer vlanId;
        /**
         * 是否开启组播
         */
        private Boolean multicast;
        /**
         * 是否启用DPDK(启用：true，未启用：false
         */
        private Boolean isDpdk;
        /**
         * 最大传输单元(Mbps)
         */
        private Integer mtu;
//        /**
//         * 物理接口
//         */
//        private String pnic;
//        /**
//         * ip地址
//         */
//        private String address;
//        /**
//         * 网络掩码
//         */
//        private String netmask;
//        /**
//         * 网关
//         */
//        private String gateway;
//        /**
//         * 是否启用LACP(启用：true，未启用：false)
//         */
//        private Boolean enableLacp;
//        /**
//         * 负载分担模式(balance-tcp:高级负载分担,balance-slb：基本负载分担，active-backup：主备负载分担)
//         */
//        private String bondMode;
//        /**
//         * 是否是管理网卡(0:否，1：是)
//         */
//        private Integer isManage;
//        /**
//         * 状态(0：不活动；1：活动)
//         */
//        private Integer status;
//        /**
//         * 虚拟交换机的增加方式，0：主机下增加，1：集群下增加
//         */
//        private Integer flag;
//        /**
//         * HA状态(0：正常，1：故障)
//         */
//        private Integer haStatus;
//        /**
//         * 是否启用DPDK(启用：true，未启用:false)
//         */
//        private Boolean enableDpdk;
        /**
         * 虚拟机网络类型用二进制位表示，从第0位到第5位分别代表：管理，业务，存储，备份，迁移，其他
         */
        private Integer networkType;
//        /**
//         * 判断虚拟交换机是否被使用
//         */
//        private Boolean runingVmUserVSwitch;
//        /**
//         * 判断虚拟交换机是否被vxlan域使用
//         */
//        private Boolean vxlanScopeUserVSwitch;


    }

}
