package com.futong.gemini.plugin.cloud.h3c.uis.service;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.convert.Converts;
import com.futong.gemini.plugin.cloud.h3c.uis.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
@Slf4j
public class BlockDeviceService {

    /**
     * 查询主机组列表
     * @param
     * @return
     */
    public static BaseResponse queryHostGroupList(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            List<Object> list = new ArrayList<>();
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getHostGroupUrl(0L), new String[]{}),
                    "data");
            if(ObjectUtil.isNotEmpty(json)) {
                JSONObject obj = JSON.parseObject(json);
                if (null != obj){
                    if (obj.get("data") instanceof Collection){
                        list = (List<Object>) obj.get("data");
                    }else {
                        list.add(obj.get("data"));
                    }
                }
            }
            return new BaseDataResponse<>(list);
        }catch (Exception e){
            log.error("查询主机组列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询主机组列表失败");
        }
    }


}
