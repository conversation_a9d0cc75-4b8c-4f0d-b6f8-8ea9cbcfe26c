

package com.futong.gemini.plugin.cloud.h3c.uis.service;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.h3c.uis.vo.UisToken;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
@Slf4j
public class AccountService {

    public static final AccountService bean = new AccountService();

    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(JSONObject arguments) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    //账号认证
    public static BaseResponse authCloudAccount(BaseCloudRequest request) {
        try {
            log.info("uis账号验证信息为：{}",request.toJSONString());
            CloudAccessBean bean = request.getBody().getAuth().toJavaObject(CloudAccessBean.class);
            UisToken uisToken = HttpClientUtil.getUisToken(bean);
//            UisToken uisToken = HttpClientUtil.getUisToken(BaseClient.auths.get());
            if(ObjectUtil.isNull(uisToken) || StrUtil.isEmpty(uisToken.getAcToken())){
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, "云账号信息认证失败");
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
    }

    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        //替换参数云账号ID
        String text = StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId());
        JSONObject result = JSON.parseObject(text);
        return new BaseDataResponse<>(result);
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        List<JSONObject> dispatchers = listAllDispatcher(result);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            }
        });
        return dispatchers;
    }

}
