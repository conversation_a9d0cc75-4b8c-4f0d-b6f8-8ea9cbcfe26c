package com.futong.gemini.plugin.cloud.h3c.uis.service;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.convert.Converts;
import com.futong.gemini.plugin.cloud.h3c.uis.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
@Slf4j
public class ComputeInstanceService {

    public static final ComputeInstanceService bean = new ComputeInstanceService();


    /**
     * 查询网络策略模板列表
     * @param
     * @return
     */
    public static BaseResponse queryNetworkPortProfileList(BaseCloudRequest request){
        try {
            CloudAccessBean cloudAccessBean = request.getBody().getAccess();
            List<Object> list = new ArrayList<>();
            String json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getNetworkPortProfileUrl(), new String[]{}),
                    "data");
            if(ObjectUtil.isNotEmpty(json)) {
                JSONObject obj = JSON.parseObject(json);
                if (null != obj){
                    if (obj.get("data") instanceof Collection){
                        list = (List<Object>) obj.get("data");
                    }else {
                        list.add(obj.get("data"));
                    }
                }
            }
            return new BaseDataResponse<>(list);
        }catch (Exception e){
            log.error("查询网络策略模板列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询网络策略模板列表失败");
        }
    }



    public static boolean toBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        if (resNum == null || resNum == 0) return true;//为0非批量创建云主机,不做处理
        JSONObject cloud = request.getBody().getCloud();
        String instanceName = cloud.getString("instanceName");
        String title = cloud.getString("hostName");
        cloud.put("title", instanceName + "-" + resNum);
//        cloud.put("hostName", hostName + "-" + resNum);
//        cloud.put("amount", 1);//默认数量
        return true;
    }

    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) return;
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
//            if (dataResponse.getData() instanceof RunInstancesResponse) {
//                RunInstancesResponse runInstancesResponse = (RunInstancesResponse) data;
//                if (runInstancesResponse.body == null
//                        || runInstancesResponse.body.instanceIdSets == null
//                        || CollUtil.isEmpty(runInstancesResponse.body.instanceIdSets.instanceIdSet)) {
//                    return;
//                }
//                JSONObject biz = request.getBody().getBiz();
//                biz.put("resId", IdUtils.encryptId(request.getBody().getAccess().getCmpId(), runInstancesResponse.body.instanceIdSets.instanceIdSet.get(0)));
//                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
//            }
        }
    }

}
