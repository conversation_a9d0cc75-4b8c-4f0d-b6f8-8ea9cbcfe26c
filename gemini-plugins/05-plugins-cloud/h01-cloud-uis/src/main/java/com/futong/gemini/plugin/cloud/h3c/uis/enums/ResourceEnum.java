package com.futong.gemini.plugin.cloud.h3c.uis.enums;

public enum ResourceEnum {

    /** uis云主机 */
    SERVER("servers"),

    VM("vm"),

    /** uis规格*/
    FLAVOR("flavors"),

    /** uis镜像*/
    IMAGE("images"),

    /** uis镜像*/
    SNAPSHOT("snapshots"),


    /** uis 集群*/
    CLUSTER("clusters"),

    /** uis 宿主机*/
    HOST("host"),

    /** uis 存储池*/
    STORAGEPOOL("storagePool"),

    /** uis 宿主机关联云主机*/
    HOST_VM("vms"),

    /** uis 云盘*/
    VOLUME("volumes"),

    /** uis vpc*/
    VPC("vpcs"),

    /** uis router*/
    ROUTER("routers"),

    /** uis 子网*/
    SUBNET("subnets"),

    /** uis 网卡*/
    PORT("ports"),

    /** uis 经典网络*/
    NETWORRK("networks"),

    /** uis 安全组*/
    SECURITY_GROUP("security_groups"),

    /** uis ip*/
    IP("ip"),

    PROJECT("project"),
    // ssh密钥
    KEYPAIR("keypairs"),
    AZONE("azone"),
    // 安全组规则
    SECURITY_GROUP_RULES("security_group_rules");

    private String value;

    private ResourceEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
