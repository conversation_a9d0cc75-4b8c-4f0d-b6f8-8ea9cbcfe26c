package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeInstancesResponse {


    @JsonProperty("state")
    private Integer state;
    @JsonProperty("errorCode")
    private Integer errorCode;

    private Integer offset;

    private String successMessage;

    private String failureMessage;

    private Integer totalLength;

    private Boolean success;

    @JsonProperty("data")
    private List<DescribeInstancesData> data;



    @Data
    public static class DescribeInstancesData {
        private String hostName;

        private String castoolsVersion;

        private Integer auto;

        private Integer memory;

        private Integer hostId;

        private Integer cpu;

        private String description;

        private Integer clusterId;

        private String title;

        private String uuid;

        private List<String> domainIps;

        private Integer system;

        private String hpName;

        private Integer autoBooting;

        private Integer enable;

        private String domainName;

        private String displayname;

        private String clusterName;

        private String osBit;

        private String osVersion;

        private Integer haManage;

        private Integer id;

        private String status;


    }

}
