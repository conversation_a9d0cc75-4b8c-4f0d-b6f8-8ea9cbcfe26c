package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import javassist.runtime.Inner;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeVirtualFirewallResponse extends BaseResponseBodyData{
    @JsonProperty("data")
    private List<DescribeVirtualFirewallData> data;


    @Data
    public static class DescribeVirtualFirewallData {
        /**
         * 虚拟防火墙id
         */
        private Long id;
        /**
         * 虚拟防火墙名称
         */
        private String name;
        /**
         * 虚拟防火墙描述
         */
        private String description;
        /**
         * 操作组编码
         */
        private String opGroupCode;
        /**
         * 操作组id
         */
        private Long opGroupId;
        /**
         * 虚拟防火墙类型(0:黑名单；1：白名单；默认为1)
         */
        private Integer ruleAction;
        /**
         * 虚拟防火墙规则
         */
        private securityRules securityRules;
        /**
         * 虚拟防火墙显示名称(展示用，可修改)
         */
        private String title;

    }

    @Data
    public static class securityRules{
        /**
         * 方向，0表示入口；1表示出口；默认为0
         */
        private Integer direction;
        /**
         * 虚拟防火墙规则id
         */
        private Long id;
        /**
         * ip类型：IPv4或者ipv6
         */
        private String ipType;
        /**
         * 结束端口，取值范围1-65535，-1 代表所有端口，0 代表无效端口
         */
        private Integer portEnd;
        /**
         * 起始端口，趋势范围1-65535，-1 代表所有端口，0 代表无效端口，默认-1
         */
        private Integer portStart;
        /**
         * 优先级，默认999(数字越小优先级越高，取值范围0-999)
         */
        private Integer priority;
        /**
         * tcp/ip 规则协议号码，icmp为1，tcp为6，udp为17，所有协议为-1
         */
        private Integer protocol;
        /**
         * 远端ip地址
         */
        private String remoteIp;
        /**
         * 远端子网掩码
         */
        private String remoteMask;
        /**
         * 虚拟防火墙id
         */
        private Long securityGroupId;
        /**
         * 虚拟防火墙规则类型，协议icmp才有值(取值范围1-255)为-1时代表所有icmp规则
         */
        private Integer type;
        /**
         * 虚拟防火墙规则编码，协议icmp才有值(取值范围1-255) 若type为-1时，code必须为-1
         */
        private Integer code;
    }

}
