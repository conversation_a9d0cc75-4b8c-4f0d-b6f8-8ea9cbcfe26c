package com.futong.gemini.plugin.cloud.h3c.uis.sampler;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.convert.Converts;
import com.futong.gemini.plugin.cloud.h3c.uis.response.BaseResponseBodyData;
import com.futong.gemini.plugin.cloud.h3c.uis.response.DescribeVmSummaryResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.response.OperateInstanceStatusResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBodyCI;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
/**
 * @创建者: fanrong
 * @创建时间: 2025/4/23 上午10:57
 * @描述:
 **/
@Slf4j
public class OperateService {



    /**
     * 新增虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateCreateInstance(BaseCloudRequest request){
        try {
            log.info("获取新增虚拟机入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().get("cloud"))){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            JSONObject cloud = request.getBody().getCloud();
            String json = Converts.operateResourceToPostByObject(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getAddVmUrl(), new String[]{}),
                    cloud);
            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            log.info("operateVmCreate.response:{}", JSONObject.toJSONString(response));
            if(response.getSuccess()){
                return BaseResponse.SUCCESS;
            }
            log.error("新增虚拟机失败:{}", JSONObject.toJSONString(response));
            return BaseResponse.ERROR;
        }catch (Exception e){
            log.error("新增虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "新增虚拟机失败");
        }
    }

    /**
     * 新增-克隆虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateDeployInstance(BaseCloudRequest request){
        try {
            log.info("获取克隆虚拟机入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().get("cloud"))){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            JSONObject cloud = request.getBody().getCloud();
            String json = Converts.operateResourceToPostByObject(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeployVmUrl(), new String[]{}),
                    cloud);
            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            log.info("operateVmCreate.response:{}", JSONObject.toJSONString(response));
            if(response.getSuccess()){
                return BaseResponse.SUCCESS;
            }
            log.error("克隆虚拟机失败:{}", JSONObject.toJSONString(response));
            return BaseResponse.ERROR.of(response.getFailureMessage());
        }catch (Exception e){
            log.error("克隆虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "克隆虚拟机失败");
        }
    }

    /**
     * 更新虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateUpdateInstance(BaseCloudRequest request){
        try {
            log.info("获取更新虚拟机入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().get("cloud"))){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            JSONObject cloud = request.getBody().getCloud();
            String json = Converts.operateResourceToPutByObject(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getUpdateVmUrl(), new String[]{}),
                    cloud);
            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            log.info("operateVmStart.response:{}", JSONObject.toJSONString(response));
            if(response.getSuccess()){
                return BaseResponse.SUCCESS;
            }
            log.error("更新虚拟机失败:{}", JSONObject.toJSONString(response));
            return BaseResponse.ERROR;
        }catch (Exception e){
            log.error("更新虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "更新虚拟机失败");
        }
    }


    /**
     * 启动虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateVmStart(BaseCloudRequest request){
        try {
            log.info("获取启动虚拟机入参为：{}",request);
            List<OperateInstanceStatusResponse> responses = new ArrayList<>();
                    CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
           for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
               if (ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                   log.error("启动虚拟机失败:{}", "openId为空!");
                   break;
               }
               final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
               String json = Converts.operateResourceToJsonString(request,
                       URLUtils.bean.makeUrl(bean, URLUtils.bean.getStartVmUrl(instanceId), new String[]{}),
                       "data");
               OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
               if(!response.getSuccess()){
                   log.error("启动虚拟机[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                   break;
               }else {
                   log.info("启动虚拟机[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
               }
               responses.add(response);
           }
            return new BaseDataResponse<>(responses);
        }catch (Exception e){
            log.error("启动虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动虚拟机失败");
        }
    }

    /**
     * 关闭虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateVmShutDown(BaseCloudRequest request){
        try {
            log.info("获取关闭虚拟机入参为：{}",request);
            List<OperateInstanceStatusResponse> responses = new ArrayList<>();
                    CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
                if (ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                    log.error("关闭虚拟机失败:{}", "openId为空!");
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "openId为空!");
//                    break;
                }
                final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
                String json = Converts.operateResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getShutDownVmUrl(instanceId), new String[]{}),
                        "data");
                OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
                if(!response.getSuccess()){
                    log.error("关闭虚拟机[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("关闭虚拟机[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
                }
                responses.add(response);
            }
            return new BaseDataResponse<>(responses);
        }catch (Exception e){
            log.error("关闭虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭虚拟机失败");
        }
    }

    /**
     * 关闭虚拟机电源，关机
     * @param request
     * @return
     */
    public static BaseResponse operateVmStop(BaseCloudRequest request){
        try {
            log.info("获取关闭虚拟机入参为：{}",request);
            List<OperateInstanceStatusResponse> responses = new ArrayList<>();
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
                if (ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                    log.error("关闭虚拟机失败:{}", "openId为空!");
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "openId为空!");
//                    break;
                }
                final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
                String json = Converts.operateResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getStopVmUrl(instanceId), new String[]{}),
                        "data");
                OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
                if(!response.getSuccess()){
                    log.error("关闭虚拟机[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("关闭虚拟机[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
                }
                responses.add(response);
            }
            return new BaseDataResponse<>(responses);
        }catch (Exception e){
            log.error("关闭虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭虚拟机失败");
        }
    }


    /**
     * 重启虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateVmRestart(BaseCloudRequest request){
        try {
            log.info("获取重启虚拟机入参为：{}",request);
            List<OperateInstanceStatusResponse> responses = new ArrayList<>();
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
                if(ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                    log.error("重启虚拟机失败:{}", "openId为空!");
                    break;
                }
                final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
                String json = Converts.operateResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getRestartVmUrl(instanceId), new String[]{}),
                        "data");
                OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
                if(!response.getSuccess()){
                   log.error("重启虚拟机[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                   break;
                }else {
                    log.info("重启虚拟机[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
                }
                responses.add(response);
            }
            return new BaseDataResponse<>(responses);
        }catch (Exception e){
            log.error("重启虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重启虚拟机失败");
        }
    }

    /**
     * 删除虚拟机
     * @param request
     * @return
     */
    public static BaseResponse operateVmDelete(BaseCloudRequest request){
        try {
            log.info("获取删除虚拟机入参为：{}",request);
            List<OperateInstanceStatusResponse> responses = new ArrayList<>();
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
                if(ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                    log.error("删除虚拟机失败:{}", "openId为空!");
                    break;
                }
                final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
                if(ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("delType"))){
                    log.error("删除虚拟机[{}]失败:{}", instanceId,"delType为空!");
                    break;
                }
                final Integer delType = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("delType")));
                String json = Converts.operateDeleteResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeleteVmUrl(instanceId,delType), new String[]{}),
                        "data");
                OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
                if(!response.getSuccess()){
                    log.error("删除虚拟机[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("删除虚拟机[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
                }
                responses.add(response);
            }
            return new BaseDataResponse<>(responses);
        }catch (Exception e){
            log.error("删除虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除虚拟机失败");
        }
    }

    /**
     * 删除虚拟机模板
     * @param request
     * @return
     */
    public static BaseResponse operateVmTemplateDelete(BaseCloudRequest request){
        try {
            log.info("获取删除虚拟机模板入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI baseCloudRequestBodyCI : cis){
                if(ObjectUtil.isEmpty(baseCloudRequestBodyCI.get("openId"))){
                    log.error("删除虚拟机模板:{}", "openId为空!");
                    break;
                }
                final Integer instanceId = Integer.valueOf(String.valueOf(baseCloudRequestBodyCI.get("openId")));
                String json = Converts.operateDeleteResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeleteVmTemplateUrl(instanceId), new String[]{}),
                        "data");
                OperateInstanceStatusResponse response = JSONObject.parseObject(json, OperateInstanceStatusResponse.class);
                if(!response.getSuccess()){
                    log.error("删除虚拟机模板[{}]失败:{}", instanceId,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("删除虚拟机模板[{}]成功:{}", instanceId,JSONObject.toJSONString(response));
                }
            }

            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("删除虚拟机模板失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除虚拟机模板失败");
        }
    }

    /**
     * 创建块设备
     * @param request
     * @return
     */
    public static BaseResponse operateCreateBlockDevice(BaseCloudRequest request){
        try {
            log.info("获取创建块设备入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();


            if(ObjectUtil.isEmpty(request.getBody().getCi())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "块设备信息为空!");
            }
            BaseCloudRequestBodyCI ci = request.getBody().getCi();

            if (ObjectUtil.isEmpty(ci.get("openName"))) {
                log.error("创建块设备异常:{}", "openName为空!");
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:openName为空!");
            }
            if (ObjectUtil.isEmpty(ci.get("lunSize"))) {
                log.error("创建块设备异常:{}", "lunSize为空!");
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:lunSize为空!");
            }
            if (!ci.containsKey("diskPoolName")) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:diskPoolName为空!");
            }
            if (!ci.containsKey("nodePoolName")) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:nodePoolName为空!");
            }
            if (!ci.containsKey("hostGroupId")) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:hostGroupId为空!");
            }
            if (!ci.containsKey("hostGroupName")) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建块设备异常:hostGroupName为空!");
            }
            log.info("获取创建块设备入参为：{}",ci);
            JSONObject param = new JSONObject();
            param.put("diskpoolName", ci.getString("diskPoolName"));
            param.put("hostGroupId", ci.getString("hostGroupId"));
            param.put("hostGroupName", ci.getString("hostGroupName"));
            param.put("lunName", ci.getString("openName"));
            param.put("lunSize", ci.get("lunSize"));
            param.put("lunType", ci.getString("lunType"));
            param.put("nodePoolName", ci.getString("nodePoolName"));
            param.put("poolName", ci.getString("poolName"));
            param.put("desc", ci.getString("desc"));
            param.put("poolStatus", ci.getString("poolStatus"));
            param.put("tGT", ci.get("tGT"));

            String json = Converts.operateResourceToPostByObject(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getCreateBlockDeviceUrl(0L), new String[]{}),
                    param);
            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            if(response.getSuccess()){
                return BaseResponse.SUCCESS;
            }
            log.error("创建块设备失败:{}", JSONObject.toJSONString(response));
            return BaseResponse.ERROR;
        }catch (Exception e){
            log.error("创建块设备失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建块设备失败");
        }
    }


//    /**
//     * 删除块设备
//     * @param request
//     * @return
//     */
//    public static BaseResponse operateDeleteBlockDevice(BaseCloudRequest request){
//        try {
//            log.info("获取删除块设备入参为：{}",request);
//            CloudAccessBean bean = request.getBody().getAccess();
//
//            String json = Converts.operateAddResourceToJsonString(request,
//                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeleteBlockDeviceUrl(0L), new String[]{}),
//                    "data");
//            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
//            if(response.getSuccess()){
//                return BaseResponse.SUCCESS;
//            }
//            log.error("删除块设备失败:{}", JSONObject.toJSONString(response));
//            return BaseResponse.ERROR;
//        }catch (Exception e){
//            log.error("删除块设备失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除块设备失败");
//        }
//    }

    /**
     * 批量删除块设备
     * @param request
     * @return
     */
    public static BaseResponse operateDeleteBlockDevice(BaseCloudRequest request){
        try {
            log.info("获取删除块设备入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "块设备信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI ci : cis){
                if(ObjectUtil.isEmpty(ci.get("openName"))){
                    log.error("删除块设备异常:{}", "openName为空!");
                    break;
                }

                final String name = String.valueOf(ci.get("openName"));
                String sourceJsonString = ci.getString("sourceJson");
                JSONObject sourceJsonObject = JSON.parseObject(sourceJsonString);
                if (!sourceJsonObject.containsKey("diskPoolName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:diskPoolName为空!");
                }
                if (!sourceJsonObject.containsKey("nodePoolName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:nodePoolName为空!");
                }
                if(ObjectUtil.isEmpty(ci.get("openId"))){
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:openId为空!");
                }
                if (!sourceJsonObject.containsKey("hostGroupId")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:hostGroupId为空!");
                }
                if (!sourceJsonObject.containsKey("hostGroupName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:hostGroupName为空!");
                }
                JSONObject param = new JSONObject();
                param.put("diskpoolName",sourceJsonObject.getString("diskPoolName"));
                param.put("hostGroupId",sourceJsonObject.getString("hostGroupId"));
                param.put("hostGroupName",sourceJsonObject.getString("hostGroupName"));
                param.put("lunId",ci.getString("openId"));
                param.put("nodePoolName",sourceJsonObject.getString("nodePoolName"));
                param.put("lunName",sourceJsonObject.getString("openName"));
                param.put("lunList",Collections.singletonList(ci.getString("openId")));

                String json = Converts.operateResourceToPostByObject(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeleteBlockDeviceUrl(0L), new String[]{}),
                        param);
                BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
                if(!response.getSuccess()){
                    log.error("删除块设备[{}]失败:{}", name,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("删除块设备[{}]成功:{}", name,JSONObject.toJSONString(response));
                }
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("删除块设备失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除块设备失败");
        }
    }

    /**
     * 更新块设备
     * @param request
     * @return
     */
    public static BaseResponse operateUpdateBlockDevice(BaseCloudRequest request){
        try {
            log.info("获取更新块设备入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();


            if(ObjectUtil.isEmpty(request.getBody().getCi())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "块设备信息为空!");
            }
            BaseCloudRequestBodyCI ci = request.getBody().getCi();

                if (ObjectUtil.isEmpty(ci.get("openName"))) {
                    log.error("更新块设备异常:{}", "openName为空!");
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:openName为空!");
                }
            if (ObjectUtil.isEmpty(ci.get("lunSize"))) {
                log.error("更新块设备异常:{}", "lunSize为空!");
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:lunSize为空!");
            }
                final String name = String.valueOf(ci.get("openName"));
                String sourceJsonString = ci.getString("sourceJson");
                JSONObject sourceJsonObject = JSON.parseObject(sourceJsonString);
                if (!sourceJsonObject.containsKey("diskPoolName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:diskpoolName为空!");
                }
                if (!sourceJsonObject.containsKey("nodePoolName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:nodePoolName为空!");
                }
            if(ObjectUtil.isEmpty(ci.get("openId"))){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:openId为空!");
            }
            if (!sourceJsonObject.containsKey("hostGroupId")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:hostGroupId为空!");
                }
            if (!sourceJsonObject.containsKey("hostGroupName")) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "更新块设备异常:hostGroupName为空!");
                }
            log.info("获取更新块设备入参为：{}",ci);
            log.info("获取更新块设备入参为：{}",sourceJsonObject);
                JSONObject param = new JSONObject();
                param.put("diskpoolName", sourceJsonObject.getString("diskPoolName"));
                param.put("hostGroupId", sourceJsonObject.getString("hostGroupId"));
                param.put("lunName", sourceJsonObject.getString("openName"));
                param.put("lunSize", ci.get("lunSize"));
                param.put("lunType", sourceJsonObject.getString("lunType"));
                param.put("nodePoolName", sourceJsonObject.getString("nodePoolName"));
                param.put("desc", sourceJsonObject.getString("desc"));
                param.put("poolName", sourceJsonObject.getString("poolName"));
                param.put("lunId", ci.getString("openId"));
                param.put("oldHostGroupId", sourceJsonObject.getString("oldHostGroupId"));
                param.put("oldSize", sourceJsonObject.get("oldSize"));
                String json = Converts.operateResourceToPutByObject(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getUpdateBlockDeviceUrl(0L), new String[]{}),
                        param);
                BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
                if(!response.getSuccess()){
                    log.error("更新块设备[{}]失败:{}", name,JSONObject.toJSONString(response));
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "更新块设备失败");
                }else {
                    log.info("更新块设备[{}]成功:{}", name,JSONObject.toJSONString(response));
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("更新块设备失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "更新块设备失败");
        }
    }


    /**
     * 新增虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse operateCreateVirtualSwitch(BaseCloudRequest request){
        try {
            log.info("获取新增虚拟交换机入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            JSONObject cloud = request.getBody().getCloud();
            List<Integer> networkType = cloud.getObject("networkType", List.class);
            int i = encodeNetworkTypes(networkType);
            cloud.put("networkType",i);
            String json = Converts.operateResourceToPostByObject(request,
                    URLUtils.bean.makeUrl(bean, URLUtils.bean.getCreateVirtualSwitchUrl(), new String[]{}),
                    cloud);
            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            if(response.getSuccess()){

//                request.getBody().getCloud().put("clusterIds", Arrays.asList(String.valueOf(cloud.getString("clusterId"))));
//                request.getBody().getCloud().put("hostPoolId", Arrays.asList(String.valueOf(cloud.getString("hostPoolId"))));
//                request.setAction(ActionType.FETCH_NEUTRON_SWITCH);
//                try {
//                    Thread.sleep(5000L);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                fetchVirtualSwitch(request);

                return BaseResponse.SUCCESS;
            }
            log.error("新增虚拟交换机失败:{}", JSONObject.toJSONString(response));
            return BaseResponse.ERROR;
        }catch (Exception e){
            log.error("新增虚拟交换机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "新增虚拟交换机失败");
        }
    }

    /**
     * 删除虚拟交换机
     * @param request
     * @return
     */
    public static BaseResponse operateDeleteVirtualSwitch(BaseCloudRequest request){
        try {
            log.info("获取删除虚拟交换机入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCis())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟交换机信息为空!");
            }
            final List<BaseCloudRequestBodyCI> cis = request.getBody().getCis();
            for (BaseCloudRequestBodyCI ci : cis){
                if(ObjectUtil.isEmpty(ci.get("openName"))){
                    log.error("删除虚拟交换机异常:{}", "openName为空!");
                    break;
                }
                final String name = String.valueOf(ci.get("openName"));
                if(ObjectUtil.isEmpty(ci.get("clusterId"))){
                    log.error("删除虚拟交换机异常:{}", "clusterId为空!");
                    break;
                }
                final Long clusterId = Long.valueOf(String.valueOf(ci.get("clusterId")));
                String json = Converts.operateDeleteResourceToJsonString(request,
                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getDeleteVirtualSwitchUrl(), new String[]{"?",name, String.valueOf(clusterId)}),
                        "data");
                BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
                if(!response.getSuccess()){
                    log.error("删除虚拟交换机[{}]失败:{}", name,JSONObject.toJSONString(response));
                    break;
                }else {
                    log.info("删除虚拟交换机[{}]成功:{}", name,JSONObject.toJSONString(response));
                }
//                request.getBody().getCloud().put("clusterIds", Arrays.asList(String.valueOf(clusterId)));
//                request.setAction(ActionType.FETCH_NEUTRON_SWITCH);
//                try {
//                    Thread.sleep(5000L);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                fetchVirtualSwitch(request);
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            log.error("删除虚拟交换机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除虚拟交换机失败");
        }
    }


    /**
     * 操作控制台
     * @param request
     * @return
     */
    public static BaseResponse operateWebConsole(BaseCloudRequest request){
        try {
            log.info("获取操作控制台入参为：{}",request);
            CloudAccessBean bean = request.getBody().getAccess();
            if(ObjectUtil.isEmpty(request.getBody().getCi())){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机信息为空!");
            }
            final BaseCloudRequestBodyCI ci = request.getBody().getCi();

            if(ObjectUtil.isEmpty(ci.get("openId"))){
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机Id为空!");
            }
            Integer instanceId = Integer.valueOf(String.valueOf(ci.get("openId")));
            String path = "/home/<USER>/";
            String protocols = "https";
            String port = "futong-novnc-port";
            String ip = "futong-zy-novnc";
//            Long instanceId = Long.valueOf(String.valueOf(ci.get("openId")));
//            if(ObjectUtil.isEmpty(request.getBody().getModel())){
//                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "额外参数为空!");
//            }
//            final JSONObject model = request.getBody().getCloud();
//            if(ObjectUtil.isEmpty(model.get("operateType"))){
//                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "操作类型为空!");
//            }
//            String operateType = String.valueOf(ci.get("operateType"));
//            String username = "";
//            String pwd = "";
//            if("4".equals(operateType)){
//                if(ObjectUtil.isEmpty(model.get("username"))){
//                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机新用户名为空!");
//                }
//                username = String.valueOf(model.get("username"));
//                if(ObjectUtil.isEmpty(model.get("password"))){
//                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "虚拟机新密码为空!");
//                }
//                pwd = String.valueOf(model.get("password"));
//            }
//            String json = "";
//            if("4".equals(operateType)){
//                json = Converts.operateAddResourceToJsonString(request,
//                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getWebConsoleUpdateUserUrl(), new String[]{"?", String.valueOf(instanceId), operateType,username,pwd}),
//                        "data");
//            }else {
//                json = Converts.operateAddResourceToJsonString(request,
//                        URLUtils.bean.makeUrl(bean, URLUtils.bean.getWebConsoleUrl(), new String[]{"?", String.valueOf(instanceId), operateType}),
//                        "data");
//            }
//
//            BaseResponseBodyData response = JSONObject.parseObject(json, BaseResponseBodyData.class);
            String json = "";
            String url =URLUtils.bean.makeUrl(bean,URLUtils.bean.getVmSummaryUrl(instanceId), null) ;
            json = Converts.fetchResourceToJsonString(request,
                    URLUtils.bean.makeUrl(bean,URLUtils.bean.getVmSummaryUrl(instanceId), null),
                        "data");
            String key = "uis_"+instanceId;
            DescribeVmSummaryResponse response = JSONObject.parseObject(json, DescribeVmSummaryResponse.class);
            if(ObjectUtil.isNotEmpty(response)&& response.getSuccess()){
                String hostIp =  response.getData().getHostIp();
                Integer vncPort =  response.getData().getVncport();
                if(ObjectUtil.isEmpty(hostIp)||vncPort ==-1){
                    throw new BaseException(BaseResponse.FAIL, "无法打开控制台，请确认设备处于开机状态.");
                }
                File d = new File(path);
                if(!d.isDirectory()) {
                    d.mkdirs();
                }
                String str = key+": "+hostIp+":"+vncPort;
                String fileName = path+"token_uis_"+instanceId+".conf";
                File f = new File(fileName);
                if(!f.isFile()) {
                    f.createNewFile();
                }
                try (PrintWriter writer = new PrintWriter(fileName, "UTF-8")) {
                    writer.println(str);
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }
//            String result = protocols+"://"+ip+":"+port+"/cmp-novnc/vnc_lite.html?host="+ip+"&port="+port2+"&path=websockify/?token="+key;
            String result = protocols+"://"+ip+":"+port+"/cmp-novnc/vnc_lite.html?host="+ip+"&port="+port+"&path=websockify/?token="+key;
            return new BaseDataResponse(result);
        }catch (Exception e){
            log.error("操作控制台失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "操作控制台失败");
        }
    }


    public static int encodeNetworkTypes(List<Integer> positions) {
        if (positions == null || positions.isEmpty()) {
            return 0; // 空列表返回0
        }

        int result = 0;
        for (Integer pos : positions) {
            if (pos != null && pos >= 0 && pos <= 5) {
                result |= (1 << pos); // 将对应位置的二进制位设为1
            } else {
                System.out.println("警告：无效的网络类型位置 " + pos + "，已忽略");
            }
        }
        return result;
    }
}
