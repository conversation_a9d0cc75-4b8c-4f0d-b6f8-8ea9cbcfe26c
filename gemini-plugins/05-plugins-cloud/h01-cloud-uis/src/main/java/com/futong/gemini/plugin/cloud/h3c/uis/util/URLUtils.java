package com.futong.gemini.plugin.cloud.h3c.uis.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;
public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取token信息*/
    private String[] tokenUrl = { "/uis/spring_check","encrypt","loginType","name","password"};

    public String[] getTokenUrl() {
        return tokenUrl.clone();
    }

    /** 获取计算资源URL */
    private String[] computeUrl = { "/uis/uis/vm","offset","limit","isNeedNetwork" };

    public String[] getComputeUrl() {
        return computeUrl.clone();
    }
    /** 获取集群URL */
    private String[] clusterUrl = { "/uis/uis/cluster/queryClusterList","offset","limit","hostPoolId" };

    public String[] getClusterUrl() {
        return clusterUrl.clone();
    }


    /** 获取主机URL */
    private String[] hostUrl = {"/uis/uis/host/queryHostInfo","offset","limit"};

    public String[] getHostUrl() {
        return hostUrl.clone();
    }

    /** 获取主机URL */
    private String[] hostPoolUrl = {"/uis/uis/host/hostpool/summary"};

    public String[] getHostPoolUrl() {
        return hostPoolUrl.clone();
    }

    /** 获取告警URL */
    private String[] alarmUrl = { "/uis/warnManage/realTimeAlarms","offset","limit","eventTime_from","eventTime_to"};

    public String[] getAlarmUrl() {
        return alarmUrl.clone();
    }

    /** 获取存储资源接口URL */

    public String[] getStorageUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/oneStorV3RbdList","offset","limit"}.clone();
    }

    /**
     * 获取模版URL
     * @return
     */
    public String[] getTemplateUrl() {
        return new String[]{"/uis/uis/vmTemplate","offset","limit"}.clone();
    }

    /**
     * 获取网络策略URL
     * @return
     */
    public String[] getVirtualSwitchUrl() {
        return new String[]{"/uis/uis/network/cluster/vswitchs","clusterId"}.clone();
    }

    public String[] getVirtualFirewallUrl() {
        return new String[]{"/uis/uis/securityGroup","offset","limit"}.clone();
    }

    /**
     * 获取主机概要信息URL
     * @return
     */
    public String[] getHostOverviewInfoUrl() {
        return new String[]{"/uis/uis/host/queryHostOverviewInfo","hostId"}.clone();
    }
    /**
     * 虚拟机CPU使用率
     * @param hostId
     * @return
     */
    public String[] getVmCPUTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/vm/"+hostId+"/cpuTrend","interval"}.clone();
    }

    /**
     * 虚拟机内存使用率
     * @param hostId
     * @return
     */
    public String[] getVmMemoryTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/vm/"+hostId+"/memTrend","interval"}.clone();
    }

    /**
     * 虚拟机磁盘使用率
     * @param hostId
     * @return
     */
    public String[] getVmDiskTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/vm/"+hostId+"/diskRate","interval"}.clone();
    }

    /**
     * 虚拟机网络吞吐量
     * @param hostId
     * @return
     */
    public String[] getVmNetWorkTrendTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/vm/"+hostId+"/netRwTrend","interval"}.clone();
    }

    /**
     * CPU使用率
     * @param hostId
     * @return
     */
    public String[] getCPUTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/host/"+hostId+"/cpuTrend","interval"}.clone();
    }

    /**
     * 内存使用率
     * @param hostId
     * @return
     */
    public String[] getMemoryTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/host/"+hostId+"/memTrend","interval"}.clone();
    }

    /**
     * 磁盘使用率
     * @param hostId
     * @return
     */
    public String[] getDiskTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/host/"+hostId+"/diskRate","interval"}.clone();
    }

    /**
     * 网络吞吐量
     * @param hostId
     * @return
     */
    public String[] getNetWorkTrendTrendUrl(Integer hostId) {
        return new String[]{"/uis/uis/host/"+hostId+"/netRwTrend","interval"}.clone();
    }

    /**
     * 获取云盘URL
     * @param vmId
     * @return
     */
    public String[] getCloudDiskUrl(String vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/domainDetail"}.clone();
    }

    /**
     * 新增虚拟机
     * @return
     */
    public String[] getAddVmUrl() {
        return new String[]{"/uis/uis/vm/add"}.clone();
    }

    /**
     * 新增虚拟机
     * @return
     */
    public String[] getDeployVmUrl() {
        return new String[]{"/uis/uis/vm/deploy"}.clone();
    }

    /**
     * 更新虚拟机
     * @return   /uis/uis/vm/batchModify
     */
    public String[] getUpdateVmUrl() {
//        return new String[]{"/uis/uis/vm"}.clone();
        return new String[]{"/uis/uis/vm/batchModify"}.clone();
    }

    /**
     * 启动虚拟机
     * @param vmId
     * @return
     */
    public String[] getStartVmUrl(Integer vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/start"}.clone();
    }

    /**
     * 安全关闭虚拟机
     * @param vmId
     * @return
     */
    public String[] getShutDownVmUrl(Integer vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/shutDown"}.clone();
    }

    /**
     * 关闭虚拟机电源
     * @param vmId
     * @return
     */
    public String[] getStopVmUrl(Integer vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/close"}.clone();
    }

    /**
     * 重启虚拟机
     * @param vmId
     * @return
     */
    public String[] getRestartVmUrl(Integer vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/restart"}.clone();
    }

    /**
     * 删除虚拟机
     * @param vmId
     * @return
     */
    public String[] getDeleteVmUrl(Integer vmId,Integer delType) {
        return new String[]{"/uis/uis/vm/"+vmId+"/"+delType}.clone();
    }

    /**
     * 删除虚拟机模板
     * @param templateId
     * @return
     */
    public String[] getDeleteVmTemplateUrl(Integer templateId) {
        return new String[]{"/uis/uis/vmTemplate/"+templateId}.clone();
    }

    /**
     * 创建块设备
     * @return
     */
    public String[] getCreateBlockDeviceUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/oneStorV3Block"}.clone();
    }

    /**
     * 删除块设备
     * @return
     */
    public String[] getDeleteBlockDeviceUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/deleOneStorV3Block"}.clone();
    }

    /**
     * 更新块设备
     * @return
     */
    public String[] getUpdateBlockDeviceUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/oneStorV3Block"}.clone();
    }

    /**
     * 创建虚拟交换机
     * @return
     */
    public String[] getCreateVirtualSwitchUrl() {
        return new String[]{"/uis/uis/network/cluster/vswitch"}.clone();
    }

    /**
     * 删除虚拟交换机
     * @return
     */
    public String[] getDeleteVirtualSwitchUrl() {
        return new String[]{"/uis/uis/network/cluster/vswitch","name","clusterId"}.clone();
    }

    /**
     * 操作控制台
     * @return
     */
    public String[] getWebConsoleUrl() {
        return new String[]{"/uis/uis/vnc/noVnc","id","operateType"}.clone();
    }

    /**
     * 操作控制台-修改用户名密码
     * @return
     */
    public String[] getWebConsoleUpdateUserUrl() {
        return new String[]{"/uis/uis/vnc/noVnc","id","operateType","username","password"}.clone();
    }

    /**
     * 查询数据池
     * @return
     */
    public String[] getDataPoolUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/datapoolList"}.clone();
    }
    /**
     * 查询主机组
     * @return
     */
    public String[] getHostGroupUrl(Long cloudId) {
        return new String[]{"/uis/uis/oneStor/"+cloudId+"/hostGroups"}.clone();
    }

    /**
     * 查询网络策略模板
     * @return
     */
    public String[] getNetworkPortProfileUrl() {
        return new String[]{"/uis/uis/network/portprofile"}.clone();
    }

    /**
     * 重启虚拟机
     * @param vmId
     * @return
     */
    public String[] getVmSummaryUrl(Integer vmId) {
        return new String[]{"/uis/uis/vm/"+vmId+"/summary"}.clone();
    }

    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
//        String url =  "http://************:80";
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

}
