package com.futong.gemini.plugin.cloud.h3c.uis.http;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.h3c.uis.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.h3c.uis.request.DescribeUisRequest;
import com.futong.gemini.plugin.cloud.h3c.uis.util.URLUtils;
import com.futong.gemini.plugin.cloud.h3c.uis.vo.UisToken;
import com.futong.gemini.plugin.cloud.h3c.uis.vo.UisTokenParam;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;

import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
@Slf4j
public class HttpClientUtil {
    private static final String HTTPS = "https";

    /***
     * 获取token
     * @param bean
     * @return
     */
    public  static UisToken getToken(CloudAccessBean bean){
        UisToken token =null;
        try {
            token = (UisToken) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        } catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }
        if(ObjectUtil.isNull(token)){
            token = getUisToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}",JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, bean.getCmpId(),token);
        }
        log.info("token获取成功");
        return token;
    }
    public  static UisToken getUisToken(CloudAccessBean bean) {
        UisToken token =null;
        /**拼接获取 token url */
        String  url  = URLUtils.bean.makeUrl(bean, URLUtils.bean.getTokenUrl(),new String[] { "?","false","authorCenter",bean.getUsername(),bean.getPassword()});
        log.info("拼接获取 token url:{}",url);
//        UisTokenParam param = new UisTokenParam();
        HashMap<String, String> quertParam = new HashMap<>();
        try {
            token =   fetchToken(url,quertParam, null,HttpClientConfig.class.newInstance(),bean);
        } catch (Exception e) {
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
        log.info("getUisToken 获取token成功");
        return token;
    }
    private static UisToken fetchToken(String url,HashMap<String, String> quertParam, String json, HttpClientConfig config, CloudAccessBean param) {


        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
//            // 构建包含查询参数的 URI
//            URIBuilder uriBuilder = new URIBuilder(url);
//            if (quertParam != null) {
//                for (Map.Entry<String, String> entry : quertParam.entrySet()) {
//                    uriBuilder.addParameter(entry.getKey(), entry.getValue());
//                }
//            }
//            URI uri = uriBuilder.build();


//            HttpPost httpPost = new HttpPost(uri);

//            CloseableHttpClient  httpClient = buildHttpClient(url);
//            HttpPost httpPost = new HttpPost(url);
//            if (config == null) {
//                config = new HttpClientConfig();
//            }
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");

            // 设置 Cookie 请求头
//            if (cookieParams != null && !cookieParams.isEmpty()) {
//                StringBuilder cookieHeaderValue = new StringBuilder();
//                for (Map.Entry<String, String> entry : cookieParams.entrySet()) {
//                    if (cookieHeaderValue.length() > 0) {
//                        cookieHeaderValue.append("; ");
//                    }
//                    cookieHeaderValue.append(entry.getKey()).append("=").append(entry.getValue());
//                }
//                httpPost.addHeader("Cookie", cookieHeaderValue.toString());
//            }
            //body请求体
            if (json != null && !json.isEmpty()) {
                EntityBuilder entityBuilder = EntityBuilder.create();
                entityBuilder.setText(json);
                entityBuilder.setContentType(ContentType.APPLICATION_JSON);
                entityBuilder.setContentEncoding(config.getCharset());
                HttpEntity requestEntity = entityBuilder.build();
                httpPost.setEntity(requestEntity);
            }

            HttpResponse response = httpClient.execute(httpPost);
            String result = getResponseStr(response, config);
            UisToken token = JSON.parseObject(result, UisToken.class);
            log.info("UisToken:{}",JSON.toJSONString(token));
//            Header[] headers = response.getHeaders(TokenEnum.AUBJECT_TOKEN.getValue());
//            if(ObjectUtil.isNotEmpty(headers)){
//                token.setAuthToken(headers[0].getValue());
//            }
            token.setAuthToken(token.getAcToken());
            return token;
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public  static String get(CloudAccessBean bean, DescribeUisRequest request, String[] url, String[] args){
       String  newUrl  = URLUtils.bean.makeUrl(bean, url,args);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(),request.getAuthToken().getAuthToken());
        // 设置 Cookie 请求头
                StringBuilder cookieHeaderValue = new StringBuilder();
//                for (Map.Entry<String, String> entry : cookieParams.entrySet()) {
//                    if (cookieHeaderValue.length() > 0) {
//                        cookieHeaderValue.append("; ");
//                    }
//                    cookieHeaderValue.append(entry.getKey()).append("=").append(entry.getValue());
//                }
        cookieHeaderValue.append(TokenEnum.AUTH_TOKEN.getValue()).append("=").append(request.getAuthToken().getAuthToken());
        config.addHeader("Cookie", cookieHeaderValue.toString());
       return  get(newUrl,config);
    }
    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @return
     */
    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            URL urlObj = new URL(url);
            if (url.startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }
    /**
     * Get http请求
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @return 响应结果字符串
     */
    public static String get(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpGet httpGet = new HttpGet(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            httpGet.addHeader("Content-Type","application/json");
            httpGet.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpGet);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public static String put(String url, HttpClientConfig config,String json) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpPut httpPut = new HttpPut(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPut.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPut.addHeader(key, header.get(key));
            }
            httpPut.addHeader("Content-Type","application/json");
            httpPut.addHeader("Accept","application/json");
            if(ObjectUtil.isNotEmpty(json)){
                EntityBuilder entityBuilder = EntityBuilder.create();
                entityBuilder.setText(json);
                entityBuilder.setContentType(ContentType.APPLICATION_JSON);
                entityBuilder.setContentEncoding(config.getCharset());
                HttpEntity requestEntity = entityBuilder.build();
                httpPut.setEntity(requestEntity);
            }
            HttpResponse response = httpClient.execute(httpPut);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    public static boolean isJsonarray(String jsonString){
        try {
            new JSONArray(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    private static String getResponseStr(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        return EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");
    }

    /**
     * 删除
     * @param url
     * @param config
     * @return
     */
    public static String delete(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpDelete httpDelete = new HttpDelete(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
}
