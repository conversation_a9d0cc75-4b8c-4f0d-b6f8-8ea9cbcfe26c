package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeTrendResponse extends BaseResponseBodyData{

    @JsonProperty("data")
    private List<DescribeTrendData> data;

    @Data
    public class DescribeTrendData {
        private Double rate;
        private Long time;
    }



}
