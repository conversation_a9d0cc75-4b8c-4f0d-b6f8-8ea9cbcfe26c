package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeHostOverviewInfoResponse extends BaseResponseBodyData{
    @JsonProperty("data")
    private List<DescribeHostOverviewInfoData> data;


    @Data
    public static class DescribeHostOverviewInfoData {
        //主机相关信息名称
        private String name;
        //主机相关信息英文名称
        private String identifier;
        //主机相关信息数值
        private String data;
        //主机相关信息百分比
        private Double progress;
        //主机名
        private String hostName;
        //主机角色0(计算)，1(超融合)，2(存储)
        private Integer businessRole;
        //主机用户
        private String hostPw;
        //主机密码
        private String hostUser;
        //CPU主频(MHz)
        private String memory;
        //内存大小(MB)
        private String cpuFrequency;
        //可用内存，带单位，单位范围： GB，TB
        private String freeMemory;
        //关闭状态下的虚拟机个数
        private String shutoff;
        //运行状态下的虚拟机个数
        private String run;
        //虚拟机总个数
        private String total;
        //虚拟机镜像个数
        private String image;
        //存储大小(MB)
        private String storage;
        //可用存储带单位，单位范围：MB，GB，TB
        private String freeStorage;
        //系统时间
        private String systemTime;
        //数据平衡存储状态 off(关闭) on(打开)
        private String storageStatus;
        //主机iLOs地址
        private List<String> iLOs;
    }


}
