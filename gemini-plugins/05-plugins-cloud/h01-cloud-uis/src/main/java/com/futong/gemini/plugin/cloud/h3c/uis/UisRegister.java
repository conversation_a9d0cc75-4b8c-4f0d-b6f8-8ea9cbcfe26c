package com.futong.gemini.plugin.cloud.h3c.uis;
import com.futong.gemini.plugin.cloud.h3c.uis.sampler.FetchService;
import com.futong.gemini.plugin.cloud.h3c.uis.sampler.OperateService;
import com.futong.gemini.plugin.cloud.h3c.uis.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.h3c.uis.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
public class UisRegister  extends BaseCloudRegister {

    @Override
    public void load() {
        onAfterLoadAccount();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadOperate();//加载云主机操作
    }

    public void onAfterLoadAccount() {
        //云账号验证
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authCloudAccount);
        //获取云账号表单信息
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);
        //获取调度添加模型
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);
        //添加默认调度任务
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);
    }
    public  void onAfterLoadFetch() {
        //默认查询页码及条数100
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_PLATFORM_CLUSTER,
                ActionType.FETCH_COMPUTE_HOST,
                ActionType.FETCH_BLOCK_STORAGE,
                ActionType.FETCH_COMPUTE_TEMPLATE,
                ActionType.FETCH_NEUTRON_SWITCH,
                ActionType.FETCH_COMPUTE_SECURITYGROUP,
                ActionType.FETCH_PLATFORM_ALARM

        );

//        //默认查询页码及条数50
        registerBefore(CloudService::defaultPage15,
                ActionType.FETCH_COMPUTE_INSTANCE

        );

        register(ActionType.FETCH_PLATFORM_HOSTPOOL, FetchService::fetchHostPool);//同步主机池
        register(ActionType.FETCH_PLATFORM_CLUSTER, FetchService::fetchCluster);//集群
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost);//同步主机
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchInstance);//同步云主机
        register(ActionType.FETCH_BLOCK_STORAGE, FetchService::fetchBlockDevice);//同步块设备
        register(ActionType.FETCH_COMPUTE_TEMPLATE, FetchService::fetchTemplate);//同步虚拟机模版
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchVirtualSwitch);//同步虚拟交换机
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchVirtualFirewall);//同步虚拟防火墙

        //获取主机监控,预处理
        registerBefore(FetchService::defaultBeforeBasicInfo,
                ActionType.FETCH_COMPUTE_HOST_PERF,
                ActionType.FETCH_COMPUTE_HOST_OVERVIEW_INFO
                );

        register(ActionType.FETCH_COMPUTE_HOST_OVERVIEW_INFO, FetchService::fetchHostOverviewInfo);//同步主机概要信息
        register(ActionType.FETCH_COMPUTE_HOST_PERF, FetchService::fetchHostPerf);//同步主机性能数据

        registerBefore(FetchService::defaultVmBeforeBasicInfo,
                ActionType.FETCH_COMPUTE_INSTANCE_PERF,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_COMPUTE_INSTANCE_NETWORK
        );
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchInstancePerf);//同步云主机性能数据
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchCloudDisk);//同步云盘
        //获取告警信息
        registerBefore(ActionType.FETCH_PLATFORM_ALARM,
                CloudService::defaultStartEndTimeOneDay);
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);
    }
    public  void onAfterLoadOperate() {

        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.openId", BaseUtils::formatSingle);

        register(ActionType.CREATE_COMPUTE_INSTANCE, OperateService::operateCreateInstance);//创建云主机
        register(ActionType.DEPLOY_COMPUTE_INSTANCE, OperateService::operateDeployInstance);//克隆云主机
        register(ActionType.UPDATE_COMPUTE_INSTANCE, OperateService::operateUpdateInstance);//修改云主机
        //批量启动云主机
        register(ActionType.START_COMPUTE_INSTANCE, OperateService::operateVmStart)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);;
        //批量关闭云主机电源
        register(ActionType.STOP_COMPUTE_INSTANCE, OperateService::operateVmStop)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量安全关闭云主机
        register(ActionType.SHUTDOWN_COMPUTE_INSTANCE, OperateService::operateVmShutDown)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);

        register(ActionType.REBOOT_COMPUTE_INSTANCE, OperateService::operateVmRestart)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);//批量重启云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, OperateService::operateVmDelete)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);//批量删除云主机

        register(ActionType.DELETE_VM_TEMPLATE, OperateService::operateVmTemplateDelete)
                .addTransferCloud("$.cis.openId","$.openId");//批量删除虚拟机模板
        register(ActionType.CREATE_VM_BLOCK_DEVICE, OperateService::operateCreateBlockDevice)
               ;//创建块设备
        register(ActionType.DELETE_VM_BLOCK_DEVICE, OperateService::operateDeleteBlockDevice)
               ;//删除块设备
        register(ActionType.UPDATE_VM_BLOCK_DEVICE, OperateService::operateUpdateBlockDevice)
               ;//扩容块设备
        //查询数据池列表
        register(ActionType.QUERY_DATA_POOL, DataPoolService::queryDataPoolList);
        //查询主机组列表
        register(ActionType.QUERY_HOST_GROUP, BlockDeviceService::queryHostGroupList);
        //查询网络策略模板列表
        register(ActionType.QUERY_NETWORK_PORT_PROFILE, ComputeInstanceService::queryNetworkPortProfileList);

        register(ActionType.CREATE_NEUTRON_VSWITCH, OperateService::operateCreateVirtualSwitch);//新增虚拟交换机
        register(ActionType.DELETE_NEUTRON_VSWITCH, OperateService::operateDeleteVirtualSwitch)
                .addTransferCloud("$.cis.openName","$.openName")
                .addTransferCloud("$.cis.clusterId","$.clusterId");//批量删除虚拟交换机
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, OperateService::operateWebConsole)
                .addTransferCloud("$.ci.openId","$.openId")
                ;//web控制台
    }
}