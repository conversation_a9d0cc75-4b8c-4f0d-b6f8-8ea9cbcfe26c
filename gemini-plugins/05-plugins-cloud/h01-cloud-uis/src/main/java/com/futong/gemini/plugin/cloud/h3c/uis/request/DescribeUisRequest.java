package com.futong.gemini.plugin.cloud.h3c.uis.request;
import com.futong.gemini.plugin.cloud.h3c.uis.vo.UisToken;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Getter
@Setter
public class DescribeUisRequest {

    /**
     * 待同步的资源ID
     */
    private List<String> ids;

    /**
     * 每次同步的条数
     */
    private Integer fecthSize;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * cloudos token信息
     */
    private UisToken authToken;

    /** 计算节点名称 */
    private String computeNode;

}
