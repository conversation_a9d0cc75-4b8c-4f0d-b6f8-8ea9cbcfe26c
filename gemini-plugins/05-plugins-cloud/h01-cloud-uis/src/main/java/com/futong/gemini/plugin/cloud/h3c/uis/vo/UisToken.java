package com.futong.gemini.plugin.cloud.h3c.uis.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UisToken implements Serializable {

    private final  long serialVersionUID = 1L;


    private String loginName;

    private Integer id;

    private String userName;

    private String loginTime;

    private String loginIp;

    private String sessionId;

    private String acToken;

    private String refreshToken;

    private Integer tokenExpireTime;


    private String authToken;
//
//    private Token token;
//    @Data
//    public static class Roles {
//        private String id;
//        private String name;
//    }
//    @Data
//    public static class ProjectDomain {
//        private String id;
//        private String name;
//    }
//    @Data
//    public static class Project {
//        private ProjectDomain domain;
//        private String id;
//        private String name;
//    }
//    @Data
//    public static class Endpoints {
//
//        private String url;
//        @JsonProperty("interface")
//        private String name;
//        private String region;
//
//        private String region_id;
//
//        private String id;
//    }
//
//    @Data
//    public static class Catalog {
//
//        private List<Endpoints> endpoints;
//
//        private String type;
//
//        private String id;
//
//        private String name;
//
//    }
//
//    @Data
//    public static class UserDomain {
//
//        private String id;
//
//        private String name;
//
//    }
//
//    @Data
//    public static class User {
//
//        private String password_expires_at;
//
//        private UserDomain domain;
//
//        private String id;
//
//        private String name;
//
//    }
//
//    @Data
//    public static class Token {
//
//        private Boolean is_domain;
//
//        private List<String> methods;
//
//        private List<Roles> roles;
//
//        private String expires_at;
//
//        private Project project;
//
//        //private List<Catalog> catalog;
//
//        private User user;
//
//        private List<String> audit_ids;
//
//        private String issued_at;
//
//    }
}