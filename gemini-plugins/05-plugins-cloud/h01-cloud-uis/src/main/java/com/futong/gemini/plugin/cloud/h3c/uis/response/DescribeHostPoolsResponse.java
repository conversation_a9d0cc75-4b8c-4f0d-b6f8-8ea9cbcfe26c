package com.futong.gemini.plugin.cloud.h3c.uis.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DescribeHostPoolsResponse extends BaseResponseBodyData{


    @JsonProperty("data")
    private List<DescribeHostPoolsData> data;

    @Data
    public static class DescribeHostPoolsData {


        private Long hpId;

        private String hpName;

        private Long operGroupId;

        private String operGroupCode;

        private Integer clusterNum;

        private Integer hostNum;

        private Integer cpu;

        private String mem;

        private String totalStorage;

        private String usableStorage;

        private String virtualHostDensity;

        private VirtualHost virtualHost;



    }


    @Data
    public static class VirtualHost {
        private Integer total;

        private Integer run;

        private Integer shutoff;

        private Integer image;
    }

}
