<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.futong.gemini</groupId>
        <artifactId>05-plugins-cloud</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>v01-cloud-vmware</artifactId>
    <properties>
        <plugin.name>cloud-vm_vmware-6.5-${yunjing.version}-${plugin.version}</plugin.name>
        <cxf.version>3.5.5</cxf.version>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.futong.gemini</groupId>
            <artifactId>01-cloud-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>sdk</artifactId>
            <version>7.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>annotations</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>ssoclient</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>sso</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.vmware</groupId>
            <artifactId>vsan</artifactId>
            <version>6.0.1.0</version>
        </dependency>
<!--        &lt;!&ndash; Java 8 及以下 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>javax.xml.ws</groupId>-->
<!--            <artifactId>jaxws-api</artifactId>-->
<!--            <version>2.3.1</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; JAXB API（JDK 1.8 自带，但可能需要手动引入） &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>javax.xml.bind</groupId>-->
<!--            <artifactId>jaxb-api</artifactId>-->
<!--            <version>2.3.1</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; Metro 实现（支持代理配置） &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.sun.xml.ws</groupId>-->
<!--            <artifactId>jaxws-rt</artifactId>-->
<!--            <version>2.3.7</version>-->
<!--        </dependency>-->
        <!-- Apache CXF JAX-WS Frontend -->
    </dependencies>
</project>
