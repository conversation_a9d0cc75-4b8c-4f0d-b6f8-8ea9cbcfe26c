package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.image.CreateImageRequest;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.CreateVswitchRequest;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

;

@Slf4j
public class NetworkVswitchService {

    public static final NetworkVswitchService bean = new NetworkVswitchService();

    // 获取虚拟交换机
    public void fetchVswitch(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbVswitchRes> vswitchList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(hostId -> {
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            List<HostVirtualSwitch> vswitchs = null;
            try {
                vswitchs = (List<HostVirtualSwitch>) morManager.getDynamicProperty(hostMor, "config.network.vswitch");
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }

            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            ManagedObjectReference dcMor = null;
            if (parentMor.getValue().contains("domain-c")) {
                dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
            }
            if (ObjectUtil.isNotEmpty(vswitchs)) {
                ManagedObjectReference finalDcMor = dcMor;
                ManagedObjectReference finalParentMor = parentMor;
                vswitchs.forEach(vswitch -> {
                    CmdbVswitchRes vswitchRes = new CmdbVswitchRes();
                    vswitchRes.setCloud_type(accessBean.getCloudType());
                    List<String> pnic = vswitch.getPnic();
                    List<String> portgroup = vswitch.getPortgroup();
                    vswitchRes.setOpen_name(vswitch.getName());
                    vswitchRes.setOpen_id(vswitch.getKey());
                    vswitchRes.setMtu(vswitch.getMtu() != null ? vswitch.getMtu().toString() : "");
                    vswitchRes.setAccount_id(accessBean.getCmpId());
                    vswitchRes.setType(ResourceEnum.VSWITCH.getValue());
                    vswitchRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), hostId, vswitch.getKey()));
                    vswitchList.add(vswitchRes);
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(accessBean, hostId, new CmdbHostRes()), vswitchRes));
                    for (String p : pnic) {
                        CmdbNetcardRes nic = new CmdbNetcardRes();
                        String pc = p.replace("key-vim.host.PhysicalNic-","");
                        nic.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), hostMor.getValue(), pc));
                        associations.add(AssociationUtils.toAssociation(nic, vswitchRes));
                    }
                    for (String p : portgroup) {
                        CmdbSubnetRes nic = new CmdbSubnetRes();
                        String pg = p.replace("key-vim.host.PortGroup-","");
                        nic.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), pg));
                        associations.add(AssociationUtils.toAssociation(nic, vswitchRes));
                    }
                    resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class,finalDcMor.getValue(), ResourceType.CMDB_VSWITCH_RES.value(), hostId+"|"+vswitch.getKey()));
                    resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class,finalParentMor.getValue(), ResourceType.CMDB_VSWITCH_RES.value(), hostId+"|"+vswitch.getKey()));
                });
            }
        });
        //发送vswitch数据
        BaseUtils.sendMessage(vswitchList, arguments);

        //发送宿主机关联关系
        BaseUtils.sendMessage(associations, arguments);

        //发送宿主机关联关系
        BaseUtils.sendMessage(resourceSets, arguments);
    }

    public void fetchPortgroup(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbSubnetRes> subnetList = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        Map<String, String> networkMap = new HashMap<>();
        try {
            //获取所有的端口组
            List<ManagedObjectReference> networkList = morManager.getMorByType(rootRef, MORTypeEnum.Network.toString());
            log.info("获取所有端口组数量：{}",networkList.size());
            List<ManagedObjectReference> newList = networkList.stream().distinct().collect(Collectors.toList());
            for(ManagedObjectReference mor:newList) {
                networkMap.put(VmwareUtils.getName(mor, morManager), mor.getValue());
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        Map<String, String> finalNetworkMap = networkMap==null?new HashMap<>():networkMap;
        request.getIds().forEach(hostId -> {
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            List<HostPortGroup> portgroups = null;
            try {
                portgroups = (List<HostPortGroup>) morManager.getDynamicProperty(hostMor, "config.network.portgroup");
                log.info("主机：{}，端口组数量：{}", hostMor.getValue(),portgroups.size());
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }
            //遍历端口组
            if (ObjectUtil.isNotEmpty(portgroups)) {
                portgroups.forEach(portgroup -> {
                    CmdbSubnetRes subnet = new CmdbSubnetRes();
                    subnet.setCloud_type(accessBean.getCloudType());
                    subnet.setAccount_id(accessBean.getCmpId());
                    subnet.setOpen_name(portgroup.getSpec().getName());
                    String subnetId = "";
                    if(finalNetworkMap.get(portgroup.getSpec().getName())!=null) {
                        subnetId = finalNetworkMap.get(portgroup.getSpec().getName());
                        subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), portgroup.getSpec().getName()));
                        subnet.setOpen_id(subnetId);
                        ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.Network.toString(), subnetId);
                        NetworkSummary summary = null;
                        try {
                            summary = (NetworkSummary) morManager.getDynamicProperty(subnetMor, "summary");
                        } catch (Exception e) {
                            log.error("获取子网信息异常{}", e);
                        }
                        if (ObjectUtil.isNotNull(summary)) {
                            subnet.setStatus(summary.isAccessible() ? "active" : "inactive");
                        }
                        subnet.setVlan_id(portgroup.getSpec().getVlanId()+"");
                        subnetList.add(subnet);
                    }

                });
            }
        });
        //发送subnetList数据
        log.info("发送标准交换机端口组数量:{}",subnetList.size());
        BaseUtils.sendMessage(subnetList, arguments);
    }

    public  void  fetchNic(DescribeVmwareRequest request, JSONObject arguments){
        List<CmdbPhysicalNetcardRes> nets = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(hostId->{
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(),hostId);
            List<PhysicalNic> nics;
            try {
                nics = (List<PhysicalNic>)morManager.getDynamicProperty(hostMor, "config.network.pnic");
            } catch (Exception e) {
                log.error("获取主机网卡信息异常{}",e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取主机网卡信息异常");
            }
            //获取网卡信息，同步获取IP信息、网卡和云主机的关系数据、网卡与IP的关系数据
            VmwareUtils.bean.getCmdbHostNetcard(resourceSets,nets, associations,morManager,hostMor,nics,accessBean);
        });
        //发送网卡数据
        BaseUtils.sendMessage(nets,arguments);

        //发送网卡与主机关系数据
        BaseUtils.sendMessage(associations,arguments);
//        //南北新仓资源关系数据
        BaseUtils.sendMessage(resourceSets,arguments);
    }

    public static BaseResponse createSwitch(JSONObject arguments) {
        String message = "成功发起创建交换机请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateVswitchRequest request = BaseClient.bodys.get().getJSONObject("cloud").toJavaObject(CreateVswitchRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        try {
            VmwareUtils.bean.createSwitch(connection, request);
        } catch (Exception e) {
            log.error("创建交换机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建交换机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse updateSwitch(JSONObject arguments) {
        String message = "成功发起修改交换机请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateVswitchRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(CreateVswitchRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        try {
            VmwareUtils.bean.updateSwitch(connection, request);
        } catch (Exception e) {
            log.error("修改交换机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改交换机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteSwitch(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<JSONObject> instanceIds = getInstanceList(body);
        for (JSONObject instance : instanceIds) {
            String hostId = instance.getString("hostId");
            String networkName = instance.getString("networkName");
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType("HostNetworkSystem");
            hostMor.setValue("networkSystem-"+hostId.split("-")[1]);
            try {
                connection.getVimPort().removeVirtualSwitch(hostMor, networkName);
            } catch (Exception e) {
                log.error("删除标准交换机异常",e);
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<JSONObject> getInstanceList(JSONObject body) {
        List<JSONObject> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject o = new JSONObject();
                JSONObject ci = cis.getJSONObject(i);
                String networkName = ci.getString("openName");
                JSONObject host = ci.getJSONObject("relationHost");
                String hostId = host.getString("relationOpenId");
                o.put("hostId",hostId);
                o.put("networkName",networkName);
                instanceIds.add(o);
            }
        }
        return instanceIds;
    }

}
