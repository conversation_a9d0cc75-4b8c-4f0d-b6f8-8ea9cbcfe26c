package com.futong.gemini.plugin.cloud.vmware.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.request.PageSortSearchRequest;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class JobUtils {

    public static final JobUtils bean = new JobUtils();


    //拆分性能采集dataJob
    public  <T> List<JobInfo> splitPerfJob(BaseDataResponse<BaseResponseDataListModel<T>> response , BasePageSortSearchRequest searchRequest, JSONObject arguments){
        List<JobInfo> jobs = new ArrayList<>();
        log.info(StrUtil.format("同步云主机性能数据，共有：{}台，分: {} 次同步.",response.getData().getCount(), (int)Math.ceil((float)response.getData().getCount()/searchRequest.getSize())));
        for (int i =2; i <= response.getData().getCount()/searchRequest.getSize(); i++) {
            PageSortSearchRequest request = new PageSortSearchRequest();
            request.setCurrent(i);
            request.setSize(searchRequest.getSize());
            request.setSort(searchRequest.getSort());
            request.setSortField(searchRequest.getSortField());
            JSONObject cloneArguments = arguments.clone();
            JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
            cloneBody.put("current", i);
            cloneBody.put("size", searchRequest.getSize());
            cloneBody.put("sort",searchRequest.getSort());
            cloneBody.put("sortField", searchRequest.getSortField());
            cloneArguments.put("body", cloneBody);
            JobInfo jobInfo = new JobInfo();
            jobInfo.setRequest(cloneArguments);
            jobs.add(jobInfo);
        }
        return jobs;
    }
    //拆分配置数据采集dataJob
    public  List<JobInfo> splitDataJob(DescribeVmwareRequest request , List<ManagedObjectReference> morList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(morList) || ObjectUtil.isEmpty(morList)){
            return null;
        }
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {
            List<String> ids = new ArrayList<>();
            Integer size = request.getFecthSize() ==null? 30: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, morList.size(), (int)Math.ceil((float)morList.size()/size)));
            for (int i =1; i <= morList.size(); i++) {
                ids.add(morList.get(i-1).getValue());
                if(i % size == 0 || i == morList.size()){
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("resourceType",resourceType);
                    cloneBody.put("fecthSize", size);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                    ids = new ArrayList<>();
                }
            }
        });
        return jobs;
    }
}
