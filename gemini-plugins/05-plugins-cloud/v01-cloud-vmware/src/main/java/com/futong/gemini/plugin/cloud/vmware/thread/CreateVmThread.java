package com.futong.gemini.plugin.cloud.vmware.thread;

import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.vmware.common.FetchConverts;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.TaskInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class CreateVmThread extends Thread {

    private ManagedObjectReference cloneTask;
    private CloudAccessBean accessBean;

    private JSONObject arguments;
    private JSONObject body;

    public CreateVmThread(ManagedObjectReference cloneTask, CloudAccessBean accessBean,JSONObject body, JSONObject arguments) {
        this.cloneTask = cloneTask;
        this.accessBean = accessBean;
        this.body = body;
        this.arguments = arguments;
    }
    public void run() {
        try {
            VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
            MorManager morManager;
            morManager = new MorManager();
            morManager.setConnection(connection);
            if (cloneTask != null) {
                TaskInfo taskInfo = (TaskInfo) morManager.getDynamicProperty(cloneTask, "info");
                while ("running".equals(taskInfo.getState().value())||"queued".equals(taskInfo.getState().value())) {
                    try {
                        Thread.sleep(30000);
                        taskInfo = (TaskInfo) morManager.getDynamicProperty(cloneTask, "info");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                log.info("同步创建虚拟机状态={}",taskInfo.getState().value());
                if ("success".equals(taskInfo.getState().value())) {
                    FetchConverts.createInstanceEventJob(arguments,((ManagedObjectReference) taskInfo.getResult()).getValue());
                    JSONObject biz = body.getJSONObject("biz");
                    if(biz!=null) {
                        biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0", ((ManagedObjectReference) taskInfo.getResult()).getValue()));
                        FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                    }
                } else {
                    JSONObject biz = body.getJSONObject("biz");
                    if(biz!=null) {
                        biz.put("error", "云主机创建失败!");
                        FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                    }
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }


    }
}
