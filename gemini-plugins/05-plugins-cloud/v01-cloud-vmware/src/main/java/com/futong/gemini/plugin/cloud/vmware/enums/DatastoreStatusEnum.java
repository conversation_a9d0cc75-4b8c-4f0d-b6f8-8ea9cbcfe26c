package com.futong.gemini.plugin.cloud.vmware.enums;

public enum DatastoreStatusEnum {
    ACTIVE("active", "活动"),
    INACTIVE("inactive", "非活动"),
    UNKNOWN("unkown", "未知");
    private String key;
    private String label;

    DatastoreStatusEnum(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }
}
