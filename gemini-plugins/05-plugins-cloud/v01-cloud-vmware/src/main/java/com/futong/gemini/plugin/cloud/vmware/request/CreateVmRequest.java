package com.futong.gemini.plugin.cloud.vmware.request;

import com.alibaba.fastjson.JSONArray;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建虚拟机请求参数
 */
@Setter
@Getter
public class CreateVmRequest implements Serializable {

    //虚拟机名称
    private String vmName;

    //虚拟机描述
    private String description;

    //服务地址
    private String serverIp;

    //数据中心Id
    private String dcId;

    //文件夹id
    private String folderId;

    //数据存储id
    private String datastoreId;

    //主机&集群id
    private String hostSite;

    //是否开机
    private boolean isPowerOn;

    //模版id
    private String templateId;

    //cpu核数
    private Integer cpuSize;

    //内存大小 GB
    private Integer memSize;

    private JSONArray nics;

    private JSONArray disks;

    //扩展参数
    private String extendJson;

    private int vmNum;

}
