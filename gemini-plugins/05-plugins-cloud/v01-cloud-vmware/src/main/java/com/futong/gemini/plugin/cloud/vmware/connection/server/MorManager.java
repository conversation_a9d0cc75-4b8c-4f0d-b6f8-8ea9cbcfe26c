package com.futong.gemini.plugin.cloud.vmware.connection.server;

import com.futong.gemini.plugin.cloud.vmware.connection.Connection;
import com.futong.gemini.plugin.cloud.vmware.connection.builders.ObjectSpecBuilder;
import com.futong.gemini.plugin.cloud.vmware.connection.builders.PropertyFilterSpecBuilder;
import com.futong.gemini.plugin.cloud.vmware.connection.builders.PropertySpecBuilder;
import com.futong.gemini.plugin.cloud.vmware.connection.builders.TraversalSpecBuilder;
import com.vmware.vim25.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MorManager {
	VimPortType vimPort;
	ServiceContent serviceContent;
	Connection connection;

	public MorManager() {
	}

	public Connection getConnection() {
		return connection;
	}

	public void setConnection(Connection connection) {
		this.connection = connection;
	}

	public VimPortType getVimPort() {
		return vimPort;
	}

	public void setVimPort(VimPortType vimPort) {
		this.vimPort = vimPort;
	}

	/**
	 * 根据类型获取所有资源 返回map，key为资源ID
	 * 
	 * @param container
	 * @param morefType
	 * @return
	 * @throws InvalidPropertyFaultMsg
	 * @throws RuntimeFaultFaultMsg
	 */
	public List<ManagedObjectReference> getMorByType(ManagedObjectReference container, String morefType)
			throws InvalidPropertyFaultMsg, RuntimeFaultFaultMsg {
		return getMorByType(container, morefType, new RetrieveOptions());
	}

	/***
	 * 根据名称获取ManagedObjectReference
	 * 
	 * @param container
	 * @param morefType
	 * @return List
	 * @throws InvalidPropertyFaultMsg
	 * @throws RuntimeFaultFaultMsg
	 */
	public List<ManagedObjectReference> getMoRefByName(ManagedObjectReference container, String morefType,
			String morname) throws InvalidPropertyFaultMsg, RuntimeFaultFaultMsg {
		if (container == null) {
			container = connection.getServiceContent().getRootFolder();
		}
		init();
		RetrieveResult rslts = containerViewByType(container, morefType, new RetrieveOptions());
		List<ManagedObjectReference> morList = new ArrayList<ManagedObjectReference>();
		String token = null;
		if (rslts != null) {
			token = rslts.getToken();
			for (ObjectContent oc : rslts.getObjects()) {
				ManagedObjectReference mr = oc.getObj();
				String entityNm = null;
				List<DynamicProperty> dps = oc.getPropSet();
				if (dps != null) {
					for (DynamicProperty dp : dps) {
						entityNm = (String) dp.getVal();
						if (entityNm != null && entityNm.endsWith(morname)) {
							morList.add(mr);
						}
					}
				}
			}
		}
		return morList;
	}

	/***
	 * 根据ID获取ManagedObjectReference
	 * 
	 * @param container
	 * @param morefType
	 * @return List
	 * @throws InvalidPropertyFaultMsg
	 * @throws RuntimeFaultFaultMsg
	 */
	public ManagedObjectReference getMoRefByKey(ManagedObjectReference container, String morefType, String key)
			throws InvalidPropertyFaultMsg, RuntimeFaultFaultMsg {
		if (container == null) {
			container = connection.getServiceContent().getRootFolder();
		}
		init();
		RetrieveResult rslts = containerViewByType(container, morefType, new RetrieveOptions());
		String token = null;
		if (rslts != null) {
			token = rslts.getToken();
			for (ObjectContent oc : rslts.getObjects()) {
				ManagedObjectReference mr = oc.getObj();
				if (mr.getValue().equals(key))
					return mr;
			}
		}
		return null;
	}

	public List<ManagedObjectReference> getMorByType(ManagedObjectReference folder, String morefType,
			RetrieveOptions retrieveOptions) throws InvalidPropertyFaultMsg, RuntimeFaultFaultMsg {
		init();
		RetrieveResult rslts = containerViewByType(folder, morefType, retrieveOptions);
		return toList(rslts);
	}

	private void init() {
		try {
			vimPort = connection.getVimPort();
			serviceContent = connection.getServiceContent();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public RetrieveResult containerViewByType(final ManagedObjectReference container, final String morefType,
			final RetrieveOptions retrieveOptions) throws RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {
		return this.containerViewByType(container, morefType, retrieveOptions, "name");
	}

	public RetrieveResult containerViewByType(final ManagedObjectReference container, final String morefType,
			final RetrieveOptions retrieveOptions, final String... morefProperties)
			throws RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {
		init();
		PropertyFilterSpec[] propertyFilterSpecs = propertyFilterSpecs(container, morefType, morefProperties);
		return containerViewByType(container, morefType, morefProperties, retrieveOptions, propertyFilterSpecs);
	}

	public PropertyFilterSpec[] propertyFilterSpecs(ManagedObjectReference container, String morefType,
			String... morefProperties) throws RuntimeFaultFaultMsg {
		init();
		ManagedObjectReference viewManager = serviceContent.getViewManager();
		ManagedObjectReference containerView = vimPort.createContainerView(viewManager, container,
				Arrays.asList(morefType), true);
		return new PropertyFilterSpec[] { new PropertyFilterSpecBuilder()
				.propSet(new PropertySpecBuilder().all(Boolean.FALSE).type(morefType).pathSet(morefProperties))
				.objectSet(new ObjectSpecBuilder().obj(containerView).skip(Boolean.TRUE).selectSet(
						new TraversalSpecBuilder().name("view").path("view").skip(false).type("ContainerView"))) };
	}

	public RetrieveResult containerViewByType(final ManagedObjectReference container, final String morefType,
			final String[] morefProperties, final RetrieveOptions retrieveOptions,
			final PropertyFilterSpec... propertyFilterSpecs) throws RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {
		init();

		RetrieveResult result = vimPort.retrievePropertiesEx(serviceContent.getPropertyCollector(),
				Arrays.asList(propertyFilterSpecs), retrieveOptions);
		if (propertyFilterSpecs != null && propertyFilterSpecs.length > 0)
			for (int i = 0; i < propertyFilterSpecs.length; i++) {
				List<ObjectSpec> objectSpecs = propertyFilterSpecs[i].getObjectSet();
				if (objectSpecs != null && objectSpecs.size() > 0) {
					for (ObjectSpec objectSpec : objectSpecs) {
						vimPort.destroyView(objectSpec.getObj());
					}
				}
			}
		return result;
	}

	public List<ManagedObjectReference> toList(RetrieveResult rslts)
			throws InvalidPropertyFaultMsg, RuntimeFaultFaultMsg {
		final List<ManagedObjectReference> morList = new ArrayList<ManagedObjectReference>();
		String token = null;
		token = populate(rslts, morList);
		while (token != null && !token.isEmpty()) {
			rslts = vimPort.continueRetrievePropertiesEx(serviceContent.getPropertyCollector(), token);
			token = populate(rslts, morList);
		}
		return morList;
	}

	public static String populate(final RetrieveResult rslts, final List<ManagedObjectReference> morList) {
		String token = null;
		if (rslts != null) {
			token = rslts.getToken();
			for (ObjectContent oc : rslts.getObjects()) {
				ManagedObjectReference mr = oc.getObj();
				morList.add(mr);
			}
		}
		return token;
	}

	public static String populateOC(final RetrieveResult rslts, final List<ObjectContent> listobjcontent) {
		String token = null;
		if (rslts != null) {
			token = rslts.getToken();
			listobjcontent.addAll(rslts.getObjects());
		}
		return token;
	}

	public Object getDynamicProperty(ManagedObjectReference mor, String propertyName) throws NoSuchMethodException,
			InvocationTargetException, IllegalAccessException, RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {
		init();
		ObjectContent[] objContent = getObjectProperties(mor, new String[] { propertyName });
		Object propertyValue = null;
		if (objContent != null) {
			List<DynamicProperty> listdp = objContent[0].getPropSet();
			if (listdp != null && listdp.size() > 0) {
				Object dynamicPropertyVal = listdp.get(0).getVal();
				String dynamicPropertyName = dynamicPropertyVal.getClass().getName();
				if (dynamicPropertyName.indexOf("ArrayOf") != -1) {
					String methodName = dynamicPropertyName.substring(
							dynamicPropertyName.indexOf("ArrayOf") + "ArrayOf".length(), dynamicPropertyName.length());
					if (methodExists(dynamicPropertyVal, "get" + methodName, null)) {
						methodName = "get" + methodName;
					} else {
						methodName = "get_" + methodName.toLowerCase();
					}
					Method getMorMethod = dynamicPropertyVal.getClass().getDeclaredMethod(methodName, (Class[]) null);
					propertyValue = getMorMethod.invoke(dynamicPropertyVal, (Object[]) null);
				} else if (dynamicPropertyVal.getClass().isArray()) {
					propertyValue = dynamicPropertyVal;
				} else {
					propertyValue = dynamicPropertyVal;
				}
			}
		}
		return propertyValue;
	}

	public ObjectContent[] getObjectProperties(ManagedObjectReference mobj, String[] properties)
			throws RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {
		if (mobj == null) {
			return null;
		}
		PropertyFilterSpec spec = new PropertyFilterSpec();
		spec.getPropSet().add(new PropertySpec());
		if ((properties == null || properties.length == 0)) {
			spec.getPropSet().get(0).setAll(Boolean.TRUE);
		} else {
			spec.getPropSet().get(0).setAll(Boolean.FALSE);
		}
		spec.getPropSet().get(0).setType(mobj.getType());
		spec.getPropSet().get(0).getPathSet().addAll(Arrays.asList(properties));
		spec.getObjectSet().add(new ObjectSpec());
		spec.getObjectSet().get(0).setObj(mobj);
		spec.getObjectSet().get(0).setSkip(Boolean.FALSE);
		List<PropertyFilterSpec> listpfs = new ArrayList<PropertyFilterSpec>(1);
		listpfs.add(spec);
		List<ObjectContent> listobjcont = retrievePropertiesAllObjects(listpfs);
		return listobjcont.toArray(new ObjectContent[listobjcont.size()]);
	}

	public List<ObjectContent> retrievePropertiesAllObjects(List<PropertyFilterSpec> listpfs)
			throws RuntimeFaultFaultMsg, InvalidPropertyFaultMsg {

		RetrieveOptions propObjectRetrieveOpts = new RetrieveOptions();

		List<ObjectContent> listobjcontent = new ArrayList<ObjectContent>();

		RetrieveResult rslts = vimPort.retrievePropertiesEx(connection.getServiceContent().getPropertyCollector(),
				listpfs, propObjectRetrieveOpts);
		if (rslts != null && rslts.getObjects() != null && !rslts.getObjects().isEmpty()) {
			listobjcontent.addAll(rslts.getObjects());
		}
		String token = null;
		if (rslts != null && rslts.getToken() != null) {
			token = rslts.getToken();
		}
		while (token != null && !token.isEmpty()) {
			rslts = vimPort.continueRetrievePropertiesEx(connection.getServiceContent().getPropertyCollector(), token);
			token = null;
			if (rslts != null) {
				token = rslts.getToken();
				if (rslts.getObjects() != null && !rslts.getObjects().isEmpty()) {
					listobjcontent.addAll(rslts.getObjects());
				}
			}
		}
		return listobjcontent;
	}

	@SuppressWarnings("rawtypes")
	public boolean methodExists(Object obj, String methodName, Class[] parameterTypes) throws NoSuchMethodException {
		boolean exists = false;
		Method method = obj.getClass().getMethod(methodName, parameterTypes);
		if (method != null) {
			exists = true;
		}
		return exists;
	}

}
