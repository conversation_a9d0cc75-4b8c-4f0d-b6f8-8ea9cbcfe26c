package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

;

@Slf4j
public class PlatResourcePoolService {

    public static final PlatResourcePoolService bean = new PlatResourcePoolService();

    public void fetchResourcePool(DescribeVmwareRequest request, JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = ConnectionUtils.getConnection(accessBean);
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        //获取资源池列表
        List<ManagedObjectReference> resourcepoolList = null;
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            resourcepoolList = morManager.getMorByType(rootRef, MORTypeEnum.ResourcePool.toString());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下资源池异常");
        }
        Map<String, String> resourcepoolMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(resourcepoolList)) {
            resourcepoolList.forEach(mor -> {
                TmdbDevops devops = new TmdbDevops();
                devops.setCloud_type(accessBean.getCloudType());
                devops.setDevops_name(VmwareUtils.getName(mor, morManager));
                devops.setDevops_value(mor.getValue());
                devops.setAccount_id(accessBean.getCmpId());
                devops.setDict_code(DevopsSide.DEVOPS_RESOURCEPOOL.value());
                devops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_RESOURCEPOOL.value(), mor.getValue()));
                devops.setInfo_json(JSONObject.toJSONString(mor));
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
                } catch (Exception e) {
                    log.error("获取资源池parent信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(parentMor)) {
                    if("Resources".equals(devops.getDevops_name())) {
                        resourcepoolMap.put(mor.getValue(), parentMor.getValue());
                    }else {
                        devopsList.add(devops);
                        String parentId = resourcepoolMap.get(parentMor.getValue())==null?parentMor.getValue():resourcepoolMap.get(parentMor.getValue());
                        TmdbDevopsLink link = new TmdbDevopsLink();
                        if (parentId.contains("datacenter-")) {
                            link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_DATA_CENTER.value(), parentId));
                        } else if (parentId.contains("domain-")) {
                            link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_CLUSTER.value(), parentId));
                        } else {
                            link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_RESOURCEPOOL.value(), parentId));
                        }
                        link.setDevops_id(devops.getBiz_id());
                        link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
                        links.add(link);
                    }
                }

                System.out.println(devops.getDevops_name()+"---"+parentMor.getValue());
                List<ManagedObjectReference> vmMors = null;
                try {
                    vmMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(mor, "vm");
                } catch (Exception e) {
                    log.error("获取资源池vm信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(parentMor) && ObjectUtil.isNotEmpty(vmMors)) {
                    ManagedObjectReference finalParentMor = parentMor;
                    if("Resources".equals(devops.getDevops_name())) {
                        vmMors.forEach(vmMor -> {
                            resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, finalParentMor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), vmMor.getValue()));
                        });
                    }else {
                        vmMors.forEach(vmMor -> {
                            resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_RESOURCEPOOL.value(), TmdbDevops.class, mor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), vmMor.getValue()));
                        });
                    }
                }
            });
        }
        //发送运维侧数据
        BaseUtils.sendMessage(devopsList, arguments);

        //发送关系数据
        BaseUtils.sendMessage(links, arguments);

        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }
}
