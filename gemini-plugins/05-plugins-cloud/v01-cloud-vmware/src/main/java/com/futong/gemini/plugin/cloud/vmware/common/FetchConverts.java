package com.futong.gemini.plugin.cloud.vmware.common;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.AlarmLevel;
import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import com.sun.media.jfxmedia.logging.Logger;
import com.vmware.vim25.AlarmInfo;
import com.vmware.vim25.AlarmState;
import com.vmware.vim25.Event;

import java.text.SimpleDateFormat;
import java.util.List;

public class FetchConverts {
    public static EventInfoBean toEvent(Event event, CloudAccessBean accessBean, MorManager morManager) {
        EventInfoBean bean = new EventInfoBean();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String occTime = sdf.format(event.getCreatedTime().toGregorianCalendar().getTime().getTime());

            bean.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), event.getKey() + "", "" + occTime));
            bean.setAccountId(BaseClient.auths.get().getCmpId());
            bean.setCloudType(CloudType.VM_VMWARE.value());

            bean.setOpenId(event.getKey() + "");
            bean.setOpenLevel("1");
            bean.setEventLevel(AlarmLevel.INFORMATION.toString().toLowerCase());
            bean.setDetail(event.getFullFormattedMessage());
            bean.setBeginTime(occTime);
            bean.setEndTime(occTime);
            bean.setEventType("system");
            bean.setJsonInfo(JSON.toJSONString(event));
            if (event.getVm() != null) {
                bean.setOpenName(morManager.getDynamicProperty(event.getVm().getVm(), "name").toString());
                bean.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                bean.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0",event.getVm().getVm().getValue()));
            } else if (event.getHost() != null) {
                bean.setOpenName(morManager.getDynamicProperty(event.getHost().getHost(), "name").toString());
                bean.setResourceType(ResourceType.CMDB_HOST_RES.value());
                bean.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), event.getHost().getHost().getValue()));
            } else if (event.getNet() != null) {
                bean.setOpenName(morManager.getDynamicProperty(event.getNet().getNetwork(), "name").toString());
                bean.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
                bean.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), event.getNet().getNetwork().getValue()));
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return bean;
    }

    public static AlarmInfoBean toAlarm(AlarmState state, CloudAccessBean accessBean, MorManager morManager) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        AlarmInfoBean alarm = new AlarmInfoBean();
        try {
            AlarmInfo info = (AlarmInfo) morManager.getDynamicProperty(state.getAlarm(), "info");
            alarm.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), state.getEntity().getValue(), info.getKey(), sdf.format(state.getTime().toGregorianCalendar().getTime())));
            alarm.setAccountId(BaseClient.auths.get().getCmpId());
            alarm.setCloudType(CloudType.VM_VMWARE.value());
            alarm.setOpenId(state.getEntity().getValue());
            alarm.setOpenName(morManager.getDynamicProperty(state.getEntity(),"name").toString());
            alarm.setOpenLevel(state.getOverallStatus().toString());
            alarm.setAlarmId(info.getKey());
            alarm.setAlarmName(info.getName());
            alarm.setDetail(info.getDescription());
            alarm.setClosedStatus(false);
            alarm.setCount(1);
            alarm.setFirstTime(sdf.format(state.getTime().toGregorianCalendar().getTime()));
            alarm.setCreateTime(sdf.format(state.getTime().toGregorianCalendar().getTime()));
            if (MORTypeEnum.VirtualMachine.toString().equalsIgnoreCase(state.getEntity().getType())) {
                alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0",state.getEntity().getValue()+""));
            } else if (MORTypeEnum.HostSystem.toString().equalsIgnoreCase(state.getEntity().getType())) {
                alarm.setResourceType(ResourceType.CMDB_HOST_RES.value());
                alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), state.getEntity().getValue()+""));
            } else if (MORTypeEnum.Network.toString().equalsIgnoreCase(state.getEntity().getType())) {
                alarm.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
                alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), state.getEntity().getValue()+""));
            }
            switch (state.getOverallStatus().toString().toLowerCase()) {
                case "red":
                    alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case "yellow":
                    alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case "gray":
                    alarm.setAlarmLevel(AlarmLevel.DEBUG.value());
                    break;
                case "green":
                    alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                    alarm.setClosedStatus(true);
                    break;
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return alarm;
    }

    public static void createInstanceEventJob(JSONObject arguments, String instanceId) {
        try {
            PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
            JSONObject req = new JSONObject();
            JSONObject instance = new JSONObject();
            instance.put("ids", new String[]{instanceId});
            instance.put("auth", arguments.getJSONObject("auth"));
            req.put("auth", arguments.getJSONObject("auth"));
            req.put("body", instance);
            req.put("action", ActionType.REFRESH_COMPUTE_INSTANCE.value());
            toJobInfo(plugin.getRealm(), plugin.getVersion(), req);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void toJobInfo(String realm, String version, JSONObject jobRequest) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(realm);
        jobInfo.setVersion(version);
        jobInfo.setCount(1);
        jobInfo.setRequest(jobRequest);
        jobInfo.setTriggerTime(System.currentTimeMillis() + 20000);
        SpringUtil.getBean(GourdProxy.class).createTempEventJob(jobInfo);
    }
}
