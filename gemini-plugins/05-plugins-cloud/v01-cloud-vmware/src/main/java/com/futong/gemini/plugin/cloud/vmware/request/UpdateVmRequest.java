package com.futong.gemini.plugin.cloud.vmware.request;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建虚拟机请求参数
 */
@Setter
@Getter
public class UpdateVmRequest implements Serializable {


    private String vmId;

    private String vmName;
    //虚拟机描述
    private String description;

    //cpu核数
    private Integer cpuSize;

    //内存大小 GB
    private Integer memSize;

    private JSONArray systemDisk;

    private JSONArray nics;

    private JSONArray dataDisk;

    private String serverIp;

}
