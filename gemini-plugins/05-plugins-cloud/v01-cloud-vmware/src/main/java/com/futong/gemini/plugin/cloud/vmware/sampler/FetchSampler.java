package com.futong.gemini.plugin.cloud.vmware.sampler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.service.*;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FetchSampler {

    public static BaseResponse fetchFolder(JSONObject arguments) {
        String message = "成功获取文件夹资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            PlatFolderService.bean.fetchFolder(request, arguments);
        }  catch (Exception e) {
            log.error("同步文件夹资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CREATE, e, "同步文件夹资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }
    public static BaseResponse fetchResourcepool(JSONObject arguments) {
        String message = "成功获取资源池资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            PlatResourcePoolService.bean.fetchResourcePool(request, arguments);
        }  catch (Exception e) {
            log.error("同步资源池资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CREATE, e, "同步资源池资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDatacenter(JSONObject arguments) {
        String message = "成功获取数据中心资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            PlatDatacenterService.bean.fetchDatacenter(request, arguments);
        }  catch (Exception e) {
            log.error("同步数据中心资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CREATE, e, "同步数据中心资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse fetchCluster(JSONObject arguments) {
        String message = "成功获取集群资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            PlatClusterService.bean.fetchCluster(request, arguments);
        }  catch (Exception e) {
            log.error("同步集群资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CREATE, e, "同步集群资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDVSwitch(JSONObject arguments) {
        String message = "成功获取分布式交换机资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "dvSwitch"://同步分布式交换机并存入
                        NetworkDVswitchService.bean.fetchDvSwitch(request, arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取分布式交换机列表，并拆分dataJob
                return new GourdJobResponse(NetworkDVswitchService.bean.splitVswitchDataJob(request,arguments));
            }
        }  catch (Exception e) {
            log.error("同步分布式交换机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步分布式交换机资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchSubnet(JSONObject arguments) {
        String message = "成功获取端口组资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "subnet"://同步子网(端口组)
                        NetworkSubnetService.bean.fetchSubnet(request, arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取子网（端口组）列表，并拆分dataJob
                return new GourdJobResponse(NetworkSubnetService.bean.splitSubnetDataJob(request,arguments),message);
            }
        }  catch (Exception e) {
            log.error("同步子网(端口组)资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步子网(端口组)资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDatastore(JSONObject arguments) {
        String message = "成功获取数据存储资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "storagePool"://同步数据存储
                        StorageDatastoreService.bean.fetchDatastore(request, arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取数据存储列表，并拆分dataJob
                return new GourdJobResponse(StorageDatastoreService.bean.splitDatastoreDataJob(request,arguments),message);
            }
        }  catch (Exception e) {
            log.error("同步数据存储资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步数据存储资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse fetchHost(JSONObject arguments) {
        String message = "成功获取物理机资源信息.";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "host"://同步物理机
                        ComputeHostService.bean.fetchHost(request, arguments);
                        break;
                    case "vSwitch"://同步虚拟交换机
                        NetworkVswitchService.bean.fetchVswitch(request, arguments);
                        break;
                    case "portgroup"://同步物理机portgroup信息
                        NetworkVswitchService.bean.fetchPortgroup(request, arguments);
                        break;
                    case "nic"://同步物理机网卡信息
                        NetworkVswitchService.bean.fetchNic(request, arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(ComputeHostService.bean.splitHostDataJob(request,arguments),message);
            }
        }  catch (Exception e) {
            log.error("同步云主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云主机资源数据失败");
        }
            return BaseResponse.SUCCESS.of(message);
        }

    public static BaseResponse fetchInstance(JSONObject arguments) {
        String message = "成功获取云主机资源信息.";
        try{
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                log.info("开始同步类型={}",request.getResourceType());
                switch (request.getResourceType()) {
                    case "vm"://同步云主机
                        ComputeInstanceService.bean.fetchInstance(request, arguments);
                        break;
                    case "snapshot"://获取快照
                        StorageSnapshotService.bean.fetchSnapshot(request, arguments);
                        break;
                    case "disk"://获取磁盘
                        StorageDiskService.bean.fetchDisk(request, arguments);
                        break;
                    case "nic"://获取网卡和IP信息
                        NetworkNicService.bean.fetchNic(request, arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                    }
                }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(ComputeInstanceService.bean.splitInstanceDataJob(request,arguments),message);
            }
        }  catch (Exception e) {
            log.error("同步云主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步云主机资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchEvent(JSONObject arguments) {
        try {
            String message = EventService.bean.fetchEvent();
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步事件数据失败");
        }
    }

    public static BaseResponse fetchAlarm(JSONObject arguments) {
        try {
            String message = AlarmService.bean.fetchAlarm();
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }
    }

    public static BaseResponse syncInstance(JSONObject arguments) {
        String message = "同步虚拟机成功";
        try {
            DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
            ComputeInstanceService.bean.fetchInstance(request, arguments);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步虚拟机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步虚拟机失败");
        }
    }
    public static BaseResponse queryInstanceTotal(JSONObject arguments) {
        String message = "同步虚拟机数量成功";
        try {
            JSONObject data = new JSONObject();
            int count = ComputeInstanceService.bean.queryInstanceTotal();
            data.put("count", count);
            return new BaseDataResponse<>(data);
        } catch (Exception e) {
            log.error("同步虚拟机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步虚拟机数量失败");
        }
    }

    public static BaseResponse queryHostTotal(JSONObject arguments) {
        String message = "同步主机数量成功";
        try {
            JSONObject data = ComputeInstanceService.bean.queryHostTotal();
            return new BaseDataResponse<>(data);
        } catch (Exception e) {
            log.error("同步主机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步主机数量失败");
        }
    }


}
