package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.VirtualMachineSnapshotInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

;

@Slf4j
public class StorageSnapshotService {

    public static final StorageSnapshotService bean = new StorageSnapshotService();

    // 获取快照信息
    public void fetchSnapshot(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbSnapshotRes> snaoshots = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(vmId -> {
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
            VirtualMachineSnapshotInfo snapshotInfo = null;
            try {
                snapshotInfo = (VirtualMachineSnapshotInfo) morManager.getDynamicProperty(vmMor, "snapshot");
            } catch (Exception e) {
                log.error("获取云主机snapshot信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机snapshot信息异常");
            }
            List<CmdbSnapshotRes> shots = VmwareUtils.bean.getSnapshot(morManager, vmMor, snapshotInfo, accessBean);
            if (ObjectUtil.isNotEmpty(shots)) {
                snaoshots.addAll(shots);
                associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), vmId, new CmdbInstanceRes()), shots.stream().collect(Collectors.toList())));
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(vmMor, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机parent信息异常{}", e);
                }
                if (parentMor.getValue().contains("domain-c")) {
                    ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), dcMor.getValue(), shots);
                    CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_CLUSTER.value(), parentMor.getValue(), shots);
                }
            }
        });

        //发送快照数据
        BaseUtils.sendMessage(snaoshots, arguments);

        //发送云主机与快照关系数据
        BaseUtils.sendMessage(associations, arguments);

        //南北新仓资源关系数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }
}
