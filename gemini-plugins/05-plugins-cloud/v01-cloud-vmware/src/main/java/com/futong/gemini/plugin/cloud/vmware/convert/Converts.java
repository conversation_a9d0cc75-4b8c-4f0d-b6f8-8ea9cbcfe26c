package com.futong.gemini.plugin.cloud.vmware.convert;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.futong.constant.dict.CloudType;
import com.futong.gemini.model.api.entity.BaseSearchApiModel;
import com.futong.gemini.model.api.model.BaseEsPageSortSearchRequest;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.BaseConstant;
import com.futong.gemini.plugin.cloud.vmware.request.PageSortSearchRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
public class Converts {
    public static BasePageSortSearchRequest toBasePageSortSearchRequest(PageSortSearchRequest request) {
        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
        if(ObjectUtil.isNotNull(request)){
            searchRequest.setCurrent(request.getCurrent()==null?1:request.getCurrent());
            searchRequest.setSize(request.getSize()==null?50:request.getSize());
            searchRequest.setSortField(request.getSortField()==null? BaseConstant.RES_ID:request.getSortField());
            searchRequest.setSort(request.getSort()==null?0:request.getSort());
        }else{
            searchRequest.setCurrent(1);
            searchRequest.setSize(50);
            searchRequest.setSortField(BaseConstant.RES_ID);
            searchRequest.setSort(0);
        }
        List<BaseSearchApiModel> searchList = new ArrayList<>();
        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
        searchApiModel.setValue(BaseClient.auths.get().getCmpId());
        searchApiModel.setSearchClassiy("0");
        searchList.add(searchApiModel);
        searchRequest.setSearchList(searchList);
        searchRequest.setSearchList(searchList);
        return searchRequest;
    }

    public static BaseEsPageSortSearchRequest toBaseEsPageSortSearchRequest() {
        BaseEsPageSortSearchRequest searchRequest = new BaseEsPageSortSearchRequest();
        searchRequest.setCloudType(CloudType.VM_VMWARE.value());
        searchRequest.setCurrentPage(1);
        searchRequest.setType("alarm");
        searchRequest.setEndTime(DateUtil.formatDateTime(new DateTime()));
        searchRequest.setStartTime(DateUtil.formatDateTime(new DateTime(new Date().getTime()-5*60*1000l)));
        searchRequest.setPageSize(100);
        return searchRequest;
    }
}

