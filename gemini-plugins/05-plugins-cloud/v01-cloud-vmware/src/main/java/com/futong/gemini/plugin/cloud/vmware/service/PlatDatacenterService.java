package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

;

@Slf4j
public class PlatDatacenterService {

    public static final PlatDatacenterService bean = new PlatDatacenterService();

    public void fetchDatacenter(DescribeVmwareRequest request, JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = ConnectionUtils.getConnection(accessBean);
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        //获取数据中心列表
        List<ManagedObjectReference> datacenterList = null;
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            datacenterList = morManager.getMorByType(rootRef, MORTypeEnum.Datacenter.toString());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下数据中心异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下数据中心异常");
        }
        if (ObjectUtil.isNotEmpty(datacenterList)) {
            datacenterList.forEach(mor -> {
                TmdbDevops devops = new TmdbDevops();
                devops.setCloud_type(accessBean.getCloudType());
                devops.setDevops_name(VmwareUtils.getName(mor, morManager));
                devops.setDevops_value(mor.getValue());
                devops.setAccount_id(accessBean.getCmpId());
                devops.setDict_code(DevopsSide.DEVOPS_DATA_CENTER.value());
                devops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_DATA_CENTER.value(), mor.getValue()));
                devops.setInfo_json(JSONObject.toJSONString(mor));
                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setDevops_id(devops.getBiz_id());
                link.setParent_devops_id(null);
                link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
                links.add(link);
                devopsList.add(devops);
            });
        }
        //发送运维侧数据
        BaseUtils.sendMessage(devopsList, arguments);

        BaseUtils.sendMessage(links, arguments);
    }
}
