package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.InstanceStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbOsRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OsType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.vmware.common.FetchConverts;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.*;
import com.futong.gemini.plugin.cloud.vmware.thread.CreateVmThread;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class ComputeInstanceService {

    public static final ComputeInstanceService bean = new ComputeInstanceService();

    public static BaseResponse createSnapshot(JSONObject arguments) {
        String message = "成功发起创建快照请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateSnapshotRequest request = BaseClient.bodys.get().toJavaObject(CreateSnapshotRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        if (ObjectUtil.isEmpty(request.getCi())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "请选择要创建快照的云主机.");
        }
        if (ObjectUtil.isEmpty(request.getModel().getString("name"))) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "快照名称不能为空.");
        }
        if(!"POWERED_ON".equals(request.getCi().getOpen_status())) {
            if (request.getModel().getBoolean("memory") || request.getModel().getBoolean("quiesce")) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "非运行状态下参数memorySnapshot,quiesce不能为true.");
            }
        }else {
            if (ObjectUtil.isAllNotEmpty(request.getModel().getBoolean("memory"), request.getModel().getBoolean("quiesce")) && request.getModel().getBoolean("memory").equals(request.getModel().getBoolean("quiesce"))) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "memorySnapshot,quiesce不能同时为true.");
            }
        }
        vmMor.setValue(request.getCi().getOpen_id());
        try {
            connection.getVimPort().createSnapshotTask(vmMor, request.getModel().getString("name"),
                    request.getModel().getString("description"), request.getModel().getBoolean("memory") == null ? false : request.getModel().getBoolean("memory"), request.getModel().getBoolean("quiesce") == null ? true : request.getModel().getBoolean("quiesce"));
        } catch (Exception e) {
            log.error("创建云主机快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建云主机快照息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    /**
     * 创建镜像
     * @param arguments
     * @return
     */
    public static BaseResponse createImage(JSONObject arguments) {
        String message = "成功发起创建镜像请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateImageRequest request = BaseClient.bodys.get().getJSONObject("cloud").toJavaObject(CreateImageRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        try {
            VmwareUtils.bean.createImage(connection, request);
        } catch (Exception e) {
            log.error("创建镜像异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建镜像异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse updateSnapshot(JSONObject arguments) {
        String message = "成功修改快照信息.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        UpdateSnapshotRequest request = BaseClient.bodys.get().toJavaObject(UpdateSnapshotRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType("VirtualMachineSnapshot");
        if (ObjectUtil.isEmpty(request.getCi().getOpen_id())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "请选择要修改的快照.");
        }
        if (ObjectUtil.isEmpty(request.getModel().getString("name"))) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "快照名称不能为空.");
        }
        vmMor.setValue(request.getCi().getOpen_id());
        try {
            connection.getVimPort().renameSnapshot(vmMor, request.getModel().getString("name"),
                    request.getModel().getString("description"));
        } catch (Exception e) {
            log.error("修改快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改快照息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse resumeSnapshot(JSONObject arguments) {
        String message = "成功发起恢复快照请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType("VirtualMachineSnapshot");
        String snapshotId = BaseClient.bodys.get().getJSONObject("ci").getString("openId");
        if (ObjectUtil.isEmpty(snapshotId)) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "请选择要恢复的快照.");
        }
        vmMor.setValue(snapshotId);
        try {
            connection.getVimPort().revertToSnapshotTask(vmMor, null, false);
        } catch (Exception e) {
            log.error("恢复快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "恢复快照息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteSnapshot(JSONObject arguments) {
        String message = "成功发起删除快照请求.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType("VirtualMachineSnapshot");
        String snapshotId = BaseClient.bodys.get().getJSONObject("ci").getString("openId");
        if (ObjectUtil.isEmpty(snapshotId)) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "请选择要删除的快照.");
        }
        vmMor.setValue(snapshotId);
        try {
            connection.getVimPort().removeSnapshotTask(vmMor, false, true);
        } catch (Exception e) {
            log.error("删除快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除快照息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    //web控制台url,需要前端根据服务拼接wss://ip:port
    public static BaseDataResponse webConsole(JSONObject arguments) {
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        if(instanceIds==null||instanceIds.size()<=0) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "虚拟机参数为空");
        }
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        vmMor.setValue(instanceIds.get(0));
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        VirtualMachineTicket machineTicket = null;
        try {
            machineTicket = connection.getVimPort().acquireTicket(vmMor, "webmks");
        } catch (Exception e) {
            log.error("获取云主机webmks信息异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机webmks信息异常");
        }
        String ticket = "wss://futong-zy-web-console/cmp-console/" + machineTicket.getHost() + "/ticket/" + machineTicket.getTicket();
        return new BaseDataResponse().withData(ticket);
    }

    //删除云主机
    public static BaseResponse deleteInstance(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            vmMor.setValue(instanceId);

            VirtualMachineSummary summary = null;
            try {
                summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
            } catch (Exception e) {
                log.error("获取云主机summary信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
            }
            if (ObjectUtil.isNull(summary))
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "云主机不存在.");
            if ("POWERED_ON".equals(summary.getRuntime().getPowerState().toString())) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "云主机处于运行状态，无法删除.");
            }
            try {
                connection.getVimPort().destroyTask(vmMor);
                FetchConverts.createInstanceEventJob(arguments,instanceId);
            } catch (Exception e) {
                log.error("关闭云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭云主机异常");
            }
        }

        return BaseResponse.SUCCESS.of(message);
    }

    //开启云主机
    public static BaseResponse startInstance(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        if (CollUtil.isEmpty(body.getJSONArray("cis"))) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的CI信息为空!");
        }
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            vmMor.setValue(instanceId);
            try {
                connection.getVimPort().powerOnVMTask(vmMor, null);
                FetchConverts.createInstanceEventJob(arguments,instanceId);
            } catch (Exception e) {
                log.error("开启云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "开启云主机异常");
            }
        }
        log.info("准备更新虚拟机状态={}",instanceIds);

        return BaseResponse.SUCCESS.of(message);
    }

    //重启云主机
    public static BaseResponse rebootInstance(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            vmMor.setValue(instanceId);
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            GuestInfo guestInfo = null;
            try {
                guestInfo = (GuestInfo) morManager.getDynamicProperty(vmMor, "guest");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机guest信息异常");
            }
            if (!"guestToolsRunning".equals(guestInfo.getToolsRunningStatus())) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "vmTools处于非运行状态，无法执行重启云主机操作.");
            }
            try {
                connection.getVimPort().rebootGuest(vmMor);
                FetchConverts.createInstanceEventJob(arguments,instanceId);
            } catch (Exception e) {
                log.error("重启云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重启云主机异常");
            }
        }

        return BaseResponse.SUCCESS.of(message);
    }

    //关闭云主机
    public static BaseResponse stopInstance(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference vmMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        vmMor.setType(MORTypeEnum.VirtualMachine.toString());
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            vmMor.setValue(instanceId);
            try {
                connection.getVimPort().powerOffVMTask(vmMor);
                FetchConverts.createInstanceEventJob(arguments,instanceId);
            } catch (Exception e) {
                log.error("关闭云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    //获取云主机数据
    public void fetchInstance(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbInstanceRes> instances = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(vmId -> {
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
            CmdbInstanceRes instance = new CmdbInstanceRes();
            instance.setCloud_type(accessBean.getCloudType());
            instance.setAccount_id(accessBean.getCmpId());

            instance.setOpen_id(vmId);
            VirtualMachineConfigInfo vmConfig = null;
            try {
                vmConfig = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
                if(vmConfig!=null) {
                    instance.setOpen_name(vmConfig.getName());
                }else {
                    log.info("获取云主机信息异常{}", vmId);
                }
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }

            /**
             * 虚拟机summary信息
             */
            VirtualMachineSummary summary = null;
            try {
                summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
            } catch (Exception e) {
                log.error("获取云主机summary信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
            }
            instance.setCpu_size(summary.getConfig().getNumCpu());
            instance.setMem_size(summary.getConfig().getMemorySizeMB());
            instance.setOpen_status(summary.getRuntime().getPowerState().toString());
            switch (summary.getRuntime().getPowerState().toString()) {
                case "POWERED_ON":
                    instance.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "POWERED_OFF":
                    instance.setStatus(InstanceStatus.STOPPED.value());
                    break;
                default:
                    instance.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
            }
            instance.setIs_template(summary.getConfig().isTemplate() ? 1 : 0);
            instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getIs_template()+"",vmId));
            instance.setDesc(summary.getConfig().getAnnotation());
            if (ObjectUtil.isNotNull(summary.getConfig())) {
                CmdbOsRes os = new CmdbOsRes();
                os.setFull_name(summary.getConfig().getGuestFullName());
                if (ObjectUtil.isNotNull(summary.getConfig().getGuestId()) && summary.getConfig().getGuestId().contains("windows")) {
                    os.setType(OsType.WINDOWS.getValue());
                } else {
                    os.setType(OsType.LINUX.getValue());
                }
                os.setOpen_id(summary.getConfig().getGuestId());
                os.setOpen_name(summary.getConfig().getGuestFullName());
                os.setCloud_type(accessBean.getCloudType());
                os.setAccount_id(accessBean.getCmpId());
                os.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_OS_RES.value(), summary.getConfig().getGuestId()));
                osList.add(os);
                associations.add(AssociationUtils.toAssociation(instance, os));
            }
            instances.add(instance);
            //获取云主机关联交换机信息
            List<ManagedObjectReference> networkMors = null;
            try {
                networkMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "network");
            } catch (Exception e) {
                log.error("获取云主机network信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(networkMors)) {
                networkMors.forEach(d -> {
                    CmdbSubnetRes subnet = new CmdbSubnetRes();
                    subnet.setCloud_type(accessBean.getCloudType());
                    subnet.setAccount_id(accessBean.getCmpId());
                    subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), d.getValue()));
                    subnet.setOpen_id(d.getValue());
                    associations.add(AssociationUtils.toAssociation(subnet, instance));
                });
            }
            VirtualMachineRuntimeInfo runtimeInfo = null;
            try {
                runtimeInfo = (VirtualMachineRuntimeInfo) morManager.getDynamicProperty(vmMor, "runtime");
            } catch (Exception e) {
                log.error("获取宿主机runtime信息异常{}", e);
            }
            ManagedObjectReference host = runtimeInfo.getHost();
            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager
                        .getDynamicProperty(host, "parent");
            } catch (Exception e) {
                log.error("获取宿主机runtime信息异常{}", e);
            }
            if (parentMor!=null&&parentMor.getValue().contains("domain-c")) {
                ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class,dcMor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class,parentMor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
            }
        });
        //发送云主机数据
        BaseUtils.sendMessage(instances, arguments);

        //发送操作系统数据
        BaseUtils.sendMessage(osList, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(resourceSets, arguments);

        //发送云主机关联关系
        BaseUtils.sendMessage(associations, arguments);


    }

    //获取云主机列表并拆分dataJob
    public List<JobInfo> splitInstanceDataJob(DescribeVmwareRequest request, JSONObject arguments) {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> vmList = null;
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            vmList = morManager.getMorByType(rootRef, MORTypeEnum.VirtualMachine.toString());
            /** 拆分宿主机dataJob */
            jobs = Stream.of(jobs,
                    JobUtils.bean.splitDataJob(request, vmList, new String[]{ResourceEnum.VM.getValue(), ResourceEnum.SNAPSHOT.getValue(), ResourceEnum.DISK.getValue(), ResourceEnum.NIC.getValue()},
                            arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下云主机异常");
        }
        log.info("vmware获取虚拟机任务数量:{}", jobs.size());
        return jobs;
    }

    public static BaseResponse createInstance(JSONObject arguments) {
        String message = "成功发起创建云主机请求.";
        log.info("vmware创建虚拟机接受参数={}", arguments.toString());
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        if (ObjectUtil.isEmpty(request.getVmName())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "云主机名称不能为空.");
        }
        if(BaseClient.bodys.get().containsKey("biz")) {
            request.setVmNum(1);
        }
        try {
            if (request.getVmNum() > 1) {
                String str = generateRandomString(4);
                String vmName = request.getVmName()+"-"+str+"-";
                for (int i = 1; i <= request.getVmNum(); i++) {
                    request.setVmName(vmName +i);
                    ManagedObjectReference cloneTask = VmwareUtils.bean.cloneVm(connection, request);
                    new CreateVmThread(cloneTask,accessBean,BaseClient.bodys.get(),arguments).start();
                }
            }else {
                if(BaseClient.bodys.get().containsKey("biz")) {
                    Integer resNum = BaseClient.bodys.get().getJSONObject("biz").getInteger("resNum");
                    if(resNum!=0) {
                        String vmName = request.getVmName() + "-" + resNum;
                        request.setVmName(vmName);
                    }
                }
                ManagedObjectReference cloneTask = VmwareUtils.bean.cloneVm(connection, request);
                new CreateVmThread(cloneTask,accessBean,BaseClient.bodys.get(),arguments).start();
            }
        } catch (Exception e) {
            log.error("创建云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建云主机息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse updateInstance(JSONObject arguments) {
        String message = "成功发起修改云主机请求.";
        log.info("vmware修改虚拟机接受参数={}", arguments.toString());
        CloudAccessBean accessBean = BaseClient.auths.get();
        UpdateVmRequest request = BaseClient.bodys.get().toJavaObject(UpdateVmRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        try {
            ManagedObjectReference cloneTask = VmwareUtils.bean.updateVm(connection, request);
        } catch (Exception e) {
            log.error("修改云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改云主机息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    /**
     * 刷新虚拟机状态
     * @param arguments
     * @return
     */
    public static BaseResponse refleshInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("触发更新虚拟机状态");
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        DescribeVmwareRequest request = BaseClient.bodys.get().toJavaObject(DescribeVmwareRequest.class);
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(vmId -> {
            List<Association> associations = new ArrayList<>();
            List<CmdbOsRes> osList = new ArrayList<>();
            List<TmdbResourceSet> resourceSets = new ArrayList<>();
            ManagedObjectReference vmMor = null;
            try {
                vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
                morManager.getDynamicProperty(vmMor, "name");
            }catch (Exception e) {
                vmMor = null;
                e.printStackTrace();
            }
            log.info("更新虚拟机状态vmMor={}",vmMor);
            if(vmMor!=null) {
                CmdbInstanceRes instance = new CmdbInstanceRes();
                instance.setCloud_type(accessBean.getCloudType());
                instance.setAccount_id(accessBean.getCmpId());

                instance.setOpen_id(vmId);
                VirtualMachineConfigInfo vmConfig = null;
                try {
                    vmConfig = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
                    if (vmConfig != null) {
                        instance.setOpen_name(vmConfig.getName());
                    } else {
                        log.info("获取云主机信息异常{}", vmId);
                    }
                } catch (Exception e) {
                    log.error("获取云主机config信息异常{}", e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
                }

                /**
                 * 虚拟机summary信息
                 */
                VirtualMachineSummary summary = null;
                try {
                    summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
                } catch (Exception e) {
                    log.error("获取云主机summary信息异常{}", e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
                }
                instance.setCpu_size(summary.getConfig().getNumCpu());
                instance.setMem_size(summary.getConfig().getMemorySizeMB());
                instance.setOpen_status(summary.getRuntime().getPowerState().toString());
                switch (summary.getRuntime().getPowerState().toString()) {
                    case "POWERED_ON":
                        instance.setStatus(InstanceStatus.RUNNING.value());
                        break;
                    case "POWERED_OFF":
                        instance.setStatus(InstanceStatus.STOPPED.value());
                        break;
                    default:
                        instance.setStatus(InstanceStatus.UNKNOWN.value());
                        break;
                }
                instance.setIs_template(summary.getConfig().isTemplate() ? 1 : 0);
                instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getIs_template() + "", vmId));
                instance.setDesc(summary.getConfig().getAnnotation());
                if (ObjectUtil.isNotNull(summary.getConfig())) {
                    CmdbOsRes os = new CmdbOsRes();
                    os.setFull_name(summary.getConfig().getGuestFullName());
                    if (ObjectUtil.isNotNull(summary.getConfig().getGuestId()) && summary.getConfig().getGuestId().contains("windows")) {
                        os.setType(OsType.WINDOWS.getValue());
                    } else {
                        os.setType(OsType.LINUX.getValue());
                    }
                    os.setOpen_id(summary.getConfig().getGuestId());
                    os.setOpen_name(summary.getConfig().getGuestFullName());
                    os.setCloud_type(accessBean.getCloudType());
                    os.setAccount_id(accessBean.getCmpId());
                    os.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_OS_RES.value(), summary.getConfig().getGuestId()));
                    osList.add(os);
                    associations.add(AssociationUtils.toAssociation(instance, os));
                }
                List<ManagedObjectReference> networkMors = null;
                try {
                    networkMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "network");
                } catch (Exception e) {
                    log.error("获取云主机network信息异常{}", e);
                }
                if (ObjectUtil.isNotEmpty(networkMors)) {
                    networkMors.forEach(d -> {
                        CmdbSubnetRes subnet = new CmdbSubnetRes();
                        subnet.setCloud_type(accessBean.getCloudType());
                        subnet.setAccount_id(accessBean.getCmpId());
                        subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), d.getValue()));
                        subnet.setOpen_id(d.getValue());
                        associations.add(AssociationUtils.toAssociation(subnet, instance));
                    });
                }
                VirtualMachineRuntimeInfo runtimeInfo = null;
                try {
                    runtimeInfo = (VirtualMachineRuntimeInfo) morManager.getDynamicProperty(vmMor, "runtime");
                } catch (Exception e) {
                    log.error("获取宿主机runtime信息异常{}", e);
                }
                ManagedObjectReference host = runtimeInfo.getHost();
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager
                            .getDynamicProperty(host, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机runtime信息异常{}", e);
                }
                if (parentMor!=null&&parentMor.getValue().contains("domain-c")) {
                    ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class,dcMor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
                    resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class,parentMor.getValue(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
                }
                log.info("推送虚拟机={}", JSONObject.toJSON(instance));
                List<CmdbInstanceRes> list = new ArrayList<>();
                list.add(instance);
                BaseCloudService.toRefreshMessageAndSend("update", list, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", osList, CmdbOsRes.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", resourceSets, TmdbResourceSet.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", associations, Association.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", list, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
            }else {
                CmdbInstanceRes ins = new CmdbInstanceRes();
                String resId= IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(),  "0",vmId);
                ins.setRes_id(resId);
                List<CmdbInstanceRes> list = new ArrayList<>();
                list.add(ins);
                List<String> deleteRelationTables = new ArrayList<>();
                deleteRelationTables.add("cmdb_disk_res");
                deleteRelationTables.add("cmdb_netcard_res");
                deleteRelationTables.add("cmdb_ip_res");
                BaseCloudService.toRefreshMessageAndSend("delete", list, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId(),deleteRelationTables);
            }
        });
        return BaseResponse.SUCCESS;
    }

    private static final String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random random = new Random();

    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(ALPHA_NUMERIC.length());
            sb.append(ALPHA_NUMERIC.charAt(index));
        }
        return sb.toString();
    }


    public int queryInstanceTotal() {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> vmList = null;
        try {
            vmList = morManager.getMorByType(rootRef, MORTypeEnum.VirtualMachine.toString());
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmList==null?0:vmList.size();
    }

    public JSONObject queryHostTotal() {
        JSONObject data = new JSONObject();
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> hostList = null;
        try {
            hostList = morManager.getMorByType(rootRef, MORTypeEnum.HostSystem.toString());
        }catch (Exception e) {
            e.printStackTrace();
        }
        if(hostList!=null) {
            int cpuCount = 0;
            int memoryCount = 0;
            int count = hostList.size();
            for (ManagedObjectReference hostMor : hostList) {
                HostListSummary summary = null;
                try {
                    summary = (HostListSummary) morManager.getDynamicProperty(hostMor, "summary");
                } catch (Exception e) {
                    log.error("获取宿主机summary信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(summary)) {
                    HostHardwareSummary hardware = summary.getHardware();
                    String model = "";
                    if (hardware.getModel() != null) {
                        model = hardware.getModel();
                    }
                    cpuCount += hardware.getNumCpuCores();
                    memoryCount += (int) (hardware.getMemorySize() / 1024 / 1024/1024);
                }
            }
            data.put("count",count);
            data.put("cpuCount",cpuCount);
            data.put("memoryCount",memoryCount);
        }
        return data;
    }
}
