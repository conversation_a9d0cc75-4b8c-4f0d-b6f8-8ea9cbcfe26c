package com.futong.gemini.plugin.cloud.vmware.enums;

import java.util.Arrays;
import java.util.List;

/**
 * vmware资源对象
 */
public enum MORTypeEnum {
    VCenter("vCenter", "VCenter"),
    Datacenter("datacenter", "数据中心"),
    Folder("folder", "文件夹"),
    ClusterComputeResource("clusterComputeResource", "群集"),
    ComputeResource("computeResource", "计算机资源"),
    ResourcePool("resourcePool", "资源池"),
    HostSystem("host", "主机"),
    VirtualMachine("vm", "虚拟机"),
    VirtualApp("virtualApp", "vApp"),
    Datastore("datastore", "存储器"),
    DistributedVirtualSwitch("distributedVirtualSwitch", "分布式交换机"),
    DistributedVirtualPortgroup("distributedVirtualPortgroup", "分布式端口组"),
    Template("template", "模板"),
    Network("network", "虚拟网络");
    private String label;
    private String key;

    MORTypeEnum(String key, String label) {
        this.label = label;
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return this.label;
    }

    public static List<MORTypeEnum> getList() {
        return Arrays.asList(values());
    }
}
