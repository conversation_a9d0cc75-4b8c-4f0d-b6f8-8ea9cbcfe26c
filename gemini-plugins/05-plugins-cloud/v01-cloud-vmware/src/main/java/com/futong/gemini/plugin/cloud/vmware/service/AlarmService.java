package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.model.BaseEsPageSortSearchRequest;
import com.futong.gemini.model.otc.common.model.OtcAetMessage;
import com.futong.gemini.model.otc.common.model.OtcPerfMessage;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.route.RouteFactory;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.common.FetchConverts;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.convert.Converts;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.vmware.vim25.AlarmState;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

@Slf4j
public class AlarmService {

    public static final AlarmService bean = new AlarmService();

    // 获取事件
    public String fetchAlarm() throws Exception{
        BaseEsPageSortSearchRequest searchRequest = Converts.toBaseEsPageSortSearchRequest();
        BaseDataResponse<List<AlarmInfoBean>> response = ApiFactory.Api.loader.queryList(searchRequest);
        List<AlarmInfoBean> hisList = new ArrayList<>();
        if (!ObjectUtil.isNull(response) && !ObjectUtil.isNull(response.getData()) && !ObjectUtil.isEmpty(response.getData())) {
            hisList = response.getData();
        }
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        morManager.setConnection(connection);
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        List<AlarmState> as = (List<AlarmState>) morManager.getDynamicProperty(rootRef,"triggeredAlarmState");
        List<AlarmInfoBean> nowList = as.stream()
                .map(t -> FetchConverts.toAlarm(t,accessBean,morManager)).collect(Collectors.toList());
        List<AlarmInfoBean> beans = closeAlarm(hisList,nowList);
        toAetMessageAndSend(beans, "alarm");
        String message = StrUtil.format("本次获取告警信息条数：{}", beans.size());
        return message;
    }

    public static <T> OtcAetMessage<T> toAetMessageAndSend(List<T> res, String type) {
        OtcAetMessage<T> message = new OtcAetMessage<>();
        message.setType(type);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routeAetMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    public static <T> OtcPerfMessage<T> toPerfMessageAndSend(List<T> res, String source) {
        OtcPerfMessage<T> message = new OtcPerfMessage<>();
        message.setSource(source);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routePrefMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }

    /**
     * 转换告警状态
     * @param hisList
     * @param nowList
     * @return
     */
    public static List<AlarmInfoBean> closeAlarm(List<AlarmInfoBean> hisList, List<AlarmInfoBean> nowList) {

        Map<String,AlarmInfoBean> hisMap = hisList.stream().collect(Collectors.toMap(AlarmInfoBean::getId, t -> t));
        Map<String,AlarmInfoBean> nowMap = nowList.stream().collect(Collectors.toMap(AlarmInfoBean::getId, t -> t));
        List<AlarmInfoBean> list = new ArrayList<>();
        for (String key : hisMap.keySet()) {
            if (!nowMap.containsKey(key)) {
                AlarmInfoBean alarmInfoBean = hisMap.get(key);
                alarmInfoBean.setClosedStatus(true);
                list.add(alarmInfoBean);
            }
        }
        for (String key : nowMap.keySet()) {
                list.add(nowMap.get(key));
        }
        return list;

    }
}
