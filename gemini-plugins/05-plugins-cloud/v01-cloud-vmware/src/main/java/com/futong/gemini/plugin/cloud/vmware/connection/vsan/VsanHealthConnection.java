package com.futong.gemini.plugin.cloud.vmware.connection.vsan;

import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.vmware.connection.Connection;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.vmware.vim25.AboutInfo;
import com.vmware.vsan.sdk.ManagedObjectReference;
import com.vmware.vsan.sdk.VsanhealthPortType;
import com.vmware.vsan.sdk.VsanhealthService;

import javax.xml.ws.BindingProvider;
import javax.xml.ws.handler.MessageContext;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class VsanHealthConnection extends VcConnection {
	
	public VsanHealthConnection(CloudAccessBean config){
		super(config);
	}

	private static final String VSAN_VC_HEALTH_SERVICEINSTANCETYPE = "VsanVcClusterHealthSystem";
	private static final String VSAN_VC_HEALTH_SERVICEINSTANCEVALUE = "vsan-cluster-health-system";
	private static final String VSAN_HOST_HEALTH_SERVICEINSTANCETYPE = "HostVsanHealthSystem";
	private static final String VSAN_HOST_HEALTH_SERVICEINSTANCEVALUE = "ha-vsan-health-system";
	private static final String VSAN_VC_DISK_MGMT_SERVICEINSTANCETYPE = "VimClusterVsanVcDiskManagementSystem";
	private static final String VSAN_VC_DISK_MGMT_SERVICEINSTANCEVALUE = "vsan-disk-management-system";
	private static final String VSAN_VC_STRETCHED_CLUSTER_SERVICEINSTANCETYPE = "VimClusterVsanVcStretchedClusterSystem";
	private static final String VSAN_VC_STRETCHED_CLUSTER_SERVICEINSTANCEVALUE = "vsan-stretched-cluster-system";
	private static final String VSAN_VC_CLUSTER_CONFIG_SERVICEINSTANCETYPE = "VsanVcClusterConfigSystem";
	private static final String VSAN_VC_CLUSTER_CONFIG_SERVICEINSTANCEVALUE = "vsan-cluster-config-system";
	private static final String VSAN_PERF_MANAGER_SERVICEINSTANCETYPE = "VsanPerformanceManager";
	private static final String VSAN_PERF_MANAGER_SERVICEINSTANCEVALUE = "vsan-performance-manager";
	private static final String VSAN_UPGRADE_SYS_EX_SERVICEINSTANCETYPE = "VsanUpgradeSystemEx";
	private static final String VSAN_UPGRADE_SYS_EX_SERVICEINSTANCEVALUE = "vsan-upgrade-systemex";
	private static final String VSAN_SPACE_REPORT_SERVICEINSTANCETYPE = "VsanSpaceReportSystem";
	private static final String VSAN_SPACE_REPORT_SERVICEINSTANCEVALUE = "vsan-cluster-space-report-system";
	private static final String VSAN_OBJECT_SYSTEM_SERVICEINSTANCETYPE = "VsanObjectSystem";
	private static final String VSAN_OBJECT_SYSTEM_SERVICEINSTANCEVALUE = "vsan-object-system";
	private static Map<String, String> vsanMoTypeValueMap = new HashMap<String, String>();
	static {
		vsanMoTypeValueMap.put(VSAN_VC_HEALTH_SERVICEINSTANCETYPE, VSAN_VC_HEALTH_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_HOST_HEALTH_SERVICEINSTANCETYPE, VSAN_HOST_HEALTH_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_VC_DISK_MGMT_SERVICEINSTANCETYPE, VSAN_VC_DISK_MGMT_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_VC_STRETCHED_CLUSTER_SERVICEINSTANCETYPE,
				VSAN_VC_STRETCHED_CLUSTER_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_VC_CLUSTER_CONFIG_SERVICEINSTANCETYPE, VSAN_VC_CLUSTER_CONFIG_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_PERF_MANAGER_SERVICEINSTANCETYPE, VSAN_PERF_MANAGER_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_UPGRADE_SYS_EX_SERVICEINSTANCETYPE, VSAN_UPGRADE_SYS_EX_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_SPACE_REPORT_SERVICEINSTANCETYPE, VSAN_SPACE_REPORT_SERVICEINSTANCEVALUE);
		vsanMoTypeValueMap.put(VSAN_OBJECT_SYSTEM_SERVICEINSTANCETYPE, VSAN_OBJECT_SYSTEM_SERVICEINSTANCEVALUE);
	}
	private ManagedObjectReference vsanVcHealthSvcInstRef;
	private ManagedObjectReference vsanHostHealthSvcInstRef;
	private ManagedObjectReference vsanVcDiskMgrSvcInstRef;
	private ManagedObjectReference vsanStretchedClusterSvcInstRef;
	private ManagedObjectReference vsanVcClusterConfigSvcInstRef;
	private ManagedObjectReference vsanPerfMgrSvcInstRef;
	private ManagedObjectReference vsanSpaceReportSvcInstRef;
	private ManagedObjectReference vsanObjSystemSvcInstRef;
	private ManagedObjectReference vsanUpgradeSystemExSvcInstRef;
	private URL vsanhealthurl;
	private VsanhealthService vsanHealthService = new VsanhealthService();
	private VsanhealthPortType vsanHealthPort = vsanHealthService.getVsanhealthPort();

	public void _connectToVsanHealth() {
		try {

			Map vimServiceCtxHeaders = super.getHeaders();
			Map VsanHealthHeaders = new HashMap();
			VsanHealthHeaders.put("Cookie", vimServiceCtxHeaders.get("Set-Cookie"));
			AboutInfo aboutInfo =getServiceContent().getAbout();
			if (aboutInfo.getApiType().equals("HostAgent")) {
				setHostVsanHealthURL();
			} else {
				setVcVsanHealthURL();
			}
			Map<String, Object> vsanCtxt = ((BindingProvider) vsanHealthPort).getRequestContext();
			vsanCtxt.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, vsanhealthurl.toString());
			vsanCtxt.put(BindingProvider.SESSION_MAINTAIN_PROPERTY, true);
			vsanCtxt.put(MessageContext.HTTP_REQUEST_HEADERS, VsanHealthHeaders);
		} catch (Exception e) {
			e.printStackTrace();
		}
	
	}
	@Override
	   public Connection connect() {
	      if (!isConnected()) {
	         super.connect();
	         try {
	            _connectToVsanHealth();
	         } catch (Exception e) {
	            e.printStackTrace();
	         }
	      }
	      return this;
	   }

	private URL setVcVsanHealthURL() {
		try {
			this.vsanhealthurl = new URL(getUrl().toString().replace("/sdk", "/vsanHealth"));
		} catch (MalformedURLException e) {
			e.printStackTrace();
		}
		return vsanhealthurl;
	}

	private URL setHostVsanHealthURL() {
		try {
			this.vsanhealthurl = new URL(getUrl().toString().replace("/sdk", "/vsan"));
		} catch (MalformedURLException e) {
			e.printStackTrace();
		}
		return vsanhealthurl;
	}

	public VsanhealthPortType getVsanHealthPort() {
		return vsanHealthPort;
	}

	public VsanhealthService getVsanHealthService() {
		return vsanHealthService;
	}

	public ManagedObjectReference getVsanVcHealthServiceInstanceReference() {
		if (vsanVcHealthSvcInstRef == null) {
			vsanVcHealthSvcInstRef = createVsanMangedObjectReference(VSAN_VC_HEALTH_SERVICEINSTANCETYPE);
		}
		return vsanVcHealthSvcInstRef;
	}

	private ManagedObjectReference createVsanMangedObjectReference(String type) {
		String value = vsanMoTypeValueMap.get(type);
		if (value == null) {
			return null;
		}
		ManagedObjectReference ref = new ManagedObjectReference();
		ref.setType(type);
		ref.setValue(value);
		return ref;
	}

	public ManagedObjectReference getVsanPerfMgrServiceInstanceReference() {
		if (vsanPerfMgrSvcInstRef == null) {
			vsanPerfMgrSvcInstRef = createVsanMangedObjectReference(VSAN_PERF_MANAGER_SERVICEINSTANCETYPE);
		}
		return vsanPerfMgrSvcInstRef;
	}
	 public ManagedObjectReference getVsanVcClusterConfigServiceInstanceReference() {
	      if (vsanVcClusterConfigSvcInstRef == null) {
	         vsanVcClusterConfigSvcInstRef =
	            createVsanMangedObjectReference(VSAN_VC_CLUSTER_CONFIG_SERVICEINSTANCETYPE);
	      }
	      return vsanVcClusterConfigSvcInstRef;
	   }
	 
	 public ManagedObjectReference getVsanSpaceReportSvcInstRef() {
	      if (vsanSpaceReportSvcInstRef == null) {
	    	  vsanSpaceReportSvcInstRef =
	            createVsanMangedObjectReference(VSAN_SPACE_REPORT_SERVICEINSTANCETYPE);
	      }
	      return vsanSpaceReportSvcInstRef;
	   }
	 
	 public ManagedObjectReference getVsanHostHealthSvcInstRef() {
	      if (vsanHostHealthSvcInstRef == null) {
	    	  vsanHostHealthSvcInstRef =
	            createVsanMangedObjectReference(VSAN_HOST_HEALTH_SERVICEINSTANCETYPE);
	      }
	      return vsanHostHealthSvcInstRef;
	   }
	 
	 public ManagedObjectReference getVsanVcDiskMgrSvcInstRef() {
	      if (vsanVcDiskMgrSvcInstRef == null) {
	    	  vsanVcDiskMgrSvcInstRef =
	            createVsanMangedObjectReference(VSAN_VC_DISK_MGMT_SERVICEINSTANCETYPE);
	      }
	      return vsanVcDiskMgrSvcInstRef;
	   }
}
