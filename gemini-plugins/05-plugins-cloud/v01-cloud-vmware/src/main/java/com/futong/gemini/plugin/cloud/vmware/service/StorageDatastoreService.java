package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.DatastoreStatusEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbStoragePoolRes;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.DatastoreSummary;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.VirtualMachineSummary;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class StorageDatastoreService {

    public static final StorageDatastoreService bean = new StorageDatastoreService();

    //获取数据存储信息
    public void fetchDatastore(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbStoragePoolRes> storagePoolList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(datastoreId -> {
            ManagedObjectReference datastoreMor = VmwareUtils.bean.getMor(MORTypeEnum.Datastore.toString(), datastoreId);
            CmdbStoragePoolRes storage = new CmdbStoragePoolRes();
            storage.setCloud_type(accessBean.getCloudType());
            storage.setAccount_id(accessBean.getCmpId());
            DatastoreSummary summary = null;
            try {
                summary = (DatastoreSummary) morManager.getDynamicProperty(datastoreMor, "summary");
            } catch (Exception e) {
                log.error("获取数据存储summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                if (summary.isAccessible()) {
                    storage.setStatus(DatastoreStatusEnum.ACTIVE.getKey());
                    storage.setTotal_size(Float.valueOf(String.format("%.2f", summary.getCapacity() / 1024 / 1024 / 1024.0)));
                    storage.setUsed_size(Float.valueOf(String.format("%.2f", (summary.getCapacity() - summary.getFreeSpace()) / 1024 / 1024 / 1024.0)));
                    if (summary.getUncommitted() != null)
                        storage.setAllocation_size(Float.valueOf(String.format("%.2f", summary.getUncommitted() / 1024 / 1024 / 1024.0)));
                    else
                        storage.setAllocation_size(0f);
                    storage.setType(summary.getType());
                } else {
                    storage.setStatus(DatastoreStatusEnum.INACTIVE.getKey());
                }
            }
            storage.setProvision_size(Float.valueOf(String.format("%.2f",(summary.getCapacity()-summary.getFreeSpace()+(summary.getUncommitted()==null?0:summary.getUncommitted()))/1024/1024/1024.0)));
            storage.setOpen_id(datastoreId);
            storage.setOpen_name(VmwareUtils.getName(datastoreMor, morManager));
            storage.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), datastoreId));
            storagePoolList.add(storage);

            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(datastoreMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (!parentMor.getValue().contains("datacenter-")) {
                parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
            }
            resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class, parentMor.getValue(), ResourceType.CMDB_STORAGE_POOL_RES.value(), datastoreMor.getValue()));

            //获取数据存储关联云主机信息
            List<ManagedObjectReference> vmMors = null;
            try {
                vmMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(datastoreMor, "vm");
            } catch (Exception e) {
                log.error("获取数据存储vm信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(vmMors)) {
                vmMors.forEach(d -> {
                    morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
                    VirtualMachineSummary vmsummary = null;
                    ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), d.getValue());
                    try {
                        vmsummary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
                    } catch (Exception e) {
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
                    }
                    CmdbInstanceRes instance = new CmdbInstanceRes();
                    instance.setCloud_type(accessBean.getCloudType());
                    instance.setAccount_id(accessBean.getCmpId());
                    instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), vmsummary.getConfig().isTemplate()?"1":"0",d.getValue()));
                    instance.setOpen_id(d.getValue());
                    associations.add(AssociationUtils.toAssociation(storage, instance));
                });
            }
        });
        //发送数据存储数据
        BaseUtils.sendMessage(storagePoolList, arguments);

        //发送云主机与磁盘关系数据
        BaseUtils.sendMessage(associations, arguments);
        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }


    //获取数据存储列表并拆分dataJob
    public List<JobInfo> splitDatastoreDataJob(DescribeVmwareRequest request, JSONObject arguments) {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> datastoreList = null;
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            datastoreList = morManager.getMorByType(rootRef, MORTypeEnum.Datastore.toString());
            /** 拆分宿主机dataJob */
            jobs = Stream.of(jobs,
                    JobUtils.bean.splitDataJob(request, datastoreList, new String[]{ResourceEnum.STORAGE_POOL.getValue()},
                            arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下数据存储异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下数据存储异常");
        }
        return jobs;
    }
}
