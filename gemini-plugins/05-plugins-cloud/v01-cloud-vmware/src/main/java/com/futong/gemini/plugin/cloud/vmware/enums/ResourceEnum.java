package com.futong.gemini.plugin.cloud.vmware.enums;

public enum ResourceEnum {

    /**
     * 宿主机
     */
    HOST("host"),

    // 云主机
    VM("vm"),

    DISK("disk"),

    /**
     * 快照
     */
    SNAPSHOT("snapshot"),
    /**
     * 网卡
     */
    NIC("nic"),

    /**
     * 网络
     */
    SUBNET("subnet"),

    PROTGROUP("portgroup"),


    /**
     *虚拟交换机
     */
    VSWITCH("vSwitch"),
    /**
     * Distributed Virtual Switch
     */
    DVSWITCH("dvSwitch"),

    IP("ip"),

    /**
     * 存储池
     */

    STORAGE_POOL("storagePool"),

    RESOURCEPOOL("resourcePool"),
    /**
     * 存储
     */
    STORAGE("storage"),

    //数据中心
    DATACENTER("datacenter"),

    //文件夹
    FOLDER("folder"),

    /** 集群 */
    CLUSTER("cluster");


    private String value;

    private ResourceEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
