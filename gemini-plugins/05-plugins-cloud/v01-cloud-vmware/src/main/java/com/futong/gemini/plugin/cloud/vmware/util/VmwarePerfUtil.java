package com.futong.gemini.plugin.cloud.vmware.util;

import com.vmware.vim25.PerfMetricId;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

public class VmwarePerfUtil {
    public static final VmwarePerfUtil bean = new VmwarePerfUtil();
    public  List<PerfMetricId>  getHostPerfMetricId(Boolean hasInstance,String version) {
        List<PerfMetricId> perfMetricIdList = new ArrayList<>();
        PerfMetricId perfMetricId = null;
        int[] meteriIds =null;
        if ((version.startsWith("7")&&!version.startsWith("7.0"))||version.startsWith("8")) {
            if(hasInstance){
                meteriIds = new int[] { 137,138,155, 156,185, 186, 187, 188, 189, 190, 191};
            }else {
                meteriIds = new int[] { 2,24};
            }
        }else{
            if(hasInstance){
                meteriIds = new int[] {  148, 149, 173, 174,178, 179, 180, 181, 182, 183, 184};
            }else {
                meteriIds = new int[] { 2,24};
            }
        }
        for (int i = 0; i < meteriIds.length; i++) {
            perfMetricId = new PerfMetricId();
            perfMetricId.setCounterId(meteriIds[i]);
            perfMetricId.setInstance("1");
            perfMetricIdList.add(perfMetricId);
        }
        return perfMetricIdList;
    }
    public  List<PerfMetricId>  getVmPerfMetricId(Boolean hasInstance,String version) {
        List<PerfMetricId> perfMetricIdList = new ArrayList<>();
        PerfMetricId perfMetricId = null;
        int[] meteriIds =null;
        if ((version.startsWith("7")&&!version.startsWith("7.0"))||version.startsWith("8")) {
            if(hasInstance){
                meteriIds = new int[] {  137,138,155, 156,185, 186, 187, 188, 189, 190, 191};
            }else {
                meteriIds = new int[] { 2,24};
            }
        }else{
            if(hasInstance){
                meteriIds = new int[] {  148, 149, 173, 174,178, 179, 180, 181, 182, 183, 184};
            }else {
                meteriIds = new int[] { 2,24};
            }
        }
        for (int i = 0; i < meteriIds.length; i++) {
            perfMetricId = new PerfMetricId();
            perfMetricId.setCounterId(meteriIds[i]);
            perfMetricId.setInstance("1");
            perfMetricIdList.add(perfMetricId);
        }
        return perfMetricIdList;
    }
    @Data
    public static class KpiInfo{
        private Integer id;
        private String value;
        public KpiInfo(Integer id, String value) {
            this.id = id;
            this.value = value;
        }
    }
    public List<KpiInfo>  getperfKpi(String version) {
        List<KpiInfo> kpiList = new ArrayList<>();
        if((version.startsWith("7")&&!version.startsWith("7.0"))||version.startsWith("8")){
            kpiList.add(new KpiInfo(2, "cpu.usage.average"));
            kpiList.add(new KpiInfo(24, "mem.usage.average"));
            kpiList.add(new KpiInfo(137, "disk.read.average"));
            kpiList.add(new KpiInfo(138, "disk.write.average"));
            kpiList.add(new KpiInfo(155, "net.received.average"));
            kpiList.add(new KpiInfo(156, "net.transmitted.average"));
            kpiList.add(new KpiInfo(180, "virtualDisk.read.average"));
            kpiList.add(new KpiInfo(181, "virtualDisk.write.average"));
            kpiList.add(new KpiInfo(185, "datastore.numberReadAveraged.average"));
            kpiList.add(new KpiInfo(186, "datastore.numberWriteAveraged.average"));
            kpiList.add(new KpiInfo(187, "datastore.read.average"));
            kpiList.add(new KpiInfo(188, "datastore.write.average"));
            kpiList.add(new KpiInfo(189, "datastore.totalReadLatency.average"));
            kpiList.add(new KpiInfo(190, "datastore.totalWriteLatency.average"));
            kpiList.add(new KpiInfo(191, "datastore.maxTotalLatency.latest"));
        }else{
            kpiList.add(new KpiInfo(2, "cpu.usage.average"));
            kpiList.add(new KpiInfo(24, "mem.usage.average"));
            kpiList.add(new KpiInfo(130, "disk.read.average"));
            kpiList.add(new KpiInfo(131, "disk.write.average"));
            kpiList.add(new KpiInfo(148, "net.received.average"));
            kpiList.add(new KpiInfo(149, "net.transmitted.average"));
            kpiList.add(new KpiInfo(173, "virtualDisk.read.average"));
            kpiList.add(new KpiInfo(174, "virtualDisk.write.average"));
            kpiList.add(new KpiInfo(178, "datastore.numberReadAveraged.average"));
            kpiList.add(new KpiInfo(179, "datastore.numberWriteAveraged.average"));
            kpiList.add(new KpiInfo(180, "datastore.read.average"));
            kpiList.add(new KpiInfo(181, "datastore.write.average"));
            kpiList.add(new KpiInfo(182, "datastore.totalReadLatency.average"));
            kpiList.add(new KpiInfo(183, "datastore.totalWriteLatency.average"));
            kpiList.add(new KpiInfo(184, "datastore.maxTotalLatency.latest"));
        }
        return kpiList;
    }
}
