package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.vmware.vim25.HostPortGroup;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

@Slf4j
public class PlatClusterService {

    public static final PlatClusterService bean = new PlatClusterService();

    public void fetchCluster(DescribeVmwareRequest request, JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = ConnectionUtils.getConnection(accessBean);
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        //获取集群列表
        List<ManagedObjectReference> clusterList = null;
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            clusterList = morManager.getMorByType(rootRef, MORTypeEnum.ClusterComputeResource.toString());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下集群异常");
        }
        if (ObjectUtil.isNotEmpty(clusterList)) {
            clusterList.forEach(mor -> {
                List<ManagedObjectReference> clusterHost = getClusterHost(mor);
                List<ManagedObjectReference> datastores = getDatastoreByHost(clusterHost);
                List<ManagedObjectReference> networks = getNetworkByHost(clusterHost);
                TmdbDevops devops = new TmdbDevops();
                devops.setCloud_type(accessBean.getCloudType());
                devops.setDevops_name(VmwareUtils.getName(mor, morManager));
                devops.setDevops_value(mor.getValue());
                devops.setAccount_id(accessBean.getCmpId());
                devops.setDict_code(DevopsSide.DEVOPS_CLUSTER.value());
                devops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_CLUSTER.value(), mor.getValue()));
                devops.setInfo_json(JSONObject.toJSONString(mor));
                devopsList.add(devops);
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
                } catch (Exception e) {
                    log.error("获取集群parent信息异常{}", e);
                }
                if (ObjectUtil.isNotEmpty(datastores)) {
                    datastores.forEach(mm -> {
                        resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, mor.getValue(), ResourceType.CMDB_STORAGE_POOL_RES.value(), mm.getValue()));
                    });
                }
                if (ObjectUtil.isNotNull(networks)) {
                    networks.forEach(mm -> {
                        resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, mor.getValue(), ResourceType.CMDB_SUBNET_RES.value(), mm.getValue()));
                    });
                }
                if (ObjectUtil.isNotNull(parentMor)) {
                    if (!parentMor.getValue().contains("datacenter-")) {
                        parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    }
                    TmdbDevopsLink link = new TmdbDevopsLink();
                    link.setDevops_id(devops.getBiz_id());
                    link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_DATA_CENTER.value(), parentMor.getValue()));
                    link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
                    links.add(link);
                }
            });
        }
        //发送运维侧数据
        BaseUtils.sendMessage(devopsList, arguments);

        //发送关系数据
        BaseUtils.sendMessage(links, arguments);

        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }

    public List<ManagedObjectReference> getClusterHost(ManagedObjectReference mor) {

        List<ManagedObjectReference> result = new ArrayList<>();
        try {
            MorManager morManager = new MorManager();
            morManager.setConnection(ConnectionUtils.getConnection(BaseClient.auths.get()));
            ManagedObjectReference hostMor = null;
            List<ManagedObjectReference> hostList = morManager.getMorByType(mor, MORTypeEnum.HostSystem.toString());
            if (ObjectUtil.isNotEmpty(hostList)) {
                hostList.forEach(host -> {
                    result.add(host);
                });
            }
       } catch (Exception e) {
            log.error("获取集群主机异常{}", e);
        }
        return result;
    }

    public static List<ManagedObjectReference> getDatastoreByHost(List<ManagedObjectReference> clusterHost) {
        //通过主机获取存储
        List<ManagedObjectReference> result = new ArrayList<>();
        try {
            clusterHost.forEach(host -> {
                try {
                    MorManager morManager = new MorManager();
                    morManager.setConnection(ConnectionUtils.getConnection(BaseClient.auths.get()));
                    List<ManagedObjectReference> datastoreMors = null;
                    try {
                        datastoreMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(host, "datastore");
                        result.addAll(datastoreMors);
                    } catch (Exception e) {
                        log.error("获取宿主机datastore信息异常{}", e);
                    }
                }catch (Exception e) {
                    log.error("获取宿主机datastore信息异常{}", e);
                }
            });
        }catch (Exception e) {
            log.error("获取宿主机datastore信息异常{}", e);
        }
        return result;
    }

    public static List<ManagedObjectReference> getNetworkByHost(List<ManagedObjectReference> clusterHost) {
        //通过主机获取portgroup
        MorManager morManager = new MorManager();
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        morManager.setConnection(connection);
        List<ManagedObjectReference> result = new ArrayList<>();
        try {
            clusterHost.forEach(host -> {
                try {
                    List<HostPortGroup> portgroups = null;
                    try {
                        portgroups = (List<HostPortGroup>) morManager.getDynamicProperty(host, "config.network.portgroup");
                        if (ObjectUtil.isNotEmpty(portgroups)) {
                            portgroups.forEach(portgroup -> {
                                ManagedObjectReference mr = new ManagedObjectReference();
                                mr.setValue(portgroup.getSpec().getName());
                                result.add(mr);
                            });
                        }
                    } catch (Exception e) {
                        log.error("获取宿主机端口组信息异常{}", e);
                    }
                }catch (Exception e) {
                    log.error("获取宿主机端口组信息异常{}", e);
                }
            });
        }catch (Exception e) {
            log.error("获取宿主机端口组信息异常{}", e);
        }
        return result;
    }
}
