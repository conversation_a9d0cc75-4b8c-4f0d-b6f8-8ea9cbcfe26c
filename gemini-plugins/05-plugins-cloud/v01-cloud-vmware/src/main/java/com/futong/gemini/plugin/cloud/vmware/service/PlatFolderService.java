package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

;

@Slf4j
public class PlatFolderService {

    public static final PlatFolderService bean = new PlatFolderService();

    public void fetchFolder(DescribeVmwareRequest request, JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = ConnectionUtils.getConnection(accessBean);
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        //获取文件夹列表
        List<ManagedObjectReference> folderList = null;
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            folderList = morManager.getMorByType(rootRef, MORTypeEnum.Folder.toString());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下文件夹异常");
        }
        if (ObjectUtil.isNotEmpty(folderList)) {
            folderList.forEach(mor -> {
                TmdbDevops devops = new TmdbDevops();
                devops.setCloud_type(accessBean.getCloudType());
                devops.setDevops_name(VmwareUtils.getName(mor, morManager));
                devops.setDevops_value(mor.getValue());
                devops.setAccount_id(accessBean.getCmpId());
                if(mor.getValue().contains("group-v")) {
                    devops.setDict_code(DevopsSide.DEVOPS_VM_FOLDER.value());
                }else {
                    devops.setDict_code(DevopsSide.DEVOPS_HOST_FOLDER.value());
                }
                devops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_VM_FOLDER.value(), mor.getValue()));
                devops.setInfo_json(JSONObject.toJSONString(mor));
                devopsList.add(devops);
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
                } catch (Exception e) {
                    log.error("获取文件夹parent信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(parentMor)) {
                   /* if (!parentMor.getValue().contains("datacenter-")) {
                        parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    }*/
                    TmdbDevopsLink link = new TmdbDevopsLink();
                    link.setDevops_id(devops.getBiz_id());
                    if (parentMor.getValue().contains("datacenter-")) {
                        link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_DATA_CENTER.value(), parentMor.getValue()));
                    } else if(parentMor.getValue().contains("group-v")){
                        link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_VM_FOLDER.value(), parentMor.getValue()));
                    } else {
                        link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), DevopsSide.DEVOPS_HOST_FOLDER.value(), parentMor.getValue()));
                    }
                    link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
                    links.add(link);
                }
            });
        }
        //发送运维侧数据
        BaseUtils.sendMessage(devopsList, arguments);

        //发送关系数据
        BaseUtils.sendMessage(links, arguments);
    }
}
