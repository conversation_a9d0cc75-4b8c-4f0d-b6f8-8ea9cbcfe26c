package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbVswitchRes;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.DVSConfigInfo;
import com.vmware.vim25.DVSSummary;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class NetworkDVswitchService {

    public static final NetworkDVswitchService bean = new NetworkDVswitchService();

    // 获取分布式交换机
    public void fetchDvSwitch(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbVswitchRes> vswitchList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(dvswitchId -> {
            ManagedObjectReference dvswitchMor = VmwareUtils.bean.getMor(MORTypeEnum.DistributedVirtualSwitch.toString(), dvswitchId);
            CmdbVswitchRes vswitch = new CmdbVswitchRes();
            vswitch.setCloud_type(accessBean.getCloudType());
            vswitch.setAccount_id(accessBean.getCmpId());
            vswitch.setOpen_id(dvswitchId);
            vswitch.setOpen_name(VmwareUtils.getName(dvswitchMor, morManager));
            vswitch.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), dvswitchId));
            vswitch.setType(ResourceEnum.DVSWITCH.getValue());
            DVSSummary summary = null;
            try {
                summary = (DVSSummary) morManager.getDynamicProperty(dvswitchMor, "summary");
            } catch (Exception e) {
                log.error("获取dvswitch summary信息异常{}", e);
            }
            DVSConfigInfo dvsConfig = null;
            try {
                dvsConfig = (DVSConfigInfo) morManager.getDynamicProperty(dvswitchMor, "config");
            } catch (Exception e) {
                log.error("获取dvswitch summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                vswitch.setDesc(summary.getDescription());
                if (ObjectUtil.isNotEmpty(summary.getHost())) {
                    summary.getHost().forEach(hostMor -> {
                        associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(accessBean, hostMor.getValue(), new CmdbHostRes()), vswitch));
                    });
                }
            }
            List<ManagedObjectReference> portgroups = null;
            try {
                portgroups = (List<ManagedObjectReference>) morManager.getDynamicProperty(dvswitchMor, "portgroup");
            } catch (Exception e) {
                log.error("获取dvswitch portgroup信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(portgroups)) {
                portgroups.forEach(portgroupMor -> {
                    associations.add(AssociationUtils.toAssociation(vswitch, CiResCloudUtils.toCiResCloud(accessBean, portgroupMor.getValue(), new CmdbSubnetRes())));
                });
            }

            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(dvswitchMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (!parentMor.getValue().contains("datacenter-")) {
                parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
            }
            log.info("保存数据中心和交换机关系={},{}", parentMor.getValue(),  dvswitchMor.getValue());
            resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class,parentMor.getValue(), ResourceType.CMDB_VSWITCH_RES.value(), dvswitchMor.getValue()));

            vswitchList.add(vswitch);
        });
        //发送dvswitch数据
        BaseUtils.sendMessage(vswitchList, arguments);

        //发送dvswitch与宿主机关联关系
        BaseUtils.sendMessage(associations, arguments);

        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }

    //获取分布式交换机列表并拆分dataJob
    public List<JobInfo> splitVswitchDataJob(DescribeVmwareRequest request, JSONObject arguments) {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> networkList = null;
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            networkList = morManager.getMorByType(rootRef, MORTypeEnum.DistributedVirtualSwitch.toString());
            /** 拆分分布式交换机dataJob */
            jobs = Stream.of(jobs,
                    JobUtils.bean.splitDataJob(request, networkList, new String[]{ResourceEnum.DVSWITCH.getValue()},
                            arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下分布式交换机异常{}", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e, "获取" + connection.getHost() + "下分布式交换机异常");
        }
        return jobs;
    }
}
