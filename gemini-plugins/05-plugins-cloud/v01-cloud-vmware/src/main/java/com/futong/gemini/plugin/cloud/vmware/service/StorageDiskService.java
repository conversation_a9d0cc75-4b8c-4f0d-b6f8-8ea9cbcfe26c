package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.VirtualMachineConfigInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

;

@Slf4j
public class StorageDiskService {

    public static final StorageDiskService bean = new StorageDiskService();

    /**
     * 获取并初始化必要对象：磁盘列表、关联关系列表及云连接管理器。
     * 根据配置信息提取磁盘详情，并将其添加到磁盘列表。
     * 构建云主机与磁盘之间的关联关系，并存储到关联关系列表。
     * 分别发送磁盘数据和云主机与磁盘的关系数据。
     */
    public void fetchDisk(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbDiskRes> disks = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(vmId -> {
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
            VirtualMachineConfigInfo vmConfig = null;
            try {
                vmConfig = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            List<CmdbDiskRes> diskRes = VmwareUtils.bean.getCmdbDisk(associations, morManager, vmMor, vmConfig, accessBean);
            if (ObjectUtil.isNotEmpty(diskRes)) {
                disks.addAll(diskRes);
                associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), vmId, new CmdbInstanceRes()), diskRes.stream().collect(Collectors.toList())));
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(vmMor, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机parent信息异常{}", e);
                }
                if (parentMor.getValue().contains("domain-c")) {
                    ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), dcMor.getValue(), diskRes);
                    CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_CLUSTER.value(), parentMor.getValue(), diskRes);
                }
            }
        });
        //发送磁盘数据
        BaseUtils.sendMessage(disks, arguments);

        //发送云主机与磁盘关系数据
        BaseUtils.sendMessage(associations, arguments);
        //南北新仓资源关系数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }
}
