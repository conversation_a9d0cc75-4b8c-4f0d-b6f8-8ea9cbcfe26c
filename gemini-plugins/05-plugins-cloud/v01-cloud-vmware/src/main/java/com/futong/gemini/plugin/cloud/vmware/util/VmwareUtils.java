package com.futong.gemini.plugin.cloud.vmware.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.DiskStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.DiskCategory;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.DiskProvisionType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.IpType;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.NicTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.*;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.google.common.graph.Network;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class VmwareUtils {
    public static final VmwareUtils bean = new VmwareUtils();

    //根据类型和ID封装ManagedObjectReference
    public ManagedObjectReference getMor(String type, String id) {
        ManagedObjectReference mor = new ManagedObjectReference();
        mor.setType(type);
        mor.setValue(id);
        return mor;
    }

    public static ManagedObjectReference getParentManagedObjectReference(ManagedObjectReference mor, MorManager morManager) {
        try {
            String parentname = (String) morManager.getDynamicProperty(mor, "name");
            if (parentname != null && parentname.equals("vm")) {
                ManagedObjectReference mor1 = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
                if (mor1.getValue().contains("datacenter-")) {
                    mor = mor1;
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return mor;
    }

    public static String getDatastoreKey(ManagedObjectReference mor, MorManager morManager) {
        String key = "";
        try {
            DatastoreSummary summary = (DatastoreSummary) morManager.getDynamicProperty(mor, "summary");
            String url = summary.getUrl();
            String[] split = url.split("\\/");
            key = url.split("\\/")[split.length-1];
        } catch (Exception e) {
            log.error("", e);
        }
        return key;
    }

    //获取数据中心
    public ManagedObjectReference getDcManagedObjectReference(ManagedObjectReference mor, MorManager morManager) {
        try {
            mor = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
            if (mor.getValue().contains("datacenter-")) {
                return mor;
            } else {
                mor = getDcManagedObjectReference(mor, morManager);
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (RuntimeFaultFaultMsg e) {
            e.printStackTrace();
        } catch (InvalidPropertyFaultMsg e) {
            e.printStackTrace();
        }
        return mor;
    }

    public List<ManagedObjectReference> getMorsbyType(ManagedObjectReference mor, MorManager morManager,String type) {
        List<ManagedObjectReference> result = new ArrayList<>();
        try {
            result = morManager.getMorByType(mor, type);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    //获取名称
    public static String getName(ManagedObjectReference mor, MorManager morManager) {
        String name = "";
        try {
            name = (String) morManager.getDynamicProperty(mor, "name");
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (RuntimeFaultFaultMsg e) {
            e.printStackTrace();
        } catch (InvalidPropertyFaultMsg e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return name;
    }

    public void getCmdbNetcard(List<TmdbResourceSet> resourceSets, List<CmdbNetcardRes> nics, List<CmdbIpRes> ips, List<Association> associations, MorManager morManager, ManagedObjectReference vmMor, VirtualMachineConfigInfo vmConfig, CloudAccessBean accessBean) {
        List<CmdbNetcardRes> cards = new ArrayList<>();
        if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
            List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
            for (VirtualDevice d : devices) {
                CmdbNetcardRes nic = new CmdbNetcardRes();
                nic.setCategory("vm");
                nic.setCloud_type(accessBean.getCloudType());
                nic.setAccount_id(accessBean.getCmpId());
                String portgroudID = "";
                if (d instanceof VirtualE1000) {
                    VirtualE1000 ve = (VirtualE1000) d;
                    nic.setMac_address(ve.getMacAddress());
                    if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                .getBacking();
                        portgroudID = v.getPort().getPortgroupKey();
                    } else if (ve
                            .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                        VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                .getBacking();
                        portgroudID = vnet.getNetwork().getValue();
                    }
                    nic.setModule(NicTypeEnum.E1000.getKey());
                    nic.setOpen_name(ve.getDeviceInfo().getLabel());
                    nic.setOpen_id(ve.getKey() + "");
                    if (ve.getConnectable() != null
                            && ve.getConnectable().isConnected()) {
                        nic.setStatus("true");
                    } else {
                        nic.setStatus("false");
                    }
                } else if (d instanceof VirtualE1000E) {
                    VirtualE1000E ve = (VirtualE1000E) d;
                    nic.setMac_address(ve.getMacAddress());
                    if (ve.getConnectable().isConnected()) {
                        nic.setStatus("true");
                    } else {
                        nic.setStatus("false");
                    }
                    if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                .getBacking();
                        portgroudID = v.getPort().getPortgroupKey();
                    } else if (ve
                            .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                        VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                .getBacking();
                        portgroudID = vnet.getNetwork().getValue();
                    }
                    nic.setModule(NicTypeEnum.E1000E.getKey());
                    nic.setOpen_name(ve.getDeviceInfo().getLabel());
                    nic.setOpen_id(ve.getKey() + "");
                } else if (d instanceof VirtualVmxnet3) {
                    VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                    nic.setMac_address(ve.getMacAddress());
                    if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                .getBacking();
                        portgroudID = v.getPort().getPortgroupKey();
                    } else if (ve
                            .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                        VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                .getBacking();
                        portgroudID = vnet.getNetwork().getValue();
                    }
                    if (ve.getConnectable().isConnected()) {
                        nic.setStatus("true");
                    } else {
                        nic.setStatus("false");
                    }
                    nic.setModule(NicTypeEnum.VMXNET3.getKey());
                    nic.setOpen_name(ve.getDeviceInfo().getLabel());
                    nic.setOpen_id(ve.getKey() + "");
                } else if (d instanceof VirtualVmxnet2) {
                    VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                    nic.setMac_address(ve.getMacAddress());
                    if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                .getBacking();
                        portgroudID = v.getPort().getPortgroupKey();
                    } else if (ve
                            .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                        VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                .getBacking();
                        portgroudID = vnet.getNetwork().getValue();
                    }
                    if (ve.getConnectable().isConnected()) {
                        nic.setStatus("true");
                    } else {
                        nic.setStatus("false");
                    }
                    nic.setModule(NicTypeEnum.VMXNET2.getKey());
                    nic.setOpen_name(ve.getDeviceInfo().getLabel());
                    nic.setOpen_id(ve.getKey() + "");
                } else {
                    continue;
                }
                nic.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), vmMor.getValue(), nic.getOpen_id()));
                //网卡与子网（端口组）关系数据
                System.out.println("portgroudID=="+portgroudID);
                ManagedObjectReference mor = new ManagedObjectReference();
                mor.setValue(portgroudID);
                mor.setType(MORTypeEnum.Network.toString());
                String name = getName(mor, morManager);
                System.out.println("name--"+name);
                associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), name, new CmdbSubnetRes()), nic));
                cards.add(nic);
            }
        }
        if (ObjectUtil.isNotEmpty(cards)) {
            //获取网卡的IP信息、网卡和Ip的关系数据
            List<CmdbIpRes> ipList = getGuest(cards, associations, morManager, vmMor, accessBean);
            //云主机和网卡的关系数据
            associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), vmMor.getValue(), new CmdbInstanceRes()), cards.stream().collect(Collectors.toList())));
            nics.addAll(cards);
            if (ObjectUtil.isNotEmpty(ipList))
                ips.addAll(ipList);
            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(vmMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (parentMor.getValue().contains("domain-c")) {
                ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), dcMor.getValue(), cards);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_CLUSTER.value(), parentMor.getValue(), cards);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), dcMor.getValue(), ipList);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_CLUSTER.value(), parentMor.getValue(), ipList);
            }
        }
    }

    public void getCmdbHostNetcard(List<TmdbResourceSet> resourceSets, List<CmdbPhysicalNetcardRes> nics, List<Association> associations, MorManager morManager, ManagedObjectReference hostMor, List<PhysicalNic> nicList, CloudAccessBean accessBean) {
        List<CmdbPhysicalNetcardRes> cards = new ArrayList<>();
        if (CollUtil.isNotEmpty(nicList)) {
            for (PhysicalNic pn : nicList) {
                CmdbPhysicalNetcardRes nic = new CmdbPhysicalNetcardRes();
                nic.setCloud_type(accessBean.getCloudType());
                nic.setAccount_id(accessBean.getCmpId());
                if (pn.getLinkSpeed() != null) {
                    nic.setModule(String.valueOf(pn.getLinkSpeed().getSpeedMb()));
                } else {
                    nic.setModule("-1");
                }
                nic.setType("全双工");
                nic.setCategory("host");
                nic.setOpen_name(pn.getDevice());
                nic.setMac_address(pn.getMac());
                nic.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), hostMor.getValue(), pn.getDevice()));
                cards.add(nic);
            }
        }
        if (ObjectUtil.isNotEmpty(cards)) {
            //主机和网卡的关系数据
            associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), hostMor.getValue(), new CmdbHostRes()), cards.stream().collect(Collectors.toList())));
            nics.addAll(cards);
            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (parentMor.getValue().contains("domain-c")) {
                ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), dcMor.getValue(), cards);
                CiResCloudUtils.toTmdbResourceSet(resourceSets, accessBean, DevopsSide.DEVOPS_CLUSTER.value(), parentMor.getValue(), cards);
            }
        }
    }

    public List<CmdbIpRes> getGuest(List<CmdbNetcardRes> nics, List<Association> associations, MorManager morManager, ManagedObjectReference vmMor, CloudAccessBean accessBean) {
        List<CmdbIpRes> ips = new ArrayList<>();
        GuestInfo guest = null;
        try {
            guest = (GuestInfo) morManager.getDynamicProperty(vmMor, "guest");
        } catch (Exception e) {
            log.error("获取云主机guest信息异常{}", e);
        }
        if (ObjectUtil.isNotNull(guest)) {
            List<GuestNicInfo> guestNicInfos = guest.getNet();
            if (ObjectUtil.isNotEmpty(guestNicInfos)) {
                guestNicInfos.forEach(g -> {
                    for (CmdbNetcardRes nic : nics) {
                        if (g.getMacAddress().equals(nic.getMac_address())) {
                            if (ObjectUtil.isNotEmpty(g.getIpConfig())) {
                                List<NetIpConfigInfoIpAddress> ipAddress = g.getIpConfig().getIpAddress();
                                ipAddress.forEach(i -> {
                                    if (!i.getIpAddress().contains(":")) {
                                        nic.setIpv4_address(i.getIpAddress());
                                    } else {
                                        nic.setIpv6_address(i.getIpAddress());
                                    }
                                });
                                CmdbIpRes ip = new CmdbIpRes();
                                ip.setMac(g.getMacAddress());
                                ip.setAddress(nic.getIpv4_address());
                                ip.setAccount_id(accessBean.getCmpId());
                                ip.setCloud_type(accessBean.getCloudType());
                                ip.setType(IpType.PRIVATE_IP.getValue());
                                ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_IP_RES.value(), nic.getMac_address()));
                                ips.add(ip);
                                //网卡和IP的关系数据
                                associations.add(AssociationUtils.toAssociation(nic, ip));
                                //云主机和IP的关系数据
                                associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(accessBean, vmMor.getValue(), new CmdbInstanceRes()), ip));
                            }
                            break;
                        }
                    }
                });
            }
        }
        return ips;
    }

    //获取磁盘数据
    public List<CmdbDiskRes> getCmdbDisk(List<Association> associations, MorManager morManager, ManagedObjectReference vmMor, VirtualMachineConfigInfo vmConfig, CloudAccessBean accessBean) {
        List<CmdbDiskRes> disks = new ArrayList<>();
        if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
            List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
            for (VirtualDevice d : devices) {
                /**
                 * 磁盘信息
                 */
                ManagedObjectReference datastoreMor = new ManagedObjectReference();
                String datastoreId = null;
                datastoreMor.setType(MORTypeEnum.Datastore.toString());
                if ((d instanceof VirtualDisk)) {
                    CmdbDiskRes diskInfo = new CmdbDiskRes();
                    VirtualDisk vd = (VirtualDisk) d;
                    if (vd != null && vd.getBacking() != null && vd
                            .getBacking() instanceof VirtualDiskRawDiskMappingVer1BackingInfo) {
                        VirtualDiskRawDiskMappingVer1BackingInfo backing = (VirtualDiskRawDiskMappingVer1BackingInfo) vd
                                .getBacking();
                        diskInfo.setPath(backing.getFileName());
                        diskInfo.setOpen_name(d.getDeviceInfo().getLabel());
                        diskInfo.setSize(Float.valueOf(String.format("%.2f", vd.getCapacityInKB() / 1024 / 1024.0)));
                        diskInfo.setProvision_type(DiskProvisionType.THIN.getValue());
                        // 获取存储
                        datastoreId = backing.getDatastore().getValue();
                        diskInfo.setOpen_id(backing.getUuid());
                        diskInfo.setType(backing.getDiskMode());
                    } else if (vd != null && vd.getBacking() != null && vd
                            .getBacking() instanceof VirtualDiskFlatVer2BackingInfo) {
                        VirtualDiskFlatVer2BackingInfo backing = (VirtualDiskFlatVer2BackingInfo) vd
                                .getBacking();
                        diskInfo.setPath(backing.getFileName());
                        diskInfo.setOpen_name(vd.getDeviceInfo().getLabel());
                        // 获取存储
                        datastoreId = backing.getDatastore().getValue();
                        if (backing.isThinProvisioned()) {
                            diskInfo.setProvision_type(DiskProvisionType.THIN.getValue());
                        } else if (!backing.isThinProvisioned()
                                && backing.isEagerlyScrub() != null
                                && backing.isEagerlyScrub()) {
                            diskInfo.setProvision_type(DiskProvisionType.THICK_EAGER.getValue());
                        } else {
                            diskInfo.setProvision_type(DiskProvisionType.THICK_LAZY.getValue());
                        }
                        diskInfo.setOpen_id(backing.getUuid());
                        diskInfo.setType(backing.getDiskMode());
                    } else if (vd != null && vd.getBacking() != null && vd
                            .getBacking() instanceof VirtualDiskSparseVer2BackingInfo) {
                        VirtualDiskSparseVer2BackingInfo backing = (VirtualDiskSparseVer2BackingInfo) vd
                                .getBacking();
                        diskInfo.setPath(backing.getFileName());
                        diskInfo.setOpen_name(vd.getDeviceInfo().getLabel());
                        diskInfo.setProvision_type(DiskProvisionType.THIN.getValue());
                        // 获取存储
                        datastoreId = backing.getDatastore().getValue();
                        diskInfo.setOpen_id(backing.getUuid());
                        diskInfo.setType(backing.getDiskMode());
                    }
                    diskInfo.setStatus(DiskStatus.IN_USE.value());
                    diskInfo.setCategory(DiskCategory.SYSTEM.getValue());
                    diskInfo.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), vmMor.getValue(), diskInfo.getPath()));
                    diskInfo.setCloud_type(accessBean.getCloudType());
                    diskInfo.setAccount_id(accessBean.getCmpId());
                    diskInfo.setSize(Float.valueOf(String.format("%.2f", vd.getCapacityInKB() / 1024 / 1024.0)));
                    if (ObjectUtil.isNotNull(datastoreId)) {
                        datastoreMor.setValue(datastoreId);
                        diskInfo.setStore_name(getName(datastoreMor, morManager));
                        associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), datastoreId, new CmdbStoragePoolRes()), diskInfo));
                    }
                    disks.add(diskInfo);
                }
            }
        }
        return disks;
    }

    //获取快照信息
    public List<CmdbSnapshotRes> getSnapshot(MorManager morManager, ManagedObjectReference vmMor, VirtualMachineSnapshotInfo snapshotInfo, CloudAccessBean accessBean) {
        List<CmdbSnapshotRes> shots = new ArrayList<>();
        if (ObjectUtil.isNotNull(snapshotInfo)) {
            getChildSnapshot(shots, snapshotInfo.getRootSnapshotList(), null, accessBean);
            VirtualMachineFileLayoutEx ex = null;
            try {
                ex = (VirtualMachineFileLayoutEx) morManager
                        .getDynamicProperty(vmMor, "layoutEx");
            } catch (Exception e) {
                log.error("获取云主机layoutEx信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(ex)) {
                List<VirtualMachineFileLayoutExFileInfo> fileInfos = ex.getFile();
                List<VirtualMachineFileLayoutExSnapshotLayout> layouts = ex.getSnapshot();
                if (ObjectUtil.isNotEmpty(layouts)) {
                    long size = 0l;
                    long disk_used = 0l;
                    long total_used = 0l;
                    long snapshot_data_size = 0l;
                    for (VirtualMachineFileLayoutExSnapshotLayout layout : layouts) {
                        size = 0l;
                        disk_used = 0l;
                        snapshot_data_size = 0l;
                        for (VirtualMachineFileLayoutExFileInfo exFileInfo : fileInfos) {
                            if (layout.getDataKey() == exFileInfo.getKey()
                                    && "snapshotData".equals(exFileInfo.getType())) {
                                size += exFileInfo.getSize();
                                snapshot_data_size += exFileInfo.getSize();
                            }
                            if ("snapshotMemory".equals(exFileInfo.getType())
                                    && layout.getMemoryKey() == exFileInfo.getKey()) {
                                size += exFileInfo.getSize();
                            }
                            for (VirtualMachineFileLayoutExDiskLayout diskLayout : layout
                                    .getDisk()) {
                                for (VirtualMachineFileLayoutExDiskUnit exDiskLayout : diskLayout
                                        .getChain()) {
                                    for (Integer it : exDiskLayout.getFileKey()) {
                                        if (exFileInfo.getKey() == it) {
                                            disk_used += exFileInfo.getSize();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        if (ObjectUtil.isNotEmpty(shots)) {
                            disk_used = disk_used + snapshot_data_size;
                            for (CmdbSnapshotRes snapshot : shots) {
                                if (layout.getKey().getValue().equals(snapshot.getOpen_id())) {
                                    snapshot.setSize(Float.valueOf(String.format("%.2f", size / 1024 / 1024 / 1024.0)));
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        return shots;
    }

    private void getChildSnapshot(List<CmdbSnapshotRes> shots, List<VirtualMachineSnapshotTree> infos,
                                  CmdbSnapshotRes parentSnapshot, CloudAccessBean accessBean) {
        CmdbSnapshotRes shot = null;
        if (infos != null && infos.size() > 0) {
            for (VirtualMachineSnapshotTree tree : infos) {
                shot = new CmdbSnapshotRes();
                shot.setDesc(tree.getDescription());
                shot.setCreate_time(tree.getCreateTime().toGregorianCalendar().getTime().getTime());
                shot.setType(ResourceType.CMDB_INSTANCE_RES.value());
                shot.setRunning_status(tree.getState().name());
                shot.setOpen_id(tree.getSnapshot().getValue());
                shot.setOpen_name(tree.getName());
                shot.setAccount_id(accessBean.getCmpId());
                shot.setCloud_type(accessBean.getCloudType());
                if (ObjectUtil.isNotNull(parentSnapshot)) {
                    shot.setP_snaps_id(parentSnapshot.getRes_id());
                    shot.setP_snaps_name(parentSnapshot.getOpen_name());
                }
                shot.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), tree.getSnapshot().getValue()));
                shots.add(shot);
                getChildSnapshot(shots, tree.getChildSnapshotList(), shot, accessBean);
            }
        }
    }


    /**
     * 获取vCenter当前时间
     *
     * @param connection
     * @return
     * @throws ParseException
     */
    public XMLGregorianCalendar getXMLGregorianCalendar(VcConnection connection) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("mm");
        SimpleDateFormat dateTimeformater = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        ManagedObjectReference ref = new ManagedObjectReference();
        ref.setType("ServiceInstance");
        ref.setValue("ServiceInstance");
        try {
            XMLGregorianCalendar calendar = connection.getVimPort().currentTime(ref);
            Date d = calendar.toGregorianCalendar().getTime();
            d = new Date(d.getTime() - (Integer.parseInt(df.format(d)) % (5)) * 60 * 1000);
            GregorianCalendar sgcal = new GregorianCalendar();
            sgcal.setTime(dateTimeformater.parse(dateTimeformater.format(d)));
            calendar = DatatypeFactory.newInstance().newXMLGregorianCalendar(sgcal);
            return calendar;
        } catch (RuntimeFaultFaultMsg e) {
            e.printStackTrace();
        } catch (DatatypeConfigurationException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取vCenter五分钟前时间
    public XMLGregorianCalendar getPreXMLGregorianCalendar(XMLGregorianCalendar calendar) throws ParseException {
        Date sTime = new Date(calendar.toGregorianCalendar().getTime().getTime() - (5 * 1000 * 60));
        GregorianCalendar sgcal = new GregorianCalendar();
        sgcal.setTime(sTime);
        XMLGregorianCalendar sxgcal = null;
        try {
            sxgcal = DatatypeFactory.newInstance().newXMLGregorianCalendar(sgcal);
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException(e);
        }
        return sxgcal;
    }

    public <T> List<PerfQuerySpec> getPerfQuerySpec(List<T> infos, VcConnection connection, XMLGregorianCalendar exgcal, String version) throws RuntimeException {
        List<PerfQuerySpec> perfQuerylist = new ArrayList<PerfQuerySpec>();
        try {
            XMLGregorianCalendar sxgcal = null;
            try {
                sxgcal = getPreXMLGregorianCalendar(exgcal);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            for (T v : infos) {
                ManagedObjectReference mor = new ManagedObjectReference();
                if (v instanceof ResInstanceDiskApiModel) {
                    ResInstanceDiskApiModel instance = (ResInstanceDiskApiModel) v;
                    mor.setType(MORTypeEnum.VirtualMachine.toString());
                    mor.setValue(instance.getOpen_id());
                    if (!"POWERED_ON".equals(instance.getOpen_status()))
                        continue;
                } else if (v instanceof ResHostStoragePoolApiModel) {
                    ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) v;
                    mor.setType(MORTypeEnum.HostSystem.toString());
                    mor.setValue(host.getOpen_id());
                    if (!"poweredOn".equals(host.getOpen_status()))
                        continue;
                }
                List<PerfMetricId> listpermeid = null;
                try {
                    listpermeid = connection.getVimPort().queryAvailablePerfMetric(connection.getServiceContent().getPerfManager(), mor, null, null, 20);
                } catch (Exception e) {
                    log.error("采集虚拟机("+mor.getValue()+")性能失败，虚拟机或者已删除");
                }
                List<PerfMetricId> mMetrics = new ArrayList<PerfMetricId>();
                if (ObjectUtil.isNotEmpty(listpermeid)) {
                    listpermeid.forEach(l -> {
                        if (v instanceof CmdbInstanceRes) {
                            VmwarePerfUtil.bean.getVmPerfMetricId(true, version).forEach(p -> {
                                if (p.getCounterId() == l.getCounterId()) {
                                    mMetrics.add(l);
                                }
                            });
                            VmwarePerfUtil.bean.getVmPerfMetricId(false, version).forEach(p -> {
                                if (p.getCounterId() == l.getCounterId()) {
                                    mMetrics.add(l);
                                }
                            });
                        } else {
                            VmwarePerfUtil.bean.getHostPerfMetricId(true, version).forEach(p -> {
                                if (p.getCounterId() == l.getCounterId()) {
                                    mMetrics.add(l);
                                }
                            });
                            VmwarePerfUtil.bean.getHostPerfMetricId(false, version).forEach(p -> {
                                if (p.getCounterId() == l.getCounterId()) {
                                    mMetrics.add(l);
                                }
                            });
                        }
                    });
                }
                PerfQuerySpec qSpec = new PerfQuerySpec();
                qSpec.setEntity(mor);
                qSpec.setIntervalId(20);
                qSpec.setStartTime(sxgcal);
                qSpec.setEndTime(exgcal);
                //判断是否获取指标，未获取到指标信息，跳过此云主机性能采集
                if (ObjectUtil.isEmpty(mMetrics))
                    continue;
                qSpec.getMetricId().addAll(mMetrics);
                perfQuerylist.add(qSpec);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return perfQuerylist;
    }

    /**克隆虚拟机
     * @param connection  连接
     * @param request  请求body
     * @throws Exception
     */
    public static ManagedObjectReference cloneVm(VcConnection connection, CreateVmRequest request) throws Exception{
        MorManager morManager = null;
        Map<String, String> vdMap = new HashMap<>();
        try {
            morManager = new MorManager();
            morManager.setConnection(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ManagedObjectReference vmFolderRef = bean.getMor(MORTypeEnum.Folder.toString(), request.getFolderId());
        log.info("获取文件夹={}",vmFolderRef.getValue());
        ManagedObjectReference vmMor = bean.getMor(MORTypeEnum.VirtualMachine.toString(), request.getTemplateId());
        log.info("获取模版={}",vmMor.getValue());
        VirtualMachineCloneSpec cloneSpec = new VirtualMachineCloneSpec();
        VirtualMachineConfigSpec vmConfigSpec = new VirtualMachineConfigSpec();

        vmConfigSpec.setMemoryMB(request.getMemSize()*1024l);
        vmConfigSpec.setNumCPUs(request.getCpuSize());

        List<VirtualDeviceConfigSpec> deviceConfigSpec = new ArrayList<VirtualDeviceConfigSpec>();
        /*** 模板虚拟机的基础信息 */
        VirtualMachineConfigInfo config = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
        List<VirtualDevice> deviceArray = config.getHardware().getDevice();
        JSONArray nics = request.getNics();
        JSONArray disks = request.getDisks();

        int disk_num = 0;
        int unitNumber = 0;
        if (deviceArray != null) {
            for (int i = 0; i < deviceArray.size(); i++) {
                VirtualDevice virtualDevice = deviceArray.get(i);
                if (virtualDevice instanceof VirtualE1000
                        || virtualDevice instanceof VirtualE1000E
                        || virtualDevice instanceof VirtualVmxnet3
                        || virtualDevice instanceof VirtualVmxnet2) {
                    deviceConfigSpec.add(editNetwork((VirtualEthernetCard) virtualDevice, request.getNics(),morManager));
                }
                if (virtualDevice instanceof VirtualDisk) {
                    if (disks != null) {
                        for (int m = 0; m < disks.size(); m++) {
                            JSONObject disk = disks.getJSONObject(m);
                            int flag = disk.getInteger("flag");
                            if(flag== 0) {
                                String path = disk.getString("path");
                                vdMap.put(path.substring(path.length() - 6, path.length()), "true");
                            }
                        }
                    }
                    deviceConfigSpec.add(editDiskSpec((VirtualDisk) virtualDevice, disks,
                            request.getVmName(), disk_num));
                    disk_num++;
                    if (unitNumber <= virtualDevice.getUnitNumber()) {
                        unitNumber = virtualDevice.getUnitNumber() + 1;
                    }
                }

                if (virtualDevice instanceof VirtualCdrom) {
                    VirtualDeviceConfigSpec cdrmConfig = new VirtualDeviceConfigSpec();
                    VirtualCdrom cdrom = (VirtualCdrom) virtualDevice;
                    VirtualCdromRemoteAtapiBackingInfo info = new VirtualCdromRemoteAtapiBackingInfo();
                    info.setDeviceName("");
                    info.setUseAutoDetect(false);
                    cdrom.setBacking(info);
                    VirtualDeviceConnectInfo in = new VirtualDeviceConnectInfo();
                    in.setStartConnected(false);
                    in.setConnected(false);
                    in.setStatus("untried");
                    in.setAllowGuestControl(true);
                    cdrom.setConnectable(in);
                    cdrmConfig.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
                    cdrmConfig.setDevice(cdrom);
                    deviceConfigSpec.add(cdrmConfig);
                }
            }
        }
        if(nics!=null&&nics.size()>0) {
            for (int i = 0; i < nics.size(); i++) {
                JSONObject net = nics.getJSONObject(i);
                Integer flag = net.getInteger("flag");
                String cardType = net.getString("cardType");
                String networkId = net.getString("name");
                String networkName = net.getString("name");
                if(flag==0) {
                    VirtualDeviceConfigSpec nicSpec = new VirtualDeviceConfigSpec();
                    nicSpec.setOperation(VirtualDeviceConfigSpecOperation.ADD);
                    VirtualEthernetCard nic = null;
                    if (cardType.equals("E1000")) {
                        nic = new VirtualE1000();
                    }else if (cardType.equals("E1000E")) {
                        nic = new VirtualE1000E();
                    } else if (cardType.equals("Vmxnet2")) {
                        nic = new VirtualVmxnet2();
                    } else {
                        nic = new VirtualVmxnet3();
                    }
                    String type = "v";
                    String switchuuid = "";
                    if (networkId.startsWith("dvportgroup")) {
                        ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.DistributedVirtualPortgroup.toString(), networkId);
                        DVPortgroupConfigInfo dvConfig = null;
                        try {
                            dvConfig = (DVPortgroupConfigInfo) morManager.getDynamicProperty(subnetMor, "config");
                        } catch (Exception e) {
                            log.error("获取子网信息异常{}", e);
                        }
                        if (ObjectUtil.isNotNull(dvConfig)) {
                            networkName = (String)morManager.getDynamicProperty(subnetMor, "key");
                            type = "dv";
                            switchuuid = dvConfig.getDistributedVirtualSwitch().getValue();
                        }
                    }
                    if ("v".equals(type)) {
                        VirtualEthernetCardNetworkBackingInfo nicBacking = new VirtualEthernetCardNetworkBackingInfo();
                        nicBacking.setDeviceName(networkName);
                        nic.setBacking(nicBacking);
                        nic.setAddressType("generated");
                    } else {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo nb = new VirtualEthernetCardDistributedVirtualPortBackingInfo();
                        DistributedVirtualSwitchPortConnection ds = new DistributedVirtualSwitchPortConnection();
                        ds.setPortgroupKey(networkName);
                        ds.setSwitchUuid(switchuuid);
                        nb.setPort(ds);
                        nic.setBacking(nb);
                        nic.setAddressType("generated");
                    }
                    nicSpec.setDevice(nic);
                    deviceConfigSpec.add(nicSpec);
                }
             }
        }

        if(disks!=null&&disks.size()>0) {
            for (int i = 0; i < disks.size(); i++) {
                JSONObject disk = disks.getJSONObject(i);
                int flag = disk.getInteger("flag");
                if(flag==1) {
                    String dsId = disk.getString("storeId");
                    ManagedObjectReference dsMor = bean.getMor(MORTypeEnum.Datastore.toString(), dsId);
                    String dsName = bean.getName(dsMor, morManager);
                    int key = 0;
                    if (unitNumber == 7)
                        unitNumber++;
                    VirtualDeviceConfigSpec diskSpec = createAddDiskConfigSpec(request.getVmName(), dsName, config, disk, unitNumber, disk_num,
                            key, vdMap);
                    deviceConfigSpec.add(diskSpec);
                    unitNumber++;
                    disk_num++;
                    key++;
                }
            }
        }
        /** 添加硬盘 */
        vmConfigSpec.getDeviceChange().addAll(deviceConfigSpec);
        vmConfigSpec.setAnnotation("");
        /** 测试结束 */
        cloneSpec.setConfig(vmConfigSpec);
        if (request.isPowerOn()) {
            cloneSpec.setPowerOn(true);
        } else {
            cloneSpec.setPowerOn(false);
        }
        cloneSpec.setTemplate(false);

        VirtualMachineRelocateSpec relocSpec = new VirtualMachineRelocateSpec();

        if(request.getHostSite().contains("host-")) {
            ManagedObjectReference hostMor = bean.getMor(MORTypeEnum.HostSystem.toString(), request.getHostSite());
            log.info("获取主机={}",hostMor.getValue());
            ManagedObjectReference hostParentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor,
                    "parent");
            String clusterId = "";
            if(hostParentMor.getValue().contains("domain-c")){
                clusterId = hostParentMor.getValue();
            }
            relocSpec.setHost(hostMor);
            ManagedObjectReference poolMor = null;
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            List<ManagedObjectReference> resourcepoolmor = bean.getMorsbyType(rootRef,morManager,MORTypeEnum.ResourcePool.toString());
            for (ManagedObjectReference mor : resourcepoolmor) {
                ManagedObjectReference parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor,
                        "parent");
                if (parentMor.getValue().equals(clusterId)) {
                    poolMor = mor;
                    break;
                }
            }
            relocSpec.setPool(poolMor);
        }else {
            ManagedObjectReference clusterMor = bean.getMor(MORTypeEnum.ClusterComputeResource.toString(), request.getHostSite());
            List<ManagedObjectReference> hosts = bean.getMorsbyType(clusterMor,morManager,MORTypeEnum.HostSystem.toString());
            String clusterId = "";
            if(hosts!=null) {
                for(ManagedObjectReference h : hosts) {
                    if (isContainHostPortgroup(morManager,h,request.getNics())&&isContainHostDatastore(morManager,h,request.getDatastoreId())) {
                        relocSpec.setHost(h);
                        break;
                    }
                }
                ManagedObjectReference hostParentMor = (ManagedObjectReference) morManager.getDynamicProperty(hosts.get(0),
                        "parent");

                if(hostParentMor.getValue().contains("domain-c")){
                    clusterId = hostParentMor.getValue();
                }
            }
            ManagedObjectReference poolMor = null;
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            List<ManagedObjectReference> resourcepoolmor = bean.getMorsbyType(rootRef,morManager,MORTypeEnum.ResourcePool.toString());
            for (ManagedObjectReference mor : resourcepoolmor) {
                ManagedObjectReference parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor,
                        "parent");
                if (parentMor.getValue().equals(clusterId)) {
                    poolMor = mor;
                    break;
                }
            }
            relocSpec.setPool(poolMor);
        }
        ManagedObjectReference dsMOR = new ManagedObjectReference();
        dsMOR.setType(MORTypeEnum.Datastore.toString());
        dsMOR.setValue(request.getDatastoreId());

        relocSpec.setDatastore(dsMOR);
        cloneSpec.setLocation(relocSpec);
        ManagedObjectReference cloneTask = connection.getVimPort().cloneVMTask(vmMor, vmFolderRef, request.getVmName(), cloneSpec);
        return cloneTask;
    }

    public static ManagedObjectReference createImage(VcConnection connection, CreateImageRequest request) throws Exception{
        MorManager morManager = null;
        Map<String, String> vdMap = new HashMap<>();
        try {
            morManager = new MorManager();
            morManager.setConnection(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ManagedObjectReference vmFolderRef = bean.getMor(MORTypeEnum.Folder.toString(), request.getVmfolder());
        log.info("获取文件夹={}",vmFolderRef.getValue());
        ManagedObjectReference vmMor = bean.getMor(MORTypeEnum.VirtualMachine.toString(), request.getTemplateId());
        log.info("获取模版={}",vmMor.getValue());
        VirtualMachineCloneSpec cloneSpec = new VirtualMachineCloneSpec();
        VirtualMachineConfigSpec vmConfigSpec = new VirtualMachineConfigSpec();

        VirtualMachineSummary summary = null;
        try {
            summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
        } catch (Exception e) {
            log.error("获取云主机summary信息异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
        }

        vmConfigSpec.setMemoryMB(summary.getConfig().getMemorySizeMB()+0l);
        vmConfigSpec.setNumCPUs(summary.getConfig().getNumCpu());

        List<VirtualDeviceConfigSpec> deviceConfigSpec = new ArrayList<VirtualDeviceConfigSpec>();
        /*** 模板虚拟机的基础信息 */
        VirtualMachineConfigInfo config = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
        List<VirtualDevice> deviceArray = config.getHardware().getDevice();

        int disk_num = 0;
        int unitNumber = 0;
        if (deviceArray != null) {
            for (int i = 0; i < deviceArray.size(); i++) {
                VirtualDevice virtualDevice = deviceArray.get(i);
                if (virtualDevice instanceof VirtualE1000
                        || virtualDevice instanceof VirtualE1000E
                        || virtualDevice instanceof VirtualVmxnet3
                        || virtualDevice instanceof VirtualVmxnet2) {
                    VirtualDeviceConfigSpec nicSpec = new VirtualDeviceConfigSpec();
                    nicSpec.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
                    nicSpec.setDevice(virtualDevice);
                    deviceConfigSpec.add(nicSpec);
                }
                if (virtualDevice instanceof VirtualDisk) {
                    VirtualDeviceConfigSpec diskSpec = new VirtualDeviceConfigSpec();
                    diskSpec.setDevice(virtualDevice);
                    deviceConfigSpec.add(diskSpec);

                }

                if (virtualDevice instanceof VirtualCdrom) {
                    VirtualDeviceConfigSpec cdrmConfig = new VirtualDeviceConfigSpec();
                    VirtualCdrom cdrom = (VirtualCdrom) virtualDevice;
                    VirtualCdromRemoteAtapiBackingInfo info = new VirtualCdromRemoteAtapiBackingInfo();
                    info.setDeviceName("");
                    info.setUseAutoDetect(false);
                    cdrom.setBacking(info);
                    VirtualDeviceConnectInfo in = new VirtualDeviceConnectInfo();
                    in.setStartConnected(false);
                    in.setConnected(false);
                    in.setStatus("untried");
                    in.setAllowGuestControl(true);
                    cdrom.setConnectable(in);
                    cdrmConfig.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
                    cdrmConfig.setDevice(cdrom);
                    deviceConfigSpec.add(cdrmConfig);
                }
            }
        }
        /** 添加硬盘 */
        vmConfigSpec.getDeviceChange().addAll(deviceConfigSpec);
        vmConfigSpec.setAnnotation("");
        /** 测试结束 */
        cloneSpec.setConfig(vmConfigSpec);
        cloneSpec.setPowerOn(false);
        cloneSpec.setTemplate(true);

        VirtualMachineRelocateSpec relocSpec = new VirtualMachineRelocateSpec();

        if(request.getHostId().contains("host-")) {
            ManagedObjectReference hostMor = bean.getMor(MORTypeEnum.HostSystem.toString(), request.getHostId());
            log.info("获取主机={}",hostMor.getValue());
            ManagedObjectReference hostParentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor,
                    "parent");
            String clusterId = "";
            if(hostParentMor.getValue().contains("domain-c")){
                clusterId = hostParentMor.getValue();
            }
            relocSpec.setHost(hostMor);
            ManagedObjectReference poolMor = null;
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            List<ManagedObjectReference> resourcepoolmor = bean.getMorsbyType(rootRef,morManager,MORTypeEnum.ResourcePool.toString());
            for (ManagedObjectReference mor : resourcepoolmor) {
                ManagedObjectReference parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor,
                        "parent");
                if (parentMor.getValue().equals(clusterId)) {
                    poolMor = mor;
                    break;
                }
            }
            relocSpec.setPool(poolMor);
        }else {
            ManagedObjectReference clusterMor = bean.getMor(MORTypeEnum.ClusterComputeResource.toString(), request.getHostId());
            List<ManagedObjectReference> hosts = bean.getMorsbyType(clusterMor,morManager,MORTypeEnum.HostSystem.toString());
            String clusterId = "";
            if(hosts!=null) {
                for(ManagedObjectReference h : hosts) {
                    if (isContainHostDatastore(morManager,h,request.getDatastoreId())) {
                        relocSpec.setHost(h);
                        break;
                    }
                }
                ManagedObjectReference hostParentMor = (ManagedObjectReference) morManager.getDynamicProperty(hosts.get(0),
                        "parent");

                if(hostParentMor.getValue().contains("domain-c")){
                    clusterId = hostParentMor.getValue();
                }
            }
            ManagedObjectReference poolMor = null;
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            List<ManagedObjectReference> resourcepoolmor = bean.getMorsbyType(rootRef,morManager,MORTypeEnum.ResourcePool.toString());
            for (ManagedObjectReference mor : resourcepoolmor) {
                ManagedObjectReference parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor,
                        "parent");
                if (parentMor.getValue().equals(clusterId)) {
                    poolMor = mor;
                    break;
                }
            }
            relocSpec.setPool(poolMor);
        }
        ManagedObjectReference dsMOR = new ManagedObjectReference();
        dsMOR.setType(MORTypeEnum.Datastore.toString());
        dsMOR.setValue(request.getDatastoreId());

        relocSpec.setDatastore(dsMOR);
        cloneSpec.setLocation(relocSpec);
        ManagedObjectReference cloneTask = connection.getVimPort().cloneVMTask(vmMor, vmFolderRef, request.getVmName(), cloneSpec);
        return cloneTask;
    }

    public static void createSwitch(VcConnection connection, CreateVswitchRequest request) throws Exception{
        MorManager morManager = null;
        try {
            morManager = new MorManager();
            morManager.setConnection(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(connection!=null) {
            HostVirtualSwitchSpec spec = new HostVirtualSwitchSpec();
            spec.setNumPorts(request.getNumPorts());
            spec.setMtu(1500);
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType("HostNetworkSystem");
            hostMor.setValue("networkSystem-"+request.getHostId().split("-")[1]);
            JSONArray nicIds = request.getNicIds();
            HostVirtualSwitchBondBridge bridge = new HostVirtualSwitchBondBridge();
            if(nicIds!=null)
                for(int i=0;i<nicIds.size();i++) {
                    bridge.getNicDevice().add(nicIds.getString(i));
                }
            spec.setBridge(bridge);
            try {
                connection.getVimPort().addVirtualSwitch(hostMor, request.getSwitchName(), spec);
            } catch (Exception e) {
                log.error("添加标准交换机异常",e);
            }
        }
    }

    public static void updateSwitch(VcConnection connection, CreateVswitchRequest request) throws Exception{
        MorManager morManager = null;
        Map<String, String> vdMap = new HashMap<>();
        try {
            morManager = new MorManager();
            morManager.setConnection(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(connection!=null) {
            HostVirtualSwitchSpec spec = new HostVirtualSwitchSpec();
            spec.setMtu(request.getMtu());
            spec.setNumPorts(128);
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType("HostNetworkSystem");
            hostMor.setValue("networkSystem-"+request.getHostId().split("-")[1]);
            try {
                connection.getVimPort().updateVirtualSwitch(hostMor, request.getSwitchName(), spec);
            } catch (Exception e) {
                log.error("修改标准交换机异常",e);
            }
        }
    }

    public static boolean isContainHostPortgroup(MorManager morManager,ManagedObjectReference hostMor, JSONArray vnList) {
        try {
            Map<String, String> networkMap = null;
            List<HostPortGroup> portgroups = null;
            try {
                portgroups = (List<HostPortGroup>) morManager.getDynamicProperty(hostMor, "config.network.portgroup");
                networkMap = portgroups.stream().collect(Collectors.toMap(mor -> mor.getSpec().getName(), mor -> mor.getKey()));
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }

            if (vnList != null && vnList.size() > 0) {
                for (int i = 0; i < vnList.size(); i++) {
                    JSONObject vn = vnList.getJSONObject(i);
                    String networkName = vn.getString("name");
                    if (networkMap.get(networkName) == null) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    public static boolean isContainHostDatastore(MorManager morManager,ManagedObjectReference hostMor, String dsId) {
        try {
            List<ManagedObjectReference> datastoreMors = null;
            try {
                datastoreMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "datastore");
            } catch (Exception e) {
                log.error("获取宿主机datastore信息异常{}", e);
            }
            if (datastoreMors != null) {
                for (ManagedObjectReference ds : datastoreMors) {
                    if (dsId.equals(ds.getValue())) {
                        return true;
                    }
                }
            }

            return false;
        }catch(Exception e){
            e.printStackTrace();
        }
        return false;
    }

    public static ManagedObjectReference updateVm(VcConnection connection, UpdateVmRequest request) throws Exception{
        MorManager morManager = null;
        try {
            morManager = new MorManager();
            morManager.setConnection(connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ManagedObjectReference vmMOR = bean.getMor(MORTypeEnum.VirtualMachine.toString(), request.getVmId());
        /** 获取虚拟机的config信息 */
        VirtualMachineConfigInfo config = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMOR, "config");
        /***
         * 设置虚拟机的修改配置
         */
        VirtualMachineConfigSpec vmConfigSpec = getReconfigVmConfigSpec(morManager, config, request);
        vmConfigSpec.setMemoryMB(Long.parseLong(request.getMemSize()*1024l + ""));
        vmConfigSpec.setNumCPUs(request.getCpuSize());
        vmConfigSpec.setAnnotation(request.getDescription());
        /** 执行虚拟机修改操作 */
        ManagedObjectReference tmor = connection.getVimPort().reconfigVMTask(vmMOR, vmConfigSpec);
        return tmor;
    }

    public static VirtualMachineConfigSpec getReconfigVmConfigSpec(MorManager morManager,
                                                                   VirtualMachineConfigInfo config, UpdateVmRequest request) throws Exception {
        List<VirtualDevice> deviceArray = config.getHardware().getDevice();
        JSONArray systemDisks = request.getSystemDisk();
        JSONArray dataDisks = request.getDataDisk();
        JSONArray nics = request.getNics();
        List<VirtualDeviceConfigSpec> deviceConfigSpec = new ArrayList<VirtualDeviceConfigSpec>();
        int unitNumber = 0;
        List existDiskUnitNumber = new ArrayList();
        int diskCtrlKey = -1;
        int disk_num = 0;
        Map<String, String> vdMap = new HashMap<>();
        for (int i = 0; i < deviceArray.size(); i++) {
            VirtualDevice virtualDevice = deviceArray.get(i);
            if (virtualDevice instanceof VirtualE1000
                    || virtualDevice instanceof VirtualE1000E
                    || virtualDevice instanceof VirtualVmxnet3
                    || virtualDevice instanceof VirtualVmxnet2) {
                deviceConfigSpec.add(editNetwork((VirtualEthernetCard) virtualDevice, request.getNics(),morManager));
            }
            if (virtualDevice instanceof VirtualDisk) {
                if (systemDisks != null) {
                    for (int m = 0; m < systemDisks.size(); m++) {
                        JSONObject disk = systemDisks.getJSONObject(m);
                        String path = disk.getString("path");
                        vdMap.put(path.substring(path.length() - 6, path.length()), "true");
                    }
                }
                deviceConfigSpec.add(editDiskSpec((VirtualDisk) virtualDevice, systemDisks,
                        request.getVmName(), disk_num));
                disk_num++;
                if (unitNumber <= virtualDevice.getUnitNumber()) {
                    unitNumber = virtualDevice.getUnitNumber() + 1;
                }
            }
        }

        if(nics!=null&&nics.size()>0) {
            for (int i = 0; i < nics.size(); i++) {
                JSONObject net = nics.getJSONObject(i);
                Integer flag = net.getInteger("flag");
                String cardType = net.getString("cardType");
                String networkId = net.getString("id");
                String networkName = net.getString("name");
                if(flag==0) {
                    VirtualDeviceConfigSpec nicSpec = new VirtualDeviceConfigSpec();
                    nicSpec.setOperation(VirtualDeviceConfigSpecOperation.ADD);
                    VirtualEthernetCard nic = null;
                    if (cardType.equals("E1000")) {
                        nic = new VirtualE1000();
                    }else if (cardType.equals("E1000E")) {
                        nic = new VirtualE1000E();
                    } else if (cardType.equals("Vmxnet2")) {
                        nic = new VirtualVmxnet2();
                    } else {
                        nic = new VirtualVmxnet3();
                    }
                    String type = "v";
                    String switchuuid = "";
                    if (networkId.startsWith("dvportgroup")) {

                    }
                    if ("v".equals(type)) {
                        VirtualEthernetCardNetworkBackingInfo nicBacking = new VirtualEthernetCardNetworkBackingInfo();
                        nicBacking.setDeviceName(networkName);
                        nic.setBacking(nicBacking);
                        nic.setAddressType("generated");
                    } else {
                        VirtualEthernetCardDistributedVirtualPortBackingInfo nb = new VirtualEthernetCardDistributedVirtualPortBackingInfo();
                        DistributedVirtualSwitchPortConnection ds = new DistributedVirtualSwitchPortConnection();
                        ds.setPortgroupKey(networkName);
                        ds.setSwitchUuid(switchuuid);
                        nb.setPort(ds);
                        nic.setBacking(nb);
                        nic.setAddressType("generated");
                    }
                    nicSpec.setDevice(nic);
                    deviceConfigSpec.add(nicSpec);
                }
            }
        }

        if(dataDisks!=null&&dataDisks.size()>0) {
            for (int i = 0; i < dataDisks.size(); i++) {
                JSONObject disk = dataDisks.getJSONObject(i);
                String dsId = disk.getString("storeId");
                ManagedObjectReference dsMor = bean.getMor(MORTypeEnum.Datastore.toString(), dsId);
                String dsName = bean.getName(dsMor, morManager);
                int key = 0;
                if (unitNumber == 7)
                    unitNumber++;
                VirtualDeviceConfigSpec diskSpec = createAddDiskConfigSpec(request.getVmName(),dsName, config, disk, unitNumber, disk_num,
                        key,vdMap);
                deviceConfigSpec.add(diskSpec);
                unitNumber++;
                disk_num++;
                key++;
            }
        }

        VirtualMachineConfigSpec configSpec = new VirtualMachineConfigSpec();
        configSpec.getDeviceChange().addAll(deviceConfigSpec);

        return configSpec;
    }

    static VirtualDeviceConfigSpec editNetwork(VirtualEthernetCard nic, JSONArray vnList,MorManager morManager) {
        VirtualDeviceConfigSpec nicSpec = new VirtualDeviceConfigSpec();
        String networkId = "";
        String networkName = "";
        if (vnList != null && vnList.size() > 0) {
            for(int i=0; i < vnList.size(); i++) {
                JSONObject vn = vnList.getJSONObject(i);
                if(vn.getInteger("flag")==1) {
                    if (vn.getInteger("key") == nic.getKey()) {
                        networkId = vn.getString("name");
                        networkName = vn.getString("name");
                        break;
                    }
                }
            }
            String type = "v";
            String portgroupKey = "";
            String switchuuid = "";
            if(StringUtils.isNotEmpty(networkId)) {
                type = "v";
                if (networkId.startsWith("dvportgroup")) {
                    ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.DistributedVirtualPortgroup.toString(), networkId);
                    DVPortgroupConfigInfo dvConfig = null;
                    try {
                        dvConfig = (DVPortgroupConfigInfo) morManager.getDynamicProperty(subnetMor, "config");
                    } catch (Exception e) {
                        log.error("获取子网信息异常{}", e);
                    }
                    log.info("dvConfig:{}",dvConfig);
                    try {
                        portgroupKey = (String) morManager.getDynamicProperty(subnetMor, "key");
                        log.info("portgroupKey:{}",portgroupKey);
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                    type = "dv";
                    if (ObjectUtil.isNotNull(dvConfig)) {
                        portgroupKey = dvConfig.getKey();
                        log.info("portgroupKey:{}",portgroupKey);
                        ManagedObjectReference distributedVirtualSwitch = dvConfig.getDistributedVirtualSwitch();
                        try {
                            switchuuid = (String) morManager.getDynamicProperty(distributedVirtualSwitch, "uuid");
                        }catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                nicSpec.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
                if ("v".equals(type)) {
                    VirtualEthernetCardNetworkBackingInfo nicBacking = new VirtualEthernetCardNetworkBackingInfo();
                    nicBacking.setDeviceName(networkName);
                    nic.setBacking(nicBacking);
                } else {
                    VirtualEthernetCardDistributedVirtualPortBackingInfo nb = new VirtualEthernetCardDistributedVirtualPortBackingInfo();
                    DistributedVirtualSwitchPortConnection ds = new DistributedVirtualSwitchPortConnection();
                    log.info("创建分布式虚拟机portgroupKey:{},switchuuid:{}", portgroupKey, switchuuid);
                    ds.setPortgroupKey(portgroupKey);
                    ds.setSwitchUuid(switchuuid);
                    nb.setPort(ds);
                    nic.setBacking(nb);
                }
            }
            VirtualDeviceConnectInfo in = new VirtualDeviceConnectInfo();
            in.setStartConnected(true);
            in.setConnected(true);
            nic.setConnectable(in);
            nicSpec.setDevice(nic);
        }else {
            nicSpec.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
            VirtualDeviceConnectInfo in = new VirtualDeviceConnectInfo();
            in.setStartConnected(true);
            in.setConnected(true);
            nic.setConnectable(in);
            nicSpec.setDevice(nic);
        }
        return nicSpec;
    }

    static VirtualDeviceConfigSpec editDiskSpec(VirtualDisk vd, JSONArray disks, String vmname, int disk_num) {
        VirtualDiskFlatVer2BackingInfo back = (VirtualDiskFlatVer2BackingInfo) vd.getBacking();
        VirtualDeviceConfigSpec diskSpec = new VirtualDeviceConfigSpec();
        if (disks != null && disks.size() > 0) {
            for(int i=0;i<disks.size();i++) {
                JSONObject disk = disks.getJSONObject(i);
                int flag = disk.getInteger("flag");
                if(flag==0) {
                    String path = disk.getString("path");
                    Integer size = disk.getInteger("size");
                    if (path.equals(back.getFileName())) {
                        diskSpec.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
                        if ((int) (Float.parseFloat(size + "")) * 1024 * 1024 > vd.getCapacityInKB()) {
                            vd.setCapacityInKB(size * 1024 * 1024);
                        }
                        diskSpec.setDevice(vd);
                        break;
                    }
                }
            }
        }else {
            diskSpec.setOperation(VirtualDeviceConfigSpecOperation.EDIT);
            diskSpec.setDevice(vd);
        }
        return diskSpec;
    }

    public static VirtualDeviceConfigSpec createAddDiskConfigSpec(String vmName, String dsName,VirtualMachineConfigInfo config,
                                                                  JSONObject d, int unitNumber, int disk_num, int key, Map<String, String> vdMap) {
        try {

            String diskMode = "persistent";
            String diskType = d.getString("diskType");
            Integer diskSize = d.getInteger("size");
            VirtualDeviceConfigSpec diskSpec = new VirtualDeviceConfigSpec();
            List<VirtualDevice> vds = config.getHardware().getDevice();
            VirtualDisk disk = new VirtualDisk();
            VirtualDiskFlatVer2BackingInfo diskfileBacking = new VirtualDiskFlatVer2BackingInfo();
            int ckey = 0;
            for (int k = 0; k < vds.size(); k++) {
                if (vds.get(k).getDeviceInfo().getLabel().equalsIgnoreCase("SCSI Controller 0")
                        || vds.get(k).getDeviceInfo().getLabel().equalsIgnoreCase("SCSI 控制器 0")) {
                    ckey = vds.get(k).getKey();
                }
            }
            if (dsName == null) {
                return null;
            }
            String fileName = "[" + dsName + "] " + vmName + "/" + vmName + "_" + disk_num
                    + ".vmdk";

            while(vdMap.containsKey(fileName.substring(fileName.length()-6,fileName.length()))) {
                System.out.println("new---"+fileName);
                disk_num++;
                fileName = "[" + dsName + "] " + vmName + "/" + vmName + "_" + disk_num
                        + ".vmdk";
            }
            diskfileBacking.setFileName(fileName);
            diskfileBacking.setDiskMode(diskMode);
            if (diskType.equals("精简置备")) {
                diskfileBacking.setThinProvisioned(true);
            } else if (diskType.equals("厚置备置零")) {
                diskfileBacking.setThinProvisioned(false);
                diskfileBacking.setEagerlyScrub(true);
            } else {
                diskfileBacking.setThinProvisioned(false);
                diskfileBacking.setEagerlyScrub(false);
            }
            disk.setControllerKey(ckey);
            disk.setUnitNumber(unitNumber);
            disk.setBacking(diskfileBacking);
            disk.setCapacityInKB(diskSize * 1024 * 1024);
            disk.setKey(key);
            diskSpec.setOperation(VirtualDeviceConfigSpecOperation.ADD);
            diskSpec.setFileOperation(VirtualDeviceConfigSpecFileOperation.CREATE);
            diskSpec.setDevice(disk);
            return diskSpec;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void addHost(VcConnection connection, CreateHostRequest request) throws Exception{
        HostConnectSpec conn = new HostConnectSpec();
        conn.setForce(true);
        conn.setHostName(request.getHostIp());
        conn.setManagementIp(request.getHostIp());
        conn.setUserName(request.getUserName());
        conn.setPassword(request.getPassword());
        ManagedObjectReference clusterMor = new ManagedObjectReference();
        clusterMor.setType(MORTypeEnum.ClusterComputeResource.toString());
        clusterMor.setValue(request.getClusterId());
        connection.getVimPort().addHostTask(clusterMor, conn, true, null, request.getLicenseKey());
    }
}
