package com.futong.gemini.plugin.cloud.vmware;

import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import com.futong.gemini.plugin.cloud.vmware.sampler.FetchSampler;
import com.futong.gemini.plugin.cloud.vmware.service.*;

public class VmwareCloudRegister extends BaseCloudRegister {


    public <Q, R, C> void register(ActionType actionType, FTExecute<Q, R, C> execute) {
        register(actionType, execute);
    }
    @Override
    public void load() {
        onAfterLoadCloudAccount();//加载同步调度信息
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadOperate();//加载云主机操作
    }

    public void onAfterLoadCloudAccount() {
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatAuthService::authCloudAccount);//云账号验证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);//获取云账号表单信息
    }
    public void onAfterLoadFetch() {
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchSampler::fetchInstance);//同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, PerfService::fetchInstancePerf);//同步云主机性能数据
        register(ActionType.FETCH_COMPUTE_HOST, FetchSampler::fetchHost);//同步物理机
        register(ActionType.FETCH_COMPUTE_HOST_PERF, PerfService::fetchHostPerf);//同步物理机性能数据
        register(ActionType.FETCH_STORAGE_POOL, FetchSampler::fetchDatastore);//同步数据存储
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchSampler::fetchSubnet);//同步端口组
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchSampler::fetchDVSwitch);//同步交换机

        register(ActionType.FETCH_PLATFORM_DATACENTER, FetchSampler::fetchDatacenter);//获取数据中心
        register(ActionType.FETCH_PLATFORM_CLUSTER, FetchSampler::fetchCluster);//获取集群
        register(ActionType.FETCH_PLATFORM_FOLDER, FetchSampler::fetchFolder);//获取文件夹
        register(ActionType.FETCH_PLATFORM_RESOURCE_POOL, FetchSampler::fetchResourcepool);//获取资源池
        register(ActionType.FETCH_PLATFORM_EVENT, FetchSampler::fetchEvent);//获取事件
        register(ActionType.FETCH_PLATFORM_ALARM, FetchSampler::fetchAlarm);//获取告警
    }
    public void onAfterLoadOperate() {
        register(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::startInstance);//启动云主机
        register(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::stopInstance);//关闭云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::rebootInstance);//重启云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance);//删除云主机

        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::webConsole);//web控制台
        register(ActionType.CREATE_COMPUTE_SNAPSHOT, ComputeInstanceService::createSnapshot);//创建快照
        register(ActionType.UPDATE_COMPUTE_SNAPSHOT, ComputeInstanceService::updateSnapshot);//修改快照
        register(ActionType.DELETE_COMPUTE_SNAPSHOT, ComputeInstanceService::deleteSnapshot);//删除快照
        register(ActionType.RESUME_COMPUTE_SNAPSHOT, ComputeInstanceService::resumeSnapshot);//恢复快照
        register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance);//创建虚拟机

        register(ActionType.ADD_COMPUTE_HOST, ComputeHostService::addHost);//添加主机
        register(ActionType.STOP_COMPUTE_HOST, ComputeHostService::stopHost);//关闭主机
        register(ActionType.CONNECT_COMPUTE_HOST, ComputeHostService::connectHost);//连接主机
        register(ActionType.DISCONNECT_COMPUTE_HOST, ComputeHostService::disConnectHost);//断开主机
        register(ActionType.ENTER_COMPUTE_HOST, ComputeHostService::enterHost);//进入维护模式
        register(ActionType.EXIT_COMPUTE_HOST, ComputeHostService::exitHost);//退出维护模式
        register(ActionType.REMOVE_COMPUTE_HOST, ComputeHostService::removeHost);//移除

        register(ActionType.DELETE_COMPUTE_TEMPLATE, ComputeInstanceService::deleteInstance);//删除模版

    }

}
