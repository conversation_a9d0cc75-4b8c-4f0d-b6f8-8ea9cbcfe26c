package com.futong.gemini.plugin.cloud.vmware;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.function.FTAction;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.vmware.sampler.FetchSampler;
import com.futong.gemini.plugin.cloud.vmware.service.*;

import java.util.HashMap;
import java.util.Map;

public class VmwareRegister {
    private static Map<String, FTAction> actions = new HashMap<>();


    public static <Q, R, C> void register(String fetchType, FTAction<JSONObject> ftAction) {
        actions.put(fetchType, ftAction);
    }

    public static boolean isNotExists(String action) {
        return !isExists(action);
    }

    public static boolean isExists(String action) {
        return actions.containsKey(action);
    }

    public static FTAction getAction(String action) {
        return actions.get(action);
    }

    public static void load(){
        //资源操作加载
        VmwareRegister.onAfterLoadCloudAccount();//加载账号相关
        VmwareRegister.onAfterLoadFetch();//加载同步调度信息
        VmwareRegister.onAfterLoadOperate();//加载云主机操作
    }

    public static void onAfterLoadCloudAccount() {
        register(ActionType.AUTH_PLATFORM_ACCOUNT.value(), PlatAuthService::authCloudAccount);//云账号验证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM.value(), AccountService::getAccountAddForm);//获取云账号表单信息
//        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL.value(), AccountService::getFetchAddModel);//获取调度添加模型
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH.value(), AccountService::createFetchDispatch);//添加默认调度任务
    }
    public static void onAfterLoadFetch() {
        register(ActionType.FETCH_COMPUTE_INSTANCE.value(), FetchSampler::fetchInstance);//同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF.value(), PerfService::fetchInstancePerf);//同步云主机性能数据
        register(ActionType.FETCH_COMPUTE_HOST.value(), FetchSampler::fetchHost);//同步物理机
        register(ActionType.FETCH_COMPUTE_HOST_PERF.value(), PerfService::fetchHostPerf);//同步物理机性能数据
        register(ActionType.FETCH_STORAGE_POOL.value(), FetchSampler::fetchDatastore);//同步数据存储
        register(ActionType.FETCH_NEUTRON_SUBNET.value(), FetchSampler::fetchSubnet);//同步端口组
        register(ActionType.FETCH_NEUTRON_SWITCH.value(), FetchSampler::fetchDVSwitch);//同步交换机

        register(ActionType.FETCH_PLATFORM_DATACENTER.value(), FetchSampler::fetchDatacenter);//获取数据中心
        register(ActionType.FETCH_PLATFORM_CLUSTER.value(), FetchSampler::fetchCluster);//获取集群
        register(ActionType.FETCH_PLATFORM_FOLDER.value(), FetchSampler::fetchFolder);//获取文件夹
        register(ActionType.FETCH_PLATFORM_RESOURCE_POOL.value(), FetchSampler::fetchResourcepool);//获取资源池
        register(ActionType.FETCH_PLATFORM_EVENT.value(), FetchSampler::fetchEvent);//获取事件
        register(ActionType.FETCH_PLATFORM_ALARM.value(), FetchSampler::fetchAlarm);//获取告警

        register(ActionType.SYNC_COMPUTE_INSTANCE.value(), FetchSampler::syncInstance);//同步云主机信息
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL.value(), FetchSampler::queryInstanceTotal);//同步云主机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL.value(), FetchSampler::queryHostTotal);//同步物理机数量
    }
    public static void onAfterLoadOperate() {
        register(ActionType.START_COMPUTE_INSTANCE.value(), ComputeInstanceService::startInstance);//启动云主机
        register(ActionType.STOP_COMPUTE_INSTANCE.value(), ComputeInstanceService::stopInstance);//关闭云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE.value(), ComputeInstanceService::rebootInstance);//重启云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE.value(), ComputeInstanceService::deleteInstance);//删除云主机

        register(ActionType.CONSOLE_COMPUTE_INSTANCE.value(), ComputeInstanceService::webConsole);//web控制台
        register(ActionType.CREATE_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::createSnapshot);//创建快照
        register(ActionType.UPDATE_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::updateSnapshot);//修改快照
        register(ActionType.DELETE_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::deleteSnapshot);//删除快照
        register(ActionType.RESUME_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::resumeSnapshot);//恢复快照
        register(ActionType.CREATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::createInstance);//创建虚拟机
        register(ActionType.UPDATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::updateInstance);//修改虚拟机

        register(ActionType.REFRESH_COMPUTE_INSTANCE.value(), ComputeInstanceService::refleshInstance);//更新云主机


        register(ActionType.ADD_COMPUTE_HOST.value(), ComputeHostService::addHost);//添加主机
        register(ActionType.MOVE_HOST.value(), ComputeHostService::moveHost);//移入主机
        register(ActionType.SCAN_HOST_DATASTORE.value(), ComputeHostService::scanHostDatastore);//扫描主机数据存储
        register(ActionType.STOP_COMPUTE_HOST.value(), ComputeHostService::stopHost);//关闭主机
        register(ActionType.CONNECT_COMPUTE_HOST.value(), ComputeHostService::connectHost);//连接主机
        register(ActionType.DISCONNECT_COMPUTE_HOST.value(), ComputeHostService::disConnectHost);//断开主机
        register(ActionType.ENTER_COMPUTE_HOST.value(), ComputeHostService::enterHost);//进入维护模式
        register(ActionType.EXIT_COMPUTE_HOST.value(), ComputeHostService::exitHost);//退出维护模式
        register(ActionType.REMOVE_COMPUTE_HOST.value(), ComputeHostService::removeHost);//移除

        register(ActionType.DELETE_COMPUTE_TEMPLATE.value(), ComputeInstanceService::deleteInstance);//删除模版

        register(ActionType.CREATE_COMPUTE_IMAGE.value(), ComputeInstanceService::createImage);//创建镜像
        register(ActionType.CREATE_NEUTRON_VSWITCH.value(), NetworkVswitchService::createSwitch);//创建交换机
        register(ActionType.UPDATE_NEUTRON_VSWITCH.value(), NetworkVswitchService::updateSwitch);//修改交换机
        register(ActionType.DELETE_NEUTRON_VSWITCH.value(), NetworkVswitchService::deleteSwitch);//删除交换机

    }
}
