package com.futong.gemini.plugin.cloud.vmware;

import com.futong.common.log.DynamicLoggerConfigurator;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudPluginTemplate;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class VmwareCloudPluginTemplate extends BaseCloudPluginTemplate {

    @Override
    public void init(String key) {
        super.init(key);
        //指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.vmware", "/cloud/vmware");

    }

    @Override
    public BaseCloudRegister getRegister() {
        return new VmwareCloudRegister();
    }


}
