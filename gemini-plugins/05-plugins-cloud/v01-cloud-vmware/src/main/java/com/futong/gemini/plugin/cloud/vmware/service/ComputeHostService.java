package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.InstanceStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.DatastoreStatusEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.CreateHostRequest;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.request.MoveHostRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbStoragePoolRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class ComputeHostService {

    public static final ComputeHostService bean = new ComputeHostService();


    public void fetchHost(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbHostRes> hosts = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(hostId -> {
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            CmdbHostRes host = new CmdbHostRes();
            host.setCloud_type(accessBean.getCloudType());
            host.setAccount_id(accessBean.getCmpId());
            host.setOpen_id(hostId);
            HostListSummary summary = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                summary = (HostListSummary) morManager.getDynamicProperty(hostMor, "summary");
            } catch (Exception e) {
                log.error("获取宿主机summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                HostHardwareSummary hardware = summary.getHardware();
                String model = "";
                if (hardware.getModel() != null) {
                    model = hardware.getModel();
                }
                host.setManufacturer(hardware.getVendor());
                host.setModel(model);
                host.setCpu_size(hardware.getNumCpuCores() + 0);
                host.setMem_size((int) (hardware.getMemorySize() / 1024 / 1024));
                HostRuntimeInfo hostRuntimeInfo = summary.getRuntime();
                if (ObjectUtil.isNotNull(hostRuntimeInfo)) {
                    host.setMaintain_mode(hostRuntimeInfo.isInMaintenanceMode() ? "1" : "0");
                    switch (hostRuntimeInfo.getPowerState().value().toString()) {
                        case "poweredOn":
                            host.setStatus(InstanceStatus.RUNNING.value());
                            break;
                        case "poweredOff":
                            host.setStatus(InstanceStatus.STOPPED.value());
                            break;
                        default:
                            host.setStatus(InstanceStatus.UNKNOWN.value());
                            break;
                    }
                    host.setOpen_status(hostRuntimeInfo.getPowerState().value());
                    long startTime = hostRuntimeInfo.getBootTime().toGregorianCalendar().getTime().getTime();
                    long endTime = System.currentTimeMillis();
                    int run_time = (int) ((endTime - startTime) / 1000 / 60 / 60);
                    host.setRun_time(run_time);
                }
            }
            List<HostVirtualNic> vsicalNics = null;
            try {
                vsicalNics = (List<HostVirtualNic>) morManager.getDynamicProperty(hostMor, "config.network.vnic");
            } catch (Exception e) {
                log.error("获取宿主机config.network.vnic信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(vsicalNics)) {
                vsicalNics.forEach(v -> {
                    if ("vmk0".equals(v.getDevice())) {
                        host.setIp(v.getSpec().getIp().getIpAddress());
                    }
                });
            }
            host.setOpen_name(VmwareUtils.getName(hostMor, morManager));
            host.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), hostId));
            hosts.add(host);

            //获取宿主机关联存储信息
            List<ManagedObjectReference> datastoreMors = null;
            try {
                datastoreMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "datastore");
            } catch (Exception e) {
                log.error("获取宿主机datastore信息异常{}", e);
            }
            Float totalSize = 0f;
            Float freeSize = 0f;
            Float allocationSize = 0f;
            for (ManagedObjectReference datastoreMor : datastoreMors) {
                DatastoreSummary dssummary = null;
                try {
                    dssummary = (DatastoreSummary) morManager.getDynamicProperty(datastoreMor, "summary");
                } catch (Exception e) {
                    log.error("获取数据存储summary信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(dssummary)) {
                    if (dssummary.isAccessible()) {
                        totalSize += Float.valueOf(String.format("%.2f", dssummary.getCapacity() / 1024 / 1024 / 1024.0));
                        freeSize += Float.valueOf(String.format("%.2f", dssummary.getFreeSpace() / 1024 / 1024 / 1024.0));
                        allocationSize += Float.valueOf(String.format("%.2f", (dssummary.getCapacity() - dssummary.getFreeSpace() + (dssummary.getUncommitted() == null ? 0 : dssummary.getUncommitted())) / 1024 / 1024 / 1024.0));
                    }
                }
            }
            host.setTotal_size(totalSize);
            host.setUsed_size(totalSize - freeSize);
            host.setAllocation_size(allocationSize);

            if (ObjectUtil.isNotEmpty(datastoreMors)) {
                datastoreMors.forEach(d -> {
                    CmdbStoragePoolRes storagePool = new CmdbStoragePoolRes();
                    storagePool.setCloud_type(accessBean.getCloudType());
                    storagePool.setAccount_id(accessBean.getCmpId());
                    storagePool.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), d.getValue()));
                    storagePool.setOpen_id(d.getValue());
                    associations.add(AssociationUtils.toAssociation(host, storagePool));
                });
            }

            /**
             * 获取宿主机关联云主机信息
             */
            List<ManagedObjectReference> vmMors = null;
            try {
                vmMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取宿主机vm信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(vmMors)) {
                vmMors.forEach(d -> {
                    CmdbInstanceRes instance = new CmdbInstanceRes();
                    instance.setCloud_type(accessBean.getCloudType());
                    instance.setAccount_id(accessBean.getCmpId());
                    instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0", d.getValue()));
                    instance.setOpen_id(d.getValue());
                    associations.add(AssociationUtils.toAssociation(host, instance));
                });
            }

            //获取宿主机关联交换机信息
            List<ManagedObjectReference> networkMors = null;
            try {
                networkMors = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
            } catch (Exception e) {
                log.error("获取宿主机network信息异常{}", e);
            }
            if (ObjectUtil.isNotEmpty(networkMors)) {
                networkMors.forEach(d -> {
                    try {
                        CmdbSubnetRes subnet = new CmdbSubnetRes();
                        subnet.setCloud_type(accessBean.getCloudType());
                        subnet.setAccount_id(accessBean.getCmpId());
                        subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), morManager.getDynamicProperty(d, "name").toString()));
                        subnet.setOpen_id(d.getValue());
                        associations.add(AssociationUtils.toAssociation(host, subnet));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (parentMor.getValue().contains("domain-c")) {
                ManagedObjectReference dcMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class, dcMor.getValue(), ResourceType.CMDB_HOST_RES.value(), host.getOpen_id()));
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, parentMor.getValue(), ResourceType.CMDB_HOST_RES.value(), host.getOpen_id()));
            }
        });
        //发送物理机数据
        BaseUtils.sendMessage(hosts, arguments);

        //发送宿主机关联关系
        BaseUtils.sendMessage(associations, arguments);

        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }


    //获取物理机列表并拆分dataJob
    public List<JobInfo> splitHostDataJob(DescribeVmwareRequest request, JSONObject arguments) {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> hostList = null;
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            hostList = morManager.getMorByType(rootRef, MORTypeEnum.HostSystem.toString());
            /** 拆分宿主机dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitDataJob(request, hostList, new String[]{ResourceEnum.HOST.getValue(), ResourceEnum.VSWITCH.getValue(), ResourceEnum.PROTGROUP.getValue(), ResourceEnum.NIC.getValue()},
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下物理机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下物理机异常");
        }
        return jobs;
    }

    public static BaseResponse addHost(JSONObject arguments) {
        String message = "成功发起添加主机请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateHostRequest request = BaseClient.bodys.get().toJavaObject(CreateHostRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        if (ObjectUtil.isEmpty(request.getHostIp())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "主机不能为空.");
        }
        try {
            VmwareUtils.addHost(connection, request);
        } catch (Exception e) {
            log.error("添加主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "添加主机息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse moveHost(JSONObject arguments) {
        String message = "成功发起移入主机请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        MoveHostRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(MoveHostRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        try {
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(request.getHostId());
            ManagedObjectReference clusterMor = new ManagedObjectReference();
            ManagedObjectReference resourceMor = null;
            if (request.getClusterId().startsWith("domain-")) {
                clusterMor = new ManagedObjectReference();
                clusterMor.setType(MORTypeEnum.ClusterComputeResource.toString());
                clusterMor.setValue(request.getClusterId());
            }
            if (request.getClusterId().startsWith("datacenter-")) {
                clusterMor = new ManagedObjectReference();
                clusterMor.setType(MORTypeEnum.Datacenter.toString());
                clusterMor.setValue(request.getClusterId());
            }

            try {
                connection.getVimPort().moveHostIntoTask(clusterMor, hostMor, resourceMor);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            log.error("添加主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移入主机息异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse scanHostDatastore(JSONObject arguments) {
        String message = "成功发起扫描主机数据存储请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        MoveHostRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(MoveHostRequest.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        try {
            if (connection != null) {
                ManagedObjectReference hostMor = new ManagedObjectReference();
                hostMor.setType("HostStorageSystem");
                hostMor.setValue("storageSystem-" + request.getHostId().split("-")[1]);
                try {
                    connection.getVimPort().rescanVmfs(hostMor);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            log.error("添加主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "扫描主机数据存储异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse stopHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().disconnectHostTask(hostMor);
            } catch (Exception e) {
                log.error("关闭主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse connectHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().reconnectHostTask(hostMor, null, null);
            } catch (Exception e) {
                log.error("连接主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "连接主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse disConnectHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().disconnectHostTask(hostMor);
            } catch (Exception e) {
                log.error("断开主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "断开主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse enterHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().enterMaintenanceModeTask(hostMor, 10, false, new HostMaintenanceSpec());
            } catch (Exception e) {
                log.error("进入维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "进入维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse exitHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().exitMaintenanceModeTask(hostMor, 10);
            } catch (Exception e) {
                log.error("退出维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "退出维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse removeHost(JSONObject arguments) {
        String message = "操作成功.";
        ManagedObjectReference hostMor = new ManagedObjectReference();
        //获取云账号信息
        CloudAccessBean accessBean = BaseClient.auths.get();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(hostId);
            try {
                connection.getVimPort().destroyTask(hostMor);
            } catch (Exception e) {
                log.error("移除主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移除主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }
}
