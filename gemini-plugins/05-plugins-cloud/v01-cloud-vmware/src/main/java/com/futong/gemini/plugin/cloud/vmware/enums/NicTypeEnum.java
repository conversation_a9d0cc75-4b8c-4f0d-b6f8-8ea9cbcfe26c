package com.futong.gemini.plugin.cloud.vmware.enums;

public enum NicTypeEnum {
    E1000("E1000", "E1000"),
    E1000E("E1000E", "E1000E"),
    VMXNET3("Vmxnet3", "Vmxnet3"),
    VMXNET2("Vmxnet2(Enhanced)", "Vmxnet2(增强)");
    private String key;
    private String label;

    NicTypeEnum(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }
}
