package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.route.RouteFactory;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.common.FetchConverts;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.common.model.OtcAetMessage;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.vmware.vim25.Event;
import com.vmware.vim25.EventFilterSpec;
import com.vmware.vim25.EventFilterSpecByTime;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.stream.Collectors;

;

@Slf4j
public class EventService {

    public static final EventService bean = new EventService();

    // 获取事件
    public String fetchEvent() throws Exception{

        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        morManager.setConnection(connection);
        ManagedObjectReference eventManagerRef = connection.getServiceContent().getEventManager();
        EventFilterSpec eventFilter_all = new EventFilterSpec();
        GregorianCalendar cal = new GregorianCalendar();
        Date date = new Date(new Date().getTime() - 300 * 1000l);
        cal.setTime(date);
        XMLGregorianCalendar gc = null;
        EventFilterSpecByTime time = new EventFilterSpecByTime();
        try {
            gc = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (Exception e) {
            log.error("获取事件初始时间异常", e);
        }
        time.setBeginTime(gc);
        eventFilter_all.setTime(time);
        ManagedObjectReference eventHistoryCollector_all = null;
        try {
            eventHistoryCollector_all = connection.getVimPort().createCollectorForEvents(eventManagerRef,
                    eventFilter_all);
        }catch (Exception e) {
            e.printStackTrace();
        }
        String message = "本次获取事件信息,本次获取条数：0";
        if(eventHistoryCollector_all!=null) {
            List<Event> eventList_all = connection.getVimPort().readNextEvents(eventHistoryCollector_all, 1000);
            List<EventInfoBean> eventList = eventList_all.stream().filter(t -> {
                        return !t.getFullFormattedMessage().contains("身份登录")
                                && !t.getFullFormattedMessage().contains("已注销")
                                && !t.getFullFormattedMessage().contains("logged in")
                                && !t.getFullFormattedMessage().contains("logged out");
                    })
                    .map(t -> FetchConverts.toEvent(t, accessBean, morManager)).collect(Collectors.toList());
            toAetMessageAndSend(eventList, "event");
            message = StrUtil.format("本次获取事件信息,本次获取条数：{}", eventList.size());
        }
        return message;
    }

    public static <T> OtcAetMessage<T> toAetMessageAndSend(List<T> res, String type) {
        OtcAetMessage<T> message = new OtcAetMessage<>();
        message.setType(type);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routeAetMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }
}
