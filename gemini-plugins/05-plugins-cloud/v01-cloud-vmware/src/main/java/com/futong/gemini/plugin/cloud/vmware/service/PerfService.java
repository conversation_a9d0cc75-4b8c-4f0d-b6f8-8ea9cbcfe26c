package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.*;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.convert.Converts;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.PageSortSearchRequest;
import com.futong.gemini.plugin.cloud.vmware.util.DateUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwarePerfUtil;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.ResourceDataNic;
import com.futong.gemini.model.otc.nxc.entity.ResourceDataStore;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;

import javax.xml.datatype.XMLGregorianCalendar;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

;

@Slf4j
public class PerfService {

    public static final PerfService bean = new PerfService();

    //同步物理机性能数据
    public static BaseResponse fetchHostPerf(JSONObject arguments) {
        String message = "成功获取物理机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取物理机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取物理机列表
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> response = ApiFactory.Api.res.listHostStoragePool(searchRequest);

        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        //获取性能数据并推送到数据队列
        fetchAndSendHostPerf(response.getData().getList(), arguments);
        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            return new GourdJobResponse(JobUtils.bean.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }

    //同步云主机性能数据
    public static BaseResponse fetchInstancePerf(JSONObject arguments) {
        String message = "成功获取云主机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取云主机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取云主机列表
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> response = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        fetchAndSendInstancePerf(response.getData().getList(), arguments);
        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            return new GourdJobResponse(JobUtils.bean.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }


    private static void fetchAndSendHostPerf(List<ResHostStoragePoolApiModel> instances, JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        //从缓存内获取连接
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        //获取vCenter的当前时间
        XMLGregorianCalendar sxgcal = null;
        try {
            sxgcal = VmwareUtils.bean.getXMLGregorianCalendar(connection);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //获取vCenter版本
        String version = "";
        try {
            version = connection.getServiceContent().getAbout().getVersion();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ResourceDataStore> datastorePerfs = new ArrayList<>();
        List<ResourceDataNic> nicList = new ArrayList<>();
        //获取vCenter的性能查询指标
        Map<Integer, VmwarePerfUtil.KpiInfo> kpiInfoMap = VmwarePerfUtil.bean.getperfKpi(version).stream().collect(Collectors.toMap(VmwarePerfUtil.KpiInfo::getId, Function.identity()));
        //获取性能查询条件
        List<PerfQuerySpec> perfQuerylist = VmwareUtils.bean.getPerfQuerySpec(instances, connection, sxgcal, version);
        Long writeTime = sxgcal.toGregorianCalendar().getTime().getTime();
        if (ObjectUtil.isNotEmpty(perfQuerylist)) {
            Map<String, ResHostStoragePoolApiModel> hostMap = instances.stream()
                    .collect(Collectors.toMap(ResHostStoragePoolApiModel::getRes_id, Function.identity()));
            try {
                //获取性能数据
                List<PerfEntityMetricBase> pembs = connection.getVimPort().queryPerf(connection.getServiceContent().getPerfManager(), perfQuerylist);
                if (ObjectUtil.isNotEmpty(pembs)) {
                    pembs.forEach(p -> {
                        PerfEntityMetric pem = (PerfEntityMetric) p;
                        String hostId = pem.getEntity().getValue();
                        ResHostStoragePoolApiModel host = hostMap.get(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), hostId));
                        if (ObjectUtil.isNull(host))
                            return;
                        List<PerfMetricSeries> vals = pem.getValue();

                        //初始化性能对象
                        ResourcePerfDetail perf = initResourcePerfDetail(accessBean, host, writeTime);
                        //初始化主机存储性能
                        Map<String,ResourceDataStore> storePerfs = initResourceDataStore(accessBean, host, writeTime);
                        //初始化主机网卡性能
                        Map<String, ResourceDataNic> nicPerfs = initResourceDataNic(accessBean, host, writeTime);

                        Map<String, String> dsMap = initDatastore(accessBean);
                        //云上获取的性能数据赋值
                        assignmentPerf(vals, kpiInfoMap, perf,dsMap,storePerfs,nicPerfs);
                        if(storePerfs!=null) {
                            //map循环
                            storePerfs.forEach((k, v) -> {
                                datastorePerfs.add(v);
                            });
                        }
                        if(nicPerfs!=null) {
                            //map循环
                            nicPerfs.forEach((k, v) -> {
                                nicList.add(v);
                            });
                        }
                        perf.setNetIo(perf.getNetIn() + perf.getNetOut());
                        perf.setDiskIo(perf.getDiskRead() + perf.getDiskWrite());
                        Float diskSize = 0.0f;
                        Float diskUsed = 0.0f;
                        if (ObjectUtil.isNotEmpty(host.getStorages())) {
                            for (ResStoragePoolApiModel disk : host.getStorages()) {
                                diskSize = diskSize + (disk.getTotal_size() == null ? 0 : disk.getTotal_size());
                                diskUsed = diskUsed + (disk.getUsed_size() == null ? 0 : disk.getUsed_size());
                            }
                        }
                        perf.setDiskSize(diskSize.doubleValue());
                        perf.setDiskUsage(0d);
                        if (diskSize > 0 && diskSize > diskUsed) {
                            perf.setDiskUsage(Double.valueOf(String.format("%.2f", 100 * diskUsed / diskSize)));
                        }
                        perf.setId(IdUtils.encryptId(accessBean.getCmpId(), host.getRes_id(), perf.getCreateTime()));
                        perfList.add(perf);
                    });
                }
            } catch (RuntimeFaultFaultMsg e) {
                log.error("获取虚拟机性能数据异常{}", e);
            }
            hostMap.clear();
        }
        kpiInfoMap.clear();
        //推送性能到数据队列
//        BaseUtils.sendMessage(perfList, arguments);
        log.info("推送vmware物理机监控数据{}条", perfList.size());

        BaseUtils.toPerfMessageAndSend(perfList, "API");
        log.info("推送vmware物理机存储监控数据{}条", datastorePerfs.size());
        BaseUtils.toDsPerfMessageAndSend(datastorePerfs, "API", accessBean.getCloudType(),accessBean.getCmpId(),DateUtils.timestampToStr(writeTime),ResourceType.CMDB_HOST_RES.value());
        log.info("推送vmware物理机网卡数据{}条", nicList.size());
        BaseUtils.toNicPerfMessageAndSend(nicList, "API", accessBean.getCloudType(),accessBean.getCmpId(),DateUtils.timestampToStr(writeTime),ResourceType.CMDB_HOST_RES.value());
    }

    private static void fetchAndSendInstancePerf(List<ResInstanceDiskApiModel> instances, JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        //从缓存内获取连接
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        //获取vCenter的当前时间
        XMLGregorianCalendar sxgcal = null;
        try {
            sxgcal = VmwareUtils.bean.getXMLGregorianCalendar(connection);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //获取vCenter版本
        String version = "";
        try {
            version = connection.getServiceContent().getAbout().getVersion();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ResourceDataStore> datastorePerfs = new ArrayList<>();
        List<ResourceDataNic> nicList = new ArrayList<>();
        //获取vCenter的性能查询指标
        Map<Integer, VmwarePerfUtil.KpiInfo> kpiInfoMap = VmwarePerfUtil.bean.getperfKpi(version).stream().collect(Collectors.toMap(VmwarePerfUtil.KpiInfo::getId, Function.identity()));
        //获取性能查询条件
        List<PerfQuerySpec> perfQuerylist = new ArrayList<>();
        try {
            perfQuerylist = VmwareUtils.bean.getPerfQuerySpec(instances, connection, sxgcal, version);
        }catch (Exception e) {
            e.printStackTrace();
        }
        Long writeTime = sxgcal.toGregorianCalendar().getTime().getTime();
        try {

            if (ObjectUtil.isNotEmpty(perfQuerylist)) {
                Map<String, ResInstanceDiskApiModel> instanceMap = instances.stream()
                        .collect(Collectors.toMap(ResInstanceDiskApiModel::getRes_id, Function.identity()));
                List<PerfEntityMetricBase> pembs = new ArrayList<>();
                try {
                    //获取性能数据
                    pembs = connection.getVimPort().queryPerf(connection.getServiceContent().getPerfManager(), perfQuerylist);
                } catch (RuntimeFaultFaultMsg e) {
                    log.error("获取虚拟机性能数据异常{}", e);
                }
                if (ObjectUtil.isNotEmpty(pembs)) {
                    pembs.forEach(p -> {
                        PerfEntityMetric pem = (PerfEntityMetric) p;
                        String vmId = pem.getEntity().getValue();
                        ResInstanceDiskApiModel vminfo = instanceMap.get(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), "0", vmId));
                        if (ObjectUtil.isNull(vminfo))
                            return;
                        List<PerfMetricSeries> vals = pem.getValue();
                        //初始化性能对象
                        ResourcePerfDetail perf = initResourcePerfDetail(accessBean, vminfo, writeTime);
                        //初始化虚拟机存储性能
                        Map<String, ResourceDataStore> storePerfs = initResourceDataStore(accessBean, vminfo, writeTime);
                        //初始化虚拟机网卡性能
                        Map<String, ResourceDataNic> nicPerfs = initResourceDataNic(accessBean, vminfo, writeTime);
                        Map<String, String> dsMap = initDatastore(accessBean);
                        //云上获取的性能数据赋值
                        assignmentPerf(vals, kpiInfoMap, perf, dsMap, storePerfs, nicPerfs);
                        if (storePerfs != null) {
                            //map循环
                            storePerfs.forEach((k, v) -> {
                                datastorePerfs.add(v);
                            });
                        }
                        if (nicPerfs != null) {
                            //map循环
                            nicPerfs.forEach((k, v) -> {
                                nicList.add(v);
                            });
                        }
                        perf.setNetIo(perf.getNetIn() + perf.getNetOut());
                        perf.setDiskIo(perf.getDiskRead() + perf.getDiskWrite());
                        Float diskSize = 0.0f;
                        if (ObjectUtil.isNotEmpty(vminfo.getDisks())) {
                            for (ResDiskApiModel disk : vminfo.getDisks()) {
                                diskSize = diskSize + disk.getSize();
                            }
                        }
                        perf.setId(IdUtils.encryptId(accessBean.getCmpId(), vminfo.getRes_id(), perf.getCreateTime()));
                        perf.setDiskSize(diskSize.doubleValue());
                        perfList.add(perf);
                    });
                }
                instanceMap.clear();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        log.info("77777");
        kpiInfoMap.clear();
        //推送性能到数据队列
        log.info("推送vmware云主机监控数据:{}", perfList.size());
        BaseUtils.toPerfMessageAndSend(perfList, "API");

        log.info("推送vmware云主机存储监控数据{}条", datastorePerfs.size());
        BaseUtils.toDsPerfMessageAndSend(datastorePerfs, "API",accessBean.getCloudType(),accessBean.getCmpId(),DateUtils.timestampToStr(writeTime),ResourceType.CMDB_HOST_RES.value());
        log.info("推送vmware云主机网卡数据{}条", datastorePerfs.size());
        BaseUtils.toDsPerfMessageAndSend(nicList, "API",accessBean.getCloudType(),accessBean.getCmpId(),DateUtils.timestampToStr(writeTime),ResourceType.CMDB_HOST_RES.value());
    }

    private static void assignmentPerf(List<PerfMetricSeries> vals, Map<Integer, VmwarePerfUtil.KpiInfo> kpiInfoMap, ResourcePerfDetail perf,Map<String, String> dsMap,Map<String,ResourceDataStore> storePerfsMap,Map<String, ResourceDataNic> nicPerfsMap) {
        if (ObjectUtil.isNotEmpty(vals)) {
            vals.forEach(v -> {
                PerfMetricIntSeries intSeries = (PerfMetricIntSeries) v;
                List<Long> longs = intSeries.getValue();
                if ("cpu.usage.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    if ("".equals(intSeries.getId().getInstance())) {
                        double value = calculateAverage(longs);
                        perf.setCpuUsage(Double.valueOf(String.format("%.2f", value / 100.0f)));
                    }
                } else if ("mem.usage.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    if ("".equals(intSeries.getId().getInstance())) {
                        double value = calculateAverage(longs);
                        perf.setMemUsage(Double.valueOf(String.format("%.2f", value / 100.0f)));
                    }
                } else if ("net.received.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if ("".equals(intSeries.getId().getInstance())) {

                        if (ObjectUtil.isNotNull(perf.getNetIn()))
                            perf.setNetIn(perf.getNetIn() + Double.valueOf(String.format("%.2f", 1.0 * value)));
                        else
                            perf.setNetIn(Double.valueOf(String.format("%.2f", 1.0 * value)));
                    }else{
                        String resId = IdUtils.encryptId(perf.getAccountId(), perf.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), perf.getOpenId(),intSeries.getId().getInstance());
                        if(nicPerfsMap.get(resId)!=null)
                            nicPerfsMap.get(resId).setNetIn(Double.valueOf(String.format("%.2f", 1.0 * value)));
                    }
                } else if ("net.transmitted.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if ("".equals(intSeries.getId().getInstance())) {
                        if (ObjectUtil.isNotNull(perf.getNetOut()))
                            perf.setNetOut(perf.getNetOut() + Double.valueOf(String.format("%.2f", 1.0 * value)));
                        else
                            perf.setNetOut(Double.valueOf(String.format("%.2f", 1.0 * value)));
                    }else {
                        String resId = IdUtils.encryptId(perf.getAccountId(), perf.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), perf.getOpenId(),intSeries.getId().getInstance());
                        if(nicPerfsMap.get(resId)!=null)
                            nicPerfsMap.get(resId).setNetOut(Double.valueOf(String.format("%.2f", 1.0 * value)));
                    }
                } else if ("virtualDisk.read.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if (ObjectUtil.isNotNull(perf.getDiskRead()))
                        perf.setDiskRead(perf.getDiskRead() + value);
                    else
                        perf.setDiskRead(value);
                } else if ("virtualDisk.write.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if (ObjectUtil.isNotNull(perf.getDiskWrite()))
                        perf.setDiskWrite(perf.getDiskWrite() + value);
                    else
                        perf.setDiskWrite(value);
                } else if ("datastore.read.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                        storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setReadAverage(Double.parseDouble(value + ""));
                        }catch (Exception e) {
                        }
                    }
                } else if ("datastore.write.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                        storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setWriteAverage(Double.parseDouble(value + ""));
                        }catch (Exception e) {
                        }
                    }
                } else if ("datastore.totalReadLatency.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                        storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setReadLatency(Double.parseDouble(value + ""));
                        }catch (Exception e) {
                        }
                    }
                } else if ("datastore.totalWriteLatency.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                        storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setWriteLatency(Double.parseDouble(value + ""));
                        }catch (Exception e) {

                        }
                    }
                } else if ("datastore.numberReadAveraged.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                            storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setIopsRead(Double.parseDouble(value + ""));
                        }catch (Exception e) {

                        }
                    }
                } else if ("datastore.numberWriteAveraged.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                            storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setIopsWrite(Double.parseDouble(value + ""));
                        }catch (Exception e) {

                        }
                    }
                } else if ("datastore.maxTotalLatency.average".equals(kpiInfoMap.get(intSeries.getId().getCounterId()).getValue())) {
                    double value = calculateAverage(longs);
                    if(intSeries.getId().getInstance()!=null){
                        try {
                        storePerfsMap.get(dsMap.get(intSeries.getId().getInstance())).setLongTime(Double.parseDouble(value + ""));
                        }catch (Exception e) {

                        }
                    }
                }
            });
        }
    }

    private static double calculateAverage(List<Long> longs) {
        if (longs == null || longs.isEmpty()) {
            return 0.0;
        }
        double sum = 0.0;
        for (Long value : longs) {
            sum += value;
        }
        return sum / longs.size();
    }

    private static <T> Map<String,ResourceDataStore> initResourceDataStore(CloudAccessBean accessBean, T info, long writeTime) {
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        Map<String,ResourceDataStore> storMap = new HashMap<>();
        if (info instanceof ResHostStoragePoolApiModel) {
            ResHostStoragePoolApiModel res = (ResHostStoragePoolApiModel) info;
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(res.getOpen_id());
            List<ManagedObjectReference> datastores;
            try {
                datastores = (ArrayList) morManager.getDynamicProperty(hostMor, "datastore");
            } catch (Exception e) {
                log.error("获取物理机storage信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取物理机storage信息异常");
            }
            for(ManagedObjectReference datastore : datastores) {
                ResourceDataStore perf = new ResourceDataStore();
                try {
                    DatastoreSummary summary = (DatastoreSummary)morManager.getDynamicProperty(datastore, "summary");
                    long capacity = summary.getCapacity();
                    long freeSpace = summary.getFreeSpace();
                    perf.setDataStoreUsage(Double.valueOf(String.format("%.2f", 100f * (capacity-freeSpace) / capacity)));
                }catch (Exception e) {
                    e.printStackTrace();
                }
                perf.setResId(res.getRes_id());
                perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                perf.setCloudType(accessBean.getCloudType());
                perf.setCreateTime(DateUtils.timestampToStr(writeTime));
                perf.setAccountId(accessBean.getCmpId());
                perf.setDataStoreResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), datastore.getValue()));
                perf.setId(IdUtils.encryptId(accessBean.getCmpId(), perf.getResId(), perf.getDataStoreResId()));
                perf.setWriteLatency(0d);
                perf.setWriteAverage(0d);
                perf.setReadLatency(0d);
                perf.setReadAverage(0d);
                perf.setLongTime(0d);
                perf.setIopsWrite(0d);
                perf.setIopsRead(0d);
                storMap.put(datastore.getValue(),perf);
            }
        }else if (info instanceof ResInstanceDiskApiModel) {
            ResInstanceDiskApiModel res = (ResInstanceDiskApiModel) info;
            ManagedObjectReference vmMor = new ManagedObjectReference();
            vmMor.setType(MORTypeEnum.VirtualMachine.toString());
            vmMor.setValue(res.getOpen_id());
            List<ManagedObjectReference> datastores;
            try {
                datastores = (ArrayList) morManager.getDynamicProperty(vmMor, "datastore");
            } catch (Exception e) {
                log.error("获取云主机storage信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机storage信息异常");
            }
            for(ManagedObjectReference datastore : datastores) {
                ResourceDataStore perf = new ResourceDataStore();
                try {
                    DatastoreSummary summary = (DatastoreSummary)morManager.getDynamicProperty(datastore, "summary");
                    long capacity = summary.getCapacity();
                    long freeSpace = summary.getFreeSpace();
                    perf.setDataStoreUsage(Double.valueOf(String.format("%.2f", 100f * (capacity-freeSpace) / capacity)));
                }catch (Exception e) {
                    e.printStackTrace();
                }
                perf.setResId(res.getRes_id());
                perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                perf.setCloudType(accessBean.getCloudType());
                perf.setCreateTime(DateUtils.timestampToStr(writeTime));
                perf.setAccountId(accessBean.getCmpId());
                perf.setDataStoreResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), datastore.getValue()));
                perf.setId(IdUtils.encryptId(accessBean.getCmpId(), perf.getResId(), perf.getDataStoreResId()));
                perf.setWriteLatency(0d);
                perf.setWriteAverage(0d);
                perf.setReadLatency(0d);
                perf.setReadAverage(0d);
                perf.setLongTime(0d);
                perf.setIopsWrite(0d);
                perf.setIopsRead(0d);
                storMap.put(datastore.getValue(),perf);
            }
        }

        return storMap;
    }

    private static <T> Map<String,ResourceDataNic> initResourceDataNic(CloudAccessBean accessBean, T info, long writeTime) {
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        Map<String,ResourceDataNic> nicMap = new HashMap<>();
        if (info instanceof ResHostStoragePoolApiModel) {
            ResHostStoragePoolApiModel res = (ResHostStoragePoolApiModel) info;
            ManagedObjectReference hostMor = new ManagedObjectReference();
            hostMor.setType(MORTypeEnum.HostSystem.toString());
            hostMor.setValue(res.getOpen_id());
            List<PhysicalNic> nics;
            try {
                nics = (List<PhysicalNic>)morManager.getDynamicProperty(hostMor, "config.network.pnic");
            } catch (Exception e) {
                log.error("获取主机网卡信息异常{}",e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取主机网卡信息异常");
            }
            for(PhysicalNic nic : nics) {
                ResourceDataNic perf = new ResourceDataNic();
                perf.setResId(res.getRes_id());
                perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                perf.setCloudType(accessBean.getCloudType());
                perf.setCreateTime(DateUtils.timestampToStr(writeTime));
                perf.setAccountId(accessBean.getCmpId());
                perf.setNicResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), hostMor.getValue(), nic.getDevice()));
                perf.setId(IdUtils.encryptId(accessBean.getCmpId(), perf.getResId(), perf.getNicResId()));
                perf.setNetIn(0d);
                perf.setNetOut(0d);
                nicMap.put(perf.getNicResId(),perf);
            }
        }else if (info instanceof ResInstanceDiskApiModel) {
            ResInstanceDiskApiModel res = (ResInstanceDiskApiModel) info;
            ManagedObjectReference vmMor = new ManagedObjectReference();
            vmMor.setType(MORTypeEnum.VirtualMachine.toString());
            vmMor.setValue(res.getOpen_id());
            VirtualMachineConfigInfo vmConfig =null;
            try {
                vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}",e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
            }
            List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
            for(VirtualDevice d : devices) {
                String id = "";
                if (d instanceof VirtualE1000) {
                    VirtualE1000 ve = (VirtualE1000) d;
                    id = ve.getKey()+"";
                } else if (d instanceof VirtualE1000E) {
                    VirtualE1000E ve = (VirtualE1000E) d;
                    id = ve.getKey()+"";
                } else if (d instanceof VirtualVmxnet3) {
                    VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                    id = ve.getKey()+"";
                } else if (d instanceof VirtualVmxnet2) {
                    VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                    id = ve.getKey()+"";
                } else {
                    continue;
                }
                ResourceDataNic perf = new ResourceDataNic();
                perf.setResId(res.getRes_id());
                perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                perf.setCloudType(accessBean.getCloudType());
                perf.setCreateTime(DateUtils.timestampToStr(writeTime));
                perf.setAccountId(accessBean.getCmpId());
                perf.setNicResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), vmMor.getValue(), id));
                perf.setId(IdUtils.encryptId(accessBean.getCmpId(), perf.getResId(), perf.getNicResId()));
                perf.setNetIn(0d);
                perf.setNetOut(0d);
                nicMap.put(perf.getNicResId(),perf);
            }
        }
        return nicMap;
    }

    private static <T> ResourcePerfDetail initResourcePerfDetail(CloudAccessBean accessBean, T info, long writeTime) {
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        ResourcePerfDetail perf = new ResourcePerfDetail();
        if (info instanceof ResInstanceDiskApiModel) {
            ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
            perf.setResId(vminfo.getRes_id());
            perf.setOpenId(vminfo.getOpen_id());
            perf.setOpenName(vminfo.getOpen_name());
            perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
            perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
            ManagedObjectReference vmMor = new ManagedObjectReference();
            vmMor.setType(MORTypeEnum.VirtualMachine.toString());
            vmMor.setValue(vminfo.getOpen_id());
            VirtualMachineStorageSummary summary = null;
            try {
                summary = (VirtualMachineStorageSummary) morManager.getDynamicProperty(vmMor, "summary.storage");
            } catch (Exception e) {
                log.error("获取云主机summary.storage信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary.storage信息异常");
            }

            if (ObjectUtil.isNotNull(summary)) {
                float capacity = 0;
                float used = 0f;
                if (summary != null) {
                    capacity = summary.getUncommitted() + summary.getCommitted();
                    used = summary.getCommitted();
                }
                if (capacity > 0)
                    perf.setDiskUsage(Double.valueOf(String.format("%.2f", used * 100 / capacity)));
                else
                    perf.setDiskUsage(0d);
            }
            perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        } else if (info instanceof ResHostStoragePoolApiModel) {
            ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
            perf.setResId(host.getRes_id());
            perf.setOpenId(host.getOpen_id());
            perf.setOpenName(host.getOpen_name());
            perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
            perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
        }
        perf.setCloudType(accessBean.getCloudType());
        perf.setCreateTime(DateUtils.timestampToStr(writeTime));
        perf.setAccountId(accessBean.getCmpId());
        perf.setDiskSize(0d);
        perf.setCpuUsage(0d);
        perf.setMemUsage(0d);
        perf.setNetIn(0d);
        perf.setNetOut(0d);
        perf.setNetIo(0d);
        perf.setDiskIo(0d);
        perf.setDiskRead(0d);
        perf.setDiskWrite(0d);
        return perf;
    }

    public static Map<String, String> initDatastore(CloudAccessBean accessBean) {
        MorManager morManager = new MorManager();
        VcConnection connection = (VcConnection)GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        morManager.setConnection(connection);
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        try {
            List<ManagedObjectReference> datastoreList = morManager.getMorByType(rootRef, MORTypeEnum.Datastore.toString());
            return datastoreList.stream().collect(Collectors.toMap(mor -> VmwareUtils.getDatastoreKey(mor, morManager), mor -> mor.getValue()));
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
