package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.vmware.util.ConnectionUtils;
import com.futong.gemini.plugin.cloud.vmware.util.JobUtils;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.vmware.vim25.DVPortgroupConfigInfo;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.NetworkSummary;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class NetworkSubnetService {

    public static final NetworkSubnetService bean = new NetworkSubnetService();

    // 获取子网（端口组）信息
    public void fetchSubnet(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbSubnetRes> subnets = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(subnetId -> {
            if(subnetId.startsWith("network-")) {
                ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.Network.toString(), subnetId);
                CmdbSubnetRes subnet = new CmdbSubnetRes();
                subnet.setCloud_type(accessBean.getCloudType());
                subnet.setAccount_id(accessBean.getCmpId());
                subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), subnetId));
                subnet.setOpen_id(subnetId);
                subnet.setOpen_name(VmwareUtils.getName(subnetMor, morManager));

                NetworkSummary summary = null;
                try {
                    summary = (NetworkSummary) morManager.getDynamicProperty(subnetMor, "summary");
                } catch (Exception e) {
                    log.error("获取子网信息异常{}", e);
                }
                subnet.setStatus("active");
                if (ObjectUtil.isNotNull(summary)) {
                    subnet.setStatus(summary.isAccessible() ? "active" : "inactive");
                }
                subnet.setVlan_id(summary.getIpPoolId()+"");
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(subnetMor, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机parent信息异常{}", e);
                }
                if (!parentMor.getValue().contains("datacenter-")) {
                    parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                }
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class, parentMor.getValue(), ResourceType.CMDB_SUBNET_RES.value(), VmwareUtils.getName(subnetMor, morManager)));
//                subnets.add(subnet);
            }else {
                ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.DistributedVirtualPortgroup.toString(), subnetId);
                CmdbSubnetRes subnet = new CmdbSubnetRes();
                subnet.setCloud_type(accessBean.getCloudType());
                subnet.setAccount_id(accessBean.getCmpId());
                subnet.setStatus("active");
                subnet.setOpen_id(subnetId);
                DVPortgroupConfigInfo config = null;
                try {
                    config = (DVPortgroupConfigInfo) morManager.getDynamicProperty(subnetMor, "config");
                } catch (Exception e) {
                    log.error("获取子网信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(config)) {
                    subnet.setOpen_name(config.getName());
                    subnet.setPort(config.getNumPorts()+"");
                }
                subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), subnet.getOpen_name()));
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(subnetMor, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机parent信息异常{}", e);
                }
                if (!parentMor.getValue().contains("datacenter-")) {
                    parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                }
                resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class, parentMor.getValue(), ResourceType.CMDB_SUBNET_RES.value(), subnet.getOpen_name()));
                subnets.add(subnet);
            }
        });
        //发送子网（端口组）数据
        log.info("同步端口组数量---{}", subnets.size());
        BaseUtils.sendMessage(subnets, arguments);
        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }
    public void fetchDvSubnet(DescribeVmwareRequest request, JSONObject arguments) {
        List<CmdbSubnetRes> subnets = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(subnetId -> {
            ManagedObjectReference subnetMor = VmwareUtils.bean.getMor(MORTypeEnum.Network.toString(), subnetId);
            CmdbSubnetRes subnet = new CmdbSubnetRes();
            subnet.setCloud_type(accessBean.getCloudType());
            subnet.setAccount_id(accessBean.getCmpId());
            subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), subnetId));
            subnet.setOpen_id(subnetId);
            subnet.setOpen_name(VmwareUtils.getName(subnetMor, morManager));

            NetworkSummary summary = null;
            try {
                summary = (NetworkSummary) morManager.getDynamicProperty(subnetMor, "summary");
            } catch (Exception e) {
                log.error("获取子网信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                subnet.setStatus(summary.isAccessible() ? "active" : "inactive");
            }

            subnet.setVlan_id(summary.getIpPoolId()+"");
            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(subnetMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (!parentMor.getValue().contains("datacenter-")) {
                parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
            }
            resourceSets.add(CiResCloudUtils.toTmdbResourceSet(accessBean, DevopsSide.DEVOPS_DATA_CENTER.value(), TmdbDevops.class, parentMor.getValue(), ResourceType.CMDB_SUBNET_RES.value(), VmwareUtils.getName(subnetMor, morManager)));
            subnets.add(subnet);
        });
        //发送子网（端口组）数据
//        BaseUtils.sendMessage(subnets, arguments);
        //推送北新仓与南新仓关联数据
        BaseUtils.sendMessage(resourceSets, arguments);
    }

    //获取子网（端口组）列表并拆分dataJob
    public List<JobInfo> splitSubnetDataJob(DescribeVmwareRequest request, JSONObject arguments) {
        VcConnection connection = ConnectionUtils.getConnection(BaseClient.auths.get());
        ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        List<ManagedObjectReference> networkList = null;
        List<ManagedObjectReference> dvnetworkList = null;
        List<ManagedObjectReference> nets = new ArrayList<>();
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            networkList = morManager.getMorByType(rootRef, MORTypeEnum.Network.toString());
            try {
                dvnetworkList = morManager.getMorByType(rootRef, MORTypeEnum.DistributedVirtualPortgroup.toString());
            }catch (Exception e) {
                e.printStackTrace();
            }
            if(networkList!=null) {
                nets.addAll(networkList);
            }
            if(dvnetworkList!=null) {
                nets.addAll(dvnetworkList);
            }
            /** 拆分子网dataJob */
            jobs = Stream.of(jobs,
                    JobUtils.bean.splitDataJob(request, nets, new String[]{ResourceEnum.SUBNET.getValue()},
                            arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取" + connection.getHost() + "下子网异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下子网异常");
        }
        return jobs;
    }

}
