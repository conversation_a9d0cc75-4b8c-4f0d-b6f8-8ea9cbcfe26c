package com.futong.gemini.plugin.cloud.vmware.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.DescribeVmwareRequest;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbIpRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbNetcardRes;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.VirtualMachineConfigInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkNicService {

    public static final NetworkNicService bean = new NetworkNicService();

    // 获取网卡信息
    public  void  fetchNic(DescribeVmwareRequest request, JSONObject arguments){
        List<CmdbNetcardRes> nets = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CloudAccessBean accessBean = BaseClient.auths.get();
        MorManager morManager = new MorManager();
        morManager.setConnection((VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId()));
        request.getIds().forEach(vmId->{
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(),vmId);
            VirtualMachineConfigInfo vmConfig =null;
            try {
                vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}",e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
            }
            //获取网卡信息，同步获取IP信息、网卡和云主机的关系数据、网卡与IP的关系数据
            VmwareUtils.bean.getCmdbNetcard(resourceSets,nets, ips, associations,morManager,vmMor,vmConfig,accessBean);
        });
        //发送网卡数据
        BaseUtils.sendMessage(nets,arguments);

        //发送IP数据
        BaseUtils.sendMessage(ips,arguments);

        //发送网卡与云主机、网卡与IP、网卡与子网(端口组)关系数据
        BaseUtils.sendMessage(associations,arguments);

        //南北新仓资源关系数据
        BaseUtils.sendMessage(resourceSets,arguments);
    }

}
