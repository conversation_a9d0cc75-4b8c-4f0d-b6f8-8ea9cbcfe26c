package com.futong.gemini.plugin.cloud.vmware.connection;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.vmware.connection.exception.ConnectionException;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.vmware.common.ssl.TrustAllTrustManager;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.apache.cxf.transports.http.configuration.ProxyServerType;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.handler.MessageContext;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class VcConnection implements Connection, Serializable {
    private static final long serialVersionUID = 1L;
    private VimService vimService;
    private VimPortType vimPort;
    private ServiceContent serviceContent;
    private UserSession userSession;
    private ManagedObjectReference svcInstRef;
    private URL url;
    private String username;
    private String password = ""; // default password is empty since on rare
    @SuppressWarnings("rawtypes")
    private Map headers;

    private long createTime;

    private String status;

    private String token;

    private String proxyHost;
    private String proxyPort;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public VcConnection(CloudAccessBean config) {

        this.username = config.getUsername();
        this.password = config.getPassword();
        String jsonStr = config.getJsonStr();
        if(jsonStr!=null) {
            JSONObject json = JSONObject.parseObject(config.getJsonStr());
            if(json.containsKey("proxyHost"))
                this.proxyHost = json.getString("proxyHost");
            if(json.containsKey("proxyPort"))
                this.proxyPort = json.getString("proxyPort");
        }
        try {
            this.url = new URL(config.getProtocol() + "://" + config.getServerIp() + ":" + config.getServerPort() + "/sdk");
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
    }

    public static VcConnection initialize(CloudAccessBean config) throws Exception {
        VcConnection connection = new VcConnection(config);
        return connection;
    }

    public VimService getVimService() {
        return vimService;
    }

    public VimPortType getVimPort() {
        return vimPort;
    }

    public ServiceContent getServiceContent() {
        return serviceContent;
    }

    public UserSession getUserSession() {
        return userSession;
    }

    public String getServiceInstanceName() {
        return "ServiceInstance";
    }

    @SuppressWarnings("rawtypes")
    public Map getHeaders() {
        return headers;
    }

    public ManagedObjectReference getServiceInstanceReference() {
        if (svcInstRef == null) {
            ManagedObjectReference ref = new ManagedObjectReference();
            ref.setType(this.getServiceInstanceName());
            ref.setValue(this.getServiceInstanceName());
            svcInstRef = ref;
        }
        return svcInstRef;
    }

    public Connection connect(String url, String username, String password) {
        try {
            this.url = new URL(url);
        } catch (MalformedURLException e1) {
            e1.printStackTrace();
        }
        this.username = username;
        this.password = password;
        if (!isConnected()) {
            try {
                _connect();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return this;
    }

    public static void trustAllHttpsCertificates() {
        try {
            javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[1];
            javax.net.ssl.TrustManager tm = new TrustAllTrustManager();
            trustAllCerts[0] = tm;
            javax.net.ssl.SSLContext sc = javax.net.ssl.SSLContext
                    .getInstance("SSL");
            javax.net.ssl.SSLSessionContext sslsc = sc
                    .getServerSessionContext();
            sslsc.setSessionTimeout(0);
            sc.init(null, trustAllCerts, null);
            HttpsURLConnection.setDefaultSSLSocketFactory(sc
                    .getSocketFactory());
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    @SuppressWarnings("rawtypes")
    private void _connect() throws RuntimeFaultFaultMsg, InvalidLocaleFaultMsg, InvalidLoginFaultMsg {
        vimService = new VimService();
        vimPort = vimService.getVimPort();
        trustAllCertificates(vimPort);
        //验证正确代理
        if(StrUtil.isNotEmpty(proxyHost)&&StrUtil.isNotEmpty(proxyPort)) {
            configureProxy(vimPort, proxyHost, Integer.parseInt(proxyPort));
        }
        //验证错误代理
//        configureProxy(vimPort, "***********", 8080);
        // 创建并设置自定义 ProxySelector
        Map<String, Object> ctxt = ((BindingProvider) vimPort).getRequestContext();
        ctxt.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, url.toString());
        ctxt.put(BindingProvider.SESSION_MAINTAIN_PROPERTY, true);
        // 创建自定义HTTP传输管道
        serviceContent = vimPort.retrieveServiceContent(this.getServiceInstanceReference());
        userSession = vimPort.login(serviceContent.getSessionManager(), username, password, null);
        headers = (Map) ((BindingProvider) vimPort).getResponseContext().get(MessageContext.HTTP_RESPONSE_HEADERS);
    }
    /**
     * 配置 HTTP/HTTPS 代理
     */
    private static void configureProxy(VimPortType port,String proxyHost, int proxyPort) {
        // 获取 CXF 的 HTTPConduit
        HTTPConduit httpConduit = (HTTPConduit) ClientProxy.getClient(port).getConduit();

        // 创建 HTTPClientPolicy
        HTTPClientPolicy clientPolicy = new HTTPClientPolicy();

        // ✅ 设置代理服务器和端口
        clientPolicy.setProxyServer(proxyHost);
        clientPolicy.setProxyServerPort(proxyPort);

        // ✅ 如果是 HTTP 代理，设置协议
        clientPolicy.setProxyServerType(ProxyServerType.HTTP);

        // ✅ 可选：设置代理认证（Basic Auth）
//        if (PROXY_USERNAME != null && !PROXY_USERNAME.isEmpty()) {
//            clientPolicy.setProxyAuthorization("Basic " +
//                    javax.xml.bind.DatatypeConverter.printBase64Binary(
//                            (PROXY_USERNAME + ":" + PROXY_PASSWORD).getBytes()
//                    )
//            );
//        }

        // ✅ 应用策略
        httpConduit.setClient(clientPolicy);

        System.out.println("✅ 代理已配置: http://" + proxyHost + ":" + proxyPort);
    }
    /**
            * 配置 CXF 客户端信任所有 SSL 证书（包括自签名证书）
            * ⚠️ 仅用于测试！生产环境禁用！
            *
            * @param port VimPortType 实例
     */
    private static void trustAllCertificates(VimPortType port) {
        try {
            // 获取 CXF 的 HTTPConduit
            HTTPConduit httpConduit = (HTTPConduit) ClientProxy.getClient(port).getConduit();

            // 创建 TLSClientParameters
            TLSClientParameters tlsParams = new TLSClientParameters();

            // ✅ 1. 设置一个信任所有证书的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        public X509Certificate[] getAcceptedIssuers() {
                            return null; // 返回 null 表示接受所有 issuer
                        }

                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                            // 什么都不做，信任所有客户端证书（通常不需要）
                        }

                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                            // 什么都不做，信任所有服务器证书
                            System.out.println("⚠️ SSL 警告：信任了服务器证书: " + certs[0].getSubjectDN());
                        }
                    }
            };

            // ✅ 2. 设置信任管理器
            tlsParams.setTrustManagers(trustAllCerts);

            // ✅ 3. 跳过主机名验证（如果证书 CN/SAN 不匹配 IP 或域名）
            tlsParams.setDisableCNCheck(true);

            // ✅ 4. 应用配置
            httpConduit.setTlsClientParameters(tlsParams);

            System.out.println("✅ 已配置 CXF 信任所有 SSL 证书（跳过验证）");

        } catch (Exception e) {
            throw new RuntimeException("Failed to configure trust all certificates", e);
        }
    }
    public boolean isConnected() {
        if (userSession == null) {
            return false;
        }
        long startTime = userSession.getLastActiveTime().toGregorianCalendar().getTime().getTime();
        return new Date().getTime() < startTime + 30 * 60 * 1000;
    }

    public Connection disconnect() {
        if (this.isConnected()) {
            try {
                vimPort.logout(serviceContent.getSessionManager());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                userSession = null;
                serviceContent = null;
                vimPort = null;
                vimService = null;
            }
        }
        return this;
    }

    public URL getURL() {
        return this.url;
    }

    private class BasicConnectionException extends ConnectionException {
        private static final long serialVersionUID = 1L;

        public BasicConnectionException(String s, Throwable t) {
            super(s, t);
        }
    }

    public Connection connect() {
        if (!isConnected()) {
            try {
                _connect();
            } catch (Exception e) {
                Throwable cause = (e.getCause() != null) ? e.getCause() : e;
                throw new BasicConnectionException("failed to connect: " + e.getMessage() + " : " + cause.getMessage(),
                        cause);
            }
        }
        return this;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public void setUrl(String url) {
        try {
            this.url = new URL(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getUrl() {
        return url.toString();
    }

    public String getHost() {
        return url.getHost();
    }

    public Integer getPort() {
        int port = url.getPort();
        return port;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsername() {
        return username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword() {
        return this.password;
    }

    public static void main(String[] args) throws Exception {
//        ProxySelector.setDefault(new VMwareProxySelector("*************", 8080, "************"));
//        System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump","true");
// 启用JAX-WS调试
//		// 启用HTTP调试
        // 设置 HTTP 代理
//        System.setProperty("http.proxyHost", "*************");
//        System.setProperty("http.proxyPort", "8080");
//
//        // 设置 HTTPS 代理
//        System.setProperty("https.proxyHost", "*************");
//        System.setProperty("https.proxyPort", "8080");
        CloudAccessBean cloudAccessBean = new CloudAccessBean();
        cloudAccessBean.setProtocol("https");
        cloudAccessBean.setServerIp("************");
        cloudAccessBean.setServerPort("443");
        cloudAccessBean.setUsername("<EMAIL>");
        cloudAccessBean.setPassword("Futong@123");
        VcConnection connection = new VcConnection(cloudAccessBean);
        connection.connect();

        MorManager morManager = new MorManager();
        morManager.setConnection(connection);
        //获取集群列表
        List<ManagedObjectReference> clusterList = null;
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            clusterList = morManager.getMorByType(rootRef, MORTypeEnum.ClusterComputeResource.toString());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下集群异常");
        }

        System.out.println("成功获取集群视图" + clusterList.size());
    }
}
