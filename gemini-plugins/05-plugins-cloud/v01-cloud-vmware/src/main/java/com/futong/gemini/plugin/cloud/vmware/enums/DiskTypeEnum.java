package com.futong.gemini.plugin.cloud.vmware.enums;

public enum DiskTypeEnum {
    THIN("thin", "精简置备"),
    THICK("thick", "厚置备"),
    THICK_EAGER("thick_eager", "厚置备置零"),
    THICK_LAZY("thick_lazy", "厚置备延迟置零");
    private String key;
    private String label;

    DiskTypeEnum(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }
}
