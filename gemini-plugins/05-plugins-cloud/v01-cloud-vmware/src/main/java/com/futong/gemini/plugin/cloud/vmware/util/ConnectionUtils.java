package com.futong.gemini.plugin.cloud.vmware.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConnectionUtils {

    public  static VcConnection getConnection(CloudAccessBean bean){
        VcConnection vcConnection =null;
        try {
            vcConnection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, BaseClient.auths.get().getCmpId());
        }catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.AUTO, BaseClient.auths.get().getCmpId());
        }
        if(ObjectUtil.isNotNull(vcConnection)){
            log.info("从缓存中获取到vCenter连接,vCenter地址 = {}",vcConnection.getHost());
            return vcConnection;
        }else{
            VcConnection  connection = connection(bean);
            return connection;
        }
    }
    public static VcConnection connection(CloudAccessBean bean){
        VcConnection  connection = new VcConnection(bean);
        try {
            connection.connect();
            String version  = connection.getServiceContent().getAbout().getVersion();
            log.info("vCenter连接成功,vCenter地址 = {},版本 = {}",connection.getHost(),version);
                /*VsanHealthConnection vsanConnection = new VsanHealthConnection(bean);
                vsanConnection.connect();*/
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.AUTO, BaseClient.auths.get().getCmpId(),connection);
        }catch (Exception e){
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD,e,"连接服务【"+bean.getServerIp()+"】异常");
        }
        return connection;
    }
}
