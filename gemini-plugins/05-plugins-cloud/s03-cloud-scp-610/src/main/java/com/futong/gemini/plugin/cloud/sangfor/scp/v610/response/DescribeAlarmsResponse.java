package com.futong.gemini.plugin.cloud.sangfor.scp.v610.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.BaseResponseBodyExt;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.BaseResponseData;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeAlarmsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeAlarmsResponseBody body;

    @Data
    public static class DescribeAlarmsResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeAlarmsResponseBodyData data;
    }

    @Data
    public static class DescribeAlarmsResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Alarm> data;
    }

    @Data
    public static class Alarm extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("level")
        public String level;

        @NameInMap("policy_id")
        public String policyId;

        @NameInMap("policy_name")
        public String policyName;

        @NameInMap("start_time")
        public String startTime;

        @NameInMap("end_time")
        public String endTime;

        @NameInMap("object_type")
        public String objectType;

        @NameInMap("object_id")
        public String objectId;

        @NameInMap("object_name")
        public String objectName;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("az_name")
        public String azName;

        @NameInMap("description")
        public String description;

        @NameInMap("alarm_advice")
        public String alarmAdvice;

        @NameInMap("converge_count")
        public Integer convergeCount;

        @NameInMap("status")
        public String status;
    }

}

