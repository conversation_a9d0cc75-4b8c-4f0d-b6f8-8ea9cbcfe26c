package com.futong.gemini.plugin.cloud.sangfor.scp.v610;

import com.futong.constant.dict.CloudType;
import com.futong.gemini.plugin.cloud.sdk.annotation.CT;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@CT(value = CloudType.PRIVATE_SCP, version = "6.10.0")
public class ScpPluginTemplate extends com.futong.gemini.plugin.cloud.sangfor.scp.ScpPluginTemplate {

    @Override
    public BaseCloudRegister getRegister() {
        return new ScpRegister();
    }

}
