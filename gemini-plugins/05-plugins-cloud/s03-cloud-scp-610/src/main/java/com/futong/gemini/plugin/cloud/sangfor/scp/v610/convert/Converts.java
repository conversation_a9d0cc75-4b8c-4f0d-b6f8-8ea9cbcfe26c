package com.futong.gemini.plugin.cloud.sangfor.scp.v610.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.futong.common.redis.FTRedisUtils;
import com.futong.constant.dict.*;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeAlarmsResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeInstancesResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class Converts {
    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, DescribeInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_INSTANCE_RES
                );
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<CmdbDiskRes> disks = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbFlavor> flavors = new ArrayList<>();
        for (DescribeInstancesResponse.Instance res : response.body.data.data) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setCpu_size(res.getCores());
            ci.setMem_size(res.getMemoryMb());
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            if ("running".equals(res.getStatus()) && res.getStorageStatus() != null) {
                FTRedisUtils.setDouble(com.futong.gemini.plugin.cloud.sangfor.scp.convert.Converts.SCP_DISK_USAGE + res.getId(), NumberUtil.mul(res.getStorageStatus().getRatio().floatValue() ,100), 60 * 60 * 2);
            }
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            toCiResCloud(request, ci);
            setStatus(res.getStatus(), ci);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_RESOURCEPOOL, res.getAzId());
            if (CollUtil.isNotEmpty(res.getDisks())) {
                for (DescribeInstancesResponse.Disk diskRes : res.getDisks()) {
                    CmdbDiskRes disk = new CmdbDiskRes();
                    disk.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId(), diskRes.getId()));
                    disk.setType(diskRes.getPhysicalDiskType());
                    disk.setCategory("data");
                    disk.setSize(diskRes.getSizeMb() / 1024);
                    disk.setStatus("use");
                    disk.setStore_name(diskRes.getStorageFile());
                    disk.setLun_id(diskRes.getStorageId());
                    disk.setOpen_id(diskRes.getId());
                    disk.setOpen_name(diskRes.getStorageName());
                    toCiResCloud(request, disk);
                    disks.add(disk);

                    Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, disk.getRes_id());
                    associations.add(diskAss);
                }
            }
            if (ObjectUtil.isNotEmpty(res.getGpuConf())) {
                CmdbFlavor flavor = new CmdbFlavor();
                flavor.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbFlavor.class.getSimpleName(), res.getId()));
                if (res.getOsOption() != null) flavor.setCpu_arch(res.getOsOption().getArch());
                flavor.setCpu_size(res.getCores());
                flavor.setMem_size(res.getMemoryMb());
                flavor.setCategory("GPU");
                flavor.setGpu_size(Float.valueOf(res.getGpuConf().getVgpuMemSize()));
                flavor.setGpu_num(res.getGpuConf().getGpuCores());
                flavor.setGpu_model(res.getGpuConf().getGpuName());
                flavor.setOpen_id(res.getId());
                flavor.setOpen_name(res.getId());
                toCiResCloud(request, flavor);
                flavors.add(flavor);

                Association flavorAss = AssociationUtils.toAssociation(ci, CmdbFlavor.class, flavor.getRes_id());
                associations.add(flavorAss);
            }
            //关联镜像
            if (StrUtil.isNotEmpty(res.getImageId())) {
                Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getImageId()));
                associations.add(image);
            }
            //操作新系统OS对象
            if (ObjectUtil.isNotEmpty(res.getOsOption())) {
                //关联镜像OS
                String distributionName = res.getOsDistribution();
                String osName = StrUtil.isEmpty(distributionName) ? StrUtil.emptyToDefault(res.getOsName(), res.getOsOption().getDistributionName()) : distributionName;
                CmdbOsRes os = new CmdbOsRes();
                os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), osName));
                os.setType(osName);
                os.setCpu_arch(res.getOsOption().getArch());
                os.setVersion(res.getOsOption().getKernelName());
                os.setFull_name(osName);
                os.setOpen_name(StrUtil.emptyToDefault(res.getOsName(), res.getOsOption().getDistributionName()));
                os.setOpen_id(osName);
                toCiResCloud(request, os);
                osList.add(os);
                Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
                associations.add(osa);
            }
            //关联主机
            associations.add(AssociationUtils.toAssociation(ci, CmdbHostRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getHostId())));
            if (CollUtil.isNotEmpty(res.getNetworks())) {
                for (DescribeInstancesResponse.Network network : res.getNetworks()) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getIpAddress()));
                    ip.setType(IpType.PRIVATE_IP.value());
                    ip.setAddress(network.getIpAddress());
                    ip.setMac(network.getMacAddress());
                    ip.setDesc(network.getDeviceId());
                    ip.setOpen_id(network.getIpAddress());
                    ip.setOpen_name(network.getName());
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联网络VPC
                    if (StrUtil.isNotEmpty(network.getVpcId())) {
                        associations.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getVpcId())));
                    }
                    //关联网络VPC子网
                    if (StrUtil.isNotEmpty(network.getSubnetId())) {
                        associations.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), network.getSubnetId())));
                    }
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ci, ip));
                }
            }
            //添加公网弹性IP,关联弹性IP
            if (ObjectUtil.isNotNull(res.getFloatingip()) && StrUtil.isNotEmpty(res.getFloatingip().getFloatingIpAddress())) {
                CmdbIpRes ip = new CmdbIpRes();
                DescribeInstancesResponse.FloatingIp eip = res.getFloatingip();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getFloatingipId()));
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(eip.getFloatingIpAddress());
                ip.setOpen_id(eip.getFloatingipId());
                ip.setOpen_name(eip.getFloatingIpAddress());
                toCiResCloud(request, ip);
                ips.add(ip);
                //IP关联云主机
                associations.add(AssociationUtils.toAssociation(ci, ip));
                //云主机关联EIP
                Association association = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getFloatingipId()));
                associations.add(association);
            }
            data.add(ci);
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbDiskRes.class, disks);
        result.put(CmdbIpRes.class, ips);
        result.put(CmdbOsRes.class, osList);
        result.put(CmdbFlavor.class, flavors);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    private static void setStatus(String status, CmdbInstanceRes ci) {
        switch (status) {
            case "creating":
                ci.setStatus(InstanceStatus.BUILDING.value());
                break;
            case "running":
                ci.setStatus(InstanceStatus.RUNNING.value());
                break;
            case "starting":
                ci.setStatus(InstanceStatus.STARTING.value());
                break;
            case "stoping":
                ci.setStatus(InstanceStatus.STOPPING.value());
                break;
            case "resetting":
                ci.setStatus(InstanceStatus.RESTARTING.value());
                break;
            case "deleting":
                ci.setStatus(InstanceStatus.DELETING.value());
                break;
            case "migrate":
                ci.setStatus(InstanceStatus.MIGRATING.value());
                break;
            case "recovery":
                ci.setStatus(InstanceStatus.RECOVERING.value());
                break;
            case "stopped":
                ci.setStatus(InstanceStatus.STOPPED.value());
                break;
            case "suspend":
                ci.setStatus(InstanceStatus.SUSPENDED.value());
                break;
            default:
                ci.setStatus(InstanceStatus.UNKNOWN.value());
                break;
        }
    }

    public static Map<Class, List> convertAlarm(BaseCloudRequest request, DescribeAlarmsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.data || CollUtil.isEmpty(response.body.data.data)) {
            result.put(AlarmInfoBean.class, null);
            return result;
        }
        List<AlarmInfoBean> data = new ArrayList<>();
        for (DescribeAlarmsResponse.Alarm alarm : response.body.data.data) {
            AlarmInfoBean ci = new AlarmInfoBean();
            ci.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alarm.getId()));
            ci.setAccountId(request.getBody().getAccess().getCmpId());
            ci.setCloudType(request.getPlugin().getRealm());
            ci.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alarm.getId()));
            ci.setOpenId(alarm.getId());
            ci.setOpenName(alarm.getObjectName());
            ci.setOpenLevel(alarm.getLevel());
            ci.setAlarmId(alarm.getPolicyId());
            ci.setAlarmName(alarm.getPolicyName());
            ci.setDetail(alarm.getDescription() + alarm.getAlarmAdvice());
            ci.setClosedStatus("close".equals(alarm.getStatus()));
            ci.setJsonInfo(JSON.toJSONString(alarm));
            ci.setCount(1);
            if (ObjectUtil.isNotEmpty(alarm.getStartTime())) {
                ci.setFirstTime(alarm.getStartTime());
                ci.setCreateTime(alarm.getStartTime());
            }
            ci.setResourceType(alarm.getObjectType());
            switch (alarm.getLevel()) {
                case "p1":
                    ci.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case "p2":
                    ci.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case "p3":
                    ci.setAlarmLevel(AlarmLevel.MINOR.value());
                    break;
            }
            data.add(ci);
        }
        result.put(AlarmInfoBean.class, data);
        return result;
    }
}