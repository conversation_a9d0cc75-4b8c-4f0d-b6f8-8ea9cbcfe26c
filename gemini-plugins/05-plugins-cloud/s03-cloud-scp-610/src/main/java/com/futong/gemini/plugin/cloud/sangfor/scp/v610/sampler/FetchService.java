package com.futong.gemini.plugin.cloud.sangfor.scp.v610.sampler;

import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.service.CloudService;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.client.EcsClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.convert.Converts;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeAlarmsResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeInstancesResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class FetchService extends com.futong.gemini.plugin.cloud.sangfor.scp.sampler.FetchService {

    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeInstances610,
                Converts::convertEcs);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

    /**
     * 告警
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        Entry.E2<DescribeAlarmsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeAlarms610,
                Converts::convertAlarm);
        BaseResponse response = CloudService.fetchAetSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.data.getTotalSize(),
                mapE2.v1.body.data.getPageSize());
    }

}
