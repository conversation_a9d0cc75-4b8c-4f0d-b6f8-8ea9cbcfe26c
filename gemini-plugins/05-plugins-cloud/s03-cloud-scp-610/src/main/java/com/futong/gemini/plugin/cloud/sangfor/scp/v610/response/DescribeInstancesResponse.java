package com.futong.gemini.plugin.cloud.sangfor.scp.v610.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.BaseResponseBodyExt;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.BaseResponseData;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeInstancesResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesResponseBody body;

    @Data
    public static class DescribeInstancesResponseBody extends BaseResponseBodyExt {
        @NameInMap("data")
        public DescribeInstancesResponseBodyData data;
    }

    @Data
    public static class DescribeInstancesResponseBodyData extends BaseResponseData {
        @NameInMap("data")
        public List<Instance> data;
    }

    @Data
    public static class Instance extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("type")
        public String type;

        @NameInMap("status")
        public String status;

        @NameInMap("os_type")
        public String osType;

        @NameInMap("os_name")
        public String osName;

        @NameInMap("os_distribution")
        public String osDistribution;

        @NameInMap("description")
        public String description;

        @NameInMap("uptime")
        public Integer uptime;

        @NameInMap("shutdown_duration")
        public Integer shutdownDuration;

        @NameInMap("ips")
        public List<String> ips;

        @NameInMap("user_id")
        public String userId;

        @NameInMap("user_name")
        public String userName;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("project_name")
        public String projectName;

        @NameInMap("group_id_path")
        public String groupIdPath;

        @NameInMap("group_name_path")
        public String groupNamePath;

        @NameInMap("cores_per_socket")
        public Integer coresPerSocket;

        @NameInMap("sockets")
        public Integer sockets;

        @NameInMap("cores")
        public Integer cores;

        @NameInMap("mhz")
        public Float mhz;

        @NameInMap("memory_mb")
        public Integer memoryMb;

        @NameInMap("disks")
        public List<Disk> disks;

        @NameInMap("networks")
        public List<Network> networks;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("az_name")
        public String azName;

        @NameInMap("dh_id")
        public String dhId;

        @NameInMap("dh_name")
        public String dhName;

        @NameInMap("host_id")
        public String hostId;

        @NameInMap("host_name")
        public String hostName;

        @NameInMap("image_id")
        public String imageId;

        @NameInMap("image_name")
        public String imageName;

        @NameInMap("storage_id")
        public String storageId;

        @NameInMap("storage_name")
        public String storageName;

        @NameInMap("storage_tag_id")
        public String storageTagId;

        @NameInMap("group_id")
        public String groupId;

        @NameInMap("group_name")
        public String groupName;

        @NameInMap("cpu_status")
        public CpuStatus cpuStatus;

        @NameInMap("memory_status")
        public MemoryStatus memoryStatus;

        @NameInMap("storage_status")
        public StorageStatus storageStatus;

        @NameInMap("io_status")
        public IoStatus ioStatus;

        @NameInMap("network_status")
        public NetworkStatus networkStatus;

        @NameInMap("advance_param")
        public AdvanceParam advanceParam;

        @NameInMap("os_installed")
        public Integer osInstalled;

        @NameInMap("vtool_installed")
        public Integer vtoolInstalled;

        @NameInMap("tags")
        public List<Tag> tags;

        @NameInMap("alarm")
        public Alarm alarm;

        @NameInMap("warning")
        public Warning warning;

        @NameInMap("in_protection")
        public Integer inProtection;

        @NameInMap("protection_id")
        public String protectionId;

        @NameInMap("protection_name")
        public String protectionName;

        @NameInMap("protection_type")
        public String protectionType;

        @NameInMap("protection_enable")
        public String protectionEnable;

        @NameInMap("backup_file_count")
        public Integer backupFileCount;

        @NameInMap("vmtype")
        public String vmtype;

        @NameInMap("iolog")
        public List<IoLog> iolog;

        @NameInMap("power_state")
        public String powerState;

        @NameInMap("is_stopped")
        public Integer isStopped;

        @NameInMap("schedule_policies")
        public List<SchedulePolicy> schedulePolicies;

        @NameInMap("location")
        public Integer location;

        @NameInMap("floatingip")
        public FloatingIp floatingip;

        @NameInMap("instant_vm")
        public String instantVm;

        @NameInMap("storage_mb")
        public Float storageMb;

        @NameInMap("expire_time")
        public String expireTime;

        @NameInMap("network_type")
        public List<String> networkType;

        @NameInMap("encrypted")
        public Integer encrypted;

        @NameInMap("cipher")
        public String cipher;

        @NameInMap("gpu_conf")
        public GpuConf gpuConf;

        @NameInMap("gpu_status")
        public GpuStatus gpuStatus;

        @NameInMap("has_gpu")
        public Integer hasGpu;

        @NameInMap("keypair")
        public Keypair keypair;

        @NameInMap("os_option")
        public OsOption osOption;

        @NameInMap("template_id")
        public String templateId;

        @NameInMap("storage_policy_id")
        public String storagePolicyId;

        @NameInMap("vm_id")
        public String vmId;

        @NameInMap("quotas")
        public List<Quota> quotas;
    }

    @Data
    public static class Disk extends TeaModel {
        @NameInMap("size_mb")
        public Float sizeMb;

        @NameInMap("preallocate")
        public String preallocate;

        @NameInMap("eagerly_scrub")
        public Integer eagerlyScrub;

        @NameInMap("id")
        public String id;

        @NameInMap("physical_disk_type")
        public String physicalDiskType;

        @NameInMap("storage_file")
        public String storageFile;

        @NameInMap("storage_id")
        public String storageId;

        @NameInMap("storage_name")
        public String storageName;

        @NameInMap("storage_tag_id")
        public String storageTagId;
    }

    @Data
    public static class Network extends TeaModel {
        @NameInMap("connect")
        public Integer connect;

        @NameInMap("device_id")
        public String deviceId;

        @NameInMap("model")
        public String model;

        @NameInMap("ip_address")
        public String ipAddress;

        @NameInMap("ipv6_address")
        public String ipv6Address;

        @NameInMap("mac_address")
        public String macAddress;

        @NameInMap("vif_id")
        public String vifId;

        @NameInMap("port_id")
        public String portId;

        @NameInMap("name")
        public String name;

        @NameInMap("vpc_id")
        public String vpcId;

        @NameInMap("vpc_name")
        public String vpcName;

        @NameInMap("network_type")
        public String networkType;

        @NameInMap("subnet_id")
        public String subnetId;

        @NameInMap("subnet_name")
        public String subnetName;

        @NameInMap("cidr")
        public String cidr;

        @NameInMap("subnet_gateway_ip")
        public String subnetGatewayIp;

        @NameInMap("custom_gateway_ip")
        public String customGatewayIp;
    }

    @Data
    public static class CpuStatus extends TeaModel {
        @NameInMap("total_mhz")
        public Float totalMhz;

        @NameInMap("used_mhz")
        public Float usedMhz;

        @NameInMap("ratio")
        public Float ratio;
    }

    @Data
    public static class MemoryStatus extends TeaModel {
        @NameInMap("total_mb")
        public Float totalMb;

        @NameInMap("used_mb")
        public Float usedMb;

        @NameInMap("ratio")
        public Float ratio;
    }

    @Data
    public static class StorageStatus extends TeaModel {
        @NameInMap("total_mb")
        public Float totalMb;

        @NameInMap("used_mb")
        public Float usedMb;

        @NameInMap("ratio")
        public Float ratio;

        @NameInMap("storage_file_size_mb")
        public Float storageFileSizeMb;
    }

    @Data
    public static class IoStatus extends TeaModel {
        @NameInMap("read_speed_byteps")
        public Float readSpeedByteps;

        @NameInMap("write_speed_byteps")
        public Float writeSpeedByteps;

        @NameInMap("read_iops")
        public Float readIops;

        @NameInMap("write_iops")
        public Float writeIops;
    }

    @Data
    public static class NetworkStatus extends TeaModel {
        @NameInMap("read_speed_bitps")
        public Float readSpeedBitps;

        @NameInMap("write_speed_bitps")
        public Float writeSpeedBitps;
    }

    @Data
    public static class AdvanceParam extends TeaModel {
        @NameInMap("balloon_memory")
        public Integer balloonMemory;

        @NameInMap("onboot")
        public Integer onboot;

        @NameInMap("schedopt")
        public Integer schedopt;

        @NameInMap("abnormal_recovery")
        public Integer abnormalRecovery;

        @NameInMap("vga")
        public String vga;
    }

    @Data
    public static class Tag extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;
    }

    @Data
    public static class Alarm extends TeaModel {
        @NameInMap("alarm")
        public Integer alarm;

        @NameInMap("alarm_info")
        public List<AlarmInfo> alarmInfo;
    }

    @Data
    public static class AlarmInfo extends TeaModel {
        @NameInMap("alarm_role_type")
        public String alarmRoleType;

        @NameInMap("severity_level")
        public String severityLevel;

        @NameInMap("object_type")
        public String objectType;

        @NameInMap("sub_object_type")
        public String subObjectType;

        @NameInMap("cluster_id")
        public String clusterId;

        @NameInMap("az_name")
        public String azName;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("id")
        public String id;

        @NameInMap("user_id")
        public String userId;

        @NameInMap("title")
        public String title;

        @NameInMap("object_id")
        public String objectId;

        @NameInMap("cluster_name")
        public String clusterName;

        @NameInMap("sub_object_name")
        public String subObjectName;

        @NameInMap("object_name")
        public String objectName;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("status")
        public String status;

        @NameInMap("resource_project_name")
        public String resourceProjectName;

        @NameInMap("description")
        public String description;

        @NameInMap("root_id")
        public String rootId;

        @NameInMap("resource_project_id")
        public String resourceProjectId;

        @NameInMap("begin_time")
        public String beginTime;

        @NameInMap("alarm_id")
        public String alarmId;

        @NameInMap("end_time")
        public String endTime;

        @NameInMap("resource_user_id")
        public String resourceUserId;
    }

    @Data
    public static class Warning extends TeaModel {
        @NameInMap("warning")
        public Integer warning;

        @NameInMap("warning_type")
        public String warningType;

        @NameInMap("warning_info")
        public String warningInfo;
    }

    @Data
    public static class IoLog extends TeaModel {
        @NameInMap("size_byte")
        public Float sizeByte;

        @NameInMap("storage_id")
        public String storageId;
    }

    @Data
    public static class SchedulePolicy extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("az_id")
        public String azId;

        @NameInMap("project_id")
        public String projectId;

        @NameInMap("name")
        public String name;

        @NameInMap("type")
        public String type;

        @NameInMap("resource_id")
        public String resourceId;

        @NameInMap("status")
        public String status;

        @NameInMap("enable")
        public Integer enable;

        @NameInMap("force")
        public Integer force;
    }

    @Data
    public static class FloatingIp extends TeaModel {
        @NameInMap("floatingip_id")
        public String floatingipId;

        @NameInMap("floating_ip_address")
        public String floatingIpAddress;

        @NameInMap("bind_status")
        public String bindStatus;

        @NameInMap("line_type")
        public String lineType;

        @NameInMap("line_type_id")
        public String lineTypeId;

        @NameInMap("bandwidth")
        public Integer bandwidth;

        @NameInMap("sharedbandwidth_name")
        public String sharedbandwidthName;

        @NameInMap("sharedbandwidth_id")
        public String sharedbandwidthId;
    }

    @Data
    public static class GpuConf extends TeaModel {
        @NameInMap("auth_require")
        public String authRequire;

        @NameInMap("gpu_cores")
        public Integer gpuCores;

        @NameInMap("gpu_mem_size_single_core")
        public String gpuMemSizeSingleCore;

        @NameInMap("gpu_name")
        public String gpuName;

        @NameInMap("gpu_type")
        public String gpuType;

        @NameInMap("total_count")
        public Integer totalCount;

        @NameInMap("vgpu_mem_size")
        public Integer vgpuMemSize;

        @NameInMap("vgpu_type")
        public String vgpuType;

        @NameInMap("schedulers")
        public List<String> schedulers;
    }

    @Data
    public static class GpuStatus extends TeaModel {
        @NameInMap("graphics_count")
        public Integer graphicsCount;

        @NameInMap("graphics_ratio")
        public Float graphicsRatio;

        @NameInMap("graphics_mem_ratio")
        public Float graphicsMemRatio;

        @NameInMap("graphics_mem_total")
        public Float graphicsMemTotal;

        @NameInMap("graphics_mem_used")
        public Float graphicsMemUsed;
    }

    @Data
    public static class Keypair extends TeaModel {
        @NameInMap("id")
        public String id;

        @NameInMap("name")
        public String name;

        @NameInMap("type")
        public String type;

        @NameInMap("user_id")
        public String userId;

        @NameInMap("public_key")
        public String publicKey;

        @NameInMap("created_at")
        public String createdAt;

        @NameInMap("fingerprint")
        public String fingerprint;
    }

    @Data
    public static class OsOption extends TeaModel {
        @NameInMap("kernel_name")
        public String kernelName;

        @NameInMap("distribution_name")
        public String distributionName;

        @NameInMap("arch")
        public String arch;
    }

    @Data
    public static class Quota extends TeaModel {
        @NameInMap("resource")
        public String resource;

        @NameInMap("value")
        public Integer value;
    }
}

