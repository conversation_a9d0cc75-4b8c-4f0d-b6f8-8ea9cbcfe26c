package com.futong.gemini.plugin.cloud.sangfor.scp.v610.client;

import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeAlarmsResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.response.DescribeInstancesResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class EcsClient extends com.futong.gemini.plugin.cloud.sangfor.scp.client.EcsClient {

    public EcsClient(Config config) throws Exception {
        super(config);
    }

    public DescribeInstancesResponse describeInstances610(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20210725/servers", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeInstancesResponse());
    }

    public DescribeAlarmsResponse describeAlarms610(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRequest("HTTPS", "query", "/janus/20190725/alarm-histories", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeAlarmsResponse());
    }

}
