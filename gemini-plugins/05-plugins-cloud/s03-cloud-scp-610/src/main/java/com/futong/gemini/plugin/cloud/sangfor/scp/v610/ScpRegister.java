package com.futong.gemini.plugin.cloud.sangfor.scp.v610;

import com.futong.gemini.plugin.cloud.sangfor.scp.v610.sampler.FetchService;
import com.futong.gemini.plugin.cloud.sangfor.scp.v610.service.CloudService;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;

public class ScpRegister extends com.futong.gemini.plugin.cloud.sangfor.scp.ScpRegister {

    @Override
    public void onAfterLoadFetch() {
        super.onAfterLoadFetch();
        //同步告警
        registerBefore(ActionType.FETCH_PLATFORM_ALARM,
                CloudService::defaultStartEndTimeOneDay);
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);
        //同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);
    }

}
