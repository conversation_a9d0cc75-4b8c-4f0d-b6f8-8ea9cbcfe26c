package com.futong.gemini.plugin.cloud.sangfor.scp.v610.service;

import cn.hutool.core.date.DateUtil;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public class CloudService extends com.futong.gemini.plugin.cloud.sangfor.scp.service.CloudService {

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        request.getBody().getCloud().remove("begin_time");
        if (!request.getBody().getCloud().containsKey("start_time")) {
            request.getBody().getCloud().put("start_time", DateUtil.offsetDay(new Date(), -1).toString("yyyy-MM-dd HH:mm:ss"));//一天前
        }
        if (!request.getBody().getCloud().containsKey("end_time")) {
            request.getBody().getCloud().put("end_time", DateUtil.date().toString("yyyy-MM-dd HH:mm:ss"));//当前时间
        }
        return true;
    }
}
