package com.futong.gemini.plugin.cloud.ali.tencent.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.tencentcloudapi.cvm.v20170312.models.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import static java.util.stream.Collectors.toList;

@Slf4j
public class Converts {

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> metrics = new HashMap<>();
    private static Entry.E2<String, String>[] metricType = new Entry.E2[]{
            new Entry.E2("Average", "average"),
            new Entry.E2("Maximum", "max"),
            new Entry.E2("Minimum", "min"),
            new Entry.E2("Value", "value"),
            new Entry.E2("Sum", "sum"),
    };

    static {
        metrics.put("CpuUsage", new Entry.E3("cpuUsage", metricType, "%"));
        metrics.put("MemUsage", new Entry.E3("memUsage", metricType, "%"));
        metrics.put("CvmDiskUsage", new Entry.E3("diskUsage", metricType, "%"));
        metrics.put("DiskReadTrafficNew", new Entry.E3("diskRead", metricType, "KB/s"));
        metrics.put("DiskWriteTrafficNew", new Entry.E3("diskWrite", metricType, "KB/s"));
        metrics.put("LanOuttraffic", new Entry.E3("netIn", metricType, "Mbps"));
        metrics.put("LanIntraffic", new Entry.E3("netOut", metricType, "Mbps"));
//        metrics.put("VPC_PublicIP_InternetInRate", new E3("ipInRate", metricType, "bit/s"));
//        metrics.put("VPC_PublicIP_InternetOutRate", new E3("ipOutRate", metricType, "bit/s"));
    }

    public static Map<String, BiConsumer<ResourcePerfDetail, Double>> perfMapping = new HashMap<>();
    static {
        perfMapping.put("CpuUsage", ResourcePerfDetail::setCpuUsage);
        perfMapping.put("MemUsage", ResourcePerfDetail::setMemUsage);
        perfMapping.put("CvmDiskUsage", ResourcePerfDetail::setDiskUsage);
        perfMapping.put("DiskReadTrafficNew", ResourcePerfDetail::setDiskRead);
        perfMapping.put("DiskWriteTrafficNew", ResourcePerfDetail::setDiskWrite);
        perfMapping.put("LanOuttraffic", ResourcePerfDetail::setNetIn);
        perfMapping.put("LanIntraffic", ResourcePerfDetail::setNetOut);
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMappingBean = new HashMap<>();
    static {
        perfMappingBean.put("CpuUsage", PerfInfoBean::setCpuUsage);
        perfMappingBean.put("MemUsage", PerfInfoBean::setMemUsage);
        perfMappingBean.put("CvmDiskUsage", PerfInfoBean::setDiskUsage);
        perfMappingBean.put("DiskReadTrafficNew", PerfInfoBean::setDiskRead);
        perfMappingBean.put("DiskWriteTrafficNew", PerfInfoBean::setDiskWrite);
        perfMappingBean.put("LanOuttraffic", PerfInfoBean::setNetIn);
        perfMappingBean.put("LanIntraffic", PerfInfoBean::setNetOut);
    }

    //转换创建云主机转换的请求数据
    public static RunInstancesRequest toRun(JSONObject requestBody) {
        RunInstancesRequest obj = new RunInstancesRequest();
//        obj.setRegionId(requestBody.getString("regionId"));
        Placement placement = new Placement();
        placement.setZone(requestBody.getString("zoneId"));
        obj.setPlacement(placement);
        obj.setInstanceType(requestBody.getString("flaverId"));
        obj.setImageId(requestBody.getString("imageId"));
        //系统盘
        SystemDisk sysDisk = new SystemDisk();
        sysDisk.setDiskSize(Long.valueOf(requestBody.getString("rootVolumeSize")));
        sysDisk.setDiskType(requestBody.getString("rootVolumeType"));
        obj.setSystemDisk(sysDisk);

        //实例基础信息
        obj.setHostName(requestBody.getString("hostName"));
        obj.setInstanceName(requestBody.getString("resourceName"));
        obj.setInstanceCount(Long.valueOf(requestBody.getInteger("count")));
//        obj.setUniqueSuffix(requestBody.getBoolean("uniqueSuffix"));
//        obj.setDescription(requestBody.getString("description"));
        //实例网络信息d
        List<String> securitygroupIds = requestBody.getJSONArray("securitygroupIds").stream().map(Object::toString).collect(toList());
        obj.setSecurityGroupIds(securitygroupIds.toArray(new String[securitygroupIds.size()]));
        InternetAccessible internetAccessible = new InternetAccessible();
        internetAccessible.setInternetMaxBandwidthOut(Long.valueOf(requestBody.getInteger("maxBandwidt")));
        internetAccessible.setInternetChargeType(requestBody.getString("internetChargeType"));
        VirtualPrivateCloud virtualPrivateCloud = new VirtualPrivateCloud();
        virtualPrivateCloud.setSubnetId((String) requestBody.getJSONArray("subnetIds").get(0));
        //实例密码信息
        LoginSettings loginSettings = new LoginSettings();
        loginSettings.setPassword(requestBody.getString("adminPass"));
        loginSettings.setKeyIds(requestBody.getString("keyPair").split(","));
        if (StrUtil.isEmpty(loginSettings.getPassword()) && loginSettings.getKeyIds().length == 0) {
            loginSettings.setKeepImageLogin("true");
        }
        obj.setLoginSettings(loginSettings);
        return obj;
    }

}