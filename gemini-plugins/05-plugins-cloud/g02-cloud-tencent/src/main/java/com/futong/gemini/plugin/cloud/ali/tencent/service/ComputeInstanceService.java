package com.futong.gemini.plugin.cloud.ali.tencent.service;

import cn.hutool.core.lang.Opt;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.tencent.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import com.tencentcloudapi.cvm.v20170312.models.DescribeInstancesModificationResponse;
import com.tencentcloudapi.cvm.v20170312.models.InstanceTypeConfigStatus;


public class ComputeInstanceService {
    public static BaseResponse queryUpdateComputeInstanceFlavor(BaseCloudRequest request) {
        DescribeInstancesModificationResponse result = CloudClient.client.execute(request.getBody(), CvmClient::DescribeInstancesModification);
        InstanceTypeConfigStatus[] supportedResources = Opt.ofNullable(result)
                .map(t -> t.getInstanceTypeConfigStatusSet()).orElse(null);
        return new BaseDataResponse<>(supportedResources);
    }
}
