package com.futong.gemini.plugin.cloud.ali.tencent.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.ali.tencent.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.tencentcloudapi.cvm.v20170312.models.RunInstancesResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public class CloudService {

    public static <Q, R, C> FTAction<BaseCloudRequest> toFTAction(FTExecute<C, Q, R> exec) {
        return (BaseCloudRequest body) -> doAction(body, exec);
    }

    public static <Q, R, C> BaseDataResponse<R> doAction(BaseCloudRequest request, FTExecute<C, Q, R> exec) {
        try {
            return new BaseDataResponse<>(CloudClient.client.execute(request.getBody(), exec));
        } catch (Exception e) {
            String message = request.getAction().operationType().cname() + request.getAction().resourceType().name() + "失败";
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }

    public static boolean defaultRegion(BaseCloudRequest request) {
        request.getBody().getCloud().put("regionId", "ap-beijing");//默认北京
        return true;
    }

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", 0);
        }
        if (!request.getBody().getCloud().containsKey("limit")) {
            request.getBody().getCloud().put("limit", 100);
        }
        return true;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", DateUtil.offsetDay(new Date(), -1).getTime());//一天前
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", new Date().getTime());//当前时间
        }
        return true;
    }

    public static void toBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) return;
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
            if (dataResponse.getData() instanceof RunInstancesResponse) {
                RunInstancesResponse runInstancesResponse = (RunInstancesResponse) data;
                if (runInstancesResponse.getInstanceIdSet() == null
                        || runInstancesResponse.getInstanceIdSet().length == 0) {
                    return;
                }
                JSONObject biz = request.getBody().getBiz();
                biz.put("resId",IdUtils.encryptId(request.getBody().getAccess().getCmpId(), runInstancesResponse.getInstanceIdSet()[0]));
                dataResponse.withData(biz);
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            }
        }
    }
}
