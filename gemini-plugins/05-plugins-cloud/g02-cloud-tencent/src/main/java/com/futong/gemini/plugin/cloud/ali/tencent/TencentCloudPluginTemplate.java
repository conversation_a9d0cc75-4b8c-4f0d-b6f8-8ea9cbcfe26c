package com.futong.gemini.plugin.cloud.ali.tencent;

import com.futong.common.log.DynamicLoggerConfigurator;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudPluginTemplate;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class TencentCloudPluginTemplate extends BaseCloudPluginTemplate {

    @Override
    public void init(String key) {
        super.init(key);
        //指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.tencent", "/cloud/tencent");

    }

    @Override
    public BaseCloudRegister getRegister() {
        return new TencentCloudRegister();
    }

}
