package com.futong.gemini.plugin.cloud.ali.tencent.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.account.CloudAccessBeanExt;
import com.qcloud.cos.COS;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.region.Region;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.net.MalformedURLException;
import java.net.URL;

@Slf4j
public class ClientUtils extends BaseClient {

    public static BaseClient base = new ClientUtils();

    private static final String cosEndpoint = "cos.{}";

    @Override
    public <C> C client(Class<C> clazz, JSONObject q) {
        try {
            //获取认证对象
            JSONObject request = arguments.get();
            CloudAccessBean accessBean = auths.get();
            CloudAccessBeanExt accessBeanExt = authExts.get();
            String regionId = regions.get();
            String endpoint = request.getString("endpoint");
            HttpProfile httpProfile = new HttpProfile();
            if (clazz == COSClient.class) {
                endpoint = StrUtil.emptyToDefault(endpoint, StrUtil.format(cosEndpoint, regionId));
                return (C) cosClient(accessBean, regionId, endpoint);
            }
            if ("http".equalsIgnoreCase(accessBean.getProtocol())) {
                httpProfile.setProtocol(HttpProfile.REQ_HTTP);
            }
            if (StrUtil.isNotEmpty(accessBeanExt.getProxyAddr())) {
                try {
                    URL url = new URL(accessBeanExt.getProxyAddr());
                    httpProfile.setProxyHost(url.getHost());
                    httpProfile.setProxyPort(url.getPort());
                } catch (MalformedURLException e) {
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT, e, "代理地址格式错误！");
                }
            }
            Credential cred = new Credential(accessBean.getUsername(), accessBean.getPassword());
            Constructor<C> constructor = clazz.getConstructor(Credential.class, String.class, ClientProfile.class);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            return constructor.newInstance(cred, regionId, clientProfile);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        }
    }

    @Override
    public <Q, R, C> R execute(C client, Q q, FTExecute<C, Q, R> exec) {
        try {
            return exec.apply(client, q);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        } finally {
            if (client instanceof COSClient) {
                ((COSClient) client).shutdown();
            }
        }
    }

    private static COS cosClient(CloudAccessBean cloudClient, String regionId, String endpoint) {
        try {
            if (StrUtil.isEmpty(endpoint) && StrUtil.isNotEmpty(regionId)) {
                endpoint = StrUtil.format(endpoint, regionId);
            }
            log.info("ossEndpoint:" + endpoint);
            return new COSClient(new BasicCOSCredentials(cloudClient.getUsername(), cloudClient.getPassword()), new ClientConfig(new Region(endpoint)));
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        }
    }
}
