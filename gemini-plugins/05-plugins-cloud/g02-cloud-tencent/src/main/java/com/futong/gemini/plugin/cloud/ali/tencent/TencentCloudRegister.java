package com.futong.gemini.plugin.cloud.ali.tencent;

import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.ali.tencent.sampler.FetchService;
import com.futong.gemini.plugin.cloud.ali.tencent.service.AccountService;
import com.futong.gemini.plugin.cloud.ali.tencent.service.CloudService;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import com.tencentcloudapi.cbs.v20170312.CbsClient;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import com.tencentcloudapi.vpc.v20170312.VpcClient;

public class TencentCloudRegister extends BaseCloudRegister {

    public <Q, R, C> Builder register(ActionType actionType, FTExecute<Q, R, C> execute) {
        return register(actionType, execute, CloudService::toFTAction);
    }

    @Override
    public void load() {
        onAfterLoadAccount();//加载账号相关
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadCompute();//加载云主机操作
        onAfterLoadComputeImage();//加载镜像操作
        onAfterLoadComputeSecurityGroup();//加载云主机安全组操作
        onAfterLoadComputeKeypair();//加载云主机密钥对操作
        onAfterLoadStorageDisk();//加载存储云硬盘操作
        onAfterLoadStorageSnapshot();//加载存储云硬盘快照操作
        onAfterLoadNeutronVpc();//加载网络VPC操作
        onAfterLoadNeutronSubnet();//加载网络子网操作
        onAfterLoadNeutronEip();//加载网络弹性IP操作
        onAfterLoadNeutronNat();//加载网络NAT网关操作
        onAfterLoadNeutronRoute();//加载网络路由表操作
    }

    public void onAfterLoadAccount() {
        registerBefore(CloudService::defaultRegion, ActionType.AUTH_PLATFORM_ACCOUNT);//认证云账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount);//认证云账号
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountFormModel);//获取云账号表单信息
    }

    public void onAfterLoadFetch() {
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_STORAGE_IMAGE,
                ActionType.FETCH_COMPUTE_SECURITYGROUP,
                ActionType.FETCH_NEUTRON_VPC,
                ActionType.FETCH_NEUTRON_SUBNET,
                ActionType.FETCH_NEUTRON_NIC,
                ActionType.FETCH_NEUTRON_NAT,
                ActionType.FETCH_NEUTRON_ROUTE,
                ActionType.FETCH_PLATFORM_KEYPAIR,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_STORAGE_SNAPSHOT,
                ActionType.FETCH_NEUTRON_EIP,
                ActionType.FETCH_NEUTRON_LOADBALANCE,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT
        );
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion);//同步地域
        register(ActionType.FETCH_PLATFORM_AZONE, FetchService::fetchZone);//同步可用区
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor);//获取规格
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage);//获取镜像
        //无法使用，安全组规则ID生成有问题需进一步开发
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup);//获取安全组
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc);//获取VPC
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);//获取Subnet
        register(ActionType.FETCH_NEUTRON_NIC, FetchService::fetchNetcard);//获取网卡
        register(ActionType.FETCH_NEUTRON_NAT, FetchService::fetchNat);//获取nat网关
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRoute);//获取路由表
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeyPair);//获取密钥对
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);//获取云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsPerf) //获取云主机监控
                .addBefore(FetchService::defaultMetricRequest)
                .addBefore(FetchService::defaultEcsMetricNames)
                .addBefore(FetchService::defaultEcsMetricDimensions);//获取云主机监控,预处理
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk);//获取磁盘
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot);//获取磁盘快照
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip);//获取EIP
        register(ActionType.FETCH_NEUTRON_LOADBALANCE, FetchService::fetchLoadBalancer);//获取负载均衡
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchBucket);//获取对象存储
        register(ActionType.FETCH_STORAGE_BUCKET_FILE, FetchService::fetchBucketFile);//获取对象存储文件
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm)//获取告警信息
                .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent)//获取事件
                .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
    }

    public void onAfterLoadCompute() {
        register(ActionType.CREATE_COMPUTE_INSTANCE, CvmClient::RunInstances)//创建云主机
                .addAfter(CloudService::toBizResId);
        register(ActionType.DELETE_COMPUTE_INSTANCE, CvmClient::TerminateInstances)//批量删除云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds");
        register(ActionType.UPDATE_COMPUTE_INSTANCE, CvmClient::ModifyInstancesAttribute)//修改云主机
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.InstanceIds")
                .addTransferCloud("$.model.resourceName", "$.InstanceName", false);
        register(ActionType.START_COMPUTE_INSTANCE, CvmClient::StartInstances)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds");
        register(ActionType.STOP_COMPUTE_INSTANCE, CvmClient::StopInstances)//批量停止云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds");
        register(ActionType.REBOOT_COMPUTE_INSTANCE, CvmClient::RebootInstances)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds");
    }

    public void onAfterLoadComputeImage() {
        register(ActionType.DELETE_COMPUTE_IMAGE, CvmClient::DeleteImages)//批量删除镜像
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.ImageIds");
    }

    public void onAfterLoadComputeSecurityGroup() {
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP, VpcClient::CreateSecurityGroup);//创建安全组
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP, VpcClient::DeleteSecurityGroup)//删除安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.ci.openId", "$.SecurityGroupId");
        //修改安全组
        register(ActionType.UPDATE_COMPUTE_SECURITYGROUP, VpcClient::ModifySecurityGroupAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.SecurityGroupId")
                .addTransferCloud("$.model.resourceName", "$.GroupName", false)
                .addTransferCloud("$.model.description", "$.GroupDescription", false);
        register(ActionType.BIND_COMPUTE_SECURITYGROUP, CvmClient::AssociateSecurityGroups)//绑定安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.ci.openId", "$.SecurityGroupIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceIds");
        register(ActionType.UNBIND_COMPUTE_SECURITYGROUP, CvmClient::DisassociateSecurityGroups)//解绑安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.ci.openId", "$.SecurityGroupIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceIds");

        register(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, VpcClient::CreateSecurityGroupPolicies);//创建安全组规则
        //无法使用，待进一步开发
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, VpcClient::DeleteSecurityGroupPolicies)//删除安全组规则
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.securityGroupId", "$.securityGroupId")
                .addTransferCloud("$.ci.openId", "$.securityGroupRuleId")
                .addTransferCloud("$.ci.sourceJson.direction", "$.direction");
    }

    public void onAfterLoadComputeKeypair() {

        register(ActionType.CREATE_COMPUTE_KEYPAIR, CvmClient::CreateKeyPair)//创建密钥对
                .addTransferCloud("$.model.regionId", "$.regionId")
                .addTransferCloud("$.model.resourceName", "$.KeyName");
        register(ActionType.DELETE_COMPUTE_KEYPAIR, CvmClient::DeleteKeyPairs)//删除密钥对
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.cis.openId", "$.KeyIds");
        register(ActionType.ATTACH_COMPUTE_KEYPAIR, CvmClient::AssociateInstancesKeyPairs)//挂载密钥对
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.KeyIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceIds");
        register(ActionType.DETACH_COMPUTE_KEYPAIR, CvmClient::DisassociateInstancesKeyPairs)//绑定密钥对
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.KeyIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceIds");
    }

    public void onAfterLoadStorageDisk() {
        register(ActionType.CREATE_STORAGE_DISK, CbsClient::CreateDisks);//创建云硬盘
        register(ActionType.UPDATE_STORAGE_DISK, CbsClient::ModifyDiskAttributes)//修改云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.DiskIds")
                .addTransferCloud("$.model.resourceName", "$.DiskName", false);
        register(ActionType.DELETE_STORAGE_DISK, CbsClient::TerminateDisks)//删除云硬盘
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.cis.openId", "$.DiskIds");
        register(ActionType.ATTACH_STORAGE_DISK, CbsClient::AttachDisks)//挂载云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.DiskIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceId");
        register(ActionType.DETACH_STORAGE_DISK, CbsClient::DetachDisks)//卸载云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.DiskIds")
                .addTransferCloud("$.model.instanceId", "$.InstanceId");
    }

    public void onAfterLoadStorageSnapshot() {

        register(ActionType.CREATE_STORAGE_SNAPSHOT, CbsClient::CreateSnapshot);//创建云硬盘快照
        register(ActionType.UPDATE_STORAGE_SNAPSHOT, CbsClient::ModifySnapshotAttribute)//修改云硬盘快照
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.SnapshotId")
                .addTransferCloud("$.model.resourceName", "$.SnapshotName", false);
        register(ActionType.DELETE_STORAGE_SNAPSHOT, CbsClient::DeleteSnapshots)//删除云硬盘快照
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.cis.openId", "$.SnapshotIds");
    }

    public void onAfterLoadNeutronVpc() {
        register(ActionType.CREATE_NEUTRON_VPC, VpcClient::CreateVpc);//创建VPC
        register(ActionType.UPDATE_NEUTRON_VPC, VpcClient::ModifyVpcAttribute)//修改VPC
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.VpcId")
                .addTransferCloud("$.model.resourceName", "$.VpcName", false);
        register(ActionType.DELETE_NEUTRON_VPC, VpcClient::DeleteVpc)//删除VPC
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.VpcId");
    }

    //创建子网
    public void onAfterLoadNeutronSubnet() {
        register(ActionType.CREATE_NEUTRON_SUBNET, VpcClient::CreateSubnet);//创建子网
        register(ActionType.UPDATE_NEUTRON_SUBNET, VpcClient::ModifySubnetAttribute)//修改子网
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.VSwitchId")
                .addTransferCloud("$.model.resourceName", "$.VSwitchName", false);
        register(ActionType.DELETE_NEUTRON_SUBNET, VpcClient::DeleteSubnet)//删除子网
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.SubnetId");
    }

    public void onAfterLoadNeutronEip() {
        register(ActionType.CREATE_NEUTRON_EIP, VpcClient::AllocateAddresses);//创建弹性IP
        //待进一步开发，修改带宽调用另外一个接口
        register(ActionType.UPDATE_NEUTRON_EIP, VpcClient::ModifyAddressAttribute)//修改弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.AddressId")
                .addTransferCloud("$.model.resourceName", "$.AddressName", false);
        register(ActionType.DELETE_NEUTRON_EIP, VpcClient::ReleaseAddresses) //删除弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.AddressId");
        register(ActionType.BIND_NEUTRON_EIP, VpcClient::AssociateAddress)//绑定弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.AddressId")
                .addTransferCloud("$.model.instanceId", "$.InstanceId");
        register(ActionType.UNBIND_NEUTRON_EIP, VpcClient::DisassociateAddress)//解绑弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.AddressId");
    }

    public void onAfterLoadNeutronNat() {
        register(ActionType.CREATE_NEUTRON_NAT, VpcClient::CreateNatGateway);//创建NAT网关
        register(ActionType.UPDATE_NEUTRON_NAT, VpcClient::ModifyNatGatewayAttribute)//修改NAT网关
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.NatGatewayId")
                .addTransferCloud("$.model.resourceName", "$.NatGatewayName", false);
        register(ActionType.DELETE_NEUTRON_NAT, VpcClient::DeleteNatGateway)//删除NAT网关
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.NatGatewayId");
    }

    public void onAfterLoadNeutronRoute() {
        register(ActionType.CREATE_NEUTRON_ROUTE, VpcClient::CreateRouteTable);//创建路由表
        register(ActionType.UPDATE_NEUTRON_ROUTE, VpcClient::ModifyRouteTableAttribute)//修改路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.RouteTableId")
                .addTransferCloud("$.model.resourceName", "$.RouteTableName", false);
        register(ActionType.DELETE_NEUTRON_ROUTE, VpcClient::DeleteRouteTable)//删除路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.RouteTableName");
    }
}
