package com.futong.gemini.plugin.cloud.ali.tencent.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.*;
import com.futong.constant.dict.*;
import com.futong.constant.dict.InstanceStatus;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.ali.tencent.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ali.tencent.common.CloudClient;
import com.futong.gemini.plugin.cloud.ali.tencent.common.Converts;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.*;
import com.tencentcloudapi.cbs.v20170312.CbsClient;
import com.tencentcloudapi.cbs.v20170312.models.DescribeDisksResponse;
import com.tencentcloudapi.cbs.v20170312.models.DescribeSnapshotsResponse;
import com.tencentcloudapi.cbs.v20170312.models.Disk;
import com.tencentcloudapi.cbs.v20170312.models.Snapshot;
import com.tencentcloudapi.clb.v20180317.ClbClient;
import com.tencentcloudapi.clb.v20180317.models.DescribeLoadBalancersResponse;
import com.tencentcloudapi.clb.v20180317.models.LoadBalancer;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import com.tencentcloudapi.cvm.v20170312.models.*;
import com.tencentcloudapi.cvm.v20170312.models.Instance;
import com.tencentcloudapi.monitor.v20180724.MonitorClient;
import com.tencentcloudapi.monitor.v20180724.models.*;
import com.tencentcloudapi.vpc.v20170312.VpcClient;
import com.tencentcloudapi.vpc.v20170312.models.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

@Slf4j
public class FetchService {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    /**
     * 获取地域
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        Map<Class, List> map = BaseCloudService.fetch(request,
                CloudClient.client,
                CvmClient::DescribeRegions,
                FetchService::convertRegion);
        BaseResponse response = BaseCloudService.fetchSend(request, map);
        if (CollUtil.isEmpty(map.get(TmdbDevops.class))) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, map.get(TmdbDevops.class), (TmdbDevops t) -> {
            JobInfo jobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_PLATFORM_AZONE);
            request.getBody().getCloud().put("regionId", t.getDevops_value());
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static Map<Class, List> convertRegion(BaseCloudRequest request, DescribeRegionsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getRegionSet())) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的规格信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_TENXUN.value(),
                DevopsSide.DEVOPS_REGION.value(),
                Stream.of(response.getRegionSet()).collect(Collectors.toList()),
                RegionInfo::getRegionName,
                RegionInfo::getRegion
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 获取可用区
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchZone(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                CvmClient::DescribeZones,
                FetchService::convertZone);
    }

    public static Map<Class, List> convertZone(BaseCloudRequest request, DescribeZonesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getZoneSet())) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN.value(),
                        request.getBody().getCloud().getString("regionId"),
                        DevopsSide.DEVOPS_REGION.value());
        for (ZoneInfo zone : response.getZoneSet()) {
            BuilderDevops devopsZone = new BuilderDevops()
                    .withDevops(devops.get(), zone.getZoneName(), zone.getZone(), DevopsSide.DEVOPS_ZONE.value())
                    .withJson(JSON.toJSONString(zone));
            data.add(devopsZone.get());
            links.add(devopsZone.builderLink(devops.get()));
        }
        result.put(TmdbDevops.class, data);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }

    /**
     * 获取规格
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchFlavor(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                CvmClient::DescribeInstanceTypeConfigs,
                FetchService::convertFlavor);
    }

    public static Map<Class, List> convertFlavor(BaseCloudRequest request, DescribeInstanceTypeConfigsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getInstanceTypeConfigSet())) {
            result.put(CmdbFlavor.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbFlavor> data = new ArrayList<>();
        for (InstanceTypeConfig res : response.getInstanceTypeConfigSet()) {
            CmdbFlavor ci = new CmdbFlavor();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceType()));
            ci.setCpu_size(res.getCPU().intValue());
            ci.setMem_size(res.getMemory().intValue());
            ci.setOpen_id(res.getInstanceType());
            ci.setOpen_name(res.getInstanceType());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_FLAVOR
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbFlavor.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 获取镜像
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchImage(BaseCloudRequest request) {
        Entry.E2<DescribeImagesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                CvmClient::DescribeImages,
                FetchService::convertImage);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertImage(BaseCloudRequest request, DescribeImagesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getImageSet())) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        for (Image image : response.getImageSet()) {
            CmdbImageRes ci = new CmdbImageRes();
            CmdbOsRes os = new CmdbOsRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), image.getImageId()));
            ci.setType(image.getPlatform());
            ci.setSize(image.getImageSize().floatValue());
            ci.setStatus(image.getImageState());
            ci.setDesc(image.getImageDescription());
            ci.setOpen_id(image.getImageId());
            ci.setOpen_name(image.getImageName());
            ci.setVisibility(image.getImageType().contains("PRIVATE") ? "private" : "public");
            ci.setImage_source(image.getImageSource());
            toCiResCloud(request, ci);
            dataImage.add(ci);
            //操作新系统OS对象
            os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), image.getImageId()));
            os.setCpu_arch(image.getArchitecture());
            os.setName(image.getOsName());
            os.setType(image.getPlatform());
            os.setVersion(image.getOsName());
            os.setFull_name(image.getOsName());
            os.setOpen_name(image.getImageName());
            os.setOpen_id(image.getImageId());
            toCiResCloud(request, ci);
            dataOs.add(os);
            Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
            dataAss.add(osa);
        }
        List<TmdbResourceSet> dataSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(), CloudType.PUBLIC_TENXUN)
                .withDataByDevopsValue(ResourceType.CMDB_IMAGE_RES, dataImage, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .withDataByDevopsValue(ResourceType.CMDB_OS_RES, dataOs, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, dataSet);
        return result;
    }

    /**
     * 获取安全组
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request) {
        Entry.E2<DescribeSecurityGroupsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeSecurityGroups,
                FetchService::convertSecurityGroup);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        //获取并发送规则信息，默认查询500条规则。无需处理分页
        VpcClient client = CloudClient.client.client(VpcClient.class, request.getBody());
        DescribeSecurityGroupPoliciesRequest requestRule = new DescribeSecurityGroupPoliciesRequest();
        for (Object obj : mapE2.v2.get(CmdbSecuritygroupRes.class)) {
            CmdbSecuritygroupRes res = (CmdbSecuritygroupRes) obj;
            requestRule.setSecurityGroupId(res.getOpen_id());
            request.getBody().getCloud().put("securityGroupId", res.getOpen_id());
            Map<Class, List> ruleMap = BaseCloudService.fetch(
                    client,
                    requestRule,
                    request,
                    CloudClient.client,
                    VpcClient::DescribeSecurityGroupPolicies,
                    FetchService::convertSecurityGroupRule);
            BaseCloudService.fetchSend(request, ruleMap);
        }
        //分页安全组
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, DescribeSecurityGroupsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getSecurityGroupSet())) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (SecurityGroup res : response.getSecurityGroupSet()) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSecurityGroupId()));
            ci.setOpen_id(res.getSecurityGroupId());
            ci.setOpen_name(res.getSecurityGroupName());
            ci.setDesc(res.getSecurityGroupDesc());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_SECURITYGROUP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSecurityGroupRule(BaseCloudRequest request, DescribeSecurityGroupPoliciesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getSecurityGroupPolicySet()) {
            result.put(CmdbSecuritygroupRule.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        String securityGroupResId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), request.getBody().getCloud().getString("securityGroupId"));
        List<CmdbSecuritygroupRule> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if (ArrayUtil.isNotEmpty(response.getSecurityGroupPolicySet().getIngress())) {
            for (SecurityGroupPolicy ingress : response.getSecurityGroupPolicySet().getIngress()) {
                CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ingress.getSecurityGroupId(), "ingress"));
                ci.setOpen_id(ingress.getSecurityGroupId());
                ci.setOpen_name(ci.getOpen_id());
                ci.setDirection("ingress");
                ci.setPolicy(ingress.getAction());
                ci.setExtend1(ingress.getProtocol());
                ci.setPort_range(ingress.getPort());
                ci.setSource_cidr(ingress.getCidrBlock());
                ci.setDesc(ingress.getPolicyDescription());
                if (StrUtil.isNotEmpty(ingress.getModifyTime())) {
                    ci.setCreate_time(TimeUtils.stringToLongTime(ingress.getModifyTime()));
                }
                toCiResCloud(request, ci);
                data.add(ci);
                //关联安全组规则
                Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, securityGroupResId);
                associations.add(rule);
            }
        }
        if (ArrayUtil.isNotEmpty(response.getSecurityGroupPolicySet().getEgress())) {
            for (SecurityGroupPolicy egress : response.getSecurityGroupPolicySet().getEgress()) {
                CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), egress.getSecurityGroupId(), "egress"));
                ci.setOpen_id(egress.getSecurityGroupId());
                ci.setOpen_name(ci.getOpen_id());
                ci.setDirection("egress");
                ci.setPolicy(egress.getAction());
                ci.setExtend1(egress.getProtocol());
                ci.setPort_range(egress.getPort());
                ci.setSource_cidr(egress.getCidrBlock());
                ci.setDesc(egress.getPolicyDescription());
                if (StrUtil.isNotEmpty(egress.getModifyTime())) {
                    ci.setCreate_time(TimeUtils.stringToLongTime(egress.getModifyTime()));
                }
                toCiResCloud(request, ci);
                data.add(ci);
                //关联安全组规则
                Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, securityGroupResId);
                associations.add(rule);
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_SECURITYGROUP_RULE
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSecuritygroupRule.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 获取VPC
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchVpc(BaseCloudRequest request) {
        Entry.E2<DescribeVpcsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeVpcs,
                FetchService::convertVpc);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertVpc(BaseCloudRequest request, DescribeVpcsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getVpcSet())) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbVpcRes> data = new ArrayList<>();
        for (Vpc res : response.getVpcSet()) {
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            ci.setOpen_id(res.getVpcId());
            ci.setOpen_name(res.getVpcName());
            ci.setCidr(res.getCidrBlock());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_VPC_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }


    /**
     * 获取子网Subnet
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        Entry.E2<DescribeSubnetsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeSubnets,
                FetchService::convertSubnet);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertSubnet(BaseCloudRequest request, DescribeSubnetsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getSubnetSet())) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_SUBNET_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_TENXUN.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Subnet res : response.getSubnetSet()) {
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            ci.setOpen_id(res.getSubnetId());
            ci.setOpen_name(res.getSubnetName());
            ci.setCidr_ipv4(res.getCidrBlock());
            ci.setCidr_ipv6(res.getIpv6CidrBlock());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            ci.setAvailable_ip_count_ipv4(res.getAvailableIpAddressCount().intValue());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getZone());
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(rule);
        }
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取网卡
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNetcard(BaseCloudRequest request) {
        Entry.E2<DescribeNetworkInterfacesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeNetworkInterfaces,
                FetchService::convertNetcard);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertNetcard(BaseCloudRequest request, DescribeNetworkInterfacesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getNetworkInterfaceSet())) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (NetworkInterface res : response.getNetworkInterfaceSet()) {
            CmdbNetcardRes ci = new CmdbNetcardRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNetworkInterfaceId()));
            ci.setOpen_id(res.getNetworkInterfaceId());
            ci.setOpen_name(res.getNetworkInterfaceId());
            ci.setType(res.getPrimary() ? "Primary" : "Secondary");
            ci.setStatus(res.getState());
            ci.setMac_address(res.getMacAddress());
            ci.setDesc(res.getNetworkInterfaceDescription());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            toCiResCloud(request, ci);
            if (ArrayUtil.isNotEmpty(res.getPrivateIpAddressSet())) {
                StringBuilder ipv4s = new StringBuilder();
                for (PrivateIpAddressSpecification ipv4 : res.getPrivateIpAddressSet()) {
                    if (StrUtil.isNotEmpty(ipv4.getPrivateIpAddress())) {
                        ipv4s.append(ipv4.getPrivateIpAddress()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv4.getPrivateIpAddress())));
                    }

                    if (StringUtils.isNotEmpty(ipv4.getPublicIpAddress())) {
                        ipv4s.append(ipv4.getPublicIpAddress()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv4.getPublicIpAddress())));
                    }
                }
                if (ipv4s.length() > 0) {
                    ipv4s.setLength(ipv4s.length() - 1);
                    ci.setIpv4_address(ipv4s.toString());
                }
            }
            if (ArrayUtil.isNotEmpty(res.getIpv6AddressSet())) {
                StringBuilder ipv6s = new StringBuilder();
                for (Ipv6Address ipv6 : res.getIpv6AddressSet()) {
                    if (StrUtil.isNotEmpty(ipv6.getAddress())) {
                        ipv6s.append(ipv6.getAddress()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv6.getAddress())));
                    }
                }
                if (ipv6s.length() > 0) {
                    ipv6s.setLength(ipv6s.length() - 1);
                    ci.setIpv6_address(ipv6s.toString());
                }
            }
            //关联VPC
            if (StrUtil.isNotEmpty(res.getVpcId())) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
                associations.add(vpc);
            }
            //关联子网
            if (StrUtil.isNotEmpty(res.getSubnetId())) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
                associations.add(subnet);
            }
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_NETCARD_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取Nat网关
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNat(BaseCloudRequest request) {
        Entry.E2<DescribeNatGatewaysResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeNatGateways,
                FetchService::convertNat);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if (CollUtil.isEmpty(mapE2.v2.get(CmdbNatRes.class))) {
            return response;
        }
        //分页子任务
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertNat(BaseCloudRequest request, DescribeNatGatewaysResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getNatGatewaySet())) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(CmdbNatRes.class, null);
            result.put(CmdbIpRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbBaseMetainfo.class, null);
            return result;
        }
        List<CmdbNatEntryRes> entryResList = new ArrayList<>();
        List<CmdbNatRes> data = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbBaseMetainfo> metainfos = new ArrayList<>();
        for (NatGateway res : response.getNatGatewaySet()) {
            CmdbNatRes ci = new CmdbNatRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNatGatewayId()));
            ci.setOpen_id(res.getNatGatewayId());
            ci.setOpen_name(res.getNatGatewayName());
            ci.setStatus(res.getState());
            ci.setAdmin_state(res.getNetworkState());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            toCiResCloud(request, ci);
            CmdbBaseMetainfo metainfo = new CmdbBaseMetainfo();
            metainfo.setAccount_id(ci.getAccount_id());
            metainfo.setCloud_type(ci.getCloud_type());
            metainfo.setRes_id(ci.getRes_id());
            metainfo.setTable("cmdb_nat_res");
            metainfo.setMetainfo(JSON.toJSONString(ci));
            metainfos.add(metainfo);
            data.add(ci);
            //关联子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            associations.add(subnet);
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(rule);
            //关联SNAT
            if (ArrayUtil.isNotEmpty(res.getSourceIpTranslationNatRuleSet())) {
                for (SourceIpTranslationNatRule snat : res.getSourceIpTranslationNatRuleSet()) {
                    String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNatGatewayId());
                    CmdbNatEntryRes entryRes = new CmdbNatEntryRes();
                    entryRes.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), snat.getNatGatewaySnatId()));
                    entryRes.setOpen_id(snat.getNatGatewaySnatId());
                    entryRes.setOpen_name(snat.getNatGatewaySnatId());
                    entryRes.setCidr(snat.getPrivateIpAddress());
                    entryRes.setPublic_ip(snat.getPrivateIpAddress());
                    entryRes.setType("SNAT");
                    toCiResCloud(request, ci);
                    //关联NAT
                    Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, resId);
                    associations.add(nat);
                    entryResList.add(entryRes);
                }
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_NAT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbNatRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        result.put(CmdbBaseMetainfo.class, metainfos);
        result.put(CmdbNatEntryRes.class, entryResList);
        return result;
    }


    /**
     * 获取路由
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchRoute(BaseCloudRequest request) {
        Entry.E2<DescribeRouteTablesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeRouteTables,
                FetchService::convertRoute);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertRoute(BaseCloudRequest request, DescribeRouteTablesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getRouteTableSet())) {
            result.put(CmdbRouteRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbRouteRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (RouteTable res : response.getRouteTableSet()) {
            CmdbRouteRes ci = new CmdbRouteRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getRouteTableId()));
            ci.setOpen_id(res.getRouteTableId());
            ci.setOpen_name(res.getRouteTableName());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreatedTime()));
            toCiResCloud(request, ci);
            //关联VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(vpc);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_ROUTE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbRouteRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取密钥对
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchKeyPair(BaseCloudRequest request) {
        Entry.E2<DescribeKeyPairsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                CvmClient::DescribeKeyPairs,
                FetchService::convertKeyPair);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertKeyPair(BaseCloudRequest request, DescribeKeyPairsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getKeyPairSet())) {
            result.put(CmdbKeypairRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbKeypairRes> data = new ArrayList<>();
        for (KeyPair res : response.getKeyPairSet()) {
            CmdbKeypairRes ci = new CmdbKeypairRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getKeyId()));
            ci.setOpen_id(res.getKeyId());
            ci.setOpen_name(res.getKeyName());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_KEYPAIR_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbKeypairRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 获取云主机
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                CvmClient::DescribeInstances,
                FetchService::convertEcs);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, DescribeInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getInstanceSet())) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_INSTANCE_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_TENXUN.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        for (Instance res : response.getInstanceSet()) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setCpu_size(res.getCPU().intValue());
            ci.setMem_size(res.getMemory().intValue() * 1024);
            ci.setOpen_status(res.getInstanceState());
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            toCiResCloud(request, ci);
            //实例状态。取值范围：
            //Pending：创建中。
            //Running：运行中。
            //Starting：启动中。
            //Stopping：停止中。
            //Stopped：已停止。
            switch (res.getInstanceState()) {
                case "PENDING":
                    ci.setStatus(InstanceStatus.BUILDING.value());
                    break;
                case "RUNNING":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "STARTING":
                    ci.setStatus(InstanceStatus.STARTING.value());
                    break;
                case "REBOOTING":
                    ci.setStatus(InstanceStatus.RESTARTING.value());
                    break;
                case "STOPPING":
                    ci.setStatus(InstanceStatus.STOPPING.value());
                    break;
                case "STOPPED":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
            }

            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getPlacement().getZone());
            //关联规格
            Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceType()));
            associations.add(flavor);
            //关联镜像
            Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getImageId()));
            associations.add(image);
            //关联镜像OS
            Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getImageId()));
            associations.add(os);
            //关联网络VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVirtualPrivateCloud().getVpcId()));
            associations.add(vpc);
            //关联网络VPC子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVirtualPrivateCloud().getSubnetId()));
            associations.add(subnet);
            //关联密钥对
            if (ObjectUtil.isNotEmpty(res.getLoginSettings())) {
                //关联密钥对
                if (ArrayUtil.isNotEmpty(res.getLoginSettings().getKeyIds())) {
                    for (String keyId : res.getLoginSettings().getKeyIds()) {
                        Association keyPair = AssociationUtils.toAssociation(ci, CmdbKeypairRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), keyId));
                        associations.add(keyPair);
                    }
                }
            }
            //添加私网IP
            if (ArrayUtil.isNotEmpty(res.getPrivateIpAddresses())) {
                for (String primaryIpAddress : res.getPrivateIpAddresses()) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), primaryIpAddress));
                    ip.setType(IpType.PRIVATE_IP.value());
                    ip.setAddress(primaryIpAddress);
                    ip.setOpen_id(primaryIpAddress);
                    ip.setOpen_name(primaryIpAddress);
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联网络VPC
                    associations.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVirtualPrivateCloud().getVpcId())));
                    //关联网络VPC子网
                    associations.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVirtualPrivateCloud().getSubnetId())));
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ip, ci));
                }
            }
            //添加公网ip(此信息如不为空则说明此为创建云主机同步创建的共网IP)
            if (ArrayUtil.isNotEmpty(res.getPublicIpAddresses())) {
                for (String ipAddress : res.getPublicIpAddresses()) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipAddress));
                    ip.setType(IpType.PUBLIC_IP.value());
                    ip.setAddress(ipAddress);
                    ip.setOpen_id(ipAddress);
                    ip.setOpen_name(ipAddress);
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ip, ci));
                }
            }
            //关联安全组
            if (ArrayUtil.isNotEmpty(res.getSecurityGroupIds())) {
                Arrays.stream(res.getSecurityGroupIds()).forEach(t -> {
                    Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), t));
                    associations.add(securityGroup);
                });
            }
            data.add(ci);
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }


    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("regionId")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数regionId不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("namespace")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数namespace不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("period")) {
            request.getBody().getCloud().put("period", "300");
        }
        if (!request.getBody().getCloud().containsKey("startTime")) {
            //当前时间-监控数据得统计周期（秒）*1000（毫秒）
            long startTime = System.currentTimeMillis() - NumberUtil.parseLong(request.getBody().getCloud().getString("period")) * 1000;
            request.getBody().getCloud().put("startTime", TimeUtils.longToString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", TimeUtils.longToString(System.currentTimeMillis()));
        }
        return true;
    }

    public static boolean defaultEcsMetricNames(BaseCloudRequest request) {
        if (!request.getBody().containsKey("MetricNames")) {
            request.getBody().put("MetricNames", Converts.metrics.keySet());
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 50);//固定50条，阿里云限制
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);

        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        //设置采集监控数据云主机ID
        String dimensions = instanceMap.keySet().stream().map(t -> "{\"instanceId\":\"" + t + "\"}").collect(Collectors.joining(",", "[", "]"));
        request.getBody().getCloud().put("dimensions", dimensions);
        return true;
    }

    /**
     * 获取云主机监控
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchEcsPerf(BaseCloudRequest request) {
        try {
            //监控请求对象
            GetMonitorDataRequest metricListRequest = request.getBody().getCloud().toJavaObject(GetMonitorDataRequest.class);
            //监控请求指标集合
            Collection<String> metricNames = request.getBody().getJSONArray("MetricNames").toJavaList(String.class);
            //云主机
            Map<String, ResInstanceDiskApiModel> instanceMap = request.getBody().getJSONObject("instanceMap").toJavaObject(Map.class);
            //设置采集监控数据云主机ID
            List<Dimension> dimensionList = new ArrayList<>();
            instanceMap.keySet().forEach(t -> {
                Dimension dimension = new Dimension();
                dimension.setName("InstanceId");
                dimension.setValue(t);
                dimensionList.add(dimension);
            });
            com.tencentcloudapi.monitor.v20180724.models.Instance instance = new com.tencentcloudapi.monitor.v20180724.models.Instance();
            instance.setDimensions(dimensionList.toArray(new Dimension[dimensionList.size()]));
            com.tencentcloudapi.monitor.v20180724.models.Instance[] instanceArr = new com.tencentcloudapi.monitor.v20180724.models.Instance[]{instance};
            metricListRequest.setInstances(instanceArr);
            /*******发起监控数据请求*******/
            MonitorClient client = CloudClient.client.client(MonitorClient.class, request.getBody());
            //发起监控数据请求
            List<GetMonitorDataResponse> metricList = new ArrayList<>();
            for (String metricName : metricNames) {
                metricListRequest.setMetricName(metricName);
                log.info("查询监控数据得请求信息:{}", JSON.toJSONString(metricListRequest));
                GetMonitorDataResponse metricResult = CloudClient.client.execute(request.getBody(), metricListRequest, MonitorClient::GetMonitorData);
                if (ObjectUtil.isNotEmpty(metricResult)) {
                    metricList.add(metricResult);
                }
            }
            Map<String, PerfInfoBean> perfMap = convertEcsPerf(request, metricList);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("获取云主机监控失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取云主机监控失败");
        }
    }

    public static Map<String, PerfInfoBean> convertEcsPerf(BaseCloudRequest request, List<GetMonitorDataResponse> metricList) {
        if (CollUtil.isEmpty(metricList)) {
            return null;
        }
        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (GetMonitorDataResponse metric : metricList) {
            String metricName = metric.getMetricName();
            DataPoint[] dataPointsArr = metric.getDataPoints();
            for (DataPoint dataPoint : dataPointsArr) {
                Long[] timestampArr = dataPoint.getTimestamps();
                Float[] valueArr = dataPoint.getValues();
                String instanceId = dataPoint.getDimensions()[0].getValue();
                for (int i1 = 0; i1 < timestampArr.length; i1++) {
                    Long timestamp = timestampArr[i1];
                    Float average = valueArr[i1];
                    String id = instanceId + "_" + metricName + "_" + timestamp;
                    PerfInfoBean perf = perfMap.get(id);
                    if (perf == null) {
                        //生成指标对象
                        ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                        perf = new PerfInfoBean();//指标对应得资源CI信息
                        perf.setAccountId(instanceDisk.getAccount_id());
                        perf.setCloudType(instanceDisk.getCloud_type());
                        perf.setResId(instanceDisk.getRes_id());
                        perf.setOpenId(instanceDisk.getOpen_id());
                        perf.setOpenName(instanceDisk.getOpen_name());
                        perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                        perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                        perf.setCreateTime(DateUtil.formatDateTime(DateUtil.date(timestamp)));
                        if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                            Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                            perf.setDiskSize(sum);
                        }
                        perf.setId(id);
                    }
                    BiConsumer<PerfInfoBean, Double> setValue = Converts.perfMappingBean.get(metricName);
                    setValue.accept(perf, average.doubleValue());//设置监控指标值
                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    /**
     * 告警信息
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        try {
            //查询阿里云云主机信息
            DescribeAlarmHistoriesRequest alertRequest = request.getBody().getCloud().toJavaObject(DescribeAlarmHistoriesRequest.class);
            DescribeAlarmHistoriesResponse response = CloudClient.client.execute(request.getBody(), alertRequest, MonitorClient::DescribeAlarmHistories);
            if (ArrayUtil.isEmpty(response.getHistories())) {
                return BaseResponse.SUCCESS.of("查询告警信息为空!");
            }
            //将获取到的云主机信息转换为CI模型
            List<AlarmInfoBean> listCI = Arrays.stream(response.getHistories()).map(t -> convertAlarm(request, t)).collect(Collectors.toList());
            BaseCloudService.toAetMessageAndSend(listCI, "alarm");
            String message = StrUtil.format("本次获取告警信息,页码：{},条数：{},本次获取条数：{}", alertRequest.getPageNumber(), alertRequest.getPageSize(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == alertRequest.getPageSize()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("pageNumber", alertRequest.getPageNumber() + 1);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }
    }

    public static AlarmInfoBean convertAlarm(BaseCloudRequest request, AlarmHistory alert) {
        AlarmInfoBean alarm = new AlarmInfoBean();
        alarm.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getAlarmId()));
        alarm.setAccountId(request.getBody().getAccess().getCmpId());
        alarm.setCloudType(CloudType.PUBLIC_ALI.value());
        alarm.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getAlarmId()));
        alarm.setOpenId(alert.getAlarmId());
        alarm.setOpenName(alert.getAlarmObject());
        alarm.setAlarmId(alert.getPolicyId());
        alarm.setAlarmName(alert.getPolicyName());
        alarm.setDetail(alert.getContent());
        alarm.setClosedStatus(false);
        alarm.setJsonInfo(JSON.toJSONString(alert));
        alarm.setCount(1);
        if (ObjectUtil.isNotEmpty(alert.getFirstOccurTime())) {
            alarm.setFirstTime(DateUtil.formatDateTime(DateUtil.date(alert.getFirstOccurTime())));
            alarm.setCreateTime(alarm.getFirstTime());
        }
        alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());

        return alarm;
    }


    /**
     * 事件
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            DescribeAccidentEventListRequest eventRequest = request.getBody().getCloud().toJavaObject(DescribeAccidentEventListRequest.class);
            DescribeAccidentEventListResponse response = CloudClient.client.execute(request.getBody(), eventRequest, MonitorClient::DescribeAccidentEventList);
            if (ArrayUtil.isEmpty(response.getAlarms())) {
                return BaseResponse.SUCCESS.of("查询事件信息为空!");
            }
            List<EventInfoBean> listCI = Arrays.stream(response.getAlarms()).map(t -> convertEvent(request, t)).collect(Collectors.toList());
            listCI.removeIf(Objects::isNull);
            BaseCloudService.toAetMessageAndSend(listCI, "event");

            String message = StrUtil.format("本次获取事件信息,页码：{},条数：{},本次获取条数：{}", eventRequest.getOffset() / 100, eventRequest.getLimit(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == eventRequest.getLimit().intValue()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("offset", (eventRequest.getOffset() / 100 + 1) * 100);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步事件数据失败");
        }
    }

    public static EventInfoBean convertEvent(BaseCloudRequest request, DescribeAccidentEventListAlarms event) {
        EventInfoBean bean = new EventInfoBean();
        bean.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), event.getAffectResource(), event.getAccidentTypeDesc(), event.getEventStatus() + "", "" + event.getOccurTime()));
        bean.setAccountId(request.getBody().getAccess().getCmpId());
        bean.setCloudType(CloudType.PUBLIC_TENXUN.value());
        bean.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), event.getAffectResource()));
        bean.setOpenId(event.getAffectResource());
        bean.setOpenName(event.getAccidentTypeDesc());
        bean.setEventType("system");
        bean.setEventName(event.getAccidentTypeDesc());
        bean.setDetail(event.getAccidentTypeDesc());
        if (StrUtil.isNotEmpty(event.getOccurTime())) {
            bean.setBeginTime(TimeUtils.instantToString(TimeUtils.stringToInstant(event.getOccurTime())));
            bean.setEndTime(bean.getBeginTime());
        }
        bean.setJsonInfo(JSON.toJSONString(event));
        return bean;
    }


    /**
     * 获取磁盘
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchDisk(BaseCloudRequest request) {
        Entry.E2<DescribeDisksResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                CbsClient::DescribeDisks,
                FetchService::convertDisk);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertDisk(BaseCloudRequest request, DescribeDisksResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getDiskSet())) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_DISK_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_TENXUN.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        for (Disk res : response.getDiskSet()) {
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getDiskId()));
            ci.setType(res.getDiskType());
            ci.setCategory(res.getDiskUsage());
            ci.setSize(res.getDiskSize().floatValue());
            ci.setStatus(res.getDiskState());
            ci.setOpen_status(res.getDiskState());
            ci.setOpen_id(res.getDiskId());
            ci.setOpen_name(res.getDiskName());
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getPlacement().getZone());
            toCiResCloud(request, ci);
            //关联实例
            if (StrUtil.isNotEmpty(res.getInstanceId())) {
                Association esc = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
                associations.add(esc);
            }
            data.add(ci);
        }
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取磁盘快照
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchSnapshot(BaseCloudRequest request) {
        Entry.E2<DescribeSnapshotsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                CbsClient::DescribeSnapshots,
                FetchService::convertSnapshot);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, DescribeSnapshotsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getSnapshotSet())) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Snapshot res : response.getSnapshotSet()) {
            CmdbSnapshotRes ci = new CmdbSnapshotRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSnapshotId()));
            ci.setOpen_id(res.getSnapshotId());
            ci.setOpen_name(res.getSnapshotName());
            ci.setStatus(res.getSnapshotState());
            ci.setSize(NumberUtil.parseFloat(res.getDiskSize().toString()));
            ci.setType(res.getSnapshotType());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreateTime()));
            toCiResCloud(request, ci);
            //关联磁盘
            Association disk = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getDiskId()));
            associations.add(disk);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_SNAPSHOT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSnapshotRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取弹性公网EIP
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchEip(BaseCloudRequest request) {
        Entry.E2<DescribeAddressesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                VpcClient::DescribeAddresses,
                FetchService::convertEip);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertEip(BaseCloudRequest request, DescribeAddressesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getAddressSet())) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbEipRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (Address eip : response.getAddressSet()) {
            CmdbEipRes ci = new CmdbEipRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getAddressId()));
            ci.setBandwidth_type(eip.getInternetServiceProvider());
            ci.setBandwidth_speed(eip.getBandwidth().toString());
            ci.setElastic_ip(eip.getAddressIp());
            ci.setStatus(eip.getAddressStatus());
            ci.setOpen_id(eip.getAddressId());
            ci.setOpen_name(eip.getAddressName());
            toCiResCloud(request, ci);
            data.add(ci);
        }

        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_EIP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    /**
     * 获取负载均衡
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchLoadBalancer(BaseCloudRequest request) {
        Entry.E2<DescribeLoadBalancersResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                ClbClient::DescribeLoadBalancers,
                FetchService::convertLoadBalancer);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getTotalCount().intValue(),
                request.getBody().getCloud().getInteger("limit"));
    }

    public static Map<Class, List> convertLoadBalancer(BaseCloudRequest request, DescribeLoadBalancersResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (ArrayUtil.isEmpty(response.getLoadBalancerSet())) {
            result.put(CmdbLoadbalanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbLoadbalanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (LoadBalancer res : response.getLoadBalancerSet()) {
            CmdbLoadbalanceRes ci = new CmdbLoadbalanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getLoadBalancerId()));
            ci.setOpen_id(res.getLoadBalancerId());
            ci.setOpen_name(res.getLoadBalancerName());
            ci.setNetwork_type(res.getLoadBalancerType());
//            ci.setAddress(res.address);
            //负载均衡实例状态。取值：
            //inactive：实例已停止，此状态的实例监听不会再转发流量。
            //active：实例运行中，实例创建后，默认状态为 active。
            //locked：实例已锁定，实例已经被锁定。
            ci.setOpen_status(res.getStatus().toString());
            ci.setStatus(res.getStatus().toString());
            if (ObjectUtil.isNotEmpty(res.getNetworkAttributes())) {
                ci.setBandwidth_speed(res.getNetworkAttributes().getInternetMaxBandwidthOut().toString());
            }
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
            toCiResCloud(request, ci);
            //关联vpc
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(vpc);
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            associations.add(subnet);
            toCiResCloud(request, ci);

        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_TENXUN,
                        ResourceType.CMDB_LOADBALANCE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbLoadbalanceRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    //对象存储桶信息
    public static BaseResponse fetchBucket(BaseCloudRequest request) {
        List<Bucket> result = CloudClient.client.execute(request.getBody(), (FTExecute<COSClient, ListBucketsRequest, List<Bucket>>) COSClient::listBuckets);
        return BaseCloudService.toGourdResponse(
                BaseResponse.SUCCESS.of("获取存储桶" + result.size() + "个"),
                result,
                (Bucket t) -> {
                    JobInfo jobInfo = new JobInfo();
                    request.setAction(ActionType.FETCH_STORAGE_BUCKET_FILE);
                    request.getBody().getCloud().put("bucket", t);
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                });

    }

    //对象存储桶信息
    public static BaseResponse fetchBucketFile(BaseCloudRequest request) {
        try {
            List<CmdbBucketRes> buckets = new ArrayList<>();
            List<CmdbBucketFileRes> files = new ArrayList<>();
            List<Association> associations = new ArrayList<>();
            List<TmdbResourceSet> sets = new ArrayList<>();
            Map<Class, List> result = new HashMap<>();
            result.put(CmdbBucketRes.class, buckets);
            result.put(CmdbBucketFileRes.class, files);
            result.put(Association.class, associations);
            result.put(TmdbResourceSet.class, sets);

            COSClient client = CloudClient.client.client(COSClient.class, request.getBody());
            Bucket bucket = request.getBody().getCloud().getObject("bucket", Bucket.class);
            //查询桶对象列表
            //查询桶对象列表
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
            listObjectsRequest.setBucketName(bucket.getName());
            List<COSObjectSummary> os = LimitUtils.query(listObjectsRequest,
                    q -> CloudClient.client.execute(client, q, COSClient::listObjects),//执行云上查询所需要的函数
                    ObjectListing::getObjectSummaries,//取结果集
                    listObjectsRequest::setMarker,//设置新的查询条件
                    (ObjectListing t) -> t.getNextMarker()//从结果中获取新的查询条件
            );
            //查询桶的防盗链
            COSClient clientSync = ClientUtils.base.client(COSClient.class);
            BucketRefererConfiguration br = ClientUtils.base.execute(clientSync, new GetBucketRefererConfigurationRequest(bucket.getName()), COSClient::getBucketRefererConfiguration);
            //查询桶的访问权限
            clientSync = ClientUtils.base.client(COSClient.class);
            AccessControlList acl = ClientUtils.base.execute(clientSync, new GetBucketAclRequest(bucket.getName()), COSClient::getBucketAcl);
            //查询桶的版本控制状态
            clientSync = ClientUtils.base.client(COSClient.class);
            BucketVersioningConfiguration bvc = ClientUtils.base.execute(clientSync, new GetBucketVersioningConfigurationRequest(bucket.getName()), COSClient::getBucketVersioningConfiguration);
            //转换对象
            CmdbBucketRes bucketRes = convertBucket(request, bucket, os, br, acl, bvc);
            buckets.add(bucketRes);
            sets.add(BuilderResourceSet.of()
                    .withInfo(request.getBody().getAccess().getCmpId(),
                            CloudType.PUBLIC_TENXUN,
                            ResourceType.CMDB_BUCKET_RES
                    ).builderResourceSet(bucketRes.getRes_id(),
                            DevopsSide.DEVOPS_REGION,
                            request.getBody().getCloud().getString("regionId")));
            for (COSObjectSummary o : os) {
                //查询文件权限
                clientSync = ClientUtils.base.client(COSClient.class);
                AccessControlList oAcl = ClientUtils.base.execute(clientSync, new GetObjectAclRequest(bucket.getName(), o.getKey()), COSClient::getObjectAcl);
                clientSync = ClientUtils.base.client(COSClient.class);

                GetObjectRequest getObjectRequest = new GetObjectRequest(bucket.getName(), o.getKey());
                URL oUrl = ClientUtils.base.execute(clientSync, getObjectRequest, COSClient::getObjectUrl);
                CmdbBucketFileRes bucketFileRes = convertBucketFile(request, o, oAcl, oUrl, associations);
                associations.add(AssociationUtils.toAssociation(bucketFileRes, CmdbBucketRes.class, bucketRes.getRes_id()));
                files.add(bucketFileRes);
            }
            return BaseCloudService.fetchSend(request, result);
        } catch (Exception e) {
            log.error("同步对象存储桶失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步对象存储桶失败");
        }
    }

    public static CmdbBucketRes convertBucket(BaseCloudRequest request, Bucket bucket, List<COSObjectSummary> os, BucketRefererConfiguration br, AccessControlList acl, BucketVersioningConfiguration bvc) {
        CmdbBucketRes ci = new CmdbBucketRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), bucket.getName()));
        ci.setOpen_id(bucket.getName());
        ci.setOpen_name(bucket.getName());
        ci.setCreate_time(TimeUtils.utcDateToInstant(bucket.getCreationDate()).toEpochMilli());
        toCiResCloud(request, ci);
        //类型
        if (ObjectUtil.isNotEmpty(bucket.getBucketType())) {
            ci.setStorage_type(bucket.getBucketType());
        }
        //访问权限
        if (ObjectUtil.isNotEmpty(acl)) {
            ci.setAcl(acl.getGrantsAsList().stream().map(t -> t.getPermission()).collect(Collectors.toList()).toString());
        }
        //多版本状态
        if (ObjectUtil.isNotEmpty(bvc)) {
            ci.setVersioning_status(bvc.getStatus());
        }
        //防盗链
        //防盗链
        if (ObjectUtil.isNotEmpty(br) && !"Disabled".equals(br.getStatus())) {
            ci.setPolicy(JSONArray.toJSONString(br.getDomainList()));
        }
        //存量
        if (CollUtil.isNotEmpty(os)) {
            float storageSize = 0f;
            for (COSObjectSummary objectSummary : os) {
                storageSize += objectSummary.getSize();
            }
            ci.setSize(storageSize);
            ci.setObject_number("" + os.size());
        }
        //生命周期
//        if (ObjectUtil.isNotEmpty(lifecycle)) {
//            ci.setLifecycle(JSONArray.toJSONString(lifecycle));
//        }
        //静态页面
//        if (ObjectUtil.isNotEmpty(bucketWebsiteResult)) {
//            obj.setStaticPage(JSONArray.toJSONString(bucketWebsiteResult.getRoutingRules()));
//        }
        //跨域访问
//        if (ObjectUtil.isNotEmpty(cors)) {
//            obj.setCrossDomain(JSONArray.toJSONString(cors));
//        }
        return ci;
    }

    public static CmdbBucketFileRes convertBucketFile(BaseCloudRequest request, COSObjectSummary object, AccessControlList acl, URL url, List<Association> list) {
        CmdbBucketFileRes ci = new CmdbBucketFileRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), object.getKey()));
        ci.setOpen_id(object.getKey());
        ci.setOpen_name(object.getKey());
        ci.setStorage_type(object.getStorageClass());
        toCiResCloud(request, ci);
        //访问权限
        if (ObjectUtil.isNotEmpty(acl.getGrantsAsList())) {
            List<Permission> collect = acl.getGrantsAsList().stream().map(Grant::getPermission).collect(toList());
            ci.setAcl(collect.toString());
        }
        // 文件大小
        ci.setFile_size(UnitUtil.convert(object.getSize(), UnitUtil.BYTE, UnitUtil.GB, 3));
        // 最后修改时间
        if (object.getLastModified() != null) {
            ci.setUpdate_time(object.getLastModified().getTime());
        }
        // url
        ci.setUrl(url.toString());

        return ci;
    }


    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("offset", (t - 1) * 100);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }
}
