package com.futong.gemini.plugin.cloud.ali.tencent.common;

import cn.hutool.core.util.StrUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import com.qcloud.cos.COS;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.region.Region;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.net.MalformedURLException;
import java.net.URL;

@Slf4j
public class CloudClient extends BaseCloudClient {
    public static final CloudClient client = new CloudClient();

    private static final String cosEndpoint = "cos.{}";

    //获取Client对象
    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        HttpProfile httpProfile = new HttpProfile();
        try {
            if (clazz == COSClient.class) {
                return (C) cosClient(body);
            }
            if ("http".equalsIgnoreCase(body.getAccess().getProtocol())) {
                httpProfile.setProtocol(HttpProfile.REQ_HTTP);
            }
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                try {
                    URL url = new URL(body.getAuth().getString("proxyAddr"));
                    httpProfile.setProxyHost(url.getHost());
                    httpProfile.setProxyPort(url.getPort());
                } catch (MalformedURLException e) {
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT, e, "代理地址格式错误！");
                }
            }
            Credential cred = new Credential(body.getAccess().getUsername(), body.getAccess().getPassword());
            Constructor<C> constructor = clazz.getConstructor(Credential.class, String.class, ClientProfile.class);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            return constructor.newInstance(cred, body.getCloud().getString("regionId"), clientProfile);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }


    private static COS cosClient(BaseCloudRequestBody body) {
        try {
            String endpoint = body.getAuth().getString("endpoint");
            endpoint = StrUtil.emptyToDefault(endpoint, StrUtil.format(cosEndpoint, body.getCloud().getString("regionId")));
            endpoint = StrUtil.format(endpoint, body.getCloud().getString("regionId"));
            log.info("ossEndpoint:" + endpoint);
            return new COSClient(new BasicCOSCredentials(body.getAccess().getUsername(), body.getAccess().getPassword()), new ClientConfig(new Region(endpoint)));
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        }
    }

}

