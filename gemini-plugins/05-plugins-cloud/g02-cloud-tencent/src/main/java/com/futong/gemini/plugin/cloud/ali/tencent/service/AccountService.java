package com.futong.gemini.plugin.cloud.ali.tencent.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.i18n.FTI18nUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.tencent.common.ClientUtils;
import com.futong.gemini.plugin.cloud.ali.tencent.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.tencentcloudapi.cvm.v20170312.CvmClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

@Slf4j
@Configuration
public class AccountService {
    private static JSONArray accountForm = JSONArray.parseArray("[\n" +
            "\t{\n" +
            "\t\t\"field\": \"cloudAccount\",\n" +
            "\t\t\"label\": \"云账号\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"请输入云账号\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"username\",\n" +
            "\t\t\"label\": \"密钥KEY\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"请输入密钥KEY\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"password\",\n" +
            "\t\t\"label\": \"密钥\",\n" +
            "\t\t\"type\": \"password\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"请输入密钥\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"infoJson.proxyAddr\",\n" +
            "\t\t\"label\": \"代理地址\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": false,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"请输入代理地址\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"description\",\n" +
            "\t\t\"label\": \"描述\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": false,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"请输入描述\"\n" +
            "\t}\n" +
            "]");
    private static JSONArray accountFormEN_US = JSONArray.parseArray("[\n" +
            "\t{\n" +
            "\t\t\"field\": \"cloudAccount\",\n" +
            "\t\t\"label\": \"Cloud Account\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"Please enter your Cloud Account.\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"username\",\n" +
            "\t\t\"label\": \"Access Key\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"Please enter your Access Key\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"password\",\n" +
            "\t\t\"label\": \"Access Key Secret\",\n" +
            "\t\t\"type\": \"password\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": true,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"Please enter your Access Key Secret\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"jsonStr.proxyAddr\",\n" +
            "\t\t\"label\": \"Proxy Address\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": false,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"Please enter your Proxy Address\"\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"field\": \"description\",\n" +
            "\t\t\"label\": \"Description\",\n" +
            "\t\t\"type\": \"input\",\n" +
            "\t\t\"value\": \"\",\n" +
            "\t\t\"required\": false,\n" +
            "\t\t\"isUpdate\": true,\n" +
            "\t\t\"tips\": \"Please enter your Description\"\n" +
            "\t}\n" +
            "]");
    public static BaseResponse getAccountFormModel(JSONObject arguments) {
        if(Locale.US.equals(LocaleContextHolder.getLocale())){
            return new BaseDataResponse<>(accountFormEN_US);
        }
        return new BaseDataResponse<>(accountForm);
    }

    public static BaseResponse authCloudAccount(JSONObject arguments) {
        try {
            BaseClient.bodys.get().put("regionId", "ap-beijing");
            BaseClient.regions.set("ap-beijing");
            ClientUtils.base.execute(CvmClient::DescribeRegions);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            CloudClient.client.execute(request.getBody(), CvmClient::DescribeRegions);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    public static <Q, R, C> FTAction<JSONObject> toFTAction(FTExecute<C, Q, R> exec, ActionType actionType) {
        return (JSONObject arguments) -> doAction(exec, actionType);
    }

    public static <Q, R, C> BaseDataResponse<R> doAction(FTExecute<C, Q, R> exec, ActionType actionType) {
        try {
            return new BaseDataResponse<>(ClientUtils.base.execute(exec));
        } catch (Exception e) {
            String message = actionType.operationType().cname() + actionType.resourceType().name() + "失败";
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }
}
