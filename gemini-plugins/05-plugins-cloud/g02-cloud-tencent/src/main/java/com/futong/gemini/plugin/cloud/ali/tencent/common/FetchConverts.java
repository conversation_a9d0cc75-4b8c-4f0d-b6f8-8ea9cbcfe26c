package com.futong.gemini.plugin.cloud.ali.tencent.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.futong.common.utils.TimeUtils;
import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.InstanceStatus;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.qcloud.cos.model.*;
import com.tencentcloudapi.cbs.v20170312.models.Disk;
import com.tencentcloudapi.cbs.v20170312.models.Snapshot;
import com.tencentcloudapi.cdb.v20170320.models.InstanceInfo;
import com.tencentcloudapi.ckafka.v20190819.models.InstanceDetail;
import com.tencentcloudapi.clb.v20180317.models.LoadBalancer;
import com.tencentcloudapi.cvm.v20170312.models.Image;
import com.tencentcloudapi.cvm.v20170312.models.Instance;
import com.tencentcloudapi.cvm.v20170312.models.InstanceTypeConfig;
import com.tencentcloudapi.cvm.v20170312.models.KeyPair;
import com.tencentcloudapi.tdmq.v20200217.models.RocketMQClusterDetail;
import com.tencentcloudapi.tke.v20180525.models.Cluster;
import com.tencentcloudapi.vpc.v20170312.models.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

@Slf4j
public class FetchConverts {

    public static CmdbInstanceRes toRes(Instance res,
                                        List<CmdbIpRes> ips,
                                        List<Association> list,
                                        BuilderResourceSet builderResourceSet) {
        CmdbInstanceRes ci = new CmdbInstanceRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setCpu_size(res.getCPU().intValue());
        ci.setMem_size(res.getMemory().intValue() * 1024);
        ci.setStatus(res.getInstanceState());
        switch (res.getInstanceState()) {
            case "PENDING":
                ci.setStatus(InstanceStatus.BUILDING.name());
                break;
            case "RUNNING":
                ci.setStatus(InstanceStatus.RUNNING.name());
                break;
            case "STARTING":
                ci.setStatus(InstanceStatus.STARTING.name());
                break;
            case "REBOOTING":
                ci.setStatus(InstanceStatus.RESTARTING.name());
                break;
            case "STOPPING":
                ci.setStatus(InstanceStatus.STOPPING.name());
                break;
            case "STOPPED":
                ci.setStatus(InstanceStatus.STOPPED.name());
                break;
        }
        ci.setDesc(res.getInstanceId());
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());

        builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getPlacement().getZone());
        //关联规格
        Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceType()));
        list.add(flavor);
        //关联镜像
        Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getImageId()));
        list.add(image);
        //关联镜像OS
        Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getImageId()));
        list.add(os);
        //关联网络VPC
        Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getVpcId()));
        list.add(vpc);
        //关联网络VPC子网
        Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getSubnetId()));
        list.add(subnet);
        //添加私网IP
        if (ArrayUtil.isNotEmpty(res.getPrivateIpAddresses())) {
            for (String primaryIpAddress : res.getPrivateIpAddresses()) {
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), primaryIpAddress));
                ip.setType("private_ip");
                ip.setAddress(primaryIpAddress);
                ip.setOpen_id(primaryIpAddress);
                ip.setOpen_name(primaryIpAddress);
                ip.setCloud_type(CloudType.PUBLIC_TENXUN.value());
                ip.setAccount_id(ClientUtils.auths.get().getCmpId());
                ips.add(ip);
                //关联网络VPC
                list.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getVpcId())));
                //关联网络VPC子网
                list.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getSubnetId())));
                //关联云主机
                list.add(AssociationUtils.toAssociation(ip, ci));
            }
        }
        //添加公网弹性IP,关联弹性IP
        if (ArrayUtil.isNotEmpty(res.getPublicIpAddresses())) {
            for (String ipAddress : res.getPublicIpAddresses()) {
                CmdbIpRes ip = new CmdbIpRes();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), ipAddress));
                ip.setType("public_ip");
                ip.setAddress(ipAddress);
                ip.setOpen_id(ipAddress);
                ip.setOpen_name(ipAddress);
                ip.setCloud_type(CloudType.PUBLIC_TENXUN.value());
                ip.setAccount_id(ClientUtils.auths.get().getCmpId());
                ips.add(ip);
                //IP关联云主机
                list.add(AssociationUtils.toAssociation(ip, ci));
            }
        }
        //关联安全组
        if (ArrayUtil.isNotEmpty(res.getSecurityGroupIds())) {
            Arrays.stream(res.getSecurityGroupIds()).forEach(t -> {
                Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), t));
                list.add(securityGroup);
            });
        }
        return ci;
    }

    public static CmdbInstanceRes toRes(Instance res, List<CmdbIpRes> ips, List<Association> list) {
        CmdbInstanceRes ci = new CmdbInstanceRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setCpu_size(res.getCPU().intValue());
        ci.setMem_size(res.getMemory().intValue());
        ci.setStatus(res.getInstanceType());
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联规格
        Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceType()));
        list.add(flavor);
        //关联镜像
        Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getImageId()));
        list.add(image);
        //关联网络VPC
        Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getVpcId()));
        list.add(vpc);
        //关联网络VPC子网
        Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getSubnetId()));
        list.add(subnet);
        //添加私网IP
        if (ArrayUtil.isNotEmpty(res.getPrivateIpAddresses())) {
            for (String primaryIpAddress : res.getPrivateIpAddresses()) {
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), primaryIpAddress));
                ip.setType("private_ip");
                ip.setAddress(primaryIpAddress);
                ip.setOpen_id(primaryIpAddress);
                ip.setOpen_name(primaryIpAddress);
                ip.setCloud_type(CloudType.PUBLIC_TENXUN.value());
                ip.setAccount_id(ClientUtils.auths.get().getCmpId());
                ips.add(ip);
                //关联网络VPC
                list.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getVpcId())));
                //关联网络VPC子网
                list.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVirtualPrivateCloud().getSubnetId())));
                //关联云主机
                list.add(AssociationUtils.toAssociation(ip, ci));
            }
        }
        //添加公网弹性IP,关联弹性IP
        if (ArrayUtil.isNotEmpty(res.getPublicIpAddresses())) {
            for (String ipAddress : res.getPublicIpAddresses()) {
                CmdbIpRes ip = new CmdbIpRes();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), ipAddress));
                ip.setType("public_ip");
                ip.setAddress(ipAddress);
                ip.setOpen_id(ipAddress);
                ip.setOpen_name(ipAddress);
                ip.setCloud_type(CloudType.PUBLIC_TENXUN.value());
                ip.setAccount_id(ClientUtils.auths.get().getCmpId());
                ips.add(ip);
                //IP关联云主机
                list.add(AssociationUtils.toAssociation(ip, ci));
            }
        }
        //关联安全组
        if (ArrayUtil.isNotEmpty(res.getSecurityGroupIds())) {
            Arrays.stream(res.getSecurityGroupIds()).forEach(t -> {
                Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), t));
                list.add(securityGroup);
            });
        }
        return ci;
    }

    public static CmdbNetcardRes toRes(NetworkInterface res, List<Association> list) {
        CmdbNetcardRes ci = new CmdbNetcardRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getNetworkInterfaceId()));
        ci.setOpen_id(res.getNetworkInterfaceId());
        ci.setOpen_name(res.getNetworkInterfaceName());
        ci.setType(res.getAttachType() == null ? "" : res.getAttachType().toString());
        ci.setStatus(res.getState());
        ci.setMac_address(res.getMacAddress());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        if (ArrayUtil.isNotEmpty(res.getPrivateIpAddressSet())) {
            StringBuilder ipv4s = new StringBuilder();
            for (PrivateIpAddressSpecification ipv4 : res.getPrivateIpAddressSet()) {
                if (StrUtil.isNotEmpty(ipv4.getPrivateIpAddress())) {
                    ipv4s.append(ipv4.getPrivateIpAddress()).append(",");
                    list.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), ipv4.getPrivateIpAddress())));
                }

                if (StringUtils.isNotEmpty(ipv4.getPublicIpAddress())) {
                    ipv4s.append(ipv4.getPublicIpAddress()).append(",");
                    list.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), ipv4.getPublicIpAddress())));
                }
            }
            if (ipv4s.length() > 0) {
                ipv4s.setLength(ipv4s.length() - 1);
                ci.setIpv4_address(ipv4s.toString());
            }
        }
        if (ArrayUtil.isNotEmpty(res.getIpv6AddressSet())) {
            StringBuilder ipv6s = new StringBuilder();
            for (Ipv6Address ipv6 : res.getIpv6AddressSet()) {
                if (StrUtil.isNotEmpty(ipv6.getAddress())) {
                    ipv6s.append(ipv6.getAddress()).append(",");
                    list.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), ipv6.getAddress())));
                }
            }
            if (ipv6s.length() > 0) {
                ipv6s.setLength(ipv6s.length() - 1);
                ci.setIpv6_address(ipv6s.toString());
            }
        }
        //关联VPC
        if (StrUtil.isNotEmpty(res.getVpcId())) {
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
            list.add(vpc);
        }
        //关联子网
        if (StrUtil.isNotEmpty(res.getSubnetId())) {
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSubnetId()));
            list.add(subnet);
        }
        return ci;
    }

    public static CmdbFlavor toRes(InstanceTypeConfig t) {
        CmdbFlavor ci = new CmdbFlavor();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), t.getInstanceType()));
        ci.setCpu_size(t.getCPU().intValue());
        ci.setMem_size(t.getMemory().intValue());
        ci.setOpen_id(t.getInstanceType());
        ci.setOpen_name(t.getInstanceType());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static CmdbVpcRes toRes(Vpc res) {
        CmdbVpcRes ci = new CmdbVpcRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
        ci.setOpen_id(res.getVpcId());
        ci.setOpen_name(res.getVpcName());
        ci.setCidr(res.getCidrBlock());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static CmdbImageRes toRes(Image res) {
        CmdbImageRes ci = new CmdbImageRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getImageId()));
        //TODO
        ci.setType(res.getPlatform());
        ci.setSize(res.getImageSize().floatValue());
        ci.setStatus(res.getImageState());
        ci.setDesc(res.getImageDescription());
        ci.setOpen_id(res.getImageId());
        ci.setOpen_name(res.getImageName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static CmdbImageRes toRes(Image res, List<CmdbOsRes> resOs) {
        CmdbImageRes ci = new CmdbImageRes();
        CmdbOsRes os = new CmdbOsRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getImageId()));
        ci.setType(res.getPlatform());
        ci.setSize(res.getImageSize().floatValue());
        ci.setStatus(res.getImageState());
        ci.setDesc(res.getImageDescription());
        ci.setOpen_id(res.getImageId());
        ci.setOpen_name(res.getImageName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //操作新系统OS对象
        os.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getImageId()));
        os.setCpu_arch(res.getArchitecture());
        os.setName(res.getOsName());
        os.setType(res.getPlatform());
//        os.setVersion(res.imageVersion);
        os.setFull_name(res.getImageName());
        os.setOpen_name(res.getImageName());
        os.setOpen_id(res.getImageId());
        os.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        os.setAccount_id(ClientUtils.auths.get().getCmpId());
        resOs.add(os);
        return ci;
    }

    public static CmdbKeypairRes toRes(KeyPair res) {
        CmdbKeypairRes ci = new CmdbKeypairRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getKeyId()));
        ci.setOpen_id(res.getKeyId());
        ci.setOpen_name(res.getKeyName());
//        ci.setFingerprint(res.);
        ci.setPublic_key(res.getPublicKey());
        ci.setPrivate_key(res.getPrivateKey());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static CmdbEipRes toRes(Address res) {
        CmdbEipRes ci = new CmdbEipRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getAddressId()));
        ci.setBandwidth_type(res.getInternetServiceProvider());
        ci.setBandwidth_speed(res.getBandwidth().toString());
        ci.setElastic_ip(res.getAddressIp());
        ci.setStatus(res.getAddressStatus());
//        ci.setDesc(res.getDescritpion());
        ci.setOpen_id(res.getAddressId());
        ci.setOpen_name(res.getAddressName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static CmdbDiskRes toRes(Disk res, List<Association> list) {
        CmdbDiskRes ci = new CmdbDiskRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getDiskId()));
        ci.setType(res.getDiskUsage());
        ci.setCategory(res.getDiskType());
        ci.setSize(res.getDiskSize().floatValue());
        ci.setStatus(res.getDiskState());
//        ci.setDesc(res.getDescription());
        ci.setOpen_id(res.getDiskId());
        ci.setOpen_name(res.getDiskName());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        ci.setExtend1(String.valueOf(res.getShareable()));
        //关联实例
        if (StrUtil.isNotEmpty(res.getInstanceId())) {
            Association esc = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
            list.add(esc);
        }
        return ci;
    }

    public static CmdbSecuritygroupRes toRes(SecurityGroup res, List<Association> list) {
        CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSecurityGroupId()));
        ci.setOpen_id(res.getSecurityGroupId());
        ci.setOpen_name(res.getSecurityGroupName());
        ci.setDesc(res.getSecurityGroupDesc());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        return ci;
    }

    public static void toRes(SecurityGroupPolicy res, CmdbSecuritygroupRes sg, List<CmdbSecuritygroupRule> resRules, List<Association> list, String direction) {
        CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSecurityGroupId(), res.getProtocol(), res.getPort(), res.getCidrBlock(), res.getIpv6CidrBlock()));
        ci.setOpen_id(res.getSecurityGroupId());
        ci.setOpen_name(sg.getOpen_name());
        //TODO
        ci.setDirection(direction);
        ci.setPolicy(res.getAction());
        ci.setExtend1(res.getProtocol());
        ci.setPort_range(res.getPort());
        ci.setSource_cidr(res.getCidrBlock());
//        ci.setDest_cidr(res.destCidrIp);
        ci.setDesc(res.getPolicyDescription());
//        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getModifyTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        resRules.add(ci);
        //关联安全组规则
        Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, sg.getRes_id());
        list.add(rule);
    }

    public static CmdbSnapshotRes toRes(Snapshot res, List<Association> list) {
        CmdbSnapshotRes ci = new CmdbSnapshotRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSnapshotId()));
        ci.setOpen_id(res.getSnapshotId());
        ci.setOpen_name(res.getSnapshotName());
        ci.setStatus(res.getSnapshotState());
        ci.setSize(res.getDiskSize().floatValue());
        ci.setType(res.getSnapshotType());
        ci.setExtend1(res.getDiskId());
        ci.setExtend2(String.valueOf(res.getEncrypt()));
//        ci.setDesc(res.getDescription());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联磁盘
        Association disk = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getDiskId()));
        list.add(disk);
        return ci;
    }

    public static CmdbNatRes toRes(NatGateway res, List<Association> list) {
        CmdbNatRes ci = new CmdbNatRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getNatGatewayId()));
        ci.setOpen_id(res.getNatGatewayId());
        ci.setOpen_name(res.getNatGatewayName());
        ci.setStatus(res.getState());
        ci.setAdmin_state(res.getNetworkState());
//        ci.setSpec(res.spec);
//        ci.setDesc(res.getDescription());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联VPC
        Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
        list.add(vpc);
        //关联
        if (ArrayUtil.isNotEmpty(res.getPublicIpAddressSet())) {
            Stream.of(res.getPublicIpAddressSet()).forEach(t -> {
                Association eip = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), t.getAddressId()));
                list.add(eip);
            });
        }
        return ci;
    }

    public static CmdbRouteRes toRes(RouteTable res, List<Association> list) {
        CmdbRouteRes ci = new CmdbRouteRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRouteTableId()));
        ci.setOpen_id(res.getRouteTableId());
        ci.setOpen_name(res.getRouteTableName());
//        ci.setStatus(res.status);
//        ci.setDefault_route(res.routeTableType);
//        ci.setDesc(res.getDescription());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联VPC
        Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
        list.add(vpc);
        return ci;
    }

    public static CmdbSubnetRes toRes(Subnet res, List<Association> list) {
        CmdbSubnetRes ci = new CmdbSubnetRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSubnetId()));
        ci.setOpen_id(res.getSubnetId());
        ci.setOpen_name(res.getSubnetName());
        ci.setCidr_ipv4(res.getCidrBlock());
        ci.setCidr_ipv6(res.getIpv6CidrBlock());
//        ci.setStatus(res.status);
//        ci.setDesc(res.getDescription());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联VPC
        Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
        list.add(rule);
        return ci;
    }

    public static CmdbLoadbalanceRes toRes(LoadBalancer res, List<Association> list) {
        CmdbLoadbalanceRes ci = new CmdbLoadbalanceRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getLoadBalancerId()));
        ci.setOpen_id(res.getLoadBalancerId());
        ci.setOpen_name(res.getLoadBalancerName());
        ci.setNetwork_type(res.getLoadBalancerType());
        if (ObjectUtil.isNotEmpty(res.getNetworkAttributes())) {
            ci.setBandwidth_speed(res.getNetworkAttributes().getInternetMaxBandwidthOut().toString());
        }
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //关联vpc
        Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getVpcId()));
        list.add(vpc);
        Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getSubnetId()));
        list.add(subnet);
        return ci;
    }

    public static CmdbBucketRes toRes(String bucketName, Bucket bucket, List<COSObjectSummary> os, BucketRefererConfiguration br, AccessControlList acl, BucketVersioningConfiguration bvc, List<Association> list) {
        CmdbBucketRes ci = new CmdbBucketRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), bucketName));
        ci.setOpen_id(bucketName);
        ci.setOpen_name(bucketName);
        ci.setCreate_time(TimeUtils.utcDateToInstant(bucket.getCreationDate()).toEpochMilli());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        //类型
//        if (ObjectUtil.isNotEmpty(bucket.getStorageClass())) {
//            ci.setStorage_type(bucket.getStorageClass().toString());
//        }
        //访问权限
        if (ObjectUtil.isNotEmpty(acl)) {
            ci.setAcl(acl.getGrantsAsList().stream().map(t -> t.getPermission()).collect(Collectors.toList()).toString());
        }
        //多版本状态
        if (ObjectUtil.isNotEmpty(bvc)) {
            ci.setVersioning_status(bvc.getStatus());
        }
        //防盗链
        if (ObjectUtil.isNotEmpty(br) && !"Disabled".equals(br.getStatus())) {
            ci.setPolicy(JSONArray.toJSONString(br.getDomainList()));
        }
        //存量
        if (CollUtil.isNotEmpty(os)) {
            float storageSize = 0f;
            for (COSObjectSummary objectSummary : os) {
                storageSize += objectSummary.getSize();
            }
            ci.setSize(storageSize);
            ci.setObject_number("" + os.size());
        }
        //生命周期
//        if (ObjectUtil.isNotEmpty(lifecycle)) {
//            ci.setLifecycle(JSONArray.toJSONString(lifecycle));
//        }
        //静态页面
//        if (ObjectUtil.isNotEmpty(bucketWebsiteResult)) {
//            obj.setStaticPage(JSONArray.toJSONString(bucketWebsiteResult.getRoutingRules()));
//        }
        //跨域访问
//        if (ObjectUtil.isNotEmpty(cors)) {
//            obj.setCrossDomain(JSONArray.toJSONString(cors));
//        }
        return ci;
    }

    public static CmdbBucketFileRes toFile(String bucketResId, COSObjectSummary object, AccessControlList acl, URL url, List<Association> list) {
        CmdbBucketFileRes ci = new CmdbBucketFileRes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), object.getKey()));
        ci.setOpen_id(object.getKey());
        ci.setOpen_name(object.getKey());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(ClientUtils.auths.get().getCmpId());
        ci.setStorage_type(object.getStorageClass());
        //访问权限
        if (ObjectUtil.isNotEmpty(acl.getGrantsAsList())) {
            List<Permission> collect = acl.getGrantsAsList().stream().map(Grant::getPermission).collect(toList());
            ci.setAcl(collect.toString());
        }
        // 文件大小
        ci.setFile_size(Long.valueOf(object.getSize()).floatValue());
        // 最后修改时间
        if (object.getLastModified() != null) {
            ci.setUpdate_time(object.getLastModified().getTime());
        }
        // url
        ci.setUrl(url.toString());
        list.add(AssociationUtils.toAssociation(ci, CmdbBucketRes.class, bucketResId));
        return ci;
    }

    /**
     * redis转换
     * @param res
     * @return
     */
    public static CmdbRedis toRes(com.tencentcloudapi.redis.v20180412.models.InstanceSet res) {
        CmdbRedis ci = new CmdbRedis();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setCapacity(String.valueOf(res.getSize()));
        ci.setEngine_version(res.getEngine());
        ci.setType(res.getProductType());
        ci.setArchitecture_type(String.valueOf(res.getType()));
        ci.setStatus(res.getInstanceTitle());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        ci.setOpen_create_time(TimeUtils.utcStringToMilliLong(res.getCreatetime()));
        return ci;
    }

    /**
     * mysql rds转换
     * @param res
     * @return
     */
    public static CmdbRds toRes(InstanceInfo res) {
        CmdbRds ci = new CmdbRds();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setCpu_size(res.getCpu().intValue());
        ci.setMem_size(res.getMemory().intValue());
        ci.setStorage(res.getVolume().floatValue());
        ci.setEngine("MySQL");
        ci.setEngine_version(res.getEngineVersion());
        ci.setDb_instance_type(String.valueOf(res.getInstanceType()));
        ci.setStatus(String.valueOf(res.getStatus()));
        ci.setOpen_create_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        return ci;
    }

    /**
     * kafka转换
     * @param res
     * @return
     */
    public static CmdbKafka toRes(InstanceDetail res) {
        CmdbKafka ci = new CmdbKafka();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setStatus(String.valueOf(res.getStatus()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        return ci;
    }

    /**
     * k8s集群转换
     * @param res
     * @return
     */
    public static CmdbKubernetes toRes(Cluster res) {
        CmdbKubernetes ci = new CmdbKubernetes();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getClusterId()));
        ci.setOpen_id(res.getClusterId());
        ci.setOpen_name(res.getClusterName());
        ci.setCluster_type(res.getClusterType());
        ci.setCluster_version(res.getClusterVersion());
        ci.setSize(res.getClusterNodeNum().intValue()+res.getClusterMaterNodeNum().intValue());
        ci.setStatus(res.getClusterStatus());
        ci.setDesc(res.getClusterDescription());
        ci.setOpen_create_time(TimeUtils.utcStringToMilliLong(res.getCreatedTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        return ci;
    }

    /**
     * RocketMQ转换
     * @param res
     * @return
     */
    public static CmdbRocketmq toRes(RocketMQClusterDetail res) {
        CmdbRocketmq ci = new CmdbRocketmq();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInfo().getClusterId()));
        ci.setOpen_id(res.getInfo().getClusterId());
        ci.setOpen_name(res.getInfo().getClusterName());
        ci.setStatus(res.getStatus().toString());
        ci.setDesc(res.getInfo().getRemark());
        ci.setOpen_create_time(res.getInfo().getCreateTime());
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        return ci;
    }

    /**
     * MongoDB转换
     * @param res
     * @return
     */
    public static CmdbMongo toRes(com.tencentcloudapi.mongodb.v20190725.models.InstanceDetail res) {
        CmdbMongo ci = new CmdbMongo();
        ci.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getInstanceId()));
        ci.setOpen_id(res.getInstanceId());
        ci.setOpen_name(res.getInstanceName());
        ci.setExpire_time(TimeUtils.utcStringToMilliLong(res.getDeadLine()));
        ci.setStart_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
        ci.setEnd_time(TimeUtils.utcStringToMilliLong(res.getDeadLine()));
        ci.setCharge_type(res.getPayMode().toString());
        ci.setStatus(res.getInstanceStatusDesc());
        ci.setEngine("MongoDB");
        ci.setStorage(res.getVolume().floatValue()/1024);
        ci.setType(res.getInstanceType().toString());
        ci.setOpen_status(res.getInstanceStatusDesc());
        ci.setNetwork_type(res.getNetType().toString());
        ci.setExpired(res.getInstanceStatusDesc());
        ci.setProtocol_type(res.getNetType().toString());
        ci.setCpu_size(res.getCpuNum().intValue());
        ci.setMem_size(res.getMemory().intValue());
        ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
        ci.setCloud_type(CloudType.PUBLIC_TENXUN.value());
        ci.setAccount_id(BaseClient.auths.get().getCmpId());
        return ci;
    }
}