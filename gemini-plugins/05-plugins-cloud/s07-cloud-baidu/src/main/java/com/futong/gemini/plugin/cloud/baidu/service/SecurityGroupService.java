package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.SecurityGroupRuleModel;
import com.baidubce.services.bcc.model.TagModel;
import com.baidubce.services.bcc.model.instance.BindSecurityGroupRequest;
import com.baidubce.services.bcc.model.instance.UnbindSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.CreateSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.DeleteSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.SecurityGroupRuleOperateRequest;
import com.baidubce.services.bcc.model.securitygroup.UpdateSecurityGroupRuleRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度云安全组服务类
 * 
 * 提供安全组管理功能，包括：
 * - 创建安全组
 * - 删除安全组
 * - 修改安全组属性
 * - 绑定/解绑安全组
 * - 管理安全组规则
 * 
 * 注意：部分功能可能受百度云SDK限制，使用HTTP API作为备选方案
 */
@Slf4j
public class SecurityGroupService {

    /**
     * 创建安全组
     * 
     * 使用百度云SDK创建安全组，支持设置规则和标签
     * 支持参数：name, description, vpcId, rules(List<JSONObject>), tags(List<JSONObject>)
     */
    public static BaseResponse createSecurityGroup(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云安全组（SDK方式）");
            JSONObject cloud = request.getBody().getCloud();
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "name")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name");
            }
            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            CreateSecurityGroupRequest sdkReq = new CreateSecurityGroupRequest();
            sdkReq.setName(cloud.getString("name"));
            if (cloud.containsKey("description")) {
                sdkReq.setDesc(cloud.getString("description"));
            }
            if (cloud.containsKey("vpcId")) {
                sdkReq.setVpcId(cloud.getString("vpcId"));
            }
            // 处理规则
            if (cloud.containsKey("rules")) {
                java.util.List<SecurityGroupRuleModel> rules = new java.util.ArrayList<>();
                for (Object obj : cloud.getJSONArray("rules")) {
                    JSONObject rule = JSONObject.parseObject(JSONObject.toJSONString(obj));
                    SecurityGroupRuleModel ruleModel = new SecurityGroupRuleModel();
                    if (rule.containsKey("remark")) ruleModel.withRemark(rule.getString("remark"));
                    if (rule.containsKey("protocol")) ruleModel.withProtocol(rule.getString("protocol"));
                    if (rule.containsKey("portRange")) ruleModel.withPortRange(rule.getString("portRange"));
                    if (rule.containsKey("direction")) ruleModel.withDirection(rule.getString("direction"));
                    if (rule.containsKey("sourceIp")) ruleModel.withSourceIp(rule.getString("sourceIp"));
                    if (rule.containsKey("sourceGroupId")) ruleModel.withSourceGroupId(rule.getString("sourceGroupId"));
                    if (rule.containsKey("destIp")) ruleModel.withDestIp(rule.getString("destIp"));
                    if (rule.containsKey("destGroupId")) ruleModel.withDestGroupId(rule.getString("destGroupId"));
                    rules.add(ruleModel);
                }
                sdkReq.setRules(rules);
            }
            // 处理标签
            if (cloud.containsKey("tags")) {
                java.util.List<TagModel> tags = new java.util.ArrayList<>();
                for (Object obj : cloud.getJSONArray("tags")) {
                    JSONObject tag = JSONObject.parseObject(JSONObject.toJSONString(obj));
                    TagModel tagModel = new TagModel();
                    if (tag.containsKey("tagKey")) tagModel.withTagKey(tag.getString("tagKey"));
                    if (tag.containsKey("tagValue")) tagModel.withTagValue(tag.getString("tagValue"));
                    tags.add(tagModel);
                }
                sdkReq.setTags(tags);
            }
            // 调用SDK创建安全组
            String securityGroupId = bccClient.createSecurityGroup(sdkReq).getSecurityGroupId();
            JSONObject result = new JSONObject();
            result.put("securityGroupId", securityGroupId);
            result.put("name", cloud.getString("name"));
            log.info("创建百度云安全组成功，安全组ID: {}", securityGroupId);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("创建百度云安全组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除安全组
     * 
     * 使用百度云SDK删除安全组
     * 支持参数：securityGroupId
     */
    public static BaseResponse deleteSecurityGroup(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云安全组（SDK方式）");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "securityGroupId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：securityGroupId");
            }
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String securityGroupId = cloud.getString("securityGroupId");
            
            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            DeleteSecurityGroupRequest sdkReq = new DeleteSecurityGroupRequest();
            sdkReq.setSecurityGroupId(securityGroupId);
            
            // 调用SDK删除安全组
            bccClient.deleteSecurityGroup(sdkReq);
            
            log.info("删除百度云安全组成功，安全组ID: {}", securityGroupId);
            return new BaseDataResponse<>(new JSONObject().fluentPut("securityGroupId", securityGroupId));
            
        } catch (Exception e) {
            log.error("删除百度云安全组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组失败: " + e.getMessage()), e);
        }
    }

    /**
     * 绑定安全组到实例
     * 
     * 使用百度云SDK绑定安全组到实例
     * 支持参数：instanceId, securityGroupId
     */
    public static BaseResponse bindSecurityGroup(BaseCloudRequest request) {
        try {
            log.info("开始绑定百度云安全组到实例（SDK方式）");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "instanceId", "securityGroupId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId, securityGroupId");
            }
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String instanceId = cloud.getString("instanceId");
            String securityGroupId = cloud.getString("securityGroupId");
            
            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            BindSecurityGroupRequest sdkReq = new BindSecurityGroupRequest();
            sdkReq.setInstanceId(instanceId);
            sdkReq.setSecurityGroupId(securityGroupId);
            
            // 调用SDK绑定安全组
            bccClient.bindInstanceToSecurityGroup(sdkReq);
            
            log.info("绑定百度云安全组到实例成功，实例ID: {}, 安全组ID: {}", instanceId, securityGroupId);
            JSONObject result = new JSONObject();
            result.put("instanceId", instanceId);
            result.put("securityGroupId", securityGroupId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("绑定百度云安全组到实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定安全组失败: " + e.getMessage()), e);
        }
    }

    /**
     * 解绑安全组
     * 
     * 使用百度云SDK解绑安全组
     * 支持参数：instanceId, securityGroupId
     */
    public static BaseResponse unbindSecurityGroup(BaseCloudRequest request) {
        try {
            log.info("开始解绑百度云安全组（SDK方式）");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "instanceId", "securityGroupId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId, securityGroupId");
            }
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String instanceId = cloud.getString("instanceId");
            String securityGroupId = cloud.getString("securityGroupId");
            
            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            UnbindSecurityGroupRequest sdkReq = new UnbindSecurityGroupRequest();
            sdkReq.setInstanceId(instanceId);
            sdkReq.setSecurityGroupId(securityGroupId);
            
            // 调用SDK解绑安全组
            bccClient.unbindInstanceFromSecurityGroup(sdkReq);
            
            log.info("解绑百度云安全组成功，实例ID: {}, 安全组ID: {}", instanceId, securityGroupId);
            JSONObject result = new JSONObject();
            result.put("instanceId", instanceId);
            result.put("securityGroupId", securityGroupId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("解绑百度云安全组失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑安全组失败: " + e.getMessage()), e);
        }
    }

    /**
     * 创建安全组规则
     *
     * 使用百度云SDK创建安全组规则
     * 支持参数：securityGroupId, direction, protocol, portRange, sourceIp, sourceGroupId, destIp, destGroupId, remark
     */
    public static BaseResponse createSecurityGroupRule(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云安全组规则（SDK方式）");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "securityGroupId", "direction", "protocol")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：securityGroupId, direction, protocol");
            }

            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String securityGroupId = cloud.getString("securityGroupId");
            String direction = cloud.getString("direction");
            String protocol = cloud.getString("protocol");
            String portRange = cloud.getString("portRange");
            String sourceIp = cloud.getString("sourceIp");
            String sourceGroupId = cloud.getString("sourceGroupId");
            String destIp = cloud.getString("destIp");
            String destGroupId = cloud.getString("destGroupId");
            String remark = cloud.getString("remark");

            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            SecurityGroupRuleOperateRequest sdkReq = new SecurityGroupRuleOperateRequest();
            sdkReq.setSecurityGroupId(securityGroupId);

            // 构造安全组规则模型
            SecurityGroupRuleModel securityGroupRuleModel = new SecurityGroupRuleModel()
                    .withDirection(direction)
                    .withProtocol(protocol);

            // 设置可选参数
            if (portRange != null && !portRange.isEmpty()) {
                securityGroupRuleModel.withPortRange(portRange);
            }
            if (sourceIp != null && !sourceIp.isEmpty()) {
                securityGroupRuleModel.withSourceIp(sourceIp);
            }
            if (sourceGroupId != null && !sourceGroupId.isEmpty()) {
                securityGroupRuleModel.withSourceGroupId(sourceGroupId);
            }
            if (destIp != null && !destIp.isEmpty()) {
                securityGroupRuleModel.withDestIp(destIp);
            }
            if (destGroupId != null && !destGroupId.isEmpty()) {
                securityGroupRuleModel.withDestGroupId(destGroupId);
            }
            if (remark != null && !remark.isEmpty()) {
                securityGroupRuleModel.withRemark(remark);
            }

            // 设置安全组规则
            sdkReq.setRule(securityGroupRuleModel);

            // 调用SDK创建安全组规则
            bccClient.authorizeSecurityGroupRule(sdkReq);

            log.info("创建百度云安全组规则成功，安全组ID: {}, 方向: {}, 协议: {}", securityGroupId, direction, protocol);
            JSONObject result = new JSONObject();
            result.put("securityGroupId", securityGroupId);
            result.put("direction", direction);
            result.put("protocol", protocol);
            if (portRange != null) result.put("portRange", portRange);
            if (sourceIp != null) result.put("sourceIp", sourceIp);
            if (sourceGroupId != null) result.put("sourceGroupId", sourceGroupId);
            if (destIp != null) result.put("destIp", destIp);
            if (destGroupId != null) result.put("destGroupId", destGroupId);
            if (remark != null) result.put("remark", remark);
            return new BaseDataResponse<>(result);

        } catch (Exception e) {
            log.error("创建百度云安全组规则失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组规则失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改安全组规则
     *
     * 使用百度云SDK修改安全组规则
     * 支持参数：securityGroupId, direction, protocol, portRange, sourceIp, sourceGroupId, destIp, destGroupId, remark
     */
    public static BaseResponse updateSecurityGroupRule(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云安全组规则（SDK方式）");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "securityGroupRuleId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：securityGroupRuleId");
            }
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String securityGroupRuleId = cloud.getString("securityGroupRuleId");
            String remark = cloud.getString("remark");
            String portRange = cloud.getString("portRange");
            String sourceIp = cloud.getString("sourceIp");
            String sourceGroupId = cloud.getString("sourceGroupId");
            String destIp = cloud.getString("destIp");
            String destGroupId = cloud.getString("destGroupId");
            String protocol = cloud.getString("protocol");

            // 参数互斥性校验
            if (sourceIp != null && !sourceIp.isEmpty() && sourceGroupId != null && !sourceGroupId.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("sourceIp与sourceGroupId不能同时存在");
            }
            if (destIp != null && !destIp.isEmpty() && destGroupId != null && !destGroupId.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("destIp与destGroupId不能同时存在");
            }

            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            UpdateSecurityGroupRuleRequest sdkReq = new UpdateSecurityGroupRuleRequest();

            // 设置必填参数
            sdkReq.setSecurityGroupRuleId(securityGroupRuleId);

            // 设置可选参数
            if (remark != null && !remark.isEmpty()) {
                sdkReq.setRemark(remark);
            }
            if (portRange != null && !portRange.isEmpty()) {
                sdkReq.setPortRange(portRange);
            }
            if (sourceIp != null && !sourceIp.isEmpty()) {
                sdkReq.setSourceIp(sourceIp);
            }
            if (sourceGroupId != null && !sourceGroupId.isEmpty()) {
                sdkReq.setSourceGroupId(sourceGroupId);
            }
            if (destIp != null && !destIp.isEmpty()) {
                sdkReq.setDestIp(destIp);
            }
            if (destGroupId != null && !destGroupId.isEmpty()) {
                sdkReq.setDestGroupId(destGroupId);
            }
            if (protocol != null && !protocol.isEmpty()) {
                sdkReq.setProtocol(protocol);
            }
            
            // 调用SDK修改安全组规则
            bccClient.updateSecurityGroupRule(sdkReq);
            
            log.info("修改百度云安全组规则成功，安全组规则ID: {}", securityGroupRuleId);
            JSONObject result = new JSONObject();
            result.put("securityGroupRuleId", securityGroupRuleId);
            if (remark != null) result.put("remark", remark);
            if (portRange != null) result.put("portRange", portRange);
            if (sourceIp != null) result.put("sourceIp", sourceIp);
            if (sourceGroupId != null) result.put("sourceGroupId", sourceGroupId);
            if (destIp != null) result.put("destIp", destIp);
            if (destGroupId != null) result.put("destGroupId", destGroupId);
            if (protocol != null) result.put("protocol", protocol);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("修改百度云安全组规则失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改安全组规则失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除安全组规则
     *
     * 使用百度云SDK删除安全组规则
     * 支持参数：securityGroupId, direction, protocol, portRange, sourceIp, sourceGroupId, destIp, destGroupId, remark
     */
    public static BaseResponse deleteSecurityGroupRule(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云安全组规则（SDK方式）");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "securityGroupId", "direction", "protocol")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：securityGroupId, direction, protocol");
            }
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String securityGroupId = cloud.getString("securityGroupId");
            String direction = cloud.getString("direction");
            String protocol = cloud.getString("protocol");
            String portRange = cloud.getString("portRange");
            String sourceIp = cloud.getString("sourceIp");
            String sourceGroupId = cloud.getString("sourceGroupId");
            String destIp = cloud.getString("destIp");
            String destGroupId = cloud.getString("destGroupId");
            String remark = cloud.getString("remark");
            
            // 构造SDK请求对象
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            SecurityGroupRuleOperateRequest sdkReq = new SecurityGroupRuleOperateRequest();
            sdkReq.setSecurityGroupId(securityGroupId);
            
            // 构造安全组规则模型
            SecurityGroupRuleModel securityGroupRuleModel = new SecurityGroupRuleModel()
                    .withDirection(direction)
                    .withProtocol(protocol);
            
            // 设置可选参数
            if (portRange != null && !portRange.isEmpty()) {
                securityGroupRuleModel.withPortRange(portRange);
            }
            if (sourceIp != null && !sourceIp.isEmpty()) {
                securityGroupRuleModel.withSourceIp(sourceIp);
            }
            if (sourceGroupId != null && !sourceGroupId.isEmpty()) {
                securityGroupRuleModel.withSourceGroupId(sourceGroupId);
            }
            if (destIp != null && !destIp.isEmpty()) {
                securityGroupRuleModel.withDestIp(destIp);
            }
            if (destGroupId != null && !destGroupId.isEmpty()) {
                securityGroupRuleModel.withDestGroupId(destGroupId);
            }
            if (remark != null && !remark.isEmpty()) {
                securityGroupRuleModel.withRemark(remark);
            }
            
            // 设置安全组规则
            sdkReq.setRule(securityGroupRuleModel);
            
            // 调用SDK删除安全组规则
            bccClient.revokeSecurityGroupRule(sdkReq);
            
            log.info("删除百度云安全组规则成功，安全组ID: {}, 方向: {}, 协议: {}", securityGroupId, direction, protocol);
            JSONObject result = new JSONObject();
            result.put("securityGroupId", securityGroupId);
            result.put("direction", direction);
            result.put("protocol", protocol);
            if (portRange != null) result.put("portRange", portRange);
            if (sourceIp != null) result.put("sourceIp", sourceIp);
            if (sourceGroupId != null) result.put("sourceGroupId", sourceGroupId);
            if (destIp != null) result.put("destIp", destIp);
            if (destGroupId != null) result.put("destGroupId", destGroupId);
            if (remark != null) result.put("remark", remark);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("删除百度云安全组规则失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组规则失败: " + e.getMessage()), e);
        }
    }
} 