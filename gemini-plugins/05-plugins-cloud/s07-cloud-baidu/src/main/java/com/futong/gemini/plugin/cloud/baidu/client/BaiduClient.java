package com.futong.gemini.plugin.cloud.baidu.client;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidubce.BceClientConfiguration;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.auth.BceCredentials;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.region.DescribeRegionsRequest;
import com.baidubce.services.billing.BillingClient;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.blb.BlbClient;
import com.baidubce.services.blb.BlbClientConfiguration;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.eip.EipClient;
import com.baidubce.services.eni.EniClient;
import com.baidubce.services.eni.EniClientConfiguration;
import com.baidubce.services.iam.IamClient;
import com.baidubce.services.nat.NatClient;
import com.baidubce.services.nat.NatClientConfiguration;
import com.baidubce.services.route.RouteClient;
import com.baidubce.services.route.RouteClientConfiguration;
import com.baidubce.services.scs.ScsClient;
import com.baidubce.services.subnet.SubnetClient;
import com.baidubce.services.subnet.SubnetClientConfiguration;
import com.baidubce.services.vpc.VpcClient;
import com.baidubce.services.vpc.VpcClientConfiguration;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baidubce.services.bcc.model.flavor.ListFlavorSpecRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsRequest;
import com.baidubce.services.bcm.BcmClient;
import com.baidubce.services.bcm.BcmClientConfiguration;

// HTTP客户端相关导入
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Base64;

/**
 * 百度云客户端类
 * 继承BaseCloudClient，实现百度云API客户端，支持SDK和HTTP API兼容调用
 */
@Slf4j
public class BaiduClient extends BaseCloudClient {
    
    public static final BaiduClient client = new BaiduClient();
    
    // 默认超时时间常量
    private static final int DEFAULT_TIMEOUT = 30000;

    private String accessKeyId;
    private String secretAccessKey;
    private String region;
    private String endpoint;

    /**
     * 默认构造函数
     */
    public BaiduClient() {
        // 默认构造函数，不初始化客户端
    }

    /**
     * 带参数构造函数
     */
    public BaiduClient(String accessKeyId, String secretAccessKey, String region, String endpoint) {
        this.accessKeyId = accessKeyId;
        this.secretAccessKey = secretAccessKey;
        this.region = region;
        this.endpoint = endpoint;
    }

    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        try {
            log.info("client中body= {}",body);
            // 创建认证凭据
            BceCredentials credentials = new DefaultBceCredentials(
                body.getAccess().getUsername(),
                body.getAccess().getPassword()
            );
            this.endpoint = body.getAuth().getString("endpoint");
            String proxyAddr = body.getAuth().getString("proxyAddr");
            Map<String, Object> proxyInfo = parseProxyAddr(proxyAddr);
            String proxyHost = (String) proxyInfo.get("proxyHost");
            int proxyPort = proxyInfo.get("proxyPort") != null ? (int) proxyInfo.get("proxyPort") : -1;

            // 根据类型返回对应的客户端
            if (clazz == BccClient.class) {
                BceClientConfiguration config = new BceClientConfiguration();
                configureClient(config, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new BccClient(config);
            } else if (clazz == BcmClient.class) {
                BcmClientConfiguration bcmConfig = new BcmClientConfiguration();
                configureClient(bcmConfig, credentials, getEndpoint("bcm", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new BcmClient(bcmConfig);
            } else if (clazz == VpcClient.class) {
                VpcClientConfiguration vpcConfig = new VpcClientConfiguration();
                configureClient(vpcConfig, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new VpcClient(vpcConfig);
            } else if (clazz == EipClient.class) {
                BceClientConfiguration config = new BceClientConfiguration();
                configureClient(config, credentials, getEndpoint("eip", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new EipClient(config);
            } else if (clazz == BosClient.class) {
                BosClientConfiguration bosConfig = new BosClientConfiguration();
                configureClient(bosConfig, credentials, getBosEndpoint(), proxyHost, proxyPort);
                return (C) new BosClient(bosConfig);
            } else if (clazz == BlbClient.class) {
                BlbClientConfiguration blbConfig = new BlbClientConfiguration();
                configureClient(blbConfig, credentials, getEndpoint("blb", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new BlbClient(blbConfig);
            } else if (clazz == SubnetClient.class) {
                SubnetClientConfiguration subnetConfig = new SubnetClientConfiguration();
                configureClient(subnetConfig, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new SubnetClient(subnetConfig);
            } else if (clazz == EniClient.class) {
                EniClientConfiguration eniConfig = new EniClientConfiguration();
                configureClient(eniConfig, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new EniClient(eniConfig);
            } else if (clazz == BillingClient.class) {
                BceClientConfiguration config = new BceClientConfiguration();
                configureClient(config, credentials, getNoRegionEndpoint("billing"), proxyHost, proxyPort);
                return (C) new BillingClient(config);
            } else if (clazz == IamClient.class) {
                BceClientConfiguration config = new BceClientConfiguration();
                configureClient(config, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new IamClient(config);
            } else if (clazz == NatClient.class) {
                NatClientConfiguration config = new NatClientConfiguration();
                configureClient(config, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new NatClient(config);
            } else if (clazz == RouteClient.class) {
                RouteClientConfiguration config = new RouteClientConfiguration();
                configureClient(config, credentials, getEndpoint("bcc", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new RouteClient(config);
            } else if (clazz == ScsClient.class) {
                BceClientConfiguration config = new BceClientConfiguration();
                configureClient(config, credentials, getEndpoint("redis", body.getCloud().getString("regionId")), proxyHost, proxyPort);
                return (C) new ScsClient(config);
            } else {
                throw new BaseException(BaseResponse.ERROR_BIZ, "不支持的客户端类型: " + clazz.getName());
            }


        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 配置客户端通用参数
     */
    private void configureClient(BceClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置VPC客户端参数
     */
    private void configureClient(VpcClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置BOS客户端参数
     */
    private void configureClient(BosClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置BLB客户端参数
     */
    private void configureClient(BlbClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置子网客户端参数
     */
    private void configureClient(SubnetClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置ENI客户端参数
     */
    private void configureClient(EniClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 配置BCM客户端参数
     */
    private void configureClient(BcmClientConfiguration config, BceCredentials credentials, String endpoint, String proxyHost, int proxyPort) {
        config.setCredentials(credentials);
        config.setEndpoint(endpoint);
        config.setConnectionTimeoutInMillis(DEFAULT_TIMEOUT);
        config.setSocketTimeoutInMillis(DEFAULT_TIMEOUT);
        if (proxyHost != null && proxyPort > 0) {
            config.setProxyHost(proxyHost);
            config.setProxyPort(proxyPort);
        }
    }

    /**
     * 解析代理地址，支持 http://、https:// 前缀
     */
    private static Map<String, Object> parseProxyAddr(String proxyAddr) {
        Map<String, Object> result = new HashMap<>();
        if (proxyAddr != null && !proxyAddr.trim().isEmpty()) {
            String addr = proxyAddr.trim();
            if (addr.startsWith("http://")) {
                addr = addr.substring(7);
            } else if (addr.startsWith("https://")) {
                addr = addr.substring(8);
            }
            if (addr.contains(":")) {
                String[] parts = addr.split(":");
                result.put("proxyHost", parts[0]);
                try {
                    result.put("proxyPort", Integer.parseInt(parts[1]));
                } catch (NumberFormatException e) {
                    log.warn("代理端口格式错误: {}", proxyAddr);
                }
            }
        }
        return result;
    }

    /**
     * 获取指定服务的API端点
     */
    public String getEndpoint(String service, String region) {
        if (endpoint != null && !endpoint.trim().isEmpty()) {
            return endpoint;
        }
        if(CharSequenceUtil.isEmpty(region)){
            region="bj"; // 默认北京
        }
        return StrUtil.format("https://{}.{}.baidubce.com", service, region);
    }

    /**
     * 获取无地域的API端点
     */
    public String getNoRegionEndpoint(String service) {
        if (endpoint != null && !endpoint.trim().isEmpty()) {
            return endpoint;
        }
        if(CharSequenceUtil.isEmpty(region)){
            region="bj"; // 默认北京
        }
        return StrUtil.format("https://{}.baidubce.com", service);
    }

    /**
     * 获取无地域的API端点
     */
    public String getBosEndpoint() {
        if (endpoint != null && !endpoint.trim().isEmpty()) {
            return endpoint;
        }
        if(CharSequenceUtil.isEmpty(region)){
            region="bj"; // 默认北京
        }
        return StrUtil.format("https://{}.bcebos.com", region);
    }

    /**
     * 获取百度云所有地域列表
     */
    public static List<Map<String, String>> getAllRegions() {
        List<Map<String, String>> regions = new ArrayList<>();
        // 百度云支持的地域列表
        regions.add(createRegionMap("华北-北京", "bj"));
        regions.add(createRegionMap("华北-保定", "bd"));
        regions.add(createRegionMap("西南-成都", "cd"));
        regions.add(createRegionMap("华南-广州", "gz"));
        regions.add(createRegionMap("华东-苏州", "su"));
        regions.add(createRegionMap("华东-上海", "fsh"));
        regions.add(createRegionMap("华中-武汉", "fwh"));
        regions.add(createRegionMap("中国香港", "hkg"));
        return regions;
    }

    /**
     * 创建地域信息Map
     */
    private static Map<String, String> createRegionMap(String label, String value) {
        Map<String, String> region = new HashMap<>();
        region.put("label", label);
        region.put("value", value);
        return region;
    }

    /**
     * 验证连接
     */
    public boolean validateConnection(BaseCloudRequest request) {
        try {
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            return true;
        } catch (Exception e) {
            log.error("验证百度云连接失败", e);
            return false;
        }
    }

    @Override
    public String toString() {
        return "CloudClient{" +
                "accessKeyId='" + accessKeyId + '\'' +
                ", region='" + region + '\'' +
                ", endpoint='" + endpoint + '\'' +
                '}';
    }

} 