package com.futong.gemini.plugin.cloud.baidu.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.image.CreateImageRequest;
import com.baidubce.services.bcc.model.image.DeleteImageRequest;
import com.baidubce.services.bcc.model.image.ListImagesRequest;
import com.baidubce.services.bcc.model.image.RemoteCopyImageRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBodyCI;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 百度云镜像服务类
 * 负责镜像的管理操作
 * 
 * 主要功能：
 * - 镜像查询（已实现）
 * - 镜像创建（暂不支持，百度云SDK限制）
 * - 镜像删除（暂不支持，百度云SDK限制）
 * - 镜像修改（暂不支持，百度云SDK限制）
 * - 镜像复制（暂不支持，百度云SDK限制）
 * - 镜像导出（暂不支持，百度云SDK限制）
 * - 镜像权限管理（暂不支持，百度云SDK限制）
 * 
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 * 使用百度云官方SDK进行API调用
 * 
 * 注意：百度云SDK v0.10.362中镜像管理操作方法不存在，已添加注释说明
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class ImageService {

    /**
     * 查询镜像
     * 
     * 使用百度云官方SDK查询镜像列表
     * 支持参数：pageNumber, pageSize, marker, maxKeys
     */
    public static BaseResponse queryImages(BaseCloudRequest request) {
        try {
            log.info("开始查询百度云镜像列表");
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            Integer pageNumber = cloud.getInteger("pageNumber");
            Integer pageSize = cloud.getInteger("pageSize");
            
            // 创建请求对象
            ListImagesRequest imageRequest = new ListImagesRequest();
            
            // 设置分页参数
            if (pageNumber != null) {
                imageRequest.setMarker(String.valueOf((pageNumber - 1) * (pageSize != null ? pageSize : 100)));
            }
            if (pageSize != null) {
                imageRequest.setMaxKeys(pageSize);
            }
            
            // 调用SDK查询镜像
            Object response = bccClient.listImages(imageRequest);
            JSONObject result = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(response));
            
            // 确保返回数据格式正确
            if (!result.containsKey("images") && result.containsKey("imageList")) {
                result.put("images", result.getJSONArray("imageList"));
            }
            
            log.info("查询百度云镜像列表成功，返回镜像数量: {}", 
                result.getJSONArray("images") != null ? result.getJSONArray("images").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询百度云镜像列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询镜像列表失败: " + e.getMessage()), e);
        }
    }

    /**
     * 创建自定义镜像
     * 
     * 使用百度云官方SDK创建自定义镜像
     * 支持参数：imageName, instanceId, snapshotId, relateCds
     */
    public static BaseResponse createImage(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云自定义镜像");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageName");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String imageName = cloud.getString("imageName");
            String instanceId = cloud.getString("instanceId");
            String snapshotId = cloud.getString("snapshotId");
            Boolean relateCds = cloud.getBoolean("relateCds");
            
            // 创建请求对象
            CreateImageRequest createImageRequest = new CreateImageRequest();
            createImageRequest.setImageName(imageName);
            
            // 设置实例ID或快照ID（二选一）
            if (instanceId != null && !instanceId.isEmpty()) {
                createImageRequest.setInstanceId(instanceId);
                log.info("从实例创建镜像，实例ID: {}", instanceId);
            } else if (snapshotId != null && !snapshotId.isEmpty()) {
                createImageRequest.setSnapshotId(snapshotId);
                log.info("从快照创建镜像，快照ID: {}", snapshotId);
            } else {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId 或 snapshotId（二选一）");
            }
            
            // 设置是否关联实例CDS磁盘
            if (relateCds != null) {
                createImageRequest.setRelateCds(relateCds);
            }
            
            // 调用SDK创建镜像
            Object response = bccClient.createImage(createImageRequest);
            JSONObject result = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(response));
            
            log.info("创建百度云自定义镜像成功，镜像名称: {}, 镜像ID: {}", imageName, result.getString("imageId"));
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云自定义镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建自定义镜像失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改自定义镜像
     * 
     * 注意：百度云SDK v0.10.362中不支持modifyImage方法
     * 此功能暂未实现，后续如SDK支持可补充
     */
    public static BaseResponse updateImage(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云自定义镜像");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageId");
            }
            
            // TODO: 百度云SDK v0.10.362中不支持modifyImage方法
            // 此功能暂未实现，后续如SDK支持可补充
            log.warn("百度云SDK暂不支持修改镜像功能，此操作被跳过");
            
            JSONObject result = new JSONObject();
            result.put("message", "百度云SDK暂不支持修改镜像功能");
            result.put("status", "not_supported");
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("修改百度云自定义镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改自定义镜像失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量删除镜像
     * 
     * 使用百度云官方SDK删除镜像
     * 支持参数：imageId
     */
    public static BaseResponse deleteImages(BaseCloudRequest request) {
        try {
            log.info("开始批量删除百度云镜像");

            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }

            //验证必需参数
            List<String> devopsLevel =request.getBody().getCis().stream().map(ci -> ci.getJSONObject("sourceJson").getString("devopsLevel01Value")).collect(Collectors.toList());
            if (devopsLevel == null || devopsLevel.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少区域ID列表");
            }

            // 获取要删除的镜像ID列表
            List<String> imageIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (imageIds == null || imageIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少镜像ID列表");
            }
            
            // 批量删除镜像
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 设置区域
                request.getBody().getCloud().put("regionId",ci.getJSONObject("sourceJson").getString("devopsLevel01Value"));

                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());

                // 创建删除请求
                DeleteImageRequest deleteRequest = new DeleteImageRequest();
                deleteRequest.setImageId(ci.getOpenId());

                // 调用SDK删除镜像
                bccClient.deleteImage(deleteRequest);
                log.info("删除镜像成功，镜像ID: {}", ci.getOpenId());
            }
            
            JSONObject result = new JSONObject();
            result.put("deletedImageIds", imageIds.toArray(new String[0]));
            result.put("message", "批量删除镜像成功");
            result.put("status", "success");
            
            log.info("批量删除百度云镜像成功，共删除 {} 个镜像", imageIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量删除百度云镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("批量删除镜像失败: " + e.getMessage()), e);
        }
    }

    /**
     * 复制镜像
     * 
     * 使用百度云官方SDK复制镜像到其他地域
     * 支持参数：imageId, name, destRegion
     */
    public static BaseResponse copyImage(BaseCloudRequest request) {
        try {
            log.info("开始复制百度云镜像");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageId", "destRegion")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageId, destRegion");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            String imageId = cloud.getString("imageId");
            String name = cloud.getString("name");
            String destRegion = cloud.getString("destRegion");
            
            // 创建复制请求
            RemoteCopyImageRequest copyRequest = new RemoteCopyImageRequest();
            copyRequest.setImageId(imageId);
            copyRequest.setName(name != null ? name : "copied-" + imageId);
            copyRequest.setDestRegion(Arrays.asList(destRegion));
            
            // 调用SDK复制镜像
            bccClient.remoteCopyImage(copyRequest);
            
            log.info("复制百度云镜像成功，源镜像ID: {}, 目标地域: {}, 新镜像名称: {}", imageId, destRegion, copyRequest.getName());
            
            return new BaseDataResponse<>(copyRequest);
            
        } catch (Exception e) {
            log.error("复制百度云镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("复制镜像失败: " + e.getMessage()), e);
        }
    }

    /**
     * 导出镜像
     * 
     * 注意：百度云SDK v0.10.362中不支持exportImage方法
     * 此功能暂未实现，后续如SDK支持可补充
     */
    public static BaseResponse exportImage(BaseCloudRequest request) {
        try {
            log.info("开始导出百度云镜像");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageId", "ossBucket", "ossKey")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageId, ossBucket, ossKey");
            }
            
            // TODO: 百度云SDK v0.10.362中不支持exportImage方法
            // 此功能暂未实现，后续如SDK支持可补充
            log.warn("百度云SDK暂不支持导出镜像功能，此操作被跳过");
            
            JSONObject result = new JSONObject();
            result.put("message", "百度云SDK暂不支持导出镜像功能");
            result.put("status", "not_supported");
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("导出百度云镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("导出镜像失败: " + e.getMessage()), e);
        }
    }

    /**
     * 管理镜像共享权限
     * 
     * 注意：百度云SDK v0.10.362中不支持镜像权限管理方法
     * 此功能暂未实现，后续如SDK支持可补充
     */
    public static BaseResponse updateImagePermission(BaseCloudRequest request) {
        try {
            log.info("开始管理百度云镜像共享权限");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageId");
            }
            
            // TODO: 百度云SDK v0.10.362中不支持镜像权限管理方法
            // 此功能暂未实现，后续如SDK支持可补充
            log.warn("百度云SDK暂不支持镜像权限管理功能，此操作被跳过");
            
            JSONObject result = new JSONObject();
            result.put("message", "百度云SDK暂不支持镜像权限管理功能");
            result.put("status", "not_supported");
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("管理百度云镜像共享权限失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("管理镜像共享权限失败: " + e.getMessage()), e);
        }
    }
} 