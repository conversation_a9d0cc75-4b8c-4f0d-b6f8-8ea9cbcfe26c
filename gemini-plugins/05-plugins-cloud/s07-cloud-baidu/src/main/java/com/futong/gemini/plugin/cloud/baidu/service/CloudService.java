package com.futong.gemini.plugin.cloud.baidu.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.common.mq.FTRabbitUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

/**
 * 百度云基础服务类
 * 继承BaseCloudService，实现通用云服务方法
 */
@Slf4j
public class CloudService extends BaseCloudService {

    /**
     * 获取百度云客户端
     */
    public static BaiduClient getClient() {
        return BaiduClient.client;
    }

    /**
     * 默认地域设置
     */
    public static boolean defaultRegion(BaseCloudRequest request) {
        request.getBody().getCloud().put("regionId", "bj"); // 默认使用北京地域
        return true;
    }

    /**
     * 默认分页设置 - 50条
     * 百度云分页参数：marker（起始位置）和maxKeys（每页数量）
     */
    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 50);//默认50
        }
        // 设置百度云分页参数
        Integer pageSize = request.getBody().getCloud().getInteger("pageSize");
        if (pageSize != null) {
            // 设置maxKeys，最大不超过1000
            int maxKeys = Math.min(pageSize, 1000);
            request.getBody().getCloud().put("maxKeys", maxKeys);
        }
        // marker参数由调用方根据返回结果动态设置
        return true;
    }

    /**
     * 默认分页设置 - 100条
     * 百度云分页参数：marker（起始位置）和maxKeys（每页数量）
     */
    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNumber")) {
            request.getBody().getCloud().put("pageNumber", 1);//默认100
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 100);//默认100
        }
        // 设置百度云分页参数
        Integer pageSize = request.getBody().getCloud().getInteger("pageSize");
        if (pageSize != null) {
            // 设置maxKeys，最大不超过1000
            int maxKeys = Math.min(pageSize, 1000);
            request.getBody().getCloud().put("maxKeys", maxKeys);
        }
        // marker参数由调用方根据返回结果动态设置
        return true;
    }

    /**
     * 默认时间范围设置 - 一天
     */
    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", DateUtil.offsetDay(new Date(), -1).getTime());//一天前
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", new Date().getTime());//当前时间
        }
        return true;
    }

    /**
     * 验证必要参数
     */
    public static boolean validateRequiredParams(Map<String, Object> params, String... requiredParams) {
        for (String param : requiredParams) {
            if (!params.containsKey(param) || params.get(param) == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 业务处理前操作
     * 参考阿里云实现，处理批量创建云主机的业务逻辑
     */
    public static boolean toBeforeBiz(BaseCloudRequest request) {
        try {
            log.info("执行业务处理前操作");
            
            // 无业务信息直接返回
            if (CollUtil.isEmpty(request.getBody().getBiz())) {
                return true;
            }
            
            // 业务批量创建云主机的批次顺序号
            Integer resNum = request.getBody().getBiz().getInteger("resNum");
            if (resNum == null || resNum == 0) {
                return true; // 为0非批量创建云主机,不做处理
            }
            
            JSONObject cloud = request.getBody().getCloud();
            String instanceName = cloud.getString("instanceName");
            
            // 为批量创建的云主机添加序号后缀
            cloud.put("instanceName", instanceName + "-" + resNum);
            cloud.put("count", 1); // 默认数量
            
            log.info("批量创建云主机，批次号: {}, 实例名称: {}", resNum, cloud.getString("instanceName"));
            return true;
            
        } catch (Exception e) {
            log.error("业务处理前操作失败", e);
            return false;
        }
    }

    /**
     * 业务处理后操作
     * 参考阿里云实现，处理创建云主机后的资源ID和MQ消息发送
     */
    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        try {
            log.info("执行业务处理后操作");
            
            if (BaseResponse.SUCCESS.isNotExt(response)) {
                return;
            }
            
            if (response instanceof BaseDataResponse) {
                BaseDataResponse dataResponse = (BaseDataResponse) response;
                Object data = dataResponse.getData();
                
                // 检查响应数据是否为JSONObject格式（百度云创建实例的响应格式）
                if (data instanceof JSONObject) {
                    JSONObject result = (JSONObject) data;
                    String instanceId = result.getString("instanceId");
                    
                    if (instanceId != null && !instanceId.trim().isEmpty()) {
                        JSONObject biz = request.getBody().getBiz();
                        // 使用IdUtils加密资源ID
                        biz.put("resId", IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instanceId));
                        
                        // 发送MQ消息
                        FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                        
                        log.info("创建云主机成功，实例ID: {}, 已发送MQ消息", instanceId);
                    } else {
                        log.warn("响应中未找到有效的实例ID");
                    }
                } else {
                    log.warn("响应数据格式不符合预期: {}", data.getClass().getSimpleName());
                }
            }
            
        } catch (Exception e) {
            log.error("业务处理后操作失败", e);
        }
    }
} 