package com.futong.gemini.plugin.cloud.baidu.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.flavor.ListBccFlavorSpecResponse;
import com.baidubce.services.bcc.model.instance.*;
import com.baidubce.services.bcc.model.flavor.ListFlavorSpecRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBodyCI;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 百度云计算实例服务类
 * 负责云服务器的管理操作
 * 
 * 主要功能：
 * - 云主机创建、删除、修改
 * - 云主机启动、停止、重启
 * - 云主机规格修改
 * - VNC登录地址查询
 * - VNC密码修改
 * 
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 * 使用百度云官方SDK进行API调用
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class ComputeInstanceService {

    /**
     * 创建云主机实例
     * //
     */
    public static BaseResponse createInstance(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云云主机实例");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "imageId", "zoneName","billing","regionId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：imageId, zoneName, billing:{paymentTiming:''}, regionId");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 将JSONObject转换为CreateInstanceRequest
            JSONObject cloud = request.getBody().getCloud();
            if(!cloud.containsKey("name")){
                cloud.put("name","instance-" + System.currentTimeMillis());
            }
            CreateInstanceRequest createRequest = request.getBody().getCloud().toJavaObject(CreateInstanceRequest.class);
            // 调用SDK创建实例
            CreateInstanceResponse response = bccClient.createInstance(createRequest);
            // 解析响应结果
            JSONObject result = new JSONObject();
            // 检查响应结构 - 旧版本SDK的响应结构可能不同
            if (response.getInstanceIds() != null && !response.getInstanceIds().isEmpty()) {
                result.put("instanceId", response.getInstanceIds().get(0));
                result.put("instanceName", cloud.getString("name"));
                log.info("创建百度云云主机实例成功，实例ID: {}", response.getInstanceIds().get(0));
            }

            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云云主机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云主机失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量删除云主机
     */
    public static BaseResponse deleteInstance(BaseCloudRequest request) {
        try {
            log.info("开始批量释放百度云云主机");
            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCis().get(0), "openId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{openId:''}]");
            }

            List<String> instanceIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (instanceIds == null || instanceIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID列表");
            }

            // 批量释放实例
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 设置区域
                request.getBody().getCloud().put("regionId",ci.getJSONObject("sourceJson").getString("devopsLevel01Value"));
                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
                // 使用SDK释放实例
                bccClient.releaseInstance(ci.getString("openId"));
                log.info("释放云主机成功，实例ID: {}", ci.getString("openId"));
            }

            JSONObject result = new JSONObject();
            result.put("deletedInstanceIds", instanceIds);
            log.info("批量释放百度云云主机成功，共删除 {} 个实例", instanceIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量释放百度云云主机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("释放云主机失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改云主机属性
     */
    public static BaseResponse updateInstance(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云云主机属性");
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "instanceId","name","desc")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId");
            }
            String instanceId = request.getBody().getCloud().getString("instanceId");
            if (instanceId == null) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());

            // 可选参数
            if (request.getBody().getCloud().containsKey("name")) {
                // 执行修改BCC操作
                bccClient.modifyInstanceAttributes(
                        new ModifyInstanceAttributesRequest()
                                .withInstanceId(instanceId)
                                .withName(request.getBody().getCloud().getString("name"))
                );
            }
            // 可选参数
            if (request.getBody().getCloud().containsKey("desc")) {
                // 执行修改BCC操作
                bccClient.modifyInstanceDesc(
                        new ModifyInstanceDescRequest()
                                .withInstanceId(instanceId)
                                .withDesc(request.getBody().getCloud().getString("desc"))
                );
            }

            log.info("修改百度云云主机属性成功，实例ID: {}", instanceId);
            return BaseResponse.SUCCESS.of("修改云主机属性成功");
            
        } catch (Exception e) {
            log.error("修改百度云云主机属性失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改云主机属性失败: " + e.getMessage()), e);
        }
    }

    /**
     * 查询可修改云主机规格
     */
    public static BaseResponse queryUpdateComputeInstanceFlavor(BaseCloudRequest request) {
        try {
            log.info("开始查询百度云可修改云主机规格");
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId");
            }
            String instanceId = request.getBody().getCloud().getString("instanceId");
            if (instanceId == null) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取实例详情
            GetInstanceRequest getRequest = new GetInstanceRequest();
            getRequest.setInstanceId(instanceId);
            GetInstanceResponse instanceResponse = bccClient.getInstance(getRequest);
            
            // 获取可用规格列表
            ListFlavorSpecRequest flavorRequest = new ListFlavorSpecRequest();
            ListBccFlavorSpecResponse flavorResponse = bccClient.listFlavorSpec(flavorRequest);
            
            // 构建响应结果
            JSONObject result = new JSONObject();
            result.put("instanceId", instanceId);
            result.put("currentFlavorId", instanceResponse.getInstance().getCpuCount() + "." + instanceResponse.getInstance().getMemoryCapacityInGB());
            result.put("availableFlavors", flavorResponse.getZoneResources());
            
            log.info("查询百度云可修改云主机规格成功，实例ID: {}", instanceId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询百度云可修改云主机规格失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询可修改云主机规格失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改云主机规格
     */
    public static BaseResponse updateComputeInstanceFlavor(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云云主机规格");
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：instanceId");
            }
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getModel(), "cpuCount","memoryCapacityInGB") && !CloudService.validateRequiredParams(request.getBody().getModel(), "spec")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cpuCount, memoryCapacityInGB或spec");
            }
            String instanceId = request.getBody().getCloud().getString("instanceId");
            Integer cpuCount = request.getBody().getModel().getInteger("cpuCount");
            Integer memoryCapacityInGB = request.getBody().getModel().getInteger("memoryCapacityInGB");
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构建修改规格请求
            ResizeInstanceRequest resizeRequest = new ResizeInstanceRequest();
            resizeRequest.setInstanceId(instanceId);
            if(cpuCount != null && memoryCapacityInGB != null){
                resizeRequest.setCpuCount(cpuCount);
                resizeRequest.setMemoryCapacityInGB(memoryCapacityInGB);
            }
            if(request.getBody().getModel().containsKey("spec")){
                resizeRequest.setSpec(request.getBody().getModel().getString("spec"));
            }
            // 调用SDK修改实例规格
            bccClient.resizeInstance(resizeRequest);
            
            log.info("修改百度云云主机规格成功，实例ID: {}, CPU: {}，内存：{}", instanceId, cpuCount, memoryCapacityInGB);
            return BaseResponse.SUCCESS.of("修改云主机规格成功");
            
        } catch (Exception e) {
            log.error("修改百度云云主机规格失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改云主机规格失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量开启云主机
     */
    public static BaseResponse startInstance(BaseCloudRequest request) {
        try {
            log.info("开始批量开启百度云云主机");

            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCis().get(0), "openId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{openId:''}]");
            }

            List<String> instanceIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (instanceIds == null || instanceIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID列表");
            }
            // 批量开启实例
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 设置区域
                request.getBody().getCloud().put("regionId",ci.getJSONObject("sourceJson").getString("devopsLevel01Value"));
                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
                StartInstanceRequest startRequest = new StartInstanceRequest();
                startRequest.setInstanceId(ci.getString("openId"));

                bccClient.startInstance(startRequest);
                log.info("开启云主机成功，实例ID: {}", ci.getString("openId"));
            }
            
            JSONObject result = new JSONObject();
            result.put("startedInstanceIds", instanceIds);
            
            log.info("批量开启百度云云主机成功，共开启 {} 个实例", instanceIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量开启百度云云主机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("开启云主机失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量关闭云主机
     */
    public static BaseResponse stopInstance(BaseCloudRequest request) {
        try {
            log.info("开始批量关闭百度云云主机");

            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCis().get(0), "openId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{openId:''}]");
            }

            List<String> instanceIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (instanceIds == null || instanceIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID列表");
            }

            // 批量停止实例
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 设置区域
                request.getBody().getCloud().put("regionId",ci.getJSONObject("sourceJson").getString("devopsLevel01Value"));
                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
                StopInstanceRequest stopRequest = new StopInstanceRequest();
                stopRequest.setInstanceId(ci.getString("openId"));

                // 可选参数：强制停止
                if (request.getBody().getCloud().containsKey("force")) {
                    stopRequest.setForceStop(request.getBody().getCloud().getBoolean("force"));
                }

                bccClient.stopInstance(stopRequest);
                log.info("关闭云主机成功，实例ID: {}", ci.getString("openId"));
            }
            
            JSONObject result = new JSONObject();
            result.put("stoppedInstanceIds", instanceIds);
            
            log.info("批量关闭百度云云主机成功，共关闭 {} 个实例", instanceIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量关闭百度云云主机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("关闭云主机失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量重启云主机
     */
    public static BaseResponse rebootInstance(BaseCloudRequest request) {
        try {
            log.info("开始批量重启百度云云主机");

            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCis().get(0), "openId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{openId:''}]");
            }

            List<String> instanceIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (instanceIds == null || instanceIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID列表");
            }

            // 批量重启实例
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 设置区域
                request.getBody().getCloud().put("regionId",ci.getJSONObject("sourceJson").getString("devopsLevel01Value"));
                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
                RebootInstanceRequest rebootRequest = new RebootInstanceRequest();
                rebootRequest.setInstanceId(ci.getString("openId"));

                // 可选参数：强制重启
                if (request.getBody().getCloud().containsKey("force")) {
                    rebootRequest.setForceStop(request.getBody().getCloud().getBoolean("force"));
                }

                bccClient.rebootInstance(rebootRequest);
                log.info("重启云主机成功，实例ID: {}", ci.getString("openId"));
            }
            
            JSONObject result = new JSONObject();
            result.put("rebootedInstanceIds", instanceIds);
            
            log.info("批量重启百度云云主机成功，共重启 {} 个实例", instanceIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量重启百度云云主机失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("重启云主机失败: " + e.getMessage()), e);
        }
    }

    /**
     * 查询云主机VNC登录地址
     */
    public static BaseResponse queryInstanceVncUrl(BaseCloudRequest request) {
        try {
            log.info("开始查询百度云云主机VNC登录地址");
            
            String instanceId = request.getBody().getCloud().getString("instanceId");
            if (instanceId == null) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 调用SDK获取VNC URL
            GetInstanceVncResponse vncUrl = bccClient.getInstanceVnc(instanceId);
            
            // 构建响应结果
            JSONObject result = new JSONObject();
            result.put("instanceId", instanceId);
            result.put("vncUrl", vncUrl.getVncUrl());
            
            log.info("查询百度云云主机VNC登录地址成功，实例ID: {}", instanceId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询百度云云主机VNC登录地址失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询VNC登录地址失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改云主机VNC登录密码
     */
    public static BaseResponse updateInstanceVncPassword(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云云主机VNC登录密码");
            
            String instanceId = request.getBody().getCloud().getString("instanceId");
            String vncPassword = request.getBody().getCloud().getString("vncPassword");
            
            if (instanceId == null || vncPassword == null) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少实例ID或VNC密码");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 调用SDK修改VNC密码
            ModifyInstancePasswordRequest vncPasswordRequest = new ModifyInstancePasswordRequest();
            vncPasswordRequest.setInstanceId(instanceId);
            vncPasswordRequest.setAdminPass(vncPassword);
            bccClient.modifyInstancePassword(vncPasswordRequest);
            
            log.info("修改百度云云主机VNC登录密码成功，实例ID: {}", instanceId);
            return BaseResponse.SUCCESS.of("修改VNC登录密码成功");
            
        } catch (Exception e) {
            log.error("修改百度云云主机VNC登录密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改VNC登录密码失败: " + e.getMessage()), e);
        }
    }
} 