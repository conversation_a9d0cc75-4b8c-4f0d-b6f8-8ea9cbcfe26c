package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.keypair.KeypairAttachRequest;
import com.baidubce.services.bcc.model.keypair.KeypairCreateRequest;
import com.baidubce.services.bcc.model.keypair.KeypairDeleteRequest;
import com.baidubce.services.bcc.model.keypair.KeypairDetachRequest;
import com.baidubce.services.bcc.model.keypair.KeypairModel;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 百度云密钥对服务类
 * 
 * 提供密钥对管理功能，包括：
 * - 创建密钥对
 * - 删除密钥对
 * - 绑定/解绑密钥对
 * 
 * 注意：部分功能可能受百度云SDK限制，使用HTTP API作为备选方案
 */
@Slf4j
public class KeypairService {

    /**
     * 创建密钥对
     * 
     * 使用百度云SDK创建密钥对，支持设置名称和描述
     * 支持参数：name, description
     */
    public static BaseResponse createKeypair(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云密钥对");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "name")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构造SDK请求对象
            KeypairCreateRequest keypairCreateRequest = new KeypairCreateRequest();
            keypairCreateRequest.setName(cloud.getString("name"));
            
            // 设置描述（可选）
            if (cloud.containsKey("description")) {
                keypairCreateRequest.setDescription(cloud.getString("description"));
            }
            
            // 调用SDK创建密钥对
            KeypairModel keypairModel = bccClient.createKeypair(keypairCreateRequest);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("keypairId", keypairModel.getKeypairId());
            result.put("name", keypairModel.getName());
            result.put("description", keypairModel.getDescription());
            result.put("publicKey", keypairModel.getPublicKey());
            result.put("privateKey", keypairModel.getPrivateKey());
            result.put("createTime", System.currentTimeMillis());
            
            log.info("创建百度云密钥对成功，密钥对ID: {}", keypairModel.getKeypairId());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云密钥对失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建密钥对失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除密钥对
     * 
     * 使用百度云SDK删除密钥对
     * 支持参数：keypairId
     */
    public static BaseResponse deleteKeypair(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云密钥对");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "keypairId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：keypairId");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构造SDK请求对象
            KeypairDeleteRequest keypairDeleteRequest = new KeypairDeleteRequest();
            keypairDeleteRequest.setKeypairId(cloud.getString("keypairId"));
            
            // 调用SDK删除密钥对
            bccClient.deleteKeypair(keypairDeleteRequest);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("keypairId", cloud.getString("keypairId"));
            result.put("status", "deleted");
            
            log.info("删除百度云密钥对成功，密钥对ID: {}", cloud.getString("keypairId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("删除百度云密钥对失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除密钥对失败: " + e.getMessage()), e);
        }
    }

    /**
     * 绑定密钥对
     * 
     * 使用百度云SDK绑定密钥对到实例
     * 支持参数：keypairId, instanceId
     */
    public static BaseResponse bindKeypair(BaseCloudRequest request) {
        try {
            log.info("开始绑定百度云密钥对");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "keypairId", "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：keypairId, instanceId");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构造SDK请求对象
            KeypairAttachRequest keypairAttachRequest = new KeypairAttachRequest();
            keypairAttachRequest.setKeypairId(cloud.getString("keypairId"));
            
            // 设置实例ID列表
            List<String> instanceIds = new ArrayList<>();
            instanceIds.add(cloud.getString("instanceId"));
            keypairAttachRequest.setInstanceIds(instanceIds);
            
            // 调用SDK绑定密钥对
            bccClient.attachKeypair(keypairAttachRequest);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("keypairId", cloud.getString("keypairId"));
            result.put("instanceId", cloud.getString("instanceId"));
            result.put("status", "attached");
            
            log.info("绑定百度云密钥对成功，密钥对ID: {}, 实例ID: {}", 
                    cloud.getString("keypairId"), cloud.getString("instanceId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("绑定百度云密钥对失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定密钥对失败: " + e.getMessage()), e);
        }
    }

    /**
     * 解绑密钥对
     * 
     * 使用百度云SDK解绑密钥对从实例
     * 支持参数：keypairId, instanceId
     */
    public static BaseResponse unbindKeypair(BaseCloudRequest request) {
        try {
            log.info("开始解绑百度云密钥对");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "keypairId", "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：keypairId, instanceId");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构造SDK请求对象
            KeypairDetachRequest keypairDetachRequest = new KeypairDetachRequest();
            keypairDetachRequest.setKeypairId(cloud.getString("keypairId"));
            
            // 设置实例ID列表
            List<String> instanceIds = new ArrayList<>();
            instanceIds.add(cloud.getString("instanceId"));
            keypairDetachRequest.setInstanceIds(instanceIds);
            
            // 调用SDK解绑密钥对
            bccClient.detachKeypair(keypairDetachRequest);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("keypairId", cloud.getString("keypairId"));
            result.put("instanceId", cloud.getString("instanceId"));
            result.put("status", "detached");
            
            log.info("解绑百度云密钥对成功，密钥对ID: {}, 实例ID: {}", 
                    cloud.getString("keypairId"), cloud.getString("instanceId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("解绑百度云密钥对失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑密钥对失败: " + e.getMessage()), e);
        }
    }
}