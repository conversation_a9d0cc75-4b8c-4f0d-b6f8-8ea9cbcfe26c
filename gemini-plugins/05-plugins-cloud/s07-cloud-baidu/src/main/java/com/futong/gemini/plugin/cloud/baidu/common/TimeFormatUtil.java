package com.futong.gemini.plugin.cloud.baidu.common;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 线程安全的时间格式转换工具类
 * 用于将时间戳转换为ISO8601 UTC格式 (2019-01-01T00:00:00Z)
 */
public class TimeFormatUtil {
    
    /**
     * 线程安全的DateTimeFormatter实例
     * 使用AtomicReference确保线程安全
     */
    private static final AtomicReference<DateTimeFormatter> formatterRef = 
        new AtomicReference<>(DateTimeFormatter.ISO_INSTANT);
    
    /**
     * 专门用于百度云BCM API的DateTimeFormatter实例
     * 格式：yyyy-MM-dd'T'HH:mm:ss'Z' (如：2019-01-01T00:00:00Z)
     * 使用AtomicReference确保线程安全
     */
    private static final AtomicReference<DateTimeFormatter> baiduBCMFormatterRef = 
        new AtomicReference<>(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(java.time.ZoneOffset.UTC));
    
    /**
     * 将时间戳转换为ISO8601 UTC格式字符串
     * 线程安全方法
     * 
     * @param timestamp 时间戳（毫秒）
     * @return ISO8601 UTC格式字符串，如：2019-01-01T00:00:00Z
     */
    public static String toISO8601UTC(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        DateTimeFormatter formatter = formatterRef.get();
        return formatter.format(instant);
    }
    
    /**
     * 将时间戳转换为ISO8601 UTC格式字符串
     * 线程安全方法
     * 
     * @param timestamp 时间戳（毫秒）
     * @return ISO8601 UTC格式字符串，如：2019-01-01T00:00:00Z
     */
    public static String toISO8601UTC(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return toISO8601UTC(timestamp.longValue());
    }
    
    /**
     * 批量转换时间戳为ISO8601 UTC格式
     * 线程安全方法
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 包含开始和结束时间的数组，格式为ISO8601 UTC
     */
    public static String[] toISO8601UTC(long startTimestamp, long endTimestamp) {
        return new String[]{
            toISO8601UTC(startTimestamp),
            toISO8601UTC(endTimestamp)
        };
    }
    
    /**
     * 批量转换时间戳为ISO8601 UTC格式
     * 线程安全方法
     * 
     * @param startTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     * @return 包含开始和结束时间的数组，格式为ISO8601 UTC
     */
    public static String[] toISO8601UTC(Long startTimestamp, Long endTimestamp) {
        if (startTimestamp == null || endTimestamp == null) {
            return new String[]{null, null};
        }
        return toISO8601UTC(startTimestamp.longValue(), endTimestamp.longValue());
    }
    
    /**
     * 专门用于百度云BCM API的时间格式转换
     * 线程安全方法
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 百度云BCM API要求的UTC格式字符串 (如：2019-01-01T00:00:00Z)
     */
    public static String toBaiduBCMUTC(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        DateTimeFormatter formatter = baiduBCMFormatterRef.get();
        return formatter.format(instant);
    }
    
    /**
     * 专门用于百度云BCM API的时间格式转换
     * 线程安全方法
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 百度云BCM API要求的UTC格式字符串 (如：2019-01-01T00:00:00Z)
     */
    public static String toBaiduBCMUTC(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return toBaiduBCMUTC(timestamp.longValue());
    }
} 