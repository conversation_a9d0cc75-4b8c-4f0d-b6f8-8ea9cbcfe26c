package com.futong.gemini.plugin.cloud.baidu.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.constant.dict.*;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.model.otc.nxc.entity.CmdbFlavor;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import cn.hutool.core.date.DateUtil;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;

/**
 * 百度云数据转换工具类
 * 负责将百度云API响应数据转换为CI模型
 */
@Slf4j
public class Convert {
    
    /**
     * 百度云监控指标到PerfInfoBean的映射
     * 用于将监控指标名称映射到PerfInfoBean的setter方法
     */
    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMappingBean = new HashMap<>();
    
    static {
        // CPU相关指标
        perfMappingBean.put("CPUUtilization", PerfInfoBean::setCpuUsage);
        perfMappingBean.put("CPUUsagePercent", PerfInfoBean::setCpuUsage);
        
        // 内存相关指标
        perfMappingBean.put("MemoryUtilization", PerfInfoBean::setMemUsage);
        perfMappingBean.put("MemUsedPercent", PerfInfoBean::setMemUsage);
        
        // 磁盘相关指标 - 添加百度云官方标准指标
        perfMappingBean.put("DiskUsage", PerfInfoBean::setDiskUsage);
        perfMappingBean.put("DiskUsedPercent", PerfInfoBean::setDiskUsage);
        perfMappingBean.put("DiskReadRate", PerfInfoBean::setDiskRead);
        perfMappingBean.put("DiskWriteRate", PerfInfoBean::setDiskWrite);
        perfMappingBean.put("DiskReadOps", PerfInfoBean::setDiskRead);
        perfMappingBean.put("DiskWriteOps", PerfInfoBean::setDiskWrite);
        perfMappingBean.put("vDiskReadOpCountPerSecond", PerfInfoBean::setDiskRead);
        perfMappingBean.put("vDiskWriteOpCountPerSecond", PerfInfoBean::setDiskWrite);
        
        // 网络相关指标 - 添加百度云官方标准指标
        perfMappingBean.put("NetworkInRate", PerfInfoBean::setNetIn);
        perfMappingBean.put("NetworkOutRate", PerfInfoBean::setNetOut);
        perfMappingBean.put("NetworkIn", PerfInfoBean::setNetIn);
        perfMappingBean.put("NetworkOut", PerfInfoBean::setNetOut);
        perfMappingBean.put("vNicInBytes", PerfInfoBean::setNetIn);
        perfMappingBean.put("vNicOutBytes", PerfInfoBean::setNetOut);
    }

    static Map<String, Map<String, String>> diskTypes = new HashMap<>();

    static {
        // 百度云磁盘类型映射
        diskTypes.put("std", createDiskEntry("std", "普通云盘"));
        diskTypes.put("hp1", createDiskEntry("hp1", "高性能云盘"));
        diskTypes.put("ssd", createDiskEntry("ssd", "SSD云盘"));
        diskTypes.put("premium", createDiskEntry("premium", "高性能云盘"));
    }

    private static Map<String, String> createDiskEntry(String key, String value) {
        Map<String, String> diskEntry = new HashMap<>();
        diskEntry.put("key", key);
        diskEntry.put("value", value);
        return diskEntry;
    }

    private static List<Map<String, String>> filterDiskTypes(List<String> keys) {
        List<Map<String, String>> filteredList = new ArrayList<>();
        for (String key : keys) {
            if (diskTypes.containsKey(key)) {
                filteredList.add(diskTypes.get(key));
            }
        }
        return filteredList;
    }

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    /**
     * 转换百度云实例数据为CI模型（参考阿里云convertEcs实现）
     */
    public static Map<Class, List> convertInstance(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("instances") || CollUtil.isEmpty(response.getJSONArray("instances"))) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_INSTANCE_RES.value()
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        JSONArray instances = response.getJSONArray("instances");
        for (int i = 0; i < instances.size(); i++) {
            JSONObject instance = instances.getJSONObject(i);
            CmdbInstanceRes ci = new CmdbInstanceRes();
            // 云类型、账号ID
            ci.setCloud_type(request.getPlugin().getRealm());
            ci.setAccount_id(request.getBody().getAccess().getCmpId());
            // 资源ID
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("id")));
            // open_id/open_name
            ci.setOpen_id(instance.getString("id"));
            ci.setOpen_name(instance.getString("name"));
            // CPU核数
            ci.setCpu_size(instance.getInteger("cpuCount"));
            // 内存GB转MB
            Integer memGb = instance.getInteger("memoryCapacityInGB");
            ci.setMem_size(memGb != null ? memGb * 1024 : null);
            // 状态标准化
            String status = instance.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertInstanceStatus(status));
            // 描述
            ci.setDesc(instance.getString("desc"));
            // 是否模板 - 百度云无此字段，设为null
            ci.setIs_template(null);
            // 创建时间
            Long createTime = instance.getLong("createTime");
            ci.setCreate_time(createTime);
            ci.setOpen_create_time(createTime);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储实例类型
            ci.setExtend1(instance.getString("instanceType"));
            // 扩展字段2 - 存储可用区
            ci.setExtend2(instance.getString("zoneName"));
            // 扩展字段3 - 存储计费方式
            ci.setExtend3(instance.getString("paymentTiming"));
            // 资源类型 - 百度云无此字段，设为null
            ci.setSet_type(null);

            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, instance.getString("zoneName"));

            //关联规格
            if (StrUtil.isNotEmpty(instance.getString("spec"))) {
                Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("spec"), request.getBody().getCloud().getString("zoneName")));
                associations.add(flavor);
            }
            // 关联镜像
            if (StrUtil.isNotEmpty(instance.getString("imageId"))) {
                Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("imageId"), request.getBody().getCloud().getString("regionId")));
                associations.add(image);
                //关联OS
                Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), instance.getString("imageId"), request.getBody().getCloud().getString("regionId")));
                associations.add(os);
            }
            // 关联VPC
            if (StrUtil.isNotEmpty(instance.getString("vpcId"))) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
                associations.add(vpc);
            }
            // 关联子网
            if (StrUtil.isNotEmpty(instance.getString("subnetId"))) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("subnetId"),request.getBody().getCloud().getString("regionId")));
                associations.add(subnet);
            }
            // 关联密钥对
            if (StrUtil.isNotEmpty(instance.getString("keypairId"))) {
                Association keyPair = AssociationUtils.toAssociation(ci, CmdbKeypairRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instance.getString("keypairId"),request.getBody().getCloud().getString("regionId")));
                associations.add(keyPair);
            }
            // 关联安全组
            JSONArray sgIds = instance.getJSONObject("nicInfo").getJSONArray("securityGroups");
            if (sgIds != null) {
                for (int j = 0; j < sgIds.size(); j++) {
                    String sgId = sgIds.getString(j);
                    if (StrUtil.isNotEmpty(sgId)) {
                        Association sg = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), sgId,request.getBody().getCloud().getString("regionId")));
                        associations.add(sg);
                    }
                }
            }
            // 采集IP
            JSONArray nicInfoIps = instance.getJSONObject("nicInfo").getJSONArray("ips");
            if (nicInfoIps != null) {
                for (int j = 0; j < nicInfoIps.size(); j++) {
                    // 采集私网IP
                    String ipAddress = nicInfoIps.getJSONObject(j).getString("privateIp");
                    if (StrUtil.isNotEmpty(ipAddress)) {
                        CmdbIpRes ip = new CmdbIpRes();
                        ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipAddress));
                        ip.setType(IpType.PRIVATE_IP.value());
                        ip.setAddress(ipAddress);
                        ip.setOpen_id(ipAddress);
                        ip.setOpen_name(ipAddress);
                        toCiResCloud(request, ip);
                        ips.add(ip);
                        //关联云主机
                        associations.add(AssociationUtils.toAssociation(ip, ci));
                    }
                    // 采集公网IP
                    String eipAddress = nicInfoIps.getJSONObject(j).getString("eip");
                    if (StrUtil.isNotEmpty(eipAddress)) {
                        CmdbIpRes ip = new CmdbIpRes();
                        ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eipAddress));
                        ip.setType(IpType.PUBLIC_IP.value());
                        ip.setAddress(eipAddress);
                        ip.setOpen_id(eipAddress);
                        ip.setOpen_name(eipAddress);
                        toCiResCloud(request, ip);
                        ips.add(ip);
                        //关联云主机
                        associations.add(AssociationUtils.toAssociation(ip, ci));
                    }
                }
            }
            data.add(ci);
        }

        result.put(CmdbInstanceRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        result.put(CmdbIpRes.class, ips);
        return result;
    }

    /**
     * 转换百度云磁盘数据为CI模型
     */
    public static Map<Class, List> convertDisk(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("volumes") || CollUtil.isEmpty(response.getJSONArray("volumes"))) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray disks = response.getJSONArray("volumes");
        for (int i = 0; i < disks.size(); i++) {
            JSONObject disk = disks.getJSONObject(i);
            
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setOpen_id(disk.getString("id"));
            ci.setOpen_name(disk.getString("name"));
            ci.setStatus(convertStatus(disk.getString("status")));
            ci.setOpen_status(disk.getString("status"));
            // 是否是共享磁盘 - 百度云无此字段，设为null
            ci.setShare_disk(null);
            ci.setType(disk.getString("storageType"));
            ci.setCategory(disk.getString("type").equals("System") ? "system" : "data");
            ci.setSize(disk.getInteger("diskSizeInGB").floatValue());
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), disk.getString("id"), request.getBody().getCloud().getString("regionId")));
            // 磁盘路径 - 百度云无此字段，设为null
            ci.setPath(null);
            // lun号 - 百度云无此字段，设为null
            ci.setLun_id(null);
            // 所属数据存储 - 百度云无此字段，设为null
            ci.setStore_name(null);
            // 描述
            ci.setDesc(disk.getString("description"));
            // 创建时间
            Long createTime = disk.getLong("createTime");
            ci.setCreate_time(createTime);
            ci.setOpen_create_time(createTime);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 磁盘已使用大小(GB) - 百度云无此字段，设为null
            ci.setUsed_size(null);
            // 配置模式 - 百度云无此字段，设为null
            ci.setConfig_mode(null);
            // 扩展字段1 - 存储快照ID
            ci.setExtend1(disk.getString("snapshotId"));
            // 扩展字段2 - 存储实例ID
            ci.setExtend2(disk.getString("instanceId"));
            // 扩展字段3 - 存储挂载时间
            ci.setExtend3(disk.getString("attachedTime"));
            
            toCiResCloud(request, ci);
            data.add(ci);
            
            // 添加关联关系
            if(CollUtil.isNotEmpty(disk.getJSONArray("attachments"))){
                for (int j = 0; j < disk.getJSONArray("attachments").size(); j++) {
                    Association instanceAss = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class,
                            IdUtils.encryptId(request.getBody().getAccess().getCmpId(), disk.getJSONArray("attachments").getJSONObject(j).getString("instanceId")));
                    associations.add(instanceAss);
                }
            }
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_DISK_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 百度云磁盘状态转换为标准状态
     * 根据百度云磁盘状态映射关系进行转换
     * @param status 百度云磁盘状态
     * @return 标准状态
     */
    private static String convertStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 卷已创建完成，状态正常，且未attach到任何虚拟机实例
            case "available":
                return DiskStatus.AVAILABLE.value();
            
            // 使用中状态 - 卷attach到虚拟机实例，正在使用中
            case "inuse":
            case "in-use":
                return DiskStatus.IN_USE.value();
            
            // 创建中状态 - 卷创建是异步操作，Creating表示系统已收到创建请求，并分配相应卷ID，但尚未创建完成
            case "creating":
                return DiskStatus.CREATING.value();
            
            // 挂载中状态 - 卷attach是异步操作，该状态表示正在进行attach操作
            case "attaching":
                return DiskStatus.ATTACHING.value();
            
            // 卸载中状态 - 卷detach是异步操作，该状态表示正在进行detach操作
            case "detaching":
                return DiskStatus.DETACHING.value();
            
            default:
                // 如果在DiskStatus中没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云云主机状态为标准状态
     * 如果在InstanceStatus中没有找到匹配的状态返回原始状态
     */
    private static String convertInstanceStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 启动中状态 - 云主机正在启动
            case "starting":
                return InstanceStatus.STARTING.value();
            
            // 运行中状态 - 云主机正常运行
            case "running":
                return InstanceStatus.RUNNING.value();
            
            // 停止中状态 - 云主机正在停止
            case "stopping":
                return InstanceStatus.STOPPING.value();
            
            // 已停止状态 - 云主机已停止
            case "stopped":
                return InstanceStatus.STOPPED.value();
            
            // 错误状态
            case "error":
                return InstanceStatus.ERROR.value();
            
            // 迁移中状态 - 实例操作变更可用区/跨AZ迁移时的状态
            case "moving":
                return InstanceStatus.MIGRATING.value();
            
            default:
                // 如果在InstanceStatus中没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云镜像状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertImageStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 创建中状态 - 镜像正在创建
            case "creating":
                return "creating";
            
            // 创建失败状态 - 镜像创建失败
            case "createdfailed":
                return "created-failed";
            
            // 可用状态 - 镜像可用
            case "available":
                return "available";
            
            // 不可用状态 - 镜像不可用
            case "notavailable":
                return "not-available";
            
            // 错误状态 - 镜像错误
            case "error":
                return "error";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云VPC状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertVpcStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - VPC可用
            case "available":
                return "available";
            
            // 创建中状态 - VPC正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - VPC正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - VPC错误
            case "error":
                return "error";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云子网状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertSubnetStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 子网可用
            case "available":
                return "available";
            
            // 创建中状态 - 子网正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - 子网正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - 子网错误
            case "error":
                return "error";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云弹性IP状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertEipStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 弹性IP可用
            case "available":
                return "available";
            
            // 使用中状态 - 弹性IP正在使用
            case "inuse":
            case "in-use":
                return "in-use";
            
            // 创建中状态 - 弹性IP正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - 弹性IP正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - 弹性IP错误
            case "error":
                return "error";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云快照状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertSnapshotStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 快照可用
            case "available":
                return "available";
            
            // 创建中状态 - 快照正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - 快照正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - 快照错误
            case "error":
                return "error";
            
            // 已完成状态 - 快照已完成
            case "completed":
                return "completed";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云网卡状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertNetworkInterfaceStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 网卡可用
            case "available":
                return "available";
            
            // 使用中状态 - 网卡正在使用
            case "inuse":
            case "in-use":
                return "in-use";
            
            // 创建中状态 - 网卡正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - 网卡正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - 网卡错误
            case "error":
                return "error";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云负载均衡器状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertLoadBalancerStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - 负载均衡器可用
            case "available":
                return "available";
            
            // 创建中状态 - 负载均衡器正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - 负载均衡器正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - 负载均衡器错误
            case "error":
                return "error";
            
            // 运行中状态 - 负载均衡器运行中
            case "running":
                return "running";
            
            // 停止状态 - 负载均衡器已停止
            case "stopped":
                return "stopped";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云NAT网关状态为标准状态
     * 如果没有找到匹配的状态返回原始状态
     */
    private static String convertNatStatus(String status) {
        if (StrUtil.isEmpty(status)) {
            return "unknown";
        }
        switch (status.toLowerCase()) {
            // 可用状态 - NAT网关可用
            case "available":
                return "available";
            
            // 创建中状态 - NAT网关正在创建
            case "creating":
                return "creating";
            
            // 删除中状态 - NAT网关正在删除
            case "deleting":
                return "deleting";
            
            // 错误状态 - NAT网关错误
            case "error":
                return "error";
            
            // 运行中状态 - NAT网关运行中
            case "running":
                return "running";
            
            // 停止状态 - NAT网关已停止
            case "stopped":
                return "stopped";
            
            default:
                // 如果没有找到匹配的状态返回原始状态
                return status;
        }
    }

    /**
     * 转换百度云镜像数据为CI模型
     */
    public static Map<Class, List> convertImage(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("images") || CollUtil.isEmpty(response.getJSONArray("images"))) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }

        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        
        JSONArray images = response.getJSONArray("images");
        for (int i = 0; i < images.size(); i++) {
            JSONObject image = images.getJSONObject(i);
            
            CmdbImageRes ci = new CmdbImageRes();
            CmdbOsRes os = new CmdbOsRes();
            
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), image.getString("id"), request.getBody().getCloud().getString("regionId")));
            ci.setType(image.getString("osType"));
            // 百度云镜像API中没有diskSize和minDiskGb字段，设为null
            ci.setSize(null);
            ci.setMin_disk(null);
            String status = image.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertImageStatus(status));
            ci.setDesc(image.getString("desc"));
            ci.setOpen_id(image.getString("id"));
            ci.setOpen_name(image.getString("name"));
            ci.setVisibility("Custom".equals(image.getString("type")) ? "private" : "public");
            ci.setImage_source(image.getString("type"));
            // 启动方式 - 百度云无此字段，设为null
            ci.setHw_firmware_type(null);
            // 镜像校验 - 百度云无此字段，设为null
            ci.setMd5(null);
            // 创建时间
            Long createTime = image.getLong("createTime");
            ci.setCreate_time(createTime);
            ci.setOpen_create_time(createTime);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 磁盘格式 - 百度云无此字段，设为null
            ci.setDisk_format(null);
            // 扩展字段1 - 存储镜像架构
            ci.setExtend1(image.getString("osArch"));
            // 扩展字段2 - 存储镜像版本
            ci.setExtend2(image.getString("osVersion"));
            // 扩展字段3 - 存储镜像类型
            ci.setExtend3(image.getString("type"));
            
            toCiResCloud(request, ci);
            dataImage.add(ci);
            
            // 操作系统信息
            os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), image.getString("id"), request.getBody().getCloud().getString("regionId")));
            os.setOpen_id(image.getString("osBuild"));
            os.setOpen_name(image.getString("name"));
            os.setFull_name(image.getString("osType")+" "+image.getString("name"));
            os.setCpu_arch(image.getString("osArch"));
            os.setName(image.getString("osName"));
            os.setVersion(image.getString("osVersion"));
            os.setType(image.getString("osType"));
            toCiResCloud(request, os);
            dataOs.add(os);
            
            // 添加关联关系
            Association ass = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
            dataAss.add(ass);
        }

        List<TmdbResourceSet> dataSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(), request.getPlugin().getRealm())
                .withDataByDevopsValue(ResourceType.CMDB_IMAGE_RES, dataImage, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .withDataByDevopsValue(ResourceType.CMDB_OS_RES, dataOs, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();

        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, dataSet);
        return result;
    }

    /**
     * 转换百度云VPC数据为CI模型
     */
    public static Map<Class, List> convertVpc(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("vpcs") || CollUtil.isEmpty(response.getJSONArray("vpcs"))) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbVpcRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray vpcs = response.getJSONArray("vpcs");
        for (int i = 0; i < vpcs.size(); i++) {
            JSONObject vpc = vpcs.getJSONObject(i);
            
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vpc.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(vpc.getString("vpcId"));
            ci.setOpen_name(vpc.getString("name"));
            String status = vpc.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertVpcStatus(status));
            // 描述 - 百度云无此字段，设为null
            ci.setDesc(null);
            ci.setCidr(vpc.getString("cidr"));
            // 创建时间 - 百度云无此字段，设为null
            ci.setCreate_time(null);
            ci.setOpen_create_time(null);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储是否默认VPC
            ci.setExtend1(vpc.getString("isDefault"));
            // 扩展字段2 - 存储DNS服务器
            ci.setExtend2(vpc.getJSONArray("dnsServerSet") != null ? vpc.getJSONArray("dnsServerSet").toJSONString() : null);
            // 扩展字段3 - 存储VPC标签
            ci.setExtend3(vpc.getJSONArray("tags") != null ? vpc.getJSONArray("tags").toJSONString() : null);
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_VPC_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云子网数据为CI模型
     */
    public static Map<Class, List> convertSubnet(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("subnets") || CollUtil.isEmpty(response.getJSONArray("subnets"))) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray subnets = response.getJSONArray("subnets");
        for (int i = 0; i < subnets.size(); i++) {
            JSONObject subnet = subnets.getJSONObject(i);
            
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnet.getString("subnetId"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(subnet.getString("subnetId"));
            ci.setOpen_name(subnet.getString("name"));
            String status = subnet.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertSubnetStatus(status));
            ci.setDesc(subnet.getString("description"));
            ci.setCidr_ipv4(subnet.getString("cidr"));
            ci.setCidr_ipv6(subnet.getString("ipv6Cidr"));
            // 创建时间
            Long createTime = subnet.getLong("createdTime");
            ci.setOpen_create_time(createTime);
            ci.setCreate_time(createTime);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            // DHCP启用状态 - 百度云无此字段，设为null
            ci.setDhcp_enable(null);
            // IPv4网关 - 百度云无此字段，设为null
            ci.setGateway_ipv4(null);
            // IPv6网关 - 百度云无此字段，设为null
            ci.setGateway_ipv6(null);
            // ipv6启用状态 - 百度云无此字段，设为null
            ci.setIpv6_enable(null);
            // IPv4可用IP数量 - 百度云无此字段，设为null
            ci.setAvailable_ip_count_ipv4(null);
            // IPv6可用IP数量 - 百度云无此字段，设为null
            ci.setAvailable_ip_count_ipv6(null);
            // 端口 - 百度云无此字段，设为null
            ci.setPort(null);
            // 类型 - 百度云无此字段，设为null
            ci.setType(null);
            // vlan标识符 - 百度云无此字段，设为null
            ci.setVlan_id(null);
            // 连接方式 - 百度云无此字段，设为null
            ci.setConnect_type(null);
            // 扩展字段1 - 存储VPC ID
            ci.setExtend1(subnet.getString("vpcId"));
            // 扩展字段2 - 存储子网类型
            ci.setExtend2(subnet.getString("subnetType"));
            // 扩展字段3 - 存储可用区
            ci.setExtend3(subnet.getString("zoneName"));
            
            toCiResCloud(request, ci);
            data.add(ci);
            
            // 添加关联关系
            if (StrUtil.isNotEmpty(subnet.getString("vpcId"))) {
                Association vpcAss = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnet.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
                associations.add(vpcAss);
            }
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SUBNET_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云弹性IP数据为CI模型
     */
    public static Map<Class, List> convertEip(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("eips") || CollUtil.isEmpty(response.getJSONArray("eips"))) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbEipRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray eips = response.getJSONArray("eips");
        for (int i = 0; i < eips.size(); i++) {
            JSONObject eip = eips.getJSONObject(i);
            
            CmdbEipRes ci = new CmdbEipRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getString("eipId"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(eip.getString("eipId"));
            ci.setOpen_name(eip.getString("name"));
            String status = eip.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertEipStatus(status));
            ci.setOpen_create_time(eip.getLong("createTime"));
            ci.setElastic_ip(eip.getString("eip"));
            ci.setBandwidth_speed(eip.getString("bandwidthInMbps"));
            // 固定IP - 百度云无此字段，设为null
            ci.setFixed_ip(null);
            // 浮动IP - 百度云无此字段，设为null
            ci.setFloating_ip(null);
            // 带宽类型 - 百度云无此字段，设为null
            ci.setBandwidth_type(null);
            // 网段 - 百度云无此字段，设为null
            ci.setCidr(null);
            // 创建时间
            ci.setCreate_time(eip.getLong("createTime"));
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 描述 - 百度云无此字段，设为null
            ci.setDesc(null);
            // 扩展字段1 - 存储实例ID
            ci.setExtend1(eip.getString("instanceId"));
            // 扩展字段2 - 存储实例类型
            ci.setExtend2(eip.getString("instanceType"));
            // 扩展字段3 - 存储计费方式
            ci.setExtend3(eip.getString("paymentTiming"));

            toCiResCloud(request, ci);
            data.add(ci);
            
            // 添加关联关系
            if (StrUtil.isNotEmpty(eip.getString("instanceId"))) {
                Association instanceAss = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getString("instanceId")));
                associations.add(instanceAss);
            }
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_EIP_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云安全组数据为CI模型
     */
    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("securityGroups") || CollUtil.isEmpty(response.getJSONArray("securityGroups"))) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray securityGroups = response.getJSONArray("securityGroups");
        for (int i = 0; i < securityGroups.size(); i++) {
            JSONObject securityGroup = securityGroups.getJSONObject(i);
            
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroup.getString("id"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(securityGroup.getString("id"));
            ci.setOpen_name(securityGroup.getString("name"));
            ci.setDesc(securityGroup.getString("desc"));
            ci.setCreate_time(securityGroup.getLong("createdTime"));
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_create_time(securityGroup.getLong("createdTime"));
            ci.setOpen_update_time(null);
            // 标题 - 百度云无此字段，设为null
            ci.setTitle(null);
            // 防火墙类型 - 百度云无此字段，设为null
            ci.setRule_action(null);
            // 扩展字段1 - 存储VPC ID
            ci.setExtend1(securityGroup.getString("vpcId"));
            // 扩展字段2 - 存储安全组规则
            ci.setExtend2(securityGroup.getJSONArray("rules") != null ? securityGroup.getJSONArray("rules").toJSONString() : null);
            // 扩展字段3 - 存储安全组标签
            ci.setExtend3(securityGroup.getJSONArray("tags") != null ? securityGroup.getJSONArray("tags").toJSONString() : null);

            toCiResCloud(request, ci);
            data.add(ci);
            
            // 添加关联关系
            if (StrUtil.isNotEmpty(securityGroup.getString("vpcId"))) {
                Association vpcAss = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroup.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
                associations.add(vpcAss);
            }
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_SECURITYGROUP_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云密钥对数据为CI模型
     */
    public static Map<Class, List> convertKeypair(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("keypairs") || CollUtil.isEmpty(response.getJSONArray("keypairs"))) {
            result.put(CmdbKeypairRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbKeypairRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray keypairs = response.getJSONArray("keypairs");
        for (int i = 0; i < keypairs.size(); i++) {
            JSONObject keypair = keypairs.getJSONObject(i);
            
            CmdbKeypairRes ci = new CmdbKeypairRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), keypair.getString("keypairId")));
            ci.setOpen_id(keypair.getString("keypairId"));
            ci.setOpen_name(keypair.getString("name"));
            ci.setFingerprint(keypair.getString("fingerPrint"));
            ci.setPublic_key(keypair.getString("publicKey"));
            ci.setPrivate_key(keypair.getString("privateKey"));
            // 创建时间
            Long createTime = keypair.getLong("createdTime");
            ci.setCreate_time(createTime);
            ci.setOpen_create_time(createTime);
            // 更新时间
            Long updateTime = keypair.getLong("updateTime");
            ci.setUpdate_time(updateTime);
            ci.setOpen_update_time(updateTime);
            // 描述 - 百度云无此字段，设为null
            ci.setDesc(null);
            // 扩展字段1 - 存储密钥对类型
            ci.setExtend1(keypair.getString("type"));
            // 扩展字段2 - 存储密钥对状态
            ci.setExtend2(keypair.getString("status"));
            // 扩展字段3 - 存储密钥对标签
            ci.setExtend3(keypair.getJSONArray("tags") != null ? keypair.getJSONArray("tags").toJSONString() : null);
            
            // 设置云类型和账号信息
            toCiResCloud(request, ci);
            
            data.add(ci);
        }
        
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_KEYPAIR_RES.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbKeypairRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云快照数据为CI模型
     */
    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray snapshots = null;
        if (response.containsKey("snapshots")) {
            snapshots = response.getJSONArray("snapshots");
        } else if (response.containsKey("snapshotList")) {
            snapshots = response.getJSONArray("snapshotList");
        }
        if (snapshots == null || CollUtil.isEmpty(snapshots)) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        String cmpId = request.getBody().getAccess().getCmpId();
        String regionId = request.getBody().getCloud().getString("regionId");
        for (int i = 0; i < snapshots.size(); i++) {
            JSONObject snap = snapshots.getJSONObject(i);
            CmdbSnapshotRes ci = new CmdbSnapshotRes();
            // 字段映射
            ci.setRes_id(IdUtils.encryptId(cmpId, snap.getString("id"), regionId));
            ci.setOpen_id(snap.getString("id"));
            ci.setOpen_name(snap.getString("name"));
            ci.setDesc(snap.getString("description"));
            String status = snap.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertSnapshotStatus(status));
            ci.setSize(snap.getFloat("sizeInGB"));
            ci.setType(null); // 百度云快照无类型字段
            ci.setP_snaps_id(null); // 父快照ID，百度云无
            ci.setP_snaps_name(null); // 父快照名，百度云无
            ci.setRunning_status(null); // 运行状态，百度云无
            ci.setTarget(snap.getString("diskId")); // 关联云硬盘ID
            ci.setOpen_create_time(snap.getLong("createTime"));
            ci.setCreate_time(snap.getLong("createTime"));
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储快照类型
            ci.setExtend1(snap.getString("type"));
            // 扩展字段2 - 存储快照状态详情
            ci.setExtend2(snap.getString("status"));
            // 扩展字段3 - 存储快照标签
            ci.setExtend3(snap.getJSONArray("tags") != null ? snap.getJSONArray("tags").toJSONString() : null);
            toCiResCloud(request, ci);
            data.add(ci);
            // 关联云硬盘
            if (StrUtil.isNotEmpty(snap.getString("volumeId"))) {
                Association diskAss = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(cmpId, snap.getString("volumeId"), regionId));
                associations.add(diskAss);
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(cmpId, request.getPlugin().getRealm(), ResourceType.CMDB_SNAPSHOT_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, regionId)
                .getData();
        result.put(CmdbSnapshotRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云规格数据为CI模型
     */
    public static Map<Class, List> convertFlavor(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        List<CmdbFlavor> data = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        String zoneName = request.getBody().getCloud().getString("zoneName");
        String cmpId = request.getBody().getAccess().getCmpId();
        String realm = request.getPlugin().getRealm();
        // 递归遍历 zoneResources.bccResources.flavorGroups.flavors
        JSONArray zoneResources = response.getJSONArray("zoneResources");
        if (zoneResources != null) {
            BuilderResourceSet builder = BuilderResourceSet.of()
                    .withInfoString(request.getBody().getAccess().getCmpId(),
                            request.getPlugin().getRealm(),
                            ResourceType.CMDB_FLAVOR.value()
                    );
            for (int i = 0; i < zoneResources.size(); i++) {
                JSONObject zone = zoneResources.getJSONObject(i);
                JSONObject bccResources = zone.getJSONObject("bccResources");
                if (bccResources == null) continue;
                JSONArray flavorGroups = bccResources.getJSONArray("flavorGroups");
                if (flavorGroups == null) continue;
                for (int k = 0; k < flavorGroups.size(); k++) {
                    JSONObject group = flavorGroups.getJSONObject(k);
                    JSONArray flavors = group.getJSONArray("flavors");
                    if (flavors == null) continue;
                    for (int m = 0; m < flavors.size(); m++) {
                        JSONObject flavorSpec = flavors.getJSONObject(m);
                        CmdbFlavor ci = new CmdbFlavor();
                        // 字段对照表实现
                        ci.setRes_id(IdUtils.encryptId(cmpId, flavorSpec.getString("spec"), zoneName));
                        ci.setCpu_arch(flavorSpec.getString("cpuModel"));
                        ci.setCpu_size(flavorSpec.getInteger("cpuCount"));
                        Integer memGb = flavorSpec.getInteger("memoryCapacityInGB");
                        ci.setMem_size(memGb != null ? memGb * 1024 : null);
                        ci.setMin_sysdisk_size(flavorSpec.getFloat("ephemeralDiskInGb"));
                        ci.setSpecification_class_code(flavorSpec.getString("specId"));
                        ci.setSpecification_class_name(flavorSpec.getString("specId"));
                        if (flavorSpec.containsKey("gpuCardType") && flavorSpec.getString("gpuCardType") != null && !flavorSpec.getString("gpuCardType").isEmpty()) {
                            ci.setCategory(flavorSpec.getString("gpuCardType"));
                        } else {
                            ci.setCategory(null);
                        }
                        // GPU大小 - 百度云未返回该字段，设为null
                        ci.setGpu_size(null);
                        ci.setGpu_num(flavorSpec.getInteger("gpuCardCount"));
                        ci.setGpu_model(flavorSpec.getString("gpuCardType"));
                        ci.setOpen_id(flavorSpec.getString("spec"));
                        ci.setOpen_name(flavorSpec.getString("spec"));
                        ci.setCloud_type(realm);
                        ci.setAccount_id(cmpId);
                        data.add(ci);
                        TmdbResourceSet zoneFlavor = builder.builderResourceSet(
                                ci.getRes_id(),
                                DevopsSide.DEVOPS_ZONE,
                                BuilderDevops.builderId(cmpId,
                                        realm,
                                        DevopsSide.DEVOPS_ZONE.value(),
                                        zone.getString("zoneName"))
                        );
                        sets.add(zoneFlavor);
                    }
                }
            }
        }
        List<TmdbResourceSet> setsRegion = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        ResourceType.CMDB_FLAVOR.value()
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        if(CollUtil.isNotEmpty(setsRegion)){
            sets.addAll(setsRegion);
        }
        result.put(CmdbFlavor.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 转换百度云网卡数据为CI模型
     */
    public static Map<Class, List> convertNetworkInterface(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        if (null == response || null == response.getJSONArray("enis") || CollUtil.isEmpty(response.getJSONArray("enis"))) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }

        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        String cmpId = request.getBody().getAccess().getCmpId();
        String regionId = request.getBody().getCloud().getString("regionId");
        JSONArray nics = response.getJSONArray("enis");
        for (int i = 0; i < nics.size(); i++) {
            JSONObject nic = nics.getJSONObject(i);
            CmdbNetcardRes ci = new CmdbNetcardRes();
            ci.setRes_id(IdUtils.encryptId(cmpId, nic.getString("eniId"), regionId));
            ci.setOpen_id(nic.getString("eniId"));
            ci.setOpen_name(nic.getString("name"));
            ci.setDesc(nic.getString("description"));
            String status = nic.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertNetworkInterfaceStatus(status));
            if(null != nic.getJSONArray("privateIpSet")){
                ci.setIpv4_address(nic.getJSONArray("privateIpSet").toJSONString());
            }
            if(null != nic.getJSONArray("ipv6PrivateIpSet")){
                ci.setIpv6_address(nic.getJSONArray("ipv6PrivateIpSet").toJSONString());
            }
            ci.setMac_address(nic.getString("macAddress"));
            // 网卡分类 - 百度云无此字段，设为null
            ci.setCategory(null);
            // 网卡类型 - 百度云无此字段，设为null
            ci.setType(null);
            // 网卡型号 - 百度云无此字段，设为null
            ci.setModule(null);
            // dns网关 - 百度云无此字段，设为null
            ci.setDns_config(null);
            // 创建时间 - 百度云无此字段，设为null
            ci.setCreate_time(null);
            ci.setOpen_create_time(null);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储VPC ID
            ci.setExtend1(nic.getString("vpcId"));
            // 扩展字段2 - 存储子网ID
            ci.setExtend2(nic.getString("subnetId"));
            // 扩展字段3 - 存储实例ID
            ci.setExtend3(nic.getString("instanceId"));
            toCiResCloud(request, ci);
            data.add(ci);
            // 关联VPC
            if (StrUtil.isNotEmpty(nic.getString("vpcId"))) {
                Association vpcAss = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(cmpId, nic.getString("vpcId"), regionId));
                associations.add(vpcAss);
            }
            // 关联子网
            if (StrUtil.isNotEmpty(nic.getString("subnetId"))) {
                Association subnetAss = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(cmpId, nic.getString("subnetId"), regionId));
                associations.add(subnetAss);
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(cmpId, request.getPlugin().getRealm(), ResourceType.CMDB_NETCARD_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, regionId)
                .getData();
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云地域数据为CI模型（参考阿里云实现，严格按百度云SDK zone结构）
     */
    public static Map<Class, List> convertRegion(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("regions") || CollUtil.isEmpty(response.getJSONArray("regions"))) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        
        // 参考阿里云实现，使用BuilderDevops.withData方法
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                response.getJSONArray("regions"),
                region -> ((JSONObject)region).getString("regionName"),  // 地域名称
                region -> ((JSONObject)region).getString("regionId")  // 地域ID
        );
        
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 转换百度云可用区数据为CI模型（参考阿里云实现，严格按百度云SDK zone结构）
     */
    public static Map<Class, List> convertZone(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray zones = response.getJSONArray("zones");
        if (zones == null || zones.isEmpty()) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        // region级devops
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        request.getBody().getCloud().getString("regionId"),
                        DevopsSide.DEVOPS_REGION.value());
        for (int i = 0; i < zones.size(); i++) {
            JSONObject zone = zones.getJSONObject(i);
            BuilderDevops devopsZone = new BuilderDevops()
                    .withDevops(devops.get(), zone.getString("zoneName"), zone.getString("zoneName"), DevopsSide.DEVOPS_ZONE.value())
                    .withJson(zone.toJSONString());
            TmdbDevops zoneDevops = devopsZone.get();
            zoneDevops.setBiz_id(BuilderDevops.builderId(zoneDevops.getAccount_id(),
                    zoneDevops.getCloud_type(),
                    zoneDevops.getDict_code(),
                    zoneDevops.getDevops_value()));
            data.add(zoneDevops);
            links.add(devopsZone.builderLink(devops.get()));
        }
        result.put(TmdbDevops.class, data);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }

    /**
     * 转换百度云告警数据为CI模型
     */
    public static Map<Class, List> convertAlarm(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        List<AlarmInfoBean> data = new ArrayList<>();
        if (null == response || null == response.getJSONObject("result")) {
            result.put(AlarmInfoBean.class, null);
            return result;
        }
        if (null == response.getJSONObject("result").getJSONArray("alarms") || CollUtil.isEmpty(response.getJSONObject("result").getJSONArray("alarms"))) {
            result.put(AlarmInfoBean.class, null);
            return result;
        }
        JSONArray alarms = response.getJSONObject("result").getJSONArray("alarms");
        for (int i = 0; i < alarms.size(); i++) {
            JSONObject alarm = alarms.getJSONObject(i);
            
            AlarmInfoBean alarmInfo = new AlarmInfoBean();
            alarmInfo.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(),
                alarm.getString("id"),request.getBody().getCloud().getString("regionId")));
            alarmInfo.setAccountId(request.getBody().getAccess().getCmpId());
            alarmInfo.setCloudType(request.getPlugin().getRealm()); // 使用字符串而不是 CloudType.PUBLIC_BAIDU
            
            // 修复空指针异常：添加对resource和identifiers的空值检查
            String resId = null;
            try {
                JSONObject resource = alarm.getJSONObject("resource");
                if (resource != null) {
                    JSONObject identifiers = resource.getJSONObject("identifiers");
                    if (identifiers != null) {
                        resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), 
                            identifiers.getString("instanceId"));
                    }
                }
            } catch (Exception e) {
                log.warn("解析告警资源信息失败，告警ID: {}, 错误: {}", alarm.getString("id"), e.getMessage());
            }
            alarmInfo.setResId(resId);
            
            alarmInfo.setOpenId(alarm.getString("id"));
            alarmInfo.setOpenName(alarm.getString("seriesId"));
            alarmInfo.setOpenLevel(null);
            alarmInfo.setAlarmId(alarm.getString("id"));
            alarmInfo.setAlarmName(alarm.getString("seriesId"));
            
            // 修复空指针异常：添加对policy的空值检查
            String detail = null;
            try {
                JSONObject policy = alarm.getJSONObject("policy");
                if (policy != null) {
                    detail = policy.getString("content");
                }
            } catch (Exception e) {
                log.warn("解析告警策略信息失败，告警ID: {}, 错误: {}", alarm.getString("id"), e.getMessage());
            }
            alarmInfo.setDetail(detail);
            
            alarmInfo.setClosedStatus("".equals(alarm.getString("ALERT")) || "".equals(alarm.getString("INSUFFICIENT_DATA")) ? false : true);
            alarmInfo.setJsonInfo(alarm.toJSONString());
            alarmInfo.setCount(1);

            if (StrUtil.isNotEmpty(alarm.getString("startTime"))) {
                alarmInfo.setFirstTime(alarm.getString("startTime"));
                alarmInfo.setClosedTime(alarm.getString("endTime"));
            }
            data.add(alarmInfo);
        }
        
        result.put(AlarmInfoBean.class, data);
        return result;
    }

    /**
     * 转换百度云事件数据为CI模型
     */
    public static Map<Class, List> convertEvent(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("events") || CollUtil.isEmpty(response.getJSONArray("events"))) {
            result.put(EventInfoBean.class, null);
            return result;
        }
        
        List<EventInfoBean> data = new ArrayList<>();
        
        JSONArray events = response.getJSONArray("events");
        for (int i = 0; i < events.size(); i++) {
            JSONObject event = events.getJSONObject(i);
            
            EventInfoBean eventInfo = new EventInfoBean();
            eventInfo.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), 
                event.getString("instanceId"), 
                event.getString("eventId"), 
                event.getString("eventTime")));
            eventInfo.setAccountId(request.getBody().getAccess().getCmpId());
            eventInfo.setCloudType(request.getPlugin().getRealm());
            eventInfo.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), event.getString("instanceId")));
            eventInfo.setOpenId(event.getString("instanceId"));
            eventInfo.setOpenName(event.getString("instanceName"));
            eventInfo.setOpenLevel(event.getString("eventLevel"));
            eventInfo.setEventName(event.getString("eventName"));
            eventInfo.setEventType(event.getString("eventType"));
            eventInfo.setDetail(event.getString("message"));
            eventInfo.setJsonInfo(event.toJSONString());
            
            if (StrUtil.isNotEmpty(event.getString("eventTime"))) {
                eventInfo.setBeginTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(event.getString("eventTime")))));
                eventInfo.setEndTime(eventInfo.getBeginTime());
            }
            
            data.add(eventInfo);
        }
        
        result.put(EventInfoBean.class, data);
        return result;
    }

    /**
     * 转换百度云负载均衡数据为CI模型
     */
    public static Map<Class, List> convertLoadBalancer(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getJSONArray("loadBalancers") || CollUtil.isEmpty(response.getJSONArray("loadBalancers"))) {
            result.put(CmdbLoadbalanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbLoadbalanceRes> data = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        JSONArray loadBalancers = response.getJSONArray("loadBalancers");
        for (int i = 0; i < loadBalancers.size(); i++) {
            JSONObject lb = loadBalancers.getJSONObject(i);
            
            CmdbLoadbalanceRes loadbalance = new CmdbLoadbalanceRes();
            
            // 基本信息
            loadbalance.setOpen_id(lb.getString("blbId"));
            loadbalance.setOpen_name(lb.getString("name"));
            loadbalance.setDesc(lb.getString("description"));
            String status = lb.getString("status");
            loadbalance.setOpen_status(status);
            loadbalance.setStatus(convertLoadBalancerStatus(status));
            loadbalance.setAddress(lb.getString("address"));
            loadbalance.setManufacturer("百度云");
            loadbalance.setModel(lb.getString("type"));
            loadbalance.setNetwork_type(lb.getString("type"));
            loadbalance.setBandwidth_speed(lb.getString("bandwidth") + "Mbps");
            
            // 时间信息
            if (StrUtil.isNotEmpty(lb.getString("createTime"))) {
                loadbalance.setCreate_time(Long.parseLong(lb.getString("createTime")));
                loadbalance.setOpen_create_time(Long.parseLong(lb.getString("createTime")));
            }
            if (StrUtil.isNotEmpty(lb.getString("updateTime"))) {
                loadbalance.setUpdate_time(Long.parseLong(lb.getString("updateTime")));
                loadbalance.setOpen_update_time(Long.parseLong(lb.getString("updateTime")));
            }
            
            // 通用字段
            toCiResCloud(request, loadbalance);
            
            data.add(loadbalance);
            
            // 创建资源集
            TmdbResourceSet resourceSet = new TmdbResourceSet();
            resourceSet.setBiz_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), lb.getString("blbId")));
            resourceSet.setAccount_id(request.getBody().getAccess().getCmpId());
            resourceSet.setCloud_type(request.getPlugin().getRealm());
            resourceSet.setResource_type("loadbalancer");
            resourceSet.setResource_id(loadbalance.getRes_id());
            resourceSet.setSet_type("nxc");
            resourceSet.setSet_table("cmdb_loadbalance_res");
            resourceSet.setSet_id(loadbalance.getOpen_id());
            resourceSets.add(resourceSet);
        }
        
        result.put(CmdbLoadbalanceRes.class, data);
        result.put(TmdbResourceSet.class, resourceSets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云Bucket数据为CI模型
     */
    public static Map<Class, List> convertBucket(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray buckets = response.getJSONArray("buckets");
        if (buckets == null || buckets.isEmpty()) {
            result.put(CmdbBucketRes.class, null);
            return result;
        }
        List<CmdbBucketRes> data = new ArrayList<>();
        for (int i = 0; i < buckets.size(); i++) {
            JSONObject bucket = buckets.getJSONObject(i);
            CmdbBucketRes ci = new CmdbBucketRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), bucket.getString("name")));
            ci.setOpen_id(bucket.getString("name"));
            ci.setOpen_name(bucket.getString("name"));
            
            // 修复日期解析问题：百度云返回的是日期字符串，需要解析为时间戳
            Long creationTime = parseBaiduDate(bucket.getString("creationDate"));
            ci.setOpen_create_time(creationTime);
            ci.setCreate_time(creationTime);
            // 桶类型 - 百度云无此字段，设为null
            ci.setType(null);
            // 访问权限 - 百度云无此字段，设为null
            ci.setAcl(null);
            // 存储类型 - 百度云无此字段，设为null
            ci.setStorage_type(null);
            // 对象个数 - 百度云无此字段，设为null
            ci.setObject_number(null);
            // 空间大小(MB) - 百度云无此字段，设为null
            ci.setSize(null);
            // 防盗链策略 - 百度云无此字段，设为null
            ci.setPolicy(null);
            // 版本控制状态 - 百度云无此字段，设为null
            ci.setVersioning_status(null);
            // 生命周期 - 百度云无此字段，设为null
            ci.setLifecycle(null);
            // 访问日志 - 百度云无此字段，设为null
            ci.setLogging(null);
            // 静态页面 - 百度云无此字段，设为null
            ci.setStatic_page(null);
            // 跨域访问 - 百度云无此字段，设为null
            ci.setCross_domain(null);
            // 存储阈值(最大容量GB) - 百度云无此字段，设为null
            ci.setStorage_capacity(null);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储桶位置
            ci.setExtend1(bucket.getString("location"));
            // 扩展字段2 - 存储桶状态
            ci.setExtend2(bucket.getString("status"));
            // 扩展字段3 - 存储桶标签
            ci.setExtend3(bucket.getJSONArray("tags") != null ? bucket.getJSONArray("tags").toJSONString() : null);
            toCiResCloud(request, ci);
            data.add(ci);
        }
        result.put(CmdbBucketRes.class, data);
        return result;
    }

    /**
     * 转换百度云Bucket文件数据为CI模型
     */
    public static Map<Class, List> convertBucketFile(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray objects = response.getJSONArray("objects");
        if (objects == null || objects.isEmpty()) {
            result.put(CmdbBucketFileRes.class, null);
            return result;
        }
        List<CmdbBucketFileRes> data = new ArrayList<>();
        for (int i = 0; i < objects.size(); i++) {
            JSONObject obj = objects.getJSONObject(i);
            CmdbBucketFileRes ci = new CmdbBucketFileRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), obj.getString("key")));
            ci.setOpen_id(obj.getString("key"));
            ci.setOpen_name(obj.getString("key"));
            ci.setFile_size(obj.getLong("size").floatValue());
            
            // 修复日期解析问题：百度云返回的是日期字符串，需要解析为时间戳
            Long lastModifiedTime = parseBaiduDate(obj.getString("lastModified"));
            ci.setCreate_time(lastModifiedTime);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_create_time(lastModifiedTime);
            ci.setOpen_update_time(null);
            // 扩展字段1 - 存储文件ETag
            ci.setExtend1(obj.getString("eTag"));
            // 扩展字段2 - 存储文件存储类型
            ci.setExtend2(obj.getString("storageClass"));
            // 扩展字段3 - 存储文件标签
            ci.setExtend3(obj.getJSONArray("tags") != null ? obj.getJSONArray("tags").toJSONString() : null);
            toCiResCloud(request, ci);
            data.add(ci);
        }
        result.put(CmdbBucketFileRes.class, data);
        return result;
    }

    /**
     * 转换百度云NAT数据为CI模型
     */
    public static Map<Class, List> convertNat(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray nats = response.getJSONArray("nats");
        if (nats == null || nats.isEmpty()) {
            result.put(CmdbNatRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (int i = 0; i < nats.size(); i++) {
            JSONObject nat = nats.getJSONObject(i);
            CmdbNatRes ci = new CmdbNatRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nat.getString("id"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(nat.getString("id"));
            ci.setOpen_name(nat.getString("name"));
            String status = nat.getString("status");
            ci.setOpen_status(status);
            ci.setStatus(convertNatStatus(status));
            // 管理状态 - 百度云无此字段，设为null
            ci.setAdmin_state(null);
            // NAT网关的规格 - 百度云无此字段，设为null
            ci.setSpec(null);
            // 创建时间 - 百度云无此字段，设为null
            ci.setCreate_time(null);
            ci.setOpen_create_time(null);
            // 更新时间 - 百度云无此字段，设为null
            ci.setUpdate_time(null);
            ci.setOpen_update_time(null);
            // 描述 - 百度云无此字段，设为null
            ci.setDesc(null);
            // 扩展字段1 - 存储VPC ID
            ci.setExtend1(nat.getString("vpcId"));
            // 扩展字段2 - 存储NAT网关类型
            ci.setExtend2(nat.getString("type"));
            // 扩展字段3 - 存储NAT网关标签
            ci.setExtend3(nat.getJSONArray("tags") != null ? nat.getJSONArray("tags").toJSONString() : null);
            toCiResCloud(request, ci);
            data.add(ci);
            //关联VPC
            Association associationVpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nat.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
            associations.add(associationVpc);
        }

        //关联区域
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_NAT_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();

        result.put(CmdbNatRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云路由表数据为CI模型
     */
    public static Map<Class, List> convertRoute(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONObject route = response.getJSONObject("route");
        if (route == null) {
            result.put(CmdbRouteRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbRouteRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        CmdbRouteRes ci = new CmdbRouteRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), route.getString("routeTableId"), request.getBody().getCloud().getString("regionId")));
        ci.setOpen_id(route.getString("routeTableId"));
        ci.setOpen_name("default");
        toCiResCloud(request, ci);
        data.add(ci);

        //关联VPC
        Association associationVpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), route.getString("vpcId"), request.getBody().getCloud().getString("regionId")));
        associations.add(associationVpc);

        //关联区域
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_NAT_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();

        result.put(CmdbRouteRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云SNAT规则数据为CI模型
     */
    public static Map<Class, List> convertSnatEntry(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray snatEntries = response.getJSONArray("rules");
        if (snatEntries == null || snatEntries.isEmpty()) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (int i = 0; i < snatEntries.size(); i++) {
            JSONObject snatEntry = snatEntries.getJSONObject(i);
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            
            // 设置基本信息
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), snatEntry.getString("id"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(snatEntry.getString("id"));
            ci.setOpen_name(snatEntry.getString("name"));
            ci.setType("SNAT");
            
            // 设置SNAT特有字段
            ci.setCidr(snatEntry.getString("sourceCidr"));
            ci.setPublic_ip(snatEntry.getString("publicIp"));
            
            // 设置通用字段
            toCiResCloud(request, ci);
            data.add(ci);
            
            // 关联NAT网关
            String natId = response.getString("natId");
            if (StrUtil.isNotEmpty(natId)) {
                Association natAssociation = AssociationUtils.toAssociation(ci, CmdbNatRes.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), natId, request.getBody().getCloud().getString("regionId")));
                associations.add(natAssociation);
            }
        }
        
        // 关联区域
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_NAT_ENTRY_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbNatEntryRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云DNAT规则数据为CI模型
     */
    public static Map<Class, List> convertDnatEntry(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        JSONArray dnatEntries = response.getJSONArray("rules");
        if (dnatEntries == null || dnatEntries.isEmpty()) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (int i = 0; i < dnatEntries.size(); i++) {
            JSONObject dnatEntry = dnatEntries.getJSONObject(i);
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            
            // 设置基本信息
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), dnatEntry.getString("id"), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(dnatEntry.getString("id"));
            ci.setOpen_name(dnatEntry.getString("name"));
            ci.setType("DNAT");
            
            // 设置DNAT特有字段
            ci.setProtocol(dnatEntry.getString("protocol"));
            ci.setPublic_ip(dnatEntry.getString("publicIp"));
            ci.setPublic_port(dnatEntry.getString("publicPort"));
            ci.setPrivate_ip(dnatEntry.getString("privateIp"));
            ci.setPrivate_port(dnatEntry.getString("privatePort"));
            
            // 设置通用字段
            toCiResCloud(request, ci);
            data.add(ci);
            
            // 关联NAT网关
            String natId = response.getString("natId");
            if (StrUtil.isNotEmpty(natId)) {
                Association natAssociation = AssociationUtils.toAssociation(ci, CmdbNatRes.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), natId, request.getBody().getCloud().getString("regionId")));
                associations.add(natAssociation);
            }
        }
        
        // 关联区域
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_NAT_ENTRY_RES.value())
                .withDataByDevopsValue(data, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbNatEntryRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换百度云云主机监控数据为性能信息
     * 处理百度云BCM API返回的多维度监控数据
     * 百度云返回数据结构：
     * {
     *   "metrics": [
     *     {
     *       "resourceId": "i-8y****gG",
     *       "metricName": "CPUUsagePercent",
     *       "dimensions": [...],
     *       "dataPoints": [
     *         {
     *           "timestamp": "2024-03-26T07:01:00Z",
     *           "average": 7.9346566231995,
     *           "sum": 15.869313246399
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    public static Map<String, PerfInfoBean> convertEcsPerf(BaseCloudRequest request, List<JSONObject> metricList) {
        if (CollUtil.isEmpty(metricList)) {
            return null;
        }
        
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        String cmpId = request.getBody().getAccess().getCmpId();
        String realm = request.getPlugin().getRealm();
        
        // 获取云主机实例信息映射（用于填充PerfInfoBean的基本信息）
        Map<String, JSONObject> instanceMap = getInstanceMap(request);
        
        for (JSONObject metricResult : metricList) {
            String metricName = metricResult.getString("metricName");
            JSONObject data = metricResult.getJSONObject("data");
            
            if (data == null) {
                continue;
            }
            
            // 处理百度云BCM API返回的数据结构
            // 百度云返回的数据结构中，metrics是一个数组
            JSONArray metrics = data.getJSONArray("metrics");
            if (metrics == null || metrics.isEmpty()) {
                continue;
            }
            
            // 遍历每个指标数据
            for (int i = 0; i < metrics.size(); i++) {
                JSONObject metric = metrics.getJSONObject(i);
                
                // 获取指标名称和实例ID
                String actualMetricName = metric.getString("metricName");
                String resourceId = metric.getString("resourceId");
                
                // 从dimensions中获取实例ID（作为备用）
                String instanceId = resourceId; // 优先使用resourceId
                JSONArray dimensions = metric.getJSONArray("dimensions");
                if (dimensions != null && !dimensions.isEmpty()) {
                    for (int j = 0; j < dimensions.size(); j++) {
                        JSONObject dimension = dimensions.getJSONObject(j);
                        if ("InstanceId".equals(dimension.getString("name"))) {
                            // 如果resourceId为空，使用dimension中的value
                            if (StrUtil.isEmpty(instanceId)) {
                                instanceId = dimension.getString("value");
                            }
                            break;
                        }
                    }
                }
                
                if (StrUtil.isEmpty(instanceId)) {
                    continue;
                }
                
                // 处理数据点
                JSONArray dataPoints = metric.getJSONArray("dataPoints");
                if (dataPoints == null || dataPoints.isEmpty()) {
                    continue;
                }
                
                for (int k = 0; k < dataPoints.size(); k++) {
                    JSONObject dataPoint = dataPoints.getJSONObject(k);
                    
                    // 获取时间戳（百度云返回的是ISO格式字符串）
                    String timestampStr = dataPoint.getString("timestamp");
                    Long timestamp = null;
                    if (StrUtil.isNotEmpty(timestampStr)) {
                        try {
                            // 将ISO格式时间转换为时间戳
                            timestamp = DateUtil.parse(timestampStr).getTime();
                        } catch (Exception e) {
                            log.warn("解析时间戳失败: {}", timestampStr, e);
                            continue;
                        }
                    }
                    
                    // 获取监控值（优先使用average，如果没有则使用sum）
                    Object value = dataPoint.get("average");
                    if (value == null) {
                        value = dataPoint.get("sum");
                    }
                    if (value == null) {
                        value = dataPoint.get("maximum");
                    }
                    if (value == null) {
                        value = dataPoint.get("minimum");
                    }
                    
                    // 如果仍然没有值，记录详细的调试信息
                    if (timestamp == null || value == null) {
                        log.warn("数据点缺少有效值 - 实例ID: {}, 指标: {}, 时间戳: {}, 数据点内容: {}", 
                                instanceId, actualMetricName, timestampStr, dataPoint.toJSONString());
                        continue;
                    }
                    
                    // 生成唯一键
                    String id = instanceId + "_" + actualMetricName + "_" + timestamp;
                    
                    // 获取或创建PerfInfoBean对象
                    PerfInfoBean perf = perfMap.get(id);
                    if (perf == null) {
                        perf = new PerfInfoBean();
                        perf.setId(id);
                        
                        // 设置基本信息
                        JSONObject instance = instanceMap.get(instanceId);
                        if (instance != null) {
                            perf.setAccountId(cmpId);
                            perf.setCloudType(realm);
                            perf.setResId(IdUtils.encryptId(cmpId, instanceId));
                            perf.setOpenId(instanceId);
                            perf.setOpenName(instance.getString("name"));
                            
                            // 设置CPU和内存信息
                            Integer cpuCount = instance.getInteger("cpuCount");
                            if (cpuCount != null) {
                                perf.setCpuSize(cpuCount.doubleValue());
                            }
                            
                            Integer memGb = instance.getInteger("memoryCapacityInGB");
                            if (memGb != null) {
                                perf.setMemSize(memGb.doubleValue() * 1024); // 转换为MB
                            }
                            
                            // 设置磁盘大小（如果有磁盘信息）
                            perf.setDiskSize(null); // 暂时设为null，后续可以根据需要获取
                            
                            // 设置创建时间
                            perf.setCreateTime(DateUtil.formatDateTime(DateUtil.date(timestamp)));
                        }
                    }
                    
                    // 设置监控指标值
                    if (value instanceof Number) {
                        double doubleValue = ((Number) value).doubleValue();
                        BiConsumer<PerfInfoBean, Double> setValue = perfMappingBean.get(actualMetricName);
                        if (setValue != null) {
                            setValue.accept(perf, doubleValue);
                        }
                    }
                    
                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        
        log.info("转换百度云监控数据完成，共处理 {} 个性能数据点", perfMap.size());
        if (perfMap.isEmpty()) {
            log.warn("监控数据转换结果为空，请检查以下可能的原因：");
            log.warn("1. 实例是否处于运行状态");
            log.warn("2. 请求的时间范围内是否有监控数据");
            log.warn("3. 监控指标名称是否正确");
            log.warn("4. 实例是否有足够的负载产生监控数据");
        }
        return perfMap;
    }
    
    /**
     * 获取云主机实例信息映射
     * 用于填充PerfInfoBean的基本信息
     */
    private static Map<String, JSONObject> getInstanceMap(BaseCloudRequest request) {
        Map<String, JSONObject> instanceMap = new HashMap<>();
        try {
            // 如果请求中已经包含了实例映射信息，直接使用
            if (request.getBody().containsKey("instanceMap")) {
                return (Map<String, JSONObject>) request.getBody().get("instanceMap");
            }
            
            // 否则尝试从云主机列表中获取
            // 这里可以调用云主机获取API，或者从缓存中获取
            // 暂时返回空映射，实际实现时可以根据需要获取实例信息
            log.debug("未找到实例映射信息，使用空映射");
            
        } catch (Exception e) {
            log.warn("获取实例映射信息失败", e);
        }
        return instanceMap;
    }

    /**
     * 解析百度云日期字符串为时间戳
     * 支持格式：Tue Jun 24 11:00:45 CST 2025
     * 
     * @param dateString 百度云返回的日期字符串
     * @return 时间戳（毫秒），如果解析失败返回当前时间戳
     */
    private static Long parseBaiduDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return System.currentTimeMillis();
        }
        
        try {
            // 百度云日期格式：Tue Jun 24 11:00:45 CST 2025
            // 使用线程安全的 DateTimeFormatter
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
            LocalDateTime localDateTime = LocalDateTime.parse(dateString, formatter);
            return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (DateTimeParseException e) {
            log.warn("解析百度云日期失败: {}, 使用当前时间", dateString, e);
            return System.currentTimeMillis();
        }
    }
} 