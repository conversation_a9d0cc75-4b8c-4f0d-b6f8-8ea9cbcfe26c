//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.futong.gemini.plugin.cloud.baidu.client2.utils;

import java.util.Date;
import java.util.Locale;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;

public class BaiduDateUtils {
    private static final DateTimeFormatter iso8601DateFormat;
    private static final DateTimeFormatter alternateIso8601DateFormat;
    private static final DateTimeFormatter rfc822DateFormat;
    private static final DateTimeFormatter compressedIso8601DateFormat;

    public BaiduDateUtils() {
    }

    public static Date parseIso8601Date(String dateString) {
        try {
            return iso8601DateFormat.parseDateTime(dateString).toDate();
        } catch (IllegalArgumentException var2) {
            return alternateIso8601DateFormat.parseDateTime(dateString).toDate();
        }
    }

    public static String formatIso8601Date(Date date) {
        return iso8601DateFormat.print(new DateTime(date));
    }

    public static Date parseAlternateIso8601Date(String dateString) {
        return alternateIso8601DateFormat.parseDateTime(dateString).toDate();
    }

    public static String formatAlternateIso8601Date(Date date) {
        return alternateIso8601DateFormat.print(new DateTime(date));
    }

    public static Date parseRfc822Date(String dateString) {
        return rfc822DateFormat.parseDateTime(dateString).toDate();
    }

    public static String formatRfc822Date(Date date) {
        return rfc822DateFormat.print(new DateTime(date));
    }

    public static Date parseCompressedIso8601Date(String dateString) {
        return compressedIso8601DateFormat.parseDateTime(dateString).toDate();
    }

    public static void validateUtcDate(String dateString) {
        if (!StringUtils.isBlank(dateString)) {
            String pattern = "[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}Z";
            if (!Pattern.matches(pattern, dateString)) {
                throw new IllegalArgumentException("UTC date format is illegal");
            }
        }
    }

    static {
        iso8601DateFormat = ISODateTimeFormat.dateTime().withZone(DateTimeZone.UTC);
        alternateIso8601DateFormat = ISODateTimeFormat.dateTimeNoMillis().withZone(DateTimeZone.UTC);
        rfc822DateFormat = DateTimeFormat.forPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'").withLocale(Locale.US).withZone(DateTimeZone.UTC);
        compressedIso8601DateFormat = ISODateTimeFormat.basicDateTimeNoMillis().withZone(DateTimeZone.UTC);
    }
}
