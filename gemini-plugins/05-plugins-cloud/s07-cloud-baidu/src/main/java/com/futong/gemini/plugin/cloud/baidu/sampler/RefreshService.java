package com.futong.gemini.plugin.cloud.baidu.sampler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.subnet.SubnetClient;
import com.baidubce.services.subnet.model.ListSubnetsRequest;
import com.baidubce.services.vpc.model.ListVpcRequest;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbRouteRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbVpcRes;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.baidu.common.Convert;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 百度云资源刷新服务
 */
@Slf4j
public class RefreshService {

    /**
     * 百度云ECS实例中间状态集合
     * 这些状态表示实例正在执行某种操作，需要继续刷新直到状态稳定
     */
    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("Starting");           // 启动中
//            add("Running");            // 运行中
            add("Stopping");           // 停止中
//            add("Stopped");            // 已停止
            add("Recycled");           // 处于回收站中
//            add("Deleted");            // 已释放，该状态为内部状态，API无法查询
            add("Scaling");            // 扩展中
            add("Expired");            // 已过期或欠费
            add("Error");              // 错误
            add("SnapshotProcessing"); // 快照操作中
            add("ImageProcessing");    // 镜像操作中
            add("Recharging");         // 续费中
            add("VolumeResizing");     // 磁盘扩容中
            add("BillingChanging");    // 计费变更中
            add("ChangeSubnet");       // 子网变更中
            add("ChangeVpc");          // VPC变更中
            add("AttachingPort");      // 挂载弹性网卡中
            add("DetachingPort");      // 卸载弹性网卡中
            add("Moving");             // 迁移中，实例操作变更可用区/跨AZ迁移时的状态
        }
    };
    private final static Set<String> INTERMEDIATE_VPC = new HashSet<String>() {
        {
            add("Pending");
        }
    };

    /**
     * 刷新云主机信息
     * 参考阿里云实现，支持中间状态刷新和删除检测
     */
    public static BaseResponse refreshInstance(BaseCloudRequest request) {
        try {
            log.info("开始刷新百度云云主机信息");
            Integer refreshCount = request.getBody().getGourd().getCount();
            Integer refreshMaxCount = 5;
            Integer refreshInterval = 5000;
            JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
            if (refreshConfig != null) {
                if (refreshConfig.containsKey("refreshMaxCount")) {
                    refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
                }
                if (refreshConfig.containsKey("refreshInterval")) {
                    refreshInterval = refreshConfig.getInteger("refreshInterval");
                }
            }
            // 手动SDK调用
            com.baidubce.services.bcc.BccClient bccClient = BaiduClient.client.client(com.baidubce.services.bcc.BccClient.class, request.getBody());
            com.baidubce.services.bcc.model.instance.ListInstancesRequest listRequest = new com.baidubce.services.bcc.model.instance.ListInstancesRequest();
            // 可根据需要设置分页、zone等参数
            Object response = bccClient.listInstances(listRequest);
            JSONObject result = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(response));
            Map<Class, List> map = Convert.convertInstance(request, result);
            List<CmdbInstanceRes> res = map.get(CmdbInstanceRes.class);
            if (CollUtil.isEmpty(res)) {
                // 发送已删除
                String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
                CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
                cmdbInstanceRes.setRes_id(resId);
                List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
                BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class, 
                    request.getBody().getAccess().getCloudType(), request.getBody().getAccess().getCmpId());
            } else {
                // 发送同步更新任务
                BaseCloudService.refreshUpdateSend(request, map);
                CmdbInstanceRes cmdbInstanceRes = res.get(0);
                if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())
                        && refreshCount > 0
                        && refreshMaxCount > refreshCount) {
                    JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
                }
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("刷新云主机信息失败", e);
            return BaseResponse.FAIL_OP.of(e);
        }
    }

    /**
     * 刷新VPC信息
     * 参考阿里云实现，支持中间状态刷新和删除检测
     */
    public static BaseResponse refreshVpc(BaseCloudRequest request) {
        try {
            log.info("开始刷新百度云VPC信息");
            Integer refreshCount = request.getBody().getGourd().getCount();
            Integer refreshMaxCount = 5;
            Integer refreshInterval = 5000;
            JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
            if (refreshConfig != null) {
                if (refreshConfig.containsKey("refreshMaxCount")) {
                    refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
                }
                if (refreshConfig.containsKey("refreshInterval")) {
                    refreshInterval = refreshConfig.getInteger("refreshInterval");
                }
            }
            // 手动SDK调用
            com.baidubce.services.vpc.VpcClient vpcClient = BaiduClient.client.client(com.baidubce.services.vpc.VpcClient.class, request.getBody());
            ListVpcRequest vpcRequest = new ListVpcRequest();
            Object response = vpcClient.listVpcs(vpcRequest);
            JSONObject result = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(response));
            Map<Class, List> map = Convert.convertVpc(request, result);
            List<CmdbVpcRes> res = map.get(CmdbVpcRes.class);
            if (CollUtil.isEmpty(res)) {
                String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
                CmdbVpcRes cmdbVpcRes = new CmdbVpcRes();
                cmdbVpcRes.setRes_id(resId);
                List<CmdbVpcRes> data = CollUtil.newArrayList(cmdbVpcRes);
                BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbVpcRes.class, 
                    request.getBody().getAccess().getCloudType(), request.getBody().getAccess().getCmpId());
            } else {
                BaseCloudService.refreshUpdateSend(request, map);
                CmdbVpcRes cmdbVpcRes = res.get(0);
                if (INTERMEDIATE_VPC.contains(cmdbVpcRes.getOpen_status())
                        && refreshCount > 0
                        && refreshMaxCount > refreshCount) {
                    JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
                }
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("刷新VPC信息失败", e);
            return BaseResponse.FAIL_OP.of(e);
        }
    }

    /**
     * 刷新子网信息
     * 参考阿里云实现，支持中间状态刷新和删除检测
     */
    public static BaseResponse refreshSubnet(BaseCloudRequest request) {
        try {
            log.info("开始刷新百度云子网信息");
            Integer refreshCount = request.getBody().getGourd().getCount();
            Integer refreshMaxCount = 5;
            Integer refreshInterval = 5000;
            JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
            if (refreshConfig != null) {
                if (refreshConfig.containsKey("refreshMaxCount")) {
                    refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
                }
                if (refreshConfig.containsKey("refreshInterval")) {
                    refreshInterval = refreshConfig.getInteger("refreshInterval");
                }
            }
            // 手动SDK调用
            SubnetClient subnetClient = BaiduClient.client.client(SubnetClient.class, request.getBody());
            ListSubnetsRequest subnetRequest = new ListSubnetsRequest();
            Object response = subnetClient.listSubnets(subnetRequest);
            JSONObject result = com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(response));
            Map<Class, List> map = Convert.convertSubnet(request, result);
            List<CmdbSubnetRes> res = map.get(CmdbSubnetRes.class);
            if (CollUtil.isEmpty(res)) {
                String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
                CmdbSubnetRes cmdbSubnetRes = new CmdbSubnetRes();
                cmdbSubnetRes.setRes_id(resId);
                List<CmdbSubnetRes> data = CollUtil.newArrayList(cmdbSubnetRes);
                BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbSubnetRes.class, 
                    request.getBody().getAccess().getCloudType(), request.getBody().getAccess().getCmpId());
            } else {
                BaseCloudService.refreshUpdateSend(request, map);
                CmdbSubnetRes cmdbSubnetRes = res.get(0);
                if (INTERMEDIATE_VPC.contains(cmdbSubnetRes.getOpen_status())
                        && refreshCount > 0
                        && refreshMaxCount > refreshCount) {
                    JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
                }
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("刷新子网信息失败", e);
            return BaseResponse.FAIL_OP.of(e);
        }
    }
} 