package com.futong.gemini.plugin.cloud.baidu.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.volume.*;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBodyCI;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 百度云磁盘服务类
 * 负责云硬盘的管理操作
 * 
 * 主要功能：
 * - 云硬盘创建、删除、修改
 * - 云硬盘挂载、卸载
 * - 云硬盘扩容
 * 
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 * 使用百度云官方SDK进行API调用
 * 
 * 注意：百度云SDK v0.10.362中部分磁盘操作方法不存在，已添加注释说明
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class DiskService {

    /**
     * 创建云硬盘
     * 
     * 使用百度云官方SDK创建云硬盘
     * 支持参数：diskName, diskSize, zoneName, storageType, chargeType, purchaseCount
     */
    public static BaseResponse createDisk(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云云硬盘");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "diskSize", "zoneName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskSize, zoneName");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            
            // 构建创建云硬盘请求
            CreateVolumeRequest createRequest = new CreateVolumeRequest()
                    .withCdsSizeInGB(cloud.getInteger("diskSize")) // 磁盘大小（GB）
                    .withZoneName(cloud.getString("zoneName")) // 可用区
                    .withStorageType(cloud.getString("storageType") != null ? cloud.getString("storageType") : "hp1") // 存储类型，默认hp1 https://cloud.baidu.com/doc/BCC/s/6jwvyo0q2#volumemodel
                    .withChargeType(cloud.getString("chargeType") != null ? cloud.getString("chargeType") : "Postpaid") // 计费方式，默认后付费
                    .withPurchaseCount(cloud.getInteger("purchaseCount") != null ? cloud.getInteger("purchaseCount") : 1); // 购买数量，默认1
            
            // 可选参数：磁盘名称
            if (cloud.containsKey("diskName")) {
                createRequest.withName(cloud.getString("diskName"));
            }
            
            // 调用百度云API创建云硬盘
            CreateVolumeResponse response = bccClient.createVolume(createRequest);
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("volumeIds", response.getVolumeIds()); // 返回创建的磁盘ID列表
            result.put("diskSize", cloud.getInteger("diskSize"));
            result.put("zoneName", cloud.getString("zoneName"));
            result.put("storageType", cloud.getString("storageType") != null ? cloud.getString("storageType") : "hp1");
            
            log.info("创建百度云云硬盘成功，磁盘ID列表: {}", response.getVolumeIds());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云硬盘失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改云硬盘
     * 
     * 使用百度云官方SDK修改云硬盘属性
     * 支持参数：diskId, diskName, description
     */
    public static BaseResponse updateDisk(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云云硬盘");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "diskId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskId");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            
            // 构建修改云硬盘请求
            ModifyCdsAttrRequest modifyRequest = new ModifyCdsAttrRequest();
            modifyRequest.setCdsId(cloud.getString("diskId")); // 设置磁盘ID
            
            // 可选参数：磁盘名称
            if (cloud.getString("diskName") != null) {
                modifyRequest.setCdsName(cloud.getString("diskName"));
            }
            
            // 可选参数：磁盘描述
            if (cloud.getString("description") != null) {
                modifyRequest.setDesc(cloud.getString("description"));
            }
            
            // 调用百度云API修改云硬盘
            bccClient.modifyCdsAttribute(modifyRequest);
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("diskId", cloud.getString("diskId"));
            if (cloud.getString("diskName") != null) {
                result.put("diskName", cloud.getString("diskName"));
            }
            if (cloud.getString("description") != null) {
                result.put("description", cloud.getString("description"));
            }
            
            log.info("修改百度云云硬盘成功，磁盘ID: {}", cloud.getString("diskId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("修改百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改云硬盘失败: " + e.getMessage()), e);
        }
    }

    /**
     * 批量删除云硬盘
     * 
     * 使用百度云官方SDK删除云硬盘
     * 支持参数：diskIds, autoSnapshot, manualSnapshot, recycle
     */
    public static BaseResponse deleteDisk(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云云硬盘");

            // 验证必需参数
            if (CollUtil.isEmpty(request.getBody().getCis())) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：cis:[{}]");
            }

            //验证必需参数
            List<String> devopsLevel =request.getBody().getCis().stream().map(ci -> ci.getJSONObject("sourceJson").getString("devopsLevel01Value")).collect(Collectors.toList());
            if (devopsLevel == null || devopsLevel.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少区域ID列表");
            }

            // 获取要删除的镜像ID列表
            List<String> diskIds =request.getBody().getCis().stream().map(ci -> ci.getString("openId")).collect(Collectors.toList());
            if (diskIds == null || diskIds.isEmpty()) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少镜像ID列表");
            }

            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();

            // 获取可选参数
            String autoSnapshot = cloud.getString("autoSnapshot") != null ? cloud.getString("autoSnapshot") : "off";
            String manualSnapshot = cloud.getString("manualSnapshot") != null ? cloud.getString("manualSnapshot") : "off";
            String recycle = cloud.getString("recycle") != null ? cloud.getString("recycle") : "off";

            // 批量删除磁盘
            for (BaseCloudRequestBodyCI ci : request.getBody().getCis()){
                // 创建BccClient
                BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());

                // 构建删除云硬盘请求
                ReleaseVolumeRequest releaseRequest = new ReleaseVolumeRequest();
                releaseRequest.setVolumeId(ci.getOpenId()); // 设置磁盘ID
                releaseRequest.setAutoSnapshot(autoSnapshot); // 是否删除磁盘关联的自动快照
                releaseRequest.setManualSnapshot(manualSnapshot); // 是否删除磁盘关联的手动快照
                releaseRequest.setRecycle(recycle); // 是否将磁盘放入回收站

                // 调用百度云API删除云硬盘
                bccClient.releaseVolume(releaseRequest);

                log.info("删除百度云云硬盘成功，磁盘ID: {}", ci.getOpenId());
            }
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("deletedDiskIds", diskIds.toArray(new String[0]));
            result.put("autoSnapshot", autoSnapshot);
            result.put("manualSnapshot", manualSnapshot);
            result.put("recycle", recycle);
            
            log.info("批量删除百度云云硬盘成功，共删除 {} 个磁盘", diskIds.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("删除百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除云硬盘失败: " + e.getMessage()), e);
        }
    }

    /**
     * 挂载云硬盘
     * 
     * 使用百度云官方SDK挂载云硬盘到实例
     * 支持参数：diskId, instanceId
     */
    public static BaseResponse attachDisk(BaseCloudRequest request) {
        try {
            log.info("开始挂载百度云云硬盘");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "diskId", "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskId, instanceId");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            
            // 构建挂载云硬盘请求
            AttachVolumeRequest attachRequest = new AttachVolumeRequest();
            attachRequest.setVolumeId(cloud.getString("diskId")); // 设置需要挂载的磁盘ID
            attachRequest.setInstanceId(cloud.getString("instanceId")); // 设置BCC虚机ID
            
            // 调用百度云API挂载云硬盘
            AttachVolumeResponse response = bccClient.attachVolume(attachRequest);
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("diskId", cloud.getString("diskId"));
            result.put("instanceId", cloud.getString("instanceId"));
            result.put("device", response.getVolumeAttachment().getDevice()); // 挂载路径
            result.put("volumeAttachment", response.getVolumeAttachment());
            
            log.info("挂载百度云云硬盘成功，磁盘ID: {}, 实例ID: {}, 挂载路径: {}", 
                    cloud.getString("diskId"), cloud.getString("instanceId"), 
                    response.getVolumeAttachment().getDevice());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("挂载百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("挂载云硬盘失败: " + e.getMessage()), e);
        }
    }

    /**
     * 卸载云硬盘
     * 
     * 使用百度云官方SDK卸载云硬盘
     * 支持参数：diskId, instanceId
     */
    public static BaseResponse detachDisk(BaseCloudRequest request) {
        try {
            log.info("开始卸载百度云云硬盘");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "diskId", "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskId, instanceId");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            
            // 构建卸载云硬盘请求
            DetachVolumeRequest detachRequest = new DetachVolumeRequest();
            detachRequest.setVolumeId(cloud.getString("diskId")); // 设置待卸载的磁盘ID
            detachRequest.setInstanceId(cloud.getString("instanceId")); // 设置所挂载的实例ID
            
            // 调用百度云API卸载云硬盘
            bccClient.detachVolume(detachRequest);
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("diskId", cloud.getString("diskId"));
            result.put("instanceId", cloud.getString("instanceId"));
            
            log.info("卸载百度云云硬盘成功，磁盘ID: {}, 实例ID: {}", 
                    cloud.getString("diskId"), cloud.getString("instanceId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("卸载百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("卸载云硬盘失败: " + e.getMessage()), e);
        }
    }

    /**
     * 扩容云硬盘
     * 
     * 使用百度云官方SDK扩容云硬盘
     * 支持参数：diskId, newSize, newVolumeType
     */
    public static BaseResponse resizeDisk(BaseCloudRequest request) {
        try {
            log.info("开始扩容百度云云硬盘");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "diskId", "newSize")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskId, newSize");
            }
            
            // 创建BccClient
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取请求参数
            JSONObject cloud = request.getBody().getCloud();
            
            // 构建扩容云硬盘请求
            ResizeVolumeRequest resizeRequest = new ResizeVolumeRequest();
            resizeRequest.setVolumeId(cloud.getString("diskId")); // 设置磁盘ID
            resizeRequest.setNewCdsSizeInGB(cloud.getInteger("newSize")); // 设置新的磁盘大小（GB）
            
            // 可选参数：新的磁盘类型
            if (cloud.getString("newVolumeType") != null) {
                resizeRequest.setNewVolumeType(cloud.getString("newVolumeType"));
            }
            
            // 调用百度云API扩容云硬盘
            bccClient.resizeVolume(resizeRequest);
            
            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("diskId", cloud.getString("diskId"));
            result.put("newSize", cloud.getInteger("newSize"));
            if (cloud.getString("newVolumeType") != null) {
                result.put("newVolumeType", cloud.getString("newVolumeType"));
            }
            
            log.info("扩容百度云云硬盘成功，磁盘ID: {}, 新大小: {}GB", 
                    cloud.getString("diskId"), cloud.getInteger("newSize"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("扩容百度云云硬盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("扩容云硬盘失败: " + e.getMessage()), e);
        }
    }
} 