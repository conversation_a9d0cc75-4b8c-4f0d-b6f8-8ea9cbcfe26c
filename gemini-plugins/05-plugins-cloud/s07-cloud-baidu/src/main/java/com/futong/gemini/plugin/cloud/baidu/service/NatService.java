package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.nat.NatClient;
import com.baidubce.services.nat.model.CreateNatRequest;
import com.baidubce.services.nat.model.ModifyNatRequest;
import com.baidubce.services.nat.model.ReleaseNatRequest;
import com.baidubce.services.nat.model.CreateNatResponse;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NatService {

    public static BaseResponse createNat(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云NAT网关");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "name", "vpcId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name, vpcId");
            }
            
            NatClient natClient = BaiduClient.client.client(NatClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            CreateNatRequest createRequest = cloud.toJavaObject(CreateNatRequest.class);
            CreateNatResponse response = natClient.createNat(createRequest);
            JSONObject result = new JSONObject();
            result.put("natId", response.getNatId());
            log.info("创建百度云NAT网关成功，NAT ID: {}", response.getNatId());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云NAT网关失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建NAT网关失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse updateNat(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云NAT网关");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "natId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：natId");
            }
            NatClient natClient = BaiduClient.client.client(NatClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            ModifyNatRequest modifyNatRequest = new ModifyNatRequest();
            modifyNatRequest.setNatId(cloud.getString("natId"));
            if (cloud.containsKey("name")) modifyNatRequest.setName(cloud.getString("name"));
            natClient.modifyNat(modifyNatRequest);
            JSONObject result = new JSONObject();
            result.put("natId", cloud.getString("natId"));
            log.info("修改百度云NAT网关成功，NAT ID: {}", cloud.getString("natId"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("修改百度云NAT网关失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改NAT网关失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse deleteNat(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云NAT网关");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "natId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：natId");
            }
            NatClient natClient = BaiduClient.client.client(NatClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            ReleaseNatRequest deleteRequest = new ReleaseNatRequest();
            deleteRequest.setNatId(cloud.getString("natId"));
            natClient.releaseNat(deleteRequest);
            JSONObject result = new JSONObject();
            result.put("natId", cloud.getString("natId"));
            log.info("删除百度云NAT网关成功，NAT ID: {}", cloud.getString("natId"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除百度云NAT网关失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除NAT网关失败: " + e.getMessage()), e);
        }
    }
} 