package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.model.snapshot.CreateSnapshotRequest;
import com.baidubce.services.bcc.model.volume.RollbackVolumeRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度云快照服务类
 * 
 * 提供快照管理功能，包括：
 * - 创建快照
 * - 删除快照
 * - 修改快照
 * - 回滚快照
 * 
 * 注意：当前百度云SDK暂不支持快照操作，后续如SDK支持可补充实现
 */
@Slf4j
public class SnapshotService {
    
    /**
     * 创建云硬盘快照
     * 
     * 使用百度云SDK创建快照，支持设置名称和描述
     * 支持参数：diskId, name, description
     */
    public static BaseResponse createSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云快照");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "diskId", "name")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskId, name");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 构造SDK请求对象
            CreateSnapshotRequest createSnapshotRequest = new CreateSnapshotRequest();
            createSnapshotRequest.setVolumeId(cloud.getString("diskId"));
            createSnapshotRequest.setSnapshotName(cloud.getString("name"));
            
            // 设置描述（可选）
            if (cloud.containsKey("description")) {
                createSnapshotRequest.setDesc(cloud.getString("description"));
            }
            
            // 调用SDK创建快照
            Object response = bccClient.createSnapshot(createSnapshotRequest);
            JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(response));
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            if (responseJson.containsKey("snapshotId")) {
                result.put("snapshotId", responseJson.getString("snapshotId"));
            }
            result.put("name", cloud.getString("name"));
            result.put("description", cloud.getString("description"));
            result.put("diskId", cloud.getString("diskId"));
            result.put("status", "creating");
            result.put("createTime", System.currentTimeMillis());
            
            log.info("创建百度云快照成功，快照ID: {}", result.getString("snapshotId"));
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建快照失败: " + e.getMessage()), e);
        }
    }
    
    /**
     * 删除云硬盘快照
     * 
     * 使用百度云SDK删除快照
     * 支持参数：snapshotId
     */
    public static BaseResponse deleteSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云快照");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "snapshotId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：snapshotId");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取快照ID
            String snapshotId = cloud.getString("snapshotId");
            
            // 调用SDK删除快照
            bccClient.deleteSnapshot(snapshotId);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("snapshotId", snapshotId);
            result.put("status", "deleted");
            result.put("deleteTime", System.currentTimeMillis());
            
            log.info("删除百度云快照成功，快照ID: {}", snapshotId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("删除百度云快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除快照失败: " + e.getMessage()), e);
        }
    }
    
    /**
     * 修改云硬盘快照
     * 
     * 支持参数：snapshotId, name, description
     */
    public static BaseResponse updateSnapshot(BaseCloudRequest request) {
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("当前百度云SDK暂不支持快照修改操作"));
    }
    
    /**
     * 回滚云硬盘快照
     * 
     * 使用百度云SDK回滚快照到指定磁盘
     * 支持参数：snapshotId, diskId
     */
    public static BaseResponse resetSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始回滚百度云快照");
            JSONObject cloud = request.getBody().getCloud();
            
            // 校验必需参数
            if (!CloudService.validateRequiredParams(cloud, "snapshotId", "diskId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：snapshotId, diskId");
            }
            
            // 获取BccClient实例
            BccClient bccClient = BaiduClient.client.client(BccClient.class, request.getBody());
            
            // 获取参数
            String snapshotId = cloud.getString("snapshotId");
            String diskId = cloud.getString("diskId");
            
            // 构造SDK请求对象
            RollbackVolumeRequest rollbackVolumeRequest = new RollbackVolumeRequest();
            rollbackVolumeRequest.setVolumeId(diskId);
            rollbackVolumeRequest.setSnapshotId(snapshotId);
            
            // 调用SDK回滚快照
            bccClient.rollbackVolume(rollbackVolumeRequest);
            
            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("snapshotId", snapshotId);
            result.put("diskId", diskId);
            result.put("status", "rollbacking");
            result.put("rollbackTime", System.currentTimeMillis());
            
            log.info("回滚百度云快照成功，快照ID: {}, 磁盘ID: {}", snapshotId, diskId);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("回滚百度云快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("回滚快照失败: " + e.getMessage()), e);
        }
    }
} 