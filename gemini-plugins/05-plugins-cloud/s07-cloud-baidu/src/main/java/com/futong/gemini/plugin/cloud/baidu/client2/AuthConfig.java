package com.futong.gemini.plugin.cloud.baidu.client2;

import lombok.Getter;

// 认证信息封装
@Getter
public class AuthConfig {
    private final String ak;
    private final String sk;
    private final String appId;

    private AuthConfig(Builder builder) {
        this.ak = builder.ak;
        this.sk = builder.sk;
        this.appId = builder.appId;
    }

    public static class Builder {
        private String ak;
        private String sk;
        private String appId;

        public Builder ak(String ak) {
            this.ak = ak;
            return this;
        }

        public Builder sk(String sk) {
            this.sk = sk;
            return this;
        }

        public Builder appId(String appId) {
            this.appId = appId;
            return this;
        }

        public AuthConfig build() {
            validate();
            return new AuthConfig(this);
        }

        private void validate() {
            if (ak == null || sk == null) {
                throw new IllegalArgumentException("Missing auth parameters");
            }
        }
    }

}
