package com.futong.gemini.plugin.cloud.baidu;

import com.futong.gemini.plugin.cloud.baidu.sampler.FetchService;
import com.futong.gemini.plugin.cloud.baidu.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.baidu.service.AccountService;
import com.futong.gemini.plugin.cloud.baidu.service.CloudService;
import com.futong.gemini.plugin.cloud.baidu.service.ComputeInstanceService;
import com.futong.gemini.plugin.cloud.baidu.service.DiskService;
import com.futong.gemini.plugin.cloud.baidu.service.ImageService;
import com.futong.gemini.plugin.cloud.baidu.service.PlatformService;
import com.futong.gemini.plugin.cloud.baidu.service.SecurityGroupService;
import com.futong.gemini.plugin.cloud.baidu.service.VpcService;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import com.futong.gemini.plugin.cloud.baidu.service.KeypairService;
import com.futong.gemini.plugin.cloud.baidu.service.SnapshotService;
import com.futong.gemini.plugin.cloud.baidu.service.SubnetService;
import com.futong.gemini.plugin.cloud.baidu.service.EipService;
import com.futong.gemini.plugin.cloud.baidu.service.BosService;
import com.futong.gemini.plugin.cloud.baidu.service.NatService;
import com.futong.gemini.plugin.cloud.baidu.service.RouteService;

public class BaiduRegister extends BaseCloudRegister {
    @Override
    public void load() {
        // 平台相关
        onAfterLoadPlatform();
        // 数据采集相关
        onAfterLoadFetch();
        // 计算资源相关
        onAfterLoadCompute();
        // 镜像相关注册
        onAfterLoadComputeImage();
        // 安全组相关注册
        onAfterLoadComputeSecurityGroup();
        // 密钥对相关注册
        onAfterLoadComputeKeypair();
        // 存储资源相关
        onAfterLoadStorageDisk();
        // 快照相关注册
        onAfterLoadStorageSnapshot();
        // VPC相关注册
        onAfterLoadNeutronVpc();
        // 子网相关注册
        onAfterLoadNeutronSubnet();
        // EIP相关注册
        onAfterLoadNeutronEip();
        // NAT相关注册
        onAfterLoadNeutronNat();
        // 路由表相关注册
        onAfterLoadNeutronRoute();
        // 对象存储相关注册
        onAfterLoadStorageBucket();
    }

    public void onAfterLoadPlatform() {
        // 平台账号相关服务
        registerBefore(CloudService::defaultRegion,
                ActionType.AUTH_PLATFORM_ACCOUNT,
                ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL,
                ActionType.CREATE_PLATFORM_FETCH_DISPATCH,
                ActionType.QUERY_PLATFORM_BILL_BALANCE);
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount); // 账号认证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm); // 获取账号表单
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel); // 获取调度模型
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch); // 创建调度任务
        register(ActionType.QUERY_PLATFORM_BILL_BALANCE, PlatformService::queryBillBalance); // 查询账单余额
    }

    public void onAfterLoadFetch() {
        // 采集注册
        // 地域
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion);

        // 可用区
        register(ActionType.FETCH_PLATFORM_AZONE, FetchService::fetchZone);

        // 密钥对 - 使用50条分页
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeyPair)
            .addBefore(CloudService::defaultPage50);

        // 告警 - 使用100条分页，包含时间范围
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm)
            .addBefore(CloudService::defaultStartEndTimeOneDay)
            .addBefore(CloudService::defaultPage100);
        
        // 事件 - 使用100条分页，包含时间范围
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent)
            .addBefore(CloudService::defaultStartEndTimeOneDay)
            .addBefore(CloudService::defaultPage100);
        
        // 云主机 - 使用100条分页
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchInstance)
            .addBefore(CloudService::defaultPage100);

        // 云主机监控 - 获取云主机监控数据
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsPerf)
            .addBefore(FetchService::defaultMetricRequest)
            .addBefore(FetchService::defaultEcsMetricNames)
            .addBefore(FetchService::defaultEcsMetricDimensions);

        // 云主机规格
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor);
        
        // 安全组 - 使用50条分页
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup)
            .addBefore(CloudService::defaultPage50);
        
        // 镜像 - 使用100条分页
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage)
            .addBefore(CloudService::defaultPage100);
        
        // 云硬盘 - 使用100条分页 1
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk)
            .addBefore(CloudService::defaultPage100);
        
        // 快照 - 使用50条分页
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot)
            .addBefore(CloudService::defaultPage50);
        
        // VPC （包含弹性网卡、路由表、NAT网关）- 使用50条分页
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc)
            .addBefore(CloudService::defaultPage50);
        
        // 子网 - 使用50条分页
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet)
            .addBefore(CloudService::defaultPage50);

        // 弹性网卡
        register(ActionType.FETCH_NEUTRON_NIC, FetchService::fetchNetworkInterface);
        
        // 弹性公网IP - 使用100条分页
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip)
            .addBefore(CloudService::defaultPage100);
        
        // 负载均衡 - 使用100条分页FetchNeutronLoadbalance
        register(ActionType.FETCH_NEUTRON_LOADBALANCE, FetchService::fetchLoadBalancer)
            .addBefore(CloudService::defaultPage100);
        
        // NAT网关 - 使用100条分页
        register(ActionType.FETCH_NEUTRON_NAT, FetchService::fetchNat)
            .addBefore(CloudService::defaultPage100);

        // SNAT网关规则
        register(ActionType.FETCH_NEUTRON_SNAT_ENTRY, FetchService::fetchSnatEntry)
                .addBefore(CloudService::defaultPage100);

        // DNAT网关规则
        register(ActionType.FETCH_NEUTRON_DNAT_ENTRY, FetchService::fetchDnatEntry)
                .addBefore(CloudService::defaultPage100);

        // 路由表
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRoute);

        // 对象存储-包含对象存储文件
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchBucket);

        // 对象存储文件
        register(ActionType.FETCH_STORAGE_BUCKET_FILE, FetchService::fetchBucketFile);
    }

    public void onAfterLoadCompute() {
        // 计算资源刷新
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshInstance) // 刷新云主机
                .addTransferCloud("$.refreshConfig.data", "$.instanceIds", BaseUtils::formatStrArray);
        
        // 创建云主机
        register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance) // 创建云主机
                .addBefore(CloudService::toBeforeBiz) // 添加业务处理
                .addAfter(CloudService::toAfterBizResId) // 发送业务MQ
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE) // 设置刷新请求，指定请求Action
                .addSetRefreshConfig(20, 5000, 20000) // 设置刷新总次数,刷新频次，首次刷新延迟
                .addSetRefreshSplitData("response","$.instanceId") // 批量创建基于响应分割刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求信息设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob); // 添加刷新任务
        
        // 批量删除云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance) // 批量删除云主机
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds") // 批量操作基于请求分割刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 修改云主机
        register(ActionType.UPDATE_COMPUTE_INSTANCE, ComputeInstanceService::updateInstance) // 修改云主机
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.resourceName", "$.name", false)
                .addTransferCloud("$.model.description", "$.desc", false)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                .addSetRefreshData("request","$.body.cloud.instanceId") // 单资源操作,直接设置刷新请求的data信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 查询可修改云主机规格
        register(ActionType.QUERY_UPDATE_COMPUTE_INSTANCE_FLAVOR, ComputeInstanceService::queryUpdateComputeInstanceFlavor) // 查询可修改云主机规格
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Value", "$.zoneId");

        // 修改云主机规格
        register(ActionType.UPDATE_COMPUTE_INSTANCE_FLAVOR, ComputeInstanceService::updateComputeInstanceFlavor) // 修改云主机规格
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 5000, 5000)
                .addSetRefreshData("request","$.body.cloud.instanceId")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 批量开启云主机
        register(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::startInstance) // 批量开启云主机
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 批量关闭云主机
        register(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::stopInstance) // 批量关闭云主机
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 批量重启云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::rebootInstance) // 批量重启云主机
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(30, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 查询云主机VNC登录地址
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::queryInstanceVncUrl) // 查询云主机VNC登录地址
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId");
        
        // 修改云主机VNC登录密码
        register(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, ComputeInstanceService::updateInstanceVncPassword) // 修改云主机VNC登录密码
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.model.vncPassword", "$.vncPassword");
    }

    // 镜像相关注册
    public void onAfterLoadComputeImage() {
        // 查询镜像
        register(ActionType.QUERY_COMPUTE_IMAGE, ImageService::queryImages); // 查询镜像
        
        // 创建自定义镜像
        register(ActionType.CREATE_COMPUTE_IMAGE, ImageService::createImage); // 创建自定义镜像
        
        // 批量删除镜像
        register(ActionType.DELETE_COMPUTE_IMAGE, ImageService::deleteImages); // 批量删除镜像
        
        // 复制镜像
        register(ActionType.COPY_COMPUTE_IMAGE, ImageService::copyImage) // 复制一个地域下的自定义镜像到其他地域
                .addTransferCloud("$.ci.openId", "$.imageId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.name", "$.name", false)
                .addTransferCloud("$.model.destRegion", "$.destRegion");

    }

    // 安全组相关注册
    public void onAfterLoadComputeSecurityGroup() {
        // 创建安全组
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP, SecurityGroupService::createSecurityGroup); // 创建安全组

        // 删除安全组
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP, SecurityGroupService::deleteSecurityGroup) // 删除安全组
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
        ;

        // 绑定安全组
        register(ActionType.BIND_COMPUTE_SECURITYGROUP, SecurityGroupService::bindSecurityGroup) // 绑定安全组
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.resourceId", "$.securityGroupId");
        
        // 解绑安全组
        register(ActionType.UNBIND_COMPUTE_SECURITYGROUP, SecurityGroupService::unbindSecurityGroup) // 解绑安全组
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.resourceId", "$.securityGroupId");

        // 创建安全组规则
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::createSecurityGroupRule) // 创建安全组规则
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.direction", "$.direction")
                .addTransferCloud("$.model.protocol", "$.protocol")
                .addTransferCloud("$.model.portRange", "$.portRange", false)
                .addTransferCloud("$.model.sourceIp", "$.sourceIp", false)
                .addTransferCloud("$.model.sourceGroupId", "$.sourceGroupId", false)
                .addTransferCloud("$.model.destIp", "$.destIp", false)
                .addTransferCloud("$.model.destGroupId", "$.destGroupId", false)
                .addTransferCloud("$.model.remark", "$.remark", false);

        // 删除安全组规则
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::deleteSecurityGroupRule) // 删除安全组规则
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.direction", "$.direction")
                .addTransferCloud("$.model.protocol", "$.protocol")
                .addTransferCloud("$.model.portRange", "$.portRange", false)
                .addTransferCloud("$.model.sourceIp", "$.sourceIp", false)
                .addTransferCloud("$.model.sourceGroupId", "$.sourceGroupId", false)
                .addTransferCloud("$.model.destIp", "$.destIp", false)
                .addTransferCloud("$.model.destGroupId", "$.destGroupId", false)
                .addTransferCloud("$.model.remark", "$.remark", false);

    }

    // 密钥对相关注册
    public void onAfterLoadComputeKeypair() {
        // 创建密钥对
        register(ActionType.CREATE_COMPUTE_KEYPAIR, KeypairService::createKeypair); // 创建密钥对

        // 删除密钥对
        register(ActionType.DELETE_COMPUTE_KEYPAIR, KeypairService::deleteKeypair) // 删除密钥对
                .addTransferCloud("$.ci.openId", "$.keypairId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);

        // 绑定密钥对
        register(ActionType.ATTACH_COMPUTE_KEYPAIR, KeypairService::bindKeypair) // 绑定密钥对
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.resourceId", "$.keypairId");

        // 解绑密钥对
        register(ActionType.DETACH_COMPUTE_KEYPAIR, KeypairService::unbindKeypair) // 解绑密钥对
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.resourceId", "$.keypairId");
    }

    // 云硬盘相关注册
    public void onAfterLoadStorageDisk() {
        // 创建云硬盘
        register(ActionType.CREATE_STORAGE_DISK, DiskService::createDisk) // 创建云硬盘
                .addBefore(CloudService::defaultRegion); // 默认地域设置
        
        // 修改云硬盘
        register(ActionType.UPDATE_STORAGE_DISK, DiskService::updateDisk) // 修改云硬盘
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.resourceName", "$.diskName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        
        // 批量删除云硬盘
        register(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisk); // 批量删除云硬盘
        
        // 挂载云硬盘
        register(ActionType.ATTACH_STORAGE_DISK, DiskService::attachDisk) // 挂载云硬盘
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        
        // 卸载云硬盘
        register(ActionType.DETACH_STORAGE_DISK, DiskService::detachDisk) // 卸载云硬盘
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        
        // 扩容云硬盘
        register(ActionType.RESIZE_STORAGE_DISK, DiskService::resizeDisk) // 扩容云硬盘
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.size", "$.newSize");
    }

    // 快照相关注册
    public void onAfterLoadStorageSnapshot() {
        // 创建云硬盘快照
        register(ActionType.CREATE_STORAGE_SNAPSHOT, SnapshotService::createSnapshot)
                .addTransferCloud("$.model.diskId", "$.diskId")
                .addTransferCloud("$.model.name", "$.name")
                .addTransferCloud("$.model.description", "$.description");

        // 批量删除云硬盘快照
        register(ActionType.DELETE_STORAGE_SNAPSHOT, SnapshotService::deleteSnapshot);

        // 回滚云硬盘快照
        register(ActionType.RESET_STORAGE_SNAPSHOT, SnapshotService::resetSnapshot)
                .addTransferCloud("$.ci.openId", "$.snapshotId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.diskId", "$.diskId");
    }

    // VPC相关注册
    public void onAfterLoadNeutronVpc() {
        // VPC刷新
        register(ActionType.REFRESH_NEUTRON_VPC, RefreshService::refreshVpc); // 刷新VPC

        // 创建VPC
        register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc) // 创建VPC
                .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshData("response","$.data.vpcId") // 将响应vpcId添加到刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 修改VPC
        register(ActionType.UPDATE_NEUTRON_VPC, VpcService::updateVpc) // 修改VPC
                .addTransferCloud("$.ci.openId", "$.vpcId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.vpcName", "$.name", false)
                .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
                .addSetRefreshConfig(0, 0, 1000)
                .addSetRefreshData("request","$.body.cloud.vpcId") // 单资源操作,直接设置刷新请求的data信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 删除VPC
        register(ActionType.DELETE_NEUTRON_VPC, VpcService::deleteVpc) // 删除VPC
                .addTransferCloud("$.ci.openId", "$.vpcId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.vpcIds") // 批量操作基于请求分割刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
    }

    // 子网相关注册
    public void onAfterLoadNeutronSubnet() {
        // 网络资源刷新
        register(ActionType.REFRESH_NEUTRON_SUBNET, RefreshService::refreshSubnet); // 刷新子网

        // 创建子网
        register(ActionType.CREATE_NEUTRON_SUBNET, SubnetService::createSubnet) // 创建子网
                .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshData("response","$.data.subnetId") // 将响应subnetId添加到刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 修改子网
        register(ActionType.UPDATE_NEUTRON_SUBNET, SubnetService::updateSubnet) // 修改子网
                .addTransferCloud("$.ci.openId", "$.subnetId")
                .addTransferCloud("$.model.subnetName", "$.name", false)
                .addTransferCloud("$.model.description", "$.description", false)
                .addTransferCloud("$.model.enableIpv6", "$.enableIpv6", false)
                .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
                .addSetRefreshConfig(0, 0, 1000)
                .addSetRefreshData("request","$.body.cloud.subnetId") // 单资源操作,直接设置刷新请求的data信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        
        // 删除子网
        register(ActionType.DELETE_NEUTRON_SUBNET, SubnetService::deleteSubnet) // 删除子网
                .addTransferCloud("$.ci.openId", "$.subnetId")
                .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.subnetIds") // 批量操作基于请求分割刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
    }

    // EIP相关注册
    public void onAfterLoadNeutronEip() {
        // 创建EIP
        register(ActionType.CREATE_NEUTRON_EIP, EipService::createEip); // 创建EIP
        
        // 修改EIP
        register(ActionType.UPDATE_NEUTRON_EIP, EipService::updateEip) // 修改EIP
                .addTransferCloud("$.ci.eip", "$.eip")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.newBandwidthInMbps", "$.newBandwidthInMbps", false);
        
        // 删除EIP
        register(ActionType.DELETE_NEUTRON_EIP, EipService::deleteEip) // 删除EIP
                .addTransferCloud("$.ci.eip", "$.eip")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        
        // 绑定EIP
        register(ActionType.BIND_NEUTRON_EIP, EipService::bindEip) // 绑定EIP
                .addTransferCloud("$.ci.eip", "$.eip")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.instanceType", "$.instanceType")
                .addTransferCloud("$.model.instanceId", "$.instanceId")
                .addTransferCloud("$.model.instanceIp", "$.instanceIp",false);
        
        // 解绑EIP
        register(ActionType.UNBIND_NEUTRON_EIP, EipService::unbindEip) // 解绑EIP
                .addTransferCloud("$.ci.eip", "$.eip")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
    }

    // NAT相关注册
    public void onAfterLoadNeutronNat() {
        // 创建NAT
        register(ActionType.CREATE_NEUTRON_NAT, NatService::createNat);

        // 修改NAT
        register(ActionType.UPDATE_NEUTRON_NAT, NatService::updateNat)
                .addTransferCloud("$.ci.openId", "$.natId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.name", "$.name");

        // 删除NAT
        register(ActionType.DELETE_NEUTRON_NAT, NatService::deleteNat)
                .addTransferCloud("$.ci.openId", "$.natId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
    }

    // 路由表相关注册
    public void onAfterLoadNeutronRoute() {
        // 分页查询路由规则
        register(ActionType.QUERY_NEUTRON_ROUTE_RULE, RouteService::queryRouteRules);
        
        // 创建路由表规则
        register(ActionType.CREATE_NEUTRON_ROUTE, RouteService::createRoute);
        
        // 修改路由表规则
        register(ActionType.UPDATE_NEUTRON_ROUTE, RouteService::updateRoute)
                .addTransferCloud("$.ci.openId", "$.routeRuleId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.model.sourceAddress", "$.sourceAddress", false)
                .addTransferCloud("$.model.destinationAddress", "$.destinationAddress", false)
                .addTransferCloud("$.model.nexthopId", "$.nexthopId", false)
                .addTransferCloud("$.model.description", "$.description", false);
        
        // 删除路由表规则
        register(ActionType.DELETE_NEUTRON_ROUTE, RouteService::deleteRoute)
                .addTransferCloud("$.ci.openId", "$.routeRuleId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        
        // 路由主备切换
        register(ActionType.BIND_NEUTRON_ROUTE, RouteService::switchRoute)
                .addTransferCloud("$.ci.openId", "$.routeRuleId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
    }

    /**
     * 注册百度云对象存储(BOS)相关操作
     * 
     * 该方法负责注册百度云对象存储服务的各种操作，包括：
     * - 存储桶的创建和删除
     * - 文件夹的创建和删除  
     * - 文件的上传、下载、查询和删除
     * 
     * 所有操作都通过BosService服务类实现具体的业务逻辑
     */
    public void onAfterLoadStorageBucket() {
        // 创建对象存储桶
        register(ActionType.CREATE_OSS_STORAGE, BosService::createBucket);
        
        // 删除对象存储桶
        register(ActionType.DELETE_OSS_STORAGE, BosService::deleteBucket)
                .addTransferCloud("$.ci.bucketName", "$.bucketName")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        
        // 在存储桶中创建文件夹
        register(ActionType.CREATE_OSS_STORAGE_BUCKET_FOLDER, BosService::createFolder);
        
        // 删除存储桶中的文件夹
        register(ActionType.DELETE_OSS_STORAGE_BUCKET_FOLDER, BosService::deleteFolder)
                .addTransferCloud("$.ci.bucketName", "$.bucketName")
                .addTransferCloud("$.ci.folderName", "$.folderName")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        
        // 上传文件到存储桶
        register(ActionType.UPLOAD_OSS_STORAGE_BUCKET_FILE, BosService::uploadFile);
        
        // 从存储桶下载文件
        register(ActionType.DOWNLOAD_OSS_STORAGE_BUCKET_FILE, BosService::downloadFile)
                .addTransferCloud("$.ci.bucketName", "$.bucketName")
                .addTransferCloud("$.ci.objectKey", "$.objectKey")
                .addTransferCloud("$.ci.localPath", "$.localPath")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        
        // 删除对象存储桶文件
        register(ActionType.DELETE_OSS_STORAGE_BUCKET_FILE, BosService::deleteFile)
                .addTransferCloud("$.ci.bucketName", "$.bucketName")
                .addTransferCloud("$.ci.objectKey", "$.objectKey")
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);

        // 查询对象存储桶文件列表
        register(ActionType.QUERY_OSS_STORAGE_BUCKET_FILE, BosService::queryFiles)
                .addTransferCloud("$.ci.bucketName", "$.bucketName")
                .addTransferCloud("$.model.prefix", "$.prefix", false)
                .addTransferCloud("$.model.marker", "$.marker", false)
                .addTransferCloud("$.model.maxKeys", "$.maxKeys", false);
    }
} 