package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import cn.hutool.extra.spring.SpringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 百度云账号服务类
 * 负责账号认证、配置管理等功能
 */
@Slf4j
public class AccountService extends BaseCloudService {
    
    // 存储账号配置表单
    public static final Map<Locale, JSONObject> accountForm = new HashMap<>();
    
    // 存储账号调度配置
    public static String accountDispatch;

    /**
     * 认证百度云账号
     */
    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            log.info("开始认证百度云账号");
            // 获取账号配置
            String accessKeyId = getAccessKeyId(request);
            String secretAccessKey = getSecretAccessKey(request);
            String region = getRegion(request);
            String endpoint = getEndpoint(request);
            
            // 验证必要参数
            if (accessKeyId == null || accessKeyId.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "Access Key ID不能为空");
            }
            if (secretAccessKey == null || secretAccessKey.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "Secret Access Key不能为空");
            }
            if (region == null || region.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "地域不能为空");
            }
            
            // 创建客户端并验证连接
            BaiduClient client = new BaiduClient(accessKeyId, secretAccessKey, region, endpoint);
            
            if (!client.validateConnection(request)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, "账号认证失败，请检查Access Key和Secret Key是否正确");
            }
            
            log.info("百度云账号认证成功");
            return BaseResponse.SUCCESS;
            
        } catch (BaseException e) {
            log.error("百度云账号认证失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("百度云账号认证异常", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    /**
     * 获取账号配置表单
     */
    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }

    /**
     * 获取调度添加模型
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        try {
            // 1. 替换调度模板中的${cmpId}
            String text = accountDispatch;
            if (text == null || text.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "调度配置未加载");
            }
            text = text.replace("${cmpId}", request.getBody().getAccess().getCmpId());
            JSONObject result = JSON.parseObject(text);

            // 2. 查询百度云所有地域
            List<Map<String, String>> regions = BaiduClient.getAllRegions();

            // 3. 判断是否全量调度
            if (request.getBody().containsKey("all")) {
                // 生成所有地域的调度任务
                List<JSONObject> dispatchers = new ArrayList<>();
                result.getJSONArray("data").forEach(item -> {
                    JSONObject itemObj = (JSONObject) item;
                    if (itemObj.getBoolean("unique")) {
                        dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
                    } else {
                        String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                        for (Map<String, String> region : regions) {
                            String itemStrRegion = itemStr.replace("${region.label}", region.get("label"))
                                                         .replace("${region.value}", region.get("value"));
                            dispatchers.add(JSON.parseObject(itemStrRegion));
                        }
                    }
                });
                return new BaseDataResponse<>(dispatchers);
            } else {
                // 4. 填充地域下拉选项到表单
                if (result.containsKey("form") && result.getJSONObject("form").containsKey("region")) {
                    result.getJSONObject("form").getJSONObject("region").put("items", regions);
                }
                return new BaseDataResponse<>(result);
            }
        } catch (Exception e) {
            log.error("获取调度添加模型失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e);
        }
    }

    /**
     * 创建调度任务
     */
    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        try {
            //调用gourd服务-添加调度层级，新增云类型层级
            GourdUtils.addGourdLevel(request.getPlugin().getRealm(), null, "云调度-百度");
            // 1. 调用gourd服务-添加调度层级，新加调度层级id=cmpId
            GourdProxy gourdProxy = SpringUtil.getBean(GourdProxy.class);
            GourdUtils.addGourdLevel(request);
            //删除旧得调度任务
            JSONObject oldWhere = new JSONObject();
            oldWhere.put("jobLevel", request.getBody().getAccess().getCmpId());
            BaseResponse response = gourdProxy.stopAndDeleteDispatcher(oldWhere);
            if (BaseResponse.SUCCESS.equals(response)) {
                log.info("删除旧得调度任务成功!");
            } else {
                log.error("删除旧得调度任务失败{}!", JSON.toJSONString(response));
                return BaseResponse.ERROR_BIZ.of("删除旧得调度任务失败");
            }
            // 2. 替换占位符${cmpId}=access.cmpId
            if (accountDispatch == null || accountDispatch.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "调度配置未加载");
            }
            String text = accountDispatch.replace("${cmpId}", request.getBody().getAccess().getCmpId());
            if(null != request.getBody().getAccess().getJsonStr()){
                JSONObject json = JSONObject.parseObject(request.getBody().getAccess().getJsonStr());
                text = text.replace("${cloudUserId}", json.getString("cloudUserId"));
            }
            JSONObject result = JSON.parseObject(text);
            
            // 3. 查询百度云所有地域
            List<Map<String, String>> regions = BaiduClient.getAllRegions();
            
            // 4. 根据地域生成全量调度任务
            List<JSONObject> dispatchers = new ArrayList<>();
            result.getJSONArray("data").forEach(item -> {
                JSONObject itemObj = (JSONObject) item;
                if (itemObj.getBoolean("unique")) {
                    dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
                } else {
                    String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                    for (Map<String, String> region : regions) {
                        String itemStrRegion = itemStr.replace("${region.label}", region.get("label"))
                                                     .replace("${region.value}", region.get("value"));
                        dispatchers.add(JSON.parseObject(itemStrRegion));
                    }
                }
            });
            
            // 5. 调用gourd服务-批量添加调度任务
            return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
            
        } catch (Exception e) {
            log.error("创建调度任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e);
        }
    }

    /**
     * 获取Access Key ID
     */
    private static String getAccessKeyId(BaseCloudRequest request) {
        return request.getBody().getAccess().getUsername();
    }

    /**
     * 获取Secret Access Key
     */
    private static String getSecretAccessKey(BaseCloudRequest request) {
        return request.getBody().getAccess().getPassword();
    }

    /**
     * 获取地域
     */
    private static String getRegion(BaseCloudRequest request) {
        return request.getBody().getCloud().getString("regionId");
    }

    /**
     * 获取端点
     */
    private static String getEndpoint(BaseCloudRequest request) {
        return request.getBody().getCloud().getString("endpoint");
    }



    /**
     * 验证账号配置
     */
    public static boolean validateAccountConfig(BaseCloudRequest request) {
        try {
            String accessKeyId = getAccessKeyId(request);
            String secretAccessKey = getSecretAccessKey(request);
            String region = getRegion(request);
            
            return accessKeyId != null && !accessKeyId.trim().isEmpty() &&
                   secretAccessKey != null && !secretAccessKey.trim().isEmpty() &&
                   region != null && !region.trim().isEmpty();
        } catch (Exception e) {
            log.error("验证账号配置失败", e);
            return false;
        }
    }

    /**
     * 获取账号状态
     */
    public static BaseResponse getAccountStatus(BaseCloudRequest request) {
        try {
            if (!validateAccountConfig(request)) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "账号配置不完整");
            }
            
            // 尝试创建客户端验证连接
            String accessKeyId = getAccessKeyId(request);
            String secretAccessKey = getSecretAccessKey(request);
            String region = getRegion(request);
            String endpoint = getEndpoint(request);
            
            BaiduClient client = new BaiduClient(accessKeyId, secretAccessKey, region, endpoint);
            
            JSONObject status = new JSONObject();
            status.put("status", "active");
            status.put("lastCheckTime", System.currentTimeMillis());
            status.put("region", region);
            status.put("endpoint", endpoint);
            
            return new BaseDataResponse<>(status);
            
        } catch (Exception e) {
            log.error("获取账号状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e);
        }
    }

    /**
     * 更新账号配置
     */
    public static BaseResponse updateAccountConfig(BaseCloudRequest request) {
        try {
            if (!validateAccountConfig(request)) {
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "账号配置不完整");
            }
            
            // 验证新配置的有效性
            String accessKeyId = getAccessKeyId(request);
            String secretAccessKey = getSecretAccessKey(request);
            String region = getRegion(request);
            String endpoint = getEndpoint(request);
            
            BaiduClient client = new BaiduClient(accessKeyId, secretAccessKey, region, endpoint);
            
            if (!client.validateConnection(request)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, "新配置验证失败，请检查Access Key和Secret Key是否正确");
            }
            
            JSONObject result = new JSONObject();
            result.put("message", "账号配置更新成功");
            result.put("updateTime", System.currentTimeMillis());
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("更新账号配置失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e);
        }
    }
} 