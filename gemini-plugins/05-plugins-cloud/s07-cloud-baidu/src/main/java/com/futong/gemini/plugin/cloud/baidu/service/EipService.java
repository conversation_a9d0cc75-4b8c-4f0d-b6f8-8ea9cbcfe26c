package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.eip.EipClient;
import com.baidubce.services.eip.model.CreateEipRequest;
import com.baidubce.services.eip.model.ResizeEipRequest;
import com.baidubce.services.eip.model.ReleaseEipRequest;
import com.baidubce.services.eip.model.BindEipRequest;
import com.baidubce.services.eip.model.UnbindEipRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度云EIP服务类
 * 负责EIP的管理操作
 * 
 * 主要功能：
 * - EIP创建、删除、修改
 * - EIP绑定、解绑
 * - EIP属性查询
 * 
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 * 使用百度云官方SDK进行API调用
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class EipService {

    /**
     * 创建EIP
     * 根据CreateEipRequest参数创建百度云EIP
     * 
     * 参数说明：
     * - ipVersion: EIP IP类型，包含ipv4和ipv6，默认ipv4
     * - routeType: EIP线路类型，包含标准BGP（BGP）和增强BGP（BGP_S），默认标准BGP
     * - bandwidthInMbps: 公网带宽，单位为Mbps（必需）
     * - billing: 订单信息（必需）
     * - name: 长度1~65个字节，字母开头，可包含字母数字-_/.字符
     * - tags: 待创建的标签键值对列表
     * - resourceGroupId: 创建EIP的同时绑定的资源分组的ID
     * - autoRenewTimeUnit: 自动续费时间单位，取值为month或year（默认month）
     * - autoRenewTime: 自动续费时间，month为1到9，year为1到3
     * - deleteProtect: 是否开启释放保护，缺省值为false
     */
    public static BaseResponse createEip(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云EIP");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "bandwidthInMbps", "billing")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bandwidthInMbps, billing");
            }
            
            // 获取必需参数
            Integer bandwidthInMbps = cloud.getInteger("bandwidthInMbps");
            JSONObject billing = cloud.getJSONObject("billing");
            
            // 验证带宽范围
            if (bandwidthInMbps < 1 || bandwidthInMbps > 5000) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("带宽必须在1-5000Mbps之间");
            }
            
            // 获取可选参数
            String ipVersion = cloud.getString("ipVersion");
            String routeType = cloud.getString("routeType");
            String name = cloud.getString("name");
            String resourceGroupId = cloud.getString("resourceGroupId");
            String autoRenewTimeUnit = cloud.getString("autoRenewTimeUnit");
            Integer autoRenewTime = cloud.getInteger("autoRenewTime");
            Boolean deleteProtect = cloud.getBoolean("deleteProtect");
            
            // 验证名称长度
            if (name != null && (name.length() < 1 || name.length() > 65)) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("名称长度必须在1-65个字节之间");
            }
            
            // 验证名称格式（字母开头）
            if (name != null && !name.matches("^[a-zA-Z][a-zA-Z0-9\\-_/.]*$")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("名称必须以字母开头，可包含字母数字-_/.字符");
            }
            
            // 验证自动续费参数
            if (autoRenewTimeUnit != null && !autoRenewTimeUnit.matches("^(month|year)$")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("自动续费时间单位必须为month或year");
            }
            
            if (autoRenewTime != null) {
                if ("month".equals(autoRenewTimeUnit) && (autoRenewTime < 1 || autoRenewTime > 9)) {
                    return BaseResponse.FAIL_PARAM_EMPTY.of("月续费时间必须在1-9之间");
                }
                if ("year".equals(autoRenewTimeUnit) && (autoRenewTime < 1 || autoRenewTime > 3)) {
                    return BaseResponse.FAIL_PARAM_EMPTY.of("年续费时间必须在1-3之间");
                }
            }
            
            // 创建EipClient
            EipClient eipClient = BaiduClient.client.client(EipClient.class, request.getBody());
            
            // 构建CreateEipRequest
            CreateEipRequest createEipRequest = new CreateEipRequest();
            createEipRequest.setBandwidthInMbps(bandwidthInMbps);
            createEipRequest.setBilling(billing.toJavaObject(com.baidubce.services.eip.model.Billing.class));
            
            // 调用SDK创建EIP
            Object response = eipClient.createEip(createEipRequest);
            
            // 解析响应结果
            JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(response));
            JSONObject result = new JSONObject();
            String eipId = null;
            
            // 从响应中获取EIP ID
            if (responseJson.containsKey("eipId")) {
                eipId = responseJson.getString("eipId");
                result.put("eipId", eipId);
            } else if (responseJson.containsKey("id")) {
                eipId = responseJson.getString("id");
                result.put("eipId", eipId);
            } else {
                // 如果响应中没有EIP ID，生成临时ID
                eipId = "eip-" + System.currentTimeMillis();
                result.put("eipId", eipId);
                log.warn("响应中未找到EIP ID，使用临时ID: {}", eipId);
            }
            
            result.put("bandwidthInMbps", bandwidthInMbps);
            result.put("ipVersion", ipVersion != null ? ipVersion : "ipv4");
            result.put("routeType", routeType != null ? routeType : "BGP");
            
            if (name != null) {
                result.put("name", name);
            }
            if (resourceGroupId != null) {
                result.put("resourceGroupId", resourceGroupId);
            }
            if (deleteProtect != null) {
                result.put("deleteProtect", deleteProtect);
            }
            
            log.info("创建百度云EIP成功，EIP ID: {}, 带宽: {}Mbps", eipId, bandwidthInMbps);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云EIP失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建EIP失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改EIP属性
     * 根据ResizeEipRequest参数更新百度云EIP带宽
     * 
     * 参数说明：
     * - eip: 被更新的EIP，包括IPv4 EIP和IPv6 EIP（必需）
     * - newBandwidthInMbps: 公网带宽，单位为Mbps（必需）
     *   - 对于预付费(prepay)以及按带宽(bandwidth)类型的EIP，限制为1~200之间的整数
     *   - 对于按流量(traffic)类型的EIP，限制为1~1000之间的整数
     */
    public static BaseResponse updateEip(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云EIP属性");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "eip", "newBandwidthInMbps")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：eip, newBandwidthInMbps");
            }
            
            // 获取必需参数
            String eip = cloud.getString("eipId");
            Integer newBandwidthInMbps = cloud.getInteger("newBandwidthInMbps");
            
            // 验证带宽范围（按流量类型，支持更大范围）
            if (newBandwidthInMbps < 1 || newBandwidthInMbps > 1000) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("带宽必须在1-1000Mbps之间");
            }

            // 创建EipClient
            EipClient eipClient = BaiduClient.client.client(EipClient.class, request.getBody());
            
            // 构建ResizeEipRequest
            ResizeEipRequest resizeEipRequest = new ResizeEipRequest();
            resizeEipRequest.setEip(eip);
            resizeEipRequest.setNewBandwidthInMbps(newBandwidthInMbps);
            
            // 调用SDK修改EIP带宽
            eipClient.resizeEip(resizeEipRequest);
            
            log.info("修改百度云EIP属性成功，EIP: {}, 新带宽: {}Mbps", eip, newBandwidthInMbps);
            return BaseResponse.SUCCESS.of("修改EIP属性成功");
            
        } catch (Exception e) {
            log.error("修改百度云EIP属性失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改EIP属性失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除EIP
     * 根据ReleaseEipRequest参数释放百度云EIP
     * 
     * 参数说明：
     * - eip: 被释放的EIP，包括IPv4 EIP和IPv6 EIP（必需）
     */
    public static BaseResponse deleteEip(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云EIP");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "eip")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：eip");
            }
            
            // 获取EIP地址
            String eip = cloud.getString("eip");
            
            // 创建EipClient
            EipClient eipClient = BaiduClient.client.client(EipClient.class, request.getBody());
            
            // 构建ReleaseEipRequest
            ReleaseEipRequest releaseEipRequest = new ReleaseEipRequest();
            releaseEipRequest.setEip(eip);
            
            // 调用SDK释放EIP
            eipClient.releaseEip(releaseEipRequest);
            
            log.info("删除百度云EIP成功，EIP: {}", eip);
            return BaseResponse.SUCCESS.of("删除EIP成功");
            
        } catch (Exception e) {
            log.error("删除百度云EIP失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除EIP失败: " + e.getMessage()), e);
        }
    }

    /**
     * 绑定EIP
     * 根据BindEipRequest参数绑定百度云EIP到实例
     * 
     * 参数说明：
     * - eip: 被绑定的EIP，包括IPv4 EIP和IPv6 EIP（必需）
     * - instanceType: 被绑定的实例类型（必需）
     * - instanceId: 被绑定的实例ID（必需）
     * - instanceIp: 实例中需要绑定EIP的IP（可选）
     */
    public static BaseResponse bindEip(BaseCloudRequest request) {
        try {
            log.info("开始绑定百度云EIP");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "eip", "instanceType", "instanceId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：eip, instanceType, instanceId");
            }
            
            // 获取必需参数
            String eip = cloud.getString("eip");
            String instanceType = cloud.getString("instanceType");
            String instanceId = cloud.getString("instanceId");
            
            // 获取可选参数
            String instanceIp = cloud.getString("instanceIp");
            
            // 创建EipClient
            EipClient eipClient = BaiduClient.client.client(EipClient.class, request.getBody());
            
            // 构建BindEipRequest
            BindEipRequest bindEipRequest = new BindEipRequest();
            bindEipRequest.setEip(eip);
            bindEipRequest.setInstanceType(instanceType);
            bindEipRequest.setInstanceId(instanceId);
            
            if (instanceIp != null) {
                bindEipRequest.setInstanceIp(instanceIp);
            }
            
            // 调用SDK绑定EIP
            eipClient.bindEip(bindEipRequest);
            
            log.info("绑定百度云EIP成功，EIP: {}, 实例类型: {}, 实例ID: {}", eip, instanceType, instanceId);
            return BaseResponse.SUCCESS.of("绑定EIP成功");
            
        } catch (Exception e) {
            log.error("绑定百度云EIP失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定EIP失败: " + e.getMessage()), e);
        }
    }

    /**
     * 解绑EIP
     * 根据UnbindEipRequest参数解绑百度云EIP
     * 
     * 参数说明：
     * - eip: 被解绑的EIP，包括IPv4 EIP和IPv6 EIP（必需）
     */
    public static BaseResponse unbindEip(BaseCloudRequest request) {
        try {
            log.info("开始解绑百度云EIP");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "eip")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：eip");
            }
            
            // 获取EIP地址
            String eip = cloud.getString("eip");
            
            // 创建EipClient
            EipClient eipClient = BaiduClient.client.client(EipClient.class, request.getBody());
            
            // 构建UnbindEipRequest
            UnbindEipRequest unbindEipRequest = new UnbindEipRequest();
            unbindEipRequest.setEip(eip);
            
            // 调用SDK解绑EIP
            eipClient.unbindEip(unbindEipRequest);
            
            log.info("解绑百度云EIP成功，EIP: {}", eip);
            return BaseResponse.SUCCESS.of("解绑EIP成功");
            
        } catch (Exception e) {
            log.error("解绑百度云EIP失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑EIP失败: " + e.getMessage()), e);
        }
    }

} 