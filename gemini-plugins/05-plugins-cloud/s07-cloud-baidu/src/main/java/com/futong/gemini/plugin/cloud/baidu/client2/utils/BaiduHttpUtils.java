//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.futong.gemini.plugin.cloud.baidu.client2.utils;

import com.baidubce.Protocol;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import org.apache.http.Header;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpRequestBase;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.BitSet;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class BaiduHttpUtils {
    private static final String DEFAULT_ENCODING = "UTF-8";
    private static BitSet URI_UNRESERVED_CHARACTERS = new BitSet();
    private static String[] PERCENT_ENCODED_STRINGS = new String[256];
    private static final Joiner queryStringJoiner = Joiner.on('&');
    private static boolean HTTP_VERBOSE = Boolean.parseBoolean(System.getProperty("bce.sdk.http", "false"));

    public BaiduHttpUtils() {
    }

    public static String normalizePath(String path) {
        return normalize(path).replace("%2F", "/");
    }

    public static String normalize(String value) {
        try {
            StringBuilder builder = new StringBuilder();

            for (byte b : value.getBytes("UTF-8")) {
                if (URI_UNRESERVED_CHARACTERS.get(b & 255)) {
                    builder.append((char) b);
                } else {
                    builder.append(PERCENT_ENCODED_STRINGS[b & 255]);
                }
            }

            return builder.toString();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String generateHostHeader(URI uri) {
        String host = uri.getHost();
        if (isUsingNonDefaultPort(uri)) {
            host = host + ":" + uri.getPort();
        }

        return host;
    }

    public static boolean isUsingNonDefaultPort(URI uri) {
        String scheme = uri.getScheme().toLowerCase();
        int port = uri.getPort();
        if (port <= 0) {
            return false;
        } else if (scheme.equals("http")) {
            return port != Protocol.HTTP.getDefaultPort();
        } else if (scheme.equals(Protocol.HTTPS.toString())) {
            return port != Protocol.HTTPS.getDefaultPort();
        } else {
            return false;
        }
    }

    public static String getCanonicalQueryString(Map<String, String> parameters, boolean forSignature) {
        if (parameters == null || parameters.isEmpty()) {
            return "";
        } else {
            List<String> parameterStrings = Lists.newArrayList();

            for (Map.Entry<String, String> entry : parameters.entrySet()) {
                if (!forSignature || !"Authorization".equalsIgnoreCase((String) entry.getKey())) {
                    String key = (String) entry.getKey();
                    Preconditions.checkNotNull(key, "parameter key should not be null");
                    String value = (String) entry.getValue();
                    if (value == null) {
                        if (forSignature) {
                            parameterStrings.add(normalize(key) + '=');
                        } else {
                            parameterStrings.add(normalize(key));
                        }
                    } else {
                        parameterStrings.add(normalize(key) + '=' + normalize(value));
                    }
                }
            }

            Collections.sort(parameterStrings);
            return queryStringJoiner.join(parameterStrings);
        }
    }

    public static URI appendUri(URI baseUri, String... pathComponents) {
        StringBuilder builder = new StringBuilder(baseUri.toASCIIString());

        for (String path : pathComponents) {
            if (path != null && path.length() > 0) {
                path = normalizePath(path);
                if (path.startsWith("/")) {
                    if (builder.charAt(builder.length() - 1) == '/') {
                        builder.setLength(builder.length() - 1);
                    }
                } else if (builder.charAt(builder.length() - 1) != '/') {
                    builder.append('/');
                }

                builder.append(path);
            }
        }

        try {
            return new URI(builder.toString());
        } catch (URISyntaxException e) {
            throw new RuntimeException("Unexpected error", e);
        }
    }

    public static void printRequest(HttpRequestBase request) {
        if (HTTP_VERBOSE) {
            System.out.println("\n-------------> ");
            System.out.println(request.getRequestLine());

            for (Header h : request.getAllHeaders()) {
                System.out.println(h.getName() + " : " + h.getValue());
            }

            RequestConfig config = request.getConfig();
            if (config != null) {
                System.out.println("getConnectionRequestTimeout: " + config.getConnectionRequestTimeout());
                System.out.println("getConnectTimeout: " + config.getConnectTimeout());
                System.out.println("getCookieSpec: " + config.getCookieSpec());
                System.out.println("getLocalAddress: " + config.getLocalAddress());
            }

        }
    }

    public static void printResponse(CloseableHttpResponse response) {
        if (HTTP_VERBOSE) {
            System.out.println("\n<------------- ");
            StatusLine status = response.getStatusLine();
            System.out.println(status.getStatusCode() + " - " + status.getReasonPhrase());
            Header[] heads = response.getAllHeaders();

            for (Header h : heads) {
                System.out.println(h.getName() + " : " + h.getValue());
            }

        }
    }

    static {
        for (int i = 97; i <= 122; ++i) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }

        for (int i = 65; i <= 90; ++i) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }

        for (int i = 48; i <= 57; ++i) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }

        URI_UNRESERVED_CHARACTERS.set(45);
        URI_UNRESERVED_CHARACTERS.set(46);
        URI_UNRESERVED_CHARACTERS.set(95);
        URI_UNRESERVED_CHARACTERS.set(126);

        for (int i = 0; i < PERCENT_ENCODED_STRINGS.length; ++i) {
            PERCENT_ENCODED_STRINGS[i] = String.format("%%%02X", i);
        }

    }
}
