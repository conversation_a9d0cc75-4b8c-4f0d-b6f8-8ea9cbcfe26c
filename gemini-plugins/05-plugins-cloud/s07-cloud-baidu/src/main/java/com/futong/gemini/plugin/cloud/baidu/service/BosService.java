package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.model.*;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;
import java.io.File;
import java.util.List;

@Slf4j
public class BosService {

    public static BaseResponse createBucket(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云存储桶");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            CreateBucketRequest createRequest = new CreateBucketRequest(cloud.getString("bucketName"));
            bosClient.createBucket(createRequest);
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            log.info("创建百度云存储桶成功，桶名称: {}", cloud.getString("bucketName"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("创建百度云存储桶失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建存储桶失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse deleteBucket(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云存储桶");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            bosClient.deleteBucket(cloud.getString("bucketName"));
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            log.info("删除百度云存储桶成功，桶名称: {}", cloud.getString("bucketName"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除百度云存储桶失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除存储桶失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse createFolder(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云存储文件夹");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName", "folderName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName, folderName");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String key = cloud.getString("folderName") + "/";
            bosClient.putObject(cloud.getString("bucketName"), key, "");
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            result.put("folderName", cloud.getString("folderName"));
            log.info("创建百度云存储文件夹成功，桶名称: {}, 文件夹名称: {}", cloud.getString("bucketName"), cloud.getString("folderName"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("创建百度云存储文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建文件夹失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse deleteFolder(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云存储文件夹");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName", "folderName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName, folderName");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String key = cloud.getString("folderName") + "/";
            bosClient.deleteObject(cloud.getString("bucketName"), key);
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            result.put("folderName", cloud.getString("folderName"));
            log.info("删除百度云存储文件夹成功，桶名称: {}, 文件夹名称: {}", cloud.getString("bucketName"), cloud.getString("folderName"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除百度云存储文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件夹失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse uploadFile(BaseCloudRequest request) {
        try {
            log.info("开始上传文件到百度云存储");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName", "objectKey", "filePath")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName, objectKey, filePath");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            PutObjectResponse response = bosClient.putObject(cloud.getString("bucketName"), cloud.getString("objectKey"), new File(cloud.getString("filePath")));
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            result.put("objectKey", cloud.getString("objectKey"));
            result.put("eTag", response.getETag());
            log.info("上传文件到百度云存储成功，桶名称: {}, 对象键: {}", cloud.getString("bucketName"), cloud.getString("objectKey"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("上传文件到百度云存储失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("上传文件失败: " + e.getMessage()), e);
        }
    }

    public static BaseResponse downloadFile(BaseCloudRequest request) {
        try {
            log.info("开始从百度云存储下载文件");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName", "objectKey", "localPath")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName, objectKey, localPath");
            }
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            bosClient.getObject(new GetObjectRequest(cloud.getString("bucketName"), cloud.getString("objectKey")), new File(cloud.getString("localPath")));
            JSONObject result = new JSONObject();
            result.put("bucketName", cloud.getString("bucketName"));
            result.put("objectKey", cloud.getString("objectKey"));
            result.put("localPath", cloud.getString("localPath"));
            log.info("从百度云存储下载文件成功，桶名称: {}, 对象键: {}, 本地路径: {}", cloud.getString("bucketName"), cloud.getString("objectKey"), cloud.getString("localPath"));
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("从百度云存储下载文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("下载文件失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除百度云存储桶中的文件
     * 
     * @param request 包含bucketName和objectKey的请求
     * @return 删除结果
     */
    public static BaseResponse deleteFile(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云存储桶中的文件");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName", "objectKey")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName, objectKey");
            }
            
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String bucketName = cloud.getString("bucketName");
            String objectKey = cloud.getString("objectKey");
            
            // 删除Object
            bosClient.deleteObject(bucketName, objectKey);
            
            JSONObject result = new JSONObject();
            result.put("bucketName", bucketName);
            result.put("objectKey", objectKey);
            log.info("删除百度云存储桶中的文件成功，桶名称: {}, 对象键: {}", bucketName, objectKey);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除百度云存储桶中的文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件失败: " + e.getMessage()), e);
        }
    }

    /**
     * 查询百度云存储桶中的文件列表
     * 
     * @param request 包含bucketName和可选查询参数的请求
     * @return 文件列表结果
     */
    public static BaseResponse queryFiles(BaseCloudRequest request) {
        try {
            log.info("开始查询百度云存储桶中的文件列表");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "bucketName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：bucketName");
            }
            
            BosClient bosClient = BaiduClient.client.client(BosClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            String bucketName = cloud.getString("bucketName");
            
            // 构建查询请求
            ListObjectsRequest listRequest = new ListObjectsRequest(bucketName);
            
            // 设置可选查询参数
            if (cloud.containsKey("prefix")) {
                listRequest.setPrefix(cloud.getString("prefix"));
            }
            if (cloud.containsKey("marker")) {
                listRequest.setMarker(cloud.getString("marker"));
            }
            if (cloud.containsKey("maxKeys")) {
                listRequest.setMaxKeys(cloud.getInteger("maxKeys"));
            }
            
            // 执行查询
            ListObjectsResponse response = bosClient.listObjects(listRequest);
            List<BosObjectSummary> objectSummaries = response.getContents();
            
            JSONObject result = new JSONObject();
            result.put("bucketName", bucketName);
            result.put("isTruncated", response.isTruncated());
            result.put("nextMarker", response.getNextMarker());
            result.put("prefix", response.getPrefix());
            result.put("marker", response.getMarker());
            result.put("maxKeys", response.getMaxKeys());
            
            // 构建文件列表
            List<JSONObject> files = new java.util.ArrayList<>();
            for (BosObjectSummary summary : objectSummaries) {
                JSONObject file = new JSONObject();
                file.put("key", summary.getKey());
                file.put("size", summary.getSize());
                file.put("lastModified", summary.getLastModified());
                file.put("eTag", summary.getETag());
                file.put("storageClass", summary.getStorageClass());
                files.add(file);
            }
            result.put("files", files);
            
            log.info("查询百度云存储桶中的文件列表成功，桶名称: {}, 文件数量: {}", bucketName, files.size());
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("查询百度云存储桶中的文件列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询文件列表失败: " + e.getMessage()), e);
        }
    }
} 