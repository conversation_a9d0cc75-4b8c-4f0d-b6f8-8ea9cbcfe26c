package com.futong.gemini.plugin.cloud.baidu.service;

import com.baidubce.services.billing.BillingClient;
import com.baidubce.services.billing.model.finance.UserBalanceQueryResponse;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度云平台服务类
 * 提供平台级别的服务，如账单查询等
 */
@Slf4j
public class PlatformService {

    /**
     * 查询平台账单余额
     * 参考阿里云实现风格，使用百度云BillingClient获取账户余额
     */
    public static BaseResponse queryBillBalance(BaseCloudRequest request) {
        try {
            log.info("查询百度云账单余额");
            
            // 使用统一的CloudClient创建BillingClient
            BillingClient client = BaiduClient.client.client(BillingClient.class, request.getBody());
            UserBalanceQueryResponse response = client.userBalanceQuery();

            log.info("百度云账单余额查询成功，余额: {}", response.getCashBalance());
            return new BaseDataResponse<>(response.getCashBalance());
            
        } catch (Exception e) {
            log.error("查询百度云账单余额失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e);
        }
    }
} 