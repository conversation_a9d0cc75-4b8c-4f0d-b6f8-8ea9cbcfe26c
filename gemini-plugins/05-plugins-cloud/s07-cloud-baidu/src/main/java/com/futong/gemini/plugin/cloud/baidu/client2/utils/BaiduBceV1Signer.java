//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.futong.gemini.plugin.cloud.baidu.client2.utils;

import com.futong.gemini.plugin.cloud.baidu.client2.AuthConfig;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.Header;
import org.apache.http.client.methods.HttpRequestBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.*;


public class BaiduBceV1Signer {
    private static final Logger logger = LoggerFactory.getLogger(BaiduBceV1Signer.class);
    private static final Charset UTF8 = Charset.forName("UTF-8");
    private static final Set<String> defaultHeadersToSign = Sets.newHashSet();
    private static final Joiner headerJoiner = Joiner.on('\n');
    private static final Joiner signedHeaderStringJoiner = Joiner.on(';');


    public static void sign(HttpRequestBase request, AuthConfig authConfig, Map<String, String> params) {
        Preconditions.checkNotNull(request, "request should not be null.");
        String accessKeyId = authConfig.getAk();
        String secretAccessKey = authConfig.getSk();
        request.addHeader("Host", BaiduHttpUtils.generateHostHeader(request.getURI()));
        Date timestamp = new Date();
        String authString = "bce-auth-v1/" + accessKeyId + "/" + BaiduDateUtils.formatAlternateIso8601Date(timestamp) + "/" + 1800;
        String signingKey = sha256Hex(secretAccessKey, authString);
        String canonicalURI = getCanonicalURIPath(request.getURI().getPath());
        String canonicalQueryString = BaiduHttpUtils.getCanonicalQueryString(params, true);
        SortedMap<String, String> headersToSign = getHeadersToSign(request.getAllHeaders());
        String canonicalHeader = getCanonicalHeaders(headersToSign);
        String signedHeaders = "";
        signedHeaders = signedHeaderStringJoiner.join(headersToSign.keySet());
        signedHeaders = signedHeaders.trim().toLowerCase();
        String canonicalRequest = request.getMethod() + "\n" + canonicalURI + "\n" + canonicalQueryString + "\n" + canonicalHeader;
        String signature = sha256Hex(signingKey, canonicalRequest);
        String authorizationHeader = authString + "/" + signedHeaders + "/" + signature;
        logger.debug("CanonicalRequest:{}\tAuthorization:{}", canonicalRequest.replace("\n", "[\\n]"), authorizationHeader);
        request.addHeader("Authorization", authorizationHeader);
    }

    private static String getCanonicalURIPath(String path) {
        if (path == null) {
            return "/";
        } else {
            return path.startsWith("/") ? BaiduHttpUtils.normalizePath(path) : "/" + BaiduHttpUtils.normalizePath(path);
        }
    }

    private static String getCanonicalHeaders(SortedMap<String, String> headers) {
        if (headers.isEmpty()) {
            return "";
        } else {
            List<String> headerStrings = Lists.newArrayList();

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String key = (String) entry.getKey();
                if (key != null) {
                    String value = (String) entry.getValue();
                    if (value == null) {
                        value = "";
                    }

                    headerStrings.add(BaiduHttpUtils.normalize(key.trim().toLowerCase()) + ':' + BaiduHttpUtils.normalize(value.trim()));
                }
            }

            Collections.sort(headerStrings);
            return headerJoiner.join(headerStrings);
        }
    }

    private static SortedMap<String, String> getHeadersToSign(Map<String, String> headers, Set<String> headersToSign) {
        SortedMap<String, String> ret = Maps.newTreeMap();
        if (headersToSign != null) {
            Set<String> tempSet = Sets.newHashSet();

            for (String header : headersToSign) {
                tempSet.add(header.trim().toLowerCase());
            }

            headersToSign = tempSet;
        }

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue() != null && !entry.getValue().isEmpty() && (headersToSign == null && isDefaultHeaderToSign(key) || headersToSign != null && headersToSign.contains(key.toLowerCase()) && !"Authorization".equalsIgnoreCase(key))) {
                ret.put(key, entry.getValue());
            }
        }

        return ret;
    }

    private static SortedMap<String, String> getHeadersToSign(Header[] headers) {
        SortedMap<String, String> ret = Maps.newTreeMap();

        for (Header entry : headers) {
            String key = (String) entry.getName();
            if (entry.getValue() != null && !entry.getValue().isEmpty() && !"Authorization".equalsIgnoreCase(key)) {
                ret.put(key, entry.getValue());
            }
        }

        return ret;
    }

    private static boolean isDefaultHeaderToSign(String header) {
        header = header.trim().toLowerCase();
        return header.startsWith("x-bce-") || defaultHeadersToSign.contains(header);
    }

    private static String sha256Hex(String signingKey, String stringToSign) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(signingKey.getBytes(UTF8), "HmacSHA256"));
            return new String(Hex.encodeHex(mac.doFinal(stringToSign.getBytes(UTF8))));
        } catch (Exception e) {
            throw new RuntimeException("Fail to generate the signature", e);
        }
    }

    static {
        defaultHeadersToSign.add("Host".toLowerCase());
        defaultHeadersToSign.add("Content-Length".toLowerCase());
        defaultHeadersToSign.add("Content-Type".toLowerCase());
        defaultHeadersToSign.add("Content-MD5".toLowerCase());
    }
}
