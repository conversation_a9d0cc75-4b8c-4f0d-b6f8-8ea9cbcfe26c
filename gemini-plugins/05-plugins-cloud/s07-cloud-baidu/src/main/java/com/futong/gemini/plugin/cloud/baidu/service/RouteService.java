package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.route.RouteClient;
import com.baidubce.services.route.model.*;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class RouteService {

    /**
     * 创建路由表规则
     */
    public static BaseResponse createRoute(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云路由表");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "routeTableId", "sourceAddress", "destinationAddress")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：routeTableId, sourceAddress, destinationAddress");
            }
            RouteClient routeClient = BaiduClient.client.client(RouteClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            CreateRouteRequest createRequest = new CreateRouteRequest();
            createRequest.setRouteTableId(cloud.getString("routeTableId"));
            createRequest.setSourceAddress(cloud.getString("sourceAddress"));
            createRequest.setDestinationAddress(cloud.getString("destinationAddress"));
            if (cloud.containsKey("nexthopId")) {
                createRequest.setNexthopId(cloud.getString("nexthopId"));
            }
            if (cloud.containsKey("nexthopType")) {
                createRequest.setNexthopType(cloud.getString("nexthopType"));
            }
            if (cloud.containsKey("description")) {
                createRequest.setDescription(cloud.getString("description"));
            }
            CreateRouteResponse response = routeClient.createRoute(createRequest);
            JSONObject result = new JSONObject();
            result.put("routeTableId", response.getRouteRuleId());
            log.info("创建路由表成功，ID: {}", response.getRouteRuleId());
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("创建路由表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建路由表失败"), e);
        }
    }

    /**
     * 修改路由表规则
     */
    public static BaseResponse updateRoute(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云路由表");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "routeRuleId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：routeRuleId");
            }
            RouteClient routeClient = BaiduClient.client.client(RouteClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            
            // 获取路由规则ID
            String routeRuleId = cloud.getString("routeRuleId");
            log.info("准备修改路由规则，ID: {}", routeRuleId);
            
            UpdateRouteRuleRequest updateRequest = new UpdateRouteRuleRequest();
            updateRequest.setRouteRuleId(routeRuleId);
            
            // 设置可选的修改参数
            if (cloud.containsKey("sourceAddress")) {
                updateRequest.setSourceAddress(cloud.getString("sourceAddress"));
            }
            if (cloud.containsKey("destinationAddress")) {
                String destinationAddress = cloud.getString("destinationAddress");
                updateRequest.setDestinationAddress(destinationAddress);
                log.info("设置目标地址: {}", destinationAddress);
            }
            if (cloud.containsKey("nexthopId")) {
                String nexthopId = cloud.getString("nexthopId");
                updateRequest.setNexthopId(nexthopId);
                log.info("设置下一跳ID: {}", nexthopId);
            }
            if (cloud.containsKey("description")) {
                String description = cloud.getString("description");
                updateRequest.setDescription(description);
                log.info("设置描述: {}", description);
            }
            
            // 调用百度云API修改路由规则
            routeClient.updateRouteRule(updateRequest);
            
            JSONObject result = new JSONObject();
            result.put("routeTableId", routeRuleId);
            log.info("修改路由表成功，ID: {}", routeRuleId);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("修改路由表失败，错误详情: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改路由表失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除路由表规则
     */
    public static BaseResponse deleteRoute(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云路由表");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "routeRuleId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：routeRuleId");
            }
            RouteClient routeClient = BaiduClient.client.client(RouteClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            
            // 获取路由规则ID
            String routeRuleId = cloud.getString("routeRuleId");
            log.info("准备删除路由规则，ID: {}", routeRuleId);
            
            DeleteRouteRequest deleteRequest = new DeleteRouteRequest();
            deleteRequest.setRouteRuleId(routeRuleId);
            
            // 调用百度云API删除路由规则
            routeClient.deleteRouteRule(deleteRequest);
            
            JSONObject result = new JSONObject();
            result.put("routeTableId", routeRuleId);
            log.info("删除路由表成功，ID: {}", routeRuleId);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除路由表失败，错误详情: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除路由表失败: " + e.getMessage()), e);
        }
    }

    /**
     * 分页查询路由规则
     */
    public static BaseResponse queryRouteRules(BaseCloudRequest request) {
        try {
            log.info("开始分页查询百度云路由规则");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "vpcId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：vpcId");
            }
            RouteClient routeClient = BaiduClient.client.client(RouteClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            
            // 获取分页参数
            Integer pageNumber = cloud.getInteger("pageNumber");
            Integer pageSize = cloud.getInteger("pageSize");
            if (pageNumber == null) pageNumber = 1;
            if (pageSize == null) pageSize = 50;

            ListRouteRuleReq listRequest = new ListRouteRuleReq();
            listRequest.setVpcId(cloud.getString("vpcId"));
            listRequest.setMarker(cloud.getString("nextMarker"));
            listRequest.setMaxKeys(pageSize);
            ListRouteRuleResponse response = routeClient.listRouteRule(listRequest);
            JSONObject result = new JSONObject();
            result.put("routeRules", response.getRouteRules());
            result.put("totalCount", null); //百度云无 根据nextMarker查询下一页
            result.put("nextMarker", response.getNextMarker());
            result.put("pageNumber", pageNumber);
            result.put("pageSize", pageSize);
            
            log.info("分页查询路由规则成功，总数: 无，下一页标识：{}", response.getNextMarker());
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("分页查询路由规则失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("分页查询路由规则失败"), e);
        }
    }

    /**
     * 路由主备切换
     */
    public static BaseResponse switchRoute(BaseCloudRequest request) {
        try {
            log.info("开始路由主备切换");
            if (!CloudService.validateRequiredParams(request.getBody().getCloud(), "routeRuleId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：routeRuleId");
            }
            RouteClient routeClient = BaiduClient.client.client(RouteClient.class, request.getBody());
            JSONObject cloud = request.getBody().getCloud();
            
            // 获取路由规则ID
            String routeRuleId = cloud.getString("routeRuleId");
            log.info("准备进行路由主备切换，ID: {}", routeRuleId);
            
            if (routeRuleId != null) {
                // 路由主备切换
                SwitchRouteHaRequest switchRouteHaRequest = new SwitchRouteHaRequest();
                switchRouteHaRequest.setRouteRuleId(routeRuleId);
                
                // 调用百度云API进行路由主备切换
                routeClient.switchRouteHa(switchRouteHaRequest);
                log.info("路由主备切换成功，ID: {}", routeRuleId);
            } else {
                log.warn("路由规则ID为空，跳过主备切换");
            }
            
            JSONObject result = new JSONObject();
            result.put("routeTableId", routeRuleId);
            log.info("路由主备切换操作完成");
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("路由主备切换失败，错误详情: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("路由主备切换失败: " + e.getMessage()), e);
        }
    }
} 