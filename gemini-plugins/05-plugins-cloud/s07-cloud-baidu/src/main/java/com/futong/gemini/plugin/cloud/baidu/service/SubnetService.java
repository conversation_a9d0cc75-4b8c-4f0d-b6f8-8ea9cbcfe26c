package com.futong.gemini.plugin.cloud.baidu.service;

import com.alibaba.fastjson.JSONObject;
import com.baidubce.services.subnet.SubnetClient;
import com.baidubce.services.subnet.model.CreateSubnetRequest;
import com.baidubce.services.subnet.model.ModifySubnetAttributesRequest;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.baidu.client.BaiduClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 百度云子网服务类
 * 负责子网的管理操作
 * 
 * 主要功能：
 * - 子网创建、删除、修改
 * - 子网属性查询
 * 
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 * 使用百度云官方SDK进行API调用
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class SubnetService {

    /**
     * 创建子网
     * 根据CreateSubnetRequest参数创建百度云子网
     * 
     * 参数说明：
     * - name: 子网名称，不能取值"default"，长度不超过65个字符
     * - enableIpv6: 是否开启IPv6网段，默认false
     * - zoneName: 可用区名称
     * - cidr: 子网cidr，需在所属VPC网段范围内
     * - vpcId: 子网所属VPC的ID
     * - vpcSecondaryCidr: 子网所属VPC的辅助网段的cidr
     * - subnetType: 子网类型，"BCC"、"BCC_NAT"、"BBC"
     * - description: 描述，不超过200字符
     * - tags: 待创建的标签键值对列表
     */
    public static BaseResponse createSubnet(BaseCloudRequest request) {
        try {
            log.info("开始创建百度云子网");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "name", "zoneName", "cidr", "vpcId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name, zoneName, cidr, vpcId");
            }
            
            // 获取必需参数
            String name = cloud.getString("name");
            String zoneName = cloud.getString("zoneName");
            String cidr = cloud.getString("cidr");
            String vpcId = cloud.getString("vpcId");

            // 验证子网名称不能为"default"
            if ("default".equals(name)) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("子网名称不能取值'default'");
            }

            // 验证子网名称长度
            if (name.length() > 65) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("子网名称长度不能超过65个字符");
            }

            // 获取可选参数
            Boolean enableIpv6 = cloud.getBoolean("enableIpv6");
            String vpcSecondaryCidr = cloud.getString("vpcSecondaryCidr");
            String subnetType = cloud.getString("subnetType");
            String description = cloud.getString("description");

            // 验证描述长度
            if (description != null && description.length() > 200) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("描述长度不能超过200字符");
            }

            // 创建SubnetClient
            SubnetClient subnetClient = BaiduClient.client.client(SubnetClient.class, request.getBody());
            
            // 构建CreateSubnetRequest
            CreateSubnetRequest createSubnetRequest = new CreateSubnetRequest();
            createSubnetRequest.setName(name);
            createSubnetRequest.setZoneName(zoneName);
            createSubnetRequest.setCidr(cidr);
            createSubnetRequest.setVpcId(vpcId);
            
            if (enableIpv6 != null) {
                createSubnetRequest.setEnableIpv6(enableIpv6);
            }
            if (vpcSecondaryCidr != null) {
                createSubnetRequest.setVpcSecondaryCidr(vpcSecondaryCidr);
            }
            if (subnetType != null) {
                createSubnetRequest.setSubnetType(subnetType);
            }
            if (description != null) {
                createSubnetRequest.setDescription(description);
            }
            if (cloud.containsKey("tags")) {
                createSubnetRequest.setTags(cloud.getObject("tags", List.class));
            }
            
            // 调用SDK创建子网
            Object response = subnetClient.createSubnet(createSubnetRequest);
            JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(response));

            // 构造返回结果
            JSONObject result = new JSONObject();
            String subnetId = null;
            
            // 从响应中获取子网ID
            if (responseJson.containsKey("subnetId")) {
                subnetId = responseJson.getString("subnetId");
                result.put("subnetId", subnetId);
            } else if (responseJson.containsKey("id")) {
                subnetId = responseJson.getString("id");
                result.put("subnetId", subnetId);
            } else {
                // 如果响应中没有子网ID，生成临时ID
                subnetId = "subnet-" + System.currentTimeMillis();
                result.put("subnetId", subnetId);
                log.warn("响应中未找到子网ID，使用临时ID: {}", subnetId);
            }
            
            result.put("subnetName", name);
            result.put("cidr", cidr);
            result.put("vpcId", vpcId);
            result.put("zoneName", zoneName);
            result.put("enableIpv6", enableIpv6 != null ? enableIpv6 : false);
            
            if (vpcSecondaryCidr != null) {
                result.put("vpcSecondaryCidr", vpcSecondaryCidr);
            }
            if (subnetType != null) {
                result.put("subnetType", subnetType);
            }
            if (description != null) {
                result.put("description", description);
            }
            
            // 处理标签
            if (cloud.containsKey("tags")) {
                result.put("tags", cloud.get("tags"));
            }
            
            log.info("创建百度云子网成功，子网 ID: {}, 名称: {}, CIDR: {}, VPC ID: {}, 可用区: {}", 
                    subnetId, name, cidr, vpcId, zoneName);
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("创建百度云子网失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建子网失败: " + e.getMessage()), e);
        }
    }

    /**
     * 修改子网属性
     * 根据ModifySubnetAttributesRequest参数更新百度云子网
     * 
     * 参数说明：
     * - subnetId: 待更新的子网实例ID
     * - name: 子网名称，不能取值"default"，长度不超过65个字符
     * - description: 描述，不超过200字符
     * - enableIpv6: 是否开启IPv6网段
     */
    public static BaseResponse updateSubnet(BaseCloudRequest request) {
        try {
            log.info("开始修改百度云子网属性");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "subnetId", "name")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：subnetId, name");
            }
            
            // 获取必需参数
            String subnetId = cloud.getString("subnetId");
            String name = cloud.getString("name");
            
            // 验证子网名称不能为"default"
            if ("default".equals(name)) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("子网名称不能取值'default'");
            }
            
            // 验证子网名称长度
            if (name.length() > 65) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("子网名称长度不能超过65个字符");
            }
            
            // 获取可选参数
            String description = cloud.getString("description");
            Boolean enableIpv6 = cloud.getBoolean("enableIpv6");

            // 验证描述长度
            if (description != null && description.length() > 200) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("描述长度不能超过200字符");
            }
            
            // 创建SubnetClient
            SubnetClient subnetClient = BaiduClient.client.client(SubnetClient.class, request.getBody());
            
            // 构建ModifySubnetAttributesRequest
            ModifySubnetAttributesRequest modifyRequest = new ModifySubnetAttributesRequest();
            modifyRequest.setSubnetId(subnetId);
            modifyRequest.setName(name);

            if (description != null) {
                modifyRequest.setDescription(description);
            }
            if (enableIpv6 != null) {
                modifyRequest.setEnableIpv6(enableIpv6);
            }
            
            // 调用SDK修改子网属性
            subnetClient.modifySubnetAttributes(modifyRequest);
            
            log.info("修改百度云子网属性成功，子网 ID: {}, 名称: {}", subnetId, name);
            return BaseResponse.SUCCESS.of("修改子网属性成功");
            
        } catch (Exception e) {
            log.error("修改百度云子网属性失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改子网属性失败: " + e.getMessage()), e);
        }
    }

    /**
     * 删除子网
     * 根据subnetId删除百度云子网
     * 
     * 参数说明：
     * - subnetId: 待删除的子网实例ID
     */
    public static BaseResponse deleteSubnet(BaseCloudRequest request) {
        try {
            log.info("开始删除百度云子网");
            
            JSONObject cloud = request.getBody().getCloud();
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(cloud, "subnetId")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：subnetId");
            }
            
            // 获取子网ID
            String subnetId = cloud.getString("subnetId");
            
            // 创建SubnetClient
            SubnetClient subnetClient = BaiduClient.client.client(SubnetClient.class, request.getBody());
            
            // 调用SDK删除子网
            subnetClient.deleteSubnet(subnetId);
            
            log.info("删除百度云子网成功，子网 ID: {}", subnetId);
            return BaseResponse.SUCCESS.of("删除子网成功");
            
        } catch (Exception e) {
            log.error("删除百度云子网失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除子网失败: " + e.getMessage()), e);
        }
    }
} 