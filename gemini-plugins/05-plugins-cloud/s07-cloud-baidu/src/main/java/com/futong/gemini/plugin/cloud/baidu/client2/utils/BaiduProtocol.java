package com.futong.gemini.plugin.cloud.baidu.client2.utils;

public enum BaiduProtocol {
    HTTP("http", 80),
    HTTPS("https", 443);

    private String protocol;
    private int defaultPort;

    private BaiduProtocol(String protocol, int defaultPort) {
        this.protocol = protocol;
        this.defaultPort = defaultPort;
    }

    public int getDefaultPort() {
        return this.defaultPort;
    }

    public String toString() {
        return this.protocol;
    }
}