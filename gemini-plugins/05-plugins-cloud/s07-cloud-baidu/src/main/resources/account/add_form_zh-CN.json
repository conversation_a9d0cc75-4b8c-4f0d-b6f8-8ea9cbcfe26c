{"description": "初始纳管账号仅支持主账号纳管", "model": [{"type": "main", "name": "百度云平台运营主账号", "description": "百度云平台运营主账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云账号"}, {"field": "jsonStr.cloudUserId", "label": "百度云账户ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云账户ID"}, {"field": "username", "label": "Access Key ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云Access Key ID"}, {"field": "password", "label": "Secret Access Key", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云Secret Access Key"}, {"field": "jsonStr.endpoint", "label": "API端点", "type": "input", "value": "bcc.bj.baidubce.com", "required": false, "isUpdate": true, "tips": "请输入百度云API端点，默认为bcc.bj.baidubce.com"}, {"field": "jsonStr.proxyAddr", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入代理地址"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}, {"scope": "resource", "type": "sub", "name": "百度云平台运营子账号", "description": "百度云平台运营子账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云账号"}, {"field": "jsonStr.cloudUserId", "label": "百度云账户ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云账户ID"}, {"field": "username", "label": "Access Key ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云Access Key ID"}, {"field": "password", "label": "Secret Access Key", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入百度云Secret Access Key"}, {"field": "jsonStr.endpoint", "label": "API端点", "type": "input", "value": "bcc.bj.baidubce.com", "required": false, "isUpdate": true, "tips": "请输入百度云API端点，默认为bcc.bj.baidubce.com"}, {"field": "jsonStr.proxyAddr", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入代理地址"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}]}