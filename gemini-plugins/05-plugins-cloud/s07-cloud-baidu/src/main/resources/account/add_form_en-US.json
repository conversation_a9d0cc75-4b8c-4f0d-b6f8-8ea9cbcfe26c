{"description": "The initial management account only supports the main account for management", "model": [{"type": "main", "name": "Baidu Cloud platform operation master account", "description": "Baidu Cloud platform operation master account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Account."}, {"field": "jsonStr.cloudUserId", "label": "Baidu Cloud User ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud User ID"}, {"field": "username", "label": "Access Key ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Access Key ID"}, {"field": "password", "label": "Secret Access Key", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Secret Access Key"}, {"field": "jsonStr.endpoint", "label": "API Endpoint", "type": "input", "value": "bcc.bj.baidubce.com", "required": false, "isUpdate": true, "tips": "Please enter your Baidu Cloud API Endpoint, default is bcc.bj.baidubce.com"}, {"field": "jsonStr.proxyAddr", "label": "Proxy Address", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Proxy Address"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}, {"scope": "resource", "type": "sub", "name": "Baidu Cloud platform operation sub account", "description": "Baidu Cloud platform operation sub account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Account."}, {"field": "jsonStr.cloudUserId", "label": "Baidu Cloud User ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud User ID"}, {"field": "username", "label": "Access Key ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Access Key ID"}, {"field": "password", "label": "Secret Access Key", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Baidu Cloud Secret Access Key"}, {"field": "jsonStr.endpoint", "label": "API Endpoint", "type": "input", "value": "bcc.bj.baidubce.com", "required": false, "isUpdate": true, "tips": "Please enter your Baidu Cloud API Endpoint, default is bcc.bj.baidubce.com"}, {"field": "jsonStr.proxyAddr", "label": "Proxy Address", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Proxy Address"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}]}