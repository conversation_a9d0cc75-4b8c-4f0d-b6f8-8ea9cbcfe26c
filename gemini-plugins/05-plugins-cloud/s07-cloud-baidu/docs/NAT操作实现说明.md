# NAT操作实现说明

## 概述

本文档说明了百度云NAT网关操作的实现，包括采集NAT网关，以及创建、修改、删除NAT网关功能。

## 实现内容

### 1. 使用 NatService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/NatService.java`

**主要功能**:
- 创建NAT网关 (`createNat`)
- 修改NAT网关 (`updateNat`)
- 删除NAT网关 (`deleteNat`)

**实现特点**:
- 使用百度云 NatClient
- 包含参数验证和异常处理
- 提供详细日志记录
- 支持完整的NAT网关创建参数

### 2. 更新 FetchService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/sampler/FetchService.java`

**更新内容**:
- 添加 `fetchNat` 方法采集NAT网关
- 添加 `fetchSnatEntry` 方法采集SNAT规则
- 添加 `fetchDnatEntry` 方法采集DNAT规则

### 3. 更新 Convert 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/common/Convert.java`

**更新内容**:
- 添加 `convertNat` 方法转换NAT数据
- 添加 `convertSnatEntry` 方法转换SNAT规则数据
- 添加 `convertDnatEntry` 方法转换DNAT规则数据

### 4. 更新 BaiduRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduRegister.java`

**更新内容**:
- 更新 `onAfterLoadNeutronNat` 方法注册操作

## 创建NAT网关参数说明

### 必需参数

| 参数名 | 类型 | 是否必需 | 说明 |
|--------|------|----------|------|
| name | String | 是 | NAT网关的名称，由大小写字母、数字以及-_ /.特殊字符组成，必须以字母开头，长度1-65 |
| vpcId | String | 是 | VPC的ID |

### 规格参数（二选一）

| 参数名 | 类型 | 是否必需 | 说明 |
|--------|------|----------|------|
| spec | String | 否 | NAT网关的大小，有small(最多支持绑定5个公网IP)、medium(最多支持绑定10个公网IP)、large(最多支持绑定15个公网IP)三种；该参数和cuNum只能二选一，仅适用于普通型NAT网关 |
| cuNum | Integer | 否 | NAT网关的CU数量，该参数和spec只能二选一，仅适用于增强型NAT网关 |

### 可选参数

| 参数名 | 类型 | 是否必需 | 说明 |
|--------|------|----------|------|
| eips | List | 否 | 关联NAT网关SNAT的EIP或者共享带宽中的一个或多个EIP |
| dnatEips | List | 否 | 关联NAT网关DNAT的EIP或者共享带宽中的一个或多个EIP |
| bindEips | List | 否 | 关联NAT网关的EIP或者共享带宽中的一个或多个EIP，仅适用于增强型NAT网关 |
| billing | Billing | 是 | 计费信息 |
| tags | List<TagModel> | 否 | 待创建的标签键值对列表 |
| resourceGroupId | String | 否 | 资源组 |
| deleteProtect | Boolean | 否 | 是否开启释放保护。缺省值为false，代表允许删除 |

### 参数验证规则

1. **名称验证**:
   - 长度必须在1-65个字符之间
   - 必须以字母开头
   - 可包含字母、数字、-_/.字符

2. **规格验证**:
   - spec参数必须是small、medium或large之一
   - cuNum参数必须大于0
   - spec和cuNum不能同时设置

3. **计费信息**:
   - 如果未提供billing参数，默认使用后付费方式

## 关键特性

- 支持NAT网关的CRUD操作
- 分页采集支持
- 数据转换为 CmdbNatRes 模型
- 完整的参数验证和错误处理
- 支持SNAT和DNAT规则采集

## 使用示例

### 创建NAT网关（普通型）
```java
JSONObject cloud = new JSONObject();
cloud.put("name", "MyNatGateway");
cloud.put("vpcId", "vpc-xxxxx");
cloud.put("spec", "medium");
cloud.put("billing", billingInfo);
request.getBody().setCloud(cloud);
BaseResponse response = NatService.createNat(request);
```

### 创建NAT网关（增强型）
```java
JSONObject cloud = new JSONObject();
cloud.put("name", "MyEnhancedNatGateway");
cloud.put("vpcId", "vpc-xxxxx");
cloud.put("cuNum", 2);
cloud.put("billing", billingInfo);
request.getBody().setCloud(cloud);
BaseResponse response = NatService.createNat(request);
```

## 注意事项

- 确保模型类 CmdbNatRes 存在
- 操作需指定VPC
- spec和cuNum参数不能同时设置
- 名称必须符合百度云命名规范
- 计费信息是必需的，如果未提供会使用默认后付费方式

## 总结

实现了百度云NAT网关的完整操作功能，包括：
- 创建NAT网关（支持所有参数）
- 修改NAT网关
- 删除NAT网关
- 采集NAT网关信息
- 采集SNAT和DNAT规则
- 完整的参数验证和错误处理

遵循项目规范，与其他云厂商实现保持一致。 