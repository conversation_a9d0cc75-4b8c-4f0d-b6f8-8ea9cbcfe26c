# 计算资源操作实现说明

## 概述

本文档说明了百度云计算资源操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadCompute` 方法编写了完整的实现。

## 实现内容

### 1. 使用 ComputeInstanceService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/ComputeInstanceService.java`

**主要功能**:
- 云主机创建 (`createInstance`)
- 云主机修改 (`updateInstance`) 
- 云主机删除 (`deleteInstance`)
- 云主机启动 (`startInstance`)
- 云主机停止 (`stopInstance`)
- 云主机重启 (`rebootInstance`)
- 云主机规格修改 (`updateComputeInstanceFlavor`)
- VNC登录地址查询 (`queryInstanceVncUrl`)
- VNC密码修改 (`updateInstanceVncPassword`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadCompute` 方法
- 添加了云主机创建、修改、删除、启动、停止、重启等操作的注册
- 配置了相应的刷新任务和参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 计算资源操作模式 (实现)
```java
// 创建云主机
register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance)
    .addBefore(CloudService::toBeforeBiz)
    .addAfter(CloudService::toAfterBizResId)
    .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
    .addSetRefreshConfig(20, 5000, 20000)
    .addSetRefreshSplitData("response","$.instanceId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);

// 修改云主机
register(ActionType.UPDATE_COMPUTE_INSTANCE, ComputeInstanceService::updateInstance)
    .addTransferCloud("$.ci.openId", "$.instanceId")
    .addTransferCloud("$.model.resourceName", "$.name", false)
    .addTransferCloud("$.model.description", "$.desc", false)
    .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
    .addSetRefreshConfig(0, 0, 1000)
    .addSetRefreshData("request","$.body.cloud.instanceId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);

// 删除云主机
register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance)
    .addTransferCloud("$.cis.openId", "$.instanceId")
    .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

## 关键特性

### 1. 参数映射
- **创建操作**: 将响应中的 `instanceId` 映射到刷新任务
- **修改操作**: 将请求中的 `instanceId` 映射到刷新任务
- **删除操作**: 支持批量删除，将请求中的 `instanceIds` 分割为多个刷新任务
- **启动/停止/重启**: 支持批量操作，将请求中的 `instanceIds` 分割为多个刷新任务

### 2. 刷新配置
- **创建**: 延迟20秒，间隔5秒，最多重试20000次
- **删除/启动/停止**: 延迟20秒，间隔2秒，最多重试1000次
- **重启**: 延迟30秒，间隔2秒，最多重试1000次
- **修改**: 立即执行，不重试，延迟1秒

### 3. 业务处理
- **创建操作**: 添加了业务处理前处理和后处理
- **所有操作**: 都添加了 `BaseCloudService::addRefreshGourdJob` 后处理

### 4. 云信息传递
- 基于请求中的 `regionId` 设置刷新请求的云信息
- 确保刷新任务在正确的区域执行

## 注意事项

### 1. SDK 支持
百度云SDK支持云主机的创建、修改、删除、启动、停止、重启等操作，因此：
- `ComputeInstanceService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 所有操作都配置了相应的刷新任务
- 使用 `REFRESH_COMPUTE_INSTANCE` 常量进行刷新
- 支持批量操作的刷新分割

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射和刷新配置的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持云主机规格修改
- 支持VNC登录功能
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 云主机创建
- 支持指定实例规格、镜像、网络等参数
- 支持业务处理前处理和后处理
- 返回生成的实例ID

### 2. 云主机修改
- 支持修改实例名称和描述
- 支持修改其他实例属性

### 3. 云主机删除
- 支持删除指定的云主机
- 支持批量删除操作

### 4. 云主机控制
- 支持启动、停止、重启云主机
- 支持批量操作

### 5. 云主机规格修改
- 支持修改云主机规格
- 包含规格查询功能

### 6. VNC功能
- 支持查询VNC登录地址
- 支持修改VNC登录密码

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadCompute` 方法编写了完整的代码。实现包括：

1. **使用了 `ComputeInstanceService` 类** - 提供云主机操作的核心功能
2. **完善了注册配置** - 支持创建、修改、删除、控制等操作
3. **配置了刷新任务** - 确保操作后的资源状态同步
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对云主机操作有良好的支持，因此实现了完整的功能，包括业务处理、刷新机制和错误处理。 