# 云主机(fetchInstance) 采集需求文档

## 一、功能需求
- 支持从百度云平台批量采集云主机（ECS/Instance）资源信息。
- 支持分页采集、全量统计、可用区过滤。
- 支持采集云主机的基础属性、运行状态、规格、网络、镜像、关联关系等。
- 支持与平台统一数据模型对齐，便于后续资源管理、展示、运维。
- 支持采集结果的标准化、结构化、批量推送。

## 二、数据字段需求
- 需采集并映射以下主表字段：
| 平台字段         | 百度云原始字段/处理逻辑                | 说明           |
|------------------|--------------------------------------|----------------|
| cloud_type       | request.getPlugin().getRealm()       | 云类型         |
| account_id       | request.getBody().getAccess().getCmpId() | 云账号ID   |
| res_id           | IdUtils.encryptId(cmpId, instance.getString("instanceId")) | 资源ID |
| open_id          | instance.getString("instanceId")     | 云平台原始资源ID |
| open_name        | instance.getString("name")           | 云平台原始资源名称 |
| cpu_size         | instance.getInteger("cpuCount")      | CPU核数        |
| mem_size         | instance.getInteger("memoryCapacityInGB") * 1024 | 内存大小(MB) |
| status           | InstanceStatus.convert(instance.getString("status")) | 实例运行状态（需字典转换）|
| open_status      | instance.getString("status")         | 原始状态       |
| desc             | instance.getString("desc")           | 描述           |
| is_template      | -                           | 是否模板       |
| create_time      | DateUtil.parse(instance.getString("createTime")).getTime() | 创建时间（转为Long）|
| update_time      | -                                    | 更新时间       |
| open_create_time | DateUtil.parse(instance.getString("createTime")).getTime() | 原始创建时间 |
| open_update_time | -                                    | 原始更新时间   |
| extend1          | -                                    | 扩展字段1      |
| extend2          | -                                    | 扩展字段2      |
| extend3          | -                                    | 扩展字段3      |
| resource_type    | -                                    | 资源类型       |
| zoneName         | instance.getString("zoneName")       | 可用区         |

- 需采集并映射以下关联数据（Association）：
| 关联名称   | 关联类型         | 平台字段→百度云原始字段/处理逻辑 |
|------------|------------------|----------------------------------|
| flavor     | CmdbFlavor       | spec → instance.getString("spec")，IdUtils.encryptId(cmpId, spec) |
| image      | CmdbImageRes     | imageId → instance.getString("imageId")，IdUtils.encryptId(cmpId, imageId) |
| vpc        | CmdbVpcRes       | vpcId → instance.getString("vpcId")，IdUtils.encryptId(cmpId, vpcId) |
| subnet     | CmdbSubnetRes    | subnetId → instance.getString("subnetId")，IdUtils.encryptId(cmpId, subnetId) |
| keyPair    | CmdbKeypairRes   | keypairId → instance.getString("keypairId")，IdUtils.encryptId(cmpId, keypairId) |
| securityGroup | CmdbSecuritygroupRes | securityGroups → instance.getJSONObject("nicInfo").getJSONArray("securityGroups").getString(i)，IdUtils.encryptId(cmpId, securityGroupId) |
| 私网IP     | CmdbIpRes        | privateIp → instance.getJSONObject("nicInfo").getJSONArray("ips").getJSONObject(i).getString("privateIp")，IdUtils.encryptId(cmpId, privateIp) |
| 公网IP     | CmdbIpRes        | eip → instance.getJSONObject("nicInfo").getJSONArray("ips").getJSONObject(i).getString("eip")，IdUtils.encryptId(cmpId, eip) |

- 需支持 instances/instanceList 字段兼容。
- 需支持 totalCount 全量统计。

## 三、接口与流程需求
- 采集接口需支持分页参数（pageNumber、pageSize）、可用区过滤（zoneName）、全量统计（needTotalCount）。
- 采集流程：
  1. 获取BccClient实例，构造ListInstancesRequest，设置参数。
  2. 调用listInstances获取响应，转为JSONObject。
  3. 调用Convert.convertInstance进行字段映射和数据转换。
  4. 调用BaseCloudService.fetchSend推送采集结果。
  5. 支持分页和分层调度（如有）。

## 四、异常与日志需求
- 采集过程需捕获所有异常，输出详细日志，便于排查。
- 采集失败需返回BaseResponse.FAIL_OP。
- 日志需包含采集起止、分页、参数、异常等关键信息。

## 五、扩展性与兼容性需求
- 字段映射、关联关系需与平台统一数据模型对齐，便于后续扩展。
- 支持关联资源的灵活扩展（如后续新增云主机属性、网络属性等）。
- 支持与平台调度、定时任务、批量采集等能力集成。
- 支持与其他资源（如云主机规格、镜像、VPC、磁盘等）统一采集风格。

---
如需扩展采集需求，请参考 fetchFlavor、fetchKeyPair、fetchDisk 的需求文档风格。
