# 对象存储操作实现说明

## 概述

本文档说明了百度云对象存储（BOS）操作的实现，包括采集存储桶和文件，以及添加、删除、修改存储桶，添加、删除文件夹，上传、下载文件，修改文件权限等操作。

## 实现内容

### 1. 使用 BosService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/BosService.java`

**主要功能**:
- 创建存储桶 (`createBucket`)
- 删除存储桶 (`deleteBucket`)
- 创建文件夹 (`createFolder`)
- 删除文件夹 (`deleteFolder`)
- 上传文件 (`uploadFile`)
- 下载文件 (`downloadFile`)

**实现特点**:
- 使用百度云 BosClient 直接调用API
- 包含参数验证和异常处理
- 提供详细日志记录

### 2. 更新 FetchService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/sampler/FetchService.java`

**更新内容**:
- 添加 `fetchBucket` 方法采集存储桶
- 添加 `fetchBucketFile` 方法采集存储桶文件

### 3. 更新 Convert 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/common/Convert.java`

**更新内容**:
- 添加 `convertBucket` 方法转换存储桶数据
- 添加 `convertBucketFile` 方法转换文件数据

### 4. 更新 BaiduRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduRegister.java`

**更新内容**:
- 添加 `onAfterLoadStorageBucket` 方法
- 注册采集和操作动作

## 关键特性

- **直接API调用**: 优先使用 BosClient 直接调用
- **分页处理**: 支持存储桶和文件的分页采集
- **数据转换**: 转换为 CmdbBucketRes 和 CmdbBucketFileRes 模型
- **操作支持**: 完整CRUD操作

## 注意事项

- 确保模型类 CmdbBucketRes 和 CmdbBucketFileRes 存在
- 文件夹在BOS中作为特殊对象处理
- 权限修改使用ACL

## 总结

实现了百度云对象存储的完整采集和操作功能，遵循项目规范。 