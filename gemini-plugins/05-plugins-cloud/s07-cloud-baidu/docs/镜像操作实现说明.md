# 镜像操作实现说明

## 概述

本文档说明了百度云镜像操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadComputeImage` 方法编写了完整的实现。

## 实现内容

### 1. 使用 ImageService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/ImageService.java`

**主要功能**:
- 镜像查询 (`queryImages`)
- 镜像创建 (`createImage`)
- 镜像删除 (`deleteImages`)
- 镜像复制 (`copyImage`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadComputeImage` 方法
- 添加了镜像查询、创建、删除、复制等操作的注册
- 配置了相应的参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 镜像操作模式 (实现)
```java
// 查询镜像
register(ActionType.QUERY_COMPUTE_IMAGE, ImageService::queryImages); // 查询镜像

// 创建自定义镜像
register(ActionType.CREATE_COMPUTE_IMAGE, ImageService::createImage); // 创建自定义镜像

// 批量删除镜像
register(ActionType.DELETE_COMPUTE_IMAGE, ImageService::deleteImages) // 批量删除镜像
    .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);

// 复制镜像
register(ActionType.COPY_COMPUTE_IMAGE, ImageService::copyImage) // 复制一个地域下的自定义镜像到其他地域
    .addTransferCloud("$.ci.openId", "$.imageId")
    .addTransferCloud("$.model.name", "$.name", false)
    .addTransferCloud("$.model.destRegion", "$.destRegion");
```

## 关键特性

### 1. 参数映射
- **查询操作**: 无需特殊参数映射
- **创建操作**: 支持自定义镜像创建
- **删除操作**: 支持批量删除，将请求中的 `regionId` 映射到操作
- **复制操作**: 将请求中的 `imageId`、`name`、`destRegion` 映射到操作

### 2. 操作类型
- **查询镜像**: 支持查询所有镜像
- **创建镜像**: 支持创建自定义镜像
- **删除镜像**: 支持批量删除镜像
- **复制镜像**: 支持跨地域复制镜像

### 3. 参数验证
- 创建镜像时验证必需参数
- 删除镜像时验证必需参数：`regionId`
- 复制镜像时验证必需参数：`imageId`、`destRegion`

## 注意事项

### 1. SDK 支持
百度云SDK支持镜像的查询、创建、删除、复制等操作，因此：
- `ImageService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 镜像操作暂未配置刷新任务
- 主要关注镜像的查询和管理功能
- 为未来添加刷新功能预留了扩展空间

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持跨地域镜像复制
- 支持批量操作
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 镜像查询
- 支持查询所有镜像
- 支持按条件筛选镜像

### 2. 镜像创建
- 支持创建自定义镜像
- 支持从云主机创建镜像

### 3. 镜像删除
- 支持删除指定的镜像
- 支持批量删除操作

### 4. 镜像复制
- 支持跨地域复制镜像
- 支持指定目标地域和镜像名称

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadComputeImage` 方法编写了完整的代码。实现包括：

1. **使用了 `ImageService` 类** - 提供镜像操作的核心功能
2. **完善了注册配置** - 支持查询、创建、删除、复制等操作
3. **配置了参数映射** - 确保操作参数的正确传递
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对镜像操作有良好的支持，因此实现了完整的功能，包括跨地域复制和批量操作。 