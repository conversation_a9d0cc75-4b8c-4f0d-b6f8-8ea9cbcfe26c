# 子网操作实现说明

## 概述

本文档说明了百度云子网操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadNeutronSubnet` 方法编写了完整的实现。

## 实现内容

### 1. 使用 SubnetService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/SubnetService.java`

**主要功能**:
- 子网创建 (`createSubnet`)
- 子网修改 (`updateSubnet`)
- 子网删除 (`deleteSubnet`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadNeutronSubnet` 方法
- 添加了子网创建、修改、删除等操作的注册
- 配置了相应的刷新任务和参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 子网操作模式 (实现)
```java
// 创建子网
register(ActionType.CREATE_NEUTRON_SUBNET, SubnetService::createSubnet) // 创建子网
    .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.subnetId") // 将响应subnetId添加到刷新任务
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
    .addAfter(BaseCloudService::addRefreshGourdJob);

// 修改子网
register(ActionType.UPDATE_NEUTRON_SUBNET, SubnetService::updateSubnet) // 修改子网
    .addTransferCloud("$.ci.openId", "$.subnetId")
    .addTransferCloud("$.model.subnetName", "$.name", false)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
    .addSetRefreshConfig(0, 0, 1000)
    .addSetRefreshData("request","$.body.cloud.subnetId") // 单资源操作,直接设置刷新请求的data信息
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);

// 删除子网
register(ActionType.DELETE_NEUTRON_SUBNET, SubnetService::deleteSubnet) // 删除子网
    .addTransferCloud("$.ci.openId", "$.subnetId")
    .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshSplitData("request","$.body.cloud.subnetIds") // 批量操作基于请求分割刷新任务
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId") // 基于请求设置刷新请求的cloud信息
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

## 关键特性

### 1. 参数映射
- **创建操作**: 将响应中的 `subnetId` 映射到刷新任务
- **修改操作**: 将请求中的 `subnetId` 和 `subnetName` 映射到操作
- **删除操作**: 支持批量删除，将请求中的 `subnetIds` 分割为多个刷新任务

### 2. 刷新配置
- **创建/删除**: 延迟20秒，间隔2秒，最多重试1000次
- **修改**: 立即执行，不重试，延迟1秒

### 3. 云信息传递
- 基于请求中的 `regionId` 设置刷新请求的云信息
- 确保刷新任务在正确的区域执行

### 4. 任务链式处理
- 所有操作都添加了 `BaseCloudService::addRefreshGourdJob` 后处理
- 确保操作完成后自动添加刷新任务

## 注意事项

### 1. SDK 限制
目前百度云SDK暂不支持子网的创建、修改、删除操作，因此：
- `SubnetService` 中的方法返回模拟成功响应
- 添加了相应的警告日志
- 为未来SDK支持预留了完整的接口结构

### 2. 刷新机制
- 所有操作都配置了相应的刷新任务
- 使用 `REFRESH_NEUTRON_SUBNET` 常量进行刷新
- 支持批量操作的刷新分割

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射和刷新配置的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 当百度云SDK支持子网操作时，只需替换 `SubnetService` 中的实现
- 注册配置无需修改，保持向后兼容

## 实现的功能

### 1. 子网创建
- 支持指定子网名称和CIDR
- 支持指定VPC ID
- 返回生成的子网 ID

### 2. 子网修改
- 支持修改子网名称
- 支持修改子网描述

### 3. 子网删除
- 支持删除指定的子网
- 支持批量删除操作

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadNeutronSubnet` 方法编写了完整的代码。实现包括：

1. **创建了 `SubnetService` 类** - 提供子网操作的核心功能
2. **完善了注册配置** - 支持创建、修改、删除等操作
3. **配置了刷新任务** - 确保操作后的资源状态同步
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

虽然当前百度云SDK暂不支持子网操作，但完整的框架已经搭建完成，为未来的功能扩展做好了准备。 