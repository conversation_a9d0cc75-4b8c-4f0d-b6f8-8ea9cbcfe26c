### 1. CloudClient.java
- 修改 `listFlavorSpec` 方法
**SDK方式实现：**
- 获取 BccClient 实例（百度云SDK的安全组相关操作类）。
- 构造 Request 请求对象，设置 regionId、分页参数等。
- 调用 bccClient.listFlavorSpec(request) 获取响应。
- 将响应对象转为 JSONObject 返回。

### 2. Convert.java - 实现convertFlavor方法
参考阿里云 `convertFlavor` 实现，完成了以下字段映射：

#### 字段对照表
| 阿里云字段 | 百度云字段 | 说明 |
|-----------|-----------|------|
| res_id | IdUtils.encryptId(cmpId, flavorId, zoneName) | 资源ID |
| cpu_arch | response.zoneResources.bccResources.flavorGroups.flavors.cpuModel | CPU架构 |
| cpu_size | response.zoneResources.bccResources.flavorGroups.flavors.cpuCount | CPU核数 |
| mem_size | response.zoneResources.bccResources.flavorGroups.flavors.memoryCapacityInGB * 1024 | 内存大小(GB转MB) |
| min_sysdisk_size | response.zoneResources.bccResources.flavorGroups.flavors.ephemeralDiskInGb | 本地存储容量 |
| specification_class_code | response.zoneResources.bccResources.flavorGroups.flavors.specId | 规格族代码 |
| specification_class_name | response.zoneResources.bccResources.flavorGroups.flavors.specId | 规格族名称 |
| category | 根据response.zoneResources.bccResources.flavorGroups.flavors.gpuCardType判断是否为空 | gpu卡类型 |
| gpu_size | 无 | GPU内存大小 |
| gpu_num | response.zoneResources.bccResources.flavorGroups.flavors.gpuCardCount | GPU数量 |
| gpu_model | response.zoneResources.bccResources.flavorGroups.flavors.gpuCardType | GPU型号 |
| open_id | response.zoneResources.bccResources.flavorGroups.flavors.spec | 原始规格ID |
| open_name | response.zoneResources.bccResources.flavorGroups.flavors.spec | 原始规格名称 |

#### 实现特点
- 使用 `Map<Class, List>` 返回值格式
- 使用 `IdUtils.encryptId` 生成资源ID
- 使用 `BuilderResourceSet` 构建资源集合
- 设置云类型为 request.getPlugin().getRealm()
- 支持GPU规格识别

### 3. FetchService.java - 实现fetchFlavor方法
- 使用 `CloudClient.client.client(BccClient.class, request.getBody());` 方法
- 调用 `CloudClient.listFlavorSpec(bccClient, request.getBody())` 获取数据
- 调用 `Convert::convertFlavor` 转换数据

### 4. BaiduCloudRegister.java - 注册fetchFlavor
- 在 `onAfterLoadFetch()` 方法中注册 `FETCH_COMPUTE_FLAVOR` 操作
- 绑定到 `FetchService::fetchFlavor` 方法

## 转换类统一字段对照表
完成对照，参考阿里云实现进行字段映射。
