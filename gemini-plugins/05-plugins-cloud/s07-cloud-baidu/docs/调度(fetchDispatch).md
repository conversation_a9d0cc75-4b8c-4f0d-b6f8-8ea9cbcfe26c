# 调度(fetchDispatch) 说明文档

## 1. 功能说明
- 调度(fetchDispatch)主要用于平台自动化任务调度，如定时采集、资源同步等。
- 通过 AccountService、CloudService 等服务实现调度任务的创建、查询、模型获取等。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| dispatch_id| 调度任务ID      |
| name       | 调度名称        |
| type       | 调度类型        |
| cron       | 定时表达式      |
| status     | 状态           |
| create_time| 创建时间        |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 获取调度模型：AccountService::getFetchAddModel
- 创建调度任务：AccountService::createFetchDispatch
- 查询调度模型：AccountService::getFetchAddModel

## 4. 参数说明
- regionId：地域ID，必填
- accountId：账号ID，必填
- cron：定时表达式，必填
- 其他调度相关参数

## 5. 实现要点
- 统一通过 AccountService/CloudService 提供调度相关接口
- 支持多种调度类型（如定时、周期、一次性等）
- 保证异常捕获和日志输出，便于排查

---
如需扩展调度逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 