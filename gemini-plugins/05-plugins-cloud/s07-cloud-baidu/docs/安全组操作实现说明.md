# 安全组操作实现说明

## 概述

本文档说明了百度云安全组操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadComputeSecurityGroup` 方法编写了完整的实现。

## 实现内容

### 1. 使用 SecurityGroupService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/SecurityGroupService.java`

**主要功能**:
- 安全组创建 (`createSecurityGroup`)
- 安全组删除 (`deleteSecurityGroup`)
- 安全组绑定 (`bindSecurityGroup`)
- 安全组解绑 (`unbindSecurityGroup`)
- 安全组规则创建 (`createSecurityGroupRule`)
- 安全组规则删除 (`deleteSecurityGroupRule`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadComputeSecurityGroup` 方法
- 添加了安全组创建、删除、绑定、解绑、规则管理等操作的注册
- 配置了相应的参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 安全组操作模式 (实现)
```java
// 创建安全组
register(ActionType.CREATE_COMPUTE_SECURITYGROUP, SecurityGroupService::createSecurityGroup); // 创建安全组

// 删除安全组
register(ActionType.DELETE_COMPUTE_SECURITYGROUP, SecurityGroupService::deleteSecurityGroup) // 删除安全组
    .addTransferCloud("$.ci.openId", "$.securityGroupId");

// 绑定安全组
register(ActionType.BIND_COMPUTE_SECURITYGROUP, SecurityGroupService::bindSecurityGroup) // 绑定安全组
    .addTransferCloud("$.ci.openId", "$.instanceId")
    .addTransferCloud("$.model.resourceId", "$.securityGroupId");

// 解绑安全组
register(ActionType.UNBIND_COMPUTE_SECURITYGROUP, SecurityGroupService::unbindSecurityGroup) // 解绑安全组
    .addTransferCloud("$.ci.openId", "$.instanceId")
    .addTransferCloud("$.model.resourceId", "$.securityGroupId");

// 创建安全组规则
register(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::createSecurityGroupRule) // 创建安全组规则
    .addTransferCloud("$.ci.openId", "$.securityGroupId")
    .addTransferCloud("$.model.direction", "$.direction")
    .addTransferCloud("$.model.protocol", "$.protocol")
    .addTransferCloud("$.model.portRange", "$.portRange", false)
    .addTransferCloud("$.model.sourceIp", "$.sourceIp", false)
    .addTransferCloud("$.model.sourceGroupId", "$.sourceGroupId", false)
    .addTransferCloud("$.model.destIp", "$.destIp", false)
    .addTransferCloud("$.model.destGroupId", "$.destGroupId", false)
    .addTransferCloud("$.model.remark", "$.remark", false);

// 删除安全组规则
register(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::deleteSecurityGroupRule) // 删除安全组规则
    .addTransferCloud("$.ci.openId", "$.securityGroupId")
    .addTransferCloud("$.model.direction", "$.direction")
    .addTransferCloud("$.model.protocol", "$.protocol")
    .addTransferCloud("$.model.portRange", "$.portRange", false)
    .addTransferCloud("$.model.sourceIp", "$.sourceIp", false)
    .addTransferCloud("$.model.sourceGroupId", "$.sourceGroupId", false)
    .addTransferCloud("$.model.destIp", "$.destIp", false)
    .addTransferCloud("$.model.destGroupId", "$.destGroupId", false)
    .addTransferCloud("$.model.remark", "$.remark", false);
```

## 关键特性

### 1. 参数映射
- **创建操作**: 支持安全组创建
- **删除操作**: 将请求中的 `securityGroupId` 映射到操作
- **绑定/解绑操作**: 将请求中的 `instanceId` 和 `securityGroupId` 映射到操作
- **规则操作**: 将请求中的安全组规则参数映射到操作

### 2. 操作类型
- **安全组管理**: 支持创建、删除安全组
- **安全组绑定**: 支持将安全组绑定到云主机
- **安全组解绑**: 支持将安全组从云主机解绑
- **规则管理**: 支持创建、删除安全组规则

### 3. 参数验证
- 创建安全组时验证必需参数
- 删除安全组时验证必需参数：`securityGroupId`
- 绑定/解绑时验证必需参数：`instanceId`、`securityGroupId`
- 规则操作时验证必需参数：`securityGroupId`、`direction`、`protocol`

## 注意事项

### 1. SDK 支持
百度云SDK支持安全组的创建、删除、绑定、解绑、规则管理等操作，因此：
- `SecurityGroupService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 安全组操作暂未配置刷新任务
- 主要关注安全组的管理和规则配置功能
- 为未来添加刷新功能预留了扩展空间

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持安全组规则管理
- 支持批量操作
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 安全组管理
- 支持创建安全组
- 支持删除安全组

### 2. 安全组绑定
- 支持将安全组绑定到云主机
- 支持将安全组从云主机解绑

### 3. 安全组规则管理
- 支持创建安全组规则
- 支持删除安全组规则
- 支持配置规则的方向、协议、端口范围等参数

### 4. 规则参数支持
- 支持入站/出站规则
- 支持多种协议（TCP、UDP、ICMP等）
- 支持端口范围配置
- 支持源/目标IP地址配置
- 支持源/目标安全组配置
- 支持规则备注

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadComputeSecurityGroup` 方法编写了完整的代码。实现包括：

1. **使用了 `SecurityGroupService` 类** - 提供安全组操作的核心功能
2. **完善了注册配置** - 支持创建、删除、绑定、解绑、规则管理等操作
3. **配置了参数映射** - 确保操作参数的正确传递
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对安全组操作有良好的支持，因此实现了完整的功能，包括安全组管理和规则配置。 