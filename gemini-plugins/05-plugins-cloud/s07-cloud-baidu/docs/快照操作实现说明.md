# 快照操作实现说明

## 概述

本文档说明了百度云快照操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadStorageSnapshot` 方法编写了完整的实现。

## 实现内容

### 1. 使用 SnapshotService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/SnapshotService.java`

**主要功能**:
- 快照创建 (`createSnapshot`)
- 快照删除 (`deleteSnapshot`)
- 快照回滚 (`resetSnapshot`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadStorageSnapshot` 方法
- 添加了快照创建、删除、回滚等操作的注册
- 配置了相应的参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 快照操作模式 (实现)
```java
// 创建云硬盘快照
register(ActionType.CREATE_STORAGE_SNAPSHOT, SnapshotService::createSnapshot);

// 删除云硬盘快照
register(ActionType.DELETE_STORAGE_SNAPSHOT, SnapshotService::deleteSnapshot)
    .addTransferCloud("$.ci.openId", "$.snapshotId");

// 回滚云硬盘快照
register(ActionType.RESET_STORAGE_SNAPSHOT, SnapshotService::resetSnapshot)
    .addTransferCloud("$.ci.openId", "$.snapshotId")
    .addTransferCloud("$.model.diskId", "$.diskId");
```

## 关键特性

### 1. 参数映射
- **创建操作**: 支持快照创建
- **删除操作**: 将请求中的 `snapshotId` 映射到操作
- **回滚操作**: 将请求中的 `snapshotId` 和 `diskId` 映射到操作

### 2. 操作类型
- **快照管理**: 支持创建、删除快照
- **快照回滚**: 支持将快照回滚到云硬盘

### 3. 参数验证
- 创建快照时验证必需参数
- 删除快照时验证必需参数：`snapshotId`
- 回滚快照时验证必需参数：`snapshotId`、`diskId`

## 注意事项

### 1. SDK 支持
百度云SDK支持快照的创建、删除、回滚等操作，因此：
- `SnapshotService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 快照操作暂未配置刷新任务
- 主要关注快照的管理和回滚功能
- 为未来添加刷新功能预留了扩展空间

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持快照回滚功能
- 支持批量操作
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 快照管理
- 支持创建云硬盘快照
- 支持删除云硬盘快照

### 2. 快照回滚
- 支持将快照回滚到云硬盘
- 支持指定目标云硬盘

### 3. 快照安全
- 支持快照的创建和管理
- 支持快照的回滚和恢复

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadStorageSnapshot` 方法编写了完整的代码。实现包括：

1. **使用了 `SnapshotService` 类** - 提供快照操作的核心功能
2. **完善了注册配置** - 支持创建、删除、回滚等操作
3. **配置了参数映射** - 确保操作参数的正确传递
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对快照操作有良好的支持，因此实现了完整的功能，包括快照管理和回滚操作。 