# 安全组(fetchSecurityGroup) 采集实现说明

## 1. CloudClient.java
- 实现 fetchSecurityGroup 方法
- 获取 BccClient 实例，构造 ListSecurityGroupsRequest 请求对象，设置 regionId、分页参数等
- 调用 bccClient.listSecurityGroups(request) 获取响应
- 响应对象转为 JSONObject 返回

## 2. Convert.java - 实现 convertSecurityGroup 方法
- 字段映射如下：
| 字段      | 百度云字段           | 说明       |
|-----------|---------------------|------------|
| res_id    | securityGroupId+region | 资源ID   |
| open_id   | securityGroupId     | 安全组ID   |
| open_name | securityGroupName   | 安全组名称 |
| desc      | description         | 描述       |
| vpcId     | vpcId               | 所属VPC    |
| rules     | rules               | 安全组规则 |
| extend1   | -                   | 扩展字段1  |
| extend2   | -                   | 扩展字段2  |
| extend3   | -                   | 扩展字段3  |
- 使用 Map<Class, List> 返回值格式
- 通过 IdUtils.encryptId 生成资源ID
- 支持VPC关联、规则列表等扩展属性

## 3. FetchService.java - 实现 fetchSecurityGroup 方法
- 采集安全组信息，手动获取BccClient，构造ListSecurityGroupsRequest，设置分页参数，调用listSecurityGroups，转为JSONObject
- 调用Convert.convertSecurityGroup进行数据转换
- 调用BaseCloudService.fetchSend发送采集结果
- 支持分页和totalCount统计

## 4. 采集流程与参数说明
- regionId：地域ID，必填
- pageNumber：页码，从1开始，选填
- pageSize：每页数量，选填，建议50或100
- needTotalCount：是否统计总数，选填
- 返回值中 totalCount 表示总条数，pageSize 表示每页数量

## 5. 实现要点
- 字段映射需与平台统一模型对齐
- 兼容 securityGroups/securityGroupList 字段
- totalCount 支持全量统计
- 支持VPC关联、规则扩展
- 保证异常捕获和日志输出，便于排查

---
如需扩展采集逻辑，请参考 fetchInstance、fetchFlavor、fetchKeyPair 的实现风格。
