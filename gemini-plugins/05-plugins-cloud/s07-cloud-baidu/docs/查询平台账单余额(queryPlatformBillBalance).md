# 查询平台账单余额(queryPlatformBillBalance) 说明文档

## 1. 功能说明
- 查询平台账单余额(queryPlatformBillBalance)用于获取云平台账号的账单余额信息。
- 通过 PlatformService::queryBillBalance 实现账单余额的获取。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| account_id | 账号ID         |
| balance    | 账单余额        |
| currency   | 币种           |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 查询账单余额：PlatformService::queryBillBalance
- 返回余额及相关信息

## 4. 参数说明
- accountId：账号ID，必填
- 其他账单余额相关参数

## 5. 实现要点
- 统一通过 PlatformService 提供账单余额相关接口
- 支持多种币种
- 保证异常捕获和日志输出，便于排查

---
如需扩展账单余额逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 