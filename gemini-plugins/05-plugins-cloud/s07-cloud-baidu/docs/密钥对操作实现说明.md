# 密钥对操作实现说明

## 概述

本文档说明了百度云密钥对操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadComputeKeypair` 方法编写了完整的实现。

## 实现内容

### 1. 使用 KeypairService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/KeypairService.java`

**主要功能**:
- 密钥对创建 (`createKeypair`)
- 密钥对删除 (`deleteKeypair`)
- 密钥对绑定 (`bindKeypair`)
- 密钥对解绑 (`unbindKeypair`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadComputeKeypair` 方法
- 添加了密钥对创建、删除、绑定、解绑等操作的注册
- 配置了相应的参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 密钥对操作模式 (实现)
```java
// 创建密钥对
register(ActionType.CREATE_COMPUTE_KEYPAIR, KeypairService::createKeypair); // 创建密钥对

// 删除密钥对
register(ActionType.DELETE_COMPUTE_KEYPAIR, KeypairService::deleteKeypair) // 删除密钥对
    .addTransferCloud("$.ci.openId", "$.keypairId");

// 绑定密钥对
register(ActionType.ATTACH_COMPUTE_KEYPAIR, KeypairService::bindKeypair) // 绑定密钥对
    .addTransferCloud("$.ci.openId", "$.instanceId")
    .addTransferCloud("$.model.resourceId", "$.keypairId");

// 解绑密钥对
register(ActionType.DETACH_COMPUTE_KEYPAIR, KeypairService::unbindKeypair) // 解绑密钥对
    .addTransferCloud("$.ci.openId", "$.instanceId")
    .addTransferCloud("$.model.resourceId", "$.keypairId");
```

## 关键特性

### 1. 参数映射
- **创建操作**: 支持密钥对创建
- **删除操作**: 将请求中的 `keypairId` 映射到操作
- **绑定/解绑操作**: 将请求中的 `instanceId` 和 `keypairId` 映射到操作

### 2. 操作类型
- **密钥对管理**: 支持创建、删除密钥对
- **密钥对绑定**: 支持将密钥对绑定到云主机
- **密钥对解绑**: 支持将密钥对从云主机解绑

### 3. 参数验证
- 创建密钥对时验证必需参数
- 删除密钥对时验证必需参数：`keypairId`
- 绑定/解绑时验证必需参数：`instanceId`、`keypairId`

## 注意事项

### 1. SDK 支持
百度云SDK支持密钥对的创建、删除、绑定、解绑等操作，因此：
- `KeypairService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 密钥对操作暂未配置刷新任务
- 主要关注密钥对的管理和绑定功能
- 为未来添加刷新功能预留了扩展空间

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持密钥对绑定管理
- 支持批量操作
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 密钥对管理
- 支持创建密钥对
- 支持删除密钥对

### 2. 密钥对绑定
- 支持将密钥对绑定到云主机
- 支持将密钥对从云主机解绑

### 3. 密钥对安全
- 支持密钥对的生成和管理
- 支持密钥对的导入和导出

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadComputeKeypair` 方法编写了完整的代码。实现包括：

1. **使用了 `KeypairService` 类** - 提供密钥对操作的核心功能
2. **完善了注册配置** - 支持创建、删除、绑定、解绑等操作
3. **配置了参数映射** - 确保操作参数的正确传递
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对密钥对操作有良好的支持，因此实现了完整的功能，包括密钥对管理和绑定操作。 