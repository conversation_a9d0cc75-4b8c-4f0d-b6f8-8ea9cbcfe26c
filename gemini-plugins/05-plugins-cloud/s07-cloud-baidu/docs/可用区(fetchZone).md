# 可用区(fetchZone) 采集实现说明

## 1. 采集流程概述
- 通过 `FetchService.fetchZone` 方法采集百度云可用区（zone）信息。
- 调用百度云 BCC SDK 的 `listZones` 方法获取所有可用区数据。
- 采集结果通过 `Convert.convertZone` 方法转换为平台统一的 CI 模型。
- 支持分层调度、磁盘类型、规格等扩展信息。

## 2. FetchService.java
- `fetchZone` 方法实现：
  1. 构造 `DescribeRegionsRequest` 请求对象。
  2. 调用 `BccClient.listZones` 获取 zone 列表。
  3. 将响应转为 JSONObject，传递给 `Convert.convertZone`。
  4. 通过 `BaseCloudService.fetchSend` 发送采集结果。
  5. 支持异常捕获和日志输出。

## 3. Convert.java
- `convertZone` 方法实现：
  1. 遍历 `zones` 数组，提取 zoneId、zoneName、localName、diskTypes、instanceTypes、zoneStatus、zoneType 等字段。
  2. 生成 TmdbDevops（可用区）、TmdbDevopsLink（关联）、TmdbResourceSet（资源集）等对象。
  3. 支持磁盘类型、可用区状态等扩展信息。
  4. 所有 TmdbDevops 的 biz_id 统一用 BuilderDevops.builderId 生成，保证唯一性。

### 字段映射示例
| 百度云字段      | 平台字段         | 说明           |
| -------------- | --------------- | -------------- |
| zoneName       | devops_value    | 可用区ID       |
| localName      | devops_name     | 可用区名称     |
| diskTypes      | diskCategories  | 支持磁盘类型   |
| instanceTypes  | 规格关联        | 支持的规格ID   |
| zoneStatus     | zoneStatus      | 可用区状态     |
| zoneType       | zoneType        | 可用区类型     |

## 4. 采集参数与返回值
- 支持多区域、多可用区采集。
- 支持磁盘类型、规格等扩展字段。
- 返回值为平台统一的 BaseResponse，包含 TmdbDevops、TmdbDevopsLink、TmdbResourceSet。

## 5. 分层调度与扩展
- 支持分层调度（如需对 zone 下资源进一步采集，可通过分层调度实现）。
- 可扩展更多 zone 相关属性和业务逻辑。

## 6. 异常处理与日志
- 全流程异常捕获，日志详细，便于排查。
- 若无可用区数据，返回空集合，保证接口兼容。

---
如需扩展采集逻辑，请参考 fetchInstance、fetchDisk 的实现风格。
如需对接更多 zone 维度的资源采集，可在 convertZone 方法中补充字段映射和关联关系。 