# 平台调度模型(fetchDispatchModel) 说明文档

## 1. 功能说明
- 平台调度模型(fetchDispatchModel)用于获取平台调度任务的模型信息。
- 通过 AccountService::getFetchAddModel 实现调度模型的获取。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| model_id   | 模型ID         |
| name       | 模型名称        |
| type       | 模型类型        |
| fields     | 字段列表        |
| create_time| 创建时间        |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 获取调度模型：AccountService::getFetchAddModel

## 4. 参数说明
- 无需特殊参数

## 5. 实现要点
- 统一通过 AccountService 提供调度模型相关接口
- 支持多种调度模型类型
- 保证异常捕获和日志输出，便于排查

---
如需扩展调度模型逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 