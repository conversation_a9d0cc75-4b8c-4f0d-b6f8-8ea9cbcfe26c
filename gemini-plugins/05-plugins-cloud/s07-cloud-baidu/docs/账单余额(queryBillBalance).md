# 账单余额(queryBillBalance) 说明文档

## 1. 功能说明
- 账单余额(queryBillBalance)用于查询云平台账号的账单余额信息。
- 通过 PlatformService::queryBillBalance 实现账单余额的获取。
- 使用百度云 BillingClient 调用 getAccountBalance() 方法获取账户余额。

## 2. 主要字段
| 字段         | 说明           | 数据来源                    |
|------------|--------------|--------------------------|
| account_id | 账号ID         | request.getBody().getAccess().getCmpId() |
| balance    | 账单余额        | GetAccountBalanceResponse.getBalance() |
| currency   | 币种           | 固定值 "CNY" (人民币)        |
| update_time| 更新时间        | System.currentTimeMillis() |
| extend1    | 扩展字段1       | GetAccountBalanceResponse.getAccountId() |
| extend2    | 扩展字段2       | GetAccountBalanceResponse.getAccountName() |
| extend3    | 扩展字段3       | GetAccountBalanceResponse.getStatus() |

## 3. 主要流程
- 查询账单余额：PlatformService::queryBillBalance
- 使用 CloudClient.client.client(BillingClient.class, request.getBody()) 创建客户端
- 调用 client.getAccountBalance() 获取余额信息
- 构建返回数据并封装为 BaseDataResponse

## 4. 参数说明
- accountId：账号ID，必填
- regionId：地域ID，用于构建账单服务端点
- 其他账单余额相关参数

## 5. 实现要点
- 统一通过 PlatformService 提供账单余额相关接口
- 使用百度云 BillingClient 进行API调用
- 支持多种币种（当前默认CNY）
- 保证异常捕获和日志输出，便于排查
- 返回数据格式与阿里云保持一致

## 6. API调用示例
```java
// 创建BillingClient
BillingClient client = CloudClient.client.client(BillingClient.class, request.getBody());

// 调用余额查询API
GetAccountBalanceResponse response = client.getAccountBalance();

// 获取余额信息
String balance = response.getBalance();
String accountId = response.getAccountId();
String accountName = response.getAccountName();
String status = response.getStatus();
```

---
如需扩展账单余额逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 