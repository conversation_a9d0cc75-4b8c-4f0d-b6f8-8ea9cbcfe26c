# 平台账号表单(accountAddForm) 说明文档

## 1. 功能说明
- 平台账号表单(accountAddForm)用于获取云平台账号的添加表单信息，支持多语言（如中英文）。
- 通过 AccountService::getAccountAddForm 实现表单的获取。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| form_id    | 表单ID         |
| name       | 表单名称        |
| fields     | 字段列表        |
| lang       | 语言类型        |
| create_time| 创建时间        |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 获取账号表单：AccountService::getAccountAddForm
- 支持多语言表单（如add_form_zh-CN.json、add_form_en-US.json）

## 4. 参数说明
- lang：语言类型，必填（如zh-CN、en-US）
- 其他表单相关参数

## 5. 实现要点
- 统一通过 AccountService 提供表单相关接口
- 支持多语言表单
- 保证异常捕获和日志输出，便于排查

---
如需扩展表单逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 