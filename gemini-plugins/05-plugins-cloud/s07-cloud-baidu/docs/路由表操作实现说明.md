# 路由表操作实现说明

## 概述

本文档说明了百度云路由表操作的实现，包括采集路由表，以及创建、修改、删除、绑定、解绑路由表功能。

## 实现内容

### 1. 使用 RouteService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/RouteService.java`

**主要功能**:
- 创建路由表 (`createRoute`)
- 修改路由表 (`updateRoute`)
- 删除路由表 (`deleteRoute`)
- 路由主备切换 (`switchRoute`)

**实现特点**:
- 使用百度云 RouteClient
- 包含参数验证和异常处理
- 提供详细日志记录
- 支持错误详情输出

### 2. 更新 FetchService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/sampler/FetchService.java`

**更新内容**:
- 添加 `fetchRoute` 方法采集路由表

### 3. 更新 Convert 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/common/Convert.java`

**更新内容**:
- 添加 `convertRoute` 方法转换路由表数据

### 4. 更新 BaiduRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduRegister.java`

**更新内容**:
- 更新 `onAfterLoadNeutronRoute` 方法注册操作

## 关键特性

- 支持路由表的CRUD和绑定操作
- 数据转换为 CmdbRouteRes 模型
- 详细的日志记录和错误处理
- 参数验证和异常捕获

## 注意事项

- 确保模型类 CmdbRouteRes 存在
- 操作需指定VPC
- 路由规则ID必须存在且有效
- 修改和删除操作需要正确的路由规则ID

## 错误处理

### 常见错误及解决方案

1. **404错误 - "The specified object is not found or resource do not exist"**
   - 原因：路由规则ID不存在或已被删除
   - 解决：检查传入的routeTableId参数是否正确

2. **参数验证失败**
   - 原因：缺少必需参数
   - 解决：确保传入所有必需参数

3. **认证失败**
   - 原因：访问密钥或权限不足
   - 解决：检查账号认证信息

## API参数说明

### 修改路由表参数
- `routeTableId`: 路由规则ID（必需）
- `sourceAddress`: 源地址（可选）
- `destinationAddress`: 目标地址（可选）
- `nexthopId`: 下一跳ID（可选）
- `description`: 描述（可选）

### 删除路由表参数
- `routeTableId`: 路由规则ID（必需）

### 路由主备切换参数
- `routeTableId`: 路由规则ID（必需）

## 总结

实现了百度云路由表的采集和操作功能，遵循项目规范，包含完善的错误处理和日志记录。 