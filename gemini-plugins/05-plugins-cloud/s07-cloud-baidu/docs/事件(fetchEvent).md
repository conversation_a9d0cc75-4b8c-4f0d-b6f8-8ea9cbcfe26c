# 事件(fetchEvent) 采集实现说明

## 1. CloudClient.java
- 实现 fetchEvent 方法
- 获取 BccClient 实例，调用 CloudClient::describeEvents 获取事件信息
- 响应对象转为 JSONObject 返回

## 2. Convert.java - 实现 convertEvent 方法
- 字段映射如下：
| 字段         | 百度云字段                | 说明           |
|------------|------------------------|--------------|
| res_id     | IdUtils.encryptId(cmpId, eventId, regionId) | 资源ID         |
| open_id    | eventId                | 事件ID         |
| open_name  | name                   | 事件名称        |
| desc       | description            | 描述           |
| status     | status                 | 事件状态        |
| type       | eventType              | 事件类型        |
| resource_id| resourceId             | 关联资源ID      |
| create_time| createTime             | 创建时间        |
| update_time| updateTime             | 更新时间        |
| region_id  | region                 | 区域ID         |
| extend1    | -                      | 扩展字段1       |
| extend2    | -                      | 扩展字段2       |
| extend3    | -                      | 扩展字段3       |
- 使用 Map<Class, List> 返回值格式
- 通过 IdUtils.encryptId 生成资源ID

## 3. FetchService.java - 实现 fetchEvent 方法
- 采集事件信息，支持分页参数
- totalCount 取当前页数量
- 调用 BaseCloudService.fetchAndSend 进行数据采集和转换

## 4. 采集流程与参数说明
- regionId：地域ID，必填
- pageNumber：页码，从1开始，选填
- pageSize：每页数量，选填，建议50或100
- needTotalCount：是否统计总数，选填
- 返回值中 totalCount 表示总条数，pageSize 表示每页数量

## 5. 实现要点
- 字段映射需与平台统一模型对齐
- totalCount 直接取当前页数量
- 保证异常捕获和日志输出，便于排查

---
如需扩展采集逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 