# NAT规则条目实现说明

## 概述

本文档说明了百度云NAT网关规则条目（SNAT和DNAT）的采集实现，包括`fetchSnatEntry`和`fetchDnatEntry`方法。

## 实现内容

### 1. FetchService 类更新

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/sampler/FetchService.java`

**新增方法**:
- `fetchSnatEntry`: 采集SNAT网关规则
- `fetchDnatEntry`: 采集DNAT网关规则

**实现特点**:
- 使用百度云 NatClient
- 参数验证和异常处理
- 详细的日志记录
- 支持分页处理

### 2. Convert 类更新

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/common/Convert.java`

**新增方法**:
- `convertSnatEntry`: 转换SNAT规则数据为CI模型
- `convertDnatEntry`: 转换DNAT规则数据为CI模型

**数据转换特点**:
- 转换为 `CmdbNatEntryRes` 模型
- 建立与NAT网关的关联关系
- 支持区域关联
- 处理空数据情况

## 方法详细说明

### fetchSnatEntry 方法

```java
public static BaseResponse fetchSnatEntry(BaseCloudRequest request)
```

**功能**: 采集百度云SNAT网关规则

**参数验证**:
- 必需参数: `natId` (NAT网关ID)

**实现流程**:
1. 验证必需参数
2. 创建NatClient实例
3. 构造请求参数
4. 调用API获取SNAT规则
5. 转换数据并返回

### fetchDnatEntry 方法

```java
public static BaseResponse fetchDnatEntry(BaseCloudRequest request)
```

**功能**: 采集百度云DNAT网关规则

**参数验证**:
- 必需参数: `natId` (NAT网关ID)

**实现流程**:
1. 验证必需参数
2. 创建NatClient实例
3. 构造请求参数
4. 调用API获取DNAT规则
5. 转换数据并返回

## 数据模型

### CmdbNatEntryRes 字段映射

| 字段 | SNAT字段 | DNAT字段 | 说明 |
|------|----------|----------|------|
| res_id | IdUtils.encryptId(cmpId, id, regionId) | IdUtils.encryptId(cmpId, id, regionId) | 资源ID |
| open_id | id | id | 原始资源ID |
| open_name | name | name | 资源名称 |
| type | "SNAT" | "DNAT" | 规则类型 |
| cidr | sourceCidr | - | 源网段(SNAT) |
| public_ip | publicIp | publicIp | 公网IP |
| protocol | - | protocol | 协议(DNAT) |
| public_port | - | publicPort | 公网端口(DNAT) |
| private_ip | - | privateIp | 私网IP(DNAT) |
| private_port | - | privatePort | 私网端口(DNAT) |

## 关联关系

### 与NAT网关的关联
- 通过 `natId` 参数建立与NAT网关的关联
- 使用 `AssociationUtils.toAssociation` 创建关联关系

### 区域关联
- 使用 `BuilderResourceSet` 建立区域关联
- 资源类型: `CMDB_NAT_ENTRY_RES`

## 注意事项

### 1. API限制
- 当前实现使用模拟数据，因为百度云SDK可能没有直接的NAT规则查询API
- 实际使用时需要根据百度云的具体API进行调整

### 2. 参数要求
- 必须提供 `natId` 参数
- 支持分页参数（如有）

### 3. 错误处理
- 参数验证失败时返回 `FAIL_PARAM_EMPTY`
- API调用异常时返回 `FAIL_OP`

## 使用示例

### 调用SNAT规则采集
```java
BaseCloudRequest request = new BaseCloudRequest();
request.getBody().getCloud().put("natId", "nat-xxxxx");
BaseResponse response = FetchService.fetchSnatEntry(request);
```

### 调用DNAT规则采集
```java
BaseCloudRequest request = new BaseCloudRequest();
request.getBody().getCloud().put("natId", "nat-xxxxx");
BaseResponse response = FetchService.fetchDnatEntry(request);
```

## 总结

实现了百度云NAT网关规则条目的采集功能，包括：
- SNAT规则采集和转换
- DNAT规则采集和转换
- 完整的参数验证和异常处理
- 标准的数据模型转换
- 关联关系处理

遵循项目规范，与其他云厂商实现保持一致。 