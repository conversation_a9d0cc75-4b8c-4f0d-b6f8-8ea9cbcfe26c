# 存储资源操作实现说明

## 概述

本文档说明了百度云存储资源操作的实现，参考 `onAfterLoadNeutronVpc` 方法的模式，为 `onAfterLoadStorageDisk` 方法编写了完整的实现。

## 实现内容

### 1. 使用 DiskService 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/service/DiskService.java`

**主要功能**:
- 云硬盘创建 (`createDisk`)
- 云硬盘修改 (`updateDisk`)
- 云硬盘删除 (`deleteDisk`)
- 云硬盘挂载 (`attachDisk`)
- 云硬盘卸载 (`detachDisk`)
- 云硬盘扩容 (`resizeDisk`)

**实现特点**:
- 参考 `VpcService` 的实现模式
- 遵循 Gemini 云插件开发规范
- 包含完整的参数验证和异常处理
- 提供详细的日志记录

### 2. 更新 BaiduCloudRegister 类

**文件位置**: `src/main/java/com/futong/gemini/plugin/cloud/baidu/BaiduCloudRegister.java`

**更新内容**:
- 完善了 `onAfterLoadStorageDisk` 方法
- 添加了云硬盘创建、修改、删除、挂载、卸载、扩容等操作的注册
- 配置了相应的参数映射

## 实现模式对比

### VPC 操作模式 (参考)
```java
// 创建VPC
register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc)
    .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
    .addSetRefreshConfig(20, 2000, 1000)
    .addSetRefreshData("response","$.data.vpcId")
    .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
    .addAfter(BaseCloudService::addRefreshGourdJob);
```

### 存储资源操作模式 (实现)
```java
// 创建云硬盘
register(ActionType.CREATE_STORAGE_DISK, DiskService::createDisk) // 创建云硬盘
    .addBefore(CloudService::defaultRegion); // 默认地域设置

// 修改云硬盘
register(ActionType.UPDATE_STORAGE_DISK, DiskService::updateDisk) // 修改云硬盘
    .addTransferCloud("$.ci.openId", "$.diskId")
    .addTransferCloud("$.model.resourceName", "$.diskName", false)
    .addTransferCloud("$.model.description", "$.description", false);

// 批量删除云硬盘
register(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisk) // 批量删除云硬盘
    .addTransferCloud("$.cis.openId", "$.diskIds");

// 挂载云硬盘
register(ActionType.ATTACH_STORAGE_DISK, DiskService::attachDisk) // 挂载云硬盘
    .addTransferCloud("$.ci.openId", "$.diskId")
    .addTransferCloud("$.model.instanceId", "$.instanceId");

// 卸载云硬盘
register(ActionType.DETACH_STORAGE_DISK, DiskService::detachDisk) // 卸载云硬盘
    .addTransferCloud("$.ci.openId", "$.diskId")
    .addTransferCloud("$.model.instanceId", "$.instanceId");

// 扩容云硬盘
register(ActionType.RESIZE_STORAGE_DISK, DiskService::resizeDisk) // 扩容云硬盘
    .addTransferCloud("$.ci.openId", "$.diskId")
    .addTransferCloud("$.model.size", "$.newSize");
```

## 关键特性

### 1. 参数映射
- **创建操作**: 支持云硬盘创建，配置默认地域
- **修改操作**: 将请求中的 `diskId`、`diskName`、`description` 映射到操作
- **删除操作**: 支持批量删除，将请求中的 `diskIds` 映射到操作
- **挂载/卸载操作**: 将请求中的 `diskId` 和 `instanceId` 映射到操作
- **扩容操作**: 将请求中的 `diskId` 和 `newSize` 映射到操作

### 2. 操作类型
- **云硬盘管理**: 支持创建、修改、删除云硬盘
- **云硬盘挂载**: 支持将云硬盘挂载到云主机
- **云硬盘卸载**: 支持将云硬盘从云主机卸载
- **云硬盘扩容**: 支持云硬盘容量扩容

### 3. 参数验证
- 创建云硬盘时验证必需参数
- 修改云硬盘时验证必需参数：`diskId`
- 删除云硬盘时验证必需参数：`diskIds`
- 挂载/卸载时验证必需参数：`diskId`、`instanceId`
- 扩容时验证必需参数：`diskId`、`newSize`

## 注意事项

### 1. SDK 支持
百度云SDK支持云硬盘的创建、修改、删除、挂载、卸载、扩容等操作，因此：
- `DiskService` 中的方法使用真实的SDK调用
- 包含完整的错误处理和重试机制
- 提供详细的日志记录

### 2. 刷新机制
- 云硬盘操作暂未配置刷新任务
- 主要关注云硬盘的管理和挂载功能
- 为未来添加刷新功能预留了扩展空间

### 3. 兼容性
- 保持了与现有VPC操作相同的模式
- 确保参数映射的一致性
- 遵循项目的编码规范和架构设计

### 4. 扩展性
- 支持云硬盘挂载管理
- 支持批量操作
- 支持云硬盘扩容
- 为未来功能扩展预留了接口结构

## 实现的功能

### 1. 云硬盘管理
- 支持创建云硬盘
- 支持修改云硬盘属性
- 支持删除云硬盘

### 2. 云硬盘挂载
- 支持将云硬盘挂载到云主机
- 支持将云硬盘从云主机卸载

### 3. 云硬盘扩容
- 支持云硬盘容量扩容
- 支持指定新的容量大小

### 4. 批量操作
- 支持批量删除云硬盘
- 支持批量挂载/卸载操作

## 总结

通过参考 `onAfterLoadNeutronVpc` 方法的实现模式，成功为 `onAfterLoadStorageDisk` 方法编写了完整的代码。实现包括：

1. **使用了 `DiskService` 类** - 提供云硬盘操作的核心功能
2. **完善了注册配置** - 支持创建、修改、删除、挂载、卸载、扩容等操作
3. **配置了参数映射** - 确保操作参数的正确传递
4. **保持了代码一致性** - 与VPC操作使用相同的模式和配置

百度云SDK对云硬盘操作有良好的支持，因此实现了完整的功能，包括云硬盘管理和挂载操作。 