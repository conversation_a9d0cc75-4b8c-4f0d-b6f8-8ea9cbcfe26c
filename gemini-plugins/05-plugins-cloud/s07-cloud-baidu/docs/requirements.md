# 百度云插件需求文档

## 项目概述

百度云插件 (s07-cloud-baidu) 是 Gemini 云平台插件系统中的重要组成部分，用于统一管理百度云平台的各类资源。该插件基于百度云 SDK v0.10.362 版本开发，支持百度云专有云 (BCC) 的资源管理和操作。

### 插件信息
- **插件名称**: cloud-private_baidu-v1.0.0
- **SDK版本**: 百度云 SDK v0.10.362
- **开发语言**: Java 1.8
- **构建工具**: Maven 3.6.0

## 支持的资源类型

### 1. 计算资源 (Compute)

#### 1.1 云主机 (BCC Instance)
- **资源类型**: `FETCH_COMPUTE_INSTANCE`
- **支持操作**:
  - 查询云主机列表
  - 创建云主机
  - 删除云主机
  - 启动/停止云主机
  - 重启云主机
  - 修改云主机属性
  - 修改云主机规格
  - 查询云主机VNC登录地址
  - 修改VNC登录密码
  - 查询云主机总数

#### 1.2 云主机规格 (Instance Flavor)
- **资源类型**: `FETCH_COMPUTE_FLAVOR`
- **支持操作**:
  - 查询实例规格列表
  - 查询可修改的云主机规格

#### 1.3 安全组 (Security Group)
- **资源类型**: `FETCH_COMPUTE_SECURITYGROUP`
- **支持操作**:
  - 查询安全组列表
  - 创建安全组
  - 删除安全组
  - 修改安全组属性
  - 管理安全组规则

#### 1.4 密钥对 (Keypair)
- **资源类型**: `FETCH_COMPUTE_KEYPAIR`
- **支持操作**:
  - 查询密钥对列表
  - 创建密钥对
  - 删除密钥对
  - 导入密钥对

### 2. 存储资源 (Storage)

#### 2.1 镜像 (Image)
- **资源类型**: `FETCH_STORAGE_IMAGE`
- **支持操作**:
  - 查询镜像列表
  - 创建自定义镜像
  - 删除镜像
  - 修改镜像属性
  - 复制镜像
  - 导出镜像
  - 管理镜像共享权限

#### 2.2 磁盘 (Disk)
- **资源类型**: `FETCH_STORAGE_DISK`
- **支持操作**:
  - 查询磁盘列表
  - 创建磁盘
  - 删除磁盘
  - 挂载/卸载磁盘
  - 扩容磁盘
  - 创建磁盘快照

#### 2.3 快照 (Snapshot)
- **资源类型**: `FETCH_STORAGE_SNAPSHOT`
- **支持操作**:
  - 查询快照列表
  - 创建快照
  - 删除快照
  - 从快照创建磁盘

### 3. 网络资源 (Network)

#### 3.1 VPC (Virtual Private Cloud)
- **资源类型**: `FETCH_NEUTRON_VPC`
- **支持操作**:
  - 查询VPC列表
  - 创建VPC
  - 删除VPC
  - 修改VPC属性

#### 3.2 子网 (Subnet)
- **资源类型**: `FETCH_NEUTRON_SUBNET`
- **支持操作**:
  - 查询子网列表
  - 创建子网
  - 删除子网
  - 修改子网属性

#### 3.3 弹性IP (EIP)
- **资源类型**: `FETCH_NEUTRON_EIP`
- **支持操作**:
  - 查询弹性IP列表
  - 申请弹性IP
  - 释放弹性IP
  - 绑定/解绑弹性IP

#### 3.4 网卡 (Network Interface)
- **资源类型**: `FETCH_NEUTRON_NIC`
- **支持操作**:
  - 查询网卡列表
  - 创建网卡
  - 删除网卡
  - 绑定/解绑网卡

### 4. 平台资源 (Platform)

#### 4.1 地域和可用区
- **资源类型**: `FETCH_PLATFORM_REGION`, `FETCH_PLATFORM_AZONE`
- **支持操作**:
  - 查询地域列表
  - 查询可用区列表

#### 4.2 账号管理
- **资源类型**: `AUTH_PLATFORM_ACCOUNT`
- **支持操作**:
  - 账号认证
  - 获取账号表单信息
  - 获取调度添加模型
  - 创建调度任务

#### 4.3 监控和告警
- **资源类型**: `FETCH_PLATFORM_ALARM`, `FETCH_PLATFORM_EVENT`
- **支持操作**:
  - 查询告警信息
  - 查询平台事件
  - 查询云主机监控数据

#### 4.4 账单管理
- **资源类型**: `FETCH_PLATFORM_BILL`, `FETCH_PLATFORM_BILL_DAY`, `QUERY_PLATFORM_BILL_BALANCE`
- **支持操作**:
  - 查询账单信息
  - 查询日账单
  - 查询账户余额

## API 接口规范

### 认证方式
- **认证类型**: AccessKey 认证
- **必需参数**:
  - `accessKeyId`: 访问密钥ID
  - `accessKeySecret`: 访问密钥Secret
  - `region`: 地域信息

### 服务域名
- **标准格式**: `{service}.{region}.baidubce.com`
- **示例**: `bcc.bj.baidubce.com` (北京地域BCC服务)

### 请求限制
- **默认分页**: 50条/页 (部分资源)
- **最大分页**: 100条/页 (部分资源)
- **请求频率**: 遵循百度云API限流规则

## 开发规范要求

### 方法签名规范

#### 默认参数设置方法
所有默认参数设置方法必须严格遵循以下规范，确保与框架兼容：

```java
// ✅ 正确的方法签名
public static boolean defaultRegion(BaseCloudRequest request) {
    request.getBody().getCloud().put("region", "bj"); // 默认使用北京地域
    return true;
}

public static boolean defaultPage50(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("pageNumber")) {
        request.getBody().getCloud().put("pageNumber", 1);//默认1
    }
    if (!request.getBody().getCloud().containsKey("pageSize")) {
        request.getBody().getCloud().put("pageSize", 50);//默认50
    }
    return true;
}

public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("startTime")) {
        request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 24 * 60 * 60 * 1000);//一天前
    }
    if (!request.getBody().getCloud().containsKey("endTime")) {
        request.getBody().getCloud().put("endTime", System.currentTimeMillis());//当前时间
    }
    return true;
}
```

#### 关键要求
1. **方法签名**：必须接收 `BaseCloudRequest request` 参数
2. **返回类型**：必须返回 `boolean` 类型
3. **参数设置**：通过 `request.getBody().getCloud().put(key, value)` 设置参数
4. **参数检查**：在设置参数前检查是否已存在，避免覆盖用户设置
5. **导入语句**：必须导入 `com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest`

#### 监控相关方法
```java
public static boolean defaultMetricRequest(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("startTime")) {
        request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 24 * 60 * 60 * 1000);//一天前
    }
    if (!request.getBody().getCloud().containsKey("endTime")) {
        request.getBody().getCloud().put("endTime", System.currentTimeMillis());//当前时间
    }
    return true;
}

public static boolean defaultInstanceMetricNames(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("metricNames")) {
        request.getBody().getCloud().put("metricNames", "CPUUtilization,MemoryUtilization,DiskUtilization");
    }
    return true;
}

public static boolean defaultInstanceMetricDimensions(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("dimensions")) {
        request.getBody().getCloud().put("dimensions", "instanceId");
    }
    return true;
}
```

#### 常见错误示例
```java
// ❌ 错误：无参数，返回void
public static void defaultRegion() {
    // 错误实现
}

// ❌ 错误：无参数，返回void
public static void defaultPage50() {
    // 错误实现
}

// ❌ 错误：无参数，返回void
public static void defaultStartEndTimeOneDay() {
    // 错误实现
}
```

### 配置要求

### 账号配置
插件需要在 `src/main/resources/account/` 目录下提供以下配置文件：

#### add_form_zh-CN.json (中文表单配置)
```json
{
  "formItems": [
    {
      "name": "accessKeyId",
      "label": "访问密钥ID",
      "type": "text",
      "required": true,
      "placeholder": "请输入百度云访问密钥ID"
    },
    {
      "name": "accessKeySecret", 
      "label": "访问密钥Secret",
      "type": "password",
      "required": true,
      "placeholder": "请输入百度云访问密钥Secret"
    },
    {
      "name": "region",
      "label": "地域",
      "type": "select",
      "required": true,
      "options": [
        {"label": "北京", "value": "bj"},
        {"label": "广州", "value": "gz"},
        {"label": "苏州", "value": "su"},
        {"label": "武汉", "value": "fsh"},
        {"label": "香港", "value": "hkg"},
        {"label": "新加坡", "value": "sin"}
      ]
    }
  ]
}
```

#### add_form_en-US.json (英文表单配置)
```json
{
  "formItems": [
    {
      "name": "accessKeyId",
      "label": "Access Key ID",
      "type": "text", 
      "required": true,
      "placeholder": "Enter Baidu Cloud Access Key ID"
    },
    {
      "name": "accessKeySecret",
      "label": "Access Key Secret", 
      "type": "password",
      "required": true,
      "placeholder": "Enter Baidu Cloud Access Key Secret"
    },
    {
      "name": "region",
      "label": "Region",
      "type": "select",
      "required": true,
      "options": [
        {"label": "Beijing", "value": "bj"},
        {"label": "Guangzhou", "value": "gz"},
        {"label": "Suzhou", "value": "su"},
        {"label": "Wuhan", "value": "fsh"},
        {"label": "Hong Kong", "value": "hkg"},
        {"label": "Singapore", "value": "sin"}
      ]
    }
  ]
}
```

#### add_dispatch.json (调度配置)
```json
{
  "schedules": [
    {
      "name": "fetch_compute_instance",
      "description": "同步云主机数据",
      "cron": "0 */5 * * * ?",
      "actionType": "FETCH_COMPUTE_INSTANCE"
    },
    {
      "name": "fetch_compute_flavor", 
      "description": "同步实例规格数据",
      "cron": "0 0 */2 * * ?",
      "actionType": "FETCH_COMPUTE_FLAVOR"
    },
    {
      "name": "fetch_storage_image",
      "description": "同步镜像数据", 
      "cron": "0 0 */4 * * ?",
      "actionType": "FETCH_STORAGE_IMAGE"
    },
    {
      "name": "fetch_storage_disk",
      "description": "同步磁盘数据",
      "cron": "0 */10 * * * ?", 
      "actionType": "FETCH_STORAGE_DISK"
    },
    {
      "name": "fetch_compute_securitygroup",
      "description": "同步安全组数据",
      "cron": "0 0 */6 * * ?",
      "actionType": "FETCH_COMPUTE_SECURITYGROUP"
    },
    {
      "name": "fetch_compute_keypair",
      "description": "同步密钥对数据",
      "cron": "0 0 */12 * * ?",
      "actionType": "FETCH_COMPUTE_KEYPAIR"
    }
  ]
}
```

## 技术实现要求

### 核心类结构
```
com.futong.gemini.plugin.cloud.baidu/
├── BaiduCloudRegister.java           # 插件注册类
├── BaiduCloudPluginTemplate.java     # 插件模板类
├── common/
│   ├── CloudClient.java              # 云客户端
│   ├── Constant.java                 # 常量定义
│   └── Convert.java                  # 数据转换工具
├── service/
│   ├── AccountService.java           # 账号服务
│   ├── CloudService.java             # 云服务基础类
│   ├── ComputeInstanceService.java   # 云主机服务
│   ├── DiskService.java              # 磁盘服务
│   ├── ImageService.java             # 镜像服务
│   ├── SecurityGroupService.java     # 安全组服务
│   ├── KeypairService.java           # 密钥对服务
│   └── PlatformService.java          # 平台服务
└── sampler/
    ├── FetchService.java             # 数据采集服务
    └── RefreshService.java           # 刷新服务
```

### 日志配置
```java
// 使用项目统一的日志配置
DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.baidu", "/cloud/baidu");
```

### 异常处理
```java
// 使用项目统一的异常处理
throw new BaseException(BaseResponse.ERROR_BIZ, e);
```

### 数据传输
```java
// 继承基础类
public class BaiduCloudClient extends BaseCloudClient {
    // 实现统一的客户端接口
}

public class BaiduCloudRegister extends BaseCloudRegister {
    // 实现统一的注册接口
}
```

## 服务注册

插件必须在 `src/main/resources/META-INF/services/` 目录下正确注册：

### 文件路径
`src/main/resources/META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`

### 文件内容
```
com.futong.gemini.plugin.cloud.baidu.BaiduPluginTemplate
```

## 开发注意事项

### 1. 数据格式统一
- 所有返回数据必须符合项目统一的数据模型
- 参考 `com.futong.gemini.model.otc.*` 包下的实体类
- 确保数据字段名称和类型的一致性

### 2. 错误处理
- 统一使用项目异常处理机制
- 记录详细的错误日志
- 提供友好的错误提示信息

### 3. 性能优化
- 合理使用连接池
- 避免频繁的API调用
- 实现数据缓存机制

### 4. 安全性
- 敏感信息不要硬编码
- 使用安全的认证方式
- 输入参数验证和过滤

## 测试要求

### 单元测试
- 覆盖核心业务逻辑
- 测试异常场景处理
- 验证数据转换正确性

### 集成测试
- 验证API调用正确性
- 测试资源操作流程
- 检查数据同步机制

### 性能测试
- 验证并发处理能力
- 测试大数据量处理
- 检查内存使用情况

## 部署和运维

### 构建配置
- 使用 Maven Shade Plugin 打包
- 排除冲突的依赖
- 确保插件包完整性

### 监控告警
- 配置插件运行状态监控
- 设置API调用失败告警
- 监控资源同步状态

### 日志管理
- 配置结构化日志输出
- 设置日志轮转策略
- 监控关键操作日志

## 版本历史

### v1.0.0 (计划中)
- 基础云主机管理功能
- 账号认证和配置
- 核心资源同步
- 基础操作支持

### 后续版本规划
- 增强监控和告警功能
- 优化性能和稳定性
- 支持更多资源类型
- 完善错误处理机制

## 参考文档

- [百度云开发者中心](https://cloud.baidu.com/doc/Developer/index.html)
- [百度云BCC API文档](https://cloud.baidu.com/doc/BCC/index.html)
- [项目开发标准](../code-standards/cloud-plugin-development-standards.md)
- [Java编码规范](../code-standards/java-coding-standards.md)
