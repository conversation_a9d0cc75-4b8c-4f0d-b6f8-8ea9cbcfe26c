# 平台账号(account) 说明文档

## 1. 功能说明
- 平台账号(account)用于云插件平台的账号管理、认证、表单获取等。
- 通过 AccountService、PlatformService 等服务实现账号认证、表单获取、账单余额查询等。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| account_id | 账号ID         |
| name       | 账号名称        |
| type       | 账号类型        |
| status     | 状态           |
| create_time| 创建时间        |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 账号认证：AccountService::authAccount
- 获取账号表单：AccountService::getAccountAddForm
- 获取调度模型：AccountService::getFetchAddModel
- 创建调度任务：AccountService::createFetchDispatch
- 查询账单余额：PlatformService::queryBillBalance

## 4. 参数说明
- accountId：账号ID，必填
- 其他账号相关参数

## 5. 实现要点
- 统一通过 AccountService/PlatformService 提供账号相关接口
- 支持多种账号类型
- 保证异常捕获和日志输出，便于排查

---
如需扩展账号逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 