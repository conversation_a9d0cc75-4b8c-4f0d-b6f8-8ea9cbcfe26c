# 平台账号认证(authAccount) 说明文档

## 1. 功能说明
- 平台账号认证(authAccount)用于对云平台账号进行认证校验，确保账号信息的有效性和可用性。
- 通过 AccountService::authAccount 实现账号认证。

## 2. 主要字段
| 字段         | 说明           |
|------------|--------------|
| account_id | 账号ID         |
| name       | 账号名称        |
| type       | 账号类型        |
| status     | 认证状态        |
| message    | 认证结果说明      |
| create_time| 创建时间        |
| update_time| 更新时间        |
| extend1    | 扩展字段1       |
| extend2    | 扩展字段2       |
| extend3    | 扩展字段3       |

## 3. 主要流程
- 账号认证：AccountService::authAccount
- 返回认证结果及说明

## 4. 参数说明
- accountId：账号ID，必填
- 其他账号认证相关参数

## 5. 实现要点
- 统一通过 AccountService 提供账号认证相关接口
- 支持多种账号类型
- 保证异常捕获和日志输出，便于排查

---
如需扩展账号认证逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 