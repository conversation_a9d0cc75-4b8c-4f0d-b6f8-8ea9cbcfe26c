# 地域(fetchRegion) 采集实现说明

## 1. CloudClient.java
- 实现 fetchRegion 方法
- 获取 BccClient 实例，构造 ListRegionsRequest 请求对象
- 调用 bccClient.listRegions(request) 获取响应
- 响应对象转为 JSONObject 返回

## 2. Convert.java - 实现 convertRegion 方法
- 字段映射如下：
| 字段         | 百度云字段                | 说明           |
|------------|------------------------|--------------|
| res_id     | IdUtils.encryptId(cmpId, regionId) | 资源ID         |
| open_id    | regionId               | 地域ID         |
| open_name  | regionName             | 地域名称        |
| desc       | description            | 描述           |
| status     | status                 | 状态           |
| create_time| createTime             | 创建时间        |
| update_time| updateTime             | 更新时间        |
| extend1    | -                      | 扩展字段1       |
| extend2    | -                      | 扩展字段2       |
| extend3    | -                      | 扩展字段3       |
- 使用 Map<Class, List> 返回值格式
- 通过 IdUtils.encryptId 生成资源ID

## 3. FetchService.java - 实现 fetchRegion 方法
- 采集地域信息，手动获取BccClient，构造ListRegionsRequest，调用listRegions，转为JSONObject
- 调用Convert.convertRegion进行数据转换
- 调用BaseCloudService.fetchSend发送采集结果
- 支持分层调度，自动生成采集可用区任务
- 使用到TmdbDevops.biz_id的地方，全改成devops.setBiz_id(BuilderDevops.builderId(devops.getAccount_id(),devops.getCloud_type(),devops.getDict_code(),devops.getDevops_value()));

## 4. 采集流程与参数说明
- 无需分页参数
- 返回值中 totalCount 表示总条数

## 5. 实现要点
- 字段映射需与平台统一模型对齐
- 支持分层调度，自动生成采集可用区任务
- 保证异常捕获和日志输出，便于排查

---
如需扩展采集逻辑，请参考 fetchInstance、fetchDisk 的实现风格。 