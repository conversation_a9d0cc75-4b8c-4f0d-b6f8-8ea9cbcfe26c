# 密钥对(fetchKeyPair) 采集实现说明

## 1. CloudClient.java
- 实现 fetchKeyPair 方法
- 获取 BccClient 实例，构造 ListKeypairsRequest 请求对象，设置分页参数
- 调用 bccClient.listKeypairs(request) 获取响应
- 响应对象转为 JSONObject 返回

## 2. Convert.java - 实现 convertKeypair 方法
- 字段映射如下：
| 字段         | 百度云字段                | 说明           |
|------------|------------------------|--------------|
| res_id     | IdUtils.encryptId(cmpId, keypairId, regionId) | 资源ID         |
| open_id    | keypairId              | 密钥对ID        |
| open_name  | keypairName            | 密钥对名称      |
| desc       | description            | 描述           |
| create_time| createTime             | 创建时间        |
| update_time| updateTime             | 更新时间        |
| region_id  | region                 | 区域ID         |
| extend1    | -                      | 扩展字段1       |
| extend2    | -                      | 扩展字段2       |
| extend3    | -                      | 扩展字段3       |
- 使用 Map<Class, List> 返回值格式
- 通过 IdUtils.encryptId 生成资源ID

## 3. FetchService.java - 实现 fetchKeyPair 方法
- 采集密钥对信息，手动获取BccClient，构造ListKeypairsRequest，设置分页参数，调用listKeypairs，转为JSONObject
- 调用Convert.convertKeypair进行数据转换
- 调用BaseCloudService.fetchSend发送采集结果

## 4. 采集流程与参数说明
- regionId：地域ID，必填
- pageNumber：页码，从1开始，选填
- pageSize：每页数量，选填，建议50或100
- needTotalCount：是否统计总数，选填
- 返回值中 totalCount 表示总条数，pageSize 表示每页数量

## 5. 实现要点
- 字段映射需与平台统一模型对齐
- 兼容 keypairs/keypairList 字段
- totalCount 直接取当前页数量
- 保证异常捕获和日志输出，便于排查

---
如需扩展采集逻辑，请参考 fetchInstance、fetchSnapshot 的实现风格。 