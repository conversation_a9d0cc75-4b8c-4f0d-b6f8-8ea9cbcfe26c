package com.futong.gemini.plugin.cloud.cas.e7.request;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.jna.Memory;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建虚拟机请求参数
 */
@Setter
@Getter
public class CreateVmRequest implements Serializable {

    //虚拟机名称
    private String domainName;
    //模版id
    private String id;

    private String targetHostId;

    private Integer cpuSocket;

    private String desc;

    private Integer cpuCore;

    private JSONObject memory;

    private String title;

    private String deployType;

    private String profileName;

    private Integer deployMode;

    private JSONArray storage;

    private JSONArray network;

    private boolean isPowerOn;
    private int vmNum;


}
