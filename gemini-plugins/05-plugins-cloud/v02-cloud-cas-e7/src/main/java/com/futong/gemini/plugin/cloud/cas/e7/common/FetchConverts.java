package com.futong.gemini.plugin.cloud.cas.e7.common;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.constant.dict.AlarmLevel;
import com.futong.constant.dict.CloudType;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasAlarm;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;

import java.util.List;

public class FetchConverts {
    public static AlarmInfoBean toAlarm(CasAlarm info) {
        AlarmInfoBean alarm = new AlarmInfoBean();
        try {
            alarm.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), info.getId(), info.getEvent_time()));
            alarm.setAccountId(BaseClient.auths.get().getCmpId());
            alarm.setCloudType(CloudType.VM_CAS.value());
            alarm.setOpenId(info.getId());
            alarm.setOpenName(info.getEvent_name());
            alarm.setOpenLevel(info.getEvent_level());
            alarm.setAlarmId("");
            alarm.setAlarmName(info.getEvent_name());
            alarm.setDetail(info.getEvent_desc());
            alarm.setClosedStatus(false);
            alarm.setCount(1);
            alarm.setFirstTime(info.getEvent_time().replace("T", " ").substring(0,19));
            alarm.setCreateTime(info.getEvent_time().replace("T", " ").substring(0,19));
            switch (info.getEvent_level()) {
                case "1":
                    alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case "2":
                    alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case "3":
                    alarm.setAlarmLevel(AlarmLevel.MINOR.value());
                    break;
                case "4":
                    alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                    alarm.setClosedStatus(true);
                    break;
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return alarm;
    }

    public static void createInstanceEventJob(JSONObject arguments, String instanceId, String type) {
        try {
            PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
            JSONObject req = new JSONObject();
            JSONObject instance = new JSONObject();
            instance.put("ids", new String[]{instanceId});
            instance.put("resourceType", type);
            instance.put("auth", arguments.getJSONObject("auth"));
            req.put("auth", arguments.getJSONObject("auth"));
            req.put("body", instance);
            req.put("action", ActionType.REFRESH_COMPUTE_INSTANCE.value());
            toJobInfo(plugin.getRealm(), plugin.getVersion(), req);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void toJobInfo(String realm, String version, JSONObject jobRequest) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(realm);
        jobInfo.setVersion(version);
        jobInfo.setCount(1);
        jobInfo.setRequest(jobRequest);
        jobInfo.setTriggerTime(System.currentTimeMillis() + 10000);
        SpringUtil.getBean(GourdProxy.class).createTempEventJob(jobInfo);
    }
}
