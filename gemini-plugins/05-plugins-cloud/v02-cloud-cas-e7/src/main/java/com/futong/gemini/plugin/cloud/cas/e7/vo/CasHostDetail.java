package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * cas原主机信息
 */
@Setter
@Getter
public class CasHostDetail extends CasVo{

    private String	user;
    private String	pwd;
    private String	hostPoolId;
    private String	clusterId;
    private String	name;
    private String	ip;
    private String	mask;
    private String	model;
    private String	vendor;
    private Integer	cpuCount;
    private String	cpuModel;
    private Integer	cpuFrequence;
    private Long	diskSize;
    private Integer	memorySize;
    private String	status;
    private Integer	cpuSockets;
    private Integer	cpuCores;
    private String	enableStorNode;
    private String	enableBackupNetwork;
    private List<PNIC> pNIC;
    private String	maintainMode;
    private String	serialNumber;
    private String	cvkMaintain;

    @Data
    public static class PNIC{
        private String	name;
        private String	description;
        private String	status;
        private String	macAddr;
    }
}
