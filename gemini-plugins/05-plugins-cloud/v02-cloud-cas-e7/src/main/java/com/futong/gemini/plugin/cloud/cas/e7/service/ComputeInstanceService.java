package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.cas.e7.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.model.snapshot.CreatsSnaspshot;
import com.futong.gemini.plugin.cloud.cas.e7.model.snapshot.ResumeSnaspshot;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.enums.JsonKeyEnum;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateVmRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.UpdateVmRequest;
import com.futong.gemini.plugin.cloud.cas.e7.util.CasUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.JobUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasHostDetail;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasVm;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbOsRes;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

@Slf4j
public class ComputeInstanceService {

    public static final ComputeInstanceService bean = new ComputeInstanceService();

    public static BaseResponse createSnapshot(JSONObject arguments) {
        String message = "成功发起创建快照请求.";
        CreatsSnaspshot snaspshot = BaseClient.bodys.get().toJavaObject(CreatsSnaspshot.class);
        if (ObjectUtil.isEmpty(snaspshot)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "创建快照参数不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getModel().getString("name"))) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "快照名称不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getCi().getOpen_id())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "vmId不能为空.");
        }
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmSnapshotUrl(), null);
        try {
            snaspshot.getModel().put("vmId",  snaspshot.getCi().getOpen_id());
            snaspshot.getModel().put("desc",  snaspshot.getModel().getString("description"));
            message = HttpClientUtil.post(url, snaspshot.getModel().toString(), new HttpClientConfig(), BaseClient.auths.get());
        } catch (Exception e) {
            log.error("创建快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建快照异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse resumeSnapshot(JSONObject arguments) {
        String message = "成功发起恢复快照请求.";
        ResumeSnaspshot snaspshot = BaseClient.bodys.get().toJavaObject(ResumeSnaspshot.class);
        if (ObjectUtil.isEmpty(snaspshot)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getCi().getOpen_name())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "快照名称不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getCi().getOpen_id())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "vmId不能为空.");
        }
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmSnapshotUrl(), new String[]{"resume"});
        log.info("url = {}", url);
        try {
            snaspshot.getModel().put("vmId",  snaspshot.getModel().getString("openId"));
            snaspshot.getModel().put("name",  snaspshot.getCi().getOpen_name());
            message = HttpClientUtil.put(url, snaspshot.getModel().toString(), new HttpClientConfig(), BaseClient.auths.get());
        } catch (Exception e) {
            log.error("恢复快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "恢复快照异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteSnapshot(JSONObject arguments) {
        String message = "成功发起删除快照请求.";
        ResumeSnaspshot snaspshot = BaseClient.bodys.get().toJavaObject(ResumeSnaspshot.class);
        if (ObjectUtil.isEmpty(snaspshot)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getCi().getOpen_name())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "快照名称不能为空.");
        }
        if (ObjectUtil.isEmpty(snaspshot.getModel().getString("openId"))) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "vmId不能为空.");
        }
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmSnapshotUrl(), new String[]{snaspshot.getModel().getString("openId"), snaspshot.getCi().getOpen_name()});
        log.info("url = {}", url);
        try {
            message = HttpClientUtil.delete(url, new HttpClientConfig(), BaseClient.auths.get());
        } catch (Exception e) {
            log.error("删除快照异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除快照异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse shutdownInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"stop");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse sleepInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"sleep");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse pauseInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"pause");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"delete");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse webConsole(JSONObject arguments) {
        String message = "操作成功.";
        String path = "/home/<USER>/";
        String protocols = "https";
        String port = "futong-novnc-port";
        String ip = "futong-zy-novnc";
        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        if(instanceIds==null||instanceIds.size()<=0) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "虚拟机参数为空");
        }
        String vmId = instanceIds.get(0);
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVncVmUrl(), new String[]{vmId});
        log.info("url = {}", url);
        try {
            message = HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
            cn.hutool.json.JSONObject map = JSONUtil.parseObj(message);
            String key = "cas_"+vmId;
            if(map!=null&&map.containsKey("ip")&&map.containsKey("port")) {
                String ip1 = map.getStr("ip");
                String port1 = map.getStr("port");
                String str = key+": "+ip1+":"+port1;

                File d = new File(path);
                if(!d.isDirectory()) {
                    d.mkdirs();
                }
                String fileName = path+"token_cas_"+vmId+".conf";
                File f = new File(fileName);
                if(!f.isFile()) {
                    f.createNewFile();
                }
                try (PrintWriter writer = new PrintWriter(fileName, "UTF-8")) {
                    writer.println(str);
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }
            String result = protocols+"://"+ip+":"+port+"/cmp-novnc/vnc_lite.html?host="+ip+"&port="+port+"&path=websockify/?token="+key;
            return new BaseDataResponse(result);
        } catch (Exception e) {
            log.error("获取控制台异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取控制台异常");
        }
    }

    public static BaseResponse resumeInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"restore");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse rebootInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"restart");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse stopInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"powerOff");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse startInstance(JSONObject arguments) {
        String message = "操作成功.";
        operateUrl(arguments,"start");
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse createInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas创建虚拟机接受参数={}", arguments.toString());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateVmUrl(), null);
        System.out.println(BaseClient.bodys.get());
        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
        try {
            CasUtils.bean.cloneVm(url,request,arguments);
        } catch (Exception e) {
            log.error("创建云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建云主机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    private static String powerOperationUrl(String action, String instanceId) {
        return URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmPowerOperationUrl(), new String[]{action, instanceId});
    }

    /**
     * 获取云主机列表信息
     *
     * @param request
     */
    public static List<JobInfo> fetchInstance(DescribeCasRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<JobInfo> jobs = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbInstanceRes> instances = new ArrayList<>();
        List<TmdbResourceSet> resourceTags = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        try {
            JSONArray array = new JSONArray();
            request.getIds().forEach(i -> {
                /**
                 * 同步云主机列表
                 */
                JSONArray vmArray = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmListUrl(), new String[]{"", i}),
                        ResourceEnum.DOMIAN.getValue());

                if (ObjectUtil.isNotEmpty(vmArray)) {
                    array.addAll(vmArray);
                    List<CmdbInstanceRes> vms = new ArrayList<>();
                    vmArray.forEach(vm -> {
                        JSONObject obj = (JSONObject) vm;
                        CasVm casVm = Converts.parseAndConvert(obj.toJSONString(), new TypeReference<CasVm>() {
                        });
                        CmdbInstanceRes instance = Converts.toNxcInstance(BaseClient.auths.get(), casVm);
                        vms.add(instance);
                        CmdbOsRes os = Converts.toNxcOs(BaseClient.auths.get(), casVm);
                        osList.add(os);
                        associations.add(AssociationUtils.toAssociation(instance, os));
                        resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_HOST_POOL.value(), TmdbDevops.class, casVm.getHostPoolId(), ResourceType.CMDB_INSTANCE_RES.value(), casVm.getId()));
                        resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, casVm.getClusterId(), ResourceType.CMDB_INSTANCE_RES.value(), casVm.getId()));
                    });
                    instances.addAll(vms);
                    try {
                        associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), i, new CmdbHostRes()), vms.stream().collect(Collectors.toList())));
                    } catch (Exception e) {
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.h3c.cas.fetch.host.vm.relation.fail"), e);
                    }
                }
            });
            /**
             * 拆分云主机dataJob获取磁盘、网卡信息
             */
            List<JobInfo> vmJobs = JobUtils.splitDataJob(request, array, arguments, new String[]{ResourceEnum.INSTANCE_DISK_NIC.getValue(), ResourceEnum.SNAPSHOT.getValue()}, JsonKeyEnum.ID.getValue());
            if (ObjectUtil.isNotNull(vmJobs))
                jobs.addAll(vmJobs);
        } catch (Exception e) {
            log.error("同步云主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.instance.fail"), e);
        }
        /**
         * 推送云主机数据
         */
        BaseUtils.sendMessage(instances, arguments);

        /**
         * 推送云主机与宿主机关系数据
         */
        log.info("推送云主机和宿主机关系数据 ={}", associations.size());
        BaseUtils.sendMessage(associations, arguments);

        // 推送云主机操作系统数据
        BaseUtils.sendMessage(osList, arguments);
        /**
         * 推送北新仓关联数据
         */
        BaseUtils.sendMessage(resourceTags, arguments);

        return jobs;
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    public static void operateUrl(JSONObject arguments,String type) {
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            String url = powerOperationUrl(type,instanceId);
            log.info("操作cas虚拟机url={}", url);
            try {
                if("delete".equals(type)) {
                    HttpClientUtil.delete(url, new HttpClientConfig(), BaseClient.auths.get());
                }else {
                    HttpClientUtil.put(url, null, new HttpClientConfig(), BaseClient.auths.get());
                }

                FetchConverts.createInstanceEventJob(arguments,instanceId,type);
            } catch (Exception e) {
                log.error("云主机操作异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "云主机操作异常");
            }
        }
    }

    public static BaseResponse updateInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas修改虚拟机接受参数={}", arguments.toString());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getUpdateVmUrl(), null);
        UpdateVmRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(UpdateVmRequest.class);
        try {
            CasUtils.bean.updateVm(url,request);
        } catch (Exception e) {
            log.error("修改云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改云主机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse refleshInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("触发更新虚拟机状态");
        CloudAccessBean accessBean = BaseClient.auths.get();
        DescribeCasRequest request = BaseClient.bodys.get().toJavaObject(DescribeCasRequest.class);
        List<CmdbInstanceRes> instances = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> resourceTags = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        if("delete".equals(request.getResourceType())) {
            request.getIds().forEach(i -> {
                CmdbInstanceRes instance = new CmdbInstanceRes();
                instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), i));
                instances.add(instance);

            });
            BaseCloudService.toRefreshMessageAndSend("delete", instances, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
        }else {
            request.getIds().forEach(i -> {
                String json  = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getVmPowerOperationUrl(),new String[]{"/"+i});
                CasVm casVm = Converts.parseAndConvert(json, new TypeReference<CasVm>() {
                });
                CmdbInstanceRes instance = Converts.toNxcInstance(BaseClient.auths.get(), casVm);
                instances.add(instance);
//                CmdbOsRes os = Converts.toNxcOs(BaseClient.auths.get(), casVm);
//                osList.add(os);
                log.info("发送创建虚拟机更新update----虚拟机id{}，{}", instance.getOpen_id(),casVm.getHostPoolId());
//                associations.add(AssociationUtils.toAssociation(instance, os));
                resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_HOST_POOL.value(), TmdbDevops.class, casVm.getHostPoolId(), ResourceType.CMDB_INSTANCE_RES.value(), casVm.getId()));
                resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, casVm.getClusterId(), ResourceType.CMDB_INSTANCE_RES.value(), casVm.getId()));
                try {
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), i, new CmdbHostRes()), instances.stream().collect(Collectors.toList())));
                } catch (Exception e) {
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.h3c.cas.fetch.host.vm.relation.fail"), e);
                }
            });
            log.info("发送创建虚拟机更新update----虚拟机条数{}，资源条数{}", instances.size(),resourceTags.size());
            BaseCloudService.toRefreshMessageAndSend("update", instances, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
            BaseCloudService.toRefreshMessageAndSend("update", associations, Association.class,accessBean.getCloudType(),accessBean.getCmpId());
//            BaseCloudService.toRefreshMessageAndSend("update", osList, CmdbOsRes.class,accessBean.getCloudType(),accessBean.getCmpId());
            BaseCloudService.toRefreshMessageAndSend("update", resourceTags, TmdbResourceSet.class,accessBean.getCloudType(),accessBean.getCmpId());
        }

        return BaseResponse.SUCCESS;
    }

    public int queryInstanceTotal() {
        JSONArray hostArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), null),
                ResourceEnum.HOST.getValue());
        int count = 0;
        for(int i=0;i<hostArray.size();i++) {
            JSONObject host = hostArray.getJSONObject(i);
            JSONArray vmArray = Converts.fetchResourceToJsonArray(
                    URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVmListUrl(), new String[]{"", host.getString("id")}),
                    ResourceEnum.DOMIAN.getValue());
            count += vmArray.size();
        }
        return count;
    }

    public JSONObject queryHostTotal() {
        JSONObject data = new JSONObject();
        JSONArray hostArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), null),
                ResourceEnum.HOST.getValue());
        int count = 0;
        int cpuCount = 0;
        int memoryCount = 0;

        if(hostArray!=null) {
            count = hostArray.size();
            for (int i = 0; i < hostArray.size(); i++) {
                JSONObject host = hostArray.getJSONObject(i);
                String json = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getHostDetailUrl(), new String[]{host.getString("id")});
                CasHostDetail casHost = Converts.parseAndConvert(json, new TypeReference<CasHostDetail>() {
                });
                cpuCount += casHost.getCpuCount();
                memoryCount += casHost.getMemorySize() / 1024;
            }
        }
        data.put("count",count);
        data.put("cpuCount",cpuCount);
        data.put("memoryCount",memoryCount);
        return data;
    }

}
