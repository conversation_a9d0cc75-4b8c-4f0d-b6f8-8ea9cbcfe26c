package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateSecurityGroupRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateVswitchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.util.CasUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRule;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class NetworkSecurityGroupService {

    public static final NetworkSecurityGroupService bean = new NetworkSecurityGroupService();


    public void fetchSecurityGroup(JSONObject arguments) {

        JSONArray securityGroupArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getSecurityGroupListUrl(),null),
                ResourceEnum.VIRTUAL_FIRE_WALL.getValue());
        List<CmdbSecuritygroupRes> list = new ArrayList<>();
        List<CmdbSecuritygroupRule> rules = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(securityGroupArray)){
            securityGroupArray.forEach(securityGroup ->{
                JSONObject securityGroupObj = (JSONObject) securityGroup;
                /**转换安全组*/
                CmdbSecuritygroupRes group = Converts.toNxcSecurityGroup(BaseClient.auths.get(), securityGroupObj);
                list.add(group);
                /**转换安全组规则*/
                List<CmdbSecuritygroupRule> ruleRes = new ArrayList<>();
                Converts.toNxcSecurityGroupRule(ruleRes,BaseClient.auths.get(), securityGroupObj);
                if(ObjectUtil.isNotEmpty(ruleRes)){
                    rules.addAll(ruleRes);
                    /**安全组与安全组规则数据*/
                    associations.add(AssociationUtils.toAssociation(group,ruleRes.stream().collect(Collectors.toList())));
                }
            });
        }
        /**
         * 推送安全组数据
         */
        BaseUtils.sendMessage(list, arguments);

        /**推送安全组规则数据*/
        BaseUtils.sendMessage(rules, arguments);

        /**推送安全组与安全组规则关系数据*/
        BaseUtils.sendMessage(associations, arguments);
    }

    public static BaseResponse createSecurityGroup(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas创建安全组接受参数={}", arguments.toString());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateSecurityGroupUrl(), null);
        CreateSecurityGroupRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(CreateSecurityGroupRequest.class);
        try {
            CasUtils.bean.createSecurityGroup(url,request);
        } catch (Exception e) {
            log.error("创建安全组异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建安全组异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteSecurityGroup(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas删除安全组接受参数={}", arguments.toString());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateSecurityGroupUrl(), null);
        CreateSecurityGroupRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(CreateSecurityGroupRequest.class);
        try {
            CasUtils.bean.deleteSecurityGroup(url,request);
        } catch (Exception e) {
            log.error("创建安全组异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除安全组异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


}
