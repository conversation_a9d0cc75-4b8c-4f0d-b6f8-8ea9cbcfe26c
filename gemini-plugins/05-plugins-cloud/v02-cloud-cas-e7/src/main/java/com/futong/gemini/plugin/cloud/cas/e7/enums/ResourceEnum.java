package com.futong.gemini.plugin.cloud.cas.e7.enums;

public enum ResourceEnum {

    /**
     * cas 宿主机
     */
    HOST("host"),
    /**
     *cas 实例
     */
    DOMIAN("domain"),

    /**
     * cas云主机
     */
    INSTANCE("instance"),

    VM("vm"),

    /**
     * 快照
     */
    SNAPSHOT("snapshot"),
    /**
     * cas云主机磁盘网卡
     */
    INSTANCE_DISK_NIC("instanceDiskNic"),

    /**
     * cas网络
     */
    NETWORK("network"),
    /**
     * cas虚拟交换机
     */
    VSWITCH("vSwitch"),

    IP("ip"),

    /**
    /**
     * cas虚拟防火墙
     */
    VIRTUAL_FIRE_WALL("rsVirFireWall"),

    /**
     * cas存储池
     */

    STORAGE_POOL("storagePool"),
    /**
     * cas存储
     */
    STORAGE("storage"),

    /** cas主机池 */
    HOSTPOOL("hostPool"),

    /** cas 集群 */
    CLUSTER("cluster"),
    ALARM("event"),
    VMSTORAGE("vmStorage"),
    VMNETWORK("vmNetwork"),
    SUBNET("subnet"),
    PORTPROFILE("portProfile");



    private String value;

    private ResourceEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
