package com.futong.gemini.plugin.cloud.cas.e7;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.function.FTAction;
import com.futong.gemini.plugin.cloud.cas.e7.sampler.FetchSampler;
import com.futong.gemini.plugin.cloud.cas.e7.service.*;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;

import java.util.HashMap;
import java.util.Map;

public class CasRegister {
    private static Map<String, FTAction> actions = new HashMap<>();


    public static <Q, R, C> void register(String fetchType, FTAction<JSONObject> ftAction) {
        actions.put(fetchType, ftAction);
    }

    public static boolean isNotExists(String action) {
        return !isExists(action);
    }

    public static boolean isExists(String action) {
        return actions.containsKey(action);
    }

    public static FTAction getAction(String action) {
        return actions.get(action);
    }

    public static void onAfterLoadCloudAccount() {
        register(ActionType.AUTH_PLATFORM_ACCOUNT.value(), PlatAuthService::authCloudAccount);//云账号验证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM.value(), AccountService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH.value(), AccountService::createFetchDispatch);//添加默认调度任务
    }
    public static void onAfterLoadFetch() {
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF.value(), PerfService::fetchInstancePerf);//同步云主机性能数据
        register(ActionType.FETCH_COMPUTE_HOST.value(), FetchSampler::fetchHost);//同步物理机
        register(ActionType.FETCH_COMPUTE_HOST_PERF.value(), PerfService::fetchHostPerf);//同步物理机性能数据
        register(ActionType.FETCH_COMPUTE_TEMPLATE.value(), FetchSampler::fetchTemplate);//同步模版
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP.value(), FetchSampler::fetchSecurityGroup);//同步安全组
        register(ActionType.FETCH_PLATFORM_HOSTPOOL.value(), FetchSampler::fetchHostpool);//同步主机池
        register(ActionType.FETCH_PLATFORM_CLUSTER.value(), FetchSampler::fetchCluster);//同步集群
        register(ActionType.FETCH_PLATFORM_ALARM.value(), FetchSampler::fetchAlarm);//获取告警
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL.value(), FetchSampler::queryInstanceTotal);//同步云主机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL.value(), FetchSampler::queryHostTotal);//同步物理机数量
    }
    public static void onAfterLoadOperate() {
        register(ActionType.CREATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::createInstance);//创建云主机
        register(ActionType.START_COMPUTE_INSTANCE.value(), ComputeInstanceService::startInstance);//启动云主机
        register(ActionType.STOP_COMPUTE_INSTANCE.value(), ComputeInstanceService::stopInstance);//关闭云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE.value(), ComputeInstanceService::rebootInstance);//重启云主机
        register(ActionType.RESUME_COMPUTE_INSTANCE.value(), ComputeInstanceService::resumeInstance);//恢复云主机
        register(ActionType.SHUTDOWN_COMPUTE_INSTANCE.value(), ComputeInstanceService::shutdownInstance);//安全关闭
        register(ActionType.HIBERNATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::sleepInstance);//休眠云主机
        register(ActionType.PAUSE_COMPUTE_INSTANCE.value(), ComputeInstanceService::pauseInstance);//暂停云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE.value(), ComputeInstanceService::deleteInstance);//删除云主机
        register(ActionType.CONSOLE_COMPUTE_INSTANCE.value(), ComputeInstanceService::webConsole);//控制台

        register(ActionType.CREATE_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::createSnapshot);//创建快照
        register(ActionType.DELETE_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::deleteSnapshot);//删除快照
        register(ActionType.RESUME_COMPUTE_SNAPSHOT.value(), ComputeInstanceService::resumeSnapshot);//恢复快照

        register(ActionType.UPDATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::updateInstance);//修改虚拟机

        register(ActionType.REFRESH_COMPUTE_INSTANCE.value(), ComputeInstanceService::refleshInstance);//更新云主机

        register(ActionType.QUERY_NETWORK_OUTPUT.value(), NetworkVswitchService::queryNetworkOutput);//获取网络出口


        register(ActionType.ADD_COMPUTE_HOST.value(), ComputeHostService::addHost);//添加主机
        register(ActionType.STOP_COMPUTE_HOST.value(), ComputeHostService::stopHost);//关闭主机
        register(ActionType.CONNECT_COMPUTE_HOST.value(), ComputeHostService::connectHost);//连接主机
        register(ActionType.DISCONNECT_COMPUTE_HOST.value(), ComputeHostService::disConnectHost);//断开主机
        register(ActionType.ENTER_COMPUTE_HOST.value(), ComputeHostService::enterHost);//进入维护模式
        register(ActionType.EXIT_COMPUTE_HOST.value(), ComputeHostService::exitHost);//退出维护模式
        register(ActionType.REMOVE_COMPUTE_HOST.value(), ComputeHostService::removeHost);//移除
        register(ActionType.REBOOT_COMPUTE_HOST.value(), ComputeHostService::rebootHost);//重启主机


        register(ActionType.DELETE_COMPUTE_TEMPLATE.value(), ComputeTemplateService::deleteTemplate);//删除模版

        register(ActionType.CREATE_NEUTRON_VSWITCH.value(), NetworkVswitchService::createVswith);//创建交换机
        register(ActionType.UPDATE_NEUTRON_VSWITCH.value(), NetworkVswitchService::updateVswith);//修改交换机
        register(ActionType.DELETE_NEUTRON_VSWITCH.value(), NetworkVswitchService::deleteVswith);//删除交换机

        register(ActionType.START_STORAGE_POOL.value(), StoragePoolService::startStoragePool);//存储启动
        register(ActionType.STOP_STORAGE_POOL.value(), StoragePoolService::stopStoragePool);//存储停止
        register(ActionType.DELETE_STORAGE_POOL.value(), StoragePoolService::deleteStoragePool);//删除存储池

        register(ActionType.CREATE_COMPUTE_SECURITYGROUP.value(), NetworkSecurityGroupService::createSecurityGroup);//创建安全组
//        register(ActionType.DELETE_COMPUTE_SECURITYGROUP.value(), NetworkSecurityGroupService::deleteSecurityGroup);//删除安全组
        register(ActionType.CREATE_STORAGE_FILE.value(), StorageDiskService::createStorageFile);//创建存储文件

    }
}
