package com.futong.gemini.plugin.cloud.cas.e7.convert;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.DiskStatus;
import com.futong.constant.dict.InstanceStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.entity.BaseSearchApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.PageSortSearchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.vo.*;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.DateUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.BaseConstant;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.DiskCategory;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.IpType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OsType;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class Converts {
    public static BasePageSortSearchRequest toBasePageSortSearchRequest(PageSortSearchRequest request) {
        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
        if (ObjectUtil.isNotNull(request)) {
            searchRequest.setCurrent(request.getCurrent() == null ? 1 : request.getCurrent());
            searchRequest.setSize(request.getSize() == null ? 50 : request.getSize());
            searchRequest.setSortField(request.getSortField() == null ? BaseConstant.RES_ID : request.getSortField());
            searchRequest.setSort(request.getSort() == null ? 0 : request.getSort());
        } else {
            searchRequest.setCurrent(1);
            searchRequest.setSize(50);
            searchRequest.setSortField(BaseConstant.RES_ID);
            searchRequest.setSort(0);
        }
        List<BaseSearchApiModel> searchList = new ArrayList<>();
        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
        searchApiModel.setValue(BaseClient.auths.get().getCmpId());
        searchApiModel.setSearchClassiy("0");
        searchList.add(searchApiModel);
        searchRequest.setSearchList(searchList);
        searchRequest.setSearchList(searchList);
        return searchRequest;
    }

    public static TmdbResourceSet toTmdbResourceSet(CloudAccessBean bean, String setType, Class c,String setId, String resourceType, String resourceId) {
        TmdbResourceSet set = new TmdbResourceSet();
        set.setAccount_id(bean.getCmpId());
        set.setCloud_type(bean.getCloudType());
        set.setResource_type(resourceType);
        set.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), resourceType, resourceId));
        set.setResource_type(resourceType);
        set.setSet_type(setType);
        set.setSet_table(AssociationUtils.otcTableName(c));
        set.setSet_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), setType, setId));
        set.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), set.getSet_id(), set.getResource_id()}));
        return set;
    }

    public static <T> T parseAndConvert(String jsonString, TypeReference<T> typeRef) {
        return JSON.parseObject(jsonString, typeRef);
    }

    public static JSONArray fetchResourceToJsonArray(String url, String key) {
        JSONArray array = new JSONArray();
        String responseJson = HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (HttpClientUtil.isJsonarray(JSONObject.parseObject(responseJson).get(key).toString())) {
            array = JSONObject.parseObject(responseJson).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(responseJson).getJSONObject(key));
        }
        return array;
    }

    public static JSONArray covertJsonArray(String json, String key) {
        JSONArray array = new JSONArray();
        if (ObjectUtil.isEmpty(json) || "null".equals(json) || ObjectUtil.isEmpty(JSONObject.parseObject(json).get(key)))
            return array;
        if (HttpClientUtil.isJsonarray(JSONObject.parseObject(json).get(key).toString())) {
            array = JSONObject.parseObject(json).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(json).getJSONObject(key));
        }
        return array;
    }

    public static CmdbHostRes toNxcHost(CloudAccessBean bean, CasHostDetail casHost) {
        CmdbHostRes nxcHostRes = new CmdbHostRes();
        /**
         * 封装资源唯一ID
         */
        nxcHostRes.setRes_id(IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), casHost.getId()}));
        nxcHostRes.setOpen_name(casHost.getName());
        nxcHostRes.setOpen_id(casHost.getId());
        nxcHostRes.setIp(casHost.getIp());
        nxcHostRes.setModel(casHost.getModel());
        nxcHostRes.setCpu_size(casHost.getCpuCount());
        nxcHostRes.setMem_size(casHost.getMemorySize() != null ? casHost.getMemorySize() : 0);
        nxcHostRes.setCloud_type(bean.getCloudType());
        nxcHostRes.setAccount_id(bean.getCmpId());
        nxcHostRes.setMaintain_mode(casHost.getMaintainMode());
        nxcHostRes.setManufacturer(casHost.getVendor());
        nxcHostRes.setSn(casHost.getSerialNumber());
        nxcHostRes.setTotal_size(Float.parseFloat(NumberUtil.roundStr(casHost.getDiskSize()/1024.0f, 2)));
        switch (casHost.getStatus().toString()) {
            case "1":
                nxcHostRes.setStatus(InstanceStatus.RUNNING.value());
                break;
            case "0":
                nxcHostRes.setStatus(InstanceStatus.STOPPED.value());
                break;
            default:
                nxcHostRes.setStatus(InstanceStatus.UNKNOWN.value());
                break;
        }
        nxcHostRes.setOpen_status(casHost.getStatus());
        return nxcHostRes;
    }

    public static List<CmdbNetcardRes> toNxcHostCard(CloudAccessBean bean, CasHostDetail casHost) {
        List<CmdbNetcardRes> cardRes = new ArrayList<>();
        /**
         * 封装物理网卡信息
         */
        if (ObjectUtil.isNotEmpty(casHost.getPNIC())) {
            casHost.getPNIC().forEach(c -> {
                CmdbNetcardRes nxcCardRes = new CmdbNetcardRes();
                nxcCardRes.setModule(c.getDescription());
                nxcCardRes.setCloud_type(bean.getCloudType());
                nxcCardRes.setAccount_id(bean.getCmpId());
                nxcCardRes.setMac_address(c.getMacAddr());
                nxcCardRes.setOpen_name(c.getName());
                nxcCardRes.setCategory("host");
                nxcCardRes.setRes_id(IdUtils.encryptId(bean.getCmpId(), casHost.getId(), c.getMacAddr()));
                cardRes.add(nxcCardRes);
            });
        }
        return cardRes;
    }

    public static <T> void toTmdbDevops(CloudAccessBean bean, T info, BuilderDevops builder) {
        if (info instanceof CasHostpool) {//处理主机池
            TmdbDevops model = builder.get();
            CasHostpool hostpool = (CasHostpool) info;
            if (ObjectUtil.isNotNull(model))
                builder.withDevops(model, hostpool.getName(), hostpool.getId(), DevopsSide.DEVOPS_HOST_POOL.value());
            else
                builder.withInfo(bean.getCmpId(), bean.getCloudType(), hostpool.getName(), hostpool.getId(), DevopsSide.DEVOPS_HOST_POOL.value());
        } else if (info instanceof CasCluster) {//处理集群
            CasCluster cluster = (CasCluster) info;
            TmdbDevops parentDev = new TmdbDevops();
            parentDev.setCloud_type(bean.getCloudType());
            parentDev.setAccount_id(bean.getCmpId());
            parentDev.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), DevopsSide.DEVOPS_HOST_POOL.value(), cluster.getHostPoolId()}));
            builder.withChildren(parentDev, DevopsSide.DEVOPS_CLUSTER.value(), Arrays.asList(cluster), CasCluster::getName, CasCluster::getId);
        }
        builder.withJson(JSON.toJSONString(info));
    }

    public static CmdbInstanceRes toNxcTemplate(CloudAccessBean bean, CasTemplate info) {
        CmdbInstanceRes instance = new CmdbInstanceRes();
        JSONArray templateArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getTemplateUrl(),new String[]{"","0","100"}),
                ResourceEnum.DOMIAN.getValue());
        /**
         * 封装资源唯一ID
         */
        instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), info.getId()));
        instance.setOpen_id(info.getId());
        instance.setOpen_name(info.getName());
        instance.setDesc(info.getDescription());
        instance.setCpu_size(info.getCpu());
        instance.setMem_size(info.getMemory());
        instance.setCloud_type(bean.getCloudType());
        instance.setAccount_id(bean.getCmpId());
        instance.setIs_template(1);
//        instance.setCreate_time(DateUtils.getCreateDate());
        return instance;
    }

    public static CmdbSecuritygroupRes toNxcSecurityGroup(CloudAccessBean bean, JSONObject info) {
        CmdbSecuritygroupRes securitygroup = new CmdbSecuritygroupRes();
        securitygroup.setAccount_id(bean.getCmpId());
        securitygroup.setCloud_type(bean.getCloudType());
        securitygroup.setDesc(info.getString("description"));
        securitygroup.setOpen_id(info.getString("id"));
        securitygroup.setOpen_name(info.getString("name"));
        securitygroup.setTitle(info.getString("title"));
        securitygroup.setRule_action(info.getInteger("ruleAction"));
        securitygroup.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
        return securitygroup;
    }

    public static void toNxcSecurityGroupRule(List<CmdbSecuritygroupRule> rules, CloudAccessBean bean, JSONObject info) {
        JSONArray ruleArray = covertJsonArray(info.toJSONString(), "rule");
        if (ObjectUtil.isNotEmpty(ruleArray)) {
            ruleArray.forEach(r -> {
                JSONObject ruleObj = (JSONObject) r;
                CmdbSecuritygroupRule rule = new CmdbSecuritygroupRule();
                rule.setAccount_id(bean.getCmpId());
                rule.setCloud_type(bean.getCloudType());
                rule.setOpen_id(info.getString("id"));
                rule.setOpen_name(info.getString("name"));
                rule.setDirection(ruleObj.getString("direction"));
                rule.setPolicy(ruleObj.getString("priority"));
                if (ObjectUtil.isNotNull(ruleObj.get("portStart")) && ObjectUtil.isNotNull(ruleObj.get("portEnd"))) {
                    rule.setPort_range(ruleObj.getInteger("portStart") + "-" + ruleObj.getInteger("portEnd"));
                }
                rule.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SECURITYGROUP_RES.value(), info.getString("id"), ruleObj.getString("id")));
                rules.add(rule);
            });
        }
    }

    public static CmdbNetcardRes toNxcCard(CloudAccessBean bean, CasVmNic info, String vmId) {
        CmdbNetcardRes nxcCardRes = new CmdbNetcardRes();
        nxcCardRes.setCloud_type(bean.getCloudType());
        nxcCardRes.setAccount_id(bean.getCmpId());
        nxcCardRes.setMac_address(info.getMac());
        nxcCardRes.setCategory("vm");
        nxcCardRes.setType(info.getNetType());
        nxcCardRes.setModule(info.getDeviceModel());
        nxcCardRes.setIpv4_address(info.getIpAddr());
        nxcCardRes.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), vmId, info.getMac()));
        return nxcCardRes;
    }

    public static CmdbNetcardRes toNxcTempCard(CloudAccessBean bean, CasTempNic info, String vmId) {
        CmdbNetcardRes nxcCardRes = new CmdbNetcardRes();
        nxcCardRes.setCloud_type(bean.getCloudType());
        nxcCardRes.setAccount_id(bean.getCmpId());
        nxcCardRes.setMac_address(info.getMac());
        nxcCardRes.setOpen_name(info.getProfileName());
        nxcCardRes.setCategory("vm");
        nxcCardRes.setType(info.getVlan());
        nxcCardRes.setModule(info.getName());
        nxcCardRes.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), vmId, info.getMac()));
        return nxcCardRes;
    }

    public static CmdbSecuritygroupRes toCmdbSecuritygroupRes(CloudAccessBean bean, CasVmNic info) {
        CmdbSecuritygroupRes res = new CmdbSecuritygroupRes();
        res.setCloud_type(bean.getCloudType());
        res.setAccount_id(bean.getCmpId());
        res.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), info.getProfileId(), ResourceType.CMDB_SECURITYGROUP_RES.value()));
        return res;
    }

    public static CmdbIpRes toNxcIp(CloudAccessBean bean, CasVmNic info, String vmId) {
        CmdbIpRes nxcIp = new CmdbIpRes();
        nxcIp.setCloud_type(bean.getCloudType());
        nxcIp.setAccount_id(bean.getCmpId());
        nxcIp.setAddress(info.getIpAddr());
        nxcIp.setType(IpType.PRIVATE_IP.getValue());
        nxcIp.setRes_id(IdUtils.encryptId(bean.getCmpId(), vmId, info.getMac(), ResourceType.CMDB_IP_RES.value()));
        return nxcIp;
    }

    public static CmdbDiskRes toNxcDisk(CloudAccessBean bean, CasVmDisk info, String vmId) {
        CmdbDiskRes disk = new CmdbDiskRes();
        disk.setCloud_type(bean.getCloudType());
        disk.setAccount_id(bean.getCmpId());
        if (ObjectUtil.isNotNull(info.getDeviceName()) && "vda".equals(info.getDeviceName()))
            disk.setCategory(DiskCategory.SYSTEM.getValue());
        else
            disk.setCategory(DiskCategory.DATA.getValue());
        disk.setPath(info.getPath());
        disk.setSize(Float.valueOf(String.format("%.2f", info.getSize() / 1024.0)));
        disk.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), vmId, info.getPath()));
        disk.setType(info.getFormat());
        disk.setStatus(DiskStatus.IN_USE.value());
        disk.setOpen_name(info.getDeviceName());
        return disk;
    }

    public static CmdbDiskRes toNxcTempDisk(CloudAccessBean bean, CasTempDisk info, String vmId) {
        CmdbDiskRes disk = new CmdbDiskRes();
        disk.setCloud_type(bean.getCloudType());
        disk.setAccount_id(bean.getCmpId());
        if (ObjectUtil.isNotNull(info.getDevice()) && "vda".equals(info.getDevice()))
            disk.setCategory(DiskCategory.SYSTEM.getValue());
        else
            disk.setCategory(DiskCategory.DATA.getValue());
        disk.setStatus(DiskStatus.IN_USE.value());
        disk.setPath(info.getStoreFile());
        disk.setSize(Float.valueOf(String.format("%.2f", info.getCapacity() / 1024.0)));
        disk.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), vmId, info.getId()));
        disk.setType(info.getFormat());
        return disk;
    }

    public static CmdbOsRes toNxcOs(CloudAccessBean bean, CasVm info) {
        CmdbOsRes os = new CmdbOsRes();
        os.setCloud_type(bean.getCloudType());
        os.setAccount_id(bean.getCmpId());
        os.setOpen_name(info.getOsDesc());
        os.setOpen_id(IdUtils.encryptId(info.getOsDesc()));
        os.setFull_name(info.getOsDesc());
        if ("1".equals(info.getSystem()))
            os.setType(OsType.LINUX.getValue());
        else if ("0".equals(info.getSystem()))
            os.setType(OsType.WINDOWS.getValue());
        os.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_OS_RES.value(), info.getOsDesc()));
        return os;
    }

    public static CmdbInstanceRes toNxcInstance(CloudAccessBean bean, CasVm info) {
        CmdbInstanceRes instance = new CmdbInstanceRes();
        /**
         * 封装资源唯一ID
         */
        instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), info.getId()));
        instance.setOpen_id(info.getId());
        instance.setOpen_name(info.getTitle());
//        instance.setInstance_running_status(info.getVmStatus());
        instance.setDesc(info.getDescription());
        instance.setCpu_size(info.getCpu());
        if(info.getMemory()<512) {
            instance.setMem_size(info.getMemory()*1024);
        }else {
            instance.setMem_size(info.getMemory());
        }
        instance.setCloud_type(bean.getCloudType());
        instance.setAccount_id(bean.getCmpId());
        instance.setIs_template(0);
        if(info.getVmStatus()!=null) {
            switch (info.getVmStatus()) {
                case "running":
                    instance.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "shutOff":
                    instance.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "paused":
                    instance.setStatus("暂停");
                    break;
                default:
                    instance.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
            }
        }else {
            switch (info.getStatus()) {
                case "2":
                    instance.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "3":
                    instance.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "4":
                    instance.setStatus("暂停");
                    break;
                default:
                    instance.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
            }
        }
        instance.setOpen_status(info.getVmStatus());
//        instance.setCreate_time(dateut);
//        instance.setCreate_time(info.getCreateDate());
//        instance.setIs_template("false");
        return instance;
    }

    public static void toNxcSnapshot(List<CmdbSnapshotRes> snapshots, CloudAccessBean bean, JSONObject obj, CmdbSnapshotRes psnapshot, String vmId) {
        if (ObjectUtil.isNotNull(obj) && ObjectUtil.isNotNull(obj.get("snapshot")) && !"null".equals(obj.get("snapshot"))) {
            if (HttpClientUtil.isJsonarray(obj.get("snapshot").toString())) {
                JSONArray array = obj.getJSONArray("snapshot");
                array.forEach(s -> {
                    JSONObject jsonObject = (JSONObject) s;
                    snapshot(snapshots, bean, jsonObject, psnapshot, vmId);
                });
            } else {
                snapshot(snapshots, bean, obj.getJSONObject("snapshot"), psnapshot, vmId);
            }
        }
    }

    @SneakyThrows
    private static void snapshot(List<CmdbSnapshotRes> snapshots, CloudAccessBean bean, JSONObject snapshotObj, CmdbSnapshotRes psnapshot, String vmId) {
        CmdbSnapshotRes snapshot = new CmdbSnapshotRes();
        snapshot.setOpen_name(snapshotObj.getString("name"));
        snapshot.setCloud_type(bean.getCloudType());
        snapshot.setAccount_id(bean.getCmpId());
        snapshot.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), vmId, snapshot.getOpen_name()));
        snapshot.setDesc(snapshotObj.getString("desc"));
        snapshot.setCreate_time(DateUtils.strToTimestamp(snapshotObj.getString("creationTime")));
        if (ObjectUtil.isNotNull(psnapshot)) {
            snapshot.setP_snaps_id(psnapshot.getRes_id());
            snapshot.setP_snaps_name(psnapshot.getOpen_name());
        }
        snapshot.setOpen_id(IdUtils.encryptId(vmId, snapshot.getOpen_name()));
        snapshots.add(snapshot);
        if (ObjectUtil.isNotNull(snapshotObj.getJSONObject("snapshots"))) {
            toNxcSnapshot(snapshots, bean, snapshotObj.getJSONObject("snapshots"), snapshot, vmId);
        }
    }

    public static <T> CmdbVswitchRes toNxcSwitch(CloudAccessBean bean, T info) {
        if (info instanceof CasHostVswitch) {
            CasHostVswitch host = (CasHostVswitch) info;
            CmdbVswitchRes vswitch = new CmdbVswitchRes();
            vswitch.setRes_id(IdUtils.encryptId(bean.getCmpId(), host.getId(), ResourceType.CMDB_VSWITCH_RES.value()));
            vswitch.setMtu(host.getMtu());
            vswitch.setOpen_name(host.getName());
            vswitch.setCloud_type(bean.getCloudType());
            vswitch.setAccount_id(bean.getCmpId());
            vswitch.setType(ResourceEnum.HOST.getValue());
            vswitch.setOpen_id(host.getId());
            vswitch.setBond_mode(host.getBondMode());
            return vswitch;
        } else if (info instanceof CasClusterVswitch) {
            CasClusterVswitch cluster = (CasClusterVswitch) info;
            CmdbVswitchRes vswitch = new CmdbVswitchRes();
            vswitch.setRes_id(IdUtils.encryptId(bean.getCmpId(), cluster.getId(), ResourceType.CMDB_VSWITCH_RES.value()));
            vswitch.setOpen_name(cluster.getName());
            vswitch.setCloud_type(bean.getCloudType());
            vswitch.setAccount_id(bean.getCmpId());
            vswitch.setType(ResourceEnum.CLUSTER.getValue());
            vswitch.setOpen_id(cluster.getId());
            vswitch.setBond_mode(cluster.getMode());
            return vswitch;
        } else {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "不支持的资源类型");
        }
    }

    public static CmdbSubnetRes toNxcSubnet(CloudAccessBean bean, CasProfile info) {
        CmdbSubnetRes sub = new CmdbSubnetRes();
        sub.setRes_id(IdUtils.encryptId(bean.getCmpId(), info.getId(), ResourceType.CMDB_SUBNET_RES.value()));
        sub.setOpen_name(info.getName());
        sub.setCloud_type(bean.getCloudType());
        sub.setAccount_id(bean.getCmpId());
        sub.setType(ResourceEnum.SUBNET.getValue());
        sub.setOpen_id(info.getId());
        sub.setDesc(info.getDescription());
        return sub;
    }

    public static CmdbStoragePoolRes toNxcStoragePool(CloudAccessBean bean, CasStoragePool info, String hostId) {
        CmdbStoragePoolRes storage = new CmdbStoragePoolRes();
        if ("dir".equals(info.getType())) {
            storage.setRes_id(IdUtils.encryptId(bean.getCmpId(), hostId, info.getPath(), ResourceType.CMDB_STORAGE_POOL_RES.value()));
        } else {
            storage.setRes_id(IdUtils.encryptId(bean.getCmpId(), info.getPath(), ResourceType.CMDB_STORAGE_POOL_RES.value()));
        }
        storage.setType(info.getType());
        storage.setOpen_name(info.getName());
        storage.setCloud_type(bean.getCloudType());
        storage.setAccount_id(bean.getCmpId());
        storage.setTotal_size(info.getTotalSize() == null ? 0 : Float.valueOf(String.format("%.2f", info.getTotalSize() / 1024.0)));
        if (ObjectUtil.isNotNull(info.getFreeSize()) && info.getFreeSize() / 1024.0 <= storage.getTotal_size()) {
            storage.setUsed_size(storage.getTotal_size() - Float.valueOf(String.format("%.2f", info.getFreeSize() / 1024.0)));
        } else {
            storage.setUsed_size(0f);
        }
        storage.setAllocation_size(info.getAllocation() == null ? 0 : Float.valueOf(String.format("%.2f", info.getAllocation() / 1024.0)));
        storage.setStatus(info.getStatus());
        storage.setDesc(info.getTitle());
        storage.setPath(info.getPath());
        return storage;
    }
}
