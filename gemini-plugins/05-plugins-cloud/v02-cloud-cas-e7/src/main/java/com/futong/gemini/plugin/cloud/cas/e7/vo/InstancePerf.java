package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class InstancePerf {

    private String	id;
    private Float	cpuRate;
    private Float	memRate;
    private String	status;
    private String	uuid;
    private String	flag;
    private String	type;
    private String	deployed;
    private String	hoststatus;
    private String	hostHaEnable;
    private String	castoolsStatus;
    private String	antivirusEnable;
    private String	uptime;
    private String	autoMem;
    private Disk	disk;
    @Data
    public static class Disk{

        private String	device;
        private String	time;
        private Float	ioStat;
        private Float	readStat;
        private Float	writeStat;
        private Float	readReqest;
        private Float	writeReqest;
        private Float	readLatency;
        private Float	writeLatency;

    }

    private Net	net;
    @Data
    public static class Net{

        private String	mac;
        private String	time;
        private Float	readFlow;
        private Float	readPackets;
        private Float	writeFlow;
        private Float	writePackets;

    }
    private List<Partition> partition;
    @Data
    public static class Partition{
        private String	device;
        private Float	usage;
        private Float	size;
        private Float	usedSize;

    }
    private String	flowLink;
    private String	originate;
}
