package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateStorageFileRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateVswitchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.CasUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasVmDisk;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasVmNic;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class StorageDiskService {
    public static final StorageDiskService bean = new StorageDiskService();

    public static void fetchDiskNic(DescribeCasRequest request, JSONObject arguments) {
        if(ObjectUtil.isEmpty(request.getIds())){
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<CmdbNetcardRes> nics = new ArrayList<>();
        List<CmdbDiskRes>  disks = new ArrayList<>();
        List<CmdbIpRes>  ips = new ArrayList<>();
        List<Association>  associations = new ArrayList<>();
        try {
            request.getIds().forEach(id -> {
                /** 获取云主机详情 */
                String json  = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getVmDetailUrl(),new String[]{id});
                String netjson  = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getVmPowerOperationUrl(),new String[]{"/"+id,"network"});
                /** 提取云主机磁盘数据 */
                JSONArray diskArray = Converts.covertJsonArray(json, ResourceEnum.STORAGE.getValue());
                /** 提取云主机网卡数据 */
                JSONArray nicArray = Converts.covertJsonArray(netjson, ResourceEnum.NETWORK.getValue());
                /** 转换云主机磁盘以及关系数据 */
                fetchDisks(diskArray, id, disks,associations);
                /** 转换云主机网卡、IP以及关系数据 */
                fetchNics(nicArray, id, nics,ips,associations);
            });
        }catch (Exception e){
            log.error("同步云主机磁盘网卡资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.h3c.cas.fetch.instance.disk.relation.fail"), e);
        }

        log.info("推送磁盘、网卡数据条数 ={},{}",nics.size(),disks.size());
        /** 推送虚拟网卡数据 */
        BaseUtils.sendMessage(nics, arguments);
        /** 推送磁盘数据 */
        BaseUtils.sendMessage(disks, arguments);

        /** 推送IP数据 */
        BaseUtils.sendMessage(ips, arguments);
        /**推送云主机与磁盘、云主机与网卡、网卡与IP关系数据*/
        BaseUtils.sendMessage(associations, arguments);
    }

    private static void fetchDisks(JSONArray diskArray, String id, List<CmdbDiskRes> disks,List<Association>  associations) {
        try {
            if (ObjectUtil.isNotEmpty(diskArray)) {
                List<CmdbDiskRes> diskRes = new ArrayList<>();
                diskArray.forEach(disk -> {
                    JSONObject diskObj = (JSONObject) disk;
                    if (!"disk".equals(diskObj.getString("device"))) return;
                    /**转换磁盘*/
                    diskRes.add(Converts.toNxcDisk(BaseClient.auths.get(), Converts.parseAndConvert(diskObj.toString(), new TypeReference<CasVmDisk>() {}), id));
                });
                if(ObjectUtil.isNotEmpty(diskRes)){
                    /**云主机和磁盘关系*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id, CmdbInstanceRes.class.newInstance()) , diskRes.stream().collect(Collectors.toList())));
                    disks.addAll(diskRes);
                }
            }
        }catch (Exception e){
            log.error("同步磁盘资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.disk.fail"), e);
        }
    }
    private static void fetchNics(JSONArray nicArray, String id, List<CmdbNetcardRes> nics,List<CmdbIpRes>  ips,List<Association> associations) {
        try {
            if (ObjectUtil.isNotEmpty(nicArray)) {
                List<CmdbNetcardRes> netcardRes = new ArrayList<>();
                nicArray.forEach(nic -> {
                    JSONObject nicObj = (JSONObject) nic;
                    /**转换网卡*/
                    CmdbNetcardRes  card = Converts.toNxcCard(BaseClient.auths.get(), Converts.parseAndConvert(nicObj.toString(), new TypeReference<CasVmNic>() {}), id);
                    /**转换IP*/
                    CmdbIpRes  ipRes = Converts.toNxcIp(BaseClient.auths.get(), Converts.parseAndConvert(nicObj.toString(), new TypeReference<CasVmNic>() {}), id);

                    //添加云主机和IP的关系
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id,new CmdbInstanceRes()) , ipRes));

                    /**获取网卡关联的安全组数据*/
                    CmdbSecuritygroupRes securitygroupRes = Converts.toCmdbSecuritygroupRes(BaseClient.auths.get(), Converts.parseAndConvert(nicObj.toString(), new TypeReference<CasVmNic>() {}));

                    //添加云主机和安全组的关系
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id,new CmdbInstanceRes()) , securitygroupRes));

                    /**网卡和安全组关系*/
                    associations.add(AssociationUtils.toAssociation(securitygroupRes,card));

                    /**IP和网卡关系*/
                    associations.add(AssociationUtils.toAssociation(card,ipRes));
                    netcardRes.add(card);
                    ips.add(ipRes);
                });
                if(ObjectUtil.isNotEmpty(netcardRes)){
                    /**云主机和网卡关系*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id,CmdbInstanceRes.class.newInstance()) , netcardRes.stream().collect(Collectors.toList())));
                    nics.addAll(netcardRes);
                }
            }
        } catch (Exception e){
            log.error("同步虚拟网卡资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.nic.fail"), e);
        }
    }

    public static BaseResponse createStorageFile(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas创建存储文件接受参数={}", arguments.toString());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateStorageFileUrl(), null);
        CreateStorageFileRequest request = BaseClient.bodys.get().toJavaObject(CreateStorageFileRequest.class);
        try {
            CasUtils.bean.createStorageFile(url,request);
        } catch (Exception e) {
            log.error("创建存储文件异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建存储文件异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

}
