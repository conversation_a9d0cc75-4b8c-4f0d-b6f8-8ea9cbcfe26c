package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasVm {

    private String	id;
    private String	hostId;
    private String	clusterId;
    private String	hostPoolId;
    private String	name;
    private String	title;
    private String	hostName;
    private String	description;
    private Integer	memory;
    private Float	cpuRate;
    private Float	memRate;
    private Integer	cpu;
    private String	vmStatus;
    private String	status;
    private String	uuid;
    private String	system;
    private String	osDesc;
    private String	createDate;
    private String	flag;
    private String	type;
    private String	deployed;
    private String	haStatus;
    private String	haManage;
    private String	protectModel;
    private String	enable;
    private String	hoststatus;
    private String	hostHaEnable;
    private String	castoolsStatus;
    private String	castoolsVersion;
    private String	antivirusEnable;
    private String	uptime;
    private String	autoMem;
    private String	vmType;
    private String	originate;
}
