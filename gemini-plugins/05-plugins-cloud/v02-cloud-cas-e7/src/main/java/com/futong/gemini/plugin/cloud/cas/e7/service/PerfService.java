package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.entity.ResStoragePoolApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.PageSortSearchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.DateUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.JobUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

;

@Slf4j
public class PerfService {

    public static final PerfService bean = new PerfService();

    //同步物理机性能数据
    public static BaseResponse fetchHostPerf(JSONObject arguments) {
        String message = "成功获取物理机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取物理机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取物理机列表
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> response = ApiFactory.Api.res.listHostStoragePool(searchRequest);
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        long current = DateUtil.current();
        fetchAndSendIHostPerf(response.getData().getList(), arguments, current);

        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            log.info("执行拆分性能数据任务");
            return new GourdJobResponse(JobUtils.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }

    //同步云主机性能数据
    public static BaseResponse fetchInstancePerf(JSONObject arguments) {
        String message = "成功获取云主机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取云主机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取云主机列表
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> response = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }

        long current = DateUtil.current();
        //同步性能数据并推送到数据队列
        fetchAndSendInstancePerf(response.getData().getList(), arguments, current);

        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            log.info("执行拆分性能数据任务");
            return new GourdJobResponse(JobUtils.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }


    private static void fetchAndSendIHostPerf(List<ResHostStoragePoolApiModel> list, JSONObject arguments, long current) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        list.forEach(res -> {
            if ("1".equals(res.getOpen_status())) {
                String json = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getHostPerfUrl(), new String[]{res.getOpen_id(), "monitor"});
                JSONObject instancePerf = JSONObject.parseObject(json);
                if (ObjectUtil.isNotNull(instancePerf)) {
                    ResourcePerfDetail perf = initResourcePerfDetail(BaseClient.auths.get(), res, current);
                    perf.setCpuUsage(instancePerf.getFloat("cpuRate") == null ? 0 : instancePerf.getFloat("cpuRate").doubleValue());
                    perf.setMemUsage(instancePerf.getFloat("memRate") == null ? 0 : instancePerf.getFloat("memRate").doubleValue());
                    //磁盘IO
                    JSONArray diskArray = new JSONArray();
                    if (ObjectUtil.isNotNull(instancePerf.get("disk"))) {
                        if (HttpClientUtil.isJsonarray(instancePerf.get("disk").toString())) {
                            diskArray.addAll(instancePerf.getJSONArray("disk"));
                        } else
                            diskArray.add(instancePerf.getJSONObject("disk"));
                    }
                    Float writeStat = 0.0f;
                    Float readStat = 0.0f;
                    Float ioStat = 0.0f;
                    if (ObjectUtil.isNotEmpty(diskArray)) {
                        for (int i = 0; i < diskArray.size(); i++) {
                            JSONObject disk = diskArray.getJSONObject(i);
                            writeStat += disk.getFloat("wr_stat") == null ? 0 : disk.getFloat("wr_stat");
                            readStat += disk.getFloat("rd_stat") == null ? 0 : disk.getFloat("rd_stat");
                        }
                        perf.setDiskWrite(writeStat.doubleValue());
                        perf.setDiskRead(readStat.doubleValue());
                        perf.setDiskIo(readStat + writeStat.doubleValue());
                    }
                    //网卡IO
                    JSONArray netArray = new JSONArray();
                    if (ObjectUtil.isNotNull(instancePerf.get("net"))) {
                        if (HttpClientUtil.isJsonarray(instancePerf.get("net").toString())) {
                            netArray.addAll(instancePerf.getJSONArray("net"));
                        } else
                            netArray.add(instancePerf.getJSONObject("net"));
                    }
                    if (ObjectUtil.isNotEmpty(netArray)) {
                        Float readFlow = 0.0f;
                        Float writeFlow = 0.0f;
                        for (int i = 0; i < netArray.size(); i++) {
                            JSONObject net = netArray.getJSONObject(i);
                            readFlow += net.getFloat("rx_bytes_rate") == null ? 0 : net.getFloat("rx_bytes_rate");
                            writeFlow += net.getFloat("tx_bytes_rate") == null ? 0 : net.getFloat("tx_bytes_rate");
                        }
                        perf.setNetIn((double) (writeFlow / 1024));
                        perf.setNetOut((double) (readFlow / 1024));
                        perf.setNetIo(perf.getNetIn() + perf.getNetOut());
                    }
                    //磁盘使用率
                    if (ObjectUtil.isNotEmpty(res.getStorages())) {
                        Float size = 0.0f;
                        Float usedSize = 0.0f;
                        for (int i = 0; i < res.getStorages().size(); i++) {
                            ResStoragePoolApiModel storage = res.getStorages().get(i);
                            if ("dir".equals(storage.getType()))
                                continue;
                            size += storage.getTotal_size() == null ? 0 : storage.getTotal_size();
                            usedSize += storage.getUsed_size() == null ? 0 : storage.getUsed_size();
                        }
                        perf.setDiskSize(size.doubleValue());
                        if (size > 0 && size >= usedSize)
                            perf.setDiskUsage(Double.valueOf(String.format("%.2f", 100 * usedSize / size)));
                    }
                    perf.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRes_id(), current + ""));
                    perfList.add(perf);
                }
            }
        });
        //推送性能到数据队列
        BaseUtils.sendMessage(perfList, arguments);
    }

    private static void fetchAndSendInstancePerf(List<ResInstanceDiskApiModel> list, JSONObject arguments, long current) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        list.forEach(res -> {
            if ("running".equals(res.getOpen_status())) {
                String json = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getInstancePerfUrl(), new String[]{res.getOpen_id(), "monitor"});
                JSONObject instancePerf = JSONObject.parseObject(json);
                if (ObjectUtil.isNotNull(instancePerf)) {
                    ResourcePerfDetail perf = initResourcePerfDetail(BaseClient.auths.get(), res, current);
                    perf.setCpuUsage(instancePerf.getFloat("cpuRate") == null ? 0 : instancePerf.getFloat("cpuRate").doubleValue());
                    perf.setMemUsage(instancePerf.getFloat("memRate") == null ? 0 : instancePerf.getFloat("memRate").doubleValue());
                    //磁盘IO
                    JSONArray diskArray = new JSONArray();
                    if (ObjectUtil.isNotNull(instancePerf.get("disk"))) {
                        if (HttpClientUtil.isJsonarray(instancePerf.get("disk").toString())) {
                            diskArray.addAll(instancePerf.getJSONArray("disk"));
                        } else
                            diskArray.add(instancePerf.getJSONObject("disk"));
                    }
                    Float writeStat = 0.0f;
                    Float readStat = 0.0f;
                    Float ioStat = 0.0f;
                    if (ObjectUtil.isNotEmpty(diskArray)) {
                        for (int i = 0; i < diskArray.size(); i++) {
                            JSONObject disk = diskArray.getJSONObject(i);
                            writeStat += disk.getFloat("writeStat") == null ? 0 : disk.getFloat("writeStat");
                            readStat += disk.getFloat("readStat") == null ? 0 : disk.getFloat("readStat");
                            ioStat += disk.getFloat("ioStat") == null ? 0 : disk.getFloat("ioStat");
                        }
                        perf.setDiskWrite(writeStat.doubleValue());
                        perf.setDiskRead(readStat.doubleValue());
                        perf.setDiskIo(ioStat.doubleValue());
                    }
                    //网卡IO
                    JSONArray netArray = new JSONArray();
                    if (ObjectUtil.isNotNull(instancePerf.get("net"))) {
                        if (HttpClientUtil.isJsonarray(instancePerf.get("net").toString())) {
                            netArray.addAll(instancePerf.getJSONArray("net"));
                        } else
                            netArray.add(instancePerf.getJSONObject("net"));
                    }
                    if (ObjectUtil.isNotEmpty(netArray)) {
                        Float readFlow = 0.0f;
                        Float writeFlow = 0.0f;
                        for (int i = 0; i < netArray.size(); i++) {
                            JSONObject net = netArray.getJSONObject(i);
                            readFlow += net.getFloat("readFlow") == null ? 0 : net.getFloat("readFlow");
                            writeFlow += net.getFloat("writeFlow") == null ? 0 : net.getFloat("writeFlow");
                        }
                        perf.setNetIn(writeFlow.doubleValue());
                        perf.setNetOut(readFlow.doubleValue());
                        perf.setNetIo(perf.getNetIn() + perf.getNetOut());
                    }
                    //磁盘使用率
                    JSONArray partitionArray = new JSONArray();
                    if (ObjectUtil.isNotNull(instancePerf.get("partition"))) {
                        if (HttpClientUtil.isJsonarray(instancePerf.get("partition").toString())) {
                            partitionArray.addAll(instancePerf.getJSONArray("partition"));
                        } else {
                            partitionArray.add(instancePerf.getJSONObject("partition"));
                        }
                        Float diskSize = 0.0f;
                        Float diskUsed = 0.0f;
                        for (int i = 0; i < partitionArray.size(); i++) {
                            JSONObject partition = partitionArray.getJSONObject(i);
                            diskSize += partition.getFloat("size") == null ? 0 : partition.getFloat("size");
                            diskUsed += partition.getFloat("usedSize") == null ? 0 : partition.getFloat("usedSize");
                        }
                        perf.setDiskSize(diskSize.doubleValue());
                        if (diskSize > 0 && diskSize >= diskUsed)
                            perf.setDiskUsage(Double.valueOf(String.format("%.2f", 100 * diskUsed / diskSize)));
                    }
                    perf.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRes_id(), current + ""));
                    perfList.add(perf);
                }
            }
        });

        //推送性能到数据队列
        BaseUtils.sendMessage(perfList, arguments);
    }

    private static <T> ResourcePerfDetail initResourcePerfDetail(CloudAccessBean accessBean, T info, long writeTime) {
        ResourcePerfDetail perf = new ResourcePerfDetail();
        if (info instanceof ResInstanceDiskApiModel) {
            ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
            perf.setResId(vminfo.getRes_id());
            perf.setOpenId(vminfo.getOpen_id());
            perf.setOpenName(vminfo.getOpen_name());
            perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
            perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        } else if (info instanceof ResHostStoragePoolApiModel) {
            ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
            perf.setResId(host.getRes_id());
            perf.setOpenId(host.getOpen_id());
            perf.setOpenName(host.getOpen_name());
            perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
            perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
        }
        perf.setCloudType(accessBean.getCloudType());
        perf.setCreateTime(DateUtils.timestampToStr(writeTime));
        perf.setAccountId(accessBean.getCmpId());
        perf.setDiskSize(0d);
        perf.setCpuUsage(0d);
        perf.setMemUsage(0d);
        perf.setDiskUsage(0d);
        perf.setNetIn(0d);
        perf.setNetOut(0d);
        perf.setNetIo(0d);
        perf.setDiskIo(0d);
        perf.setDiskRead(0d);
        perf.setDiskWrite(0d);
        return perf;
    }
}
