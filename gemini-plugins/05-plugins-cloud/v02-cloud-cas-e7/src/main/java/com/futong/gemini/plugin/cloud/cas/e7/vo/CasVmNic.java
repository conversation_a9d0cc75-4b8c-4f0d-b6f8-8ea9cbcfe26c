package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasVmNic {
    private String	netType;
    private String	mac;
    private String	ipAddr;
    private String	ipv4Dhcp;
    private String	ipv6;
    private String	ipv6Dhcp;
    private String	vsId;
    private String	vsName;
    private String	vsMode;
    private String	deviceModel;
    private String	isKernelAccelerated;
    private String	vlan;
    private String	profileId;
    private String	profileName;
    private String	vlanType;
    private String	bindIp;
    private String	bindIpv6;

}
