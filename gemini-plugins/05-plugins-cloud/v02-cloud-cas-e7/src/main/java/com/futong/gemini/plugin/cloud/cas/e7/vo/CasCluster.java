package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasCluster {

    private String id;

    private String hostPoolId;

    private String hostPoolName;

    private String name;

    private String description;

    private String enableHA;

    private String priority;

    private String enableLB;

    private String persistTime;

    private String checkInterval;

    private String enableIPM;

    private String persistTimeIPM;

    private String checkIntervalIPM;

    private String operatorGroupId;

    private String operatorGroupCode;

    private String childNum;

    private String enableSLB;

    private String slbPersistTime;

    private String slbCheckInterval;

    private String enableStorageHA;

    private String haControlStrategy;

    private String enableBusinessHA;

    private String triggerAction;
}
