package com.futong.gemini.plugin.cloud.cas.e7.util;

import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;

public class CiResCloudUtils {

    public static <T> T toCiResCloud(CloudAccessBean bean, String id, T classBean) {
        if (classBean instanceof CmdbHostRes) {
            CmdbHostRes host = (CmdbHostRes) classBean;
            host.setCloud_type(bean.getCloudType());
            host.setAccount_id(bean.getCmpId());
            host.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), id));
            return (T) host;
        } else if (classBean instanceof CmdbInstanceRes) {
            CmdbInstanceRes instance = (CmdbInstanceRes) classBean;
            instance.setCloud_type(bean.getCloudType());
            instance.setAccount_id(bean.getCmpId());
            instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), id));
            return (T) instance;
        }
        return classBean;
    }
}
