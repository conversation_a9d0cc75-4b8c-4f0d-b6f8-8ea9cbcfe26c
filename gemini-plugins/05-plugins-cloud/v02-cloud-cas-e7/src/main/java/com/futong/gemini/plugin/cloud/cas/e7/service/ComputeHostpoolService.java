package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasHostpool;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ComputeHostpoolService {
    public static final ComputeHostpoolService bean = new ComputeHostpoolService();


    public void fetchHostpool(JSONObject arguments) {
        /**同步主机池资源数据*/
        JSONArray array = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostpoolListUrl(),null),
                ResourceEnum.HOSTPOOL.getValue());
        BuilderDevops builder = new BuilderDevops();
        List<TmdbDevopsLink> links = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp ->{
                JSONObject tempObj = (JSONObject) temp;
                /** 主机池资源数据模型转换 */
                Converts.toTmdbDevops(BaseClient.auths.get(), Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CasHostpool>() {}),builder);
                links.add(builder.builderLink(new TmdbDevops()));
            });
        }
        /**
         * 推送主机池数据
         */
        List<TmdbDevops> devops = new ArrayList<>();

        if(ObjectUtil.isNotNull(builder.get()))
            devops.add(builder.get());
        if(ObjectUtil.isNotEmpty(builder.getData()))
            devops.addAll(builder.getData());

        BaseUtils.sendMessage(devops, arguments);

        BaseUtils.sendMessage(links, arguments);
    }
}
