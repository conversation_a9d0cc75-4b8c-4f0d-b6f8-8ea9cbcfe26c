package com.futong.gemini.plugin.cloud.cas.e7.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;

public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**宿主机列表*/
    private  String[] hostUrl = { "/cas/casrs/host"};

    public  String[] getHostUrl() {
        return hostUrl.clone();
    }

    /**宿主机详情*/
    private  String[] hostDetailUrl = { "/cas/casrs/host/id/"};

    public  String[] getHostDetailUrl() {  return hostDetailUrl.clone(); }

    /**宿主机概览*/
    private  String[] hostOverviewUrl = { "/cas/casrs/host/overview/"};

    public  String[] getHostOverviewUrl() {  return hostOverviewUrl.clone(); }

    /**云主机列表*/
    private String[] vmListUrl = { "/cas/casrs/vm/vmList?","hostId"};

    public  String[] getVmListUrl() {  return vmListUrl.clone();  }

    /**创建云主机*/
    private String[] createVmUrl = { "/cas/casrs/vm/deploy"};

    public  String[] getCreateVmUrl() {  return createVmUrl.clone();  }

    /**获取任务*/
    private String[] taskUrl = { "/cas/casrs/message"};

    public  String[] getTaskUrl() {  return taskUrl.clone();  }

    /**创建云主机*/
    private String[] updateVmUrl = { "/cas/casrs/vm/modify"};

    public  String[] getUpdateVmUrl() {  return updateVmUrl.clone();  }

    /**创建交换机*/
    private String[] createVswitchUrl = { "/cas/casrs/vswitch/clusterVswitch"};

    private String[] createHostVswitchUrl = { "/cas/casrs/vswitch"};

    public  String[] getCreateVswitchUrl(String type) {
        if("host".equals(type)) {
            return createHostVswitchUrl.clone();
        }else {
            return createVswitchUrl.clone();
        }
    }

    private String[] queryHostNetportUrl = { "/cas/casrs/host"};

    public  String[] queryHostNetportUrl() {  return queryHostNetportUrl.clone();  }
    /**创建存储文件*/
    private String[] createStorageFileUrl = { "/cas/casrs/storage/volume"};

    public  String[] getCreateStorageFileUrl() {  return createStorageFileUrl.clone();  }

    /**创建交换机*/
    private String[] createSecurityGroupUrl = { "/cas/casrs/virFireWall"};

    public  String[] getCreateSecurityGroupUrl() {  return createSecurityGroupUrl.clone();  }


    /**vnc*/
    private String[] vncVmUrl = { "/cas/casrs/vmvnc/vnc/"};

    public  String[] getVncVmUrl() {  return vncVmUrl.clone();  }

    /**云主机详情*/
    private String[] vmDetailUrl = { "/cas/casrs/vm/detail/"};

    public  String[] getVmDetailUrl() {  return vmDetailUrl.clone();   }

    /**快照*/
    private String[] vmSnapshotUrl = { "/cas/casrs/vm/snapshot/"};

    public  String[] getVmSnapshotUrl() {  return vmSnapshotUrl.clone(); }

    /**模版*/
    private String[] templateUrl = { "/cas/casrs/vmTemplate/vmTemplateList?","offset","limit"};

    public  String[] getTemplateUrl() {
        return templateUrl.clone();
    }

    private String[] delTemplateUrl = { "/cas/casrs/vmTemplate/deleteVmTemplate?","id"};

    public  String[] getDelTemplateUrl() {
        return delTemplateUrl.clone();
    }

    private String[] templateDiskUrl = { "/cas/casrs/vmTemplate/storage?","vmId"};

    public  String[] getTemplateDiskUrl() {
        return templateDiskUrl.clone();
    }

    private String[] templateNicUrl = { "/cas/casrs/vmTemplate/network?","vmId"};

    public  String[] getTemplateNicUrl() {
        return templateNicUrl.clone();
    }

    /**存储池*/
    private String[] storagePoolUrl = new String[]{"/cas/casrs/storage/pool?", "hostId"};

    public String[] getStoragePoolUrl() {
        return this.storagePoolUrl.clone();
    }

    public String[] getStorageHostUrl(String type) {

        return new String[]{"/cas/casrs/storage/"+type+"?","id","poolName","hostName"}.clone();
    }

    /**宿主机关联交换机*/
    private String[] hostVswitchUrl = new String[]{"/cas/casrs/vswitch?", "hostId"};

    public String[] getHostVswitchUrl() {
        return this.hostVswitchUrl.clone();
    }

    private String[] vswitchUrl = new String[]{"/cas/casrs/vswitch/"};

    public String[] getVswitchUrl() {
        return this.vswitchUrl.clone();
    }


    private String[] clusterVswitchUrl = new String[]{"/cas/casrs/vswitch?", "clusterId"};

    public String[] getClusterVswitchUrl() {
        return this.clusterVswitchUrl.clone();
    }

    private String[] hostSubnetUrl = new String[]{"/cas/casrs/vswitch/internalPort?", "hostId","vswitchId"};

    public String[] getHostSubnetUrl() {
        return this.hostSubnetUrl.clone();
    }

    private String[] profileUrl = new String[]{"/cas/casrs/profile/all"};

    public String[] getProfileUrl() {
        return this.profileUrl.clone();
    }


    /**安全组列表*/
    private String[] securityGroupListUrl = new String[]{"/cas/casrs/virFireWall/all"};

    public String[] getSecurityGroupListUrl() {
        return this.securityGroupListUrl.clone();
    }

    private String[] alarmListUrl = new String[]{"/cas/casrs/warnManage/warnManageList","offset","limit","eventTime_from","eventTime_to"};

    public String[] getAlarmListUrl() {
        return this.alarmListUrl.clone();
    }



    /**主机池列表*/
    private String[] hostpoolListUrl = new String[]{"/cas/casrs/hostpool/all"};

    public String[] getHostpoolListUrl() {
        return this.hostpoolListUrl.clone();
    }

    /**集群列表*/
    private String[] clusterListUrl = new String[]{"/cas/casrs/cluster/clusters"};

    public String[] getClusterListUrl() {
        return this.clusterListUrl.clone();
    }

    private String[] instancePerfUrl = new String[]{"/cas/casrs/vm/id"};

    public String[] getInstancePerfUrl() {
        return this.instancePerfUrl.clone();
    }

    private String[] hostPerfUrl = new String[]{"/cas/casrs/host/id"};

    public String[] getHostPerfUrl() {
        return this.hostPerfUrl.clone();
    }

    private String[] vmPowerOperationUrl = new String[]{"/cas/casrs/vm"};

    public String[] getVmPowerOperationUrl() {
        return this.vmPowerOperationUrl.clone();
    }





    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

}
