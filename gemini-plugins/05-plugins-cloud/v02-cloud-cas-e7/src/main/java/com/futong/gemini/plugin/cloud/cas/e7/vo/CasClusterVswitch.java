package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasClusterVswitch {
    private String id;

    private String name;

    private String mode;

    private String enableLacp;

    private String isRuningVmUseVSwitch;

    private String flag;

    private String haStatus;

    private String enableDpdk;

    private String reselect;

    private String isVxlanScopeUseVSwitch;

    private String storageNodeSetted;

    private String networkType;
}
