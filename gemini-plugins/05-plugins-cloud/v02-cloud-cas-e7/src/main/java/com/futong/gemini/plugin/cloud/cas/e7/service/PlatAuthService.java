package com.futong.gemini.plugin.cloud.cas.e7.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PlatAuthService {
    public static final PlatAuthService bean = new PlatAuthService();

    //cas账号认证
    //cas认证采用摘要认证的方式，因此此功能通过调用主机池接口的方式验证端口、用户名密码登是否有效
    public static BaseResponse authCloudAccount(JSONObject arguments) {
        try {
            JSONArray array = Converts.fetchResourceToJsonArray(
                    URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostpoolListUrl(),null),
                    ResourceEnum.HOSTPOOL.getValue());
        } catch (Exception e) {
            log.error("账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.auth.success");
    }
}
