package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasStoragePool;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbStoragePoolRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class StoragePoolService {
    public static final StoragePoolService bean = new StoragePoolService();


    public void fetchStoragePool(DescribeCasRequest request, JSONObject arguments) {
        if(ObjectUtil.isEmpty(request.getIds())){
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<CmdbStoragePoolRes> storages = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(i->{
                List<CmdbStoragePoolRes> storagePoolRes = new ArrayList<>();
                /**获取宿主机关联的存储池资源数据*/
                JSONArray array = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStoragePoolUrl(),new String[]{"",i}),
                        ResourceEnum.STORAGE_POOL.getValue());
                if(ObjectUtil.isNotEmpty(array)){
                    array.forEach(storage -> {
                        JSONObject obj = (JSONObject) storage;
                        /**cas转换存储池转换为nxc存储池数据*/
                        storagePoolRes.add(Converts.toNxcStoragePool(BaseClient.auths.get(),Converts.parseAndConvert(obj.toJSONString(), new TypeReference<CasStoragePool>() {}),i));
                    });
                }
                if(ObjectUtil.isNotEmpty(storagePoolRes)){
                    storages.addAll(storagePoolRes);
                    /**宿主机与存储池关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),i,new CmdbHostRes()),storagePoolRes.stream().collect(Collectors.toList())));
                }
            });
        } catch (Exception e){
            log.error("同步存储池资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.storagePool.fail"), e);
        }

        log.info("推送存储池数据条数 ={}",storages.size());
        /**
         * 推送存储池数据
         */
        BaseUtils.sendMessage(storages, arguments);

        /**
         * 推送宿主机与存储池关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }


    public static BaseResponse startStoragePool(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        List<JSONObject> instanceIds = getInstanceList(body);
        for (JSONObject obj : instanceIds) {
            String hostId = obj.getString("hostId");
            String poolName = obj.getString("poolName");
            String hostName = obj.getString("hostName");
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageHostUrl("start"),new String[]{"",hostId,poolName,hostName});
            try {
                HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("启动主机存储池操作异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动主机存储池操作异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse stopStoragePool(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        List<JSONObject> instanceIds = getInstanceList(body);
        for (JSONObject obj : instanceIds) {
            String hostId = obj.getString("hostId");
            String poolName = obj.getString("poolName");
            String hostName = obj.getString("hostName");
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageHostUrl("stop"),new String[]{"",hostId,poolName,hostName});
            try {
                HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("启动主机存储池操作异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "停止主机存储池操作异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteStoragePool(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        List<JSONObject> instanceIds = getInstanceList(body);
        for (JSONObject obj : instanceIds) {
            String hostId = obj.getString("hostId");
            String poolName = obj.getString("poolName");
            String hostName = obj.getString("hostName");
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageHostUrl("delete"),new String[]{"",hostId,poolName,hostName});
            try {
                HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("启动主机存储池操作异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除主机存储池操作异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<JSONObject> getInstanceList(JSONObject body) {
        List<JSONObject> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject o = new JSONObject();
                JSONObject ci = cis.getJSONObject(i);
                o.put("poolName",ci.getString("open_id"));
                JSONArray relationHost = ci.getJSONArray("relationHost");
                if(relationHost!=null&&relationHost.size()>0) {
                    JSONObject host = relationHost.getJSONObject(0);
                    o.put("hostId",host.getString("relationopenId"));
                    o.put("hostName",host.getString("relationopenName"));
                }
                instanceIds.add(o);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            JSONObject o = new JSONObject();
            o.put("poolName",ci.getString("open_id"));
            JSONArray relationHost = ci.getJSONArray("relationHost");
            if(relationHost!=null&&relationHost.size()>0) {
                JSONObject host = relationHost.getJSONObject(0);
                o.put("hostId",host.getString("relationopenId"));
                o.put("hostName",host.getString("relationopenName"));
            }
            instanceIds.add(o);
        }
        return instanceIds;
    }

}
