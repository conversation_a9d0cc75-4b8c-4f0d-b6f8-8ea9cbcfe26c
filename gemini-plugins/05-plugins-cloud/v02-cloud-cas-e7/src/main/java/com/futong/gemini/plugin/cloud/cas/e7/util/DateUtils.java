package com.futong.gemini.plugin.cloud.cas.e7.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class DateUtils {

    public static  SimpleDateFormat utc_sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    public static  SimpleDateFormat utc_sdf8 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'+08:00'");

    public static  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String timestampToStr(long timestamp) {
        return simpleDateFormat.format(new Date(timestamp));
    }

    public static Long strToTimestamp(String timestamp) throws ParseException {
        return simpleDateFormat.parse(timestamp).getTime();
    }
    public static Timestamp utcToTimestamp(String utcTime) {
        utc_sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = utc_sdf.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        utc_sdf.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = utc_sdf.format(utcDate.getTime());
        try {
            locatlDate = utc_sdf.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new Timestamp(locatlDate.getTime());
    }
}
