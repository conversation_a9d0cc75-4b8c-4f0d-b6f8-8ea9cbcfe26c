package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class StorageSnapshotService {
    public static final StorageSnapshotService bean = new StorageSnapshotService();


    /**
     * 同步快照信息
     * @param request
     */
    public static void fetchSnapshot(DescribeCasRequest request, JSONObject arguments) {
        if(ObjectUtil.isEmpty(request.getIds())){
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<CmdbSnapshotRes> snapshots = new ArrayList<>();
        List<Association>  associations = new ArrayList<>();
        try {
            request.getIds().forEach(id -> {
                List<CmdbSnapshotRes> snapshotRes = new ArrayList<>();
                String json  = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getVmSnapshotUrl(),new String[]{id});
                /**同步并转换云主机快照信息*/
                Converts.toNxcSnapshot(snapshotRes,BaseClient.auths.get(),JSONObject.parseObject(json),null,id);
                if(ObjectUtil.isNotEmpty(snapshotRes)){//判断云主机是否存在快照，存在则推送数据及云主机关系数据
                    snapshots.addAll(snapshotRes);
                    /**云主机与快照关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id,new CmdbInstanceRes()),snapshotRes.stream().collect(Collectors.toList())));
                }
            });
        }catch (Exception e){
            log.error("同步快照资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.snapshot.fail"), e);
        }
        /**推送云主机快照数据*/
        BaseUtils.sendMessage(snapshots, arguments);
        /**推送云主机与快照关系数据*/
        log.info("推送云主机与快照关系数据条数 ={}",associations.size());
        BaseUtils.sendMessage(associations, arguments);
    }
}
