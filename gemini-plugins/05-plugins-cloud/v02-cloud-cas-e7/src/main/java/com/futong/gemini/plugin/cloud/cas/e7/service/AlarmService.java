package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.route.RouteFactory;
import com.futong.gemini.plugin.cloud.cas.e7.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasAlarm;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.common.model.OtcAetMessage;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

;

@Slf4j
public class AlarmService {

    public static final AlarmService bean = new AlarmService();

    // 获取事件
    public String fetchAlarm(DescribeCasRequest request, JSONObject arguments) throws Exception{
        List<CasAlarm> alarms = fetchCasAlarm(arguments);
        List<AlarmInfoBean> beans = alarms.stream()
                .map(t -> FetchConverts.toAlarm(t)).collect(Collectors.toList());
        toAetMessageAndSend(beans, "alarm");
        String message = StrUtil.format("本次获取告警信息条数：{}", alarms.size());
        return message;
    }

    public List<CasAlarm> fetchCasAlarm(JSONObject arguments){
        List<CasAlarm> alarms =  new ArrayList<>();
        long startTime =  new Date().getTime() -5*60*1000l;
        String toTime = DateUtil.formatDateTime(new Date()).replace(" ", "%20");
        String fromTime = DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(startTime), -5)).replace(" ", "%20");
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlarmListUrl(),new String[]{"?","0","300",fromTime,toTime});
        JSONArray array = Converts.fetchResourceToJsonArray(url ,
                ResourceEnum.ALARM.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 解析并转换JSON对象为CloudosFlavor对象
                CasAlarm alarm = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CasAlarm>() {});
                alarms.add(alarm);
            });
        }
        return alarms;
    }

    public static <T> OtcAetMessage<T> toAetMessageAndSend(List<T> res, String type) {
        OtcAetMessage<T> message = new OtcAetMessage<>();
        message.setType(type);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routeAetMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }
}
