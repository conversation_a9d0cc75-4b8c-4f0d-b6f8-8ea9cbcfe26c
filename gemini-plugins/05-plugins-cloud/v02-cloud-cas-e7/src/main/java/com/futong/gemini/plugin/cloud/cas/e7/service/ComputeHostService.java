package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.enums.JsonKeyEnum;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.JobUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasHostDetail;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasStoragePool;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

;

@Slf4j
public class ComputeHostService {
    public static final ComputeHostService bean = new ComputeHostService();

    public void fetchHost(DescribeCasRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<CmdbHostRes> hosts = new ArrayList<>();
        List<TmdbResourceSet> resourceTags = new ArrayList<>();
        try {
            request.getIds().forEach(i -> {
                String json = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getHostDetailUrl(), new String[]{i});
                CasHostDetail casHost = Converts.parseAndConvert(json, new TypeReference<CasHostDetail>() {
                });
                //转换宿主机信息
                CmdbHostRes hostRes = Converts.toNxcHost(BaseClient.auths.get(), casHost);
                Float totalSize = hostRes.getTotal_size();
                Float useSize = 0f;
                Float freeSize = 0f;
                try {
                    String overview = HttpClientUtil.get(BaseClient.auths.get(), URLUtils.bean.getHostOverviewUrl(), new String[]{i});
                    JSONObject obj = JSON.parseObject(overview);
                    if (obj.getString("freeStorage").endsWith("TB"))
                        freeSize = Float.parseFloat(obj.getString("freeStorage").replace("TB", "")) * 1024;
                    else if (obj.getString("freeStorage").endsWith("GB"))
                        freeSize = Float.parseFloat(obj.getString("freeStorage").replace("GB", ""));
                }catch (Exception e) {
                    e.printStackTrace();
                }
                useSize = totalSize - freeSize;
                hostRes.setTotal_size(totalSize);
                hostRes.setUsed_size(useSize);
                hosts.add(hostRes);
                /**宿主机与结构的*/

                resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_HOST_POOL.value(), TmdbDevops.class,  casHost.getHostPoolId(), ResourceType.CMDB_HOST_RES.value(), casHost.getId()));
                resourceTags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, casHost.getClusterId(), ResourceType.CMDB_HOST_RES.value(), casHost.getId()));
                //转换宿主机网卡信息
                /*List<CmdbNetcardRes>  netcardRes = Converts.toNxcHostCard(BaseClient.auths.get(),casHost);
                if(ObjectUtil.isNotEmpty(netcardRes)){
                    nics.addAll(netcardRes);
                    associations.add(AssociationUtils.toAssociation(hostRes, netcardRes.stream().collect(Collectors.toList())));
                }*/
            });
        } catch (Exception e) {
            log.error("同步宿主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.host.fail"), e);
        }
        /**
         * 推送宿主机数据
         */
        BaseUtils.sendMessage(hosts, arguments);
        /**
         * 推送北新仓关联数据
         */
        BaseUtils.sendMessage(resourceTags, arguments);

        /**
         * 推送宿主机和网卡关系数据
         */
//        BaseUtils.sendMessage(associations, arguments);
    }

    public List<JobInfo> splitInstanceDataJob(DescribeCasRequest request, JSONObject arguments) {
        /**获取宿主机列表*/
        JSONArray hostArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), null),
                ResourceEnum.HOST.getValue());

        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        /** 拆分宿主机dataJob */
        jobs = Stream.of(jobs,
                JobUtils.splitDataJob(request, hostArray,
                        arguments,
                        new String[]{ResourceEnum.HOST.getValue(), ResourceEnum.INSTANCE.getValue(), ResourceEnum.STORAGE_POOL.getValue(), ResourceEnum.VSWITCH.getValue(),ResourceEnum.SUBNET.getValue()},
                        JsonKeyEnum.ID.getValue()))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        return jobs;
    }

    public static BaseResponse addHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("into");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("进入维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "进入维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse stopHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("shutoff");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("关闭主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse rebootHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("reboot");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("重启主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重启主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse connectHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("connect/v2");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("连接主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "连接主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse disConnectHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("into");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("进入维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "进入维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse enterHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("into");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("进入维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "进入维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse exitHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = powerOperationUrl("exit");
            JSONObject param = new JSONObject();
            param.put("id", hostId);
            try {
                HttpClientUtil.put(url, param.toJSONString(),new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("退出维护模式异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "退出维护模式异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse removeHost(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        JSONArray cis = body.getJSONArray("cis");
        List<String> hostIds = cis.stream().map(x -> ((JSONObject) x).getString("openId")).collect(Collectors.toList());
        for (String hostId : hostIds) {
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), new String[]{"delete", hostId});
            try {
                HttpClientUtil.delete(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("移除主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移除主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }


    private static String powerOperationUrl(String action) {
        return URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), new String[]{action});
    }
}
