package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasTempDisk;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasTempNic;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasTemplate;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ComputeTemplateService {
    public static final ComputeTemplateService bean = new ComputeTemplateService();

    public void fetchTemplate(DescribeCasRequest request, JSONObject arguments) {

        /**同步模版资源数据*/
        JSONArray templateArray = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getTemplateUrl(),new String[]{"","0","100"}),
                ResourceEnum.DOMIAN.getValue());
        List<CmdbInstanceRes> instanceList = new ArrayList<>();
        List<CmdbNetcardRes> nics = new ArrayList<>();
        List<CmdbDiskRes>  disks = new ArrayList<>();
        List<Association>  associations = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(templateArray)){
            templateArray.forEach(temp ->{
                JSONObject tempObj = (JSONObject) temp;
                String id = tempObj.getString("id");
                JSONArray diskArray = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getTemplateDiskUrl(),new String[]{"",id}),
                        ResourceEnum.VMSTORAGE.getValue());
                JSONArray nicArray = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getTemplateNicUrl(),new String[]{"",id}),
                        ResourceEnum.VMNETWORK.getValue());
                fetchDisks(diskArray, id, disks,associations);
                /** 转换云主机网卡、IP以及关系数据 */
                fetchNics(nicArray, id, nics,associations);
                /**模版资源数据模型转换*/
                instanceList.add(Converts.toNxcTemplate(BaseClient.auths.get(), Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CasTemplate>() {})));
            });
        }
        /**
         * 推送模版数据
         */
        BaseUtils.sendMessage(instanceList, arguments);

        /** 推送网卡数据 */
        BaseUtils.sendMessage(nics, arguments);
        /** 推送磁盘数据 */
        BaseUtils.sendMessage(disks, arguments);

        /**推送云主机与磁盘、云主机与网卡、网卡与IP关系数据*/
        BaseUtils.sendMessage(associations, arguments);
    }


    private static void fetchDisks(JSONArray diskArray, String id, List<CmdbDiskRes> disks,List<Association>  associations) {
        try {
            if (ObjectUtil.isNotEmpty(diskArray)) {
                List<CmdbDiskRes> diskRes = new ArrayList<>();
                diskArray.forEach(disk -> {
                    JSONObject diskObj = (JSONObject) disk;
                    if (!"disk".equals(diskObj.getString("diskDevice"))) return;
                    /**转换磁盘*/
                    diskRes.add(Converts.toNxcTempDisk(BaseClient.auths.get(), Converts.parseAndConvert(diskObj.toString(), new TypeReference<CasTempDisk>() {}), id));
                });
                if(ObjectUtil.isNotEmpty(diskRes)){
                    /**云主机和磁盘关系*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id, CmdbInstanceRes.class.newInstance()) , diskRes.stream().collect(Collectors.toList())));
                    disks.addAll(diskRes);
                }
            }
        }catch (Exception e){
            log.error("同步磁盘资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.disk.fail"), e);
        }
    }
    private static void fetchNics(JSONArray nicArray, String id, List<CmdbNetcardRes> nics,List<Association> associations) {
        try {
            if (ObjectUtil.isNotEmpty(nicArray)) {
                List<CmdbNetcardRes> netcardRes = new ArrayList<>();
                nicArray.forEach(nic -> {
                    JSONObject nicObj = (JSONObject) nic;
                    /**转换网卡*/
                    CmdbNetcardRes  card = Converts.toNxcTempCard(BaseClient.auths.get(), Converts.parseAndConvert(nicObj.toString(), new TypeReference<CasTempNic>() {}), id);
                    netcardRes.add(card);
                });
                if(ObjectUtil.isNotEmpty(netcardRes)){
                    /**云主机和网卡关系*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),id,CmdbInstanceRes.class.newInstance()) , netcardRes.stream().collect(Collectors.toList())));
                    nics.addAll(netcardRes);
                }
            }
        } catch (Exception e){
            log.error("同步虚拟网卡资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.nic.fail"), e);
        }
    }

    public static BaseResponse deleteTemplate(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDelTemplateUrl(), new String[]{"",instanceId});
            try {
                HttpClientUtil.delete(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                log.error("删除模版异常{}", e);
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
