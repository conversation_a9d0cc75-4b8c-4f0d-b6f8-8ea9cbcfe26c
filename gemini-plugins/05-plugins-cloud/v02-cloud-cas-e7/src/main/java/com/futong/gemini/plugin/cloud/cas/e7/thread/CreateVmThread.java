package com.futong.gemini.plugin.cloud.cas.e7.thread;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.cas.e7.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.DefaultHttpClient;

import java.util.Map;

@Slf4j
public class CreateVmThread extends Thread {
    private CloudAccessBean accessBean;

    private HttpClientConfig config;
    private String json;
    private String result;

    private com.alibaba.fastjson.JSONObject arguments;
    public CreateVmThread(String result, com.alibaba.fastjson.JSONObject arguments,String json, HttpClientConfig config, CloudAccessBean accessBean) {
        this.result = result;
        this.accessBean = accessBean;
        this.json = json;
        this.config = config;
        this.arguments = arguments;
    }
    public void run() {
        try {
            int count = 1;
            while(count<20) {
                JSONObject res = JSONUtil.parseObj(result);
                String taskId = res.getStr("msgId");
                String taskUrl = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getTaskUrl(), new String[]{"/" + taskId});
                String s = HttpClientUtil.get(taskUrl, config, accessBean);
                JSONObject result = JSONUtil.parseObj(s);
                String completed = result.getStr("completed");
                String targetId = "";
                log.info("刷新创建任务状态------"+completed+"---targetId=="+targetId+"---"+result);
                if("true".equals(completed)) {

                    JSONArray refreshData = result.getJSONArray("refreshData");
                    if(refreshData!=null) {
                        for(int i=0;i<refreshData.size();i++) {
                            JSONObject jsonObject = refreshData.getJSONObject(i);
                            String key = jsonObject.getStr("key");
                            if("5".equals(key)) {
                                targetId = jsonObject.getStr("value");
                            }
                        }
                    }
                    try {
                        extendDisk(accessBean, json, targetId);
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                    FetchConverts.createInstanceEventJob(arguments,targetId,"update");
                    com.alibaba.fastjson.JSONObject biz = arguments.getJSONObject("body").getJSONObject("biz");
                    if(biz!=null) {
                        log.info("创建虚拟机发起工单---targetId---{}---bizId---{}", targetId,IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), targetId));
                        biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), targetId));
                        FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                    }
                    break;
                }
                count++;
                Thread.sleep(30000);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void extendDisk(CloudAccessBean accessBean,String json, String vmId) {
        String url = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getVmDetailUrl(), new String[]{vmId});
        String detail = HttpClientUtil.get(url, new HttpClientConfig(), accessBean);
        com.alibaba.fastjson.JSONObject vmDetail = JSON.parseObject(detail);
        com.alibaba.fastjson.JSONArray oldStorage = vmDetail.getJSONArray("storage");
        com.alibaba.fastjson.JSONObject vmConfig = JSON.parseObject(json);
        com.alibaba.fastjson.JSONObject vmObj = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONArray storages = vmConfig.getJSONArray("storage");
        vmObj.put("id",vmId);
        if(storages!=null) {
            for(int i=0;i<storages.size();i++){
                com.alibaba.fastjson.JSONObject storage =  storages.getJSONObject(i);
                if(storage.getInteger("minDisValue")>=storage.getInteger("disValue"))
                    continue;
                if(oldStorage!=null) {
                    for(int j=0;j<oldStorage.size();j++) {
                        com.alibaba.fastjson.JSONObject diskInfo = oldStorage.getJSONObject(j);
                        if("disk".equals(diskInfo.getString("device"))&&diskInfo.getString("path").endsWith("/"+storage.getString("dest"))){
                            com.alibaba.fastjson.JSONObject disk = new com.alibaba.fastjson.JSONObject();
                            disk.put("device", diskInfo.getString("deviceName"));
                            disk.put("format", diskInfo.getString("format"));
                            disk.put("size", storage.getInteger("disValue")*1024);
                            disk.put("cacheType", diskInfo.getString("cacheType"));
                            vmObj.put("storage", disk);
                            String extendUrl = URLUtils.bean.makeUrl(accessBean, URLUtils.bean.getUpdateVmUrl(), null);
                            String result = HttpClientUtil.put(extendUrl, vmObj.toJSONString(), new HttpClientConfig(),accessBean);
                            com.alibaba.fastjson.JSONObject re = JSON.parseObject(result);
                            if("0".equals(re.get("RESULT_CODE"))){
                                log.info("扩容磁盘发起成功");
                            }else {
                                log.info("扩容磁盘失败");
                            }
                            break;
                        }
                    }
                }
            }
        }
    }
}
