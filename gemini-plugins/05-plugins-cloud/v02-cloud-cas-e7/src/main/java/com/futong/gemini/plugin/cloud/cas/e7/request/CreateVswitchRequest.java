package com.futong.gemini.plugin.cloud.cas.e7.request;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 修改虚拟机请求参数
 */
@Setter
@Getter
public class CreateVswitchRequest implements Serializable {
    private String switchType;
    private JSONArray hostNetInfos;
    private JSONArray networkType;
    private String name;
    private String description;
    private String clusterId;
    private int portNum;
    private int mode;
    private boolean enableLacp;
    private String bondMode;
    private String vlanId;
    private String algorithm;
    private int mtu;
    private boolean multicast;
}
