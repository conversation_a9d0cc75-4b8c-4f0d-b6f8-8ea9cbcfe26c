package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasVmDisk {

        private String	device;
        private String	deviceName;
        private String	fileType;
        private String	path;
        private String	format;
        private String	targetBus;
        private Long	size;
        private String	allocation;
        private String	cacheType;
        private String	snapShot;
        private Long	maxSize;
        private String	mode;
        private Long	clusterSize;
        private Boolean	isShareStorage;
}
