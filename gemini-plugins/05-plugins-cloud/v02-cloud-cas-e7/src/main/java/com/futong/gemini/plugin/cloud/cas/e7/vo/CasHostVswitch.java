package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CasHostVswitch {
    private String id;

    private String hostId;

    private String name;

    private String portNum;

    private String mode;

    private String address;

    private String netmask;

    private String gateway;

    private String enableLacp;

    private String bondMode;

    private String isManage;

    private String status;

    private String isRuningVmUseVSwitch;

    private String flag;

    private String haStatus;

    private String enableDpdk;

    private String reselect;

    private String isVxlanScopeUseVSwitch;

    private String mtu;

    private String storageNodeSetted;

    private String networkType;

    private String multicast;
}
