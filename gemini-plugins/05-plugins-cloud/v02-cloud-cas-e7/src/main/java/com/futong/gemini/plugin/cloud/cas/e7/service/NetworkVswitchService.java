package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.request.CreateVswitchRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.request.UpdateVmRequest;
import com.futong.gemini.plugin.cloud.cas.e7.util.CasUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasProfile;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasHostVswitch;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbVswitchRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class NetworkVswitchService {

    public static final NetworkVswitchService bean = new NetworkVswitchService();


    public void fetchHostVswitch(DescribeCasRequest request, JSONObject arguments) {
        if(ObjectUtil.isEmpty(request.getIds())){
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数ids不能为空.");
        }
        List<CmdbVswitchRes> vswitchs = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(i->{
                List<CmdbVswitchRes> vswitchRes = new ArrayList<>();
                /**同步宿主机下虚拟交换机数据*/
                JSONArray array = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostVswitchUrl(),new String[]{"",i}),
                        ResourceEnum.VSWITCH.getValue());
                if(ObjectUtil.isNotEmpty(array)){
                    array.forEach(vswitch -> {
                        JSONObject obj = (JSONObject) vswitch;
                        /**虚拟交换机模型转换*/
                        vswitchRes.add(Converts.toNxcSwitch(BaseClient.auths.get(),Converts.parseAndConvert(obj.toJSONString(), new TypeReference<CasHostVswitch>() {})));
                    });
                }
                if(ObjectUtil.isNotEmpty(vswitchRes)){
                    vswitchs.addAll(vswitchRes);
                    /**宿主机与虚拟交换机关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),i,new CmdbHostRes()),vswitchRes.stream().collect(Collectors.toList())));
                }
            });
        } catch (Exception e){
            log.error("同步主机关联的虚拟交换机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步主机关联的虚拟交换机资源数据失败");
        }
        /**
         * 推送虚拟机交换机数据
         */
        BaseUtils.sendMessage(vswitchs, arguments);

        /**
         * 推送宿主机与虚拟机交换机关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }

    public void fetchHostSubnet(DescribeCasRequest request, JSONObject arguments) {
        if(ObjectUtil.isEmpty(request.getIds())){
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数ids不能为空.");
        }
        List<CmdbSubnetRes> subnetRess = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(i->{
                /**同步宿主机下虚拟交换机数据*/
                JSONArray array = Converts.fetchResourceToJsonArray(
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostVswitchUrl(),new String[]{"",i}),
                        ResourceEnum.VSWITCH.getValue());
                if(ObjectUtil.isNotEmpty(array)){
                    array.forEach(vswitch -> {
                        JSONObject vswitchObj = (JSONObject) vswitch;
                        List<CmdbSubnetRes> subnetRes = new ArrayList<>();
                        /**虚拟交换机模型转换*/
                        JSONArray subnets = Converts.fetchResourceToJsonArray(
                                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getProfileUrl(),null),
                                ResourceEnum.PORTPROFILE.getValue());

                        if(ObjectUtil.isNotEmpty(subnets)){
                            subnets.forEach(subnet -> {
                                JSONObject sub = (JSONObject) subnet;
                                /**虚拟交换机模型转换*/
                                subnetRes.add(Converts.toNxcSubnet(BaseClient.auths.get(),Converts.parseAndConvert(sub.toJSONString(), new TypeReference<CasProfile>() {})));

                            });
                            CloudAccessBean bean = BaseClient.auths.get();
                            CmdbVswitchRes vs = new CmdbVswitchRes();
                            vs.setRes_id(IdUtils.encryptId(bean.getCmpId(), vswitchObj.getString("id"), ResourceType.CMDB_VSWITCH_RES.value()));
                            vs.setOpen_id(i);
                            vs.setAccount_id(bean.getCmpId());
                            vs.setCloud_type(bean.getCloudType());
                            associations.add(AssociationUtils.toAssociation(vs,subnetRes.stream().collect(Collectors.toList())));
                            if(ObjectUtil.isNotEmpty(subnetRes)){
                                subnetRess.addAll(subnetRes);
                                /**交换机和子网关系数据*/
                            }
                        }
                    });
                }

            });
        } catch (Exception e){
            log.error("同步主机关联的子网资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步子网资源数据失败");
        }
        /**
         * 推送虚拟机交换机数据
         */
        BaseUtils.sendMessage(subnetRess, arguments);

        /**
         * 推送宿主机与虚拟机交换机关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }

    public static BaseResponse deleteVswith(JSONObject arguments) {
        String message = "操作成功.";
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVswitchUrl(), new String[]{instanceId});
                HttpClientUtil.delete(url, new HttpClientConfig(), BaseClient.auths.get());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("删除交换机异常{}", e);
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse createVswith(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas创建交换机接受参数={}", arguments.toString());
        CreateVswitchRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(CreateVswitchRequest.class);
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateVswitchUrl(request.getSwitchType()), null);
        try {
            CasUtils.bean.createVswith(url,request);
        } catch (Exception e) {
            log.error("创建交换机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建交换机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse updateVswith(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas修改交换机接受参数={}", arguments.toString());
        CreateVswitchRequest request = BaseClient.bodys.get().getJSONObject("model").toJavaObject(CreateVswitchRequest.class);
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCreateVswitchUrl(request.getSwitchType()), null);
        try {
            CasUtils.bean.updateVswith(url,request);
        } catch (Exception e) {
            log.error("修改交换机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改交换机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    public static BaseResponse queryNetworkOutput(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cas查询网络端口接受参数={}", arguments.toString());
        String hostId = BaseClient.bodys.get().getJSONObject("model").getString("openId");
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.queryHostNetportUrl(), new String[]{hostId,"eth"});
        List<Object> list = new ArrayList<>();
        try {

            String result = HttpClientUtil.get(url, new HttpClientConfig(), BaseClient.auths.get());
            if(ObjectUtil.isNotEmpty(result)) {
                JSONObject obj = JSON.parseObject(result);
                if (null != obj){
                    if (obj.get("pNIC") instanceof Collection){
                        list = (List<Object>) obj.get("pNIC");
                    }else {
                        list.add(obj.get("pNIC"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("cas查询网络端口异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "cas查询网络端口异常");
        }
        return new BaseDataResponse<>(list);
    }
}
