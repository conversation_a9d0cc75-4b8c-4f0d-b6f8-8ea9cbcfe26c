package com.futong.gemini.plugin.cloud.cas.e7.sampler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.request.DescribeCasRequest;
import com.futong.gemini.plugin.cloud.cas.e7.service.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public class FetchSampler {
    public static BaseResponse fetchHost(JSONObject arguments) {
        try {
            DescribeCasRequest request = BaseClient.bodys.get().toJavaObject(DescribeCasRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())){//若ids不为空时，获取指定资源，否则获取宿主机列表并拆分dataJob
                switch (request.getResourceType()){
                    case "host"://同步宿主机详情
                        ComputeHostService.bean.fetchHost(request,arguments);
                        break;
                    case "instance"://获取云主机数据并拆分dataJob
                        return new GourdJobResponse(ComputeInstanceService.bean.fetchInstance(request,arguments),"gemini.public.fetch.host.success");
                    case "instanceDiskNic"://获取云主机磁盘和网卡信息
                        StorageDiskService.bean.fetchDiskNic(request,arguments);
                        break;
                    case "snapshot"://获取快照信息
                        StorageSnapshotService.bean.fetchSnapshot(request,arguments);
                        break;
                    case "storagePool"://存储池
                        StoragePoolService.bean.fetchStoragePool(request,arguments);
                        break;
                    case "vSwitch"://虚拟交换机
                        NetworkVswitchService.bean.fetchHostVswitch(request,arguments);
                        break;
                    case "subnet"://虚拟端口组
                        NetworkVswitchService.bean.fetchHostSubnet(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{//获取宿主机列表并拆分dataJob
                return new GourdJobResponse(ComputeHostService.bean.splitInstanceDataJob(request,arguments),"gemini.public.fetch.host.success");
            }
            return BaseResponse.SUCCESS.ofI18n("gemini.public.fetch.host.success");
        } catch (Exception e) {
            log.error("同步宿主机及其关联资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.host.fail"), e);
        }
    }
    public static BaseResponse fetchTemplate(JSONObject arguments) {
        try {
            DescribeCasRequest request = BaseClient.bodys.get().toJavaObject(DescribeCasRequest.class);
            ComputeTemplateService.bean.fetchTemplate(request, arguments);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("同步模版资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.template.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.fetch.template.success");
    }

    public static BaseResponse fetchHostpool(JSONObject arguments) {
        try {
            ComputeHostpoolService.bean.fetchHostpool(arguments);
        } catch (Exception e) {
            log.error("同步主机池资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.hostpool.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.fetch.hostpool.success");
    }


    public static BaseResponse fetchCluster(JSONObject arguments) {
        try {
            ComputeClusterService.bean.processCluster(arguments);
        } catch (Exception e) {
            log.error("同步集群资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.cluster.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.fetch.cluster.success");
    }

    public static BaseResponse fetchAlarm(JSONObject arguments) {
        DescribeCasRequest request = BaseClient.bodys.get().toJavaObject(DescribeCasRequest.class);
        String message = "成功获取告警资源信息.";
        try{
            AlarmService.bean.fetchAlarm(request,arguments);
        }  catch (Exception e) {
            log.error("同步告警资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步告警资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse fetchSecurityGroup(JSONObject arguments) {
        try {
            NetworkSecurityGroupService.bean.fetchSecurityGroup(arguments);
        } catch (Exception e) {
            log.error("同步安全组资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.securityGroup.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.fetch.securityGroup.success");
    }

    public static BaseResponse queryInstanceTotal(JSONObject arguments) {
        String message = "同步虚拟机数量成功";
        try {
            JSONObject data = new JSONObject();
            int count = ComputeInstanceService.bean.queryInstanceTotal();
            data.put("count", count);
            return new BaseDataResponse<>(data);
        } catch (Exception e) {
            log.error("同步虚拟机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步虚拟机数量失败");
        }
    }

    public static BaseResponse queryHostTotal(JSONObject arguments) {
        String message = "同步主机数量成功";
        try {
            JSONObject data = ComputeInstanceService.bean.queryHostTotal();
            return new BaseDataResponse<>(data);
        } catch (Exception e) {
            log.error("同步主机数量失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步主机数量失败");
        }
    }
}
