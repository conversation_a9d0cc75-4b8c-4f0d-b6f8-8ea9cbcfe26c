package com.futong.gemini.plugin.cloud.cas.e7.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CasAlarm {

    private String event_level;

    private String event_desc;

    private String event_time;

    private String event_name;

    private String event_src;

    private String id;

    private String state;

    private String event_type;

}

