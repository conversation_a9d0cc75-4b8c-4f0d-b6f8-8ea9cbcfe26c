package com.futong.gemini.plugin.cloud.cas.e7.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cas.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cas.e7.request.*;
import com.futong.gemini.plugin.cloud.cas.e7.thread.CreateVmThread;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Slf4j
public class CasUtils {
    public static final CasUtils bean = new CasUtils();

    /**克隆虚拟机
     * @param request  请求body
     * @throws Exception
     */
    public static void cloneVm(String url, CreateVmRequest request,JSONObject arguments) throws Exception{
        if(BaseClient.bodys.get().containsKey("biz")) {
            request.setVmNum(1);
        }
        int amount = request.getVmNum();
        if(amount>1) {
            String str = generateRandomString(4);
            String vmName = request.getDomainName() + "-" + str + "-";
            for (int i = 1; i <= amount; i++) {
                request.setDomainName(vmName + i);
                request.setTitle(vmName + i);
                JSONObject vmBody = getVmBody(request);
                log.info("创建cas虚拟机参数={}",vmBody.toJSONString());
                String result = HttpClientUtil.post(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
                log.info("创建cas虚拟机结果返回={}",result);
                new CreateVmThread(result, arguments,vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get()).start();
            }
        }else {
            if(BaseClient.bodys.get().containsKey("biz")) {
                Integer resNum = BaseClient.bodys.get().getJSONObject("biz").getInteger("resNum");
                if(resNum!=0) {
                    String vmName = request.getDomainName() + "-" + resNum;
                    request.setDomainName(vmName);
                }
            }
            JSONObject vmBody = getVmBody(request);
            log.info("创建cas虚拟机参数={}",vmBody.toJSONString());
            String result = HttpClientUtil.post(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
            log.info("创建cas虚拟机结果返回={}",result);
            new CreateVmThread(result, arguments,vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get()).start();
        }

    }

    public static JSONObject getVmBody(CreateVmRequest request) {
        JSONObject server = new JSONObject();
        try{
            Integer deployMode = request.getDeployMode();
            String domainName = request.getDomainName();
            String targetHostId = request.getTargetHostId();
            String id = request.getId();
            String desc = request.getDesc();
            String title = request.getTitle();
            String deployType = request.getDeployType();
            JSONArray storage = request.getStorage();
            JSONArray network = request.getNetwork();
            String profileName = "";
            if(network!=null&&network.size()>0) {
                profileName = network.getJSONObject(0).getString("profileName");
            }
            if(storage!=null) {
                for(int i=0;i<storage.size();i++) {
                    JSONObject storageObj = storage.getJSONObject(i);
                    storageObj.put("dest", "VM_"+domainName+"_"+id);
                }
            }

            JSONObject memory = request.getMemory();
            Integer cpuSocket = request.getCpuSocket();
            Integer cpuCore = request.getCpuCore();

            server.put("profileName", profileName);
            server.put("deployMode", deployMode);
            server.put("domainName", domainName);
            server.put("targetHostId", targetHostId);
            server.put("id", id);
            server.put("desc", desc);
            server.put("title", title);
            server.put("deployType", deployType);
            server.put("memory", memory.getInteger("size"));
            server.put("memoryInit", memory.getInteger("size"));
            server.put("memoryUnit", memory.getString("unit"));
            server.put("cpuSocket", cpuSocket);
            server.put("cpuCore", cpuCore);
            server.put("storage", storage);
            server.put("network", network);

        }catch (Exception e) {
            e.printStackTrace();
        }
        return server;

    }

    public static JSONObject getVmUpdateBody(UpdateVmRequest request) {
        JSONObject vmObj = new JSONObject();
        try{
            String id = request.getId();
            String desc = request.getDesc();
            String title = request.getTitle();

            Integer memory = request.getMemory();
            Integer cpuSocket = request.getCpuSocket();
            Integer cpuCore = request.getCpuCore();

            vmObj.put("id", id);
            vmObj.put("title", title);
            JSONObject cpuObj = new JSONObject();
            cpuObj.put("cpuSockets", cpuSocket);
            cpuObj.put("cpuCores", cpuCore);
            cpuObj.put("cpuShares", 512);
            JSONObject memoryObj = new JSONObject();
            memoryObj.put("size", (int)(memory*1024));
            vmObj.put("memory", memoryObj);
            vmObj.put("cpu", cpuObj);


            JSONObject basic = new JSONObject();
            basic.put("pae", 1);
            basic.put("acpi", 1);
            basic.put("apic", 1);
            basic.put("clock", "localtime");
            basic.put("autoMem", 1);
            basic.put("autoMigrate", 1);
            basic.put("blkiotune", 500);
            basic.put("desc", desc);
            vmObj.put("basic", basic);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmObj;

    }

    public static void updateVm(String url, UpdateVmRequest request) throws Exception{
        JSONObject vmBody = getVmUpdateBody(request);
        log.info("修改cas虚拟机参数={}",vmBody.toJSONString());
        String post = HttpClientUtil.put(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
        log.info("修改cas虚拟机结果={}",post);
    }


    public static void createVswith(String url, CreateVswitchRequest request) throws Exception{
        JSONObject vmBody = getCreateVswithBody(request);
        log.info("创建Vswith参数={}",vmBody.toJSONString());
        String post = HttpClientUtil.post(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
        log.info("创建Vswith结果={}",post);
    }

    public static void updateVswith(String url, CreateVswitchRequest request) throws Exception{
        JSONObject vmBody = getUpdateVswithBody(request);
        log.info("修改Vswith参数={}",vmBody.toJSONString());
        HttpClientUtil.put(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
    }

    public static void createSecurityGroup(String url, CreateSecurityGroupRequest request) throws Exception{
        JSONObject vmBody = getCreateSecurityGroupBody(request);
        log.info("创建securityGroup参数={}",vmBody.toJSONString());
        HttpClientUtil.post(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
    }

    public static void deleteSecurityGroup(String url, CreateSecurityGroupRequest request) throws Exception{

    }

    public static void createStorageFile(String url, CreateStorageFileRequest request) throws Exception{
        JSONObject vmBody = getCreateStorageFileBody(request);
        log.info("创建Vswith参数={}",vmBody.toJSONString());
        HttpClientUtil.post(url, vmBody.toJSONString(), new HttpClientConfig(), BaseClient.auths.get());
    }

    public static JSONObject getCreateVswithBody(CreateVswitchRequest request) {
        JSONObject vmObj = new JSONObject();
        try{
            int networkType = getType(request.getNetworkType());
            if("host".equals(request.getSwitchType())) {
                JSONObject jsonObject = request.getHostNetInfos().getJSONObject(0);
                vmObj.remove("hostNetInfos");
                vmObj.put("address", jsonObject.getString("address"));
                vmObj.put("netmask", jsonObject.getString("netmask"));
                vmObj.put("gateway", jsonObject.getString("gateway"));
                vmObj.put("pnic", jsonObject.getString("pnic"));
                vmObj.put("hostId", jsonObject.getString("openId"));
                vmObj.put("name", request.getName());
                vmObj.put("description", request.getDescription());
                vmObj.put("portNum", request.getPortNum());
                vmObj.put("mode", request.getMode());
                vmObj.put("enableLacp", request.isEnableLacp());
                vmObj.put("bondMode", request.getBondMode());
                vmObj.put("vlanId", request.getVlanId());
                vmObj.put("algorithm", request.getAlgorithm());
                vmObj.put("mtu", request.getMtu());
                vmObj.put("multicast", request.isMulticast());
                vmObj.put("networkType", networkType);
            }else if("cluster".equals(request.getSwitchType())) {

                JSONArray hostNetInfos1 = request.getHostNetInfos();
                JSONArray hostnetInfo = new JSONArray();
                for (int i = 0; i < hostNetInfos1.size(); i++) {
                    JSONObject jsonObject = hostNetInfos1.getJSONObject(i);
                    JSONObject obj = new JSONObject();
                    obj.put("hostId", Integer.parseInt(jsonObject.getString("openId")));
                    obj.put("pnics",jsonObject.get("pnic"));
                    obj.put("ipAddr",jsonObject.getString("ipAddr"));
                    obj.put("netmask",jsonObject.getString("netmask"));
                    obj.put("gateway",jsonObject.getString("gateway"));
                    obj.put("algorithm","speed");
                    obj.put("lacpMode","off");
                    obj.put("reselect",false);
                    hostnetInfo.add(obj);
                }
                vmObj.put("hostNetInfos", hostnetInfo);
                vmObj.put("networkType", networkType);
                vmObj.put("operation", "add");
                vmObj.remove("serverIp");
                vmObj.put("name", request.getName());
                vmObj.put("description", request.getDescription());
                vmObj.put("portNum", request.getPortNum());
                vmObj.put("mode", request.getMode());
                vmObj.put("enableDpdk", false);
                vmObj.put("vlanId", request.getVlanId());
                vmObj.put("mtu", request.getMtu());
                vmObj.put("multicast", request.isMulticast());
                vmObj.put("clusterId", request.getClusterId());
                vmObj.put("networkType", networkType);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmObj;
    }

    public static JSONObject getCreateStorageFileBody(CreateStorageFileRequest request) {
        JSONObject vmObj = new JSONObject();
        try{
            vmObj.put("volName", request.getVolName());
            vmObj.put("clustersize", request.getClustersize());
            vmObj.put("mode", request.getMode());
            vmObj.put("format", request.getFormat());
            vmObj.put("capacity", request.getCapacity());
            vmObj.put("hostId", request.getHostId());
            vmObj.put("spName", request.getSpName());
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmObj;
    }

    public static JSONObject getCreateSecurityGroupBody(CreateSecurityGroupRequest request) {
        JSONObject vmObj = new JSONObject();
        try{
            vmObj.put("title", request.getTitle());
            vmObj.put("description", request.getDescription());
            vmObj.put("ruleAction", request.getRuleAction());
            vmObj.put("rule", request.getRule());
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmObj;
    }

    public static JSONObject getUpdateVswithBody(CreateVswitchRequest request) {
        JSONObject vmObj = new JSONObject();
        try{
            int networkType = getType(request.getNetworkType());
            if("host".equals(request.getSwitchType())) {
                JSONObject jsonObject = request.getHostNetInfos().getJSONObject(0);
                vmObj.remove("hostNetInfos");
                vmObj.put("operation", "mod");
                vmObj.put("address", jsonObject.getString("address"));
                vmObj.put("netmask", jsonObject.getString("netmask"));
                vmObj.put("gateway", jsonObject.getString("gateway"));
                vmObj.put("pnic", jsonObject.getString("pnic"));
                vmObj.put("vswitchId", jsonObject.getString("openId"));
                vmObj.put("name", request.getName());
                vmObj.put("description", request.getDescription());
                vmObj.put("portNum", request.getPortNum());
                vmObj.put("mode", request.getMode());
                vmObj.put("enableLacp", request.isEnableLacp());
                vmObj.put("bondMode", request.getBondMode());
                vmObj.put("vlanId", request.getVlanId());
                vmObj.put("algorithm", request.getAlgorithm());
                vmObj.put("mtu", request.getMtu());
                vmObj.put("multicast", request.isMulticast());
                vmObj.put("networkType", networkType);
            }else if("cluster".equals(request.getSwitchType())) {

                JSONArray hostNetInfos1 = request.getHostNetInfos();
                JSONArray hostnetInfo = new JSONArray();
                for (int i = 0; i < hostNetInfos1.size(); i++) {
                    JSONObject jsonObject = hostNetInfos1.getJSONObject(i);
                    JSONObject obj = new JSONObject();
                    obj.put("vswitchId", Integer.parseInt(jsonObject.getString("openId")));
                    obj.put("pnics",jsonObject.get("pnic"));
                    obj.put("ipAddr",jsonObject.getString("ipAddr"));
                    obj.put("netmask",jsonObject.getString("netmask"));
                    obj.put("gateway",jsonObject.getString("gateway"));
                    obj.put("algorithm","speed");
                    obj.put("lacpMode","off");
                    obj.put("reselect",false);
                    hostnetInfo.add(obj);
                }
                vmObj.put("hostNetInfos", hostnetInfo);
                vmObj.put("networkType", networkType);
                vmObj.put("operation", "add");
                vmObj.remove("serverIp");
                vmObj.put("name", request.getName());
                vmObj.put("description", request.getDescription());
                vmObj.put("portNum", request.getPortNum());
                vmObj.put("mode", request.getMode());
                vmObj.put("enableDpdk", false);
                vmObj.put("vlanId", request.getVlanId());
                vmObj.put("mtu", request.getMtu());
                vmObj.put("multicast", request.isMulticast());
                vmObj.put("clusterId", request.getClusterId());
                vmObj.put("networkType", networkType);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return vmObj;
    }

    public static int getType(JSONArray types) {
        int count = 0;
        try {
            if(types!=null)
                for(int i=0;i<types.size();i++) {
                    Integer type = types.getInteger(i);
                    count += Math.pow(2,type);
                }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    private static final String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final Random random = new Random();

    public static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(ALPHA_NUMERIC.length());
            sb.append(ALPHA_NUMERIC.charAt(index));
        }
        return sb.toString();
    }
}
