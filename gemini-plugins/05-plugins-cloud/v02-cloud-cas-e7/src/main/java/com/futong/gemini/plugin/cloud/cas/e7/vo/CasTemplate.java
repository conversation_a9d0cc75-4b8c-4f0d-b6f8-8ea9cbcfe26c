package com.futong.gemini.plugin.cloud.cas.e7.vo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CasTemplate {

    private String	id;
    private String	name;
    private String	description;
    private Integer	memory;
    private Integer	cpu;
    private String	system;
    private String	osVersion;
    private String	createDate;
    private Long    storageCapacity;
    private String	deployed;
    private String	hoststatus;
    private String	hostHaEnable;
    private String	castoolsStatus;
    private String	antivirusEnable;
    private String	uptime;
    private String	autoMem;
    private String	templetStoragePath;
    private String	memoryFormat;
    private String	storageCapacityFormat;
    private String	originate;
}
