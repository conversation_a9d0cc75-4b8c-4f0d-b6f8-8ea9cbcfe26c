package com.futong.gemini.plugin.cloud.cas.e7.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.cas.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cas.e7.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasCluster;
import com.futong.gemini.plugin.cloud.cas.e7.vo.CasClusterVswitch;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbVswitchRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

;

@Slf4j
public class ComputeClusterService {
    public static final ComputeClusterService bean = new ComputeClusterService();

    public void processCluster(JSONObject arguments) {
        List<CmdbVswitchRes> vswitchs = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        JSONArray array = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getClusterListUrl(), null),
                ResourceEnum.CLUSTER.getValue());
        BuilderDevops builder = new BuilderDevops();
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                /** 集群资源数据模型转换 */
                CasCluster cluster = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CasCluster>() {
                });
                Converts.toTmdbDevops(BaseClient.auths.get(), cluster, builder);
                /**集群关联虚拟交换机数据*/
                processClusterVswitch(vswitchs, tags, arguments, cluster.getHostPoolId(), cluster.getId());
            });
        }
        /**
         * 推送集群数据
         */
        BaseUtils.sendMessage(builder.getData(), arguments);

        /**推送集群与主机池关系数据*/
        BaseUtils.sendMessage(builder.getLink(), arguments);

        //推送虚拟交换机数据
        BaseUtils.sendMessage(vswitchs, arguments);

        //推送北新仓关联数据
        BaseUtils.sendMessage(tags, arguments);
    }


    private void processClusterVswitch(List<CmdbVswitchRes> vswitchs, List<TmdbResourceSet> tags, JSONObject arguments, String hostpoolId, String clusterId) {
        CloudAccessBean bean = BaseClient.auths.get();
        JSONArray array = Converts.fetchResourceToJsonArray(
                URLUtils.bean.makeUrl(bean, URLUtils.bean.getClusterVswitchUrl(), new String[]{"", clusterId}),
                ResourceEnum.VSWITCH.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(vswitch -> {
                JSONObject obj = (JSONObject) vswitch;
                /**虚拟交换机模型转换*/
                CmdbVswitchRes vswitchRes = Converts.toNxcSwitch(BaseClient.auths.get(), Converts.parseAndConvert(obj.toJSONString(), new TypeReference<CasClusterVswitch>() {
                }));
                vswitchs.add(vswitchRes);
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_HOST_POOL.value(), TmdbDevops.class, hostpoolId, ResourceType.CMDB_VSWITCH_RES.value(), vswitchRes.getOpen_id()));
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), DevopsSide.DEVOPS_CLUSTER.value(), TmdbDevops.class, clusterId, ResourceType.CMDB_VSWITCH_RES.value(), vswitchRes.getOpen_id()));
            });
        }
    }
}
