package com.futong.gemini.plugin.cloud.cas.e7.http;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cas.e7.util.URLUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;

import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Map;
@Slf4j
public class HttpClientUtil {
    private static final String HTTPS = "https";



    public  static String get(CloudAccessBean bean, String[] url, String[] args){
       String  newUrl  = URLUtils.bean.makeUrl(bean, url,args);
       return  get(newUrl,new HttpClientConfig(),bean);
    }
    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @param username
     * @param password
     * @return
     */
    private static CloseableHttpClient buildDigestHttpClient(String url,String username ,String password) {
        try {
            URL urlObj = new URL(url);
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(new AuthScope(urlObj.getHost(), urlObj.getPort(), "VMC RESTful Web Services"), new UsernamePasswordCredentials(username, password));
            if (url.startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setDefaultCredentialsProvider(credentialsProvider).setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().setDefaultCredentialsProvider(credentialsProvider).build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }
    /**
     * Get http请求
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @return 响应结果字符串
     */
    public static String get(String url, HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient httpClient = buildDigestHttpClient(url,param.getUsername(),param.getPassword());
        HttpGet httpGet = new HttpGet(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            httpGet.addHeader("Content-Type","application/json");
            httpGet.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpGet);
            log.info("response==={}",response);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    public static boolean isJsonarray(String jsonString){
        try {
            new JSONArray(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, String json, HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient  httpClient = buildDigestHttpClient(url,param.getUsername(),param.getPassword());
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200 || response.getStatusLine().getStatusCode() == 204) {
                return getResponseStr(response, config);
            } else {
                String errMsg = "操作失败";
                for (Header header1 : response.getHeaders("Error-Message")) {
                    String value = header1.getValue();
                    errMsg = new String(value.getBytes("ISO-8859-1"), "GBK");
                    System.out.println("编码后的错误信息 "+errMsg);
                }
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, errMsg);
            }
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public static String put(String url, String json, HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient  httpClient = buildDigestHttpClient(url,param.getUsername(),param.getPassword());
        HttpPut httpPut = new HttpPut(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPut.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPut.addHeader(key, header.get(key));
            }
            httpPut.addHeader("Content-Type","application/json");
            httpPut.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            if(ObjectUtil.isNotEmpty(json))
                entityBuilder.setText(json);
            else
                entityBuilder.setText(new JSONObject().toJSONString());
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPut.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPut);
            if (response.getStatusLine().getStatusCode() == 200 || response.getStatusLine().getStatusCode() == 204) {
                return getResponseStr(response, config);
            } else {
                String errMsg = "操作失败";
                for (Header header1 : response.getHeaders("Error-Message")) {
                    String value = header1.getValue();
                    errMsg = new String(value.getBytes("ISO-8859-1"), "GBK");
                    System.out.println("编码后的错误信息 "+errMsg);
                }
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, errMsg);
            }
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public static String delete(String url,HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient  httpClient = buildDigestHttpClient(url,param.getUsername(),param.getPassword());
        HttpDelete httpDelete = new HttpDelete(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            log.info("response.getStatusLine().getStatusCode() = {}",response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() == 200 || response.getStatusLine().getStatusCode() == 204) {
                String msg = "操作成功";
                try {
                    msg =  getResponseStr(response, config);
                }catch (Exception e) {
                    e.printStackTrace();
                }
                return msg;
            } else {
                String errMsg = "操作失败";
                for (Header header1 : response.getHeaders("Error-Message")) {
                    String value = header1.getValue();
                    errMsg = new String(value.getBytes("ISO-8859-1"), "GBK");
                    System.out.println("编码后的错误信息 "+errMsg);
                }
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, errMsg);
            }
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    private static String getResponseStr(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        return EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");

    }
}
