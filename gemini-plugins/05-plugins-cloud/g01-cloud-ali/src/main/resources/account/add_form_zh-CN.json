{"description": "初始纳管账号仅支持主账号纳管", "model": [{"type": "main", "name": "云平台运营主账号", "description": "云平台运营主账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入云账号"}, {"field": "username", "label": "密钥KEY", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥KEY"}, {"field": "password", "label": "密钥", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥"}, {"field": "jsonStr.proxyAddr", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入代理地址"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}, {"scope": "resource", "type": "sub", "name": "云平台运营子账号", "description": "云平台运营子账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入云账号"}, {"field": "username", "label": "密钥KEY", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥KEY"}, {"field": "password", "label": "密钥", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥"}, {"field": "jsonStr.proxyAddr", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入代理地址"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}]}