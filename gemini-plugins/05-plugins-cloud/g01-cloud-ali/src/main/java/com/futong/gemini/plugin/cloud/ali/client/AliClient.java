package com.futong.gemini.plugin.cloud.ali.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class AliClient extends Client {

    public AliClient(Config config, String productId) throws Exception {
        super(config);
        this._productId = productId;
        this._endpoint = EndpointUtils.getEndpoint(productId, this._regionId, this._endpoint);
    }

    private Map<String, ?> doRpc(String action, String version, String method, String bodyType, Map<String, Object> query) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("query", com.aliyun.openapiutil.Client.query(query))));
        return doRPCRequest(action, version, this._protocol, method, "AK", bodyType, req, runtime);
    }
    public Map<String, ?> doRpcPost(String action, String version, String bodyType, Map<String, Object> query) throws Exception {
        return doRpc(action, version, "POST", bodyType, query);
    }

    public JSONObject doRpcPostJSONBody(String action, String version, Map<String, Object> query) throws Exception {
        Map<String, ?> result = doRpcPost(action, version, "string", query);
        return JSONObject.parseObject(result.get("body").toString());
    }

    public Object doRpcPostBody(BaseCloudRequest request) throws Exception {
        String action = request.getBody().getModel().getString("apiAction");
        String version = request.getBody().getModel().getString("apiVersion");
        String method = request.getBody().getModel().getString("apiMethod");
        String bodyType = request.getBody().getModel().getString("apiBodyType");
        Map<String, ?> result = doRpc(action, version, StrUtil.emptyToDefault(method, "POST"), StrUtil.emptyToDefault(bodyType, "json"), request.getBody().getCloud());
        return result.get("body");
    }

    public JSONObject doRpcPostJSONBody(BaseCloudRequest request) throws Exception {
        String action = request.getBody().getApi().getString("action");
        String version = request.getBody().getApi().getString("version");
        String method = request.getBody().getApi().getString("method");
        Map<String, ?> result = doRpc(action, version, StrUtil.emptyToDefault(method, "POST"), "string", request.getBody().getCloud());
        return JSONObject.parseObject(result.get("body").toString());
    }

    public static void main(String[] args) throws Exception {
        Config config = new Config();
        config.accessKeyId = "LTAI5t6bMLqwtAfWWxwqj7FC";
        config.accessKeySecret = "******************************";
        config.regionId = "cn-beijing";
        AliClient client = new AliClient(config, "ecs");
        JSONObject query=new JSONObject();
        query.put("regionId", "cn-beijing");
        JSONObject result = client.doRpcPostJSONBody("DescribeAutoSnapshotPolicyEx", "2014-05-26", query);
        System.out.println(result.toJSONString());

    }


}
