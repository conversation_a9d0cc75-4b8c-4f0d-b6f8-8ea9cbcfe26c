package com.futong.gemini.plugin.cloud.ali.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.bssopenapi20171214.models.DescribeInstanceBillRequest;
import com.aliyun.bssopenapi20171214.models.DescribeInstanceBillResponseBody;
import com.aliyun.cms20190101.models.DescribeAlertLogListResponseBody;
import com.aliyun.cms20190101.models.DescribeSystemEventAttributeResponseBody;
import com.aliyun.ecs20140526.models.*;
import com.aliyun.oss.model.*;
import com.aliyun.slb20140515.models.DescribeLoadBalancersResponse;
import com.aliyun.slb20140515.models.DescribeLoadBalancersResponseBody;
import com.aliyun.vpc20160428.models.DescribeRouteTableListResponse;
import com.aliyun.vpc20160428.models.DescribeRouteTableListResponseBody;
import com.aliyun.vpc20160428.models.DescribeSnatTableEntriesResponse;
import com.aliyun.vpc20160428.models.DescribeSnatTableEntriesResponseBody;
import com.futong.common.utils.TimeUtils;
import com.futong.common.utils.UnitUtil;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.BillInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.ali.common.Constant;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

@Slf4j
public class Convert {
    static Map<String, Map<String, String>> diskTypes = new HashMap<>();

    static {
        // 定义一个辅助方法用于创建内部的Map
        diskTypes.put("cloud", createDiskEntry("cloud", "普通云盘"));
        diskTypes.put("cloud_efficiency", createDiskEntry("cloud_efficiency", "高效云盘"));
        diskTypes.put("cloud_ssd", createDiskEntry("cloud_ssd", "SSD 云盘"));
        diskTypes.put("cloud_essd", createDiskEntry("cloud_essd", "ESSD 云盘"));
        diskTypes.put("cloud_auto", createDiskEntry("cloud_auto", "ESSD AutoPL 云盘"));
        diskTypes.put("cloud_essd_entry", createDiskEntry("cloud_essd_entry", "ESSD Entry 云盘"));
        diskTypes.put("cloud_regional_disk_auto", createDiskEntry("cloud_regional_disk_auto", "ESSD 同城冗余云盘"));
        diskTypes.put("elastic_ephemeral_disk_standard", createDiskEntry("elastic_ephemeral_disk_standard", "弹性临时盘-标准版"));
        diskTypes.put("elastic_ephemeral_disk_premium", createDiskEntry("elastic_ephemeral_disk_premium", "弹性临时盘-高级版"));
    }

    private static Map<String, String> createDiskEntry(String key, String value) {
        Map<String, String> diskEntry = new HashMap<>();
        diskEntry.put("key", key);
        diskEntry.put("value", value);
        return diskEntry;
    }

    private static List<Map<String, String>> filterDiskTypes(List<String> keys) {
        List<Map<String, String>> filteredList = new ArrayList<>();
        for (String key : keys) {
            if (diskTypes.containsKey(key)) {
                filteredList.add(diskTypes.get(key));
            }
        }
        return filteredList;
    }

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(CloudType.PUBLIC_ALI.value());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    public static Map<Class, List> convertRegion(BaseCloudRequest request, DescribeRegionsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body
                || null == response.body.regions
                || CollUtil.isEmpty(response.body.regions.region)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的规格信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_ALI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                response.body.regions.region,
                DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion::getLocalName,
                DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion::getRegionId
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    public static Map<Class, List> convertZone(BaseCloudRequest request, DescribeZonesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.zones || CollUtil.isEmpty(response.body.zones.zone)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        //将获取到的可用区信息转换为CI模型
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI.value(),
                        request.getBody().getCloud().getString("regionId"),
                        DevopsSide.DEVOPS_REGION.value());
        BuilderResourceSet builder = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_FLAVOR
                );
        for (DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone zone : response.body.zones.zone) {
            JSONObject infoJson = new JSONObject();
            if (zone.getAvailableDiskCategories() != null
                    && CollUtil.isNotEmpty(zone.getAvailableDiskCategories().diskCategories)) {
                infoJson.put("diskCategories", filterDiskTypes(zone.getAvailableDiskCategories().diskCategories));
            }

            BuilderDevops devopsZone = new BuilderDevops()
                    .withDevops(devops.get(), zone.localName, zone.zoneId, DevopsSide.DEVOPS_ZONE.value())
                    .withJson(infoJson.toJSONString());
            data.add(devopsZone.get());
            links.add(devopsZone.builderLink(devops.get()));
            if (ObjectUtil.isEmpty(zone.getAvailableResources())
                    || CollUtil.isEmpty(zone.getAvailableResources().resourcesInfo)
            ) {
                log.info("{}可用区无可用规格", zone.getLocalName());
            } else {
                zone.getAvailableResources().resourcesInfo.stream()
                        .filter(t -> ObjectUtil.isNotNull(t.instanceTypes)
                                && CollUtil.isNotEmpty(t.instanceTypes.supportedInstanceType))
                        .flatMap(t -> t.instanceTypes.supportedInstanceType.stream())
                        .forEach(instanceType -> {
                            TmdbResourceSet zoneFlavor = builder.builderResourceSet(
                                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), instanceType, request.getBody().getCloud().getString("regionId")),
                                    DevopsSide.DEVOPS_ZONE,
                                    devopsZone.get().getBiz_id()
                            );
                            sets.add(zoneFlavor);
                        });
            }
        }
        result.put(TmdbDevops.class, data);
        result.put(TmdbDevopsLink.class, links);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<Class, List> convertFlavor(BaseCloudRequest request, DescribeInstanceTypesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.instanceTypes || CollUtil.isEmpty(response.body.instanceTypes.instanceType)) {
            result.put(CmdbFlavor.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbFlavor> data = new ArrayList<>();
        for (DescribeInstanceTypesResponseBody.DescribeInstanceTypesResponseBodyInstanceTypesInstanceType res : response.body.instanceTypes.instanceType) {
            CmdbFlavor ci = new CmdbFlavor();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceTypeId(), request.getBody().getCloud().getString("regionId")));
            ci.setCpu_arch(res.cpuArchitecture);
            ci.setCpu_size(res.getCpuCoreCount());
            ci.setMem_size((int) (res.getMemorySize() * 1024));//库里存MB
            if (null != res.getLocalStorageCapacity()) {
                ci.setMin_sysdisk_size(res.getLocalStorageCapacity().floatValue());
            }
            ci.setSpecification_class_code(res.instanceTypeFamily);
            ci.setSpecification_class_name(res.instanceTypeFamily);
            if (StrUtil.isNotEmpty(res.GPUSpec)) {
                ci.setCategory("GPU");
            } else {
                ci.setCategory("CPU");
            }
            ci.setGpu_size(res.GPUMemorySize);
            ci.setGpu_num(res.GPUAmount);
            ci.setGpu_model(res.GPUSpec);
            ci.setOpen_id(res.getInstanceTypeId());
            ci.setOpen_name(res.getInstanceTypeId());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_FLAVOR
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbFlavor.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<Class, List> convertImage(BaseCloudRequest request, DescribeImagesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.images || CollUtil.isEmpty(response.body.images.image)) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        for (DescribeImagesResponseBody.DescribeImagesResponseBodyImagesImage image : response.body.images.image) {
            CmdbImageRes ci = new CmdbImageRes();
            CmdbOsRes os = new CmdbOsRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), image.getImageId(), request.getBody().getCloud().getString("regionId")));
            ci.setType(image.getOSType());
            ci.setSize(image.getSize().floatValue());
            ci.setStatus(image.getStatus());
            ci.setDesc(image.getDescription());
            ci.setOpen_id(image.getImageId());
            ci.setOpen_name(image.getImageName());
            ci.setVisibility(image.isPublic ? "public" : "private");
            /**
             * ImageOwnerAlias
             * 镜像来源。取值范围：
             *
             * system：阿里云官方提供的，且不是通过云市场发布的镜像，和控制台中的“公共镜像”概念不同。
             * self：您创建的自定义镜像。
             * others：包含共享镜像（其他阿里云用户直接共享给您的镜像）和社区镜像（任意阿里云用户将其自定义镜像完全公开共享后的镜像）。您需要注意：
             * 查找社区镜像时，IsPublic 必须为 true。
             * 查找共享镜像时，IsPublic 需要设置为 false 或者不传值。
             * marketplace：阿里云或者第三方供应商 ISV 在云市场发布的镜像，需要和 ECS 一起购买。请自行留意云市场镜像的收费详情。
             */
            switch (image.getImageOwnerAlias()) {
                case "others":
                case "marketplace":
                    ci.setImage_source("other");
                    break;
                default:
                    ci.setImage_source(image.getImageOwnerAlias());
            }
            toCiResCloud(request, ci);
            dataImage.add(ci);
            //操作新系统OS对象
            os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), image.getImageId(), request.getBody().getCloud().getString("regionId")));
            os.setCpu_arch(image.architecture);
            os.setName(image.platform);
            os.setType(image.getOSType());
            os.setVersion(image.getOSName());
            os.setFull_name(image.getOSName());
            os.setOpen_name(image.imageName);
            os.setOpen_id(image.getImageId());
            toCiResCloud(request, os);
            dataOs.add(os);
            Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
            dataAss.add(osa);
        }
        List<TmdbResourceSet> dataSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(), CloudType.PUBLIC_ALI)
                .withDataByDevopsValue(ResourceType.CMDB_IMAGE_RES, dataImage, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .withDataByDevopsValue(ResourceType.CMDB_OS_RES, dataOs, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, dataSet);
        return result;
    }

    public static Map<Class, List> convertEip(BaseCloudRequest request, com.aliyun.vpc20160428.models.DescribeEipAddressesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.eipAddresses || CollUtil.isEmpty(response.body.eipAddresses.eipAddress)) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbEipRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (com.aliyun.vpc20160428.models.DescribeEipAddressesResponseBody.DescribeEipAddressesResponseBodyEipAddressesEipAddress eip : response.body.eipAddresses.eipAddress) {
            CmdbEipRes ci = new CmdbEipRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getAllocationId()));
            ci.setBandwidth_type(eip.getISP());
            ci.setBandwidth_speed(eip.bandwidth);
            ci.setElastic_ip(eip.getIpAddress());
            /*
            云原生状态为：
            Associating：绑定中。
            Unassociating：解绑中。
            InUse：已分配。
            Available：可用。
            Releasing：释放中。
            统一后为：
            inuse：已分配。
            available：可用。
            error:错误
            other:其他
             */
            if("InUse".equalsIgnoreCase(eip.getStatus())){
                ci.setStatus("inuse");
            }else if("Available".equalsIgnoreCase(eip.getStatus())){
                ci.setStatus("available");
            }else {
                ci.setStatus("other");
            }
            ci.setOpen_status(eip.status);
            ci.setDesc(eip.getDescritpion());
            ci.setOpen_id(eip.getAllocationId());
            ci.setOpen_name(eip.getName());
            toCiResCloud(request, ci);
            data.add(ci);
            if (StrUtil.isNotEmpty(eip.getInstanceId())) {
                if ("EcsInstance".equals(eip.getInstanceType())) {
                    Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getInstanceId()));
                    associations.add(instance);
                }
                if ("Nat".equals(eip.getInstanceType())) {
                    Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getInstanceId()));
                    associations.add(nat);
                }
            }
        }

        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_EIP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertKeyPair(BaseCloudRequest request, DescribeKeyPairsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.keyPairs || CollUtil.isEmpty(response.body.keyPairs.keyPair)) {
            result.put(CmdbKeypairRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbKeypairRes> data = new ArrayList<>();
        for (DescribeKeyPairsResponseBody.DescribeKeyPairsResponseBodyKeyPairsKeyPair res : response.body.keyPairs.keyPair) {
            CmdbKeypairRes ci = new CmdbKeypairRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getKeyPairName()));
            ci.setOpen_id(res.getKeyPairName());
            ci.setOpen_name(res.getKeyPairName());
            ci.setFingerprint(res.getKeyPairFingerPrint());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_KEYPAIR_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbKeypairRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, DescribeSecurityGroupsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.securityGroups || CollUtil.isEmpty(response.body.securityGroups.securityGroup)) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSecurityGroupsResponseBody.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup res : response.body.securityGroups.securityGroup) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSecurityGroupId()));
            ci.setOpen_id(res.getSecurityGroupId());
            ci.setOpen_name(res.getSecurityGroupName());
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreationTime()));
            //关联VPC
            if (StrUtil.isNotEmpty(res.getVpcId())) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
                associations.add(vpc);
            }
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_SECURITYGROUP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    public static Map<Class, List> convertSecurityGroupRule(BaseCloudRequest request, DescribeSecurityGroupAttributeResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.permissions || CollUtil.isEmpty(response.body.permissions.permission)) {
            result.put(CmdbSecuritygroupRule.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        String securityGroupResId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), response.body.securityGroupId);
        List<CmdbSecuritygroupRule> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSecurityGroupAttributeResponseBody.DescribeSecurityGroupAttributeResponseBodyPermissionsPermission res : response.body.permissions.permission) {
            CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.securityGroupRuleId));
            ci.setOpen_id(res.securityGroupRuleId);
            ci.setOpen_name(ci.getOpen_id());
            ci.setDirection(res.direction);
            ci.setOpen_direction(res.direction);
            ci.setPolicy(res.policy);
            ci.setOpen_policy(res.policy);
            ci.setIp_protocol(res.ipProtocol);
            ci.setPriority(res.priority);
            ci.setPort_range(res.portRange);
            ci.setSource_cidr(res.sourceCidrIp);
            ci.setDest_cidr(res.destCidrIp);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.createTime));
            toCiResCloud(request, ci);
            data.add(ci);
            //关联安全组规则
            Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, securityGroupResId);
            associations.add(rule);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_SECURITYGROUP_RULE
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSecuritygroupRule.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertVpc(BaseCloudRequest request, com.aliyun.vpc20160428.models.DescribeVpcsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.vpcs || CollUtil.isEmpty(response.body.vpcs.vpc)) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbVpcRes> data = new ArrayList<>();
        for (com.aliyun.vpc20160428.models.DescribeVpcsResponseBody.DescribeVpcsResponseBodyVpcsVpc res : response.body.vpcs.vpc) {
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            ci.setOpen_id(res.vpcId);
            ci.setOpen_name(res.vpcName);
            ci.setCidr(res.cidrBlock);
            ci.setStatus(res.status);
            ci.setOpen_status(res.status);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_VPC_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<Class, List> convertSubnet(BaseCloudRequest request, com.aliyun.vpc20160428.models.DescribeVSwitchesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.vSwitches || CollUtil.isEmpty(response.body.vSwitches.vSwitch)) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_SUBNET_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_ALI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (com.aliyun.vpc20160428.models.DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch res : response.body.vSwitches.vSwitch) {
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVSwitchId()));
            ci.setOpen_id(res.vSwitchId);
            ci.setOpen_name(res.vSwitchName);
            ci.setCidr_ipv4(res.cidrBlock);
            ci.setCidr_ipv6(res.ipv6CidrBlock);
            ci.setStatus(res.status);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            ci.setAvailable_ip_count_ipv4(res.availableIpAddressCount.intValue());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getZoneId());
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vpcId));
            associations.add(rule);
        }
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertNat(BaseCloudRequest request, com.aliyun.vpc20160428.models.DescribeNatGatewaysResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.natGateways || CollUtil.isEmpty(response.body.natGateways.natGateway)) {
            result.put(CmdbNatRes.class, null);
            result.put(CmdbIpRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbBaseMetainfo.class, null);
            return result;
        }
        List<CmdbNatRes> data = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbBaseMetainfo> metainfos = new ArrayList<>();
        for (com.aliyun.vpc20160428.models.DescribeNatGatewaysResponseBody.DescribeNatGatewaysResponseBodyNatGatewaysNatGateway res : response.body.natGateways.natGateway) {
            CmdbNatRes ci = new CmdbNatRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.natGatewayId));
            ci.setOpen_id(res.natGatewayId);
            ci.setOpen_name(res.name);
            ci.setStatus(res.status);
            ci.setAdmin_state(res.businessStatus);
            ci.setSpec(res.spec);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            CmdbBaseMetainfo metainfo = new CmdbBaseMetainfo();
            metainfo.setAccount_id(ci.getAccount_id());
            metainfo.setCloud_type(ci.getCloud_type());
            metainfo.setRes_id(ci.getRes_id());
            metainfo.setTable("cmdb_nat_res");
            metainfo.setMetainfo(JSON.toJSONString(ci));
            metainfos.add(metainfo);
            toCiResCloud(request, ci);
            data.add(ci);
            //关联子网
            if (null != res.natGatewayPrivateInfo
                    && StrUtil.isNotEmpty(res.natGatewayPrivateInfo.vswitchId)) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNatGatewayPrivateInfo().getVswitchId()));
                associations.add(subnet);
                //要查询的 NAT 网关的类型，取值：
                //internet：公网 NAT 网关。
                //intranet：VPC NAT 网关。
                if ("intranet".equals(res.getNetworkType())
                        && StrUtil.isNotEmpty(res.natGatewayPrivateInfo.privateIpAddress)
                ) {
                    CmdbIpRes privateIp = new CmdbIpRes();
                    privateIp.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.natGatewayPrivateInfo.privateIpAddress));
                    privateIp.setType(IpType.PRIVATE_IP.value());
                    privateIp.setAddress(res.natGatewayPrivateInfo.privateIpAddress);
                    privateIp.setOpen_id(res.natGatewayPrivateInfo.privateIpAddress);
                    privateIp.setOpen_name(res.natGatewayPrivateInfo.privateIpAddress);
                    toCiResCloud(request, privateIp);
                    ips.add(privateIp);
                    Association privateIpAssociation = AssociationUtils.toAssociation(ci, privateIp);
                    associations.add(privateIpAssociation);
                }
            }
            //internet：公网 NAT 网关。则此信息不为空，内存公网ip
            if (null != res.getIpLists()
                    && CollUtil.isNotEmpty(res.getIpLists().ipList)
            ) {
                for (com.aliyun.vpc20160428.models.DescribeNatGatewaysResponseBody.DescribeNatGatewaysResponseBodyNatGatewaysNatGatewayIpListsIpList nip : res.getIpLists().ipList) {
                    CmdbIpRes publicIp = new CmdbIpRes();
                    publicIp.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nip.ipAddress));
                    publicIp.setType(IpType.PUBLIC_IP.value());
                    publicIp.setAddress(nip.ipAddress);
                    publicIp.setOpen_id(nip.ipAddress);
                    publicIp.setOpen_name(nip.ipAddress);
                    toCiResCloud(request, publicIp);
                    ips.add(publicIp);
                    Association publicIpAssociation = AssociationUtils.toAssociation(ci, publicIp);
                    associations.add(publicIpAssociation);
                    CmdbIpRes privateIp = new CmdbIpRes();
                    privateIp.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nip.privateIpAddress));
                    privateIp.setType(IpType.PRIVATE_IP.value());
                    privateIp.setAddress(nip.privateIpAddress);
                    privateIp.setOpen_id(nip.privateIpAddress);
                    privateIp.setOpen_name(nip.privateIpAddress);
                    toCiResCloud(request, privateIp);
                    ips.add(privateIp);
                    Association privateIpAssociation = AssociationUtils.toAssociation(ci, privateIp);
                    associations.add(privateIpAssociation);
                }
            }
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vpcId));
            associations.add(rule);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_NAT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbNatRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        result.put(CmdbBaseMetainfo.class, metainfos);
        return result;
    }

    public static Map<Class, List> convertNatEntry(BaseCloudRequest request, com.aliyun.vpc20160428.models.DescribeForwardTableEntriesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.forwardTableEntries || CollUtil.isEmpty(response.body.forwardTableEntries.forwardTableEntry)) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (com.aliyun.vpc20160428.models.DescribeForwardTableEntriesResponseBody.DescribeForwardTableEntriesResponseBodyForwardTableEntriesForwardTableEntry res : response.body.forwardTableEntries.forwardTableEntry) {
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.forwardEntryId));
            ci.setOpen_id(res.forwardEntryId);
            ci.setOpen_name(res.forwardEntryName);
            ci.setProtocol(res.ipProtocol);
            ci.setPrivate_port(res.getInternalPort());
            ci.setPrivate_ip(res.getInternalIp());
            ci.setPublic_port(res.getExternalPort());
            ci.setPublic_ip(res.getExternalIp());
            ci.setType("DNAT");
            toCiResCloud(request, ci);
            //关联NAT
            Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, request.getBody().getCi().getString("resId"));
            associations.add(nat);
            data.add(ci);
        }
        result.put(CmdbNatEntryRes.class, data);
        result.put(Association.class, associations);
        return result;
    }


    public static Map<Class, List> convertNatEntry(BaseCloudRequest request, DescribeSnatTableEntriesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.snatTableEntries || CollUtil.isEmpty(response.body.snatTableEntries.snatTableEntry)) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSnatTableEntriesResponseBody.DescribeSnatTableEntriesResponseBodySnatTableEntriesSnatTableEntry res : response.body.snatTableEntries.snatTableEntry) {
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.snatEntryId));
            ci.setOpen_id(res.snatEntryId);
            ci.setOpen_name(res.snatEntryName);
            ci.setCidr(res.sourceCIDR);
            ci.setPublic_ip(res.snatIp);
            ci.setType("SNAT");
            toCiResCloud(request, ci);
            //关联NAT
            Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, request.getBody().getCi().getString("resId"));
            associations.add(nat);
            if (StrUtil.isNotEmpty(res.sourceVSwitchId)) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.sourceVSwitchId));
                associations.add(subnet);
            }
            associations.add(nat);
            data.add(ci);
        }
        result.put(CmdbNatEntryRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertRoute(BaseCloudRequest request, DescribeRouteTableListResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.routerTableList || CollUtil.isEmpty(response.body.routerTableList.routerTableListType)) {
            result.put(CmdbRouteRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbRouteRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeRouteTableListResponseBody.DescribeRouteTableListResponseBodyRouterTableListRouterTableListType res : response.body.routerTableList.routerTableListType) {
            CmdbRouteRes ci = new CmdbRouteRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.routeTableId));
            ci.setOpen_id(res.routeTableId);
            ci.setOpen_name(res.routeTableName);
            ci.setStatus(res.status);
            ci.setDefault_route(res.routeTableType);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            toCiResCloud(request, ci);
            //关联VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vpcId));
            if (null != res.getVSwitchIds() && CollUtil.isNotEmpty(res.getVSwitchIds().vSwitchId)) {
                for (String subnetId : res.getVSwitchIds().vSwitchId) {
                    Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnetId));
                    associations.add(subnet);
                }
            }
            associations.add(vpc);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_ROUTE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbRouteRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, DescribeSnapshotsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.snapshots || CollUtil.isEmpty(response.body.snapshots.snapshot)) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSnapshotsResponseBody.DescribeSnapshotsResponseBodySnapshotsSnapshot res : response.body.snapshots.snapshot) {
            CmdbSnapshotRes ci = new CmdbSnapshotRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.snapshotId));
            ci.setOpen_id(res.snapshotId);
            ci.setOpen_name(res.snapshotName);
            ci.setStatus(res.status);
            ci.setSize(NumberUtil.parseFloat(res.sourceDiskSize));
            ci.setType(res.snapshotType);
            ci.setEncrypted(res.encrypted);
            ci.setProgress(res.progress);
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            toCiResCloud(request, ci);
            //关联磁盘
            Association disk = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.sourceDiskId));
            associations.add(disk);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_SNAPSHOT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbSnapshotRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertLoadBalancer(BaseCloudRequest request, DescribeLoadBalancersResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.loadBalancers || CollUtil.isEmpty(response.body.loadBalancers.loadBalancer)) {
            result.put(CmdbLoadbalanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbLoadbalanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeLoadBalancersResponseBody.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer res : response.body.loadBalancers.loadBalancer) {
            CmdbLoadbalanceRes ci = new CmdbLoadbalanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.loadBalancerId));
            ci.setOpen_id(res.loadBalancerId);
            ci.setOpen_name(res.loadBalancerName);
            ci.setNetwork_type(res.networkType);
            ci.setAddress(res.address);
            //负载均衡实例状态。取值：
            //inactive：实例已停止，此状态的实例监听不会再转发流量。
            //active：实例运行中，实例创建后，默认状态为 active。
            //locked：实例已锁定，实例已经被锁定。
            ci.setOpen_status(res.getLoadBalancerStatus());
            ci.setStatus(LoadBalanceStatus.fromValue(res.getLoadBalancerStatus()).name());
            if (res.bandwidth != null) {
                ci.setBandwidth_speed(res.bandwidth.toString());
            }
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.createTime));
            toCiResCloud(request, ci);
            data.add(ci);
            //关联vpc
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vpcId));
            associations.add(vpc);
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vSwitchId));
            associations.add(subnet);
            toCiResCloud(request, ci);

        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_LOADBALANCE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbLoadbalanceRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, DescribeInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.instances || CollUtil.isEmpty(response.body.instances.instance)) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_INSTANCE_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_ALI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        for (DescribeInstancesResponseBody.DescribeInstancesResponseBodyInstancesInstance res : response.body.instances.instance) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setCpu_size(res.getCpu());
            ci.setMem_size(res.getMemory());
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            toCiResCloud(request, ci);
            //实例状态。取值范围：
            //Pending：创建中。
            //Running：运行中。
            //Starting：启动中。
            //Stopping：停止中。
            //Stopped：已停止。
            switch (res.getStatus()) {
                case "Pending":
                    ci.setStatus(InstanceStatus.BUILDING.value());
                    break;
                case "Running":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "Starting":
                    ci.setStatus(InstanceStatus.STARTING.value());
                    break;
                case "Stopping":
                    ci.setStatus(InstanceStatus.STOPPING.value());
                    break;
                case "Stopped":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
            }

            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getZoneId());
            //关联规格
            Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceType(), request.getBody().getCloud().getString("regionId")));
            associations.add(flavor);
            //关联镜像
            Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getImageId(), request.getBody().getCloud().getString("regionId")));
            associations.add(image);
            //关联镜像OS
            Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getImageId(), request.getBody().getCloud().getString("regionId")));
            associations.add(os);
            //关联网络VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcAttributes().vpcId));
            associations.add(vpc);
            //关联网络VPC子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcAttributes().getVSwitchId()));
            associations.add(subnet);
            //关联密钥对
            if (StrUtil.isNotEmpty(res.getKeyPairName())) {
                //关联密钥对
                Association keyPair = AssociationUtils.toAssociation(ci, CmdbKeypairRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getKeyPairName()));
                associations.add(keyPair);
            }
            //添加私网IP
            if (ObjectUtil.isNotNull(res.getNetworkInterfaces()) && CollectionUtil.isNotEmpty(res.getNetworkInterfaces().networkInterface)) {
                for (DescribeInstancesResponseBody.DescribeInstancesResponseBodyInstancesInstanceNetworkInterfacesNetworkInterface ni : res.getNetworkInterfaces().networkInterface) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ni.primaryIpAddress));
                    ip.setType(IpType.PRIVATE_IP.value());
                    ip.setAddress(ni.primaryIpAddress);
                    ip.setOpen_id(ni.networkInterfaceId);
                    ip.setOpen_name(ni.primaryIpAddress);
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联网络VPC
                    associations.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcAttributes().vpcId)));
                    //关联网络VPC子网
                    associations.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcAttributes().getVSwitchId())));
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ip, ci));
                }
            }
            //添加公网ip(此信息如不为空则说明此为创建云主机同步创建的共网IP)
            if (ObjectUtil.isNotNull(res.getPublicIpAddress()) && CollectionUtil.isNotEmpty(res.getPublicIpAddress().getIpAddress())) {
                for (String ipAddress : res.getPublicIpAddress().getIpAddress()) {
                    CmdbIpRes ip = new CmdbIpRes();
                    ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipAddress));
                    ip.setType(IpType.PUBLIC_IP.value());
                    ip.setAddress(ipAddress);
                    ip.setOpen_id(ipAddress);
                    ip.setOpen_name(ipAddress);
                    toCiResCloud(request, ip);
                    ips.add(ip);
                    //关联云主机
                    associations.add(AssociationUtils.toAssociation(ip, ci));
                }
            }
            //添加公网弹性IP,关联弹性IP
            if (ObjectUtil.isNotNull(res.eipAddress) && StrUtil.isNotEmpty(res.eipAddress.ipAddress)) {
                CmdbIpRes ip = new CmdbIpRes();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.eipAddress.ipAddress));
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(res.eipAddress.ipAddress);
                ip.setOpen_id(res.eipAddress.ipAddress);
                ip.setOpen_name(res.eipAddress.ipAddress);
                toCiResCloud(request, ip);
                ips.add(ip);
                //IP关联云主机
                associations.add(AssociationUtils.toAssociation(ip, ci));
                //云主机关联EIP
                Association eip = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.eipAddress.allocationId));
                associations.add(eip);
            }
            //关联安全组
            if (res.getSecurityGroupIds() != null && CollectionUtil.isNotEmpty(res.getSecurityGroupIds().securityGroupId)) {
                res.getSecurityGroupIds().securityGroupId.forEach(t -> {
                    Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), t));
                    associations.add(securityGroup);
                });
            }
            data.add(ci);
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertNetcard(BaseCloudRequest request, DescribeNetworkInterfacesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.networkInterfaceSets || CollUtil.isEmpty(response.body.networkInterfaceSets.networkInterfaceSet)) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNetworkInterfacesResponseBody.DescribeNetworkInterfacesResponseBodyNetworkInterfaceSetsNetworkInterfaceSet res : response.body.networkInterfaceSets.networkInterfaceSet) {
            CmdbNetcardRes ci = new CmdbNetcardRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.networkInterfaceId));
            ci.setOpen_id(res.networkInterfaceId);
            ci.setOpen_name(res.networkInterfaceName);
            ci.setType(res.type);
            ci.setStatus(res.status);
            ci.setMac_address(res.macAddress);
            ci.setDesc(res.description);
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.creationTime));
            toCiResCloud(request, ci);
            if (res.privateIpSets != null && CollectionUtil.isNotEmpty(res.privateIpSets.privateIpSet)) {
                StringBuilder ipv4s = new StringBuilder();
                for (DescribeNetworkInterfacesResponseBody.DescribeNetworkInterfacesResponseBodyNetworkInterfaceSetsNetworkInterfaceSetPrivateIpSetsPrivateIpSet ipv4 : res.privateIpSets.privateIpSet) {
                    if (StrUtil.isNotEmpty(ipv4.getPrivateIpAddress())) {
                        ipv4s.append(ipv4.getPrivateIpAddress()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv4.getPrivateIpAddress())));
                    }

                    if (ipv4.getAssociatedPublicIp() != null && StrUtil.isNotEmpty(ipv4.getAssociatedPublicIp().getPublicIpAddress())) {
                        ipv4s.append(ipv4.getAssociatedPublicIp().getPublicIpAddress()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv4.getAssociatedPublicIp().getPublicIpAddress())));
                    }
                }
                if (ipv4s.length() > 0) {
                    ipv4s.setLength(ipv4s.length() - 1);
                    ci.setIpv4_address(ipv4s.toString());
                }
            }
            if (res.ipv6Sets != null && CollectionUtil.isNotEmpty(res.ipv6Sets.ipv6Set)) {
                StringBuilder ipv6s = new StringBuilder();
                for (DescribeNetworkInterfacesResponseBody.DescribeNetworkInterfacesResponseBodyNetworkInterfaceSetsNetworkInterfaceSetIpv6SetsIpv6Set ipv6 : res.ipv6Sets.ipv6Set) {
                    if (StrUtil.isNotEmpty(ipv6.getIpv6Address())) {
                        ipv6s.append(ipv6.getIpv6Address()).append(",");
                        associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), ipv6.getIpv6Address())));
                    }
                }
                if (ipv6s.length() > 0) {
                    ipv6s.setLength(ipv6s.length() - 1);
                    ci.setIpv6_address(ipv6s.toString());
                }
            }
            //关联VPC
            if (StrUtil.isNotEmpty(res.vpcId)) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vpcId));
                associations.add(vpc);
            }
            //关联子网
            if (StrUtil.isNotEmpty(res.vSwitchId)) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.vSwitchId));
                associations.add(subnet);
            }
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_NETCARD_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertDisk(BaseCloudRequest request, DescribeDisksResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.disks || CollUtil.isEmpty(response.body.disks.disk)) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_ALI,
                        ResourceType.CMDB_DISK_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_ALI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        for (DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk res : response.body.disks.disk) {
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getDiskId()));
            ci.setType(res.getCategory());
            ci.setCategory(res.getType());
            ci.setShare_disk(res.getMultiAttach());
            ci.setSize(res.getSize().floatValue());
            ci.setStatus(DiskStatus.fromValue(res.status).value());
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            ci.setOpen_id(res.getDiskId());
            ci.setOpen_name(res.getDiskName());
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getZoneId());
            toCiResCloud(request, ci);
            //关联实例
            if (StrUtil.isNotEmpty(res.getInstanceId())) {
                Association esc = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
                associations.add(esc);
            }
            data.add(ci);
        }
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<String, PerfInfoBean> convertEcsPerf(BaseCloudRequest request, List<JSONObject> response) {
        if (CollUtil.isEmpty(response)) {
            return null;
        }
        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (JSONObject metric : response) {
            String instanceId = metric.getString("instanceId");//资源ID
            String metricName = metric.getString("metricName");//指标名称
            Double average = metric.getDouble("Average");//指标平均值
            Long timestamp = metric.getLong("timestamp");//时间戳
            String id = instanceId + "_" + timestamp;
            PerfInfoBean perf = perfMap.get(id);
            if (perf == null) {
                //生成指标对象
                ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                perf = new PerfInfoBean();//指标对应得资源CI信息
                perf.setAccountId(instanceDisk.getAccount_id());
                perf.setCloudType(instanceDisk.getCloud_type());
                perf.setResId(instanceDisk.getRes_id());
                perf.setOpenId(instanceDisk.getOpen_id());
                perf.setOpenName(instanceDisk.getOpen_name());
                perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                perf.setCreateTime(DateUtil.formatDateTime(DateUtil.date(timestamp)));
                if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                    Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                    perf.setDiskSize(sum);
                }
                perf.setId(id);
            }
            BiConsumer<PerfInfoBean, Double> setValue = Constant.perfMapping.get(metricName);
            setValue.accept(perf, average);//设置监控指标值
            perfMap.put(perf.getId(), perf);
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    public static CmdbBucketRes convertBucket(BaseCloudRequest request, Bucket bucket, List<OSSObjectSummary> os, BucketReferer br, AccessControlList acl, BucketVersioningConfiguration bvc) {
        CmdbBucketRes ci = new CmdbBucketRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), bucket.getName()));
        ci.setOpen_id(bucket.getName());
        ci.setOpen_name(bucket.getName());
        ci.setCreate_time(TimeUtils.utcDateToInstant(bucket.getCreationDate()).toEpochMilli());
        toCiResCloud(request, ci);
        //类型
        if (ObjectUtil.isNotEmpty(bucket.getStorageClass())) {
            ci.setStorage_type(bucket.getStorageClass().toString());
        }
        //访问权限
        if (ObjectUtil.isNotEmpty(acl)) {
            ci.setAcl(acl.getCannedACL().toString());
        }
        //多版本状态
        if (ObjectUtil.isNotEmpty(bvc)) {
            ci.setVersioning_status(bvc.getStatus());
        }
        //防盗链
        if (CollUtil.isNotEmpty(br.getRefererList())) {
            ci.setPolicy(JSONArray.toJSONString(br.getRefererList()));
        }
        //存量
        if (CollUtil.isNotEmpty(os)) {
            float storageSize = 0f;
            for (OSSObjectSummary objectSummary : os) {
                storageSize += objectSummary.getSize();
            }
            ci.setSize(UnitUtil.convert(storageSize, UnitUtil.BYTE, UnitUtil.GB, 3));
            ci.setObject_number("" + os.size());
        }
        //生命周期
//        if (ObjectUtil.isNotEmpty(lifecycle)) {
//            ci.setLifecycle(JSONArray.toJSONString(lifecycle));
//        }
        //静态页面
//        if (ObjectUtil.isNotEmpty(bucketWebsiteResult)) {
//            obj.setStaticPage(JSONArray.toJSONString(bucketWebsiteResult.getRoutingRules()));
//        }
        //跨域访问
//        if (ObjectUtil.isNotEmpty(cors)) {
//            obj.setCrossDomain(JSONArray.toJSONString(cors));
//        }
        return ci;
    }

    public static CmdbBucketFileRes convertBucketFile(BaseCloudRequest request, OSSObjectSummary object, ObjectAcl acl, URL url, List<Association> list) {
        CmdbBucketFileRes ci = new CmdbBucketFileRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), object.getKey()));
        ci.setOpen_id(object.getKey());
        ci.setOpen_name(object.getKey());
        ci.setStorage_type(object.getStorageClass());
        toCiResCloud(request, ci);
        //访问权限
        if (ObjectUtil.isNotEmpty(acl.getPermission())) {
            ci.setAcl(acl.getPermission().toString());
        }
        // 文件大小
        ci.setFile_size(UnitUtil.convert(object.getSize(), UnitUtil.BYTE, UnitUtil.GB, 3));
        // 最后修改时间
        if (object.getLastModified() != null) {
            ci.setUpdate_time(object.getLastModified().getTime());
        }
        // url
        ci.setUrl(url.toString());

        return ci;
    }

    public static AlarmInfoBean convertAlarm(BaseCloudRequest request, DescribeAlertLogListResponseBody.DescribeAlertLogListResponseBodyAlertLogList alert) {
        AlarmInfoBean alarm = new AlarmInfoBean();
        alarm.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getInstanceId(), alert.getRuleId(), alert.getAlertTime()));
        alarm.setAccountId(request.getBody().getAccess().getCmpId());
        alarm.setCloudType(CloudType.PUBLIC_ALI.value());
        alarm.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getInstanceId()));
        alarm.setOpenId(alert.getInstanceId());
        alarm.setOpenName(alert.getInstanceName());
        alarm.setOpenLevel(alert.getLevel());
        alarm.setAlarmId(alert.getRuleId());
        alarm.setAlarmName(alert.getRuleName());
        alarm.setDetail(alert.getMessage());
        alarm.setClosedStatus(false);
        alarm.setJsonInfo(JSON.toJSONString(alert));
        alarm.setCount(1);
        if (StrUtil.isNotEmpty(alert.getAlertTime())) {
            alarm.setFirstTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(alert.getAlertTime()))));
            alarm.setCreateTime(alarm.getFirstTime());
        }
        if ("ecs".equalsIgnoreCase(alert.getProduct())) {
            alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        } else if ("rds".equalsIgnoreCase(alert.getProduct())) {
        } else if ("rds_pg".equalsIgnoreCase(alert.getProduct())) {
        } else if ("slb".equalsIgnoreCase(alert.getProduct())) {
            alarm.setResourceType(ResourceType.CMDB_LOADBALANCE_RES.value());
        } else if ("mongodb_sharding".equalsIgnoreCase(alert.getProduct())) {
        } else if ("mongodb".equalsIgnoreCase(alert.getProduct())) {
        } else if ("redis_sharding".equalsIgnoreCase(alert.getProduct())) {
        } else if ("redis_splitrw".equalsIgnoreCase(alert.getProduct())) {
        } else if ("redis_standard".equalsIgnoreCase(alert.getProduct())) {
        }
        switch (alert.level) {
            case "P2":
                alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                break;
            case "P3":
                alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                break;
            case "P4":
                alarm.setAlarmLevel(AlarmLevel.MINOR.value());
                break;
            case "OK":
                alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                alarm.setClosedStatus(true);
                break;
        }
        return alarm;
    }

    public static EventInfoBean convertEvent(BaseCloudRequest request, DescribeSystemEventAttributeResponseBody.DescribeSystemEventAttributeResponseBodySystemEventsSystemEvent event) {
        EventInfoBean bean = new EventInfoBean();
        if (StrUtil.isEmpty(event.content)) return null;
        JSONObject content = JSON.parseObject(event.getContent());
        if (!content.containsKey("resourceId")) return null;
        String resourceId = content.getString("resourceId");
        String resourceType = content.getString("resourceType");
        String state = content.getString("state");
        bean.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), event.resourceId, event.name, state, "" + event.time));
        bean.setAccountId(request.getBody().getAccess().getCmpId());
        bean.setCloudType(CloudType.PUBLIC_ALI.value());
        bean.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), resourceId));
        bean.setOpenId(resourceId);
        bean.setOpenName(event.instanceName);
        bean.setOpenLevel(event.level);
        /**
         * 事件级别。取值：
         *
         * CRITICAL：严重。
         * WARN：警告。
         * INFO：信息
         */
        switch (event.level) {
            case "CRITICAL":
                bean.setEventLevel(AlarmLevel.CRITICAL.value());
                break;
            case "WARN":
                bean.setEventLevel(AlarmLevel.MAJOR.value());
                break;
            case "INFO":
                bean.setEventLevel(AlarmLevel.INFORMATION.value());
                break;
        }
        bean.setEventType("system");
        bean.setEventName(event.name);
        bean.setDetail(event.content);
        if (event.time != null) {
            bean.setBeginTime(DateUtil.formatDateTime(DateUtil.date(event.time)));
            bean.setEndTime(bean.getBeginTime());
        }
        bean.setJsonInfo(JSON.toJSONString(event));
        if (StrUtil.isNotEmpty(resourceType)) {
            if ("ecs".equalsIgnoreCase(resourceType)
                    || "instance".equalsIgnoreCase(resourceType)
                    || "ALIYUN::Instance".equalsIgnoreCase(resourceType)
                    || "ALIYUN::ECS::Instance".equalsIgnoreCase(resourceType)
            ) {
                bean.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
            } else if ("disk".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_DISK_RES.value());
            } else if ("snapshot".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SNAPSHOT_RES.value());
            } else if ("image".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_IMAGE_RES.value());
            } else if ("securitygroup".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SECURITYGROUP_RES.value());
            } else if ("vpc".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_VPC_RES.value());
            } else if ("vswitch".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SUBNET_RES.value());
            } else if ("bucket".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_BUCKET_RES.value());
            } else if ("eni".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
            } else if ("eip".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_EIP_RES.value());
            }
        }
        return bean;
    }

    public static BillInfoBean convertBill(BaseCloudRequest request, DescribeInstanceBillResponseBody.DescribeInstanceBillResponseBodyDataItems
            item, DescribeInstanceBillRequest billRequest) {
        BillInfoBean bill = new BillInfoBean();
        bill.setId(UUID.randomUUID().toString());
        bill.setAccountId(request.getBody().getAccess().getCmpId());
        bill.setCloudType(CloudType.PUBLIC_ALI.value());
        bill.setBillingCycle(billRequest.getBillingCycle());
        bill.setBillingDate(item.getBillingDate());
        bill.setBeginTime(item.getBillingDate() + " 00:00:00");
        bill.setEndTime(item.getBillingDate() + " 23:59:59");
        switch (item.getItem()) {
            case "SubscriptionOrder":
                bill.setMethod("包年包月");
                break;
            case "PayAsYouGoBill":
                bill.setMethod("按需计费");
                break;
            case "Refund":
                bill.setMethod("退款");
                break;
            case "Adjustment":
                bill.setMethod("调账");
                break;
            default:
                bill.setMethod(item.getItem());
                break;
        }

        bill.setRegion(item.getRegion());
        bill.setZone(item.getZone());
        bill.setBusinessName(item.getProductName());
        bill.setProductName(item.getProductDetail());
        bill.setInstanceConfig(item.getInstanceConfig());
        bill.setOpenId(item.getInstanceID());
        bill.setOpenName(item.getNickName());
        bill.setPrice(item.getPretaxGrossAmount());
        bill.setDscPrice(NumberUtil.sub(item.getPretaxGrossAmount(), item.getPretaxAmount()).floatValue());
        bill.setActualPrice(item.getPretaxAmount());
        bill.setCashActualPrice(NumberUtil.add(item.getDeductedByPrepaidCard(), item.getPaymentAmount()).floatValue());
        bill.setDeductActualPrice((float) NumberUtil.add(NumberUtil.parseFloat(item.getDeductedByResourcePackage()), item.getInvoiceDiscount().floatValue()));
        bill.setSourceJson(JSON.toJSONString(item));
        bill.setCreateTime(DateUtil.now());
        return bill;
    }
}

