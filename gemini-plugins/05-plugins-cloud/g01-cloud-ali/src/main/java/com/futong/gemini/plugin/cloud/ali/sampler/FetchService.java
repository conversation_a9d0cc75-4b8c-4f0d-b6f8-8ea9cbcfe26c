package com.futong.gemini.plugin.cloud.ali.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.bssopenapi20171214.models.DescribeInstanceBillRequest;
import com.aliyun.bssopenapi20171214.models.DescribeInstanceBillResponse;
import com.aliyun.bssopenapi20171214.models.DescribeInstanceBillResponseBody;
import com.aliyun.cms20190101.models.*;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.*;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.*;
import com.aliyun.slb20140515.models.DescribeLoadBalancersResponse;
import com.aliyun.vpc20160428.models.*;
import com.aliyun.vpc20160428.models.DescribeEipAddressesResponse;
import com.aliyun.vpc20160428.models.DescribeForwardTableEntriesResponse;
import com.aliyun.vpc20160428.models.DescribeNatGatewaysResponse;
import com.aliyun.vpc20160428.models.DescribeNatGatewaysResponseBody;
import com.aliyun.vpc20160428.models.DescribeVSwitchesResponse;
import com.aliyun.vpc20160428.models.DescribeVpcsResponse;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.common.utils.LimitUtils;
import com.futong.common.utils.PageUtils;
import com.futong.common.utils.TimeUtils;
import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.BillInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbBucketFileRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbBucketRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbNatRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.ali.common.Constant;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class FetchService {


    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) return response;
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) return response;
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("pageNumber", t);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        Map<Class, List> map = BaseCloudService.fetch(request,
                CloudClient.client,
                Client::describeRegions,
                Convert::convertRegion);
        BaseResponse response = BaseCloudService.fetchSend(request, map);
        if (CollUtil.isEmpty(map.get(TmdbDevops.class))) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, map.get(TmdbDevops.class), (TmdbDevops t) -> {
            JobInfo jobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_PLATFORM_AZONE);
            request.getBody().getCloud().put("regionId", t.getDevops_value());
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static BaseResponse fetchZone(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                Client::describeZones,
                Convert::convertZone);
    }

    public static BaseResponse fetchFlavor(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                Client::describeInstanceTypes,
                Convert::convertFlavor);
    }


    public static BaseResponse fetchImage(BaseCloudRequest request) {
        Entry.E2<DescribeImagesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeImages,
                Convert::convertImage);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchEip(BaseCloudRequest request) {
        Entry.E2<DescribeEipAddressesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeEipAddresses,
                Convert::convertEip);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    //密钥对
    public static BaseResponse fetchKeyPair(BaseCloudRequest request) {
        Entry.E2<DescribeKeyPairsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeKeyPairs,
                Convert::convertKeyPair);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request) {
        Entry.E2<DescribeSecurityGroupsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeSecurityGroups,
                Convert::convertSecurityGroup);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        //获取并发送规则信息，默认查询500条规则。无需处理分页
        Client client = CloudClient.client.client(Client.class, request.getBody());
        DescribeSecurityGroupAttributeRequest requestRule = new DescribeSecurityGroupAttributeRequest();
        requestRule.setRegionId(request.getBody().getCloud().getString("regionId"));
        for (Object obj : mapE2.v2.get(CmdbSecuritygroupRes.class)) {
            CmdbSecuritygroupRes res = (CmdbSecuritygroupRes) obj;
            requestRule.setSecurityGroupId(res.getOpen_id());
            Map<Class, List> ruleMap = BaseCloudService.fetch(
                    client,
                    requestRule,
                    request,
                    CloudClient.client,
                    Client::describeSecurityGroupAttribute,
                    Convert::convertSecurityGroupRule);
            BaseCloudService.fetchSend(request, ruleMap);
        }
        //分页安全组
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchVpc(BaseCloudRequest request) {
        Entry.E2<DescribeVpcsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeVpcs,
                Convert::convertVpc);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        Entry.E2<DescribeVSwitchesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeVSwitches,
                Convert::convertSubnet);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchNat(BaseCloudRequest request) {
        Entry.E2<DescribeNatGatewaysResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeNatGateways,
                Convert::convertNat);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if (CollUtil.isEmpty(mapE2.v2.get(CmdbNatRes.class))) return response;
        //分页子任务
        response = toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
        //查询nat的规则明细DNAT,SNAT
        response = BaseCloudService.toflatMapGourdResponse(response, mapE2.v1.body.natGateways.natGateway, (DescribeNatGatewaysResponseBody.DescribeNatGatewaysResponseBodyNatGatewaysNatGateway t) -> {
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), t.natGatewayId);
            request.getBody().getCloud().put("pageNumber", 1);
            List<JobInfo> result = new ArrayList<>();
            if (t.getForwardTableIds() != null && CollUtil.isNotEmpty(t.getForwardTableIds().forwardTableId)) {
                t.getForwardTableIds().forwardTableId.stream().map(forwardTableId -> {
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setSeed(true);
                    request.setAction(ActionType.FETCH_NEUTRON_NAT_ENTRY);
                    request.getBody().getCloud().put("forwardTableId", forwardTableId);
                    request.getBody().getCi().put("resId",resId);
                    request.getBody().getCi().put("type", "DNAT");
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                }).forEach(result::add);
            }
            if (t.getSnatTableIds() != null && CollUtil.isNotEmpty(t.getSnatTableIds().snatTableId)) {
                t.getSnatTableIds().snatTableId.stream().map(forwardTableId -> {
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setSeed(true);
                    request.setAction(ActionType.FETCH_NEUTRON_NAT_ENTRY);
                    request.getBody().getCloud().put("snatTableId", forwardTableId);
                    request.getBody().getCi().put("resId", resId);
                    request.getBody().getCi().put("type", "SNAT");
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                }).forEach(result::add);
            }
            return result.stream();
        });
        return response;
    }


    public static BaseResponse fetchNatEntry(BaseCloudRequest request) {
        if ("DNAT".equals(request.getBody().getCi().getString("type"))) {
            Entry.E2<DescribeForwardTableEntriesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                    CloudClient.client,
                    com.aliyun.vpc20160428.Client::describeForwardTableEntries,
                    Convert::convertNatEntry);
            BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
            return toPageGourdResponse(request, response,
                    mapE2.v1.body.getTotalCount(),
                    mapE2.v1.body.getPageSize());
        } else {
            Entry.E2<DescribeSnatTableEntriesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                    CloudClient.client,
                    com.aliyun.vpc20160428.Client::describeSnatTableEntries,
                    Convert::convertNatEntry);
            BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
            return toPageGourdResponse(request, response,
                    mapE2.v1.body.getTotalCount(),
                    mapE2.v1.body.getPageSize());
        }

    }


    public static BaseResponse fetchRoute(BaseCloudRequest request) {
        Entry.E2<DescribeRouteTableListResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeRouteTableList,
                Convert::convertRoute);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchSnapshot(BaseCloudRequest request) {
        Entry.E2<DescribeSnapshotsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeSnapshots,
                Convert::convertSnapshot);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    //负载均衡
    public static BaseResponse fetchLoadBalancer(BaseCloudRequest request) {
        Entry.E2<DescribeLoadBalancersResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.slb20140515.Client::describeLoadBalancers,
                Convert::convertLoadBalancer);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeInstances,
                Convert::convertEcs);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    //网卡
    public static BaseResponse fetchNetcard(BaseCloudRequest request) {
        Entry.E2<DescribeNetworkInterfacesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeNetworkInterfaces,
                Convert::convertNetcard);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static BaseResponse fetchDisk(BaseCloudRequest request) {
        Entry.E2<DescribeDisksResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeDisks,
                Convert::convertDisk);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("regionId")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数regionId不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("namespace")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数namespace不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("period")) {
            request.getBody().getCloud().put("period", "300");
        }
        if (!request.getBody().getCloud().containsKey("startTime")) {
            //当前时间-监控数据得统计周期（秒）*1000（毫秒）
            long startTime = System.currentTimeMillis() - NumberUtil.parseLong(request.getBody().getCloud().getString("period")) * 1000;
            request.getBody().getCloud().put("startTime", Long.toString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", System.currentTimeMillis() + "");
        }
        return true;
    }

    public static boolean defaultEcsMetricNames(BaseCloudRequest request) {
        if (!request.getBody().containsKey("MetricNames")) {
            request.getBody().put("MetricNames", Constant.metrics.keySet());
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        searchJsonRequest.put("size", 50);//固定50条，阿里云限制
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);

        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        //设置采集监控数据云主机ID
        String dimensions = instanceMap.keySet().stream().map(t -> "{\"instanceId\":\"" + t + "\"}").collect(Collectors.joining(",", "[", "]"));
        request.getBody().getCloud().put("dimensions", dimensions);
        return true;
    }

    public static BaseResponse fetchEcsPerf(BaseCloudRequest request) {
        try {
            //监控请求对象
            DescribeMetricListRequest metricRequest = request.getBody().getCloud().toJavaObject(DescribeMetricListRequest.class);
            //监控请求指标集合
            Collection<String> metricNames = request.getBody().getJSONArray("MetricNames").toJavaList(String.class);
            /*******发起监控数据请求*******/
            com.aliyun.cms20190101.Client client = CloudClient.client.client(com.aliyun.cms20190101.Client.class, request.getBody());
            List<JSONObject> metricList = new ArrayList<>();
            for (String metricName : metricNames) {
                metricRequest.setMetricName(metricName);
                metricRequest.setNextToken(null);//初始nextToken为空，否则会导致上次查询结果得nextToken代入
                log.info("查询监控数据得请求信息:{}", JSON.toJSONString(metricRequest));
                //分页查询
                List<JSONObject> metricResult = LimitUtils.query(metricRequest,
                        t -> CloudClient.client.execute(client, t, com.aliyun.cms20190101.Client::describeMetricList),
                        (DescribeMetricListResponse t) -> JSON.parseArray(t.getBody().getDatapoints(), JSONObject.class),
                        metricRequest::setNextToken,
                        (DescribeMetricListResponse t) -> t.getBody().getNextToken()
                );
                log.info("查询监控数据得响应信息:{}", JSON.toJSONString(metricResult));
                metricResult.stream().forEach(t -> t.put("metricName", metricName));//设置结果得指标名称
                if (CollUtil.isNotEmpty(metricResult)) metricList.addAll(metricResult);
            }
            Map<String, PerfInfoBean> perfMap = Convert.convertEcsPerf(request, metricList);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("获取云主机监控失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取云主机监控失败");
        }
    }


    //对象存储桶信息
    public static BaseResponse fetchBucket(BaseCloudRequest request) {
        BucketList result = CloudClient.client.execute(request.getBody(), (FTExecute<OSSClient, ListBucketsRequest, BucketList>) OSSClient::listBuckets);
        return BaseCloudService.toGourdResponse(
                BaseResponse.SUCCESS.of("获取存储桶" + result.getBucketList().size() + "个"),
                result.getBucketList(),
                (Bucket t) -> {
                    JobInfo jobInfo = new JobInfo();
                    request.setAction(ActionType.FETCH_STORAGE_BUCKET_FILE);
                    request.getBody().getCloud().put("bucket", t);
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                });

    }


    //对象存储桶信息
    public static BaseResponse fetchBucketFile(BaseCloudRequest request) {
        try {
            List<CmdbBucketRes> buckets = new ArrayList<>();
            List<CmdbBucketFileRes> files = new ArrayList<>();
            List<Association> associations = new ArrayList<>();
            List<TmdbResourceSet> sets = new ArrayList<>();
            Map<Class, List> result = new HashMap<>();
            result.put(CmdbBucketRes.class, buckets);
            result.put(CmdbBucketFileRes.class, files);
            result.put(Association.class, associations);
            result.put(TmdbResourceSet.class, sets);

            OSSClient client = CloudClient.client.client(OSSClient.class, request.getBody());
            Bucket bucket = request.getBody().getCloud().getObject("bucket", Bucket.class);
            //查询桶对象列表
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket.getName());
            List<OSSObjectSummary> os = LimitUtils.query(listObjectsRequest,
                    q -> CloudClient.client.execute(client, q, OSSClient::listObjects),//执行云上查询所需要的函数
                    ObjectListing::getObjectSummaries,//取结果集
                    listObjectsRequest::setMarker,//设置新的查询条件
                    (ObjectListing t) -> t.getNextMarker()//从结果中获取新的查询条件
            );
            //查询桶的防盗链
            BucketReferer br = CloudClient.client.execute(client, new GenericRequest(bucket.getName()), OSSClient::getBucketReferer);
            //查询桶的访问权限
            AccessControlList acl = CloudClient.client.execute(client, new GenericRequest(bucket.getName()), OSSClient::getBucketAcl);
            //查询桶的版本控制状态
            BucketVersioningConfiguration bvc = CloudClient.client.execute(client, new GenericRequest(bucket.getName()), OSSClient::getBucketVersioning);
            //转换对象
            CmdbBucketRes bucketRes = Convert.convertBucket(request, bucket, os, br, acl, bvc);
            buckets.add(bucketRes);
            sets.add(BuilderResourceSet.of()
                    .withInfo(request.getBody().getAccess().getCmpId(),
                            CloudType.PUBLIC_ALI,
                            ResourceType.CMDB_BUCKET_RES
                    ).builderResourceSet(bucketRes.getRes_id(),
                            DevopsSide.DEVOPS_REGION,
                            request.getBody().getCloud().getString("regionId")));
            for (OSSObjectSummary o : os) {
                //查询文件权限
                ObjectAcl oAcl = CloudClient.client.execute(client, new GenericRequest(bucket.getName(), o.getKey()), OSSClient::getObjectAcl);
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket.getName(), o.getKey());
                generatePresignedUrlRequest.setExpiration(new Date(System.currentTimeMillis() + TimeUtils.MONTH_MILLISECOND));
                URL oUrl = CloudClient.client.execute(client, generatePresignedUrlRequest, OSSClient::generatePresignedUrl);
                CmdbBucketFileRes bucketFileRes = Convert.convertBucketFile(request, o, oAcl, oUrl, associations);
                associations.add(AssociationUtils.toAssociation(bucketFileRes, CmdbBucketRes.class, bucketRes.getRes_id()));
                files.add(bucketFileRes);
            }
            return BaseCloudService.fetchSend(request, result);
        } catch (Exception e) {
            log.error("同步对象存储桶失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步对象存储桶失败");
        }
    }


    //告警信息
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        try {
            //查询阿里云云主机信息
            DescribeAlertLogListRequest alertRequest = request.getBody().getCloud().toJavaObject(DescribeAlertLogListRequest.class);
            DescribeAlertLogListResponse response = CloudClient.client.execute(request.getBody(), alertRequest, com.aliyun.cms20190101.Client::describeAlertLogList);
            if (null == response.body || CollUtil.isEmpty(response.body.alertLogList)) {
                return BaseResponse.SUCCESS.of("查询告警信息为空!");
            }
            //将获取到的云主机信息转换为CI模型
            List<AlarmInfoBean> listCI = response.body.alertLogList.stream().map(t -> Convert.convertAlarm(request, t)).collect(Collectors.toList());
            BaseCloudService.toAetMessageAndSend(listCI, "alarm");
            String message = StrUtil.format("本次获取告警信息,页码：{},条数：{},本次获取条数：{}", alertRequest.getPageNumber(), alertRequest.getPageSize(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == alertRequest.getPageSize()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("pageNumber", alertRequest.getPageNumber() + 1);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }
    }


    //告警信息
    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            DescribeSystemEventAttributeRequest eventRequest = request.getBody().getCloud().toJavaObject(DescribeSystemEventAttributeRequest.class);
            //查询阿里云云主机信息
            DescribeSystemEventAttributeResponse response = CloudClient.client.execute(request.getBody(), eventRequest, com.aliyun.cms20190101.Client::describeSystemEventAttribute);
            if (null == response.body || null == response.body.systemEvents || CollUtil.isEmpty(response.body.systemEvents.systemEvent)) {
                return BaseResponse.SUCCESS.of("查询事件信息为空!");
            }
            List<EventInfoBean> listCI = response.body.systemEvents.systemEvent.stream().map(t -> Convert.convertEvent(request, t)).collect(Collectors.toList());
            listCI.removeIf(Objects::isNull);
            BaseCloudService.toAetMessageAndSend(listCI, "event");

            String message = StrUtil.format("本次获取事件信息,页码：{},条数：{},本次获取条数：{}", eventRequest.getPageNumber(), eventRequest.getPageSize(), listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == eventRequest.getPageSize()) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("pageNumber", eventRequest.getPageNumber() + 1);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步事件数据失败");
        }
    }

    //账单信息
    public static BaseResponse fetchBill(BaseCloudRequest request) {
        request.getBody().getCloud().put("isBillingItem", true);
        request.getBody().getCloud().put("granularity", "DAILY");
        //每月4号以后不查上月数据
        Date begin = DateUtil.thisDayOfMonth() < 4 ? DateUtil.beginOfMonth(DateUtil.lastMonth()) : DateUtil.beginOfMonth(DateUtil.date());
        List<Entry.E2<String, String>> days = DateUtil.rangeFunc(
                begin,
                DateUtil.yesterday(),
                DateField.DAY_OF_MONTH,
                t -> new Entry.E2<>(DateUtil.formatDate(t), DateUtil.format(t, "yyyy-MM")));
        return BaseCloudService.toGourdResponse(
                BaseResponse.SUCCESS.of(StrUtil.format("查询账单信息，拆分子任务{}到{}每天的账单信息", DateUtil.formatDate(begin), DateUtil.formatDate(DateUtil.yesterday()))),
                days,
                (Entry.E2<String, String> t) -> {
                    JobInfo jobInfo = new JobInfo();
                    request.setAction(ActionType.FETCH_PLATFORM_BILL_DAY);
                    request.getBody().getCloud().put("billingDate", t.v1);
                    request.getBody().getCloud().put("billingCycle", t.v2);
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                });
    }


    //账单信息
    public static BaseResponse fetchBillDay(BaseCloudRequest request) {
        try {
            DescribeInstanceBillRequest billRequest = request.getBody().getCloud().toJavaObject(DescribeInstanceBillRequest.class);
            com.aliyun.bssopenapi20171214.Client client = CloudClient.client.client(com.aliyun.bssopenapi20171214.Client.class, request.getBody());
            List<DescribeInstanceBillResponseBody.DescribeInstanceBillResponseBodyDataItems> result = LimitUtils.query(
                    billRequest,
                    t -> CloudClient.client.execute(client, t, com.aliyun.bssopenapi20171214.Client::describeInstanceBill),
                    (DescribeInstanceBillResponse t) -> null == t.body
                            || null == t.body.data
                            || CollUtil.isEmpty(t.body.data.items) ? null : t.body.data.items,
                    billRequest::setNextToken,
                    (DescribeInstanceBillResponse t) -> t.getBody().data.getNextToken()
            );
            //判断返回信息是否为空，为空则返回空状态
            if (CollUtil.isEmpty(result)) {
                log.error("获取阿里云账单信息为空");
                return BaseResponse.SUCCESS.of("无账单信息");
            }
            //将获取到的云主机信息转换为CI模型
            List<BillInfoBean> listCI = result.stream().map(t -> Convert.convertBill(request, t, billRequest)).collect(Collectors.toList());
            BaseCloudService.toBillMessageAndSend(request.getBody(), listCI, billRequest.getBillingCycle(), billRequest.getBillingDate());
            return BaseResponse.SUCCESS.of(StrUtil.format("推送账单{}数据共{}条", billRequest.getBillingDate(), listCI.size()));
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }
    }

}
