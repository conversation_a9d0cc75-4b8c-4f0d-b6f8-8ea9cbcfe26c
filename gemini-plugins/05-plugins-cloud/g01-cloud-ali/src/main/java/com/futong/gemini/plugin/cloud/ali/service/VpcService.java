package com.futong.gemini.plugin.cloud.ali.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.ecs20140526.models.RunInstancesResponse;
import com.aliyun.ecs20140526.models.RunInstancesResponseBody;
import com.aliyun.vpc20160428.Client;
import com.aliyun.vpc20160428.models.CreateVSwitchRequest;
import com.aliyun.vpc20160428.models.CreateVpcResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.AliCloudPluginTemplate;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;

import java.util.ArrayList;
import java.util.List;

public class VpcService {
    public static BaseResponse createVpcAndVSwitch(BaseCloudRequest request) {
        CreateVpcResponse vpcResponse = CloudClient.client.execute(request.getBody(), Client::createVpc);
        if (request.getBody().getCloud().containsKey("subnet")) {
            JSONObject subnet = request.getBody().getCloud().getJSONObject("subnet");
            subnet.put("regionId", request.getBody().getCloud().get("regionId"));
            subnet.put("vpcId", vpcResponse.body.vpcId);
            CreateVSwitchRequest subnetRequest = subnet.toJavaObject(CreateVSwitchRequest.class);
            CloudClient.client.execute(request.getBody(), subnetRequest, Client::createVSwitch);
        }
        return BaseResponse.SUCCESS;
    }

    public static void addRefreshCreateVpcCallbackCreateVSwitch(BaseCloudRequest request, BaseResponse response) {
        if (request.getBody().getCloud().containsKey("subnet")) {
            JSONObject refreshRequest = request.getBody().getJSONObject("refreshRequest");
            JSONObject refreshBody = refreshRequest.getJSONObject("body");
            JSONObject subnet = request.getBody().getCloud().getJSONObject("subnet");
            subnet.put("vpcId", JSONPath.eval(response, "$.data.body.vpcId"));
            subnet.put("regionId", request.getBody().getCloud().get("regionId"));
            refreshBody.put("subnet", subnet);
        }
    }

    public static void executeRefreshCreateVpcCallbackCreateVSwitch(BaseCloudRequest request, BaseResponse response) {
        //结果为GourdJobResponse表示未刷新至正常状态
        if ((!(response instanceof GourdJobResponse)) && request.getBody().containsKey("subnet")) {
            JSONObject subnet = request.getBody().getJSONObject("subnet");
            JSONObject requestSubnet = BaseCloudService.getRequestTemplate(request, ActionType.CREATE_NEUTRON_SUBNET);
            JSONPath.set(requestSubnet,"$.body.cloud", subnet);
            AliCloudPluginTemplate.template.execute(requestSubnet);
        }
    }


    public static void main(String[] args) {
        RunInstancesResponse response = new RunInstancesResponse();
        RunInstancesResponseBody body = new RunInstancesResponseBody();
        RunInstancesResponseBody.RunInstancesResponseBodyInstanceIdSets sets = new RunInstancesResponseBody.RunInstancesResponseBodyInstanceIdSets();
        List<String> instanceIdSet = new ArrayList<>();
        instanceIdSet.add("1");
        instanceIdSet.add("2");
        sets.instanceIdSet = instanceIdSet;
        body.instanceIdSets = sets;
        response.body = body;
        BaseResponse baseResponse = new BaseDataResponse<>(response);
        Object eval = JSONPath.eval(baseResponse, "$.data.body.instanceIdSets.instanceIdSet");
        System.out.println(eval);
    }
}
