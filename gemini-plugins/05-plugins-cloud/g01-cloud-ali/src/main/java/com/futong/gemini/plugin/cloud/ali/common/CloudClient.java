package com.futong.gemini.plugin.cloud.ali.common;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.teaopenapi.models.Config;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;

@Slf4j
public class CloudClient extends BaseCloudClient {
    public static final CloudClient client = new CloudClient();

    //获取Client对象
    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            if (clazz == OSSClient.class) {
                return (C) ossClient(body);
            } else {
                //请求Client对象配置信息
                Config config = new Config();
                config.setAccessKeyId(body.getAccess().getUsername());
                config.setAccessKeySecret(body.getAccess().getPassword());
                config.setRegionId(body.getCloud().getString("regionId"));
                //设置代理
                if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                    config.setHttpProxy(body.getAuth().getString("proxyAddr"));
                }
                if (clazz == com.aliyun.cms20190101.Client.class) {
                    config.setEndpoint(StrUtil.format("metrics.{}.aliyuncs.com", body.getCloud().getString("regionId")));
                }
                return clazz.getConstructor(Config.class).newInstance(config);
            }
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    private static final String ossEndpoint = "oss-{}.aliyuncs.com";

    private static OSS ossClient(BaseCloudRequestBody body) {
        try {
            String endpoint = body.getAuth().getString("endpoint");
            endpoint = StrUtil.emptyToDefault(endpoint, StrUtil.format(ossEndpoint, body.getCloud().getString("regionId")));
            endpoint = StrUtil.format(endpoint, body.getCloud().getString("regionId"));
            ClientBuilderConfiguration configuration = new ClientBuilderConfiguration();
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                URL url = new URL(body.getAuth().getString("proxyAddr"));
                configuration.setProxyHost(url.getHost());
                configuration.setProxyPort(url.getPort());
            }
            return new OSSClientBuilder().build(endpoint,
                    body.getAccess().getUsername(),
                    body.getAccess().getPassword(),
                    configuration);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

}

