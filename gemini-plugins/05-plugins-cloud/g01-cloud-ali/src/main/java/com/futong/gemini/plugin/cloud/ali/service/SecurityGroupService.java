package com.futong.gemini.plugin.cloud.ali.service;

import cn.hutool.core.util.StrUtil;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.*;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class SecurityGroupService {
    public static BaseResponse createSecurityGroupRule(BaseCloudRequest request) {
        String direction = request.getBody().getCloud().getString("direction");
        if("egress".equals(direction)){
            AuthorizeSecurityGroupEgressResponse response = CloudClient.client.execute(request.getBody(),Client::authorizeSecurityGroupEgress);
            return new BaseDataResponse<>(response.getBody());
        }else if("ingress".equals(direction)){
            AuthorizeSecurityGroupResponse response = CloudClient.client.execute(request.getBody(),Client::authorizeSecurityGroup);
            return new BaseDataResponse<>(response.getBody());
        }else{
            return BaseResponse.ERROR_BIZ.of("缺少出入方向参数");
        }

    }

    public static BaseResponse deleteSecurityGroupRule(BaseCloudRequest request) {
        try {
            String direction = request.getBody().getCloud().getString("direction");
            if("egress".equals(direction)){
                RevokeSecurityGroupEgressResponse response = CloudClient.client.execute(request.getBody(),Client::revokeSecurityGroupEgress);
                return new BaseDataResponse<>(response.getBody());
            }
            if("ingress".equals(direction)){
                RevokeSecurityGroupResponse response = CloudClient.client.execute(request.getBody(),Client::revokeSecurityGroup);
                return new BaseDataResponse<>(response.getBody());
            }
            return BaseResponse.SUCCESS;
        }catch (Exception e){
            throw new BaseException(BaseResponse.FAIL_OP, StrUtil.format("删除安全组{}规则失败",  request.getBody().getCloud().getString("securityGroupId")));
        }
    }
}
