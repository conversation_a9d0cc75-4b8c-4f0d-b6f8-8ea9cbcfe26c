package com.futong.gemini.plugin.cloud.ali.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.BucketReferer;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTJsonUtils;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

import java.util.List;

public class OssService {
    public static BaseResponse deleteBucket(BaseCloudRequest request) {
        OSSClient client = CloudClient.client.client(OSSClient.class, request.getBody());
        client.deleteBucket(request.getBody().getCloud().getString("bucketName"));
        return BaseResponse.SUCCESS;
    }

    public static BaseResponse updateBucket(BaseCloudRequest request) {
        OSSClient client = CloudClient.client.client(OSSClient.class, request.getBody());
        String bucketName = request.getBody().getCloud().getString("bucketName");
        boolean allowEmptyReferer = request.getBody().getCloud().getBoolean("allowEmptyReferer");
        List<String> refererList = request.getBody().getCloud().getObject("refererList", FTJsonUtils.LIST_STR);
        client.setBucketReferer(bucketName, new BucketReferer(allowEmptyReferer, refererList));
        return BaseResponse.SUCCESS;
    }

}
