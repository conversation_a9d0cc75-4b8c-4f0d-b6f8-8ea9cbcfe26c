package com.futong.gemini.plugin.cloud.ali.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.teaopenapi.models.Config;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;

public class ClientUtils {

    //获取Client对象
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            //请求Client对象配置信息
            Config config = new Config();
            config.setAccessKeyId(body.getAccess().getUsername());
            config.setAccessKeySecret(body.getAccess().getPassword());
            //设置代理
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                config.setHttpProxy(body.getAuth().getString("proxyAddr"));
            }
            String regionId= StrUtil.emptyToDefault(body.getCloud().getString("regionId"),body.getApi().getString("regionId"));
            String productId = body.getApi().getString("productId");
            config.setRegionId(regionId);
            if (clazz == AliClient.class) {
                return clazz.getConstructor(Config.class, String.class).newInstance(config, productId);
            } else {
                return clazz.getConstructor(Config.class).newInstance(config);
            }
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    public static void main(String[] args) throws Exception {
        JSONObject json= JSONObject.parseObject("{\n" +
                "\t\"action\": \"AuthPlatformAccount\",\n" +
                "\t\"body\": {\n" +
                "\t\t\"auth\": {\n" +
                "\t\t\t\"accountType\": \"main\",\n" +
                "\t\t\t\"cmpId\": \"6756a21f92e953e3debdbefd58509a06\",\n" +
                "\t\t\t\"currentCmpId\": \"6756a21f92e953e3debdbefd58509a06\",\n" +
                "\t\t\t\"jsonStr\": \"{\\\"proxyAddr\\\":\\\"\\\"}\",\n" +
                "\t\t\t\"proxyAddr\": \"\",\n" +
                "\t\t\t\"description\": \"\",\n" +
                "\t\t\t\"updateTime\": \"2025-08-19T17:50:10\",\n" +
                "\t\t\t\"version\": \"4.5.10\",\n" +
                "\t\t\t\"companyId\": \"\",\n" +
                "\t\t\t\"password\": \"******************************\",\n" +
                "\t\t\t\"createTime\": \"2025-05-06T16:39:35\",\n" +
                "\t\t\t\"healthStatus\": \"ok\",\n" +
                "\t\t\t\"cloudAccount\": \"alitest\",\n" +
                "\t\t\t\"cloudType\": \"public_ali\",\n" +
                "\t\t\t\"tenantId\": \"6ae92a066e29410db2cb276f8267e580\",\n" +
                "\t\t\t\"exceptionReason\": \"\",\n" +
                "\t\t\t\"id\": 32,\n" +
                "\t\t\t\"projectId\": \"\",\n" +
                "\t\t\t\"status\": \"success\",\n" +
                "\t\t\t\"username\": \"LTAI5t6bMLqwtAfWWxwqj7FC\"\n" +
                "\t\t},\n" +
                "\t\t\"api\": {\n" +
                "\t\t\t\"productId\": \"ecs\",\n" +
                "\t\t\t\"regionId\": \"cn-beijing\",\n" +
                "\t\t\t\"action\": \"DescribeRegions\",\n" +
                "\t\t\t\"version\": \"2014-05-26\"\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}");
        BaseCloudRequest request = new BaseCloudRequest(json);
        AliClient client = ClientUtils.client(AliClient.class, request.getBody());
        //请求方式1：基于请求中的API信息调用RPC方法
        JSONObject result = client.doRpcPostJSONBody(request);
        System.out.println(result.toJSONString());
        //请求方式2：代码执行API信息调用RPC方法
        JSONObject req= JSONObject.parseObject("{\"regionId\":\"cn-beijing\"}");
        JSONObject result2 = client.doRpcPostJSONBody("DescribeZones", "2014-05-26", req);
        System.out.println(result2.toJSONString());
    }
}
