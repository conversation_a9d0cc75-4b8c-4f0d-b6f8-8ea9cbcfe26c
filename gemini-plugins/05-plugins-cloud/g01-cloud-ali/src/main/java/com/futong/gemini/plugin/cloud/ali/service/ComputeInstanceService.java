package com.futong.gemini.plugin.cloud.ali.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.cms20190101.models.DescribeMetricLastRequest;
import com.aliyun.cms20190101.models.DescribeMetricLastResponse;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.DescribeInstanceVncUrlResponse;
import com.aliyun.ecs20140526.models.DescribeResourcesModificationResponse;
import com.aliyun.ecs20140526.models.DescribeResourcesModificationResponseBody;
import com.aliyun.ecs20140526.models.RunInstancesResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

import java.util.List;

public class ComputeInstanceService {
    public static BaseResponse queryUpdateComputeInstanceFlavor(BaseCloudRequest request) {
        request.getBody().getCloud().put("destinationResource", "InstanceType");
        DescribeResourcesModificationResponse result = CloudClient.client.execute(request.getBody(), Client::describeResourcesModification);
        List<DescribeResourcesModificationResponseBody.DescribeResourcesModificationResponseBodyAvailableZonesAvailableZoneAvailableResourcesAvailableResourceSupportedResourcesSupportedResource> supportedResources = Opt.ofNullable(result.getBody())
                .map(t -> t.getAvailableZones())
                .map(t -> t.getAvailableZone()).map(t -> t.get(0))
                .map(t -> t.availableResources)
                .map(t -> t.getAvailableResource()).map(t -> t.get(0))
                .map(t -> t.getSupportedResources())
                .map(t -> t.getSupportedResource()).orElse(null);
        return new BaseDataResponse<>(supportedResources);
    }

    public static BaseResponse queryInstanceVncUrl(BaseCloudRequest request) {
        DescribeInstanceVncUrlResponse response = CloudClient.client.execute(request.getBody(), Client::describeInstanceVncUrl);
        return new BaseDataResponse<>(response.getBody());
    }

    public static BaseResponse queryInstanceTotal(BaseCloudRequest request) {
        JSONObject data = new JSONObject();
        DescribeMetricLastRequest totalInstanceNum = new DescribeMetricLastRequest();
        totalInstanceNum.setNamespace("acs_ecs_dashboard");
        totalInstanceNum.setMetricName("TotalInstanceNum");
        com.aliyun.cms20190101.Client client = CloudClient.client.client(com.aliyun.cms20190101.Client.class, request.getBody());
        DescribeMetricLastResponse totalInstanceNumResponse = CloudClient.client.execute(client, totalInstanceNum, com.aliyun.cms20190101.Client::describeMetricLast);
        Object count = JSONPath.read(totalInstanceNumResponse.body.datapoints, "$[-1].Value");
        data.put("count", count == null ? 0 : count);
        return new BaseDataResponse<>(data);
    }

    public static boolean toBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        if (resNum == null || resNum == 0) return true;//为0非批量创建云主机,不做处理
        JSONObject cloud = request.getBody().getCloud();
        String instanceName = cloud.getString("instanceName");
        String hostName = cloud.getString("hostName");
        cloud.put("instanceName", instanceName + "-" + resNum);
        cloud.put("hostName", hostName + "-" + resNum);
        cloud.put("amount", 1);//默认数量
        return true;
    }

    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) return;
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
            if (dataResponse.getData() instanceof RunInstancesResponse) {
                RunInstancesResponse runInstancesResponse = (RunInstancesResponse) data;
                if (runInstancesResponse.body == null
                        || runInstancesResponse.body.instanceIdSets == null
                        || CollUtil.isEmpty(runInstancesResponse.body.instanceIdSets.instanceIdSet)) {
                    return;
                }
                JSONObject biz = request.getBody().getBiz();
                biz.put("resId", IdUtils.encryptId(request.getBody().getAccess().getCmpId(), runInstancesResponse.body.instanceIdSets.instanceIdSet.get(0)));
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            }
        }
    }

}
