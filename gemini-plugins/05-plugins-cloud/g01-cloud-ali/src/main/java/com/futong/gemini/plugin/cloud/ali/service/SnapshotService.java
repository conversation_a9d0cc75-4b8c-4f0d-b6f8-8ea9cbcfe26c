package com.futong.gemini.plugin.cloud.ali.service;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.ecs20140526.Client;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class SnapshotService {
    public static BaseResponse deleteSnapshots(BaseCloudRequest request) {
        for (JSONObject ci : request.getBody().getCis()) {
            request.getBody().getCloud().put("snapshotId", ci.get("openId"));
            CloudClient.client.execute(request.getBody(), Client::deleteSnapshot);
        }
        return BaseResponse.SUCCESS;
    }

}
