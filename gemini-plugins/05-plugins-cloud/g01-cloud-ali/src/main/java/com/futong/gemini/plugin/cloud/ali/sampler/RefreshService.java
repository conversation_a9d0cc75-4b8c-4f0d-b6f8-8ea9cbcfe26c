package com.futong.gemini.plugin.cloud.ali.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.DescribeInstancesResponse;
import com.aliyun.ecs20140526.models.RunInstancesResponse;
import com.aliyun.vpc20160428.models.DescribeVSwitchesResponse;
import com.aliyun.vpc20160428.models.DescribeVpcsResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbVpcRes;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class RefreshService {

    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("Pending");
            add("Starting");
            add("Stopping");
        }
    };
    private final static Set<String> INTERMEDIATE_VPC = new HashSet<String>() {
        {
            add("Pending");
        }
    };
    private final static List<String> deleteRelationTables=Arrays.asList("cmdb_disk_res");

    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                Client::describeInstances,
                Convert::convertEcs);
        List<CmdbInstanceRes> res = mapE2.v2.get(CmdbInstanceRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
            cmdbInstanceRes.setRes_id(resId);
            List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class, request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId(),deleteRelationTables);
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbInstanceRes cmdbInstanceRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }

    public static BaseResponse refreshVpc(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<DescribeVpcsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeVpcs,
                Convert::convertVpc);
        List<CmdbVpcRes> res = mapE2.v2.get(CmdbVpcRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbVpcRes cmdbVpcRes = new CmdbVpcRes();
            cmdbVpcRes.setRes_id(resId);
            List<CmdbVpcRes> data = CollUtil.newArrayList(cmdbVpcRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbVpcRes.class, request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbVpcRes cmdbVpcRes = res.get(0);
            if (INTERMEDIATE_VPC.contains(cmdbVpcRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }
    public static BaseResponse refreshVSwitch(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<DescribeVSwitchesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                com.aliyun.vpc20160428.Client::describeVSwitches,
                Convert::convertSubnet);
        List<CmdbSubnetRes> res = mapE2.v2.get(CmdbSubnetRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbSubnetRes cmdbSubnetRes = new CmdbSubnetRes();
            cmdbSubnetRes.setRes_id(resId);
            List<CmdbSubnetRes> data = CollUtil.newArrayList(cmdbSubnetRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbSubnetRes.class, request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbSubnetRes cmdbSubnetRes = res.get(0);
            if (INTERMEDIATE_VPC.contains(cmdbSubnetRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }
}
