package com.futong.gemini.plugin.cloud.ali;

import com.aliyun.ecs20140526.Client;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.CreateBucketRequest;
import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.ali.sampler.FetchService;
import com.futong.gemini.plugin.cloud.ali.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.ali.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class AliCloudRegister extends BaseCloudRegister {
    public <Q, R, C> Builder registerAli(ActionType actionType, FTExecute<Q, R, C> execute) {
        return register(actionType, execute, CloudService::toFTAction);
    }


    @Override
    public void load() {
        //资源操作加载
        onAfterLoadPlatform();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadCompute();//加载云主机操作
        onAfterLoadComputeImage();//加载镜像操作
        onAfterLoadComputeSecurityGroup();//加载云主机安全组操作
        onAfterLoadComputeKeypair();//加载云主机密钥对操作
        onAfterLoadStorageDisk();//加载存储云硬盘操作
        onAfterLoadStorageSnapshot();//加载存储云硬盘快照操作
        onAfterLoadStorageOSS();//加载对象存储操作
        onAfterLoadNeutronVpc();//加载网络VPC操作
        onAfterLoadNeutronSubnet();//加载网络子网操作
        onAfterLoadNeutronEip();//加载网络弹性IP操作
        onAfterLoadNeutronNat();//加载网络NAT网关操作
        onAfterLoadNeutronRoute();//加载网络路由表操作
    }


    public void onAfterLoadPlatform() {
        //平台账号
        registerBefore(CloudService::defaultRegion,
                ActionType.AUTH_PLATFORM_ACCOUNT,
                ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL,
                ActionType.CREATE_PLATFORM_FETCH_DISPATCH,
                ActionType.QUERY_PLATFORM_BILL_BALANCE);//认证云账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount);//认证云账号
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);//获取调度添加模型
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);//添加默认调度任务
        register(ActionType.QUERY_PLATFORM_BILL_BALANCE, PlatformService::queryBillBalance);//平台账单
    }

    public void onAfterLoadFetch() {
        registerBefore(CloudService::defaultPage50,
                ActionType.FETCH_PLATFORM_KEYPAIR,
                ActionType.FETCH_COMPUTE_SECURITYGROUP,
                ActionType.FETCH_STORAGE_SNAPSHOT,
                ActionType.FETCH_NEUTRON_VPC,
                ActionType.FETCH_NEUTRON_SUBNET,
                ActionType.FETCH_NEUTRON_NIC,
                ActionType.FETCH_NEUTRON_NAT,
                ActionType.FETCH_NEUTRON_NAT_ENTRY,
                ActionType.FETCH_NEUTRON_ROUTE,
                ActionType.FETCH_NEUTRON_LOADBALANCE
        );//默认查询页码及条数50
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_STORAGE_IMAGE,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_NEUTRON_EIP);//默认查询页码及条数100
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion);//同步地域
        register(ActionType.FETCH_PLATFORM_AZONE, FetchService::fetchZone);//同步可用区
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeyPair);//获取密钥对
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm)//获取告警信息
                .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
        register(ActionType.FETCH_PLATFORM_BILL, FetchService::fetchBill);//获取对象存储文件
        register(ActionType.FETCH_PLATFORM_BILL_DAY, FetchService::fetchBillDay);//获取对象存储文件
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent)//获取事件
                .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);//获取云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsPerf)//获取云主机监控
                .addBefore(FetchService::defaultMetricRequest)
                .addBefore(FetchService::defaultEcsMetricNames)
                .addBefore(FetchService::defaultEcsMetricDimensions);//获取云主机监控,预处理
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor);//获取规格
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup);//获取安全组
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage);//获取镜像
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk);//获取磁盘
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot);//获取磁盘快照
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchBucket);//获取对象存储
        register(ActionType.FETCH_STORAGE_BUCKET_FILE, FetchService::fetchBucketFile);//获取对象存储文件
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc);//获取VPC
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);//获取Subnet
        register(ActionType.FETCH_NEUTRON_NIC, FetchService::fetchNetcard);//获取网卡
        register(ActionType.FETCH_NEUTRON_NAT, FetchService::fetchNat);//获取nat网关
        register(ActionType.FETCH_NEUTRON_NAT_ENTRY, FetchService::fetchNatEntry);//获取nat网关
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRoute);//获取路由表
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip);//获取EIP
        register(ActionType.FETCH_NEUTRON_LOADBALANCE, FetchService::fetchLoadBalancer);//获取负载均衡
    }

    public void onAfterLoadCompute() {
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)//刷新云主机
                .addTransferCloud("$.refreshConfig.data", "$.instanceIds", BaseUtils::formatStrArray);//从刷新配置中获取实例ID
        registerAli(ActionType.CREATE_COMPUTE_INSTANCE, Client::runInstances)//创建云主机
                .addBefore(ComputeInstanceService::toBeforeBiz)//添加业务处理
                .addAfter(ComputeInstanceService::toAfterBizResId)//发送业务MQ
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)//设置刷新请求，指定请求Action
                .addSetRefreshConfig(20, 5000, 20000)//设置刷新总次数,刷新频次，首次刷新延迟
                .addSetRefreshSplitData("response", "$.data.body.instanceIdSets.instanceIdSet")//批量创建基于响应分割刷新任务
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")//基于请求信息设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);//添加刷新任务
        registerAli(ActionType.DELETE_COMPUTE_INSTANCE, Client::deleteInstances)//批量删除云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request", "$.body.cloud.instanceId")//批量操作基于请求分割刷新任务
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")//基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.UPDATE_COMPUTE_INSTANCE, Client::modifyInstanceAttribute)//修改云主机
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.resourceName", "$.instanceName", false)
                .addTransferCloud("$.model.description", "$.description", false)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                .addSetRefreshData("request", "$.body.cloud.instanceId")//单资源操作,直接设置刷新请求的data信息
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        register(ActionType.QUERY_UPDATE_COMPUTE_INSTANCE_FLAVOR, ComputeInstanceService::queryUpdateComputeInstanceFlavor)//查询可修改云主机规格
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.resourceId");
        registerAli(ActionType.UPDATE_COMPUTE_INSTANCE_FLAVOR, Client::modifyInstanceSpec)//修改云主机规格
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 5000, 5000)
                .addSetRefreshData("request", "$.body.cloud.instanceId")
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.START_COMPUTE_INSTANCE, Client::startInstances)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request", "$.body.cloud.instanceId")
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.STOP_COMPUTE_INSTANCE, Client::stopInstances)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request", "$.body.cloud.instanceId")
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.REBOOT_COMPUTE_INSTANCE, Client::rebootInstances)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(30, 2000, 1000)
                .addSetRefreshSplitData("request", "$.body.cloud.instanceId")
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::queryInstanceVncUrl)//查询云主机VNC登陆地址
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId");
        registerAli(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, Client::modifyInstanceVncPasswd)//修改云主机VNC登录密码
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.Password", "$.vncPassword");
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL, ComputeInstanceService::queryInstanceTotal)
                .addBefore(CloudService::defaultRegion);

    }

    public void onAfterLoadComputeImage() {
        register(ActionType.DELETE_COMPUTE_IMAGE, ImageService::deleteImages)//批量删除镜像
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        registerAli(ActionType.QUERY_COMPUTE_IMAGE, Client::describeImages);//查询镜像
        registerAli(ActionType.CREATE_COMPUTE_IMAGE, Client::createImage);//创建自定义镜像
        registerAli(ActionType.UPDATE_COMPUTE_IMAGE, Client::modifyImageAttribute)//修改自定义镜像
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId")
                .addTransferCloud("$.model.resourceName", "$.imageName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.COPY_COMPUTE_IMAGE, Client::copyImage)//复制一个地域下的自定义镜像到其他地域
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId");
        registerAli(ActionType.EXPORT_COMPUTE_IMAGE, Client::exportImage)//导出一份自定义镜像到OSS
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId");
        registerAli(ActionType.UPDATE_COMPUTE_IMAGE_PERMISSION, Client::modifyImageSharePermission)//管理镜像共享权限
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId");
    }

    public void onAfterLoadComputeSecurityGroup() {
        registerAli(ActionType.CREATE_COMPUTE_SECURITYGROUP, Client::createSecurityGroup);//创建安全组
        registerAli(ActionType.DELETE_COMPUTE_SECURITYGROUP, Client::deleteSecurityGroup)//删除安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId");
        registerAli(ActionType.UPDATE_COMPUTE_SECURITYGROUP, Client::modifySecurityGroupAttribute)//修改安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.model.resourceName", "$.securityGroupName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.BIND_COMPUTE_SECURITYGROUP, Client::joinSecurityGroup)//绑定安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.resourceId", "$.securityGroupId");
        registerAli(ActionType.UNBIND_COMPUTE_SECURITYGROUP, Client::leaveSecurityGroup)//解绑安全组
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::createSecurityGroupRule)//创建安全组规则
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId");
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::deleteSecurityGroupRule)//删除安全组规则
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.securityGroupId", "$.securityGroupId")
                .addTransferCloud("$.ci.openId", "$.securityGroupRuleId")
                .addTransferCloud("$.ci.sourceJson.direction", "$.direction");
    }

    public void onAfterLoadComputeKeypair() {
        registerAli(ActionType.CREATE_COMPUTE_KEYPAIR, Client::createKeyPair)//创建密钥对
                .addTransferCloud("$.model.regionId", "$.regionId")
                .addTransferCloud("$.model.resourceName", "$.keyPairName");
        registerAli(ActionType.DELETE_COMPUTE_KEYPAIR, Client::deleteKeyPairs)//删除密钥对
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.keyPairNames", BaseUtils::formatStrArray);
        registerAli(ActionType.ATTACH_COMPUTE_KEYPAIR, Client::attachKeyPair)//挂载密钥对
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.keyPairName")
                .addTransferCloud("$.model.instanceId", "$.instanceIds", BaseUtils::formatStrArray);
        registerAli(ActionType.DETACH_COMPUTE_KEYPAIR, Client::detachKeyPair)//绑定密钥对
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.keyPairName")
                .addTransferCloud("$.model.instanceId", "$.instanceIds", BaseUtils::formatStrArray);
    }

    public void onAfterLoadStorageDisk() {
        registerAli(ActionType.CREATE_STORAGE_DISK, Client::createDisk);//创建云硬盘
        registerAli(ActionType.UPDATE_STORAGE_DISK, Client::modifyDiskAttribute)//修改云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.DiskIds")
                .addTransferCloud("$.model.resourceName", "$.DiskName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        register(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisks)//删除云硬盘
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        registerAli(ActionType.ATTACH_STORAGE_DISK, Client::attachDisk)//挂载云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        registerAli(ActionType.DETACH_STORAGE_DISK, Client::detachDisk)//卸载云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.ci.relationInstance.relationOpenId", "$.instanceId");
        registerAli(ActionType.RESIZE_STORAGE_DISK, Client::resizeDisk)//扩容云硬盘
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.diskId")
                .addTransferCloud("$.model.size", "$.newSize");
    }

    public void onAfterLoadStorageSnapshot() {
        registerAli(ActionType.CREATE_STORAGE_SNAPSHOT, Client::createSnapshot)//创建云硬盘快照
                .addTransferCloud("$.model.regionId", "$.regionId")
                .addTransferCloud("$.model.diskId", "$.diskId")
                .addTransferCloud("$.model.resourceName", "$.snapshotName");
        registerAli(ActionType.UPDATE_STORAGE_SNAPSHOT, Client::modifySnapshotAttribute)//修改云硬盘快照
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.snapshotId")
                .addTransferCloud("$.model.resourceName", "$.snapshotName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        register(ActionType.DELETE_STORAGE_SNAPSHOT, SnapshotService::deleteSnapshots)//删除云硬盘快照
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle);
        registerAli(ActionType.RESET_STORAGE_SNAPSHOT, Client::resetDisk)//回滚快照
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.snapshotId")
                .addTransferCloud("$.ci.relationDisk.relationOpenId", "$.diskId");

        register(ActionType.QUERY_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//查询自动快照策略
                .addValidNull("$.body.cloud.regionId", "请选择云区域!")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "DescribeAutoSnapshotPolicyEx")
                .addDefValue("$.body.api.version", "2014-05-26");
        register(ActionType.CREATE_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//创建自动快照策略
                .addValidNull("$.body.cloud.regionId", "请选择云区域!")
                .addValidNull("$.body.cloud.autoSnapshotPolicyName", "请选择自动快照策略的名称!")
                .addValidNull("$.body.cloud.timePoints", "请选择自动快照的创建时间点!")
                .addValidNull("$.body.cloud.repeatWeekdays", "请选择自动快照的重复日期!")
                .addValidNull("$.body.cloud.retentionDays", "请选择自动快照的保留时间!")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "CreateAutoSnapshotPolicy")
                .addDefValue("$.body.api.version", "2014-05-26");
        register(ActionType.UPDATE_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//修改自动快照策略
                .addValidNull("$.body.ci", "请提供CI模型信息!")
                .addTransferCloud("$.ci.RegionId", "$.regionId")
                .addTransferCloud("$.ci.AutoSnapshotPolicyId", "$.autoSnapshotPolicyId")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "ModifyAutoSnapshotPolicyEx")
                .addDefValue("$.body.api.version", "2014-05-26");
        register(ActionType.DELETE_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//删除自动快照策略
                .addValidNull("$.body.ci", "请提供CI模型信息!")
                .addTransferCloud("$.ci.RegionId", "$.regionId")
                .addTransferCloud("$.ci.AutoSnapshotPolicyId", "$.autoSnapshotPolicyId")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "DeleteAutoSnapshotPolicy")
                .addDefValue("$.body.api.version", "2014-05-26");
        register(ActionType.ATTACH_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//为一块或者多块云盘应用自动快照策略
                .addValidNull("$.body.ci", "请提供CI模型信息!")
                .addValidNull("$.body.cloud.diskIds", "请提供需要挂载的云盘ID!")
                .addTransferCloud("$.ci.RegionId", "$.regionId")
                .addTransferCloud("$.ci.AutoSnapshotPolicyId", "$.autoSnapshotPolicyId")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "ApplyAutoSnapshotPolicy")
                .addDefValue("$.body.api.version", "2014-05-26");
        register(ActionType.DETACH_STORAGE_SNAPSHOT_POLICY, CloudService::doApiPostBody)//取消一块或者多块云盘的自动快照策略
                .addValidNull("$.body.ci", "请提供CI模型信息!")
                .addValidNull("$.body.cloud.diskIds", "请提供需要挂载的云盘ID!")
                .addTransferCloud("$.ci.RegionId", "$.regionId")
                .addTransferCloud("$.ci.AutoSnapshotPolicyId", "$.autoSnapshotPolicyId")
                .addDefValue("$.body.api.productId", "ecs")
                .addDefValue("$.body.api.action", "CancelAutoSnapshotPolicy")
                .addDefValue("$.body.api.version", "2014-05-26");

    }
    private void onAfterLoadStorageOSS() {
        registerAli(ActionType.CREATE_STORAGE_OSS_BUCKET, (FTExecute<OSSClient, CreateBucketRequest, Bucket >)OSSClient::createBucket);
        register(ActionType.DELETE_STORAGE_OSS_BUCKET, OssService::deleteBucket)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.bucketName");
        register(ActionType.DELETE_STORAGE_OSS_BUCKET, OssService::updateBucket)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.bucketName");
    }

    public void onAfterLoadNeutronVpc() {
        register(ActionType.REFRESH_NEUTRON_VPC, RefreshService::refreshVpc)//创建VPC
                .addTransferCloud("$.refreshConfig.data", "$.vpcId")//从刷新配置中获取实例ID
                .addAfter(VpcService::executeRefreshCreateVpcCallbackCreateVSwitch);
        registerAli(ActionType.CREATE_NEUTRON_VPC, Client::createVpc)//创建VPC
                .addSetRefresh(ActionType.REFRESH_NEUTRON_VPC)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshData("response", "$.data.body.vpcId")//将响应vpcId添加到刷新任务
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")//基于请求设置刷新请求的cloud信息
                .addAfter(VpcService::addRefreshCreateVpcCallbackCreateVSwitch)//增加刷新任务回调创建子网任务
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.UPDATE_NEUTRON_VPC, com.aliyun.vpc20160428.Client::modifyVpcAttribute)//修改VPC
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.vpcId")
                .addTransferCloud("$.model.resourceName", "$.vpcName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.DELETE_NEUTRON_VPC, com.aliyun.vpc20160428.Client::deleteVpc)//删除VPC
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.vpcId");

    }

    public void onAfterLoadNeutronSubnet() {
        register(ActionType.REFRESH_NEUTRON_SUBNET, RefreshService::refreshVSwitch)//创建VPC
                .addTransferCloud("$.refreshConfig.data", "$.vSwitchId");//从刷新配置中获取实例ID
        registerAli(ActionType.CREATE_NEUTRON_SUBNET, com.aliyun.vpc20160428.Client::createVSwitch)//创建子网
                .addSetRefresh(ActionType.REFRESH_NEUTRON_SUBNET)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshData("response", "$.data.body.vSwitchId")//将响应vpcId添加到刷新任务
                .addSetRefreshCloud("request", "$.body.cloud.regionId", "$.regionId")//基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerAli(ActionType.UPDATE_NEUTRON_SUBNET, com.aliyun.vpc20160428.Client::modifyVSwitchAttribute)//修改子网
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.vSwitchId")
                .addTransferCloud("$.model.resourceName", "$.vSwitchName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.DELETE_NEUTRON_SUBNET, com.aliyun.vpc20160428.Client::deleteVSwitch)//删除子网
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.vSwitchId");
    }

    public void onAfterLoadNeutronEip() {
        registerAli(ActionType.CREATE_NEUTRON_EIP, com.aliyun.vpc20160428.Client::allocateEipAddress);//创建弹性IP
        registerAli(ActionType.UPDATE_NEUTRON_EIP, com.aliyun.vpc20160428.Client::modifyEipAddressAttribute)//修改弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.allocationId")
                .addTransferCloud("$.model.resourceName", "$.name", false)
                .addTransferCloud("$.model.bandwidth", "$.bandwidth", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.DELETE_NEUTRON_EIP, com.aliyun.vpc20160428.Client::releaseEipAddress)//删除弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.allocationId");
        registerAli(ActionType.BIND_NEUTRON_EIP, com.aliyun.vpc20160428.Client::associateEipAddress)//绑定弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.allocationId")
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        registerAli(ActionType.UNBIND_NEUTRON_EIP, com.aliyun.vpc20160428.Client::unassociateEipAddress)//解绑弹性IP
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.allocationId");
    }

    public void onAfterLoadNeutronNat() {

        registerAli(ActionType.CREATE_NEUTRON_NAT, com.aliyun.vpc20160428.Client::createNatGateway);//创建NAT网关
        registerAli(ActionType.UPDATE_NEUTRON_NAT, com.aliyun.vpc20160428.Client::modifyNatGatewayAttribute)//修改NAT网关
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.natGatewayId")
                .addTransferCloud("$.model.resourceName", "$.name", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.DELETE_NEUTRON_NAT, com.aliyun.vpc20160428.Client::deleteNatGateway)//删除NAT网关
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.natGatewayId");
    }

    public void onAfterLoadNeutronRoute() {

        registerAli(ActionType.CREATE_NEUTRON_ROUTE, com.aliyun.vpc20160428.Client::createRouteTable);//创建路由表
        registerAli(ActionType.UPDATE_NEUTRON_ROUTE, com.aliyun.vpc20160428.Client::modifyRouteTableAttributes)//修改路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.routeTableId")
                .addTransferCloud("$.model.resourceName", "$.routeTableName", false)
                .addTransferCloud("$.model.description", "$.description", false);
        registerAli(ActionType.DELETE_NEUTRON_ROUTE, com.aliyun.vpc20160428.Client::deleteRouteTable)//删除路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.routeTableId");
        registerAli(ActionType.BIND_NEUTRON_ROUTE, com.aliyun.vpc20160428.Client::associateRouteTable)//绑定路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.routeTableId")
                .addTransferCloud("$.model.switchId", "$.vSwitchId");
        registerAli(ActionType.UNBIND_NEUTRON_ROUTE, com.aliyun.vpc20160428.Client::unassociateRouteTable)//解绑路由表
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.routeTableId")
                .addTransferCloud("$.model.switchId", "$.vSwitchId");
    }


}
