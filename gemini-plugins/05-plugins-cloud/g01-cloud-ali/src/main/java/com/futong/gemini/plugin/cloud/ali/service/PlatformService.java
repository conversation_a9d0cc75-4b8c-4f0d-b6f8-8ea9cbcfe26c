package com.futong.gemini.plugin.cloud.ali.service;

import com.aliyun.bssopenapi20171214.Client;
import com.aliyun.bssopenapi20171214.models.QueryAccountBalanceResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class PlatformService {
    public static BaseResponse queryBillBalance(BaseCloudRequest request) {
        QueryAccountBalanceResponse result = CloudClient.client.executeNull(request.getBody(), Client::queryAccountBalance);
        return new BaseDataResponse<>(result.body.data.getAvailableAmount());
    }
}
