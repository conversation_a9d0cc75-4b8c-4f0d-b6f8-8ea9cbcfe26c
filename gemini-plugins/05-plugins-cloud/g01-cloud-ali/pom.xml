<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>05-plugins-cloud</artifactId>
        <groupId>com.futong.gemini</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>g01-cloud-ali</artifactId>
    <properties>
        <plugin.name>cloud-public_ali-4.5.10-${yunjing.version}-${plugin.version}</plugin.name>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.futong.gemini</groupId>
            <artifactId>01-cloud-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ecs20140526</artifactId>
            <version>5.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cms20190101</artifactId>
            <version>7.0.36</version>
        </dependency>
        <!-- 路由引用VPC jiar包 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>vpc20160428</artifactId>
            <version>2.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>actiontrail20200706</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- 对象存储，同步 -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.15.1</version>
        </dependency>
        <!-- RDS -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>rds20140815</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!-- kms密钥 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>kms20160120</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>bssopenapi20171214</artifactId>
            <version>1.7.9</version>
        </dependency>
        <!-- 资源管理 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>resourcemanager20200331</artifactId>
            <version>2.1.0</version>
        </dependency>
        <!-- 负载均衡升级版SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>slb20140515</artifactId>
            <version>3.3.18</version>
        </dependency>
        <!-- Redis 数据库-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>r_kvstore20150101</artifactId>
            <version>2.20.5</version>
        </dependency>
        <!-- MongoDB 升级版SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dds20151201</artifactId>
            <version>3.6.13</version>
        </dependency>
        <!-- K8s -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cs20151215</artifactId>
            <version>3.0.17</version>
        </dependency>
        <!-- 文件存储 升级版SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>nas20170626</artifactId>
            <version>2.0.2</version>
        </dependency>
        <!-- kafka 升级版SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alikafka20190916</artifactId>
            <version>1.3.5</version>
        </dependency>

        <!-- 分布式原生数据库2.0 polardbx 升级版SDK -->
        <!--<dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>polardbx20200202</artifactId>
            <version>2.2.2</version>
        </dependency>-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>polardbx20200202</artifactId>
            <version>2.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>drds20190123</artifactId>
            <version>1.0.10</version>
        </dependency>

        <!-- rocketmq 5.0版 升级版SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>rocketmq20220801</artifactId>
            <version>1.0.3</version>
        </dependency>
    </dependencies>

</project>