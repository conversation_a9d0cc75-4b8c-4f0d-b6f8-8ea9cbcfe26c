{"data": [{"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取租户", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取租户", "jobInfo": "{\"action\": \"FetchCaasTenant\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取项目", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取项目", "jobInfo": "{\"action\": \"FetchCaasProject\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群", "jobInfo": "{\"action\": \"FetchCaasCluster\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群命名空间", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群命名空间", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterNamespace\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群主机节点", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群主机节点", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterNode\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群工作负载-有状态部署", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群工作负载-有状态部署", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterWorkDeployments\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群工作负载-无状态部署", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群工作负载-无状态部署", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterWorkStatefulsets\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群工作负载-守护进程", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群工作负载-守护进程", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterWorkDeamonset\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群工作负载-普通任务", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群工作负载-普通任务", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterWorkJob\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群工作负载-pod容器组", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群工作负载-pod容器组", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterWorkPods\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群配置文件", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群配置文件", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterConfigMap\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群存储-PV", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群存储-PV", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterStoragePv\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群存储-PVC", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群存储-PVC", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterStoragePvc\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取集群存储-服务class", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取集群存储-服务class", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterStorageClass\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取Ingress路由", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取Ingress路由", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterNetIngressNginx\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取网络内部服务", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取网络内部服务", "jobInfo": "{\"action\": \"FetchCaasClusterNetServiceInternal\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取网络外部服务", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取网络外部服务", "jobInfo": "{\"action\": \"FetchCaasClusterNetServiceExternal\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取nginx负载均衡", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取nginx负载均衡", "jobInfo": "{\"action\": \"FetchCaasClusterNetIcNginx\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取中间件-MySQL", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取中间件-MySQL", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterMiddlewareMysql\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取中间件-Redis", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取中间件-Redis", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterMiddlewareRedis\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取中间件-MongoDB", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取中间件-MongoDB", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterMiddlewareMongodb\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "form": ["region"], "dispatcher_info": {"jobName": "获取中间件-RocketMQ", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_relation,bxc_resource,cmp_atlas_exchange.cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取中间件-RocketMQ", "jobInfo": "{\"action\": \"FetchCaasClusterSeed\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"},\"model\": {\"childAction\": \"FetchCaasClusterMiddlewareRocketmq\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}