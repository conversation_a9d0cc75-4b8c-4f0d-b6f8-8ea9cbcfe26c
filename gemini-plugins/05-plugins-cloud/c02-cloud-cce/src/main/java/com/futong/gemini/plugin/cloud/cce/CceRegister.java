package com.futong.gemini.plugin.cloud.cce;

import com.futong.gemini.plugin.cloud.cce.service.BaseService;
import com.futong.gemini.plugin.cloud.cce.service.ClusterNodeService;
import com.futong.gemini.plugin.cloud.cce.service.ClusterService;
import com.futong.gemini.plugin.cloud.cce.service.FetchService;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class CceRegister extends BaseCloudRegister {

    @Override
    public void load() {
        //加载云平台操作
        onAfterLoadPlatform();
        //加载同步调度信息
        onAfterLoadFetch();
        //加载集群操作
        onAfterLoadCluster();
        //加载节点操作
        onAfterLoadNode();
    }

    public void onAfterLoadPlatform() {
        //基础操作
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);//添加默认调度任务
        //平台账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, BaseService::authAccount);//认证云账号
//        register(ActionType.QUERY_CAAS_GENERAL_GET_DATA,BasicGeneralService::getData);//通用数据获取
//        register(ActionType.QUERY_CAAS_GENERAL_POST_DATA,BasicGeneralService::postData);//通用数据获取
    }

    public void onAfterLoadFetch() {
        //同步集群--平台接口
        register(ActionType.FETCH_CAAS_CLUSTER, FetchService::fetchCluster);
        //同步集群资源种子任务
        register(ActionType.FETCH_CAAS_CLUSTER_SEED, FetchService::fetchClusterSeed);
        //同步集群命名空间--调用k8s原生API
        register(ActionType.FETCH_CAAS_CLUSTER_NAMESPACE, FetchService::fetchNamespace);
        //同步集群主机节点--平台接口
        register(ActionType.FETCH_CAAS_CLUSTER_NODE, FetchService::fetchNode);
        //同步集群工作负载-无状态部署--调用k8s原生API
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_DEPLOYMENTS, FetchService::fetchWorkDeployments);
        //同步集群工作负载-有状态部署
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_STATEFULSETS, FetchService::fetchWorkStatefulsets);
        //同步集群工作负载-pod容器组
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_PODS, FetchService::fetchWorkPods);
        //同步集群工作负载-守护进程
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_DEAMONSET, FetchService::fetchWorkDeamonset);
        //同步集群工作负载-普通任务
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_JOB, FetchService::fetchWorkJob);
        //同步集群工作负载-定时任务
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_CRONJOB, FetchService::fetchWorkCronjob);
        //同步配置-配置文件
        register(ActionType.FETCH_CAAS_CLUSTER_CONFIG_MAP, FetchService::fetchConfigMap);
        //同步网络服务
        register(ActionType.FETCH_CAAS_CLUSTER_NET_SERVICE_EXTERNAL, FetchService::fetchNetService);
        //同步Ingress路由
        register(ActionType.FETCH_CAAS_CLUSTER_NET_INGRESS_NGINX, FetchService::fetchNetNginxIngress);
    }

    public void onAfterLoadCluster() {
        //查询集群详情
        register(ActionType.QUERY_CAAS_CLUSTER_DETAIL, ClusterService::queryClusterInfo);
        //创建集群
        register(ActionType.CREATE_CAAS_CLUSTER, ClusterService::createCluster);
        //删除集群
        register(ActionType.DELETE_CAAS_CLUSTER, ClusterService::deleteCluster);

    }

    public void onAfterLoadNode() {
        //查询节点详情
        register(ActionType.QUERY_CAAS_CLUSTER_NODE_DETAIL, ClusterNodeService::queryNodeDetail);
        //删除节点
//        register(ActionType.DELETE_CAAS_CLUSTER_NODE, ClusterNodeService::deleteClusterNode);
    }
}
