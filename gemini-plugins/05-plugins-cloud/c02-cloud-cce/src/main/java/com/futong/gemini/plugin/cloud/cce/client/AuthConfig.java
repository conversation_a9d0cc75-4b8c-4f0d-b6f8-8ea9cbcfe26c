package com.futong.gemini.plugin.cloud.cce.client;

import lombok.Getter;

@Getter
public class AuthConfig {

    private final String username;
    private final String password;

    private AuthConfig(Builder builder) {
        this.username = builder.username;
        this.password = builder.password;
    }

    public static class Builder {
        private String username;
        private String password;

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }


        public AuthConfig build() {
            validate();
            return new AuthConfig(this);
        }

        private void validate() {
            if (username == null || password == null) {
                throw new IllegalArgumentException("Missing auth parameters");
            }
        }
    }
}
