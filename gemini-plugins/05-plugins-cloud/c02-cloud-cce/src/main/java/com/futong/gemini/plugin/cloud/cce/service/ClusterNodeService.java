package com.futong.gemini.plugin.cloud.cce.service;

import cn.hutool.core.util.StrUtil;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cce.client.CceClient;
import com.futong.gemini.plugin.cloud.cce.client.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClusterNodeService {

    public static BaseResponse queryNodeDetail(BaseCloudRequest request) {
        CceClient client = ClientUtils.client(CceClient.class, request.getBody());
        String apiPath = "/api/v3/clusters/{cluster}/nodes/{node}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse deleteClusterNode(BaseCloudRequest request) {
        CceClient client = ClientUtils.client(CceClient.class, request.getBody());
        String apiPath = "/api/v3/clusters/{cluster}/nodes/{node}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }
}
