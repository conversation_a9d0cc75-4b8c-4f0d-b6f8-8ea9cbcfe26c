package com.futong.gemini.plugin.cloud.cce.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cce.client.CceClient;
import com.futong.gemini.plugin.cloud.cce.client.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class WorkDeploymentsService {

    public static BaseResponse createWorkDeployments(BaseCloudRequest request) {
        try {
            CceClient client = ClientUtils.client(CceClient.class, request.getBody());
            String apiPath = "/apis/apps/v1/namespaces/{namespace}/deployments";
            apiPath = StrUtil.format(apiPath, request.getBody().getModel());
            String cluster = request.getBody().getModel().getString("cluster");
            Map<String,String> params = new HashMap<>();
            params.put("X-Cluster-Id", cluster);
            JSONObject result = client.doPostJSON(apiPath, request.getBody().getCloud(), params);
            return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
        }catch (Exception e){
            log.error("创建工作负载-无状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建工作负载-无状态失败!"), e);
        }
    }

    public static BaseResponse deleteWorkDeployments(BaseCloudRequest request) {
        try {
            CceClient client = ClientUtils.client(CceClient.class, request.getBody());
            String cluster = request.getBody().getModel().getString("cluster");
            Map<String,String> params = new HashMap<>();
            params.put("X-Cluster-Id", cluster);
            String apiPath = "/apis/apps/v1/namespaces/{namespace}/deployments/{deployment}";
            apiPath = StrUtil.format(apiPath, request.getBody().getModel());
            Object result = client.doDeleteJSON(apiPath, params);
            return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
        }catch (Exception e){
            log.error("删除工作负载-无状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除工作负载-无状态失败!"), e);
        }
    }

    public static BaseResponse updateWorkDeploymentsReplicas(BaseCloudRequest request) {
        try {
            CceClient client = ClientUtils.client(CceClient.class, request.getBody());
            String cluster = request.getBody().getModel().getString("cluster");
            Map<String,String> params = new HashMap<>();
            params.put("X-Cluster-Id", cluster);
            String apiPath = "/apis/apps/v1/namespaces/{namespace}/deployments/{deployment}";
            apiPath = StrUtil.format(apiPath, request.getBody().getModel());
            JSONObject result = client.doPatchJSON(apiPath, request.getBody().getCloud(),params);
            return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
        }catch (Exception e){
            log.error("伸缩容工作负载-无状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("伸缩容工作负载-无状态失败!"), e);
        }
    }

    public static BaseResponse updateWorkDeploymentsYaml(BaseCloudRequest request) {
        try {
            CceClient client = ClientUtils.client(CceClient.class, request.getBody());
            String apiPath = "/apis/apps/v1/namespaces/{namespace}/deployments/{deployment}";
            apiPath = StrUtil.format(apiPath, request.getBody().getModel());
            Map<String,String> params = new HashMap<>();
            params.put("X-Cluster-Id", request.getBody().getModel().getString("cluster"));
            JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud(), params);
            return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
        }catch (Exception e){
            log.error("更新工作负载-无状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("更新工作负载-无状态失败!"), e);
        }
    }

    public static BaseResponse queryWorkDeploymentsDetail(BaseCloudRequest request) {
        try {
            CceClient client = ClientUtils.client(CceClient.class, request.getBody());
            String cluster = request.getBody().getModel().getString("cluster");
            request.getBody().getModel().remove("cluster");
            Map<String,String> params = new HashMap<>();
            params.put("X-Cluster-Id", cluster);
            String apiPath = "/apis/apps/v1/namespaces/{namespace}/deployments/{deployment}";
            apiPath = StrUtil.format(apiPath, request.getBody().getModel());
            Object result = client.doGetJSON(apiPath, params);
            return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
        }catch (Exception e){
            log.error("查询工作负载-无状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询工作负载-无状态失败!"), e);
        }
    }
}
