<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>05-plugins-cloud</artifactId>
        <groupId>com.futong.gemini</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>s01-cloud-cloudos-e7</artifactId>
    <properties>
        <plugin.name>cloud-private_huasan-e7-${yunjing.version}-${plugin.version}</plugin.name>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.futong.gemini</groupId>
            <artifactId>01-cloud-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.2.2</version>
        </dependency>
        <dependency>
            <artifactId>aws-java-sdk-s3</artifactId>
            <groupId>com.amazonaws</groupId>
            <version>1.11.622</version>
        </dependency>
        <dependency>
            <artifactId>aws-java-sdk-kms</artifactId>
            <groupId>com.amazonaws</groupId>
            <version>1.11.622</version>
        </dependency>
        <dependency>
            <artifactId>aws-java-sdk-core</artifactId>
            <groupId>com.amazonaws</groupId>
            <version>1.11.622</version>
        </dependency>
        <dependency>
            <artifactId>aws-java-sdk-sts</artifactId>
            <groupId>com.amazonaws</groupId>
            <version>1.11.622</version>
        </dependency>
        <dependency>
            <artifactId>guava</artifactId>
            <groupId>com.google.guava</groupId>
            <version>21.0</version>
        </dependency>

    </dependencies>

</project>