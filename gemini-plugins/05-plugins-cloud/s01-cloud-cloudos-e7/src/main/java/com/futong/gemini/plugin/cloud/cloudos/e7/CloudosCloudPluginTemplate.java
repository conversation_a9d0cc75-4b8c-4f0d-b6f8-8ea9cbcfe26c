package com.futong.gemini.plugin.cloud.cloudos.e7;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.log.DynamicLoggerConfigurator;
import com.futong.gemini.plugin.cloud.cloudos.e7.service.AccountService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudPluginTemplate;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Locale;
@Slf4j
public class CloudosCloudPluginTemplate extends BaseCloudPluginTemplate {

    @Override
    public void init(String key) {
        super.init(key);
        loadAccountForm();
        loadAccountDispatch();
        //指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.cloudos7.0", "/cloud/cloudos7.0");

    }


    @Override
    public BaseCloudRegister getRegister() {
        return new CloudosCloudRegister();
    }
    private static String[] files = {
            "add_form_en-US.json",
            "add_form_zh-CN.json"
    };

    public void loadAccountForm() {
        try {
            for (String file : files) {
                InputStream is = getClass().getClassLoader().getResourceAsStream("account/" + file);
                String text = IoUtil.readUtf8(is);
                JSONObject json = JSON.parseObject(text);
                String lang = extractLocaleFromFilename(file);
                Locale locale = Locale.forLanguageTag(lang);
                AccountService.accountForm.put(locale, json);
            }
        } catch (Exception e) {
            log.error("加载插件账号表单信息失败!", e);
        }
    }

    public void loadAccountDispatch() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("account/add_dispatch.json");
            String text = IoUtil.readUtf8(is);
            String[] split = key.split(":");
            //替换参数插件作用域
            text = StrUtil.replace(text, "#{plugin_realm}", split[0]);
            //替换参数插件版本
            text = StrUtil.replace(text, "#{plugin_version}", split[1]);
            AccountService.accountDispatch = text;
        } catch (Exception e) {
            log.error("加载"+key+"插件账号调度任务信息失败!", e);
        }
    }

    public static String extractLocaleFromFilename(String filename) {
        // 假设文件名格式为 "add_form_<locale>.json"
        // 找到最后一个 "_" 的位置
        int lastUnderscoreIndex = filename.lastIndexOf('_');
        if (lastUnderscoreIndex == -1 || !filename.endsWith(".json")) {
            throw new IllegalArgumentException("Invalid filename format: " + filename);
        }
        // 提取下划线之后、".json"之前的部分
        return filename.substring(lastUnderscoreIndex + 1, filename.length() - 5);
    }
}
