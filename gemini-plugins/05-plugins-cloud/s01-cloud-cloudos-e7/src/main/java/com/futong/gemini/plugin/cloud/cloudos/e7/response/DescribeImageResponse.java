package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeImageResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeImageResponseBody body;

    @Data
    public static class DescribeImageResponseBody extends BaseResponseBody {
        @NameInMap("pageNumber")
        public Integer pageNumber;
        @NameInMap("pageSize")
        public Integer pageSize;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("images")
        public List<DescribeImageResponseBodyData> images;

    }

    @Data
    public static class DescribeImageResponseBodyData extends TeaModel {
        @NameInMap("imageId")
        public String imageId;
        @NameInMap("status")
        public String status;
        @NameInMap("imageName")
        public String imageName;
        @NameInMap("operatingSystem")
        public String operatingSystem;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("zoneId")
        public String zoneId;
        @NameInMap("size")
        public Integer size;
        @NameInMap("ostype")
        public String ostype;
        @NameInMap("creationTime")
        public Long creationTime;
//        @NameInMap("dataImageInfo")
//        public List<DescribeImageResponseBodyDataAz> dataImageInfo;
    }

    @Data
    public static class DescribeImageResponseBodyDataAz extends TeaModel {
        @NameInMap("ImageId")
        public String imageId;
        @NameInMap("TemplateId")
        public String templateId;
        @NameInMap("Size")
        public Integer size;
    }

}
