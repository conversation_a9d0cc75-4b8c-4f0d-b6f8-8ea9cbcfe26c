package com.futong.gemini.plugin.cloud.cloudos.e7.response;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class CreateDiskResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public CreateDiskResponseBody body;
    @Data
    public static class CreateDiskResponseBody extends BaseResponseBody{
        @NameInMap("Ids")
        public List<String> ids;
        @NameInMap("OrderId")
        public String orderId;
    }

}
