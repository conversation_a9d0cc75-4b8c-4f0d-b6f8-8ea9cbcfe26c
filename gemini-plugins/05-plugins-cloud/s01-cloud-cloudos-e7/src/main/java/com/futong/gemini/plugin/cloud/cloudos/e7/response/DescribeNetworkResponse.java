package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeNetworkResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNetworkResponseBody body;

    @Data
    public static class DescribeNetworkResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public List<DescribeNetworkResponseBodyData> res;

    }

    @Data
    public static class DescribeNetworkResponseBodyData extends TeaModel {
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("Status")
        public String status;
        @NameInMap("InstanceCode")
        public String instanceCode;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("Cidr")
        public String cidr;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("Subnets")
        public List<DescribeNetworkResponseBodyDataAz> subnets;
    }

    @Data
    public static class DescribeNetworkResponseBodyDataAz extends TeaModel {
        @NameInMap("Name")
        public String name;
        @NameInMap("Id")
        public String id;
        @NameInMap("DnsNameServers")
        public List<String> dnsNameServers;
        @NameInMap("IpAssignedCount")
        public Integer ipAssignedCount;
        @NameInMap("Cidr")
        public String cidr;
        @NameInMap("IpTotalCount")
        public Integer ipTotalCount;
        @NameInMap("IsCrossZone")
        public boolean isCrossZone;
        @NameInMap("AzoneId")
        public String azoneId;
        @NameInMap("GatewayIp")
        public String gatewayIp;
        @NameInMap("IpV6Cidr")
        public String ipV6Cidr;
        @NameInMap("IpV6GatewayIp")
        public String ipV6GatewayIp;
        @NameInMap("HasIpV6")
        public boolean hasIpV6;
//        @NameInMap("BindResources")
//        public List<DescribeNetworkResponseBodyDataBz> bindResources;
//        @NameInMap("AllocationPool")
//        public List<DescribeNetworkResponseBodyDataCz> allocationPool;
    }

//    @Data
//    public static class DescribeNetworkResponseBodyDataBz extends TeaModel {
//        @NameInMap("ReviceId")
//        public String reviceId;
//        @NameInMap("ResourceType")
//        public String resourceType;
//    }
//
//    @Data
//    public static class DescribeNetworkResponseBodyDataCz extends TeaModel {
//        @NameInMap("End")
//        public String end;
//        @NameInMap("Start")
//        public String start;
//    }

}
