package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
@Data
public class DescribeDBInstancesResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeDBInstancesResponseBody body;
    @Data
    public static class DescribeDBInstancesResponseBody extends BaseResponseBody{
        @NameInMap("Items")
        public List<DescribeDBInstancesResponseBodyData> list;
        @NameInMap("PageNumber")
        public Integer page;
        @NameInMap("PageSize")
        public Integer pageSize;
        @NameInMap("TotalRecordCount")
        public Integer totalRecordCount;
        @NameInMap("TotalPageCount")
        public Integer totalPageCount;
        @NameInMap("RequestId")
        public String requestId;


    }
    @Data
    public static class DescribeDBInstancesResponseBodyData extends TeaModel{
        @NameInMap("DBInstanceId")
        public String instanceId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("InstanceType")
        public String instanceType;
        @NameInMap("InstanceMode")
        public String instanceMode;
        @NameInMap("InstanceStatus")
        public String status;
        @NameInMap("InstanceClass")
        public String instanceClass;
        @NameInMap("Engine")
        public String engine;
        @NameInMap("EngineVersion")
        public String engineVersion;
        @NameInMap("PayType")
        public String payType;
        @NameInMap("VpcId")
        public String vpcId;
        @NameInMap("CreateTime")
        public String createTime;
        @NameInMap("ExpireTime")
        public String expireTime;
        @NameInMap("CreateUserName")
        public String createUserName;
        @NameInMap("ProjectName")
        public String projectName;
        @NameInMap("InstanceStorageType")
        public String instanceStorageType;
        @NameInMap("NodeList")
        public List<DescribeDBInstanceAttributeResponse.DescribeDBInstanceAttributeResponseBodyData> nodeList;

    }

}
