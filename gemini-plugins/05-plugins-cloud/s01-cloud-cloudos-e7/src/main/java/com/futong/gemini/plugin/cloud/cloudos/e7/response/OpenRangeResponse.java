package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class OpenRangeResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public Body body;

    @Data
    public static class Body extends BaseResponseBody {
        @NameInMap("code")
        public String code;
        @NameInMap("auth")
        public Boolean auth;
        @NameInMap("status")
        public Boolean status;
        @NameInMap("res")
        public List<BodyRes> res;
    }

    @Data
    public static class BodyRes extends TeaModel{
        @NameInMap("data")
        public BodyResData data;
        @NameInMap("status")
        public String status;
    }

    @Data
    public static class BodyResData extends TeaModel{
        @NameInMap("result")
        public List<BodyResDataResult> result;
        @NameInMap("resultType")
        public String resultType;
    }

    @Data
    public static class BodyResDataResult extends TeaModel{
        @NameInMap("metric")
        public BodyResDataResultMetric metric;
        @NameInMap("values")
        public List<List<Double>> values;
    }

    @Data
    public static class BodyResDataResultMetric extends TeaModel{
        @NameInMap("defaultUnit")
        public String defaultUnit;
        @NameInMap("category")
        public String category;
        @NameInMap("instance")
        public String instance;
        @NameInMap("showUnit")
        public String showUnit;
        @NameInMap("categoryName")
        public String categoryName;
    }
}
