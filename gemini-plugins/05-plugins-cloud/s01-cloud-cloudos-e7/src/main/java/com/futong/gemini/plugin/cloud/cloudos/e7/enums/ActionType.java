package com.futong.gemini.plugin.cloud.cloudos.e7.enums;

import cn.hutool.core.util.StrUtil;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ModuleType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OperationType;
import com.google.common.base.CaseFormat;

public enum ActionType {

    QUERY_COMPUTE_RESOURCE("计算资源-查询全部计算容量信息",ModuleType.COMPUTE,null,OperationType.QUERY);

    String cname;
    ModuleType moduleType;
    ResourceType resourceType;
    OperationType operationType;

    ActionType(String cname, ModuleType moduleType, ResourceType resourceType, OperationType operationType) {
        this.cname = cname;
        this.moduleType = moduleType;
        this.resourceType = resourceType;
        this.operationType = operationType;
    }

    public String cname() {
        return cname;
    }

    public ModuleType moduleType() {
        return moduleType;
    }

    public ResourceType resourceType() {
        return resourceType;
    }

    public OperationType operationType() {
        return operationType;
    }

    public String value() {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, this.name());
    }

    public static ActionType fromValue(String value) {
        for (ActionType type : values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }

    public static ActionType fromCamelValue(String value) {
        value = StrUtil.toUnderlineCase(value);
        return fromValue(value);
    }
}
