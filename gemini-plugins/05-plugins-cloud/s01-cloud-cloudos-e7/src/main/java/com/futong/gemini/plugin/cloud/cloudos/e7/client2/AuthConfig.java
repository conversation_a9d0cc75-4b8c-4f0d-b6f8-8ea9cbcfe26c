package com.futong.gemini.plugin.cloud.cloudos.e7.client2;

import lombok.Getter;

// 认证信息封装
@Getter
public class AuthConfig {
    private final String appId;
    private final String username;
    private final String password;

    private AuthConfig(Builder builder) {
        this.username = builder.username;
        this.password = builder.password;
        this.appId = builder.appId;
    }

    public static class Builder {
        private String appId;
        private String username;
        private String password;

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder appId(String appId) {
            this.appId = appId;
            return this;
        }

        public AuthConfig build() {
            validate();
            return new AuthConfig(this);
        }

        private void validate() {
            if (username == null || password == null) {
                throw new IllegalArgumentException("Missing auth parameters");
            }
        }
    }

}