package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeNetworkInterfacesResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNetworkInterfacesResponseBody body;
    @Data
    public static class DescribeNetworkInterfacesResponseBody extends BaseResponseBody{
        @NameInMap("list")
        public List<DescribeNetworkInterfacesResponseBodyData> list;
        @NameInMap("page")
        public Integer page;
        @NameInMap("size")
        public Integer size;
        @NameInMap("RequestId")
        public String RequestId;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("totalPages")
        public Integer totalPages;
    }
    @Data
    public static class DescribeNetworkInterfacesResponseBodyData extends TeaModel{
        @NameInMap("InstanceId")
        private String instanceId;
        @NameInMap("instanceName")
        private String instanceName;
        @NameInMap("azoneId")
        private String azoneId;
        @NameInMap("azoneName")
        private String azoneName;
        @NameInMap("vmId")
        private String vmId;
        @NameInMap("ipv4Addr")
        private String ipv4Addr;
        @NameInMap("ipv6Addr")
        private String ipv6Addr;
        @NameInMap("type")
        private String type;
        @NameInMap("status")
        private String status;
        @NameInMap("releaseType")
        private Integer releaseType;
        @NameInMap("createTime")
        private Long createTime;
        @NameInMap("vpcId")
        private String vpcId;
        @NameInMap("subnetId")
        private String subnetId;
        @NameInMap("storageInfo")
        private Object storageInfo;
        @NameInMap("ability")
        private List<Object> ability;
        @NameInMap("eipIp")
        private String eipIp;
        @NameInMap("eipName")
        private String eipName;
        @NameInMap("eipStatus")
        private String eipStatus;
        @NameInMap("eipCategory")
        private String eipCategory;
        @NameInMap("bandWidth")
        private Integer bandWidth;
        @NameInMap("eipId")
        private String eipId;
        @NameInMap("sgList")
        private List<SecurityGroup> sgList;
        @NameInMap("vpcName")
        private String vpcName;
        @NameInMap("vpcCidr")
        private String vpcCidr;
        @NameInMap("subnetName")
        private String subnetName;
        @NameInMap("subnetCidr")
        private String subnetCidr;
    }

    @Data
    public static class SecurityGroup extends TeaModel{
        private String instanceId;
        private String instanceName;
        private String hostName;
    }

}
