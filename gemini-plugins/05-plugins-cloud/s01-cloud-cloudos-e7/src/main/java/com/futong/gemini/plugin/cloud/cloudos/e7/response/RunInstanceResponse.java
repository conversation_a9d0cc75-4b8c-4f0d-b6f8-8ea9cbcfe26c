package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.ArrayList;

@Data
public class RunInstanceResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public RunInstanceResponseBody body;

    @Data
    public static class RunInstanceResponseBody extends BaseResponseBody {
        @NameInMap("orderId")
        public String orderId;
        @NameInMap("instanceIds")
        public ArrayList<String> instanceIds;
    }
}
