package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import lombok.Data;

@Data
public class StorageAcctResponse {

    private Storage storage;
    private FileSpace file;

    @Data
    public static class Storage {
        private Float Total;
        private Float SaleTotal;
        private Float Used;
        private Float Efficiency;
        private Float UsedPercent;
    }

    @Data
    public static class FileSpace {
        private int SpaceTotal;
        private int SpaceUsed;
        private double UsedSpaceRate;
    }
}
