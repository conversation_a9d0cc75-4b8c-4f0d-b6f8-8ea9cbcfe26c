package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

@Data
public class DescribeInstanceVncUrlResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstanceVncUrlResponseBody body;
    @Data
    public static class DescribeInstanceVncUrlResponseBody extends BaseResponseBody{
        @NameInMap("wssAddress")
        public String wssAddress;
    }

}
