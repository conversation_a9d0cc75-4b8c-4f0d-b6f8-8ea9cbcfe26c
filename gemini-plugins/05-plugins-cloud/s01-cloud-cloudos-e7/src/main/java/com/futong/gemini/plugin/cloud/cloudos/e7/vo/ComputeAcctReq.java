package com.futong.gemini.plugin.cloud.cloudos.e7.vo;

import lombok.Data;

@Data
public class ComputeAcctReq {

    /**
     * 裸金属售卖率不小于
     */
    private double BareMetalSellRateSize;
    /**
     * 集群标签
     */
    private String ComputeTypes;
    /**
     * cpu使用率不小于
     */
    private double CpuRateSize;
    /**
     * 本地存储盘分配率不小于
     */
    private double DiskRateSize;
    /**
     * GPU分配率不小于
     */
    private double GpuRateSize;
    /**
     * 内存使用率不小于
     */
    private double MemRateSize;
    /**
     * 当前页
     */
    private int PageNumber;
    /**
     * 每页面数量
     */
    private int PageSize;
    /**
     * 查看粒度
     */
    private String QueryType;
    /**
     * 资源池
     */
    private String Regions;
    /**
     * 资源池
     */
    private String ResourcePool;
    /**
     * 可用区
     */
    private String AvailabilityZone;

    private String Action;
}
