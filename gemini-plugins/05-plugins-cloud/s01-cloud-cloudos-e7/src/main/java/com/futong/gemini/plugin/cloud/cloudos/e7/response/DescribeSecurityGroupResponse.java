package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeSecurityGroupResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeSecurityGroupResponseBody body;

    @Data
    public static class DescribeSecurityGroupResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeSecurityGroupResponseBodyData res;

    }

    @Data
    public static class DescribeSecurityGroupResponseBodyData extends TeaModel {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Integer total;
        @NameInMap("Data")
        public List<DescribeSecurityGroupResponseBodyDataAz> data;
    }

    @Data
    public static class DescribeSecurityGroupResponseBodyDataAz extends TeaModel {
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("Status")
        public String status;
        @NameInMap("InstanceNum")
        public Integer instanceNum;
        @NameInMap("InstanceCode")
        public String instanceCode;
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("Description")
        public String description;
        @NameInMap("UserId")
        public String userId;
    }

}
