package com.futong.gemini.plugin.cloud.cloudos.e7.response;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DetailInstancesResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DetailInstancesResponseBody body;
    @Data
    public static class DetailInstancesResponseBody extends BaseResponseBody{
        @NameInMap("id")
        public String instanceId;
        @NameInMap("name")
        public String instanceName;
        @NameInMap("status")
        public String status;
        @NameInMap("sgIds")
        public List<String> sgIds;
        @NameInMap("keyPair")
        public String keyPair;
    }


}
