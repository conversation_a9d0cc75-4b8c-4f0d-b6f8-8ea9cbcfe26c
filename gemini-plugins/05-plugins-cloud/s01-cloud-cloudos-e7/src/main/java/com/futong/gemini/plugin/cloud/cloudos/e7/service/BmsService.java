package com.futong.gemini.plugin.cloud.cloudos.e7.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeInstanceVncUrlResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class BmsService {

    /**
     * 获取云主机的VNC远程登录地址
     *
     * @param request
     * @return
     */
    public static BaseResponse describeBmsVncUrl(BaseCloudRequest request) {
        try {
            JSONObject result = CloudClient.client.execute(request.getBody(), EcsClient::describeBmsVncUrl);
            JSONObject response = new JSONObject();
            if (result.getJSONObject("body") != null && !StrUtil.isEmpty(result.getJSONObject("body").getString("wssAddress"))) {
                String replaceDomain = StrUtil.replace(result.getJSONObject("body").getString("wssAddress"), "{domain}", request.getBody().getAccess().getServerIp() + ":12011");
                response.put("protocol", result.getJSONObject("body").getString("wssAddress").startsWith("wss") ? "wss" : "ws");
                response.put("wssAddress", "/websock/" + replaceDomain);
                return new BaseDataResponse<>(response);
            } else {
                throw new BaseException(BaseResponse.FAIL, "获取裸金属vnc地址信息为空!");
            }
        } catch (Exception e) {
            log.error("获取裸金属的VNC远程登录地址失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机的VNC远程登录地址失败");
        }
    }

    /**
     * 查询云主机密码
     *
     * @param request
     * @return
     */
    public static BaseResponse getBmsPassword(BaseCloudRequest request) {
        try {
            String password = "";
            Map<String, ?> map = CloudClient.client.execute(request.getBody(), EcsClient::getBmsPassword);
            if (CollUtil.isNotEmpty(map)) {
                String json = JSON.toJSONString(map);
                password = JSON.parseObject(json).getJSONObject("body").getString("password");
            }
            return new BaseDataResponse<>(password);
        } catch (Exception e) {
            log.error("查询云主机密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询云主机密码失败");
        }
    }
}
