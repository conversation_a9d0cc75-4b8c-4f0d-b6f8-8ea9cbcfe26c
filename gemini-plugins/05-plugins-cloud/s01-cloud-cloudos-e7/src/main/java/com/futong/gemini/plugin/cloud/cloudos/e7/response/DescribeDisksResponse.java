package com.futong.gemini.plugin.cloud.cloudos.e7.response;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DescribeDisksResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeDisksResponseBody body;
    @Data
    public static class DescribeDisksResponseBody extends BaseResponseBody{
        @NameInMap("records")
        public List<DescribeDisksResponseBodyData> records;
        @NameInMap("page")
        public Integer pages;
        @NameInMap("size")
        public Integer size;
        @NameInMap("totalCount")
        public Integer total;


    }
    @Data
    public static class DescribeDisksResponseBodyData extends TeaModel{
        @NameInMap("diskName")
        public String diskName;
        @NameInMap("instanceEndTime")
        public Long instanceEndTime;
        @NameInMap("chargeType")
        public Integer chargeType;
        @NameInMap("description")
        public String description;
        @NameInMap("updateTime")
        public Date updateTime;
        @NameInMap("instanceStartTime")
        public Long instanceStartTime;
        @NameInMap("diskSize")
        public String diskSize;
        @NameInMap("userId")
        public String userId;
        @NameInMap("azoneId")
        public String azoneId;
        @NameInMap("payType")
        public String payType;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("createTime")
        public Date createTime;
        @NameInMap("storageType")
        public String storageType;
        @NameInMap("diskId")
        public String diskId;
        @NameInMap("diskType")
        public String diskType;
        @NameInMap("specificationCode")
        public String specificationCode;
        @NameInMap("status")
        public String status;
        @NameInMap("attachInfos")
        public List<DescribeDisksResponseBodyDataInstances> attachInfos;
    }

    @Data
    public static class DescribeDisksResponseBodyDataInstances extends TeaModel{
        @NameInMap("instanceId")
        public String instanceId;
        @NameInMap("type")
        public String type;
    }

}
