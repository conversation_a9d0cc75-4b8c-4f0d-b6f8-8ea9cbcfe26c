package com.futong.gemini.plugin.cloud.cloudos.e7.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbBmsRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeInstancesResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.RunInstanceResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.service.CloudService;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class RefreshService {
    public static JobInfo toJobInfo(BaseCloudRequest request, JSONObject jobRequest, Integer interval) {
        GourdInfo gourd = request.getBody().getGourd();
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(request.getPlugin().getRealm());
        jobInfo.setVersion(request.getPlugin().getVersion());
        jobInfo.setCount(gourd.getCount());
        jobInfo.setRequest(jobRequest);
        if (interval != null) {
            log.info("refreshEcs interval:{}", jobInfo);
            jobInfo.setTriggerTime(System.currentTimeMillis() + interval);
        }
        return jobInfo;
    }

    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("CREATING");//创建中
            add("SHUTTING_DOWN");//关机中
            add("BOOTING");//开机中
            add("REBOOTING");//重启中
            add("REBUILDING");//重装中
            add("STOPPING");//停服中
            add("SUSPENDING");//挂起中
            add("RESUMING");//恢复中
            add("UPGRADING");//调整配置中
            add("CREATING_SYS_SNAPSHOT");//快照创建中
            add("SYS_ROLLING_BACK");//快照回滚中
        }
    };

    public static void addRefreshEcs(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) return;
        Collection<String> instanceIds = getInstanceId(request, response);
        if (instanceIds == null) return;
        //刷新最大次数，刷新频次，首次刷新延迟
        Integer refreshMaxCount = 5, refreshInterval = 5000, firstRefreshLatency = 5000;
        if (request.getAction() == ActionType.CREATE_COMPUTE_INSTANCE) {
            refreshMaxCount = 20;// 创建实例需要增加刷新次数
            firstRefreshLatency = 30000;//创建实例需要增加首次刷新延迟30秒
        }
        if (request.getAction() == ActionType.DELETE_COMPUTE_INSTANCE) {
            firstRefreshLatency = 30000;//删除实例需要增加首次刷新延迟30秒
        }
        for (Object object : instanceIds) {
            addRefreshEcs(request, object.toString(), refreshMaxCount, refreshInterval, firstRefreshLatency);
        }

    }

    private static Collection<String> getInstanceId(BaseCloudRequest request, BaseResponse response) {
        Object instanceIds = null;
        if (request.getBody().getCloud().containsKey("InstanceId")) {
            instanceIds = request.getBody().getCloud().get("InstanceId");
        } else if (request.getBody().getCloud().containsKey("InstanceIds")) {
            instanceIds = request.getBody().getCloud().get("InstanceIds");
        } else if (request.getAction() == ActionType.CREATE_COMPUTE_INSTANCE) {
            if (response instanceof BaseDataResponse) {
                BaseDataResponse dataResponse = (BaseDataResponse) response;
                Object data = dataResponse.getData();
                if (data instanceof RunInstanceResponse) {
                    RunInstanceResponse runInstancesResponse = (RunInstanceResponse) data;
                    if (runInstancesResponse.body != null
                            && CollUtil.isNotEmpty(runInstancesResponse.body.instanceIds)) {
                        instanceIds = runInstancesResponse.body.instanceIds;
                    }
                }
            }
        }
        if (instanceIds == null) return null;
        if (instanceIds instanceof Collection) {
            return (Collection<String>) instanceIds;
        } else if (instanceIds instanceof String) {
            String instanceIdStr = (String) instanceIds;
            if (instanceIdStr.startsWith("[")) {
                return JSON.parseArray(instanceIdStr, String.class);
            } else {
                return StrUtil.split(instanceIdStr, ",");
            }
        }
        return null;
    }

    public static void addRefreshEcs(BaseCloudRequest request, String instanceId, Integer refreshMaxCount, Integer refreshInterval, Integer firstRefreshLatency) {
        JSONObject jobRequest = new JSONObject();
        JSONObject jobBody = new JSONObject();
        JSONObject model = new JSONObject();
        JSONObject cloud = new JSONObject();
        jobRequest.put("action", ActionType.REFRESH_COMPUTE_INSTANCE.value());
        jobRequest.put("body", jobBody);
        jobBody.put("model", model);
        jobBody.put("cloud", cloud);
        jobBody.put("auth", request.getBody().getAuth());
        model.put("instanceId", instanceId);
        model.put("refreshMaxCount", refreshMaxCount);
        model.put("refreshInterval", refreshInterval);
        cloud.put("RegionId", request.getBody().getCloud().getString("RegionId"));
        JobInfo jobInfo = toJobInfo(request, jobRequest, firstRefreshLatency);
        //SpringUtil.getBean(GourdProxy.class).createTempEventJob(jobInfo);
    }

    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeInstances,
                Convert::convertEcs);
        List<CmdbInstanceRes> res = mapE2.v2.get(CmdbInstanceRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
            cmdbInstanceRes.setRes_id(resId);
            List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbInstanceRes cmdbInstanceRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }

    public static BaseResponse refreshBms(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        Entry.E2<JSONObject, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeBms,
                Convert::convertBms);
        List<CmdbBmsRes> res = mapE2.v2.get(CmdbBmsRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), refreshConfig.getString("data"));
            CmdbBmsRes cmdbBmsRes = new CmdbBmsRes();
            cmdbBmsRes.setRes_id(resId);
            List<CmdbBmsRes> data = CollUtil.newArrayList(cmdbBmsRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbBmsRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, mapE2.v2);
            CmdbBmsRes cmdbBmsRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbBmsRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }
}
