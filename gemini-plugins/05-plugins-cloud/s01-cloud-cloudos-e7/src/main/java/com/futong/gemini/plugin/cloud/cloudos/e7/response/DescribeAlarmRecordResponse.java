package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeAlarmRecordResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeAlarmRecordResponseBody body;

    @Data
    public static class DescribeAlarmRecordResponseBody extends BaseResponseBody {
        @NameInMap("status")
        public String status;
        @NameInMap("code")
        public String code;
//        @NameInMap("msg")
//        public String msg;
        @NameInMap("auth")
        public boolean auth;
        @NameInMap("res")
        public DescribeAlarmRecordesponseBodyData res;

    }

    @Data
    public static class DescribeAlarmRecordesponseBodyData extends TeaModel {
        @NameInMap("page")
        public Integer page;
        @NameInMap("size")
        public Integer size;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("totalPages")
        public Integer totalPages;
        @NameInMap("list")
        public List<DescribeAlarmRecordListResponseBodyData> list;
        @NameInMap("records")
        public List<Object> records;
    }

    @Data
    public static class DescribeAlarmRecordListResponseBodyData extends TeaModel {
        @NameInMap("id")
        public String id;
        @NameInMap("userId")
        public String userId;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("alarmRuleId")
        public String alarmRuleId;
        @NameInMap("alarmRuleType")
        public String alarmRuleType;
        @NameInMap("alarmRuleName")
        public String alarmRuleName;
        @NameInMap("alarmJobType")
        public String alarmJobType;
        @NameInMap("alarmJobTypeName")
        public String alarmJobTypeName;
        @NameInMap("alarmRuleDesc")
        public String alarmRuleDesc;
        @NameInMap("startAt")
        public Long startAt;
        @NameInMap("status")
        public String status;
        @NameInMap("alarmTime")
        public Long alarmTime;
        @NameInMap("alarmRuleLevel")
        public String alarmRuleLevel;
        @NameInMap("informResult")
        public Long informResult;
        @NameInMap("convergenceTimes")
        public Long convergenceTimes;
        @NameInMap("objectId")
        public String objectId;
        @NameInMap("tags")
        public String tags;
    }

}
