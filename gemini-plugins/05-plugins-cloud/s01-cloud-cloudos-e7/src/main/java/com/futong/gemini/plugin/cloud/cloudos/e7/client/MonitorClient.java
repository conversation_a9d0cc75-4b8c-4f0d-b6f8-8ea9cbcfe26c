package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.OpenRangeResponse;

import java.util.Map;

public class MonitorClient extends ZiguangClient {

    public MonitorClient(Config config) throws Exception {
        super(config);
    }

    /**
     * 查询区域
     *
     * @throws Exception
     */
    public OpenRangeResponse openRange(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(doRPCRequest("openRange", "2020-07-30", "HTTPS", "GET", "AK", "/monitor", "json", req, runtime,null), new OpenRangeResponse());
    }
}
