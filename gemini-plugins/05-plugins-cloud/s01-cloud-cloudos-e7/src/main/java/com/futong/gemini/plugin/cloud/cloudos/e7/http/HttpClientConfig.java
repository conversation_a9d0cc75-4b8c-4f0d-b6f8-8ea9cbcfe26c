package com.futong.gemini.plugin.cloud.cloudos.e7.http;

import lombok.Getter;
import lombok.Setter;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class HttpClientConfig {
    // 字符集
    private String charset = "UTF-8";
    // 请求头
    private Map<String, String> header = new HashMap<>();
    // 设置连接超时时间，单位毫秒
    private int connectTimeout = 30000;
    private int connectionRequestTimeout = 30000;
    private int socketTimeout = 60000;
    public RequestConfig buildRequestConfig() {
        Builder builder = RequestConfig.custom();
        builder.setConnectTimeout(connectTimeout);
        builder.setConnectionRequestTimeout(connectionRequestTimeout);
        builder.setSocketTimeout(socketTimeout);
        return builder.build();
    }
    public void addHeader(String key, String value) {
        header.put(key, value);
    }
}
