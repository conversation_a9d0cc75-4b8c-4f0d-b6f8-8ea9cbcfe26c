package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeInstancesResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesResponseBody body;
    @Data
    public static class DescribeInstancesResponseBody extends BaseResponseBody{
        @NameInMap("list")
        public List<DescribeInstancesResponseBodyData> list;
        @NameInMap("page")
        public Integer page;
        @NameInMap("size")
        public Integer pageSize;
        @NameInMap("RequestId")
        public String requestId;
        @NameInMap("totalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeInstancesResponseBodyData extends TeaModel{
        @NameInMap("instanceId")
        public String instanceId;
        @NameInMap("instanceName")
        public String instanceName;
        @NameInMap("sysDiskSize")
        public Integer sysDiskSize;
        @NameInMap("sysDiskCode")
        public String sysDiskCode;
        @NameInMap("status")
        public String status;
        @NameInMap("imageId")
        public String imageId;
        @NameInMap("imageType")
        public String imageType;
        @NameInMap("imageCode")
        public String imageCode;
        @NameInMap("imageParentCode")
        public String imageParentCode;
        @NameInMap("instanceSystem")
        public String instanceSystem;
        @NameInMap("description")
        public String description;
        @NameInMap("ip")
        public String ip;
        @NameInMap("eipId")
        public String eipId;
        @NameInMap("eipAddr")
        public String eipAddr;
        @NameInMap("eipSize")
        public Integer eipSize;
        @NameInMap("eipName")
        public String eipName;
        @NameInMap("eipCode")
        public String eipCode;
        @NameInMap("instanceCode")
        public String instanceCode;
        @NameInMap("instanceCodeName")
        public String instanceCodeName;
        @NameInMap("payType")
        public String payType;
        @NameInMap("startTime")
        public String startTime;
        @NameInMap("endTime")
        public String endTime;
        @NameInMap("bindDiskCount")
        public Integer bindDiskCount;
        @NameInMap("maxDisk")
        public Integer maxDisk;
        @NameInMap("eniId")
        public String eniId;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("regionName")
        public String regionName;
        @NameInMap("secondaryEni")
        public List<String> secondaryEni;
        @NameInMap("azoneId")
        public String azoneId;
        @NameInMap("vpcId")
        public String vpcId;
        @NameInMap("vpcCidr")
        public String vpcCidr;
        @NameInMap("subnetId")
        public String subnetId;
        @NameInMap("subnetCidr")
        public String subnetCidr;
        @NameInMap("projectId")
        public String projectId;
        @NameInMap("instanceCpu")
        public Integer instanceCpu;
        @NameInMap("instanceMemory")
        public Integer instanceMemory;
        @NameInMap("sgIds")
        public List<String> sgIds;
    }

}
