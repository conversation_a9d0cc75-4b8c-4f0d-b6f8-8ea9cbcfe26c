package com.futong.gemini.plugin.cloud.cloudos.e7.response;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
@Data
public class DescribeDBInstanceAttributeResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeDBInstanceAttributeResponseBody body;
    @Data
    public static class DescribeDBInstanceAttributeResponseBody extends BaseResponseBody{
        @NameInMap("DBInstanceId")
        public String instanceId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("ZoneId")
        public String zoneId;
        @NameInMap("Engine")
        public String engine;
        @NameInMap("Category")
        public String category;
        @NameInMap("InstanceNetworkType")
        public String instanceNetworkType;
        @NameInMap("DBVersionType")
        public String engineVersion;
        @NameInMap("MajorVersion")
        public String majorVersion;
        @NameInMap("Status")
        public String status;
        @NameInMap("CreatedAt")
        public String createTime;

        @NameInMap("DBInstanceStorage")
        public String dbInstanceStorage;
        @NameInMap("VpcId")
        public String vpcId;
        @NameInMap("VpcName")
        public String vpcName;
        @NameInMap("SubnetId")
        public String subnetId;
        @NameInMap("Nodes")
        public List<DescribeDBInstanceAttributeResponseBodyData> list;
    }

    @Data
    public static class DescribeDBInstanceAttributeResponseBodyData extends TeaModel {
        @NameInMap("NodeId")
        public String nodeId;
        @NameInMap("NodeStatus")
        public String nodeStatus;
        @NameInMap("Cpu")
        public Integer cpu;
        @NameInMap("Memory")
        public Integer memory;
        @NameInMap("InstanceStorage")
        public Integer instanceStorage;
        @NameInMap("InstanceStorageType")
        public String instanceStorageType;
    }
}
