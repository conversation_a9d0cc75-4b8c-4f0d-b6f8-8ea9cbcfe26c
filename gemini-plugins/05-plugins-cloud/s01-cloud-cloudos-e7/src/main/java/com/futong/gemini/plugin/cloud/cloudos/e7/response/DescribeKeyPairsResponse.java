package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeKeyPairsResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeKeyPairsResponseBody body;
    @Data
    public static class DescribeKeyPairsResponseBody extends BaseResponseBody{
        @NameInMap("list")
        public List<DescribeKeyPairsResponseBodyData> list;
        @NameInMap("page")
        public Integer page;
        @NameInMap("size")
        public Integer pageSize;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("totalPages")
        public Integer totalPages;


    }
    @Data
    public static class DescribeKeyPairsResponseBodyData extends TeaModel{
        @NameInMap("instanceId")
        public String instanceId;
        @NameInMap("name")
        public String instanceName;
        @NameInMap("fingerPrint")
        public String fingerPrint;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("status")
        public String status;
        @NameInMap("publicKey")
        public String publicKey;
        @NameInMap("createTime")
        public Long createTime;

    }

}
