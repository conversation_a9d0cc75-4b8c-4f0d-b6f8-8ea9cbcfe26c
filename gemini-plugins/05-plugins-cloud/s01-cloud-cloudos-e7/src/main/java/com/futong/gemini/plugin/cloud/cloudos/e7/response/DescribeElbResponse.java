package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeElbResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeElbResponseBody body;

    @Data
    public static class DescribeElbResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeElbResponseBodyData res;

    }

    @Data
    public static class DescribeElbResponseBodyData extends TeaModel {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Integer total;
        @NameInMap("Data")
        public List<DescribeElbResponseBodyDataAz> data;
    }

    @Data
    public static class DescribeElbResponseBodyDataAz extends TeaModel {
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("VpcId")
        public String vpcId;
        @NameInMap("ZoneId")
        public String ZoneId;
        @NameInMap("SubnetId")
        public String subnetId;
        @NameInMap("Address")
        public String address;
        @NameInMap("Status")
        public String status;
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("Deleted")
        public boolean deleted;
        @NameInMap("PortId")
        public String portId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("InstanceCode")
        public String instanceCode;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("InstanceOperationInfo")
        public InstanceOperationInfo instanceOperationInfo;
        @NameInMap("InstanceOperationType")
        public InstanceOperationType instanceOperationType;
    }

    public static class InstanceOperationInfo extends TeaModel {
        @NameInMap("AzId")
        public String azId;
        @NameInMap("IsPay")
        public Integer isPay;
        @NameInMap("Id")
        public String id;
        @NameInMap("RegionName")
        public String regionName;
        @NameInMap("ProductType")
        public String productType;
        @NameInMap("ChargeType")
        public Integer chargeType;
        @NameInMap("OrderId")
        public Integer orderId;
        @NameInMap("RentUnit")
        public String rentUnit;
        @NameInMap("OperationStatus")
        public Integer operationStatus;
        @NameInMap("PayType")
        public String payType;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("IsPayClose")
        public boolean isPayClose;
        @NameInMap("InstanceEndTime")
        public String instanceEndTime;
        @NameInMap("InstanceStartTime")
        public String instanceStartTime;
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("RenewType")
        public String renewType;
        @NameInMap("UpdateTime")
        public String updateTime;
        @NameInMap("ProductCode")
        public String productCode;
        @NameInMap("FormalPayTime")
        public String formalPayTime;
        @NameInMap("InstanceLabel")
        public Integer instanceLabel;
        @NameInMap("RentCount")
        public Integer rentCount;
        @NameInMap("IsOld")
        public Integer isOld;
        @NameInMap("UserName")
        public String userName;
        @NameInMap("DueTime")
        public String dueTime;
        @NameInMap("CreateTime")
        public String createTime;
        @NameInMap("UserId")
        public String userId;
    }

    public static class InstanceOperationType extends TeaModel {
        @NameInMap("Describe")
        public String describe;
        @NameInMap("SpecificationCode")
        public String specificationCode;
        @NameInMap("MaximumConnection")
        public String maximumConnection;
        @NameInMap("ComponentCode")
        public String componentCode;
        @NameInMap("CPS")
        public String cps;
        @NameInMap("SpecificationClassCode")
        public String specificationClassCode;
        @NameInMap("ComponentProperty")
        public InstanceOperationTypeAz componentProperty;
        @NameInMap("QPS")
        public String qps;
        @NameInMap("MonitorNum")
        public String monitorNum;
        @NameInMap("ComponentName")
        public String componentName;
        @NameInMap("SpecificationName")
        public String specificationName;
    }

    public static class InstanceOperationTypeAz extends TeaModel {
        @NameInMap("SpecificationCode")
        public String specificationCode;
    }

}
