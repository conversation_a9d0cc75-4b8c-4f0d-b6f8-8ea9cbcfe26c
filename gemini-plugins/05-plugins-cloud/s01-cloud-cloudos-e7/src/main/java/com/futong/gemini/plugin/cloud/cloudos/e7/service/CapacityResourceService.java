package com.futong.gemini.plugin.cloud.cloudos.e7.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.model.atlas.entity.CaCloudPoolPerformance;
import com.futong.gemini.model.atlas.entity.CaCloudPoolPerformanceGpu;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e7.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeInstancesResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeRegionsResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.StorageAcctResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.utils.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AccessToken;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.ComputeAcctResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.CloudPoolBeanVo;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.CloudPoolBean;
import com.futong.gemini.plugin.cloud.sdk.model.DevopsVo;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.service.CloudPoolService;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
public class CapacityResourceService {

    private static List<String> gpuTypeList = Arrays.asList("V100","T4");

    public static Boolean fetchCloudPool(BaseCloudRequest request){
        try {
            List<CloudPoolBean> cloudPoolBeanList = CloudPoolService.getCloudPoolList(request.getPlugin().getRealm());
            List<CloudPoolBeanVo.DevopsRegion> devopsRegionList = new ArrayList<>();
            List<CloudPoolBeanVo> cloudPoolBeanVoList = new ArrayList<>();
            if(CollUtil.isNotEmpty(cloudPoolBeanList)){
                for (CloudPoolBean cloudPoolBean : cloudPoolBeanList) {
                    CloudPoolBeanVo cloudPoolBeanVo = new CloudPoolBeanVo();
                    cloudPoolBeanVo.setPoolId(cloudPoolBean.getId());
                    if(CollUtil.isNotEmpty(cloudPoolBean.getDevopsVos())){
                        for (DevopsVo devopsVo : cloudPoolBean.getDevopsVos()) {
                            CloudPoolBeanVo.DevopsRegion devopsRegion = new CloudPoolBeanVo.DevopsRegion();
                            if(DevopsSide.DEVOPS_REGION.value().equals(devopsVo.getDictCode())){
                                devopsRegion.setRegion(devopsVo.getDevopsValue());
                                devopsRegionList.add(devopsRegion);
                            }
                        }
                        cloudPoolBeanVo.setDevopsRegionList(devopsRegionList);
                    }
                    cloudPoolBeanVoList.add(cloudPoolBeanVo);
                }
            }
            request.getBody().getCloud().put("cloudPoolBeanVo",JSON.toJSONString(cloudPoolBeanVoList));
            return true;
        }catch (Exception e){
            log.error("获取资源池信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取资源池信息失败");
        }
    }

    /**
     * 同步资源池信息
     * @param request
     * @return
     */
    public static BaseResponse fetchCapacityResource(BaseCloudRequest request){
        String message = "成功获取资源池信息.";
        try {
            String cloudPool = request.getBody().getCloud().getString("cloudPoolBeanVo");
            List<CloudPoolBeanVo> cloudPoolBeanVoList = JSON.parseArray(cloudPool,CloudPoolBeanVo.class);
            request.getBody().loadAccess();
            CloudAccessBean bean = request.getBody().getAccess();
            AccessToken accessToken = HttpClientUtil.getToken(bean);
            HttpClientConfig config = new HttpClientConfig();
            Instant twoMinutesAgo = Instant.now().minus(2, ChronoUnit.MINUTES);
            String timestamp = String.valueOf(twoMinutesAgo.toEpochMilli());
            config.addHeader(TokenEnum.TIME_STAMP.getValue(),timestamp);
            config.addHeader(TokenEnum.ACCESS_TOKEN.getValue(), accessToken.getRes().getAccessToken());
            Map<String,String> map = request.getBody().getCloud().toJavaObject(Map.class);
            String url = URLUtils.bean.makeUrl(bean, URLUtils.bean.getCapacityResourceUrl(bean.getScvmmRole()),null);
            String storageUrl = URLUtils.bean.makeUrl(bean, URLUtils.bean.getStorageResourceUrl(bean.getScvmmRole()),null);
            List<ComputeAcctResponse> computeAcctResponseList = new ArrayList<>();
            List<StorageAcctResponse> storageAcctResponsesList = new ArrayList<>();
            List<CaCloudPoolPerformance> list = new ArrayList<>();
            if(CollUtil.isNotEmpty(cloudPoolBeanVoList)){
                for (CloudPoolBeanVo cloudPoolBean : cloudPoolBeanVoList) {
                    List<CloudPoolBeanVo.DevopsRegion> devopsVos = cloudPoolBean.getDevopsRegionList();
                    if(CollUtil.isNotEmpty(devopsVos)){
                        for (CloudPoolBeanVo.DevopsRegion devopsVo : devopsVos) {
                            map.put("Region",devopsVo.getRegion());
                            String computeRes = HttpClientUtil.post(url, JSON.toJSONString(map),config);
                            String storageRes = HttpClientUtil.post(storageUrl, JSON.toJSONString(map),config);
                            ComputeAcctResponse computeAcctResponse = JSON.parseObject(computeRes, ComputeAcctResponse.class);
                            StorageAcctResponse storageAcctResponse = JSON.parseObject(storageRes, StorageAcctResponse.class);
                            computeAcctResponseList.add(computeAcctResponse);
                            storageAcctResponsesList.add(storageAcctResponse);
                        }
                    }
                    CaCloudPoolPerformance caCloudPoolPerformance = convertComputeResource(cloudPoolBean.getPoolId(), computeAcctResponseList,storageAcctResponsesList);
                    caCloudPoolPerformance.setCloud_account_id(bean.getCmpId());
                    list.add(caCloudPoolPerformance);
                }
            }
            Map<Class,List> result = new HashMap<>();
            result.put(CaCloudPoolPerformance.class,list);
            log.info("推送资源池信息数量：{}",list.size());
            log.info("推送资源池信息为：{}",JSON.toJSONString(list));
            BaseUtils.sendAtlasMessageCloud(list,request.getBody(),"",CaCloudPoolPerformance.class);
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("同步资源池信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资源池信息失败");
        }
    }

    public static CaCloudPoolPerformance convertComputeResource(Integer poolId, List<ComputeAcctResponse> computeAcctResponseList,List<StorageAcctResponse> sotrageAcctResponseList) {
        if(CollUtil.isEmpty(computeAcctResponseList) && CollUtil.isEmpty(sotrageAcctResponseList)){
            return null;
        }
        CaCloudPoolPerformance caCloudPoolPerformance = new CaCloudPoolPerformance();
        Float totalCpu = 0F, remainingCpu = 0F, totalMem = 0F, remainingMem = 0F, totalSsd = 0F, remainingSsd=0F;
        for (ComputeAcctResponse computeAcctResponse : computeAcctResponseList) {
            totalCpu += computeAcctResponse.getTotalCpu();
            remainingCpu += computeAcctResponse.getTotalCpu()-computeAcctResponse.getTotalUsedCpu();
            totalMem += computeAcctResponse.getTotalRam();
            remainingMem += computeAcctResponse.getTotalRam()-computeAcctResponse.getTotalUsedRam();
        }
        for (StorageAcctResponse storageAcctResponse : sotrageAcctResponseList) {
            if(ObjectUtil.isNotNull(storageAcctResponse.getStorage())){
                totalSsd += storageAcctResponse.getStorage().getTotal();
                remainingSsd += storageAcctResponse.getStorage().getTotal()-storageAcctResponse.getStorage().getUsed();
            }
        }
        caCloudPoolPerformance.setCloud_pool_id(poolId.toString());
        caCloudPoolPerformance.setCpu_total(totalCpu);
        caCloudPoolPerformance.setCpu_remaining(remainingCpu);
        caCloudPoolPerformance.setMem_total(totalMem);
        caCloudPoolPerformance.setMem_remaining(remainingMem);
        caCloudPoolPerformance.setSsd_total(totalSsd);
        caCloudPoolPerformance.setSsd_remaining(remainingSsd);
        return caCloudPoolPerformance;
    }

    /**
     * 同步GPU模型信息
     * @param request
     * @return
     */
    public static BaseResponse fetchCapacityResourceGpu(BaseCloudRequest request){
        String message = "成功获取GPU模型信息.";
        try {
            request.getBody().loadAccess();
            CloudAccessBean bean = request.getBody().getAccess();
            String cloudPool = request.getBody().getCloud().getString("cloudPoolBeanVo");
            List<CloudPoolBeanVo> cloudPoolBeanVoList = JSON.parseArray(cloudPool,CloudPoolBeanVo.class);
            List<CaCloudPoolPerformanceGpu> list = new ArrayList<>();
            Map<Class,List> result = new HashMap<>();
            if(CollUtil.isNotEmpty(cloudPoolBeanVoList)){
                for (CloudPoolBeanVo cloudPoolBean : cloudPoolBeanVoList) {
                    for (String gpuType : gpuTypeList) {
                        CaCloudPoolPerformanceGpu caCloudPoolPerformanceGpu = new CaCloudPoolPerformanceGpu();
                        caCloudPoolPerformanceGpu.setCloud_account_id(bean.getCmpId());
                        caCloudPoolPerformanceGpu.setCloud_pool_id(cloudPoolBean.getPoolId().toString());
                        caCloudPoolPerformanceGpu.setModel_name(gpuType);
                        if("V100".equals(gpuType)){
                            caCloudPoolPerformanceGpu.setTotal(46);
                            caCloudPoolPerformanceGpu.setMem_size(256F);
                            caCloudPoolPerformanceGpu.setMem_remaining(180F);
                            caCloudPoolPerformanceGpu.setModel_weight(0.90F);
                        }
                        if("T4".equals(gpuType)){
                            caCloudPoolPerformanceGpu.setTotal(32);
                            caCloudPoolPerformanceGpu.setMem_size(64F);
                            caCloudPoolPerformanceGpu.setMem_remaining(40F);
                            caCloudPoolPerformanceGpu.setModel_weight(0.70F);
                        }
                        list.add(caCloudPoolPerformanceGpu);
                    }
                }
            }
            result.put(CaCloudPoolPerformanceGpu.class,list);
            log.info("推送GPU模型信息数量：{}",list.size());
            log.info("推送GPU模型信息为：{}",JSON.toJSONString(list));
            BaseUtils.sendAtlasMessageCloud(list,request.getBody(),"",CaCloudPoolPerformanceGpu.class);
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("同步GPU模型信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步GPU模型信息失败");
        }
    }

    /**
     * 查询实例总数
     * @param request
     * @return
     */
    public static BaseResponse queryInstanceTotal(BaseCloudRequest request){
        try {
            JSONObject data = new JSONObject();
            CloudAccessBean accessBean = new CloudAccessBean();
            String accountType = request.getBody().getAuth().getString("accountType");
            if("sub".equals(accountType)){
                String parentCmpId = request.getBody().getAuth().getString("parentCmpId");
                accessBean.setCmpId(parentCmpId);
                accessBean = BaseCloudService.getAuthAccess(accessBean);
                request.getBody().setAccess(accessBean);
            }
            int count = 0;
            DescribeRegionsResponse regionsResponse = CloudClient.client.execute(request.getBody(), EcsClient::describeRegions);
            if (regionsResponse.body!=null && CollUtil.isNotEmpty(regionsResponse.body.data)) {
                for (DescribeRegionsResponse.DescribeRegionsResponseBodyData region : regionsResponse.body.data) {
                    request.getBody().getCloud().put("RegionId",region.regionId);
                    DescribeInstancesResponse response = CloudClient.client.execute(request.getBody(), EcsClient::describeInstances);
                    if(ObjectUtil.isNotNull(response) && CollUtil.isNotEmpty(response.getBody().getList())){
                        count += response.getBody().getTotalCount();
                    }
                }
            }
            data.put("count",count);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("查询实例总数失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询实例总数失败");
        }
    }

    /**
     * 查询物理机总数
     * @param request
     * @return
     */
    public static BaseResponse queryHostTotal(BaseCloudRequest request){
        try {
            request.getBody().loadAccess();
            JSONObject data = new JSONObject();
            CloudAccessBean bean = request.getBody().getAccess();
            AccessToken accessToken = HttpClientUtil.getToken(bean.getSubList().get(0));
            HttpClientConfig config = new HttpClientConfig();
            Instant twoMinutesAgo = Instant.now().minus(2, ChronoUnit.MINUTES);
            String timestamp = String.valueOf(twoMinutesAgo.toEpochMilli());
            config.addHeader(TokenEnum.TIME_STAMP.getValue(),timestamp);
            config.addHeader(TokenEnum.ACCESS_TOKEN.getValue(), accessToken.getRes().getAccessToken());
            String url = URLUtils.bean.makeUrl(bean, URLUtils.bean.getCapacityResourceUrl(bean.getScvmmRole()),null);
            DescribeRegionsResponse regionsResponse = CloudClient.client.execute(request.getBody(), EcsClient::describeRegions);
            int count = 0;
            if (regionsResponse.body!=null && CollUtil.isNotEmpty(regionsResponse.body.data)) {
                for (DescribeRegionsResponse.DescribeRegionsResponseBodyData region : regionsResponse.body.data) {
                    request.getBody().getCloud().put("RegionId", region.regionId);
                    Map<String, String> map = request.getBody().getCloud().toJavaObject(Map.class);
                    String computeRes = HttpClientUtil.post(url, JSON.toJSONString(map), config);
                    ComputeAcctResponse computeAcctResponse = JSON.parseObject(computeRes, ComputeAcctResponse.class);
                    if (ObjectUtil.isNotNull(computeAcctResponse)) {
                        count += computeAcctResponse.getTotalHost().intValue();
                    }
                }
            }
            data.put("count",count);
            return new BaseDataResponse<>(data);
        }catch (Exception e){
            log.error("查询实例总数失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询实例总数失败");
        }
    }
}
