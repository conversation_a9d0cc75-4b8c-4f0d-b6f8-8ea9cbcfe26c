package com.futong.gemini.plugin.cloud.cloudos.e7.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.SourceOperationClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeInstanceTypesResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeInstanceVncUrlResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.ResetEcsPasswordResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ComputeInstanceService {

    /**
     * 获取磁盘类型
     *
     * @param request
     * @return
     */
    public static BaseResponse queryDiskStandards(BaseCloudRequest request) {
        try {
            request.getBody().getCloud().put("ProductCode", "VM");
            request.getBody().getCloud().put("PaymentMethodCode", "DAY_MONTH");
            request.getBody().getCloud().put("ComponentCode", "SystemDisk");
            DescribeInstanceTypesResponse result = CloudClient.client.execute(request.getBody(), EcsClient::describeInstanceTypes);
            List<DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfos> diskResource = Opt.ofNullable(result.getBody())
                    .map(t -> t.getData()).map(t -> t.get(0))
                    .map(t -> t.getSpecificationInfos()).orElse(null);
            return new BaseDataResponse<>(diskResource);
        } catch (Exception e) {
            log.error("获取磁盘类型失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取磁盘类型失败");
        }
    }

    /**
     * 获取云主机的VNC远程登录地址
     *
     * @param request
     * @return
     */
    public static BaseResponse describeInstanceVncUrl(BaseCloudRequest request) {
        try {
            DescribeInstanceVncUrlResponse result = CloudClient.client.execute(request.getBody(), EcsClient::describeInstanceVncUrl);
            JSONObject response = new JSONObject();
            if (result.getBody() != null && !StrUtil.isEmpty(result.getBody().getWssAddress())) {
                String replaceDomain = StrUtil.replace(result.getBody().getWssAddress(), "{domain}", request.getBody().getAccess().getServerIp() + ":12011");
                response.put("protocol", result.getBody().getWssAddress().startsWith("wss") ? "wss" : "ws");
                response.put("wssAddress", "/websock/" + replaceDomain);
                return new BaseDataResponse<>(response);
            } else {
                throw new BaseException(BaseResponse.FAIL, "获取云上vnc地址信息为空!");
            }
        } catch (Exception e) {
            log.error("获取云主机的VNC远程登录地址失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机的VNC远程登录地址失败");
        }
    }

    public static BaseResponse queryDiskType(BaseCloudRequest request) {
        request.getBody().getCloud().put("Category", "spec");
        request.getBody().getCloud().put("Code", "DATA_DISK");
        Map<String, ?> result = CloudClient.client.execute(request.getBody(), EcsClient::queryDiskType);
        return new BaseDataResponse<>(result);
    }

    /**
     * 查询云主机密码
     *
     * @param request
     * @return
     */
    public static BaseResponse getEcsPassword(BaseCloudRequest request) {
        try {
            String password = "";
            Map<String, ?> map = CloudClient.client.execute(request.getBody(), EcsClient::getEcsPassword);
            if (CollUtil.isNotEmpty(map)) {
                String json = JSON.toJSONString(map);
                password = JSON.parseObject(json).getJSONObject("body").getString("password");
            }
            return new BaseDataResponse<>(password);
        } catch (Exception e) {
            log.error("查询云主机密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询云主机密码失败");
        }
    }

    /**
     * 更新云主机密码
     *
     * @param request
     * @return
     */
    public static BaseResponse resetEcsPassword(BaseCloudRequest request) {
        try {
            ResetEcsPasswordResponse result = CloudClient.client.execute(request.getBody(), SourceOperationClient::resetEcsPassword);
            return new BaseDataResponse<>(result.getBody());
        } catch (Exception e) {
            log.error("更新云主机密码失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "更新云主机密码失败");
        }
    }
}
