package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeSecurityGroupRuleResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeSecurityGroupRuleResponseBody body;

    @Data
    public static class DescribeSecurityGroupRuleResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeSecurityGroupRuleResponseBodyData res;
    }

    @Data
    public static class DescribeSecurityGroupRuleResponseBodyData extends TeaModel {
        @NameInMap("SgRules")
        public List<SgRulesData> sgRules;
        @NameInMap("TotalCount")
        public Long totalCount;
        @NameInMap("Page")
        public Long page;
        @NameInMap("Size")
        public Long size;
    }

    @Data
    public static class SgRulesData extends TeaModel {
        @NameInMap("PortRangeMin")
        public Integer portRangeMin;
        @NameInMap("RemoteIpPrefix")
        public String RemoteIpPrefix;
        @NameInMap("Id")
        public String id;
        @NameInMap("RemoteGroupId")
        public String remoteGroupId;
        @NameInMap("Direction")
        public String direction;
        @NameInMap("IsDeny")
        public Integer isDeny;
        @NameInMap("SecurityGroupId")
        public String securityGroupId;
        @NameInMap("Protocol")
        public String protocol;
        @NameInMap("Priority")
        public Integer priority;
        @NameInMap("EtherType")
        public String etherType;
        @NameInMap("PortRangeMax")
        public Integer portRangeMax;
        @NameInMap("PortRanges")
        public String portRanges;
    }
}
