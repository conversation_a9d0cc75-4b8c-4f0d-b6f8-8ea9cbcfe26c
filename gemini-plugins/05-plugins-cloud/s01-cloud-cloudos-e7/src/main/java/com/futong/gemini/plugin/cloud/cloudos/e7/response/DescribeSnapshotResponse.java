package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeSnapshotResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeSnapshotResponseBody body;

    @Data
    public static class DescribeSnapshotResponseBody extends BaseResponseBody {
        @NameInMap("pageNumber")
        public Integer pageNumber;
        @NameInMap("pageSize")
        public Integer pageSize;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("snapshots")
        public List<DescribeSnapshotResponseBodyData> snapshots;

    }

    @Data
    public static class DescribeSnapshotResponseBodyData extends TeaModel {
        @NameInMap("snapshotId")
        public String snapshotId;
        @NameInMap("sourceDiskId")
        public String sourceDiskId;
        @NameInMap("sourceDiskInstanceCode")
        public String sourceDiskInstanceCode;
        @NameInMap("creationTime")
        public String creationTime;
        @NameInMap("snapshotType")
        public Integer snapshotType;
        @NameInMap("sourceDiskName")
        public String sourceDiskName;
        @NameInMap("description")
        public String description;
        @NameInMap("expireAt")
        public String expireAt;
        @NameInMap("userId")
        public String userId;
        @NameInMap("sourceDiskType")
        public String sourceDiskType;
        @NameInMap("snapshotName")
        public String snapshotName;
        @NameInMap("sourceDiskSize")
        public String sourceDiskSize;
        @NameInMap("status")
        public String status;
//        @NameInMap("dataImageInfo")
//        public List<DescribeImageResponseBodyDataAz> dataImageInfo;
    }

    @Data
    public static class DescribeImageResponseBodyDataAz extends TeaModel {
        @NameInMap("ImageId")
        public String imageId;
        @NameInMap("TemplateId")
        public String templateId;
        @NameInMap("Size")
        public Integer size;
    }

}
