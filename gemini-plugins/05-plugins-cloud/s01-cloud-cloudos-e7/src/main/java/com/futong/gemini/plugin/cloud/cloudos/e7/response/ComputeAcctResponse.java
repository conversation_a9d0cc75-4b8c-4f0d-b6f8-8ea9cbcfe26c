package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import lombok.Data;


@Data
public class ComputeAcctResponse {

    /**
     * 裸金属总数量
     */
    private Float BareMetalTotal;
    /**
     * 裸金属使用数量
     */
    private Float BareMetalUsed;
    /**
     * 物理CPU总量
     */
    private Float PhysicsTotalCpu;
    /**
     * CPU分配率
     */
    private double QuotedCpuRate;
    /**
     * 内存分配率
     */
    private double QuotedRamRate;
    /**
     * 服务器可用数量
     */
    private int TotalAvailableHost;
    /**
     * 超分CPU总量
     */
    private Float TotalCpu;
    /**
     * 服务器总数量
     */
    private Float TotalHost;
    /**
     * 实例总数量
     */
    private Float TotalInstance;
    /**
     * 实例异常数量
     */
    private Float TotalInstanceAbnormal;
    /**
     * 实例停止数量
     */
    private Float TotalInstanceDown;
    /**
     * 实例正常数量
     */
    private Float TotalInstanceUp;
    /**
     * 服务器维护数量
     */
    private Float TotalMaintainingHost;
    /**
     * 内存总量
     */
    private Float TotalRam;
    /**
     * 超分CPU分配量
     */
    private Float TotalUsedCpu;
    /**
     * 内存分配量
     */
    private Float TotalUsedRam;

}
