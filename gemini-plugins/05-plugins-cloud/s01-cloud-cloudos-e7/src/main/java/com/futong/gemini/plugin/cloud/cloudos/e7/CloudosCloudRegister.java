package com.futong.gemini.plugin.cloud.cloudos.e7;

import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.SourceOperationClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.sampler.FetchService;
import com.futong.gemini.plugin.cloud.cloudos.e7.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.cloudos.e7.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class CloudosCloudRegister extends BaseCloudRegister {

//    public <Q, R, C> void register(ActionType actionType, FTExecute<Q, R, C> execute) {
//        register(actionType, execute, CloudService::toFTAction);
//    }

    public <Q, R, C> Builder registerCloudOs(ActionType actionType, FTExecute<Q, R, C> execute) {
        return register(actionType, execute, CloudService::toFTAction);
    }

    @Override
    public void load() {
        //加载云平台操作
        onAfterLoadPlatform();
        //加载同步调度信息
        onAfterLoadFetch();
        //加载云主机操作
        onAfterLoadCompute();
        //加载镜像操作
        onAfterLoadComputeImage();
        //加载云主机安全组操作
        onAfterLoadComputeSecurityGroup();
        //加载云主机密钥对操作
        onAfterLoadComputeKeypair();
        //加载存储云硬盘操作
        onAfterLoadStorageDisk();
        //加载存储云硬盘快照操作
        onAfterLoadStorageSnapshot();
        //加载网络VPC操作
        onAfterLoadNeutronVpc();
        //加载网络子网操作
        onAfterLoadNeutronSubnet();
        //加载网络弹性IP操作
        onAfterLoadNeutronEip();
        //加载网络NAT网关操作
        onAfterLoadNeutronNat();
        //加载网络路由表操作
        onAfterLoadNeutronRoute();
        //加载裸金属操作
        onAfterLoadBms();
    }

    public void onAfterLoadPlatform() {
        //认证云账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authCloudAccount);
        //获取云账号表单信息
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);
        //获取调度添加模型
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);
        //添加默认调度任务
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);
    }

    public void onAfterLoadFetch() {
        //默认查询页码及条数50
        registerBefore(CloudService::defaultPage50,
                ActionType.FETCH_STORAGE_IMAGE,
                ActionType.FETCH_COMPUTE_SECURITYGROUP,
                ActionType.FETCH_NEUTRON_ROUTE,
                ActionType.FETCH_NEUTRON_NAT,
                ActionType.FETCH_NEUTRON_DNAT_ENTRY,
                ActionType.FETCH_NEUTRON_SNAT_ENTRY,
                ActionType.FETCH_PLATFORM_KEYPAIR,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_STORAGE_SNAPSHOT,
                ActionType.FETCH_NEUTRON_EIP,
                ActionType.FETCH_NEUTRON_LOADBALANCE,
                ActionType.FETCH_NEUTRON_NIC,
                ActionType.FETCH_COMPUTE_BMS
        );
        registerBefore(CloudService::defaultPage20,
                ActionType.FETCH_COMPUTE_INSTANCE
        );
        //同步地域
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion);
        //获取镜像
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage);
        //获取安全组
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup);
        //获取VPC
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc);
        //获取Subnet
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);
        //获取网卡
        register(ActionType.FETCH_NEUTRON_NIC, FetchService::fetchNetcard);
        //获取路由表
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRoute);
        //获取nat网关
        register(ActionType.FETCH_NEUTRON_NAT, FetchService::fetchNat);
        //获取Dnats
        register(ActionType.FETCH_NEUTRON_DNAT_ENTRY, FetchService::fetchDnatEntry);
        //获取Snats
        register(ActionType.FETCH_NEUTRON_SNAT_ENTRY, FetchService::fetchSnatEntry);
        //获取密钥对
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeyPair);
        //获取云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);
        //获取规格
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor);
        //获取云主机监控,预处理
        registerBefore(ActionType.FETCH_COMPUTE_INSTANCE_PERF,
                FetchService::defaultMetricRequest,
                FetchService::defaultEcsMetricNames,
                FetchService::defaultEcsMetricDimensions);
        //获取云主机监控
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsPerf);
        //获取磁盘
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk);
        //获取磁盘快照
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot);
        //获取EIP
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip);
        //获取负载均衡
        register(ActionType.FETCH_NEUTRON_LOADBALANCE, FetchService::fetchLoadBalancer);
        //获取对象存储
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchBucket);
        //获取对象存储文件
        register(ActionType.FETCH_STORAGE_BUCKET_FILE, FetchService::fetchBucketFile);
        //默认查询时间区间一天
        registerBefore(CloudService::defaultStartEndTimeOneDay,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT);
        registerBefore(CloudService::defaultPage50Low,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT
        );
        //获取告警信息
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);
        //获取事件
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent);
        //获取事件
        registerBefore(CapacityResourceService::fetchCloudPool,
                ActionType.FETCH_COMPUTE_CAPACITY_RESOURCE,
                ActionType.FETCH_COMPUTE_CAPACITY_RESOURCE_GPU);
        register(ActionType.FETCH_COMPUTE_CAPACITY_RESOURCE, CapacityResourceService::fetchCapacityResource);
        register(ActionType.FETCH_COMPUTE_CAPACITY_RESOURCE_GPU, CapacityResourceService::fetchCapacityResourceGpu);
        //同步云主机数量
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL, CapacityResourceService::queryInstanceTotal);
        //同步物理机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL, CapacityResourceService::queryHostTotal);
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost).addBefore(CloudService::defaultPage100Low);//同步主机
        register(ActionType.FETCH_COMPUTE_HOST_PERF, FetchService::fetchHostPerf).addBefore(CloudService::defaultPage100Low);//同步主机性能
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL, CapacityResourceService::queryHostTotal);
        //获取裸金属
        register(ActionType.FETCH_COMPUTE_BMS, FetchService::fetchBms);
        //同步裸金属性能
        register(ActionType.FETCH_COMPUTE_BMS_PERF, FetchService::fetchBmsPerf)
                .addBefore(CloudService::defaultPage50)
                .addBefore(CloudService::defaultStartEndTime);

    }

    public void onAfterLoadCompute() {
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.InstanceId", BaseUtils::formatSingle);
        //批量创建云主机
        registerCloudOs(ActionType.CREATE_COMPUTE_INSTANCE, SourceOperationClient::runInstance)
                .addBefore(CloudService::toBeforeBiz)
                .addAfter(CloudService::toAfterBizResId)
                //设置刷新请求，指定请求Action
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                //设置刷新总次数,刷新频次，首次刷新延迟
                .addSetRefreshConfig(20, 5000, 20000)
                //批量创建基于响应分割刷新任务
                .addSetRefreshSplitData("response","$.data.body.instanceIds")
                //基于请求信息设置刷新请求的cloud信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.RegionId")
                //添加刷新任务
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改云主机
        registerCloudOs(ActionType.UPDATE_COMPUTE_INSTANCE, SourceOperationClient::updateEcsName)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId")
                .addTransferCloud("$.model.resourceName", "$.InstanceName", true)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                //单资源操作,直接设置刷新请求的data信息
                .addSetRefreshData("request","$.body.cloud.InstanceId")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量删除云主机
        registerCloudOs(ActionType.DELETE_COMPUTE_INSTANCE, SourceOperationClient::deleteInstance)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                //批量操作基于请求分割刷新任务
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                //基于请求设置刷新请求的cloud信息
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量开启云主机
        registerCloudOs(ActionType.START_COMPUTE_INSTANCE, SourceOperationClient::startInstance)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量关闭云主机
        registerCloudOs(ActionType.STOP_COMPUTE_INSTANCE, SourceOperationClient::stopInstance)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量重启云主机
        registerCloudOs(ActionType.REBOOT_COMPUTE_INSTANCE, SourceOperationClient::rebootInstance)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改云主机规格
        registerCloudOs(ActionType.UPDATE_COMPUTE_INSTANCE_FLAVOR, SourceOperationClient::updateEcsFlaver)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 5000, 5000)
                .addSetRefreshData("request","$.body.cloud.InstanceId")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //获取云主机的VNC远程登录地址
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::describeInstanceVncUrl)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
        //获取云主机密码
        register(ActionType.QUERY_COMPUTE_INSTANCE_PASSWORD, ComputeInstanceService::getEcsPassword)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
        //修改云主机密码
        registerCloudOs(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, SourceOperationClient::resetEcsPassword)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId",BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceId",BaseUtils::formatSingle)
                .addTransferCloud("$.model.Password", "$.Password");
        //查询存储规格
        register(ActionType.QUERY_UPDATE_COMPUTE_INSTANCE_FLAVOR, ComputeInstanceService::queryDiskStandards);
    }

    public void onAfterLoadComputeImage() {
        //创建自定义镜像
        registerCloudOs(ActionType.CREATE_COMPUTE_IMAGE, SourceOperationClient::createImage);
        //删除镜像
        registerCloudOs(ActionType.DELETE_COMPUTE_IMAGE, SourceOperationClient::deleteImage)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.ImageIds", BaseUtils::formatList);
    }

    public void onAfterLoadComputeSecurityGroup() {
        //创建安全组
        registerCloudOs(ActionType.CREATE_COMPUTE_SECURITYGROUP, SourceOperationClient::createSecurityGroup);
        //删除安全组
        registerCloudOs(ActionType.DELETE_COMPUTE_SECURITYGROUP, SourceOperationClient::deleteSecurityGroup)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SgId");
        //修改安全组
        registerCloudOs(ActionType.UPDATE_COMPUTE_SECURITYGROUP, SourceOperationClient::modifySecurityGroupAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SgId")
                .addTransferCloud("$.model.resourceName", "$.Name")
                .addTransferCloud("$.model.description", "$.Description", false);
        //创建安全组规则
        registerCloudOs(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SourceOperationClient::createSecurityGroupRule)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SgId");
        //删除安全组规则
        registerCloudOs(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SourceOperationClient::deleteSecurityGroupRule)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.securityGroupId", "$.SgId")
                .addTransferCloud("$.ci.openId", "$.SecurityGroupRuleIds",BaseUtils::formatList);
        //绑定安全组
        registerCloudOs(ActionType.BIND_COMPUTE_SECURITYGROUP,SourceOperationClient::bindSecurityGroup)
                .addBefore(CloudService::toCIPortId)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId")
                .addTransferCloud("$.model.resourceId", "$.SgIds",BaseUtils::formatList);
        //解绑安全组
        registerCloudOs(ActionType.UNBIND_COMPUTE_SECURITYGROUP, SourceOperationClient::unBindSecurityGroup)
                .addBefore(CloudService::toModelPortId)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.model.instanceId", "$.InstanceId")
                .addTransferCloud("$.ci.openId", "$.SgIds",BaseUtils::formatList);
    }

    public void onAfterLoadComputeKeypair() {
        //创建密钥对
        registerCloudOs(ActionType.CREATE_COMPUTE_KEYPAIR, SourceOperationClient::createKeyPair)
                .addTransferCloud("$.model.RegionId", "$.RegionId")
                .addTransferCloud("$.model.Name", "$.Name");
        //删除密钥对
        registerCloudOs(ActionType.DELETE_COMPUTE_KEYPAIR, SourceOperationClient::deleteKeyPairs)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
    }

    public void onAfterLoadStorageDisk() {
        //创建云硬盘
        registerCloudOs(ActionType.CREATE_STORAGE_DISK, SourceOperationClient::createDisk);
        //修改云硬盘
        registerCloudOs(ActionType.UPDATE_STORAGE_DISK, SourceOperationClient::modifyDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.VolumeId")
                .addTransferCloud("$.model.resourceName", "$.Name", false)
                .addTransferCloud("$.model.description", "$.Description", false);
        //删除云硬盘
        registerCloudOs(ActionType.DELETE_STORAGE_DISK, SourceOperationClient::deleteDisk)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.VolumeIdLList");
        //挂载云硬盘
        registerCloudOs(ActionType.ATTACH_STORAGE_DISK, SourceOperationClient::ecsAttachDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.DiskId")
                .addTransferCloud("$.model.instanceId", "$.InstanceId");
        //卸载云硬盘
        registerCloudOs(ActionType.DETACH_STORAGE_DISK, SourceOperationClient::ecsDetachDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.DiskId")
                .addTransferCloud("$.ci.relationInstance.relationOpenId", "$.InstanceId");
        //扩容云硬盘
        registerCloudOs(ActionType.RESIZE_STORAGE_DISK, SourceOperationClient::resizeDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.VolumeId")
                .addTransferCloud("$.model.size", "$.Capacity");
    }

    public void onAfterLoadStorageSnapshot() {
        //创建云硬盘快照
        registerCloudOs(ActionType.CREATE_STORAGE_SNAPSHOT, SourceOperationClient::createSnapshot)
                .addTransferCloud("$.model.regionId", "$.RegionId")
                .addTransferCloud("$.model.diskId", "$.VolumeId")
                .addTransferCloud("$.model.resourceName", "$.Name")
                .addTransferCloud("$.model.description", "$.Description", false);
        //修改云硬盘快照
        registerCloudOs(ActionType.UPDATE_STORAGE_SNAPSHOT, SourceOperationClient::modifySnapshotAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SnapshotId")
                .addTransferCloud("$.model.resourceName", "$.Name", false)
                .addTransferCloud("$.model.description", "$.Description", false);
        //删除云硬盘快照
        registerCloudOs(ActionType.DELETE_STORAGE_SNAPSHOT, SourceOperationClient::deleteSnapshot)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.SnapshotIds",BaseUtils::formatSingle);
        //快照回滚
        registerCloudOs(ActionType.RESET_STORAGE_SNAPSHOT, SourceOperationClient::resetDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SnapshotId")
                .addTransferCloud("$.ci.relationDisk.relationOpenId", "$.VolumeId");
    }

    public void onAfterLoadNeutronVpc() {
        //创建VPC
        registerCloudOs(ActionType.CREATE_NEUTRON_VPC,  SourceOperationClient::createVpc);
        //修改VPC
        registerCloudOs(ActionType.UPDATE_NEUTRON_VPC, SourceOperationClient::modifyVpcAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.VpcId")
                .addTransferCloud("$.model.resourceName", "$.Name", true);
        //删除VPC
        registerCloudOs(ActionType.DELETE_NEUTRON_VPC, SourceOperationClient::deleteVpc)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
    }

    public void onAfterLoadNeutronSubnet() {
        //创建子网
        registerCloudOs(ActionType.CREATE_NEUTRON_SUBNET, SourceOperationClient::createSubnet);
        //删除子网
        registerCloudOs(ActionType.DELETE_NEUTRON_SUBNET, SourceOperationClient::deleteSubnet)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.SubnetId")
                .addTransferCloud("$.ci.relationVpc.relationOpenId", "$.VpcId");
    }


    public void onAfterLoadNeutronEip() {
        //创建弹性IP
        registerCloudOs(ActionType.CREATE_NEUTRON_EIP, SourceOperationClient::allocateEipAddress);
        //修改弹性IP
        registerCloudOs(ActionType.UPDATE_NEUTRON_EIP, SourceOperationClient::modifyEipAddressAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InsId")
                .addTransferCloud("$.model.resourceName", "$.InstanceName", true);
        //删除弹性IP
        registerCloudOs(ActionType.DELETE_NEUTRON_EIP, SourceOperationClient::releaseEipAddress)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
        //绑定弹性IP
        registerCloudOs(ActionType.BIND_NEUTRON_EIP, SourceOperationClient::associateEipAddress)
                .addBefore(CloudService::toModelPortId)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.model.instanceId", "$.ParentId")
                .addTransferCloud("$.ci.openId", "$.EipId");
        //解绑弹性IP
        registerCloudOs(ActionType.UNBIND_NEUTRON_EIP, SourceOperationClient::unAssociateEipAddress)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.EipId")
                .addTransferCloud("$.ci.relationInstance.relationOpenId", "$.ParentId");
        ;
    }

    public void onAfterLoadNeutronNat() {
        //创建NAT网关
        registerCloudOs(ActionType.CREATE_NEUTRON_NAT, SourceOperationClient::createNatGateway);
        //修改NAT网关
        registerCloudOs(ActionType.UPDATE_NEUTRON_NAT, SourceOperationClient::modifyNatGatewayAttribute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.NatId")
                .addTransferCloud("$.model.resourceName", "$.Name", false)
                .addTransferCloud("$.model.description", "$.Description", false);
        //删除NAT网关
        registerCloudOs(ActionType.DELETE_NEUTRON_NAT, SourceOperationClient::deleteNatGateway)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.NatIds");
    }

    public void onAfterLoadNeutronRoute() {
        //创建路由表
        registerCloudOs(ActionType.CREATE_NEUTRON_ROUTE, SourceOperationClient::createVpcRoute);
        //修改路由表
        registerCloudOs(ActionType.UPDATE_NEUTRON_ROUTE, SourceOperationClient::modifyVpcRoute)
                //.addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.RouteTableId")
                .addTransferCloud("$.ci.relationVpc.relationOpenId", "$.VpcId")
                .addTransferCloud("$.model.resourceName", "$.Name", true)
                .addTransferCloud("$.model.description", "$.Description", true);
        //删除路由表
        registerCloudOs(ActionType.DELETE_NEUTRON_ROUTE, SourceOperationClient::deleteVpcRoute)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.Id")
                .addTransferCloud("$.ci.relationVpc.relationOpenId", "$.VpcId");
    }

    public void onAfterLoadBms() {
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshBms)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.InstanceId", BaseUtils::formatSingle);
        //批量创建云主机
        registerCloudOs(ActionType.CREATE_COMPUTE_BMS, SourceOperationClient::runBms)
                .addBefore(CloudService::toBmsBeforeBiz)
                .addAfter(CloudService::toAfterBizResId)
                //设置刷新请求，指定请求Action
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                //设置刷新总次数,刷新频次，首次刷新延迟
                .addSetRefreshConfig(20, 5000, 20000)
                //批量创建基于响应分割刷新任务
                .addSetRefreshSplitData("response","$.data.body.instanceIds")
                //基于请求信息设置刷新请求的cloud信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.RegionId")
                //添加刷新任务
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改云主机
        registerCloudOs(ActionType.UPDATE_COMPUTE_BMS, SourceOperationClient::updateBmsName)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId")
                .addTransferCloud("$.model.resourceName", "$.InstanceName", true)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                //单资源操作,直接设置刷新请求的data信息
                .addSetRefreshData("request","$.body.cloud.InstanceId")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量删除云主机
        registerCloudOs(ActionType.DELETE_COMPUTE_BMS, SourceOperationClient::deleteBms)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                //批量操作基于请求分割刷新任务
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                //基于请求设置刷新请求的cloud信息
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量开启云主机
        registerCloudOs(ActionType.START_COMPUTE_BMS, SourceOperationClient::startBms)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量关闭云主机
        registerCloudOs(ActionType.STOP_COMPUTE_BMS, SourceOperationClient::stopBms)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //批量重启云主机
        registerCloudOs(ActionType.REBOOT_COMPUTE_BMS, SourceOperationClient::rebootBms)
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.RegionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.InstanceIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.InstanceIds")
                .addSetRefreshCloud("request","$.body.cloud.RegionId","$.RegionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //获取裸金属的VNC远程登录地址
        register(ActionType.CONSOLE_COMPUTE_BMS, BmsService::describeBmsVncUrl)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
        //获取裸金属密码
        register(ActionType.QUERY_COMPUTE_BMS_PASSWORD, BmsService::getBmsPassword)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId");
        //修改裸金属密码
        registerCloudOs(ActionType.UPDATE_COMPUTE_BMS_PASSWORD, SourceOperationClient::resetBmsPassword)
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.RegionId")
                .addTransferCloud("$.ci.openId", "$.InstanceId")
                .addTransferCloud("$.model.Password", "$.Password");
    }
}
