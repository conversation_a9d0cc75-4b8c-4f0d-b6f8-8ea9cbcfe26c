package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeDBInstanceAttributeResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeDBInstancesResponse;

import java.util.Map;

public class DbClient extends ZiguangClient {

    public static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("ProductCode", "VM")));

    public DbClient(Config config) throws Exception {
        super(config);
    }



    /**
     * 查询MYSQL
     *
     * @throws Exception
     */
    public DescribeDBInstancesResponse describeDBInstances(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeDBInstances", "2020-07-30", "HTTPS", "GET", "AK", "/api/rds", "json", req, runtime,null), new DescribeDBInstancesResponse());
    }

    public DescribeDBInstanceAttributeResponse describeDBInstanceAttribute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeDBInstanceAttribute", "2020-07-30", "HTTPS", "GET", "AK", "/api/rds", "json", req, runtime,null), new DescribeDBInstanceAttributeResponse());
    }

    /**
     * 查询Redis
     *
     * @throws Exception
     */
    public Map<String,?> DescribeDBInstances(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DescribeDBInstances", "2020-07-30", "HTTPS", "GET", "AK", "/api/redis", "json", req, runtime, null);
        return resultMap;
    }
}
