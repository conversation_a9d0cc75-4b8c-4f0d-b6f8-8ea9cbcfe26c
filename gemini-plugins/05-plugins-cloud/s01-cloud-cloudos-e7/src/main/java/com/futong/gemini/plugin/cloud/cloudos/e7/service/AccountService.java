package com.futong.gemini.plugin.cloud.cloudos.e7.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.ClientUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client2.ZiguangClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.DescribeRegionsResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AccessToken;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Configuration
public class AccountService {
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();
    public static String accountDispatch;
    private static List<String> accountTypeList = Arrays.asList("main","sub");

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }

    public static void main(String[] args) {
        String json = "{\"plugin\":{\"realm\":\"private_huasan\",\"version\":\"e7\"},\"auth\":{\"accountType\":\"sub\",\"cmpId\":\"c8b86dc875ae46c7c8e878065872971a\",\"jsonStr\":\"{\\\"cloudAccount\\\":\\\"CloudOS7.0TEST\\\",\\\"serverIp\\\":\\\"**************\\\",\\\"appId\\\":\\\"uni188bc83fa39541788b90f69488de868d\\\",\\\"serverPort\\\":\\\"30990\\\",\\\"protocol\\\":\\\"https\\\"}\",\"description\":\"\",\"serverPort\":\"30990\",\"type\":\"account_operation\",\"version\":\"e7\",\"parentId\":14,\"isAddSubAccount\":0,\"companyId\":\"\",\"password\":\"ss8YlnzVEPDtXwaJdZaM3RVW0esbRu11\",\"protocol\":\"https\",\"c\n" +
                "loudAccount\":\"CloudOS7.0TEST\",\"cloudType\":\"private_huasan\",\"appId\":\"uni188bc83fa39541788b90f69488de868d\",\"tenantId\":\"6ae92a066e29410db2cb276f8267e580\",\"serverIp\":\"**************\",\"status\":\"success\",\"username\":\"nURUe9Wm6205mHzg\"},\"action\":\"AuthPlatformAccount\",\"body\":{\"auth\":{\"accountType\":\"sub\",\"cmpId\":\"c8b86dc875ae46c7c8e878065872971a\",\"jsonStr\":\"{\\\"cloudAccount\\\":\\\"CloudOS7.0TEST\\\",\\\"serverIp\\\":\\\"**************\\\",\\\"appId\\\":\\\"uni188bc83fa39541788b90f69488de868d\\\",\\\"serverPort\\\":\\\"30990\\\",\\\"protocol\\\":\\\"https\\\"}\",\"description\":\"\",\"serverPort\":\"30990\",\"type\":\"account_operation\",\"version\":\"e7\",\"parentId\":14,\"isAddSubAccount\":0,\"companyId\":\"\",\"password\":\"ss8YlnzVEPDtXwaJdZaM3RVW0esbRu1\",\"protocol\":\"https\",\"cloudAccount\":\"CloudOS7.0TEST\",\"cloudType\":\"private_huasan\",\"appId\":\"uni188bc83fa39541788b90f69488de868d\",\"tenantId\":\"6ae92a066e29410db2cb276f8267e580\",\"serverIp\":\"**************\",\"status\":\"success\",\"username\":\"nURUe9Wm6205mHzg1\"},\"ci\":{}}}";
        JSONObject request = JSONObject.parseObject(json);
        String accountType = request.getJSONObject("body").getJSONObject("auth").getString("accountType");
        if(accountTypeList.get(1).equals(accountType)){
            CloudAccessBean bean = request.getJSONObject("body").getJSONObject("auth").toJavaObject(CloudAccessBean.class);
            AccessToken accessToken = HttpClientUtil.getAccessToken(bean);
            if(ObjectUtil.isNull(accessToken) || !accessToken.isStatus()){
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, "云账号信息认证失败");
            }
        }
    }

    public static BaseResponse authCloudAccount(BaseCloudRequest request) {
        try {
            log.info("CloudOS7.0账号验证信息为：{}",request.toJSONString());
            String accountType = request.getBody().getAuth().getString("accountType");
            if(accountTypeList.get(0).equals(accountType)){
                Map<String,Object> map = new HashMap<>();
                CloudClient.client.execute(request.getBody(),map, EcsClient::describeRegions);
            }
            if(accountTypeList.get(1).equals(accountType)){
                ZiguangClient client= com.futong.gemini.plugin.cloud.cloudos.e7.client2.ClientUtils.client(ZiguangClient.class, request.getBody());
                String accessToken = client.getToken();
                if(StrUtil.isEmpty(accessToken)){
                    throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, "云账号信息认证失败");
                }
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        //替换参数云账号ID
        String text = StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId());
        JSONObject result = JSON.parseObject(text);
        DescribeRegionsResponse describeRegionsResponse = CloudClient.client.execute(request.getBody(), EcsClient::describeRegions);
        List<DescribeRegionsResponse.DescribeRegionsResponseBodyData> regions = describeRegionsResponse.getBody().getData();
        if (request.getBody().containsKey("all")) {
            //根据地域生成全量调度任务
            return new BaseDataResponse<>(listAllDispatcher(result, regions));
        } else {
            //获取region信息
            List<HashMap<String, String>> formRegionItems = regions.stream().map(t -> {
                HashMap<String, String> region = new HashMap<>();
                region.put("label", t.getRegionName());
                region.put("value", t.getRegionId());
                return region;
            }).collect(Collectors.toList());
            result.getJSONObject("form").getJSONObject("region").put("items", formRegionItems);
            return new BaseDataResponse<>(result);
        }
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, List<DescribeRegionsResponse.DescribeRegionsResponseBodyData> regions) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                for (DescribeRegionsResponse.DescribeRegionsResponseBodyData region : regions) {
                    String itemStrRegion = StrUtil.replace(itemStr, "${region.label}", region.getRegionName());
                    itemStrRegion = StrUtil.replace(itemStrRegion, "${region.value}", region.getRegionId());
                    dispatchers.add(JSON.parseObject(itemStrRegion));
                }
            }
        });
        return dispatchers;
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        GourdProxy gourdProxy = SpringUtil.getBean(GourdProxy.class);
        //调用gourd服务-添加调度层级，新增云类型层级
        GourdUtils.addGourdLevel(request.getPlugin().getRealm(), null, "云调度-谐云");
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //删除旧得调度任务
        JSONObject oldWhere = new JSONObject();
        oldWhere.put("jobLevel", request.getBody().getAccess().getCmpId());
        BaseResponse response = gourdProxy.stopAndDeleteDispatcher(oldWhere);
        if (BaseResponse.SUCCESS.equals(response)) {
            log.info("删除旧得调度任务成功!");
        } else {
            log.error("删除旧得调度任务失败{}!", JSON.toJSONString(response));
            return BaseResponse.ERROR_BIZ.of("删除旧得调度任务失败");
        }
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        DescribeRegionsResponse describeRegionsResponse = CloudClient.client.execute(request.getBody(), EcsClient::describeRegions);
        List<JSONObject> dispatchers = listAllDispatcher(result, describeRegionsResponse.getBody().getData());
        //调用gourd服务-批量添加调度任务
        return gourdProxy.createDispatchers(dispatchers);
    }

//    public static <Q, R, C> FTAction<JSONObject> toFTAction(FTExecute<C, Q, R> exec, ActionType actionType) {
//        return (JSONObject arguments) -> doAction(exec, actionType);
//    }

    public static <Q, R, C> BaseDataResponse<R> doAction(FTExecute<C, Q, R> exec, ActionType actionType) {
        try {
            return new BaseDataResponse<>(ClientUtils.base.execute(exec));
        } catch (Exception e) {
            String message = actionType.operationType().cname() + actionType.resourceType().name() + "失败";
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }

}
