package com.futong.gemini.plugin.cloud.cloudos.e7.client2;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;

public class ClientUtils {
    //获取Client对象
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        String accountType = body.getAuth().getString("accountType");
        CloudAccessBean sub = StrUtil.isNotEmpty(accountType)&&accountType.equals("sub")?body.getAccess():body.getAccess().getSubList().get(0);
        try {
            String appId = null;
            String proxyHost = null;
            Integer proxyPort = null;
            if (StrUtil.isNotEmpty(sub.getJsonStr())) {
                String jsonStr = sub.getJsonStr();
                JSONObject json = JSONObject.parseObject(jsonStr);
                proxyHost = json.getString("proxyHost");
                proxyPort = json.getInteger("proxyPort");
                appId = json.getString("appId");
            }
            //请求Client对象配置信息
            ConnectionConfig.Builder builder = new ConnectionConfig.Builder()
                    .protocol(sub.getProtocol())
                    .host(sub.getServerIp())
                    .port(sub.getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .username(sub.getUsername())
                            .password(sub.getPassword())
                            .appId(appId)
                            .build()
                    );
            if(StrUtil.isNotEmpty(proxyHost)&& ObjUtil.isNotEmpty(proxyPort)) {
                builder.proxy(proxyHost,proxyPort);
            }
            ConnectionConfig config = builder.build();
            return clazz.getConstructor(ConnectionConfig.class).newInstance(config);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
}
