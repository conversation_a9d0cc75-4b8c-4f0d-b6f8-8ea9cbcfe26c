package com.futong.gemini.plugin.cloud.cloudos.e7.sampler;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.*;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.MonitorClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client2.AuthConfig;
import com.futong.gemini.plugin.cloud.cloudos.e7.client2.ClientUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.client2.ConnectionConfig;
import com.futong.gemini.plugin.cloud.cloudos.e7.client2.ZiguangClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.*;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.net.URL;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

@Slf4j
public class FetchService {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        DescribeRegionsResponse regionsResponse = CloudClient.client.execute(request.getBody(), EcsClient::describeRegions);
        Map<Class, List> regionMap = Convert.convertRegion(request, regionsResponse);
        BaseResponse response = BaseCloudService.fetchSend(request, regionMap);
        regionsResponse.body.data.forEach(t -> {
            fetchZone(request, t);
        });
        return response;
    }


    public static BaseResponse fetchZone(BaseCloudRequest request, DescribeRegionsResponse.DescribeRegionsResponseBodyData response) {
        Map<Class, List> map = Convert.convertZone(request, response);
        return BaseCloudService.fetchSend(request, map);
    }


    /**
     * 获取镜像
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchImage(BaseCloudRequest request) {
        Entry.E2<DescribeImageResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeImages,
                Convert::convertImage);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }


    /**
     * 获取安全组
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request) {
        Entry.E2<DescribeSecurityGroupResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeSecurityGroups,
                Convert::convertSecurityGroup);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if(CollUtil.isNotEmpty(mapE2.v2.get(CmdbSecuritygroupRes.class))){
            for (Object obj : mapE2.v2.get(CmdbSecuritygroupRes.class)) {
                CmdbSecuritygroupRes res = (CmdbSecuritygroupRes) obj;
                request.getBody().getCloud().put("SgId", res.getOpen_id());
                Map<Class, List> ruleMap = BaseCloudService.fetch(
                        request,
                        CloudClient.client,
                        EcsClient::describeSecurityGroupRule,
                        Convert::convertSecurityGroupRule);
                BaseCloudService.fetchSend(request, ruleMap);
                request.getBody().getCloud().remove("SgId");
            }
            //分页安全组
            return toPageGourdResponse(request, response,
                    mapE2.v1.body.res.getTotal(),
                    mapE2.v1.body.res.getSize());
        }
        return response;
    }

    /**
     * 获取VPC
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchVpc(BaseCloudRequest request) {
        Entry.E2<DescribeNetworkResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeVpcs,
                Convert::convertVpc);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        response = BaseCloudService.toflatMapGourdResponse(response, mapE2.v1.body.getRes(), (DescribeNetworkResponse.DescribeNetworkResponseBodyData t) -> {
            List<JobInfo> result = new ArrayList<>();
            JobInfo subnetJobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_NEUTRON_SUBNET);
            request.getBody().getCloud().put("RegionId", t.getRegionId());
            request.getBody().getCloud().put("VpcId", t.getInstanceId());
            subnetJobInfo.setRequest(request.cloneJSONObject());
            result.add(subnetJobInfo);

            JobInfo routeJobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_NEUTRON_ROUTE);
            request.getBody().getCloud().put("RegionId", t.getRegionId());
            request.getBody().getCloud().put("VpcId", t.getInstanceId());
            routeJobInfo.setRequest(request.cloneJSONObject());
            result.add(routeJobInfo);
            return result.stream();
        });
        return response;
    }

    /**
     * 获取Subnet
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        Entry.E2<DescribeNetworkSubnetResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeSubnets,
                Convert::convertSubnet);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return response;
    }


    /**
     * 获取网卡
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNetcard(BaseCloudRequest request) {
        Entry.E2<DescribeNetworkInterfacesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeNetworkInterfaces,
                Convert::convertNetcard);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getSize());
    }

    /**
     * 获取路由
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchRoute(BaseCloudRequest request) {
        Entry.E2<DescribeRouteTableListResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeRouteTableList,
                Convert::convertRoute);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotalCount(),
                mapE2.v1.body.res.getSize());
    }

    /**
     * 获取Nat
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNat(BaseCloudRequest request) {
        Entry.E2<DescribeNatResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeNatGateways,
                Convert::convertNat);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if (CollUtil.isEmpty(mapE2.v2.get(CmdbNatRes.class))) {
            return response;
        }
        //分页子任务
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotal().intValue(),
                mapE2.v1.body.res.getSize());
    }

    public static BaseResponse fetchDnatEntry(BaseCloudRequest request) {
        Entry.E2<DescribeNatDnatResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeNatGatewaysDnat,
                Convert::convertDnatEntry);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotal().intValue(),
                mapE2.v1.body.res.getSize());
    }

    public static BaseResponse fetchSnatEntry(BaseCloudRequest request) {
        Entry.E2<DescribeNatSnatResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeNatGatewaysSnat,
                Convert::convertSnatEntry);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotal().intValue(),
                mapE2.v1.body.res.getSize());
    }

    //密钥对
    public static BaseResponse fetchKeyPair(BaseCloudRequest request) {
        Entry.E2<DescribeKeyPairsResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeKeyPairs,
                Convert::convertKeyPair);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }

    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        Entry.E2<DescribeInstancesResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeInstances,
                Convert::convertEcs);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        if(CollUtil.isNotEmpty(mapE2.v2.get(CmdbInstanceRes.class))){
            for (Object obj : mapE2.v2.get(CmdbInstanceRes.class)) {
                CmdbInstanceRes res = (CmdbInstanceRes) obj;
                request.getBody().getCloud().put("InstanceId", res.getOpen_id());
                try {
                    Map<Class, List> ruleMap = BaseCloudService.fetch(
                            request,
                            CloudClient.client,
                            EcsClient::detailEcs,
                            Convert::convertEcsDetail);
                    BaseCloudService.fetchSend(request, ruleMap);
                } catch (Exception e) {
                    continue;
                }
                request.getBody().getCloud().remove("InstanceId");
            }
            return toPageGourdResponse(request, response,
                    mapE2.v1.body.getTotalCount(),
                    mapE2.v1.body.getPageSize());
        }
        return response;
    }

    /**
     * 获取云主机与裸金属规格
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchFlavor(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                EcsClient::describeInstanceTypes,
                Convert::convertFlavor);
    }


    public static boolean defaultMetricRequest(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("regionId")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数regionId不能为空!");
        }
        if (!request.getBody().getCloud().containsKey("period")) {
            request.getBody().getCloud().put("period", "300");
        }
        if (!request.getBody().getCloud().containsKey("startTime")) {
            //当前时间-监控数据得统计周期（秒）*1000（毫秒）
            long startTime = System.currentTimeMillis() - NumberUtil.parseLong(request.getBody().getCloud().getString("period")) * 1000;
            request.getBody().getCloud().put("start", Long.toString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("end", System.currentTimeMillis() + "");
        }
        return true;
    }

    public static boolean defaultEcsMetricNames(BaseCloudRequest request) {
        if (!request.getBody().containsKey("MetricNames")) {
            request.getBody().put("MetricNames", Converts.metrics.keySet());
        }
        return true;
    }

    public static boolean defaultEcsMetricDimensions(BaseCloudRequest request) {
        //处理查询南新仓云主机+磁盘信息接口请求
        if (!request.getBody().containsKey("BasePageSortSearchRequest")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数BasePageSortSearchRequest不能为空!");
        }
        JSONObject searchJsonRequest = request.getBody().getJSONObject("BasePageSortSearchRequest");
        if (!searchJsonRequest.containsKey("size")) {
            searchJsonRequest.put("size", 50);
        }
        BasePageSortSearchRequest searchRequest = searchJsonRequest.toJavaObject(BasePageSortSearchRequest.class);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得云主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchJsonRequest.put("current", t);
                request.put("async", true);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().put("response", baseResponse);
        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().put("instanceMap", instanceMap);
        //设置采集监控数据云主机ID
        String instanceIds = instanceMap.keySet().stream().collect(Collectors.joining(","));
        request.getBody().getCloud().put("instanceIds", instanceIds);
        return true;
    }

    public static BaseResponse fetchEcsPerf(BaseCloudRequest request) {
        try {
            //监控请求指标集合
            Collection<String> metricNames = request.getBody().getJSONArray("MetricNames").toJavaList(String.class);
            List<Map<String, List<OpenRangeResponse.BodyResDataResult>>> metricList = new ArrayList<>();
            for (String metricName : metricNames) {
                request.getBody().getCloud().put("category", metricName);
                OpenRangeResponse result = CloudClient.client.execute(request.getBody(), MonitorClient::openRange);
                List<OpenRangeResponse.BodyResDataResult> rs =
                        Optional.ofNullable(result)
                                .map(r -> r.body)
                                .map(b -> b.res)
                                .filter(list -> !list.isEmpty())
                                .map(list -> list.get(0))
                                .map(OpenRangeResponse.BodyRes::getData)
                                .map(OpenRangeResponse.BodyResData::getResult)
                                .orElse(null);
//                List<OpenRangeResponse.BodyResDataResult> rs = Opt.ofEmptyAble(result.body.res).map(t -> t.get(0)).map(OpenRangeResponse.BodyRes::getData).map(OpenRangeResponse.BodyResData::getResult).orElse(null);
//                Map<String,List<OpenRangeResponse.BodyResDataResult>> resultMap = rs.stream()
//                        .collect(Collectors.groupingBy(k -> k.getMetric().getInstance() + "-" + k.getMetric().getCategory(), HashMap::new,
//                                Collectors.toList()));
                Map<String, List<OpenRangeResponse.BodyResDataResult>> resultMap =
                        Optional.ofNullable(rs) // 处理rs为null的情况
                                .orElse(Collections.emptyList()) // 如果rs为null，转为空List
                                .stream()
                                .filter(Objects::nonNull) // 过滤掉List中的null元素
                                .filter(k -> k.getMetric() != null) // 确保metric不为null
                                .filter(k -> k.getMetric().getInstance() != null) // 确保instance不为null
                                .filter(k -> k.getMetric().getCategory() != null) // 确保category不为null
                                .collect(Collectors.groupingBy(
                                        k -> k.getMetric().getInstance() + "-" + k.getMetric().getCategory(),
                                        HashMap::new,
                                        Collectors.toList()
                                ));
                metricList.add(resultMap);
            }
            request.getBody().getCloud().remove("instanceIds");
            Map<String, PerfInfoBean> perfMap = Convert.convertEcsPerf(request, metricList);
            log.info("转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            if (request.getBody().containsKey("response")) {
                return request.getBody().getObject("response", BaseResponse.class);
            } else {
                return BaseResponse.SUCCESS;
            }
        } catch (Exception e) {
            log.error("获取云主机监控失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "获取云主机监控失败");
        }
    }


    public static BaseResponse fetchDisk(BaseCloudRequest request) {
        Entry.E2<DescribeDisksResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeDisks,
                Convert::convertDisk);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotal(),
                mapE2.v1.body.getSize());
    }


    public static BaseResponse fetchSnapshot(BaseCloudRequest request) {
        Entry.E2<DescribeSnapshotResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeSnapshots,
                Convert::convertSnapshot);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.getTotalCount(),
                mapE2.v1.body.getPageSize());
    }

    public static BaseResponse fetchEip(BaseCloudRequest request) {
        Entry.E2<DescribeEipResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeEipAddresses,
                Convert::convertEip);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotal(),
                mapE2.v1.body.res.getSize());
    }

    //负载均衡
    public static BaseResponse fetchLoadBalancer(BaseCloudRequest request) {
        Entry.E2<DescribeElbResponse, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeLoadBalancers,
                Convert::convertLoadBalancer);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.body.res.getTotal(),
                mapE2.v1.body.res.getSize());
    }

    //对象存储桶信息
    public static BaseResponse fetchBucket(BaseCloudRequest request) {
        List<Bucket> result = CloudClient.client.execute(request.getBody(), (FTExecute<AmazonS3, ListBucketsRequest, List<Bucket>>) AmazonS3::listBuckets);
        log.info("获取存储桶:{},result.size:{}", JSONObject.toJSONString(result),result.size());
        return BaseCloudService.toGourdResponse(
                BaseResponse.SUCCESS.of("获取存储桶" + result.size() + "个"),
                result,
                (Bucket t) -> {
                    JobInfo jobInfo = new JobInfo();
                    request.setAction(ActionType.FETCH_STORAGE_BUCKET_FILE);
                    request.getBody().getCloud().put("bucket", t);
                    jobInfo.setRequest(request.cloneJSONObject());
                    return jobInfo;
                });

    }

    //对象存储桶信息
    public static BaseResponse fetchBucketFile(BaseCloudRequest request) {
        try {

            log.info("获取桶请求:{}", JSONObject.toJSONString(request));
            List<CmdbBucketRes> buckets = new ArrayList<>();
           // List<CmdbBucketFileRes> files = new ArrayList<>();
            List<Association> associations = new ArrayList<>();
            List<TmdbResourceSet> sets = new ArrayList<>();
            Map<Class, List> result = new HashMap<>();
            result.put(CmdbBucketRes.class, buckets);
         //   result.put(CmdbBucketFileRes.class, files);
            result.put(Association.class, associations);
            result.put(TmdbResourceSet.class, sets);

            AmazonS3 client = CloudClient.client.client(AmazonS3.class, request.getBody());
            Bucket bucket = request.getBody().getCloud().getObject("bucket", Bucket.class);
            //查询桶对象列表
/*            ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
            listObjectsRequest.setBucketName(bucket.getName());
            List<S3ObjectSummary> os = LimitUtils.query(listObjectsRequest,
                    q -> CloudClient.client.execute(client, q, AmazonS3::listObjects),//执行云上查询所需要的函数
                    ObjectListing::getObjectSummaries,//取结果集
                    listObjectsRequest::setMarker,//设置新的查询条件
                    (ObjectListing t) -> t.getNextMarker()//从结果中获取新的查询条件
            );*/
            //查询桶的防盗链
            BucketPolicy br = CloudClient.client.execute(client, new GetBucketPolicyRequest(bucket.getName()), AmazonS3::getBucketPolicy);
            //查询桶的访问权限
            AccessControlList acl = CloudClient.client.execute(client, new GetBucketAclRequest(bucket.getName()), AmazonS3::getBucketAcl);
            //转换对象
            CmdbBucketRes bucketRes = convertBucket(request, bucket, br, acl);
            buckets.add(bucketRes);

            List<TmdbResourceSet> tes = BuilderResourceSet.of()
                    .withInfo(request.getBody().getAccess().getCmpId(),
                            CloudType.fromValue(request.getPlugin().getRealm()),
                            ResourceType.CMDB_BUCKET_RES
                    ).withDataByDevopsValue(buckets,
                            DevopsSide.DEVOPS_REGION,
                            request.getBody().getCloud().getString("RegionId"))
                    .getData();
            sets.addAll(tes);
/*            for (S3ObjectSummary o : os) {
                //查询文件权限
                AccessControlList oAcl = CloudClient.client.execute(client, new GetObjectAclRequest(bucket.getName(), o.getKey()), AmazonS3::getObjectAcl);
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket.getName(), o.getKey());
                generatePresignedUrlRequest.setExpiration(new Date(System.currentTimeMillis() + TimeUtils.MONTH_MILLISECOND));
                URL oUrl = CloudClient.client.execute(client, generatePresignedUrlRequest, AmazonS3::generatePresignedUrl);
                CmdbBucketFileRes bucketFileRes = convertBucketFile(request, o, oAcl, oUrl, associations);
                associations.add(AssociationUtils.toAssociation(bucketFileRes, CmdbBucketRes.class, bucketRes.getRes_id()));
                files.add(bucketFileRes);
            }*/
            log.info("对象采集结果：{}",JSONObject.toJSONString(result));
            return BaseCloudService.fetchSend(request, result);
        } catch (Exception e) {
            log.error("同步对象存储桶失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步对象存储桶失败");
        }
    }


    public static CmdbBucketRes convertBucket(BaseCloudRequest request, Bucket bucket,  BucketPolicy br, AccessControlList acl) {
        CmdbBucketRes ci = new CmdbBucketRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), bucket.getName()));
        ci.setOpen_id(bucket.getName());
        ci.setOpen_name(bucket.getName());
        ci.setCreate_time(TimeUtils.utcDateToInstant(bucket.getCreationDate()).toEpochMilli());
        toCiResCloud(request, ci);
        //访问权限
        if (ObjectUtil.isNotEmpty(acl)) {
            ci.setAcl(acl.getGrantsAsList().stream().map(t -> t.getPermission()).collect(Collectors.toList()).toString());
        }
        //存量
     /*   if (CollUtil.isNotEmpty(os)) {
            float storageSize = 0f;
            for (S3ObjectSummary objectSummary : os) {
                storageSize += objectSummary.getSize();
            }
            ci.setSize(UnitUtil.convert(storageSize, UnitUtil.BYTE, UnitUtil.GB, 3));
            ci.setObject_number("" + os.size());
        }*/
        return ci;
    }

    public static CmdbBucketFileRes convertBucketFile(BaseCloudRequest request, S3ObjectSummary object, AccessControlList acl, URL url, List<Association> list) {
        CmdbBucketFileRes ci = new CmdbBucketFileRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), object.getKey()));
        ci.setOpen_id(object.getKey());
        ci.setOpen_name(object.getKey());
        ci.setStorage_type(object.getStorageClass());
        toCiResCloud(request, ci);
        //访问权限
        if (ObjectUtil.isNotEmpty(acl.getGrantsAsList())) {
            List<Permission> collect = acl.getGrantsAsList().stream().map(Grant::getPermission).collect(toList());
            ci.setAcl(collect.toString());
        }
        // 文件大小
        ci.setFile_size(UnitUtil.convert(object.getSize(), UnitUtil.BYTE, UnitUtil.GB, 3));
        // 最后修改时间
        if (object.getLastModified() != null) {
            ci.setUpdate_time(object.getLastModified().getTime());
        }
        // url
        ci.setUrl(url.toString());

        return ci;
    }

    /**
     * 告警
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        try {
            Map<String, Object> map = request.getBody().getCloud().toJavaObject(Map.class);
            DescribeAlarmRecordResponse response = CloudClient.client.execute(request.getBody(), map, EcsClient::listAlarmRecord);
            if (null == response.body || response.body.res == null || CollUtil.isEmpty(response.body.res.list)) {
                return BaseResponse.SUCCESS.of("查询告警信息为空!");
            }
            //将获取到的云主机信息转换为CI模型
            List<AlarmInfoBean> listCI = response.body.res.list.stream().map(t -> convertAlarm(request, t)).collect(Collectors.toList());
            BaseCloudService.toAetMessageAndSend(listCI, "alarm");
            String message = StrUtil.format("本次获取告警信息,页码：{},条数：{},本次获取条数：{}", response.body.res.page, response.body.res.size, listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == response.body.res.size) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("page", response.body.res.page + 1);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步告警数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步告警数据失败");
        }
    }

    public static AlarmInfoBean convertAlarm(BaseCloudRequest request, DescribeAlarmRecordResponse.DescribeAlarmRecordListResponseBodyData alert) {
        AlarmInfoBean alarm = new AlarmInfoBean();
        alarm.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getId()));
        alarm.setAccountId(request.getBody().getAccess().getCmpId());
        alarm.setCloudType(request.getPlugin().getRealm());
        alarm.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alert.getId()));
        alarm.setOpenId(alert.getId());
        alarm.setOpenName(alert.getAlarmRuleName());
        alarm.setOpenLevel(alert.getAlarmRuleLevel());
        alarm.setAlarmId(alert.getAlarmRuleId());
        alarm.setAlarmName(alert.getAlarmRuleName());
        alarm.setDetail(alert.getAlarmRuleDesc());
        if ("WARNNING".equals(alert.getStatus())) {
            alarm.setClosedStatus(true);
        } else {
            alarm.setClosedStatus(false);
        }
        alarm.setJsonInfo(JSON.toJSONString(alert));
        alarm.setCount(1);
        if (ObjectUtil.isNotEmpty(alert.getAlarmTime())) {
            alarm.setFirstTime(DateUtil.formatDateTime(DateUtil.date(alert.getAlarmTime())));
            alarm.setCreateTime(alarm.getFirstTime());
        }
        if ("ECS".equalsIgnoreCase(alert.getAlarmJobType())) {
            alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        }
        switch (alert.getAlarmRuleLevel()) {
            case "EMERGENCY":
                alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                break;
            case "IMPORTANT":
                alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                break;
            case "ORDINARY":
                alarm.setAlarmLevel(AlarmLevel.MINOR.value());
                break;
            case "INFORMATION":
                alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                alarm.setClosedStatus(true);
                break;
        }
        return alarm;
    }


    /**
     * 事件
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            Map<String, Object> map = request.getBody().getCloud().toJavaObject(Map.class);
            //查询阿里云云主机信息
            DescribeEventHistoryResponse response = CloudClient.client.execute(request.getBody(), map, EcsClient::countEventHistoryGroupByCategory);
            if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.list)) {
                return BaseResponse.SUCCESS.of("查询事件信息为空!");
            }
            List<EventInfoBean> listCI = response.body.res.list.stream().map(t -> convertEvent(request, t)).collect(Collectors.toList());
            listCI.removeIf(Objects::isNull);
            BaseCloudService.toAetMessageAndSend(listCI, "event");

            String message = StrUtil.format("本次获取事件信息,页码：{},条数：{},本次获取条数：{}", response.body.res.page, response.body.res.size, listCI.size());
            //因为接口没有返回总条数，如果查询告警条数==请求条数，则继续尝试查询
            if (CollUtil.isNotEmpty(listCI) && listCI.size() == response.body.res.size) {
                message += StrUtil.format("。查询条数等于请求条数，将尝试获取下页数据!");
                request.getBody().getCloud().put("page", response.body.res.page + 1);
                JobInfo jobInfo = new JobInfo();
                jobInfo.setRequest(request.cloneJSONObject());
                //返回带子任务的响应信息
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo), message);
            }
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("同步事件数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP, e, "同步事件数据失败");
        }
    }

    public static EventInfoBean convertEvent(BaseCloudRequest request, DescribeEventHistoryResponse.DescribeEventHistoryListResponseBodyData event) {
        EventInfoBean bean = new EventInfoBean();
        bean.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), event.getDataCategory(), event.getDataCategoryName(), "" + event.getLastTriggerTime()));
        bean.setAccountId(request.getBody().getAccess().getCmpId());
        bean.setCloudType(request.getPlugin().getRealm());
        bean.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), "" + event.getLastTriggerTime()));
        bean.setOpenId(event.getDataCategory());
        bean.setOpenName(event.getDataCategoryName());
        bean.setOpenLevel(event.getAlarmRuleLevel());
        /**
         * 事件级别。取值：
         *
         * CRITICAL：严重。
         * WARN：警告。
         * INFO：信息
         */
        switch (event.getAlarmRuleLevel()) {
            case "EMERGENCY":
                bean.setEventLevel(AlarmLevel.CRITICAL.value());
                break;
            case "IMPORTANT":
                bean.setEventLevel(AlarmLevel.MAJOR.value());
                break;
            case "ORDINARY":
                bean.setEventLevel(AlarmLevel.MINOR.value());
                break;
            case "INFORMATION":
                bean.setEventLevel(AlarmLevel.INFORMATION.value());
                break;
        }
        bean.setEventType("system");
        bean.setEventName(event.getDataCategoryName());
        bean.setDetail(event.getDataCategoryName());
        if (event.getLastTriggerTime() != null) {
            bean.setBeginTime(DateUtil.formatDateTime(DateUtil.date(Long.parseLong(event.getLastTriggerTime()))));
            bean.setEndTime(bean.getBeginTime());
        }
        bean.setJsonInfo(JSON.toJSONString(event));
        String resourceType = event.getResourceType();
        if (StrUtil.isNotEmpty(event.getResourceType())) {
            if ("ecs".equalsIgnoreCase(resourceType)
                    || "instance".equalsIgnoreCase(resourceType)
                    || "ALIYUN::Instance".equalsIgnoreCase(resourceType)
                    || "ALIYUN::ECS::Instance".equalsIgnoreCase(resourceType)
            ) {
                bean.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
            } else if ("disk".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_DISK_RES.value());
            } else if ("snapshot".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SNAPSHOT_RES.value());
            } else if ("image".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_IMAGE_RES.value());
            } else if ("securitygroup".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SECURITYGROUP_RES.value());
            } else if ("vpc".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_VPC_RES.value());
            } else if ("vswitch".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_SUBNET_RES.value());
            } else if ("bucket".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_BUCKET_RES.value());
            } else if ("eni".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
            } else if ("eip".equalsIgnoreCase(resourceType)) {
                bean.setResourceType(ResourceType.CMDB_EIP_RES.value());
            }
        }
        return bean;
    }

    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) {
            return response;
        }
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("Page", t);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    /**
     * 获取物理主机信息
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHost(BaseCloudRequest request) {
        ZiguangClient client = ClientUtils.client(ZiguangClient.class, request.getBody());
        try {
            String regionId = request.getBody().getModel().getString("regionId");
            JSONObject resultRes = client.doGetResJSON("/ops/compute/compute/omc/hosts/list", request.getBody().getCloud(), regionId);
            JSONArray resultResData = resultRes.getJSONArray("data");
            Map<Class, List> data = Convert.convertHost(request, resultResData);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    resultRes.getInteger("total"),
                    resultRes.getInteger("size"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("存储-存储服务class!"), e);
        }
    }

    /**
     * 获取物理主机信息
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchHostPerf(BaseCloudRequest request) {
        ZiguangClient client = ClientUtils.client(ZiguangClient.class, request.getBody());
        try {
            String regionId = request.getBody().getModel().getString("regionId");
            //获取宿主机信息
            JSONObject resultRes = client.doGetResJSON("/ops/compute/compute/omc/hosts/list", request.getBody().getCloud(), regionId);
            JSONArray resultResData = resultRes.getJSONArray("data");
            //获取宿主机当前时间CPU性能信息
            Long time = System.currentTimeMillis() / 1000;
            JSONObject json = JSONArray.parseObject("{\n" +
                    "\t\"query\": \"node_cpu_usage_avg{model_code=\\\"server\\\",region=\\\"" + regionId + "\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}\",\n" +
                    "\t\"time\": \"" + (time) + "\"\n" +
                    "}");
            JSONObject resultResCpuPerf = client.doGetJSON("/monitor/open/open/query", json, regionId);
            JSONObject resultResCpuPerfData = resultResCpuPerf.getJSONObject("data");
            JSONArray resultResCpuPerfDataResult = resultResCpuPerfData.getJSONArray("result");
            JSONObject json2 = JSONArray.parseObject("{\n" +
                    "\t\"query\": \"node_cpu_usage_avg{model_code=\\\"server\\\",region=\\\"" + regionId + "\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}\",\n" +
                    "\t\"time\": \"" + (time) + "\"\n" +
                    "}");
            JSONObject resultResMemoryPerf = client.doGetJSON("/monitor/open/open/query", json2, regionId);
            JSONObject resultResMemoryPerfData = resultResMemoryPerf.getJSONObject("data");
            JSONArray resultResMemoryPerfDataResult = resultResMemoryPerfData.getJSONArray("result");

            List<ResourcePerfDetail> data = Convert.convertHostPerf(request, resultResData, resultResCpuPerfDataResult, resultResMemoryPerfDataResult);
            BaseCloudService.toPerfMessageAndSend(data, "API");
            BaseResponse response = BaseResponse.SUCCESS.of("获取物理主机性能数据成功");
            return toPageGourdResponse(request, response,
                    resultRes.getInteger("total"),
                    resultRes.getInteger("size"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取物理主机性能数据!"), e);
        }
    }


    public static BaseResponse fetchBms(BaseCloudRequest request) {
        Entry.E2<JSONObject, Map<Class, List>> mapE2 = BaseCloudService.fetchR(request,
                CloudClient.client,
                EcsClient::describeBms,
                Convert::convertBms);
        BaseResponse response = BaseCloudService.fetchSend(request, mapE2.v2);
        return toPageGourdResponse(request, response,
                mapE2.v1.getJSONObject("body").getInteger("totalCount"),
                mapE2.v1.getJSONObject("body").getInteger("size"));
    }

    /**
     * 获取物理主机信息
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchBmsPerf(BaseCloudRequest request) {
        try {
            List<Map<String, List<OpenRangeResponse.BodyResDataResult>>> metricList = new ArrayList<>();
            String regionId = request.getBody().getCloud().getString("regionId");
            JSONObject bmsResult = CloudClient.client.execute(request.getBody(), EcsClient::describeBms);
            request.getBody().getCloud().remove("RegionId");
            request.getBody().getCloud().put("regionId",regionId);
            if (null == bmsResult || ObjectUtil.isEmpty(bmsResult.getJSONObject("body").getJSONArray("list"))) {
                throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得裸金属"));
            }
            JSONArray bmsInfos = bmsResult.getJSONObject("body").getJSONArray("list");
            String instanceIds = IntStream.range(0, bmsInfos.size())
                    .mapToObj(bmsInfos::getJSONObject)
                    .map(obj -> obj.getString("instanceId"))
                    .collect(Collectors.joining(","));
            request.getBody().getCloud().put("instanceIds", instanceIds);
            for (String metricName : Converts.bmsMetrics.keySet()) {
                request.getBody().getCloud().put("category", metricName);
                OpenRangeResponse result = CloudClient.client.execute(request.getBody(), MonitorClient::openRange);
                List<OpenRangeResponse.BodyResDataResult> rs =
                        Optional.ofNullable(result)
                                .map(r -> r.body)
                                .map(b -> b.res)
                                .filter(list -> !list.isEmpty())
                                .map(list -> list.get(0))
                                .map(OpenRangeResponse.BodyRes::getData)
                                .map(OpenRangeResponse.BodyResData::getResult)
                                .orElse(null);
                Map<String, List<OpenRangeResponse.BodyResDataResult>> resultMap =
                        Optional.ofNullable(rs)
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull)
                                .filter(k -> k.getMetric() != null)
                                .filter(k -> k.getMetric().getInstance() != null)
                                .filter(k -> k.getMetric().getCategory() != null)
                                .collect(Collectors.groupingBy(
                                        k -> k.getMetric().getInstance() + "-" + k.getMetric().getCategory(),
                                        HashMap::new,
                                        Collectors.toList()
                                ));
                metricList.add(resultMap);
            }
            request.getBody().getCloud().remove("instanceIds");
            Map<String, ResourcePerfDetail> perfMap = Convert.convertBmsPerf(request, metricList,bmsInfos);
            log.info("bms转换监控信息为:{}", JSON.toJSONString(perfMap));
            BaseCloudService.toPerfMessageAndSend(new ArrayList<>(perfMap.values()), "API");
            BaseResponse response = BaseResponse.SUCCESS.of("获取物理主机性能数据成功");
            return toPageGourdResponse(request, response,
                    bmsResult.getJSONObject("body").getInteger("totalCount"),
                    bmsResult.getJSONObject("body").getInteger("size"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取裸金属性能数据!"), e);
        }
    }

}
