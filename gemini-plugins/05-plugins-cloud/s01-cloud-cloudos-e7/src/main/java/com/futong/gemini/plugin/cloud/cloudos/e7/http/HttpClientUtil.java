package com.futong.gemini.plugin.cloud.cloudos.e7.http;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e7.utils.EncryptUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.utils.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AccessToken;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AppToken;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AppTokenParam;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import java.net.URL;
import java.security.cert.X509Certificate;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;

@Slf4j
public class HttpClientUtil {

    private static final String HTTPS = "https";

    public static AccessToken getToken(CloudAccessBean bean) {
        AccessToken token =null;
        try {
            token = (AccessToken) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, bean.getCmpId());
        }
        if (ObjectUtil.isNull(token)) {
            token = getAccessToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}", JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, bean.getCmpId(), token);
        }
        return token;
    }

    public static AccessToken getAccessToken(CloudAccessBean bean) {
        try {
            log.info("获取accessToken参数为：{}",JSON.toJSONString(bean));
            String appTokenUrl = URLUtils.bean.makeUrl(bean, URLUtils.bean.getAppTokenUrl(bean.getScvmmRole()),null);
            AppTokenParam appTokenParam = new AppTokenParam();
            JSONObject jsonObject = JSON.parseObject(bean.getJsonStr());
            appTokenParam.setAppId(EncryptUtils.encodeToSHA256(jsonObject.getString("appId")));
            appTokenParam.setAppKey(EncryptUtils.encodeToSHA256(bean.getUsername()));
            appTokenParam.setAppSecret(EncryptUtils.encodeToSHA256(bean.getPassword()));
            Instant twoMinutesAgo = Instant.now().minus(2, ChronoUnit.MINUTES);
            String timestamp = String.valueOf(twoMinutesAgo.toEpochMilli());
            appTokenParam.setTimestamp(timestamp);
            return fetchToken(appTokenUrl,JSON.toJSONString(appTokenParam),HttpClientConfig.class.newInstance(),bean);
        } catch (Exception e) {
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
    }

    public static AccessToken fetchToken(String url, String json, HttpClientConfig config,CloudAccessBean bean) {
        AccessToken accessToken = null;
        log.info("获取appToken的参数为：{}",json);
        String response = HttpClientUtil.post(url,json,config);
        AppToken appToken = JSON.parseObject(response, AppToken.class);
        if(ObjectUtil.isNotNull(appToken.getRes())){
            String accessTokenUrl = URLUtils.bean.makeUrl(bean, URLUtils.bean.getAccessTokenUrl(bean.getScvmmRole()),null);
            String accessResponse = HttpClientUtil.post(accessTokenUrl,JSON.toJSONString(appToken.getRes()),config);
            accessToken = JSON.parseObject(accessResponse, AccessToken.class);
        }
        return accessToken;
    }

    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }


    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @return
     */
    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            URL urlObj = new URL(url);
            if (url.toLowerCase().startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }

    private static String getResponseStr(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        String entity = EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");
        return entity;
    }
}
