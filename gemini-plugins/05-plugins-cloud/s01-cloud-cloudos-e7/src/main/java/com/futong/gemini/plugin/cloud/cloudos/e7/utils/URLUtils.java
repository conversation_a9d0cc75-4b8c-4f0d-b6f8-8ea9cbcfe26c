package com.futong.gemini.plugin.cloud.cloudos.e7.utils;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;

public class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取appToken（临时令牌）*/
    private String[] appTokenUrl = { "/open-center/sys/token/app" };
    public String[] getAppTokenUrl(String prefix) {
        return getCommonUrl(prefix,appTokenUrl);
    }

    /**获取accessToken（访问令牌）*/
    private String[] accessTokenUrl = { "/open-center/sys/token/access" };
    public String[] getAccessTokenUrl(String prefix) {
        return getCommonUrl(prefix,accessTokenUrl);
    }

    private String[] capacityResourceUrl = { "/ops/volacct/volacct/overview/v1/getComputeInformation?Action=ComputeInformation" };
    public String[] getCapacityResourceUrl(String prefix) {
        return getCommonUrl(prefix,capacityResourceUrl);
    }

    private String[] storageResourceUrl = {"/ops/volacct/volacct/overview/v1/getStorageResourceInformation?Action=StorageResourceInformation"};
    public String[] getStorageResourceUrl(String prefix) {
        return getCommonUrl(prefix,storageResourceUrl);
    }

    public String[] getCommonUrl(String prefix,String[] url) {
        if(prefix==null||"/".equals(prefix)) {
            return url.clone();
        }else {
            String s = url[0];
            if (!s.startsWith(prefix)) {
                if (prefix != null && prefix.length() > 0) {
                    s = prefix + s;
                }
                url[0] = s;
            }
        }
        return url.clone();
    }

    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }

    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }
}
