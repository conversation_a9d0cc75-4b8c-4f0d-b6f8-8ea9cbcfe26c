package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeNatResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNatResponseBody body;

    @Data
    public static class DescribeNatResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeNatResponseBodyData res;

    }

    @Data
    public static class DescribeNatResponseBodyData extends TeaModel {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Long total;
        @NameInMap("Data")
        public List<DescribeNatResponseBodyDataAz> data;
    }

    @Data
    public static class DescribeNatResponseBodyDataAz extends TeaModel {
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("AzoneId")
        public String azoneId;
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("Status")
        public String status;
        @NameInMap("VpcId")
        public String vpcId;
        @NameInMap("VpcName")
        public String vpcName;
        @NameInMap("CreateTime")
        public String createTime;
        @NameInMap("UpdateTime")
        public String updateTime;
        @NameInMap("InstanceCode")
        public String instanceCode;
        @NameInMap("Description")
        public String description;
        @NameInMap("InstanceOperationInfo")
        public InstanceOperationInfo instanceOperationInfo;
        @NameInMap("Eips")
        public List<Eips> eips;
        @NameInMap("InstanceOperationType")
        public Object instanceOperationType;
        @NameInMap("EipList")
        public List<String> eipList;
    }


    @Data
    public static class InstanceOperationInfo extends TeaModel {
        @NameInMap("CreateTime")
        public Long createTime;
    }

    @Data
    public static class Eips extends TeaModel {
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("Bandwidth")
        public Integer bandwidth;
        @NameInMap("Level")
        public Integer level;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("IpAddress")
        public String ipAddress;
    }

}
