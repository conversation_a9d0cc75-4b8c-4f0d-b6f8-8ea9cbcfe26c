package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeNatDnatResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNatDnatResponseBody body;

    @Data
    public static class DescribeNatDnatResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeNatDnatResponseBodyData res;

    }

    @Data
    public static class DescribeNatDnatResponseBodyData extends TeaModel {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Long total;
        @NameInMap("Data")
        public List<DescribeNatDnatResponseBodyDataAz> data;
    }

    @Data
    public static class DescribeNatDnatResponseBodyDataAz extends TeaModel {
        @NameInMap("UserId")
        public String userId;
        @NameInMap("DnatId")
        public String dnatId;
        @NameInMap("NatId")
        public String natId;
        @NameInMap("EipId")
        public String eipId;
        @NameInMap("EipAddr")
        public String eipAddr;
        @NameInMap("EipPortNum")
        public Integer eipPortNum;
        @NameInMap("FixedPortId")
        public String fixedPortId;
        @NameInMap("OwnerEcsId")
        public String ownerEcsId;
        @NameInMap("FixedIpAddr")
        public String fixedIpAddr;
        @NameInMap("FixedPortNum")
        public Integer fixedPortNum;
        @NameInMap("Protocal")
        public String protocal;
        @NameInMap("Status")
        public String status;
    }

}
