package com.futong.gemini.plugin.cloud.cloudos.e7.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.ClientUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.CloudClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.RunInstanceResponse;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.model.NetCardBean;
import com.futong.gemini.plugin.cloud.sdk.service.NetCardService;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CloudService {

    public static <Q, R, C> FTAction<BaseCloudRequest> toFTAction(FTExecute<C, Q, R> exec) {
        return (BaseCloudRequest body) -> doAction(body, exec);
    }

    public static <Q, R, C> BaseDataResponse<R> doAction(BaseCloudRequest request, FTExecute<C, Q, R> exec) {
        try {
            return new BaseDataResponse<>(CloudClient.client.execute(request.getBody(), exec));
        } catch (Exception e) {
            String message = request.getAction().operationType().cname() + request.getAction().resourceType().name() + "失败";
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }


    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("Page")) {
            request.getBody().getCloud().put("Page", 1);
        }
        if (!request.getBody().getCloud().containsKey("Size")) {
            request.getBody().getCloud().put("Size", 50);
        }
        return true;
    }

    public static boolean defaultPage20(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("Page")) {
            request.getBody().getCloud().put("Page", 1);
        }
        if (!request.getBody().getCloud().containsKey("Size")) {
            request.getBody().getCloud().put("Size", 20);
        }
        return true;
    }

    public static boolean defaultPage50Low(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("page")) {
            request.getBody().getCloud().put("page", 1);
        }
        if (!request.getBody().getCloud().containsKey("size")) {
            request.getBody().getCloud().put("size", 50);
        }
        return true;
    }
    public static boolean defaultPage100Low(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("page")) {
            request.getBody().getCloud().put("page", 1);
        }
        if (!request.getBody().getCloud().containsKey("size")) {
            request.getBody().getCloud().put("size", 100);
        }
        return true;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", DateUtil.offsetDay(new Date(), -1).getTime());
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", System.currentTimeMillis());
        }
        return true;
    }

    public static boolean defaultStartEndTime(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("period")) {
            request.getBody().getCloud().put("period", "300");
        }
        if (!request.getBody().getCloud().containsKey("start")) {
            //当前时间-监控数据得统计周期（秒）*1000（毫秒）
            long startTime = System.currentTimeMillis() - NumberUtil.parseLong(request.getBody().getCloud().getString("period")) * 1000;
            request.getBody().getCloud().put("start", Long.toString(startTime));
        }
        if (!request.getBody().getCloud().containsKey("end")) {
            request.getBody().getCloud().put("end", System.currentTimeMillis() + "");
        }
        return true;
    }

    public static boolean toBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        //为0非批量创建云主机,不做处理
        if (resNum == null || resNum == 0) return true;
        JSONObject cloud = request.getBody().getCloud();
        String instanceName = cloud.getString("instanceName");
        String hostName = cloud.getString("hostName");
        cloud.put("instanceName", instanceName + "-" + resNum);
        cloud.put("hostName", hostName + "-" + resNum);
        cloud.put("baseQuantity", 1);
        return true;
    }

    public static boolean toBmsBeforeBiz(BaseCloudRequest request) {
        //无业务信息直接返回
        if (CollUtil.isEmpty(request.getBody().getBiz())) return true;
        //业务批量创建云主机的批次顺序号
        Integer resNum = request.getBody().getBiz().getInteger("resNum");
        //为0非批量创建云主机,不做处理
        if (resNum == null || resNum == 0) return true;
        JSONObject cloud = request.getBody().getCloud();
        String instanceName = cloud.getString("instanceName");
        String hostName = cloud.getString("hostName");
        cloud.put("instanceName", instanceName + "-" + resNum);
        cloud.put("hostName", hostName + "-" + resNum);
        cloud.put("amount", 1);
        return true;
    }

    public static void toAfterBizResId(BaseCloudRequest request, BaseResponse response) {
        if (BaseResponse.SUCCESS.isNotExt(response)) {
            return;
        }
        if (response instanceof BaseDataResponse) {
            BaseDataResponse dataResponse = (BaseDataResponse) response;
            Object data = dataResponse.getData();
            if (dataResponse.getData() instanceof RunInstanceResponse) {
                RunInstanceResponse runInstancesResponse = (RunInstanceResponse) data;
                if (runInstancesResponse.body == null
                        || runInstancesResponse.body.instanceIds == null
                        || CollUtil.isEmpty(runInstancesResponse.body.instanceIds)) {
                    return;
                }
                JSONObject biz = request.getBody().getBiz();
                biz.put("resId",IdUtils.encryptId(request.getBody().getAccess().getCmpId(), runInstancesResponse.body.instanceIds.get(0)));
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            }
        }
    }

    public static boolean toCIPortId(BaseCloudRequest request) {
        if (StrUtil.isNotEmpty(request.getBody().getCloud().getString("PortId"))) {
            return true;
        }
        String instanceResId = request.getBody().getCi().getString("resId");
        if (StrUtil.isEmpty(instanceResId)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的云主机ID为空!");
        }
        List<NetCardBean> netCardBeanList = NetCardService.getNetCardsByInstanceId(instanceResId);
        if (CollUtil.isEmpty(netCardBeanList)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的云主机网卡ID为空!");
        }
        String PortId = netCardBeanList.stream().map(t -> t.getOpenId()).collect(Collectors.joining(","));
        request.getBody().getCloud().put("PortId", PortId);
        return true;
    }

    public static boolean toModelPortId(BaseCloudRequest request) {
        if (StrUtil.isNotEmpty(request.getBody().getCloud().getString("PortId"))) {
            return true;
        }
        String instanceResId = request.getBody().getModel().getString("resId");
        if (StrUtil.isEmpty(instanceResId)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的云主机ID为空!");
        }
        List<NetCardBean> netCardBeanList = NetCardService.getNetCardsByInstanceId(instanceResId);
        if (CollUtil.isEmpty(netCardBeanList)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "云操作对应的云主机网卡ID为空!");
        }
        String PortId = netCardBeanList.stream().map(t -> t.getOpenId()).collect(Collectors.joining(","));
        request.getBody().getCloud().put("PortId", PortId);
        return true;
    }

    public static boolean defaultRegion(BaseCloudRequest request) {
        request.getBody().getCloud().put("RegionId","region-ys-zz");
        return true;
    }
}
