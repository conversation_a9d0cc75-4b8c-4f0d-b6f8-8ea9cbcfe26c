package com.futong.gemini.plugin.cloud.cloudos.e7.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppToken implements Serializable {

    private boolean status;
    private boolean auth;
    private String code;
    private Res res;
    private String msg;

    @Data
    public static class Res {
        private String appToken;
    }
}
