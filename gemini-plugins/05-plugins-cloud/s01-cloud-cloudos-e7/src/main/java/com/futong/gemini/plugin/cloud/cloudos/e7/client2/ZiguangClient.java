package com.futong.gemini.plugin.cloud.cloudos.e7.client2;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.utils.EncryptUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;

@Slf4j
public class ZiguangClient {
    protected final CloseableHttpClient httpClient;
    protected final ConnectionConfig config;
    private final String X_AUTH_TOKEN = "x-access-token";
    private final String X_TIME_STAMP = "x-time-stamp";
    private final String X_REQUEST_REGION = "X-Request-Region";

    public ZiguangClient(ConnectionConfig config) {
        this.httpClient = createHttpClient(config);
        this.config = config;
    }

    private CloseableHttpClient createHttpClient(ConnectionConfig config) {
        RequestConfig.Builder requestConfigBuilder = RequestConfig.custom()
                .setConnectTimeout(config.getConnectTimeout())
                .setSocketTimeout(config.getSocketTimeout());
        // 判断是否配置了代理
        if (config.getProxyHost() != null && !config.getProxyHost().isEmpty() && config.getProxyPort() > 0) {
            requestConfigBuilder.setProxy(new HttpHost(config.getProxyHost(), config.getProxyPort()));
        }
        RequestConfig requestConfig = requestConfigBuilder.build();
        try {
            // 忽略SSL证书验证
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            return HttpClientBuilder.create()
                    .setDefaultRequestConfig(requestConfig)
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client with SSL context", e);
        }
    }

    public String getToken() {
        Object token = null;
        String key = config.getAddress();
        try {
            token = GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, key);
        } catch (Exception e) {
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, key);
        }
        if (ObjectUtil.isNull(token)) {
            try {
                token = doToken();
            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, key, token);
        }
        return token.toString();
    }

    public synchronized String doToken() throws Exception {
        String appToken = doAppToken();
        JSONObject accessTokenRes = doAccessToken(appToken);
        return accessTokenRes.getString("accessToken");
    }

    public synchronized String doAppToken() throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getAddress() + "/open-center/sys/token/app";
        Instant twoMinutesAgo = Instant.now().minus(2, ChronoUnit.MINUTES);
        String timestamp = String.valueOf(twoMinutesAgo.toEpochMilli());
        String authJson = String.format("{" +
                        "\"appId\": \"%s\"," +
                        "\"appKey\": \"%s\"," +
                        "\"appSecret\": \"%s\"," +
                        "\"timestamp\": \"%s\"" +
                        "}",
                EncryptUtils.encodeToSHA256(auth.getAppId()),
                EncryptUtils.encodeToSHA256(auth.getUsername()),
                EncryptUtils.encodeToSHA256(auth.getPassword()),
                timestamp);
        log.info("调用云上接口获取AppToken认证信息url  = {},authJson={}", url, authJson);
        HttpPost request = new HttpPost(url);
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        request.setEntity(new StringEntity(authJson));
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("Authentication failed: " +
                        EntityUtils.toString(response.getEntity()));
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != 0) {
                String msg = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, msg);
            }
            return JSONPath.read(responseBody, "$.res.appToken", String.class);
        }
    }

    public synchronized JSONObject doAccessToken(String appToken) throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getAddress() + "/open-center/sys/token/access";
        String authJson = String.format("{" +
                        "\"appToken\": \"%s\"" +
                        "}",
                appToken);
        log.info("调用云上接口获取AccessToken认证信息url  = {},authJson={}", url, authJson);
        HttpPost request = new HttpPost(url);
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        request.setEntity(new StringEntity(authJson));
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("Authentication failed: " +
                        EntityUtils.toString(response.getEntity()));
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != 0) {
                String msg = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, msg);
            }
            return JSONPath.read(responseBody, "$.res", JSONObject.class);
        }
    }

    public synchronized JSONObject doRenewAccessToken(String appToken) throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getAddress() + "/open-center/sys/token/renew";
        String authJson = String.format("{" +
                        "\"appToken\": \"%s\"" +
                        "}",
                appToken);
        log.info("调用云上接口续期AccessToken认证信息url  = {},authJson={}", url, authJson);
        HttpPost request = new HttpPost(url);
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        request.setEntity(new StringEntity(authJson));
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("Authentication failed: " +
                        EntityUtils.toString(response.getEntity()));
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != 0) {
                String msg = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, msg);
            }
            return JSONPath.read(responseBody, "$.res", JSONObject.class);
        }
    }

    // 通用请求执行方法
    private String executeRequest(HttpRequestBase request, String regionId) {
        String token;
        try {
            token = getToken();
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
        Instant twoMinutesAgo = Instant.now().minus(2, ChronoUnit.MINUTES);
        String timestamp = String.valueOf(twoMinutesAgo.toEpochMilli());
        request.setHeader(X_REQUEST_REGION, regionId);
        log.info("调用云上接口url  = {},token={},timestamp={}", request.getURI(), token, timestamp);
        request.setHeader(X_AUTH_TOKEN, token);
        request.setHeader(X_TIME_STAMP, timestamp);
        //设置超时时间
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            if (response.getStatusLine().getStatusCode() < 200 ||
                    response.getStatusLine().getStatusCode() >= 300) {
                throw new RuntimeException("API request failed: " + responseBody);
            }
            Integer errorCode = JSONPath.read(responseBody, "$.errcode", Integer.class);
            if (errorCode != null) {
                log.error("请求失败:{}", responseBody);
                String message = JSONPath.read(responseBody, "$.errmsg", String.class);
                //{"errcode":"40001","errmsg":"wrong accessToken"}
                if(errorCode.equals(40001)){
                    log.info("accessToken已过期,重新获取");
                    //移除缓存token
                    GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, config.getAddress());
                }
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("调用云接口响应错误信息:" + message));
            }
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != null && code != 0) {
                log.error("请求失败:{}", responseBody);
                String message = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("调用云接口响应错误信息:" + message));
            }
            return responseBody;
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("发送云操作请求信息失败!"), e);
        }
    }

    // GET请求
    public String doGet(String apiPath, Map<String, ?> params, String regionId) {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String url = config.getAddress() + apiPath;
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(query);
        }
        return executeRequest(new HttpGet(url), regionId);
    }

    // GET请求
    public String doDelete(String apiPath, Map<String, ?> params, String regionId) {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String url = config.getAddress() + apiPath;
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(query);
        }
        return executeRequest(new HttpDelete(url), regionId);
    }

    // POST JSON请求
    public String doPost(String apiPath, JSONObject requestBody, String regionId) {
        String url = config.getAddress() + apiPath;
        HttpPost request = new HttpPost(url);
        String json = requestBody.toJSONString();
        try {
            request.setEntity(new StringEntity(json, "UTF-8"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, "设置POST请求body信息失败");
        }
        return executeRequest(request, regionId);
    }

    // POST JSON请求
    public String doPut(String apiPath, JSONObject requestBody, String regionId) {
        String url = config.getAddress() + apiPath;
        HttpPut request = new HttpPut(url);
        String json = requestBody.toJSONString();
        try {
            request.setEntity(new StringEntity(json, "UTF-8"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, "设置Put请求body信息失败");
        }
        return executeRequest(request, regionId);
    }

    public JSONObject doPostJSON(String apiPath, JSONObject requestBody, String regionId) {
        String result = doPost(apiPath, requestBody, regionId);
        return JSONObject.parseObject(result);
    }

    public JSONObject doPutJSON(String apiPath, JSONObject requestBody, String regionId) {
        String result = doPut(apiPath, requestBody, regionId);
        return JSONObject.parseObject(result);
    }

    public JSONObject doDeleteJSON(String apiPath, Map<String, ?> params, String regionId) {
        String result = doDelete(apiPath, params, regionId);
        return JSONObject.parseObject(result);
    }

    public JSONObject doGetJSON(String apiPath, Map<String, ?> params, String regionId) {
        String result = doGet(apiPath, params, regionId);
        return JSONObject.parseObject(result);
    }

    public Object doGetDataByType(String apiPath, Map<String, ?> params, String type, String regionId) {
        StrUtil.emptyToDefault(type, "null");
        String lowerCase = type.toLowerCase();
        switch (lowerCase) {
            case "json":
                return doGetResJSON(apiPath, params, regionId);
            case "string":
                return doGetDataStr(apiPath, params, regionId);
            case "jsonarray":
                return doGetDataJSONArray(apiPath, params, regionId);
            default:
                return doGetJSON(apiPath, params, regionId);
        }
    }

    public JSONObject doGetResJSON(String apiPath, Map<String, ?> params, String regionId) {
        JSONObject result = doGetJSON(apiPath, params, regionId);
        return result.getJSONObject("res");
    }

    public String doGetDataStr(String apiPath, Map<String, ?> params, String regionId) {
        JSONObject result = doGetJSON(apiPath, params, regionId);
        return result.getString("data");
    }

    public JSONArray doGetDataJSONArray(String apiPath, Map<String, ?> params, String regionId) {
        JSONObject result = doGetJSON(apiPath, params, regionId);
        return result.getJSONArray("data");
    }

    public static void main(String[] args) throws Exception {
        ConnectionConfig config = new ConnectionConfig.Builder()
                .protocol("https")
                .host("**************")
                .port("30990")
                .authConfig(new AuthConfig.Builder()
                        .appId("unif123d7759182440b866d5f6272b6690c")
                        .username("atSTZN0yughNTBZw")
                        .password("OcI2jFsBIMdwNXuYG7VvHDA0H4va8t").build())
//                .proxy("***********", 3128)
                .build();
        ZiguangClient client = new ZiguangClient(config);
        JSONObject json = JSONArray.parseObject("{\n" +
                "\t\"query\": \"avg_over_time(node_cpu_usage_avg{model_code=\\\"server\\\",region=\\\"region-ys-zz\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}[5m]) " +
                " or avg_over_time(node_disk_usage_avg{model_code=\\\"server\\\",region=\\\"region-ys-zz\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}[5m])" +
                " or avg_over_time(node_memory_usage_avg{model_code=\\\"server\\\",region=\\\"region-ys-zz\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}[5m])\",\n" +
                "\t\"start\": \"1753693066\",\n" +
                "\t\"end\": \"1753697485\"," +
                "\t\"step\": 300" +
                "}");
        JSONObject json1 = JSONArray.parseObject("{\n" +
                "\t\"query\": \"avg_over_time(node_cpu_usage_avg{model_code=\\\"server\\\",region=\\\"region-ys-zz\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}[15m])\",\n" +
                "\t\"start\": \""+(System.currentTimeMillis()/1000-900)+"\",\n" +
                "\t\"end\": \""+(System.currentTimeMillis()/1000)+"\",\n" +
                "\t\"step\": 120" +
                "}");
        JSONObject json2 = JSONArray.parseObject("{\n" +
                "\t\"query\": \"node_cpu_usage_avg{model_code=\\\"server\\\",region=\\\"region-ys-zz\\\",group=~\\\".*cvk\\\",group!~\\\"(mgt|dmz).*cvk\\\"}\",\n" +
                "\t\"time\": \""+(System.currentTimeMillis()/1000)+"\"\n" +
//                "\t\"end\": \""+System.currentTimeMillis()/1000+"\",\n" +
//                "\t\"step\": 120" +
                "}");
//        JSONObject json = JSONObject.parseObject("{\"page\":1,\"size\":500}");
//        JSONObject s1 = client.doGetJSON("/monitor/open/open/queryRange", json1, "region-ys-zz");
        JSONObject s2 = client.doGetJSON("/monitor/open/open/query", json2, "region-ys-zz");
//        JSONObject s = client.doGetJSON("/monitor/open/open/express", json, "region-ys-zz");
//        System.out.println(s1);
        System.out.println(s2);
//        System.out.println(s.getJSONObject("data").getJSONArray("result").size());

    }
}
