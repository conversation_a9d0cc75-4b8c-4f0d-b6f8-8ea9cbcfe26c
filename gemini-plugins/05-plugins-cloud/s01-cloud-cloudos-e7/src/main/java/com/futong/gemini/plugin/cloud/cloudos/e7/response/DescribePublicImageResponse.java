package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribePublicImageResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribePublicImageResponse body;

    @Data
    public static class DescribePublicImageResponseBody extends BaseResponseBody {
        @NameInMap("Data")
        public List<DescribePublicImageResponseBodyData> data;
    }

    @Data
    public static class DescribePublicImageResponseBodyData extends TeaModel {
        @NameInMap("ComponentCode")
        public String componentCode;
        @NameInMap("ComponentName")
        public String componentName;
        @NameInMap("SpecificationInfos")
        public List<DescribePublicImageResponseBodyDataInfos> specificationInfos;

    }

    @Data
    public static class DescribePublicImageResponseBodyDataInfos extends TeaModel  {
        @NameInMap("SpecificationClassCode")
        public String specificationClassCode;
        @NameInMap("SpecificationClassName")
        public String specificationClassName;
        @NameInMap("SpecificationName")
        public String specificationName;
        @NameInMap("SpecificationCode")
        public String specificationCode;
        @NameInMap("ChargeUnit")
        public String chargeUnit;
        @NameInMap("Describe")
        public String describe;
        @NameInMap("SpecificAttribute")
        public DescribePublicImageResponseBodyDataInfosAttribute specificAttribute;
    }

    @Data
    public static class DescribePublicImageResponseBodyDataInfosAttribute extends TeaModel{
        @NameInMap("memory")
        public String memory;
        @NameInMap("memory_unit")
        public String memory_unit;
        @NameInMap("vCPU")
        public String vCPU;
        @NameInMap("ioQos")
        public String ioQos;
        @NameInMap("vCPU_unit")
        public String vCPU_unit;
        @NameInMap("ioQos_unit")
        public String ioQos_unit;
        @NameInMap("wrBps_unit")
        public String wrBps_unit;
        @NameInMap("wrBps")
        public String wrBps;
        @NameInMap("wrIops_unit")
        public String wrIops_unit;
        @NameInMap("wrIops")
        public String wrIops;
        @NameInMap("gpu_type")
        public String gpu_type;
        @NameInMap("gpu_count_unit")
        public String gpu_count_unit;
        @NameInMap("gpu_count")
        public String gpu_count;
        @NameInMap("isShared")
        public String isShared;
    }
}
