package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

public class SourceOperationClient extends ZiguangClient{

    public static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("ProductCode", "VM")));

    public SourceOperationClient(Config config) throws Exception {
        super(config);
    }

    /**
     * 创建云主机
     *
     * @throws Exception
     */
    public RunInstanceResponse runInstance(Map<String, Object> request) throws Exception {
        String json = JSONObject.toJSONString(request.get("systemDisks"));
        JSONObject jsonObject = JSON.parseObject(json);
        String sysDiskSpecificationCode = jsonObject.getString("sysDiskSpecificationCode");
        String sysDiskSize =jsonObject.getString("sysDiskSize");
        request.put("sysDiskSpecificationCode",sysDiskSpecificationCode);
        request.put("sysDiskSize",sysDiskSize);
        request.remove("systemDisks");
        if(ObjectUtil.isEmpty(request.get("description"))){
            request.remove("description");
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("RunEcs", "2020-07-30", "HTTPS", "POST", "AK", "/compute/ecs/instances", "json", req, runtime, null), new RunInstanceResponse());
    }

    /**
     * 开启云主机
     *
     * @throws Exception
     */
    public StartInstanceResponse startInstance(Map<String, Object> request) throws Exception {
        StartInstanceResponse instanceResponse = new StartInstanceResponse();
        StartInstanceResponse.StartInstanceResponseBody body = new StartInstanceResponse.StartInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
//        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            StartInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("StartEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new StartInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 关闭云主机
     *
     * @throws Exception
     */
    public StopInstanceResponse stopInstance(Map<String, Object> request) throws Exception {
        StopInstanceResponse.StopInstanceResponseBody body = new StopInstanceResponse.StopInstanceResponseBody();
        StopInstanceResponse instanceResponse = new StopInstanceResponse();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
//        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            StopInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("StopEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new StopInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 重启云主机
     *
     * @throws Exception
     */
    public RebootInstanceResponse rebootInstance(Map<String, Object> request) throws Exception {
        RebootInstanceResponse instanceResponse = new RebootInstanceResponse();
        RebootInstanceResponse.RebootInstanceResponseBody body = new RebootInstanceResponse.RebootInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
//        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            RebootInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("RebootEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new RebootInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 删除云主机
     *
     * @throws Exception
     */
    public DeleteInstanceResponse deleteInstance(Map<String, Object> request) throws Exception {
        DeleteInstanceResponse instanceResponse = new DeleteInstanceResponse();
        DeleteInstanceResponse.DeleteInstanceResponseBody body = new DeleteInstanceResponse.DeleteInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
//        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            DeleteInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("DeleteEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DeleteInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 修改云主机名称
     *
     * @throws Exception
     */
    public UpdateEcsNameResponse updateEcsName(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateEcsName", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new UpdateEcsNameResponse());
    }

    /**
     * 修改云主机规格
     *
     * @throws Exception
     */
    public UpdateEcsFlaverResponse updateEcsFlaver(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateEcsConfig", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new UpdateEcsFlaverResponse());
    }


    /**
     * 修改云主机密码
     *
     * @throws Exception
     */
    public ResetEcsPasswordResponse resetEcsPassword(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ResetEcsPassword", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new ResetEcsPasswordResponse());
    }

    /**
     * 创建安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse createSecurityGroup(Map<String, Object> request) throws Exception {
        String description = request.get("Description").toString();
        if(StrUtil.isEmpty(description)){
            request.remove("Description");
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateSecurityGroup", "2020-07-30", "HTTPS", "POST", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 修改安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse modifySecurityGroupAttribute(Map<String, Object> request) throws Exception {
        request.put("DefaultPermit", false);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateSecurityGroup", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 删除安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse deleteSecurityGroup(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DeleteSecurityGroup", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 绑定安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse bindSecurityGroup(Map<String, Object> request) throws Exception {
        request.put("InstanceType","ECS");
        List<String> sgIdList = (List)request.get("SgIds");
        request.remove("SgIds");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        req.setBody(sgIdList);
        return TeaModelExt.toModel(this.doRPCRequest2("BindSecurityGroup", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 解绑安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse unBindSecurityGroup(Map<String, Object> request) throws Exception {
        request.put("InstanceType","ECS");
        List<String> sgIdList = (List)request.get("SgIds");
        request.remove("SgIds");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        req.setBody(sgIdList);
        return TeaModelExt.toModel(this.doRPCRequest2("UnbindSecurityGroup", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 创建安全组规则
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse createSecurityGroupRule(Map<String, Object> request) throws Exception {
        String ruleJson = JSON.toJSONString(request.get("ruleList"));
        Map<String,Object> ruleMap = JSON.parseObject(ruleJson).toJavaObject(Map.class);
        List<Map<String,Object>> ruleList = new ArrayList<>();
        ruleList.add(ruleMap);
        request.remove("ruleList");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        req.setBody(ruleList);
        return TeaModelExt.toModel(this.doRPCRequest2("CreateSecurityGroupRule", "2020-07-30", "HTTPS", "POST", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 删除安全组规则
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse deleteSecurityGroupRule(Map<String, Object> request) throws Exception {
        List<String> SecurityGroupRuleIds = (List) request.get("SecurityGroupRuleIds");
        request.remove("SecurityGroupRuleIds");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        req.setBody(SecurityGroupRuleIds);
        return TeaModelExt.toModel(this.doRPCRequest2("DeleteSecurityGroupRule", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 创建密钥对
     *
     * @throws Exception
     */
    public CreateKeyPairsResponse createKeyPair(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateKeyPair", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new CreateKeyPairsResponse());
    }

    /**
     * 删除密钥对
     *
     * @throws Exception
     */
    public CreateKeyPairsResponse deleteKeyPairs(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DeleteKeyPairs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new CreateKeyPairsResponse());
    }

    /**
     * 创建云盘
     *
     * @throws Exception
     */
    public CreateDiskResponse createDisk(Map<String, Object> request) throws Exception {
        if(ObjectUtils.isNull(request.get("Description"))){
            request.remove("Description");
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateDisk", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new CreateDiskResponse());
    }

    /**
     * 删除云盘
     *
     * @throws Exception
     */
    public DeleteDiskResponse deleteDisk(Map<String, Object> request) throws Exception {
        DeleteDiskResponse result = new DeleteDiskResponse();
        List<String> VolumeIdLList  = (List) request.get("VolumeIdLList");
        DeleteDiskResponse.DeleteDiskResponseBody body = new DeleteDiskResponse.DeleteDiskResponseBody();
        request.remove("VolumeIdLList");
        StringBuffer buffer = new StringBuffer();
        for (String VolumeIds : VolumeIdLList) {
            request.put("VolumeIds",VolumeIds);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            DeleteDiskResponse response = TeaModelExt.toModel(this.doRPCRequest("DeleteDisk", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new DeleteDiskResponse());
            buffer.append(response.body.requestId);
        }
        body.requestId = buffer.toString();
        result.setBody(body);
        return result;
    }

    /**
     * 修改云盘
     *
     * @throws Exception
     */
    public ModifyDiskResponse modifyDisk(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ModifyDisk", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new ModifyDiskResponse());
    }

    /**
     * 云主机
     *
     * @throws Exception
     */
    public EcsAttachDiskResponse ecsAttachDisk(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("EcsAttachDisk", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new EcsAttachDiskResponse());
    }

    /**
     * 云主机
     *
     * @throws Exception
     */
    public EcsDetachDiskResponse ecsDetachDisk(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("EcsDetachDisk", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new EcsDetachDiskResponse());
    }

    /**
     * 查询云盘
     *
     * @throws Exception
     */
    public ResizeDiskResponse resizeDisk(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ReSizeDisk", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new ResizeDiskResponse());
    }

    /**
     * 创建快照
     *
     * @throws Exception
     */
    public DescribeSnapshotResponse createSnapshot(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateSnapshot", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new DescribeSnapshotResponse());
    }

    /**
     * 修改快照
     *
     * @throws Exception
     */
    public DescribeSnapshotResponse modifySnapshotAttribute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ModifySnapshot", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new DescribeSnapshotResponse());
    }

    /**
     * 删除快照
     *
     * @throws Exception
     */
    public DescribeSnapshotResponse deleteSnapshot(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DeleteSnapshot", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new DescribeSnapshotResponse());
    }

    /**
     * 删除快照
     *
     * @throws Exception
     */
    public ResetDisksResponse resetDisk(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ResetDisk", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null), new ResetDisksResponse());
    }

    /**
     * 创建VPC
     *
     * @throws Exception
     */
    public DescribeNetworkResponse createVpc(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateVpc", "2020-07-30", "HTTPS", "POST", "AK", "/networks/vpc", "json", req, runtime, null), new DescribeNetworkResponse());
    }

    /**
     * 修改VPC
     *
     * @throws Exception
     */
    public DescribeNetworkResponse modifyVpcAttribute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateVpc", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/vpc", "json", req, runtime, null), new DescribeNetworkResponse());
    }

    /**
     * 删除VPC
     *
     * @throws Exception
     */
    public DescribeNetworkResponse deleteVpc(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DeleteVpc", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/vpc", "json", req, runtime, null), new DescribeNetworkResponse());
    }

    /**
     * 创建子网
     *
     *
     * @throws Exception
     */
    public DescribeNetworkSubnetResponse createSubnet(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateSubnet", "2020-07-30", "HTTPS", "POST", "AK", "/networks/vpc", "json", req, runtime, null), new DescribeNetworkSubnetResponse());
    }

    /**
     * 删除子网
     *
     * @throws Exception
     */
    public DescribeNetworkSubnetResponse deleteSubnet(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteSubnet", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/vpc", "json", req, runtime, null), new DescribeNetworkSubnetResponse());
    }

    /**
     * 创建EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse allocateEipAddress(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("AllocateEip", "2020-07-30", "HTTPS", "POST", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 修改EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse modifyEipAddressAttribute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ModifyEip", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 删除EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse releaseEipAddress(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("ReleaseEip", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 绑定EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse associateEipAddress(Map<String, Object> request) throws Exception {
        request.put("ParentType","ECS");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("AssociateEip", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 解绑EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse unAssociateEipAddress(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UnassociateEip", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 创建NAT网关
     *
     * @throws Exception
     */
    public DescribeNatResponse createNatGateway(Map<String, Object> request) throws Exception {
        String description = request.get("Description").toString();
        List<Map<String,Object>> productProperties = new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        map.put("VpcId",request.get("VpcId"));
        request.remove("VpcId");
        request.remove("Description");
        if(StrUtil.isNotEmpty(description)){
            map.put("Description",description);
        }
        productProperties.add(map);
        request.put("ProductProperties",productProperties);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        System.out.println(JSON.toJSONString(request));
        return TeaModelExt.toModel(this.doRPCRequest("CreateNat", "2020-07-30", "HTTPS", "POST", "AK", "/networks/nat", "json", req, runtime, null), new DescribeNatResponse());
    }

    /**
     * 修改NAT网关
     *
     * @throws Exception
     */
    public DescribeNatResponse modifyNatGatewayAttribute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateNat", "2020-07-30", "HTTPS", "PUT", "AK", "/networks/nat", "json", req, runtime, null), new DescribeNatResponse());
    }

    /**
     * 删除NAT网关
     *
     * @throws Exception
     */
    public DescribeNatResponse deleteNatGateway(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DeleteNat", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/nat", "json", req, runtime, null), new DescribeNatResponse());
    }

    /**
     * 创建镜像
     *
     * @throws Exception
     */
    public DescribeImageResponse createImage(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        if(ObjectUtils.isNull(request.get("Description"))){
            request.remove("Description");
        }
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("CreateImage", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeImageResponse());
    }

    /**
     * 删除镜像
     *
     * @throws Exception
     */
    public DescribeImageResponse deleteImage(Map<String, Object> request) throws Exception {
        StringBuffer buffer = new StringBuffer();
        DescribeImageResponse result = new DescribeImageResponse();
        List<String>  imageIds = (List) request.get("ImageIds");
        request.remove("ImageIds");
        for (String imageId : imageIds) {
            request.put("ImageId",imageId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            DescribeImageResponse response = TeaModelExt.toModel(this.doRPCRequest("DeleteImages", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeImageResponse());
            buffer.append(response.body.requestId);
        }
        DescribeImageResponse.DescribeImageResponseBody body = new DescribeImageResponse.DescribeImageResponseBody();
        body.requestId = buffer.toString();
        result.setBody(body);
        return null;
    }


    /**
     * 创建路由
     *
     * @throws Exception
     */
    public DescribeRouteTableListResponse createVpcRoute(Map<String, Object> request) throws Exception {
        String description = request.get("Description").toString();
        if(StrUtil.isEmpty(description)){
            request.remove("Description");
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("CreateVpcRoute", "2020-07-30", "HTTPS", "POST", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeRouteTableListResponse());
    }

    /**
     * 删除路由
     *
     * @throws Exception
     */
    public DescribeRouteTableListResponse deleteVpcRoute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DeleteVpcRoute", "2020-07-30", "HTTPS", "DELETE", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeRouteTableListResponse());
    }

    /**
     * 创建路由
     *
     * @throws Exception
     */
    public DescribeRouteTableListResponse modifyVpcRoute(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("ModifyVpcRoute", "2020-07-30", "HTTPS", "POST", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeRouteTableListResponse());
    }

    /**
     * 创建裸金属
     *
     * @throws Exception
     */
    public RunInstanceResponse runBms(Map<String, Object> request) throws Exception {
        if(ObjectUtil.isEmpty(request.get("description"))){
            request.remove("description");
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("RunBms", "2020-07-30", "HTTPS", "POST", "AK", "/compute/bms", "json", req, runtime, null), new RunInstanceResponse());
    }


    /**
     * 修改裸金属名称
     *
     * @throws Exception
     */
    public UpdateEcsNameResponse updateBmsName(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("UpdateBmsName", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null), new UpdateEcsNameResponse());
    }

    /**
     * 删除裸金属
     *
     * @throws Exception
     */
    public DeleteInstanceResponse deleteBms(Map<String, Object> request) throws Exception {
        DeleteInstanceResponse instanceResponse = new DeleteInstanceResponse();
        DeleteInstanceResponse.DeleteInstanceResponseBody body = new DeleteInstanceResponse.DeleteInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            DeleteInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("DeleteBms", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null), new DeleteInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 开启裸金属
     *
     * @throws Exception
     */
    public StartInstanceResponse startBms(Map<String, Object> request) throws Exception {
        StartInstanceResponse instanceResponse = new StartInstanceResponse();
        StartInstanceResponse.StartInstanceResponseBody body = new StartInstanceResponse.StartInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            StartInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("StartBms", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null), new StartInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 关闭云主机
     *
     * @throws Exception
     */
    public StopInstanceResponse stopBms(Map<String, Object> request) throws Exception {
        StopInstanceResponse.StopInstanceResponseBody body = new StopInstanceResponse.StopInstanceResponseBody();
        StopInstanceResponse instanceResponse = new StopInstanceResponse();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            StopInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("StopBms", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null), new StopInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 重启云主机
     *
     * @throws Exception
     */
    public RebootInstanceResponse rebootBms(Map<String, Object> request) throws Exception {
        RebootInstanceResponse instanceResponse = new RebootInstanceResponse();
        RebootInstanceResponse.RebootInstanceResponseBody body = new RebootInstanceResponse.RebootInstanceResponseBody();
        List<String> InstanceIds = (List)request.get("InstanceIds");
        StringBuffer buffer = new StringBuffer();
        request.remove("InstanceIds");
        for (String InstanceId : InstanceIds) {
            request.put("InstanceId", InstanceId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                    new TeaPair("query", request)
            ));
            RebootInstanceResponse response = TeaModelExt.toModel(this.doRPCRequest("RebootBms", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null), new RebootInstanceResponse());
            buffer.append(response.body.requestId).append(",");
        }
        body.requestId = buffer.toString();
        instanceResponse.setBody(body);
        return instanceResponse;
    }

    /**
     * 修改云主机密码
     *
     * @throws Exception
     */
    public Map<String,?> resetBmsPassword(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return this.doRPCRequest("ResetBmsPassword", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null);
    }

}
