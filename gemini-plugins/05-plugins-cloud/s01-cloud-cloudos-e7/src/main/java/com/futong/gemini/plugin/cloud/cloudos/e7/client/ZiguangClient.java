package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.common.base.Joiner;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class ZiguangClient extends Client {

    public ZiguangClient(Config config) throws Exception {
        super(config);
    }

    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime, String signatureVersion) throws Exception {
        TeaModelExt.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.readTimeout, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                request_.query = TeaConverter.merge(String.class,
                        TeaConverter.buildMap(
                                new TeaPair("Action", action),
                                new TeaPair("Format", "json"),
                                new TeaPair("Version", version),
                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("Timestamp", "2022-11-08T08:45:05Z"),
                                new TeaPair("SignatureNonce", com.aliyun.teautil.Common.getNonce())
//                                new TeaPair("SignatureNonce", "570bd31b-e0e5-4e6e-8600-69e45a2da5dc16678971049331")
                        ),
                        request.query
                );
                Map<String, String> headers = this.getRpcHeaders();
                if (com.aliyun.teautil.Common.isUnset(headers)) {
                    // endpoint is setted in product client
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint),
                            new TeaPair("x-acs-version", version),
                            new TeaPair("x-acs-action", action),
                            new TeaPair("user-agent", this.getUserAgent())
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint),
                                    new TeaPair("x-acs-version", version),
                                    new TeaPair("x-acs-action", action),
                                    new TeaPair("user-agent", this.getUserAgent())
                            ),
                            headers
                    );
                }

                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
                    Map<String, Object> m = com.aliyun.teautil.Common.assertAsMap(request.body);
                    request_.body = Tea.toReadable(com.aliyun.teautil.Common.toJSONString(m));
                    request_.headers.put("content-type", "application/json");
                }

                if (!com.aliyun.teautil.Common.equalString(authType, "Anonymous")) {
                    String accessKeyId = this.getAccessKeyId();
                    String accessKeySecret = this.getAccessKeySecret();
                    String securityToken = this.getSecurityToken();
                    if (!com.aliyun.teautil.Common.empty(securityToken)) {
                        request_.query.put("SecurityToken", securityToken);
                    }

                    request_.query.put("SignatureMethod", "HMAC-SHA1");
                    if (StringUtils.isNotEmpty(signatureVersion)) {
                        request_.query.put("SignatureVersion", signatureVersion);
                    } else {
                        request_.query.put("SignatureVersion", "1.0");
                    }
                    request_.query.put("AccessKeyId", accessKeyId);
                    Map<String, Object> t = null;
                    if (!com.aliyun.teautil.Common.isUnset(request.body)) {
                        t = com.aliyun.teautil.Common.assertAsMap(request.body);
                    }

                    Map<String, String> signedParam = TeaConverter.merge(String.class,
                            request_.query,
                            query(t)
                    );
                    //请求地址需要全参,包含body里得信息
                    request_.query = signedParam;
                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
                }

                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                _lastResponse = response_;

                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    log.info("调用接口失败！状态码：{},返回信息：{}", response_.statusCode, _res.toString());
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                if (com.aliyun.teautil.Common.equalString(bodyType, "binary")) {
                    Map<String, Object> resp = TeaConverter.buildMap(
                            new TeaPair("body", response_.body),
                            new TeaPair("headers", response_.headers)
                    );
                    return resp;
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "byte")) {
                    byte[] byt = com.aliyun.teautil.Common.readAsBytes(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", byt),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }


    public Map<String, ?> doRPCRequest2(String action, String version, String protocol, String method, String authType, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime, String signatureVersion) throws Exception {
        TeaModelExt.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.readTimeout, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                request_.query = TeaConverter.merge(String.class,
                        TeaConverter.buildMap(
                                new TeaPair("Action", action),
                                new TeaPair("Format", "json"),
                                new TeaPair("Version", version),
                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("Timestamp", "2022-11-08T08:45:05Z"),
                                new TeaPair("SignatureNonce", com.aliyun.teautil.Common.getNonce())
//                                new TeaPair("SignatureNonce", "570bd31b-e0e5-4e6e-8600-69e45a2da5dc16678971049331")
                        ),
                        request.query
                );
                Map<String, String> headers = this.getRpcHeaders();
                if (com.aliyun.teautil.Common.isUnset(headers)) {
                    // endpoint is setted in product client
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint),
                            new TeaPair("x-acs-version", version),
                            new TeaPair("x-acs-action", action),
                            new TeaPair("user-agent", this.getUserAgent())
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint),
                                    new TeaPair("x-acs-version", version),
                                    new TeaPair("x-acs-action", action),
                                    new TeaPair("user-agent", this.getUserAgent())
                            ),
                            headers
                    );
                }

                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
//                    Map<String, Object> m = com.aliyun.teautil.Common.assertAsMap(request.body);
                    request_.body = Tea.toReadable(com.aliyun.teautil.Common.toJSONString(request.body));
                    request_.headers.put("content-type", "application/json");
                }

                if (!com.aliyun.teautil.Common.equalString(authType, "Anonymous")) {
                    String accessKeyId = this.getAccessKeyId();
                    String accessKeySecret = this.getAccessKeySecret();
                    String securityToken = this.getSecurityToken();
                    if (!com.aliyun.teautil.Common.empty(securityToken)) {
                        request_.query.put("SecurityToken", securityToken);
                    }

                    request_.query.put("SignatureMethod", "HMAC-SHA1");
                    if (StringUtils.isNotEmpty(signatureVersion)) {
                        request_.query.put("SignatureVersion", signatureVersion);
                    } else {
                        request_.query.put("SignatureVersion", "1.0");
                    }
                    request_.query.put("AccessKeyId", accessKeyId);
                    Map<String, Object> t = null;
                    if (!com.aliyun.teautil.Common.isUnset(request.body)) {
//                        t = com.aliyun.teautil.Common.assertAsMap(request.body);
                    }

                    Map<String, String> signedParam = TeaConverter.merge(String.class,
                            request_.query,
                            query(t)
                    );
                    //请求地址需要全参,包含body里得信息
                    request_.query = signedParam;
                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
                }

                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                _lastResponse = response_;

                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    log.info("调用接口失败！状态码：{},返回信息：{}", response_.statusCode, _res.toString());
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                if (com.aliyun.teautil.Common.equalString(bodyType, "binary")) {
                    Map<String, Object> resp = TeaConverter.buildMap(
                            new TeaPair("body", response_.body),
                            new TeaPair("headers", response_.headers)
                    );
                    return resp;
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "byte")) {
                    byte[] byt = com.aliyun.teautil.Common.readAsBytes(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", byt),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

    public Map<String, ?> doRPCRequestUNI3(String action, String product, String version, String protocol, String method, String authType, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModelExt.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.readTimeout, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                //APIUNI3请求对象
                SignatureUserReqVO vo = new SignatureUserReqVO();
                vo.setAkId(getAccessKeyId());
                vo.setSk(getAccessKeySecret());
                JSONObject headerParams = new JSONObject();
                String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                // 注意时区，否则容易出错
                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                headerParams.put("X-UNI-Timestamp", timestamp);
                vo.setHeaderParams(headerParams);
                vo.setContentType("application/json; charset=utf-8");
                vo.setHost(_endpoint);
                vo.setServiceName(product);
                vo.setRequestMethod(method);

                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                request_.query = TeaConverter.merge(String.class,
                        TeaConverter.buildMap(
                                new TeaPair("Action", action)
                        ),
                        request.query
                );
                vo.setCanonicalQueryString(Joiner.on(com.aliyun.openapiutil.Client.SEPARATOR).withKeyValueSeparator("=").join(request_.query));
                String authorization = APIUNI3.getGetAuthorization(vo, timestamp);
                vo.headerParams.put("Authorization", authorization);
                request_.headers = com.aliyun.teautil.Common.stringifyMapValue(vo.headerParams);
                request_.headers.put("content-type", "application/json; charset=utf-8");
                request_.headers.put("host", _endpoint);
                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                _lastResponse = response_;

                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                if (com.aliyun.teautil.Common.equalString(bodyType, "binary")) {
                    Map<String, Object> resp = TeaConverter.buildMap(
                            new TeaPair("body", response_.body),
                            new TeaPair("headers", response_.headers)
                    );
                    return resp;
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "byte")) {
                    byte[] byt = com.aliyun.teautil.Common.readAsBytes(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", byt),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

    public static Map<String, String> query(Map<String, ?> filter) throws Exception {
        Map<String, String> outMap = new HashMap<>();
        if (null != filter) {
            processObject(outMap, "", filter);
        }
        return outMap;
    }

    private static void processObject(Map<String, String> map, String key, Object value) throws UnsupportedEncodingException {
        if (null == value) {
            return;
        }
        if (value instanceof List) {
            if (key.startsWith(".")) {
                key = key.substring(1);
            }
            map.put(key, JSON.toJSONString(value));
        } else if (value instanceof TeaModelExt) {
            Map<String, Object> subMap = (Map<String, Object>) (((TeaModelExt) value).toMap());
            for (Map.Entry<String, Object> entry : subMap.entrySet()) {
                processObject(map, key + "." + (entry.getKey()), entry.getValue());
            }
        } else if (value instanceof Map) {
            Map<String, Object> subMap = (Map<String, Object>) value;
            for (Map.Entry<String, Object> entry : subMap.entrySet()) {
                processObject(map, key + "." + (entry.getKey()), entry.getValue());
            }
        } else {
            if (key.startsWith(".")) {
                key = key.substring(1);
            }
            if (value instanceof byte[]) {
                map.put(key, new String((byte[]) value, com.aliyun.openapiutil.Client.UTF8));
            } else {
                map.put(key, String.valueOf(value));
            }
        }
    }

    @Data
    public static class SignatureUserReqVO {
        //@ApiModelProperty(value = "服务名称")
        private String serviceName;
        //@ApiModelProperty(value = "host")
        private String host;
        //@ApiModelProperty(value = "查询参数")
        private String canonicalQueryString;
        //@ApiModelProperty(value = "查询参数")
        private String contentType;
        //@ApiModelProperty(value = "ak主键ID")
        private String akId;
        //@ApiModelProperty(value = "sk")
        private String sk;
        //@ApiModelProperty(value = "HTTP 请求方法（GET、POST ）")
        private String requestMethod;
        //@ApiModelProperty(value = "其他头信息参数")
        private JSONObject headerParams;
        //@ApiModelProperty(value = "请求正文")
        private JSONObject body;
    }

    public static class APIUNI3 {
        private final static Charset UTF8 = StandardCharsets.UTF_8;
        private final static String DEFAULT_HOST = "api.unicloud.com";
        private final static String DEFAULT_CONTENT_TYPE = "application/json;charset=utf-8";
        private final static String END_REQUEST_FLAG = "uni3_request";
        private final static String ALGORITHM = "UNI3-HMAC-SHA256";

        public static byte[] hmac256(byte[] key, String msg) throws Exception {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key,
                    mac.getAlgorithm());
            mac.init(secretKeySpec);
            return mac.doFinal(msg.getBytes(UTF8));
        }

        public static String sha256Hex(String s) throws Exception {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] d = md.digest(s.getBytes(UTF8));
            return DatatypeConverter.printHexBinary(d).toLowerCase();
        }

        public static String getGetAuthorization(SignatureUserReqVO vo, String
                timestamp) throws Exception {
            // 时间入参
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // 注意时区，否则容易出错
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));
            // ************* 步骤 1：拼接规范请求串 *************
            String canonicalUri = "/";
            String contentType = StringUtils.isNotBlank(vo.getContentType()) ?
                    vo.getContentType().toLowerCase() : DEFAULT_CONTENT_TYPE;
            String host = StringUtils.isNotBlank(vo.getHost()) ?
                    vo.getHost().toLowerCase() : DEFAULT_HOST;
            String canonicalHeaders;
            String signedHeaders;
            /*
              canonicalHeaders
              1、头部 key 和 value 统一转成小写，并去掉首尾空格，按照 key:value\n 格式拼接；
              2、多个头部，按照头部 key（小写）的 ASCII 升序进行拼接。

              signedHeaders
              1、头部 key 统一转成小写；
              2、多个头部 key（小写）按照 ASCII 升序进行拼接，并且以分号（;）分隔。
             */
            if (!CollectionUtils.isEmpty(vo.getHeaderParams())) {
                JSONObject headerParamJsonObject = vo.getHeaderParams();
                Set<String> keySet = headerParamJsonObject.keySet();
                //小写
                SortedMap<String, Object> newHeaderParamMap = new TreeMap<>();
                for (String key : keySet) {
                    String newLowerCaseKey = key.toLowerCase().trim();
                    Object newValue = headerParamJsonObject.get(key);
                    String newLowCaseValue = null;
                    if (!Objects.isNull(newValue)) {
                        newLowCaseValue =
                                String.valueOf(newValue).toLowerCase().trim();
                    }
                    newHeaderParamMap.put(newLowerCaseKey, newLowCaseValue);
                }
                //排序
                SortedMap<String, Object> params = new TreeMap<>
                        (newHeaderParamMap);
                params.put("content-type", contentType);
                params.put("host", host);
                canonicalHeaders =
                        Joiner.on("\n").withKeyValueSeparator(":").join(params) + "\n";
                TreeSet<String> headers = new TreeSet<>(params.keySet());
                signedHeaders = Joiner.on(";").join(headers);
            } else {
                canonicalHeaders = new StringBuilder("contenttype:").append(contentType).append("\n")
                        .append("host:").append(host).append("\n").toString();
                signedHeaders = "content-type;host";
            }
            String canonicalQueryString =
                    StringUtils.isBlank(vo.getCanonicalQueryString()) ? "" :
                            vo.getCanonicalQueryString();
            String payload = CollectionUtils.isEmpty(vo.getBody()) ? "" :
                    JSONObject.toJSONString(vo.getBody());
            String httpRequestMethod = vo.getRequestMethod();
            if ("GET".equalsIgnoreCase(httpRequestMethod)) {
                //对于 GET 请求，RequestPayload 固定为空字符串
                payload = "";
                String tempQueryStr = canonicalQueryString;
                // canonicalQueryString 信息强制升序排序
                canonicalQueryString = APIUNI3.aseSortQueryString(tempQueryStr);
                //对query的Key和Value进行urlEncode处理
                canonicalQueryString = APIUNI3.urlEncodeQueryKeyAndValue(canonicalQueryString);
            } else {
                //对于 POST/PUT(非GET) 请求，固定为空字符串""
                canonicalQueryString = "";
            }
            String hashedRequestPayload;
            hashedRequestPayload = sha256Hex(payload);
            String canonicalRequest = new
                    StringBuilder(httpRequestMethod).append("\n")
                    .append(canonicalUri).append("\n")
                    .append(canonicalQueryString).append("\n")
                    .append(canonicalHeaders).append("\n")
                    .append(signedHeaders).append("\n")
                    .append(hashedRequestPayload).toString();
            // ************* 步骤 2：拼接待签名字符串 *************
            String hashedCanonicalRequest;
            hashedCanonicalRequest = sha256Hex(canonicalRequest);
            String credentialScope = new StringBuilder(date).append("/")
                    .append(vo.getServiceName()).append("/")
                    .append(END_REQUEST_FLAG).toString();
            String stringToSign = new StringBuilder(ALGORITHM).append("\n")
                    .append(timestamp).append("\n")
                    .append(credentialScope).append("\n")
                    .append(hashedCanonicalRequest).toString();
            // ************* 步骤 3：计算签名 *************
            String signature;
            byte[] secretDate = hmac256(("UNI3" + vo.getSk()).getBytes(UTF8),
                    date);
            byte[] secretService = hmac256(secretDate, vo.getServiceName());
            byte[] secretSigning = hmac256(secretService, END_REQUEST_FLAG);
            signature = DatatypeConverter.printHexBinary(hmac256(secretSigning,
                    stringToSign)).toLowerCase();
            // ************* 步骤 4：拼接 Authorization *************
            String authorization = new StringBuilder(ALGORITHM).append(" ")
                    .append("Credential=").append(vo.getAkId()).append("/")
                    .append(credentialScope).append(", ")
                    .append("SignedHeaders=").append(signedHeaders).append(", ")
                    .append("Signature=").append(signature).toString();
            return authorization;
        }

        private static String urlEncodeQueryKeyAndValue(String canonicalQueryString) throws UnsupportedEncodingException {
            if (StringUtils.isBlank(canonicalQueryString)) {
                return canonicalQueryString;
            }
            List<String> newCanonicalQueryList = new LinkedList<>();
            String[] afterSplit = canonicalQueryString.split("&");
            for (String queryKeyValue : afterSplit) {
                String[] afterSplitKeyValue = queryKeyValue.split("=");
                String key = afterSplitKeyValue[0];
                String value = afterSplitKeyValue[1];
                key = URLEncoder.encode(key, "UTF8");
                value = URLEncoder.encode(value, "UTF8")
                        .replace("+", "%20")
                        .replace("*", "%2A")
                        .replace("%7E", "~");
                newCanonicalQueryList.add(new
                        StringBuilder(key).append("=").append(value).toString());
            }
            return Joiner.on("&").join(newCanonicalQueryList);
        }

        private static String aseSortQueryString(String canonicalQueryString) {
            if (StringUtils.isBlank(canonicalQueryString)) {
                return canonicalQueryString;
            }
            String[] afterSplit = canonicalQueryString.split("&");
            TreeSet<String> sortedSet = new TreeSet<>();
            sortedSet.addAll(Arrays.asList(afterSplit));
            return Joiner.on("&").join(sortedSet);
        }
    }
}
