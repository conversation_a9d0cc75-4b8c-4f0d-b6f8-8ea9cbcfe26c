package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.teaopenapi.models.Config;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SDKGlobalConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CloudClient extends BaseCloudClient {
    public static final CloudClient client = new CloudClient();

    private static final String endpoint = "{}.unicloudsrv.com";

    private static Map<String, String> OSSRegionArea = new HashMap<String, String>() {
        {
            // 北京三
            put("HB1-BJMYB", "oss-cn-north-2");
            // 华北1-天津
            put("a717c4d1-223a-40d3-81a5-8f2a21718635", "oss-cn-north-1");
            // 华东2-连云港
            put("HD1-JSMY", "oss-cn-north-1");
            // 华东1-上海
            put("HD1-SHMY", "oss-cn-east-1");
            // 华南1-广州1
            put("HN1-GZMY", "oss-cn-south-1");
            // 西南1-重庆1  https://uos-cn-southwest-1.unicloudsrv.com/
            put("XN1-CQ1", "uos-cn-southwest-1");
            // 华东3-杭州   https://oss-cn-east-3.unicloudsrv.com/
            put("HD3-HZMY", "oss-cn-east-3");
        }
    };

    //获取Client对象
    @Override
    public <C> C            client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        JSONObject authOther =JSONObject.parseObject(body.getAccess().getJsonStr());
        String ossEndpoint = authOther.getString("ossEndpoint");
        String regionId = body.getCloud().getString("RegionId");
        String protocol = body.getAccess().getProtocol() == null ? "https" : body.getAccess().getProtocol().toLowerCase();
        String proxyAddr =authOther.getString("proxyAddr");

        C client = null;
        try {
            if (clazz == AmazonS3.class) {
                // 创建对象存储客户端
                ossEndpoint = createObjectStorageEndpoint(ossEndpoint, regionId);
                log.info("创建对象存储客户端，使用域名: {}", ossEndpoint);
                client = (C) getAmazonS3Client(body.getAccess().getUsername(), body.getAccess().getPassword(), ossEndpoint,protocol, proxyAddr);
            } else {
                //无需特殊处理的 Client 对象
                Config config = new Config();
                config.setAccessKeyId(body.getAccess().getUsername());
                config.setAccessKeySecret(body.getAccess().getPassword());
                config.setRegionId(body.getCloud().getString("RegionId"));
                config.setProtocol(protocol);
                config.setReadTimeout(90000);
                config.setConnectTimeout(90000);
              /*  if (StrUtil.isNotEmpty(ossEndpoint)) {
                    config.setEndpoint(ossEndpoint);
                } else*/
                if (StrUtil.isNotEmpty(body.getAccess().getServerIp()) && StrUtil.isNotEmpty(body.getAccess().getServerPort())) {
                    config.setEndpoint(body.getAccess().getServerIp() + ":" + body.getAccess().getServerPort());
                } else {
                    config.setEndpoint("**************:30990");
                }
                if (StrUtil.isNotEmpty(proxyAddr)) {
                    config.setHttpProxy(proxyAddr);
                }
                client = clazz.getConstructor(Config.class).newInstance(config);
            }
            return client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }


    /**
     * 创建对象存储域名
     * 如果用户没有配置域名，则根据地域ID自动生成默认域名
     */
    private static String createObjectStorageEndpoint(String ossEndpoint, String regionId) {
        if (StrUtil.isNotEmpty(ossEndpoint)) {
            // 确保域名包含协议
            if (!ossEndpoint.startsWith("http://") && !ossEndpoint.startsWith("https://")) {
                return "https://" + ossEndpoint;
            }
            return ossEndpoint;
        }

        // 使用默认域名格式
        String defaultRegion = OSSRegionArea.get(regionId);
        if (StrUtil.isNotEmpty(defaultRegion)) {
            return StrUtil.format("https://{}.unicloudsrv.com", defaultRegion);
        }

        // 如果找不到对应的地域映射，使用原始地域ID
        log.warn("未找到地域 {} 对应的对象存储域名映射，使用默认格式", regionId);
        return StrUtil.format("https://oss-{}.unicloudsrv.com", regionId);
    }

    public static AmazonS3 getAmazonS3Client(String accessKey, String secretKey, String endPoint,String protocol,String proxyAddr) {
        BasicAWSCredentials cred = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration clientConfiguration = new ClientConfiguration();

        if ("https".equalsIgnoreCase(protocol)) {
            clientConfiguration.setProtocol(Protocol.HTTPS);
        } else {
            clientConfiguration.setProtocol(Protocol.HTTP);
        }

        // 设置兼容性配置，适配非AWS S3服务
        System.setProperty(SDKGlobalConfiguration.DISABLE_CERT_CHECKING_SYSTEM_PROPERTY, "true");
        clientConfiguration.setSignerOverride("AWSS3V4SignerType");  // 使用 V4 签名
        clientConfiguration.setConnectionTimeout(30000);  // 连接超时 30 秒
        clientConfiguration.setSocketTimeout(60000);      // 读取超时 60 秒
        clientConfiguration.setMaxErrorRetry(3);          // 最大重试次数

        // 设置代理（如果配置了代理地址）
        if (StrUtil.isNotEmpty(proxyAddr)) {
            try {
                // 解析代理地址，支持 host:port 格式
                if (proxyAddr.contains(":")) {
                    String[] proxyParts = proxyAddr.split(":");
                    clientConfiguration.setProxyHost(proxyParts[0]);
                    if (proxyParts.length > 1) {
                        clientConfiguration.setProxyPort(Integer.parseInt(proxyParts[1]));
                    }
                } else {
                    clientConfiguration.setProxyHost(proxyAddr);
                }
                log.info("设置代理: {}", proxyAddr);
            } catch (Exception e) {
                log.warn("代理地址格式错误: {}", proxyAddr, e);
            }
        }

        AmazonS3 s3 = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(cred))
                .withClientConfiguration(clientConfiguration)
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endPoint, ""))
                .withPathStyleAccessEnabled(true)  // 启用路径风格访问，兼容非AWS S3服务
                .build();
        return s3;
    }
}