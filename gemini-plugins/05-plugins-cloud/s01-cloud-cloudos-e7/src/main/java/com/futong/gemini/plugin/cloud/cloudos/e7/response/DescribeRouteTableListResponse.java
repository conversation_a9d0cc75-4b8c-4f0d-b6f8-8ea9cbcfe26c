package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeRouteTableListResponse  extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeRouteTableListResponseBody body;


    @Data
    public static class DescribeRouteTableListResponseBody extends BaseResponseBody {

        @NameInMap("Res")
        public DescribeRouteTableListResponseBodyRes res;
    }

    @Data
    public static class DescribeRouteTableListResponseBodyRes extends TeaModel {

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("TotalPages")
        public Integer totalPage;

        @NameInMap("Page")
        public Integer page;

        @NameInMap("Size")
        public Integer size;

        @NameInMap("List")
        public List<DescribeRouteTableListResponseBodyData> list;

    }

    @Data
    public static class DescribeRouteTableListResponseBodyData extends TeaModel {
        @NameInMap("Id")
        public String id;

        @NameInMap("Name")
        public String name;

        @NameInMap("VpcId")
        public String vpcId;

        @NameInMap("Type")
        public Integer type;

        @NameInMap("Description")
        public String description;

        @NameInMap("SubnetIds")
        public List<String> subnetIds;
    }
}
