package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeNatSnatResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNatSnatResponseBody body;

    @Data
    public static class DescribeNatSnatResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeNatSnatResponseBodyData res;

    }

    @Data
    public static class DescribeNatSnatResponseBodyData extends TeaModel {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Long total;
        @NameInMap("Data")
        public List<DescribeNatSnatResponseBodyDataAz> data;
    }

    @Data
    public static class DescribeNatSnatResponseBodyDataAz extends TeaModel {
        @NameInMap("SnatId")
        public String snatId;
        @NameInMap("NatId")
        public String natId;
        @NameInMap("VpcId")
        public String vpcId;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("EipId")
        public String eipId;
        @NameInMap("EipAddr")
        public String eipAddr;
        @NameInMap("Status")
        public String status;
        @NameInMap("Subnets")
        public List<Subnets> subnets;
    }

    @Data
    public static class Subnets extends TeaModel {
        @NameInMap("SubnetId")
        public String subnetId;
        @NameInMap("Cidr")
        public String cidr;
    }
}
