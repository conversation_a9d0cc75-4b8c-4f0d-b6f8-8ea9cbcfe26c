package com.futong.gemini.plugin.cloud.cloudos.e7.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.Map;

public class ResponseUtil {

    public static JSONObject convertJSONObject(Map<String,?> map){
        JSONObject jsonObject = new JSONObject();
        if(map.isEmpty()){
            return jsonObject;
        }
        jsonObject = JSON.parseObject(JSON.toJSONString(map, SerializerFeature.WriteMapNullValue));
        return jsonObject;
    }
}
