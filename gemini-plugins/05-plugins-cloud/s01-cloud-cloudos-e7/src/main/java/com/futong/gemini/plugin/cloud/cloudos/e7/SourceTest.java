package com.futong.gemini.plugin.cloud.cloudos.e7;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.DbClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.EcsClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.ZiguangClient;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.*;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class SourceTest {

    public static void main(String[] args) throws Exception{


        Config config = new Config();
        config.setAccessKeyId("PthCPBFRFl9whJMv");
        config.setAccessKeySecret("wVAgK92vaJ4WxXacbSB0DnwLB7lQHZ");
//        config.setRegionId("region-ys-zz");
        config.setProtocol("https");
        // 12011是控制台，30990是API, 10
        config.setEndpoint("60.191.123.122:30990");
//        test(config);
//        test11(config);
        test22(config);
//        test2(config);
    }

    public static void test(Config config) throws Exception{
        String setId = BuilderDevops.builderId("10f99478228508fe37110d40aeac0d53", "private_ziguang", "devops_region", null);
        System.out.println(setId);
//        String time = "2024-09-25T14:15:05.000+08:00";
//        System.out.println(TimeUtils.stringToLongTime(TimeUtils.instantToString(TimeUtils.utcStringToInstant(time))));
        //VPC和子网有关联关系
        // 云盘和云盘快照报错
        EcsClient client = new EcsClient(config);
        HashMap<String, Object> map = new HashMap<>();
        map.put("RegionId","region-ys-zz");
//        map.put("PageNumber",1);
//        map.put("PageSize",10);
        map.put("DBInstanceId","mysql-pb5p6z2gzhhb");
//        map.put("InstanceId","ecs-pb5qa50tjuwv");
//        map.put("DataCategory","cpuRate");
//        LocalDateTime now = LocalDateTime.now();
//        LocalDateTime oneHourAgo = now.minusHours(1); // 减去一个小时
        Calendar calendar = Calendar.getInstance();
        Long endTime = calendar.getTimeInMillis();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        calendar.set(Calendar.HOUR_OF_DAY,hour-1);
        Long startTime = calendar.getTimeInMillis();
        map.put("StartTime",startTime);
        map.put("EndTime",endTime);
        Date date = new Date(1730687094000L);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = formatter.format(date);
        System.out.println("格式化后的日期: " + formattedDate);
        DescribeInstancesResponse res = client.describeInstances(map);
        System.out.println(JSON.toJSONString(res, SerializerFeature.WriteMapNullValue));
    }

    public static void test11(Config config) throws Exception{
        DbClient client = new DbClient(config);
        HashMap<String, Object> map = new HashMap<>();
//        map.put("RegionId","region-ys-zz");
//        map.put("DBInstanceId","mysql-pb5p6z2gzhhb");
        DescribeDBInstancesResponse res = client.describeDBInstances(map);
        System.out.println(JSON.toJSONString(res, SerializerFeature.WriteMapNullValue));
    }

    public static void test22(Config config) throws Exception{
        String specificationClassCode = "ecs.image.private";
        String  type = specificationClassCode.substring(specificationClassCode.lastIndexOf(".")+1);
        System.out.println(type);
        EcsClient client = new EcsClient(config);
        Map<String, Object> request = new HashMap<>();
        request.put("RegionId", "region-ys-zz");
//        request.put("page", "1");
//        request.put("size","10");
//        request.put("startTime", String.valueOf(DateUtil.offsetDay(new Date(), -1).getTime()));
//        request.put("endTime", String.valueOf(System.currentTimeMillis()));
//        request.put("ruleType", "monitor");
//        request.put("instanceId", "ecs-pb5qdsqax7w4");
//        request.put("VpcId", "vpc-pb5qd8ijuvhk");
//        request.put("PaymentMethodCode", "DAY_MONTH");
        DescribeNetworkInterfacesResponse response = client.describeNetworkInterfaces(request);
        System.out.println(JSON.toJSONString(response, SerializerFeature.WriteMapNullValue));
    }

    public static void test2(Config config) throws Exception {
        String specificationClassCode = "ecs.image.private";
        String  type = specificationClassCode.substring(specificationClassCode.lastIndexOf(".")+1);
        System.out.println(type);
        Map<String, Object> request = new HashMap<>();
//        request.put("category", "ecs_memory_util");
//        request.put("start", 1667372400l);
//        request.put("end", 1667374578l);
//        request.put("instanceIds", "ecs-mce51k2liqoo,ecs-mcfau61ghtp4");
        request.put("RegionId", "region-ys-zz");
        request.put("ProductCode", "VM");
        request.put("PaymentMethodCode", "DAY_MONTH");

        ZiguangClient client = new ZiguangClient(config);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = client.doRPCRequestUNI3("GetSaleSpecification","product", "2024-07-06", "HTTPS", "GET", "AK", "/v3/product", "json", req, runtime);
        System.out.println(JSON.toJSONString(stringMap));
    }
}
