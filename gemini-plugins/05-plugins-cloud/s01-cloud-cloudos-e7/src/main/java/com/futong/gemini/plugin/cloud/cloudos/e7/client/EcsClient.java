package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.futong.gemini.plugin.cloud.cloudos.e7.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.*;
import com.futong.gemini.plugin.cloud.cloudos.e7.utils.ResponseUtil;
import com.futong.gemini.plugin.cloud.cloudos.e7.vo.AccessToken;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
@Slf4j
public class EcsClient extends ZiguangClient {

    public static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("ProductCode", "VM")));

    public EcsClient(Config config) throws Exception {
        super(config);
    }

    /**
     * 查询镜像
     *
     * @throws Exception
     */
    public DescribeImageResponse describeImages(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> map = this.doRPCRequest("DescribeImages", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null);
        System.out.println(JSON.toJSONString(map));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeImages", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeImageResponse());
    }

    /**
     * 查询区域
     *
     * @throws Exception
     */
    public DescribeRegionsResponse describeRegions(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request),
                product
        ));
        return TeaModelExt.toModel(this.doRPCRequest("GetRegion", "2020-07-30", "HTTPS", "GET", "AK", "/product", "json", req, runtime, null), new DescribeRegionsResponse());
    }

    /**
     * 查询区域
     *
     * @throws Exception
     */
    public Map<String, ?> describeUserRegions(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request),
                product
        ));
        return this.doRPCRequest("ListUserRegion", "2020-07-30", "HTTPS", "GET", "AK", "/user/user", "json", req, runtime, null);
    }

    /**
     * 查询密钥对
     *
     * @throws Exception
     */
    public DescribeKeyPairsResponse describeKeyPairs(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeKeyPairs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeKeyPairsResponse());
    }

    /**
     * 查询云主机
     *
     * @throws Exception
     */
    public DescribeInstancesResponse describeInstances(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeInstancesResponse());
    }

    /**
     * 查询云主机详情
     *
     * @throws Exception
     */
    public DetailInstancesResponse detailEcs(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DetailEcs", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DetailInstancesResponse());
    }

    /**
     * 云产品告警
     *
     * @throws Exception
     */
    public DescribeAlarmRecordResponse listAlarmRecord(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequestUNI3("openListAlarmRecord", "openMonitor", "2020-07-30", "HTTPS", "GET", "AK", "/openMonitor", "json", req, runtime), new DescribeAlarmRecordResponse());
    }

    /**
     * 事件历史记录
     *
     * @throws Exception
     */
    public DescribeEventHistoryResponse countEventHistoryGroupByCategory(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("countEventHistoryGroupByCategory", "2020-07-30", "HTTPS", "GET", "AK", "/monitor", "json", req, runtime, null), new DescribeEventHistoryResponse());
    }

    /**
     * 查询VPC
     *
     * @throws Exception
     */
    public DescribeNetworkResponse describeVpcs(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DescribeVpc", "2020-07-30", "HTTPS", "GET", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeNetworkResponse());
    }

    /**
     * 查询子网Subnet
     *
     * @throws Exception
     */
    public DescribeNetworkSubnetResponse describeSubnets(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DescribeSubnet", "2020-07-30", "HTTPS", "GET", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeNetworkSubnetResponse());
    }

    /**
     * 查询路由
     *
     * @throws Exception
     */
    public DescribeRouteTableListResponse describeRouteTableList(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DescribeVpcRoute", "2020-07-30", "HTTPS", "GET", "AK", "/networks/vpc", "json", req, runtime, null);
        return TeaModelExt.toModel(resultMap, new DescribeRouteTableListResponse());
    }

    /**
     * 查询EIP
     *
     * @throws Exception
     */
    public DescribeEipResponse describeEipAddresses(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeEip", "2020-07-30", "HTTPS", "GET", "AK", "/networks/eip", "json", req, runtime, null), new DescribeEipResponse());
    }

    /**
     * 查询云盘
     *
     * @throws Exception
     */
    public DescribeDisksResponse describeDisks(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = this.doRPCRequest("DescribeDisks", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null);
        String s = JSON.toJSONString(stringMap);
        DescribeDisksResponse describeDisksResponses = JSON.parseObject(s, DescribeDisksResponse.class);
        return describeDisksResponses;
    }

    /**
     * 查询快照
     *
     * @throws Exception
     */
    public DescribeSnapshotResponse describeSnapshots(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = this.doRPCRequest("DescribeSnapshots", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null);
        return TeaModelExt.toModel(stringMap, new DescribeSnapshotResponse());
    }

    /**
     * 查询NAT网关
     *
     * @throws Exception
     */
    public DescribeNatResponse describeNatGateways(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeNat", "2020-07-30", "HTTPS", "GET", "AK", "/networks/nat", "json", req, runtime, null), new DescribeNatResponse());
    }

    /**
     * 查询NAT网关DNAT
     *
     * @throws Exception
     */
    public DescribeNatDnatResponse describeNatGatewaysDnat(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = this.doRPCRequest("DescribeDnats", "2020-07-30", "HTTPS", "GET", "AK", "/networks/nat", "json", req, runtime, null);
//        System.out.println(JSON.toJSONString(stringMap));
        return TeaModelExt.toModel(stringMap, new DescribeNatDnatResponse());
    }

    /**
     * 查询NAT网关SNAT
     *
     * @throws Exception
     */
    public DescribeNatSnatResponse describeNatGatewaysSnat(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = this.doRPCRequest("DescribeSnats", "2020-07-30", "HTTPS", "GET", "AK", "/networks/nat", "json", req, runtime, null);
//        System.out.println(JSON.toJSONString(stringMap));
        return TeaModelExt.toModel(stringMap, new DescribeNatSnatResponse());
    }

    /**
     * 查询安全组
     *
     * @throws Exception
     */
    public DescribeSecurityGroupResponse describeSecurityGroups(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeSecurityGroup", "2020-07-30", "HTTPS", "GET", "AK", "/networks/securitygroup", "json", req, runtime, null), new DescribeSecurityGroupResponse());
    }

    /**
     * 查询安全组规则
     *
     * @throws Exception
     */
    public DescribeSecurityGroupRuleResponse describeSecurityGroupRule(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> stringMap = this.doRPCRequest("DescribeSecurityGroupRuleList", "2020-07-30", "HTTPS", "GET", "AK", "/networks/securitygroup", "json", req, runtime, null);
        return TeaModelExt.toModel(stringMap, new DescribeSecurityGroupRuleResponse());
    }

    /**
     * 查询负载均衡
     *
     * @throws Exception
     */
    public DescribeElbResponse describeLoadBalancers(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("DescribeSlb", "2020-07-30", "HTTPS", "GET", "AK", "/networks/slb", "json", req, runtime, null), new DescribeElbResponse());
    }

    /**
     * 查询监控
     *
     * @throws Exception
     */
    public Map<String,?> describeMonitor(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("Monitor", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null);
        return resultMap;
    }

    /**
     * 查询规格
     *
     * @throws Exception
     */
    public DescribeInstanceTypesResponse describeInstanceTypes(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequestUNI3("GetSaleSpecification","product", "2024-07-06", "HTTPS", "GET", "AK", "/v3/product", "json", req, runtime);
        String json = JSON.toJSONString(resultMap);
        System.out.println(json);
        return TeaModelExt.toModel(resultMap, new DescribeInstanceTypesResponse());
    }

    /**
     * 查询规格
     *
     * @throws Exception
     */
    public DescribeNetworkInterfacesResponse describeNetworkInterfaces(Map<String, Object> request) throws Exception {
        request.put("OnlySecondary",false);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> resultMap = this.doRPCRequest("DescribeEnis", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null);
        String json = JSON.toJSONString(resultMap);
        DescribeNetworkInterfacesResponse response = JSON.parseObject(json,DescribeNetworkInterfacesResponse.class);
        return response;
    }

    /**
     * 查询区域
     *
     * @throws Exception
     */
    public DescribeInstanceVncUrlResponse describeInstanceVncUrl(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        return TeaModelExt.toModel(this.doRPCRequest("GetEcsVnc", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null), new DescribeInstanceVncUrlResponse());
    }

    /**
     * 查询云盘类型
     *
     * @throws Exception
     */
    public Map<String,?> queryDiskType(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> result = this.doRPCRequest("QueryDiskType", "2", "HTTPS", "GET", "AK", "/ebs", "json", req, runtime, null);
        return  result;
    }

    /**
     * 查询云主机密码
     *
     * @throws Exception
     */
    public Map<String,?> getEcsPassword(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> map = this.doRPCRequest("GetEcsPassword", "2020-07-30", "HTTPS", "GET", "AK", "/compute/ecs/instances", "json", req, runtime, null);
        return map;
    }

    /**
     * 查询裸金属
     *
     * @throws Exception
     */
    public JSONObject describeBms(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> result = this.doRPCRequest("DescribeBms", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null);
        return JSON.parseObject(JSON.toJSONString(result));
    }

    /**
     * 查询裸金属
     *
     * @throws Exception
     */
    public JSONObject describeBmsVncUrl(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> result = this.doRPCRequest("GetBmsVnc", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null);
        return JSON.parseObject(JSON.toJSONString(result));
    }

    /**
     * 查询云主机密码
     *
     * @throws Exception
     */
    public Map<String,?> getBmsPassword(Map<String, Object> request) throws Exception {
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String,?> map = this.doRPCRequest("GetBmsPassword", "2020-07-30", "HTTPS", "GET", "AK", "/compute/bms", "json", req, runtime, null);
        return map;
    }
}