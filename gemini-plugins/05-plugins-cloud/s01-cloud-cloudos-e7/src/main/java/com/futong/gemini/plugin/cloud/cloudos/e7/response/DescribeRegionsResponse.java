package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeRegionsResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeRegionsResponseBody body;
    @Data
    public static class DescribeRegionsResponseBody extends BaseResponseBody{
        @NameInMap("Data")
        public List<DescribeRegionsResponseBodyData> data;

    }
    @Data
    public static class DescribeRegionsResponseBodyData extends TeaModel{
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("RegionName")
        public String regionName;
        @NameInMap("AzList")
        public List<DescribeRegionsResponseBodyDataAz> azList;
    }
    @Data
    public static class DescribeRegionsResponseBodyDataAz extends TeaModel{
        @NameInMap("AzId")
        public String azId;
        @NameInMap("AzName")
        public String azName;
    }

}
