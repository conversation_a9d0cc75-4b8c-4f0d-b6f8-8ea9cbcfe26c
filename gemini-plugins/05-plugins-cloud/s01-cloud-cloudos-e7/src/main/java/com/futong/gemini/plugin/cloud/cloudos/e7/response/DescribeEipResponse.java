package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeEipResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeEipResponseBody body;

    @Data
    public static class DescribeEipResponseBody extends TeaModel {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public DescribeEipResponseBodyAz res;
    }

    @Data
    public static class DescribeEipResponseBodyAz extends BaseResponseBody {
        @NameInMap("Page")
        public Integer page;
        @NameInMap("Size")
        public Integer size;
        @NameInMap("Total")
        public Integer total;
        @NameInMap("Data")
        public List<DescribeEipResponseBodyData> data;
    }

    @Data
    public static class DescribeEipResponseBodyData extends TeaModel {
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("AzoneId")
        public String azoneId;
        @NameInMap("Bandwidth")
        public Integer bandwidth;
        @NameInMap("ParentType")
        public String parentType;
        @NameInMap("CbwId")
        public String cbwId;
        @NameInMap("IpAddress")
        public String ipAddress;
        @NameInMap("Status")
        public String status;
        @NameInMap("InstanceCode")
        public String instanceCode;
        @NameInMap("InstanceId")
        public String instanceId;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("ParentInstanceId")
        public String parentInstanceId;
        @NameInMap("InstanceOperationType")
        public InstanceOperationType instanceOperationType;
        @NameInMap("InstanceOperationInfo")
        public InstanceOperationInfo instanceOperationInfo;
    }

    @Data
    public static class InstanceOperationType extends TeaModel {
        @NameInMap("SpecificationId")
        public String specificationId;
        @NameInMap("End_bandwidth")
        public String end_bandwidth;
        @NameInMap("RangeAttribute")
        public String rangeAttribute;
        @NameInMap("UpdateTime")
        public Integer UpdateTime;
        @NameInMap("Start_bandwidth")
        public String start_bandwidth;
        @NameInMap("Step")
        public String step;
        @NameInMap("SpecificationName")
        public String specificationName;
        @NameInMap("SpecificationCode")
        public String specificationCode;
        @NameInMap("ComponentCode")
        public String componentCode;
        @NameInMap("Step_unit")
        public String step_unit;
        @NameInMap("SpecificationClassCode")
        public String specificationClassCode;
        @NameInMap("Bandwidth_unit")
        public String bandwidth_unit;
        @NameInMap("CreateTime")
        public Integer CreateTime;
        @NameInMap("Describe")
        public String describe;
    }

    @Data
    public static class InstanceOperationInfo extends TeaModel {
        @NameInMap("InstanceStartTime")
        public Integer instanceStartTime;
        @NameInMap("IsPay")
        public Integer isPay;
        @NameInMap("DueTime")
        public String dueTime;
        @NameInMap("IsOld")
        public Integer isOld;
        @NameInMap("RentUnit")
        public String rentUnit;
        @NameInMap("SubUserId")
        public String subUserId;
        @NameInMap("AzId")
        public String azId;
        @NameInMap("Creator")
        public String creator;
        @NameInMap("IsThirdProduct")
        public boolean isThirdProduct;
        @NameInMap("RegionName")
        public String regionName;
        @NameInMap("OperationStatus")
        public Integer operationStatus;
        @NameInMap("InstanceLabel")
        public Integer instanceLabel;
        @NameInMap("UserName")
        public String userName;
        @NameInMap("ProductCode")
        public String productCode;
        @NameInMap("CreateTime")
        public Long createTime;
        @NameInMap("ProductType")
        public String productType;
        @NameInMap("PayType")
        public String payType;
        @NameInMap("FormalPayTime")
        public Integer formalPayTime;
        @NameInMap("OrderId")
        public Integer orderId;
        @NameInMap("IsMspProduct")
        public boolean isMspProduct;
        @NameInMap("RentCount")
        public Integer rentCount;
        @NameInMap("InstanceName")
        public String instanceName;
        @NameInMap("ChargeFlag")
        public Integer chargeFlag;
        @NameInMap("ChargeType")
        public Integer chargeType;
        @NameInMap("UserId")
        public String userId;
        @NameInMap("UpdateTime")
        public Integer updateTime;
        @NameInMap("RenewType")
        public Integer renewType;
        @NameInMap("Id")
        public String id;
        @NameInMap("InstanceEndTime")
        public Integer instanceEndTime;
        @NameInMap("RegionId")
        public String regionId;
        @NameInMap("IsPayClose")
        public boolean isPayClose;
    }

}
