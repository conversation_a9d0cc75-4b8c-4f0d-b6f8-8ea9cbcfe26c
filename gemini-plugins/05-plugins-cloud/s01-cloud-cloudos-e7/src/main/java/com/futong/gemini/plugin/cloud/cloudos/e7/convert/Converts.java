package com.futong.gemini.plugin.cloud.cloudos.e7.convert;

import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

@Slf4j
public class Converts {

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> metrics = new HashMap<>();
    private static Entry.E2<String, String>[] metricType = new Entry.E2[]{
            new Entry.E2("Average", "average"),
            new Entry.E2("Maximum", "max"),
            new Entry.E2("Minimum", "min"),
            new Entry.E2("Value", "value"),
            new Entry.E2("Sum", "sum"),
    };

    static {
        metrics.put("ecs_cpu_util", new Entry.E3("cpuUsage", "average", "%"));
        metrics.put("ecs_memory_util", new Entry.E3("memUsage", "average", "%"));
        metrics.put("ecs_volume_mountpoint_util", new Entry.E3("diskUsage", "average", "%"));
        metrics.put("ecs_volume_read_ops", new Entry.E3("diskRead", "average", "Count/s"));
        metrics.put("ecs_volume_write_ops", new Entry.E3("diskWrite", "average", "Count/s"));
        metrics.put("ecs_network_byte_in", new Entry.E3("netIn", "average", "bit/s"));
        metrics.put("ecs_network_byte_out", new Entry.E3("netOut", "average", "bit/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMapping = new HashMap<>();

    static {
        perfMapping.put("ecs_cpu_util", PerfInfoBean::setCpuUsage);
        perfMapping.put("ecs_memory_util", PerfInfoBean::setMemUsage);
        perfMapping.put("ecs_volume_mountpoint_util", PerfInfoBean::setDiskUsage);
        perfMapping.put("ecs_volume_read_ops", PerfInfoBean::setDiskRead);
        perfMapping.put("ecs_volume_write_ops", PerfInfoBean::setDiskWrite);
        perfMapping.put("ecs_network_byte_in", PerfInfoBean::setNetIn);
        perfMapping.put("ecs_network_byte_out", PerfInfoBean::setNetOut);
    }


    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> bmsMetrics = new HashMap<>();
    static {
        bmsMetrics.put("internal_cpu_util_usage", new Entry.E3("cpuUsage", "average", "%"));
        bmsMetrics.put("internal_memory_usage", new Entry.E3("memUsage", "average", "%"));
        bmsMetrics.put("internal_filesystem_size_usage", new Entry.E3("diskUsage", "average", "%"));
        bmsMetrics.put("internal_disk_read_ops", new Entry.E3("diskRead", "average", "Count/s"));
        bmsMetrics.put("internal_disk_write_ops", new Entry.E3("diskWrite", "average", "Count/s"));
        bmsMetrics.put("internal_network_bit_in", new Entry.E3("netIn", "average", "bit/s"));
        bmsMetrics.put("internal_network_bit_out", new Entry.E3("netOut", "average", "bit/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> bmsPerfMapping = new HashMap<>();

    static {
        bmsPerfMapping.put("internal_cpu_util_usage", PerfInfoBean::setCpuUsage);
        bmsPerfMapping.put("internal_memory_usage", PerfInfoBean::setMemUsage);
        bmsPerfMapping.put("internal_filesystem_size_usage", PerfInfoBean::setDiskUsage);
        bmsPerfMapping.put("internal_disk_read_ops", PerfInfoBean::setDiskRead);
        bmsPerfMapping.put("internal_disk_write_ops", PerfInfoBean::setDiskWrite);
        bmsPerfMapping.put("internal_network_bit_in", PerfInfoBean::setNetIn);
        bmsPerfMapping.put("internal_network_bit_out", PerfInfoBean::setNetOut);
    }
}