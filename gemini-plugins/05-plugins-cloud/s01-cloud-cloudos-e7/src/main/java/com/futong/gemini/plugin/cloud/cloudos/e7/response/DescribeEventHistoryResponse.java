package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;

@Data
public class DescribeEventHistoryResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeEventHistoryResponseBody body;

    @Data
    public static class DescribeEventHistoryResponseBody extends BaseResponseBody {
        @NameInMap("status")
        public String status;
        @NameInMap("code")
        public String code;
        @NameInMap("msg")
        public String msg;
        @NameInMap("auth")
        public boolean auth;
        @NameInMap("res")
        public DescribeEventHistoryResponseBodyData res;

    }

    @Data
    public static class DescribeEventHistoryResponseBodyData extends TeaModel {
        @NameInMap("page")
        public Integer page;
        @NameInMap("size")
        public Integer size;
        @NameInMap("totalCount")
        public Integer totalCount;
        @NameInMap("totalPages")
        public Integer totalPages;
        @NameInMap("list")
        public List<DescribeEventHistoryListResponseBodyData> list;
    }

    @Data
    public static class DescribeEventHistoryListResponseBodyData extends TeaModel {
        @NameInMap("dataCategory")
        public String dataCategory;
        @NameInMap("dataCategoryName")
        public String dataCategoryName;
        @NameInMap("alarmRuleLevel")
        public String alarmRuleLevel;
        @NameInMap("resourceType")
        public String resourceType;
        @NameInMap("count")
        public Integer count;
        @NameInMap("lastTriggerTime")
        public String lastTriggerTime;
    }

}
