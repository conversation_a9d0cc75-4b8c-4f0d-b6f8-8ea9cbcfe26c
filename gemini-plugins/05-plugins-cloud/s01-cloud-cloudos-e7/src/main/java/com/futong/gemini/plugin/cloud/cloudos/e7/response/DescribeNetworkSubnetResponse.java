package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DescribeNetworkSubnetResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;
    @NameInMap("body")
    @Validation(required = true)
    public DescribeNetworkSubnetResponseBody body;

    @Data
    public static class DescribeNetworkSubnetResponseBody extends BaseResponseBody {
        @NameInMap("Code")
        public String code;
        @NameInMap("Msg")
        public String msg;
        @NameInMap("Res")
        public List<DescribeNetworkSubnetResponseBodyData> res;
    }

    @Data
    public static class DescribeNetworkSubnetResponseBodyData extends TeaModel {
        @NameInMap("Name")
        public String name;
        @NameInMap("Id")
        public String id;
        @NameInMap("DnsNameServers")
        public List<String> dnsNameServers;
        @NameInMap("IpAssignedCount")
        public Integer ipAssignedCount;
        @NameInMap("Cidr")
        public String cidr;
        @NameInMap("IpTotalCount")
        public Integer ipTotalCount;
        @NameInMap("IsCrossZone")
        public boolean isCrossZone;
        @NameInMap("AzoneId")
        public String azoneId;
        @NameInMap("GatewayIp")
        public String gatewayIp;
        @NameInMap("IpV6Cidr")
        public String ipV6Cidr;
        @NameInMap("IpV6GatewayIp")
        public String ipV6GatewayIp;
        @NameInMap("HasIpV6")
        public boolean hasIpV6;
    }
}
