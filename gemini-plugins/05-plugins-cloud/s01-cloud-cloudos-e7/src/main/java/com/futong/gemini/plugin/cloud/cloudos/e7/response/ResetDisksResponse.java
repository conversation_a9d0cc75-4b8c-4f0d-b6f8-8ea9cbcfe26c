package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.Map;

public class ResetDisksResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(
            required = true
    )
    public Map<String, String> headers;
    @NameInMap("statusCode")
    @Validation(
            required = true
    )
    public Integer statusCode;
    @NameInMap("body")
    @Validation(
            required = true
    )
    public ResetDisksResponseBody body;

    public ResetDisksResponse() {
    }

    public static ResetDisksResponse build(Map<String, ?> map) throws Exception {
        ResetDisksResponse self = new ResetDisksResponse();
        return (ResetDisksResponse)TeaModel.build(map, self);
    }

    public ResetDisksResponse setHeaders(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    public Map<String, String> getHeaders() {
        return this.headers;
    }

    public ResetDisksResponse setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
        return this;
    }

    public Integer getStatusCode() {
        return this.statusCode;
    }

    public ResetDisksResponse setBody(ResetDisksResponseBody body) {
        this.body = body;
        return this;
    }

    @Data
    public static class ResetDisksResponseBody extends BaseResponseBody{
    }

    public ResetDisksResponseBody getBody() {
        return this.body;
    }
}
