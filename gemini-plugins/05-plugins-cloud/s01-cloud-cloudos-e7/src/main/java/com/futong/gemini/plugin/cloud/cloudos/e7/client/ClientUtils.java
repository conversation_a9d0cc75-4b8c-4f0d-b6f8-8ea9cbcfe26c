package com.futong.gemini.plugin.cloud.cloudos.e7.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.teaopenapi.models.Config;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.SDKGlobalConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.model.account.CloudAccessBeanExt;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


@Slf4j
public class ClientUtils extends BaseClient {

    public static BaseClient base = new ClientUtils();

    private static final String endpoint = "{}.unicloudsrv.com";


    @Override
    public <C> C client(Class<C> clazz, JSONObject q) {
        try {
            //获取认证对象
            JSONObject request = arguments.get();
            CloudAccessBean accessBean = auths.get();
            CloudAccessBeanExt accessBeanExt = authExts.get();
            String regionId = regions.get();
            String ossEndpoint = request.getString("endpoint");
            C client = null;
            if (clazz == AmazonS3.class) {
                if (StrUtil.isEmpty(ossEndpoint)) {
                    ossEndpoint = StrUtil.emptyToDefault(ossEndpoint, StrUtil.format(ossEndpoint, OSSRegionArea.get(regionId)));
                }
                client = (C) getAmazonS3Client(accessBean.getUsername(), accessBean.getPassword(), ossEndpoint);
            }else{
                //无需特殊处理的 Client 对象
                Config config = new Config();
                config.setAccessKeyId(accessBean.getUsername());
                config.setAccessKeySecret(accessBean.getPassword());
                config.setRegionId(regionId);
                config.setProtocol(accessBean.getProtocol()==null?"https":accessBean.getProtocol().toLowerCase());
                if (StrUtil.isNotEmpty(ossEndpoint)) {
                    config.setEndpoint(ossEndpoint);
                } else if (StrUtil.isNotEmpty(accessBean.getServerIp()) && StrUtil.isNotEmpty(accessBean.getServerPort())) {
                    config.setEndpoint(accessBean.getServerIp()+":"+accessBean.getServerPort());
                } else {
                    config.setEndpoint("**************:30990");
                }
                String proxyAddr;
                if (StrUtil.isNotEmpty(proxyAddr = accessBeanExt.getProxyAddr())) {
                    config.setHttpProxy(proxyAddr);
                }
                client = clazz.getConstructor(Config.class).newInstance(config);
            }
            return client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        }
    }

    private static Map<String, String> OSSRegionArea = new HashMap<String, String>() {
        {
            // 北京三
            put("HB1-BJMYB", "oss-cn-north-2");
            // 华北1-天津
            put("a717c4d1-223a-40d3-81a5-8f2a21718635", "oss-cn-north-1");
            // 华东2-连云港
            put("HD1-JSMY", "oss-cn-north-1");
            // 华东1-上海
            put("HD1-SHMY", "oss-cn-east-1");
            // 华南1-广州1
            put("HN1-GZMY", "oss-cn-south-1");
            // 西南1-重庆1  https://uos-cn-southwest-1.unicloudsrv.com/
            put("XN1-CQ1", "uos-cn-southwest-1");
            // 华东3-杭州   https://oss-cn-east-3.unicloudsrv.com/
            put("HD3-HZMY", "oss-cn-east-3");
        }
    };

    public static AmazonS3 getAmazonS3Client(String accessKey, String secretKey, String endPoint) {
        System.setProperty(SDKGlobalConfiguration.DISABLE_CERT_CHECKING_SYSTEM_PROPERTY, "true");
        BasicAWSCredentials cred = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setProtocol(Protocol.HTTPS);
        AmazonS3 s3 = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(cred))
                .withClientConfiguration(clientConfiguration)
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endPoint, endPoint))
                .build();
        return s3;
    }

    @Override
    public <Q, R, C> R execute(C client, Q q, FTExecute<C, Q, R> exec) {
        try {
            return exec.apply(client, q);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e);
        }
    }
}
