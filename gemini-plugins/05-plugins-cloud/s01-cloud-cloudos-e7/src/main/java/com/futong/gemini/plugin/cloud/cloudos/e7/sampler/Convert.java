package com.futong.gemini.plugin.cloud.cloudos.e7.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.utils.TimeUtils;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.ResDiskApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.cloudos.e7.client.TeaModelExt;
import com.futong.gemini.plugin.cloud.cloudos.e7.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e7.response.*;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

public class Convert {

    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    public static Map<Class, List> convertRegion(BaseCloudRequest request, DescribeRegionsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || CollUtil.isEmpty(response.body.data)) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        //将获取到的规格信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                response.body.data,
                DescribeRegionsResponse.DescribeRegionsResponseBodyData::getRegionName,
                DescribeRegionsResponse.DescribeRegionsResponseBodyData::getRegionId
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    public static Map<Class, List> convertZone(BaseCloudRequest request, DescribeRegionsResponse.DescribeRegionsResponseBodyData response) {
        Map<Class, List> result = new HashMap<>();
        List<TmdbDevops> data = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        //将获取到的可用区信息转换为CI模型
        BuilderDevops devops = new BuilderDevops()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        response.getRegionId(),
                        DevopsSide.DEVOPS_REGION.value());
        for (DescribeRegionsResponse.DescribeRegionsResponseBodyDataAz zone : response.azList) {
            BuilderDevops devopsZone = new BuilderDevops()
                    .withDevops(devops.get(), zone.getAzName(), zone.getAzId(), DevopsSide.DEVOPS_ZONE.value())
                    .withJson(JSON.toJSONString(zone));
            data.add(devopsZone.get());
            links.add(devopsZone.builderLink(devops.get()));
        }
        result.put(TmdbDevops.class, data);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }

    public static Map<Class, List> convertImage(BaseCloudRequest request, DescribeImageResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.images || CollUtil.isEmpty(response.body.images)) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        for (DescribeImageResponse.DescribeImageResponseBodyData image : response.body.images) {
            CmdbImageRes ci = new CmdbImageRes();
            CmdbOsRes os = new CmdbOsRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), image.getImageId()));
            ci.setType(image.getOstype());
            ci.setSize(image.getSize().floatValue());
            ci.setStatus(image.getStatus());
            ci.setOpen_id(image.getImageId());
            ci.setOpen_name(image.getImageName());
            ci.setVisibility("private");
            ci.setImage_source("self");
            ci.setExtend1("ecs.image.private");
            toCiResCloud(request, ci);
            dataImage.add(ci);
            //操作新系统OS对象
            os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), image.getImageId()));
            os.setType(image.getOstype().toLowerCase());
            os.setVersion(image.getOperatingSystem());
            os.setFull_name(image.getImageName());
            os.setOpen_name(image.getImageName());
            os.setOpen_id(image.getImageId());
            toCiResCloud(request, ci);
            dataOs.add(os);
            Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
            dataAss.add(osa);
        }
        List<TmdbResourceSet> dataSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(), CloudType.fromValue(request.getPlugin().getRealm()))
                .withDataByDevopsValue(ResourceType.CMDB_IMAGE_RES, dataImage, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .withDataByDevopsValue(ResourceType.CMDB_OS_RES, dataOs, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, dataSet);
        return result;
    }

    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, DescribeSecurityGroupResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSecurityGroupResponse.DescribeSecurityGroupResponseBodyDataAz res : response.body.res.data) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_SECURITYGROUP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSecurityGroupRule(BaseCloudRequest request, DescribeSecurityGroupRuleResponse responseRule) {
        Map<Class, List> result = new HashMap<>();
        if (null == responseRule.body || null == responseRule.body.res || CollUtil.isEmpty(responseRule.body.res.sgRules)) {
            result.put(CmdbSecuritygroupRule.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSecuritygroupRule> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSecurityGroupRuleResponse.SgRulesData res : responseRule.body.res.sgRules) {
            String securityGroupResId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSecurityGroupId());
            CmdbSecuritygroupRule ci = new CmdbSecuritygroupRule();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(ci.getOpen_id());
            ci.setDirection(res.getDirection());
            if ("ingress".equals(res.getDirection())) {
                ci.setSource_cidr(res.getRemoteIpPrefix());
            }
            if ("egress".equals(res.getDirection())) {
                ci.setDest_cidr(res.getRemoteIpPrefix());
            }
            ci.setPolicy(res.getIsDeny() == 0 ? "accept" : "refuse");
            ci.setIp_protocol(res.getProtocol());
            ci.setPort_range(res.getPortRanges() == null ? "any" : res.getPortRanges());
            ci.setPriority(res.getPriority().toString());
            ci.setOpen_direction(res.getDirection());
            ci.setOpen_policy(res.getPriority().toString());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联安全组规则
            Association rule = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, securityGroupResId);
            associations.add(rule);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_SECURITYGROUP_RULE
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbSecuritygroupRule.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertVpc(BaseCloudRequest request, DescribeNetworkResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res)) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_VPC_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("RegionId"));
        List<CmdbVpcRes> data = new ArrayList<>();
        for (DescribeNetworkResponse.DescribeNetworkResponseBodyData res : response.body.res) {
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setCidr(res.getCidr());
            ci.setStatus(res.getStatus());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            List<DescribeNetworkResponse.DescribeNetworkResponseBodyDataAz> azList = res.getSubnets();
            if (CollUtil.isNotEmpty(azList)) {
                for (DescribeNetworkResponse.DescribeNetworkResponseBodyDataAz dataAz : azList) {
                    String azId = dataAz.getAzoneId();
                    builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, azId);
                }
            }
        }
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        return result;
    }

    public static Map<Class, List> convertSubnet(BaseCloudRequest request, DescribeNetworkSubnetResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body.res || null == response.body.res || CollUtil.isEmpty(response.body.res)) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_SUBNET_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("RegionId"));
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNetworkSubnetResponse.DescribeNetworkSubnetResponseBodyData res : response.body.res) {
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setCidr_ipv4(res.getCidr());
            ci.setCidr_ipv6(res.getIpV6Cidr());
            toCiResCloud(request, ci);
            data.add(ci);
            //关联地域可用区
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getAzoneId());
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), request.getBody().getCloud().getString("VpcId")));
            associations.add(rule);
        }
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertNetcard(BaseCloudRequest request, DescribeNetworkInterfacesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || CollUtil.isEmpty(response.body.getList())) {
            result.put(CmdbNetcardRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNetcardRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNetworkInterfacesResponse.DescribeNetworkInterfacesResponseBodyData res : response.body.getList()) {
            CmdbNetcardRes ci = new CmdbNetcardRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setType(res.getType());
            ci.setStatus(res.getStatus());
            ci.setOpen_status(res.getStatus());
//            ci.setMac_address(res.macAddress);
//            ci.setDesc(res.description);
            ci.setCreate_time(res.getCreateTime());
            toCiResCloud(request, ci);
            if (StrUtil.isNotEmpty(res.getIpv4Addr())) {
                associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getIpv4Addr())));
                ci.setIpv4_address(res.getIpv4Addr());
            }

            if (StrUtil.isNotEmpty(res.getIpv6Addr())) {
                associations.add(AssociationUtils.toAssociation(ci, CmdbIpRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getIpv6Addr())));
                ci.setIpv6_address(res.getIpv6Addr());
            }
            //关联VPC
            if (StrUtil.isNotEmpty(res.getVpcId())) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
                associations.add(vpc);
            }
            //关联子网
            if (StrUtil.isNotEmpty(res.getSubnetId())) {
                Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
                associations.add(subnet);
            }
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_NETCARD_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbNetcardRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertRoute(BaseCloudRequest request, DescribeRouteTableListResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.list)) {
            result.put(CmdbRouteRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbRouteRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeRouteTableListResponse.DescribeRouteTableListResponseBodyData res : response.body.res.list) {
            CmdbRouteRes ci = new CmdbRouteRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getId()));
            ci.setOpen_id(res.getId());
            ci.setOpen_name(res.getName());
            ci.setDefault_route(res.getType() + "");
            ci.setDesc(res.getDescription());
            toCiResCloud(request, ci);
            //关联VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            if (CollUtil.isNotEmpty(res.getSubnetIds())) {
                for (String subnetId : res.getSubnetIds()) {
                    Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnetId));
                    associations.add(subnet);
                }
            }
            associations.add(vpc);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_ROUTE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbRouteRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertNat(BaseCloudRequest request, DescribeNatResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbNatRes.class, null);
            result.put(CmdbIpRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNatResponse.DescribeNatResponseBodyDataAz res : response.body.res.data) {
            CmdbNatRes ci = new CmdbNatRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setStatus(res.getStatus());
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getCreateTime()));
            toCiResCloud(request, ci);
            data.add(ci);
            //关联VPC
            Association rule = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(rule);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_NAT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbNatRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertDnatEntry(BaseCloudRequest request, DescribeNatDnatResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNatDnatResponse.DescribeNatDnatResponseBodyDataAz res : response.body.res.data) {
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getDnatId()));
            ci.setOpen_id(res.getDnatId());
            ci.setOpen_name(res.getDnatId());
            ci.setProtocol(res.getProtocal());
            ci.setPrivate_port(res.getFixedPortId());
            ci.setPrivate_ip(res.getFixedIpAddr());
//            ci.setPublic_port(res.getE());
//            ci.setPublic_ip(res.getExternalIp());
            ci.setType("DNAT");
            toCiResCloud(request, ci);
            //关联NAT
            Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNatId()));
            associations.add(nat);
            data.add(ci);
        }
        result.put(CmdbNatEntryRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSnatEntry(BaseCloudRequest request, DescribeNatSnatResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbNatEntryRes.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbNatEntryRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeNatSnatResponse.DescribeNatSnatResponseBodyDataAz res : response.body.res.data) {
            CmdbNatEntryRes ci = new CmdbNatEntryRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSnatId()));
            ci.setOpen_id(res.getSnatId());
            ci.setOpen_name(res.getSnatId());
            ci.setCidr(res.getEipAddr());
            ci.setPublic_ip(res.getEipId());
            ci.setType("SNAT");
            toCiResCloud(request, ci);
            //关联NAT
            Association nat = AssociationUtils.toAssociation(ci, CmdbNatRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getNatId()));
            associations.add(nat);
            if (CollUtil.isNotEmpty(res.getSubnets())) {
                res.getSubnets().forEach(subnet -> {
                    Association ass = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnet.getSubnetId()));
                    associations.add(ass);
                });
            }
            associations.add(nat);
            data.add(ci);
        }
        result.put(CmdbNatEntryRes.class, data);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertKeyPair(BaseCloudRequest request, DescribeKeyPairsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.list || CollUtil.isEmpty(response.body.list)) {
            result.put(CmdbKeypairRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        List<CmdbKeypairRes> data = new ArrayList<>();
        for (DescribeKeyPairsResponse.DescribeKeyPairsResponseBodyData res : response.body.list) {
            CmdbKeypairRes ci = new CmdbKeypairRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setFingerprint(res.getFingerPrint());
            ci.setCreate_time(res.getCreateTime());
            toCiResCloud(request, ci);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_KEYPAIR_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbKeypairRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, DescribeInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.list || CollUtil.isEmpty(response.body.list)) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_INSTANCE_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("RegionId"));
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        for (DescribeInstancesResponse.DescribeInstancesResponseBodyData res : response.body.list) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setCpu_size(res.getInstanceCpu());
            ci.setMem_size(res.getInstanceMemory() * 1024);
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setExtend1(res.getInstanceCode());
            toCiResCloud(request, ci);
            switch (res.getStatus()) {
                case "CREATING":
                    ci.setStatus(InstanceStatus.BUILDING.value());
                    break;
                case "RUNNING":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "BOOTING":
                    ci.setStatus(InstanceStatus.STARTING.value());
                    break;
                case "REBOOTING":
                    ci.setStatus(InstanceStatus.RESTARTING.value());
                    break;
                case "SHUTTING_DOWN":
                    ci.setStatus(InstanceStatus.STOPPING.value());
                    break;
                case "DOWN":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "RESUMING":
                    ci.setStatus(InstanceStatus.RECOVERING.value());
                    break;
                case "ERROR":
                    ci.setStatus(InstanceStatus.ERROR.value());
                    break;
                default:
                    ci.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
            }

            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getAzoneId());
            //关联规格
            Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceCode()));
            associations.add(flavor);
            //关联镜像
            Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getImageId()));
            associations.add(image);
            //关联镜像OS
            Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getImageId()));
            associations.add(os);
            //关联网络VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(vpc);
            //关联网络VPC子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            associations.add(subnet);
            //添加公网弹性IP,关联弹性IP
            if (ObjectUtil.isNotNull(res.getEipAddr()) && StrUtil.isNotEmpty(res.getEipAddr())) {
                CmdbIpRes ip = new CmdbIpRes();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getEipAddr()));
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(res.getEipAddr());
                ip.setOpen_id(res.getEipId());
                ip.setOpen_name(res.getEipName());
                toCiResCloud(request, ip);
                ips.add(ip);
                //IP关联云主机
                associations.add(AssociationUtils.toAssociation(ip, ci));
                //云主机关联EIP
                Association eip = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getEipId()));
                associations.add(eip);
            }
            if (StrUtil.isNotEmpty(res.getIp())) {
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getIp()));
                ip.setType(IpType.PRIVATE_IP.value());
                ip.setAddress(res.getIp());
                ip.setOpen_id(res.getIp());
                ip.setOpen_name(res.getIp());
                toCiResCloud(request, ip);
                ips.add(ip);
                //关联网络VPC
                associations.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId())));
                //关联网络VPC子网
                associations.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId())));
                //关联云主机
                associations.add(AssociationUtils.toAssociation(ip, ci));
            }
            //关联主虚拟网卡
            if (StrUtil.isNotEmpty(res.getEniId())) {
                associations.add(AssociationUtils.toAssociation(ci, CmdbNetcardRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getEniId())));
            }
            //关联辅助虚拟网卡
            if (CollUtil.isNotEmpty(res.getSecondaryEni())) {
                res.getSecondaryEni().forEach(t -> {
                    Association eni = AssociationUtils.toAssociation(ci, CmdbNetcardRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), t));
                    associations.add(eni);
                });
            }
            data.add(ci);
        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertEcsDetail(BaseCloudRequest request, DetailInstancesResponse response) {
        Map<Class, List> result = new HashMap<>();
        List<Association> associations = new ArrayList<>();
        CmdbInstanceRes ci = new CmdbInstanceRes();
        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), response.body.getInstanceId()));
        if (CollUtil.isNotEmpty(response.body.getSgIds())) {
            response.body.getSgIds().forEach(t -> {
                Association securityGroup = AssociationUtils.toAssociation(ci, CmdbSecuritygroupRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), t));
                associations.add(securityGroup);
            });
        }
        if (StrUtil.isNotEmpty(response.body.getKeyPair())) {
            //关联密钥对
            Association keyPair = AssociationUtils.toAssociation(ci, CmdbKeypairRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), response.body.getKeyPair()));
            associations.add(keyPair);
        }
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertFlavor(BaseCloudRequest request, DescribeInstanceTypesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || CollUtil.isEmpty(response.body.data)) {
            result.put(CmdbFlavor.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(CmdbImageRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(CmdbDiskRes.class, null);
            return result;
        }
        List<CmdbFlavor> data = new ArrayList<>();
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<CmdbDiskRes> dataDisk = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        for (DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyData res : response.body.data) {
            // vm代表云主机规格
            if ("vm".equals(res.getComponentCode())) {
                for (DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfos info : res.getSpecificationInfos()) {
                    CmdbFlavor ci = new CmdbFlavor();
                    ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), info.getSpecificationCode()));
                    ci.setCpu_size(Integer.valueOf(info.getSpecificAttribute().getVCPU()));
                    ci.setMem_size(Integer.valueOf(info.getSpecificAttribute().getMemory()));
                    ci.setOpen_id(info.getSpecificationCode());
                    ci.setOpen_name(info.getSpecificationName());
                    ci.setDesc(info.getDescribe());
                    ci.setGpu_model(info.getSpecificAttribute().getGpu_type());
                    if (StrUtil.isNotEmpty(info.getSpecificAttribute().getGpu_type())) {
                        ci.setCategory("gpu");
                    } else {
                        ci.setCategory("cpu");
                    }
                    ci.setSpecification_class_code(info.getSpecificationClassCode());
                    ci.setSpecification_class_name(info.getSpecificationClassName());
//                    ci.setProduct_code("VM");
                    ci.setGpu_num(info.getSpecificAttribute().getGpu_count() == null ? null : Integer.valueOf(info.getSpecificAttribute().getGpu_count()));
                    toCiResCloud(request, ci);
                    data.add(ci);
                }
            }
            // ecs.image代表云主机镜像
            if ("ecs.image".equals(res.getComponentCode())) {
                for (DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfos info : res.getSpecificationInfos()) {
                    CmdbImageRes ci = new CmdbImageRes();
                    CmdbOsRes os = new CmdbOsRes();
                    DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfosAttribute attribute = info.getSpecificAttribute();
                    if(attribute != null){
                        ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), attribute.getImageId()));
                        ci.setOpen_id(attribute.getImageId());
                        ci.setOpen_name(attribute.getImageId());
                        ci.setDesc(info.getDescribe());
                        ci.setSize(attribute.getSize() == null ? null : Float.parseFloat(attribute.getSize()));
                        ci.setType(attribute.getImageType());
                        String specificationClassCode = info.getSpecificationClassCode();
                        ci.setVisibility(specificationClassCode.substring(specificationClassCode.lastIndexOf(".") + 1));
                        if (specificationClassCode.contains("private")) {
                            ci.setImage_source("self");
                        } else {
                            ci.setImage_source("system");
                        }
                        ci.setExtend1(info.getSpecificationClassCode());
//                        ci.setProduct_code("VM");
                        toCiResCloud(request, ci);
                        dataImage.add(ci);
                        //操作新系统OS对象
                        os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(),res.getComponentCode(), attribute.getImageId()));
                        os.setCpu_arch(attribute.getOsType());
                        os.setName(attribute.getOsVersion());
                        os.setType(attribute.getImageType());
                        os.setVersion(attribute.getVersion());
                        os.setFull_name(attribute.getVersion());
                        os.setOpen_name(attribute.getVersion());
                        os.setOpen_id(attribute.getImageId());
                        toCiResCloud(request, os);
                        dataOs.add(os);
                        Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
                        dataAss.add(osa);
                    }
                }
            }
            //裸金属的规格
            if ("bms.instance".equals(res.getComponentCode())) {
                for (DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfos info : res.getSpecificationInfos()) {
                    CmdbFlavor ci = new CmdbFlavor();
                    ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), info.getSpecificationCode()));
                    ci.setCpu_size(Integer.valueOf(info.getSpecificAttribute().getCpu()));
                    ci.setMem_size(Integer.valueOf(info.getSpecificAttribute().getMemory()));
                    ci.setOpen_id(info.getSpecificationCode());
                    ci.setOpen_name(info.getSpecificationName());
                    ci.setDesc(info.getDescribe());
                    ci.setGpu_model(info.getSpecificAttribute().getGpu_type());
                    if (StrUtil.isNotEmpty(info.getSpecificAttribute().getGpu_type())) {
                        ci.setCategory("gpu");
                    } else {
                        ci.setCategory("cpu");
                    }
                    ci.setSpecification_class_code(info.getSpecificationClassCode());
                    ci.setSpecification_class_name(info.getSpecificationClassName());
//                    ci.setProduct_code("BMS");
                    ci.setGpu_num(info.getSpecificAttribute().getGpuCount() == null ? null : Integer.valueOf(info.getSpecificAttribute().getGpuCount()));
                    toCiResCloud(request, ci);
                    data.add(ci);
                }
            }
            // 裸金属镜像
            if ("bms.image".equals(res.getComponentCode())) {
                for (DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfos info : res.getSpecificationInfos()) {
                    CmdbImageRes ci = new CmdbImageRes();
                    CmdbOsRes os = new CmdbOsRes();
                    DescribeInstanceTypesResponse.DescribeInstanceTypesResponseBodyDataInfosAttribute attribute = info.getSpecificAttribute();
                    ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), attribute.getImageId()));
                    ci.setOpen_id(attribute.getImageId());
                    ci.setOpen_name(attribute.getImageId());
                    ci.setDesc(info.getDescribe());
                    ci.setSize(attribute.getSize() == null ? null : Float.parseFloat(attribute.getSize()));
                    ci.setType(attribute.getImageType().toLowerCase());
                    String specificationClassCode = info.getSpecificationClassCode();
                    ci.setVisibility(specificationClassCode.substring(specificationClassCode.lastIndexOf(".") + 1));
                    if (specificationClassCode.contains("private")) {
                        ci.setImage_source("self");
                    } else {
                        ci.setImage_source("system");
                    }
                    ci.setExtend1(info.getSpecificationClassCode());
//                    ci.setProduct_code("BMS");
                    toCiResCloud(request, ci);
                    dataImage.add(ci);
                    //操作新系统OS对象
                    os.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(),res.getComponentCode(), attribute.getImageId()));
                    os.setCpu_arch(attribute.getOsType());
                    os.setName(attribute.getOsVersion());
                    os.setType(attribute.getImageType());
                    os.setVersion(attribute.getVersion());
                    os.setFull_name(attribute.getVersion());
                    os.setOpen_name(attribute.getVersion());
                    os.setOpen_id(attribute.getImageId());
                    toCiResCloud(request, os);
                    dataOs.add(os);
                    Association osa = AssociationUtils.toAssociation(ci, CmdbOsRes.class, os.getRes_id());
                    dataAss.add(osa);
                }
            }
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(), CloudType.fromValue(request.getPlugin().getRealm()))
                .withDataByDevopsValue(ResourceType.CMDB_FLAVOR, data, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .withDataByDevopsValue(ResourceType.CMDB_IMAGE_RES, dataImage, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .withDataByDevopsValue(ResourceType.CMDB_OS_RES, dataOs, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .withDataByDevopsValue(ResourceType.CMDB_DISK_RES, dataDisk, DevopsSide.DEVOPS_REGION, request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbFlavor.class, data);
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(CmdbDiskRes.class, dataDisk);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static Map<String, PerfInfoBean> convertEcsPerf(BaseCloudRequest request, List<Map<String, List<OpenRangeResponse.BodyResDataResult>>> metricList) {
        if (CollUtil.isEmpty(metricList)) {
            return null;
        }
        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        for (Map<String, List<OpenRangeResponse.BodyResDataResult>> stringListMap : metricList) {
            for (String key : stringListMap.keySet()) {
                String instanceId = key.substring(0, key.lastIndexOf("-"));
                String metricName = key.substring(key.lastIndexOf("-") + 1, key.length());
//                List<List<Double>> res = stringListMap.get(key).stream().map(t->t.getValues().stream().collect(Collectors.collectingAndThen(Collectors.reducing((o1, o2) ->
//                        o1.get(0).compareTo(o2.get(0)) > 0 ? o1 : o2), Optional::get))).collect(toList());
                List<List<Double>> res = stringListMap.get(key).stream()
                        .flatMap(t -> t.getValues().stream())  // 扁平化所有 List<Double>
                        .sorted((list1, list2) -> Double.compare(list2.get(0), list1.get(0)))  // 降序排序
                        .limit(3)  // 取前 3 条
                        .collect(Collectors.toList());
                for (List<Double> doubleList : res) {
                    BigDecimal bigDecimalValue = new BigDecimal(doubleList.get(0));
                    Long longValue = bigDecimalValue.longValue();
                    Long timestamp = Long.valueOf(longValue);
                    Instant instant = Instant.ofEpochSecond(timestamp);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                            .withZone(ZoneId.systemDefault());
                    String createTime = formatter.format(instant) + ":00";
                    Double average = Double.valueOf(doubleList.get(1).toString());
                    String id = instanceId + "_" + timestamp;
                    PerfInfoBean perf = perfMap.get(id);
                    if (perf == null) {
                        //生成指标对象
                        ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                        perf = new PerfInfoBean();//指标对应得资源CI信息
                        perf.setAccountId(instanceDisk.getAccount_id());
                        perf.setCloudType(instanceDisk.getCloud_type());
                        perf.setResId(instanceDisk.getRes_id());
                        perf.setOpenId(instanceDisk.getOpen_id());
                        perf.setOpenName(instanceDisk.getOpen_name());
                        perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                        perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                        perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                        perf.setCreateTime(createTime);
                        if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                            Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                            perf.setDiskSize(sum);
                        }
                        perf.setId(id);
                    }
                    BiConsumer<PerfInfoBean, Double> setValue = Converts.perfMapping.get(metricName);
                    setValue.accept(perf, average);//设置监控指标值
                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    public static Map<Class, List> convertDisk(BaseCloudRequest request, DescribeDisksResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.records || CollUtil.isEmpty(response.body.records)) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_DISK_RES
                );
        String regionId = request.getBody().getCloud().getString("RegionId");
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("RegionId"));
        for (DescribeDisksResponse.DescribeDisksResponseBodyData res : response.body.records) {
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getDiskId()));
            ci.setType(res.getSpecificationCode());
            if ("SYS_DISK".equals(res.getDiskType())) {
                ci.setCategory("system");
            }
            if ("DATA_DISK".equals(res.getDiskType())) {
                ci.setCategory("data");
            }
            ci.setSize(Float.parseFloat(res.getDiskSize()));
            switch (res.getStatus()) {
                case "In-use":
                    ci.setStatus(DiskStatus.IN_USE.value());
                    break;
                case "Available":
                    ci.setStatus(DiskStatus.AVAILABLE.value());
                    break;
                case "Attaching":
                    ci.setStatus(DiskStatus.ATTACHING.value());
                    break;
                case "Detaching":
                    ci.setStatus(DiskStatus.DETACHING.value());
                    break;
                case "Creating":
                    ci.setStatus(DiskStatus.CREATING.value());
                    break;
                default:
                    ci.setStatus(res.getStatus().toLowerCase());
            }
            ci.setOpen_status(res.getStatus());
            ci.setDesc(res.getDescription());
            ci.setOpen_id(res.getDiskId());
            ci.setOpen_name(res.getDiskName());
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getAzoneId());
            toCiResCloud(request, ci);
            //关联实例
            if (CollUtil.isNotEmpty(res.getAttachInfos())) {
                for (DescribeDisksResponse.DescribeDisksResponseBodyDataInstances attachInfo : res.getAttachInfos()) {
                    Association esc = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), attachInfo.getInstanceId()));
                    associations.add(esc);
                }
            }
            data.add(ci);
        }
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, DescribeSnapshotResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.snapshots || CollUtil.isEmpty(response.body.snapshots)) {
            result.put(CmdbSnapshotRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbSnapshotRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeSnapshotResponse.DescribeSnapshotResponseBodyData res : response.body.snapshots) {
            CmdbSnapshotRes ci = new CmdbSnapshotRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSnapshotId()));
            ci.setOpen_id(res.getSnapshotId());
            ci.setOpen_name(res.getSnapshotName());
            ci.setStatus(res.getStatus().toLowerCase());
            ci.setOpen_status(res.getStatus());
            ci.setSize(NumberUtil.parseFloat(res.getSourceDiskSize()));
            ci.setType(res.getSnapshotType().toString());
            ci.setDesc(res.getDescription());
            ci.setCreate_time(TimeUtils.stringToLongTime(res.getCreationTime()));
            toCiResCloud(request, ci);
            //关联磁盘
            Association disk = AssociationUtils.toAssociation(ci, CmdbDiskRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSourceDiskId()));
            associations.add(disk);
            data.add(ci);
        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_SNAPSHOT_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbSnapshotRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertEip(BaseCloudRequest request, DescribeEipResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbEipRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbEipRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeEipResponse.DescribeEipResponseBodyData eip : response.body.res.data) {
            CmdbEipRes ci = new CmdbEipRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eip.getInstanceId()));
            ci.setBandwidth_type(eip.getInstanceOperationType().getSpecificationCode());
            ci.setBandwidth_speed(eip.getBandwidth().toString());
            ci.setElastic_ip(eip.getIpAddress());
            if ("RUNNING".equals(eip.getStatus())) {
                if (StrUtil.isNotEmpty(eip.getParentInstanceId())) {
                    ci.setStatus("inuse");
                } else {
                    ci.setStatus("available");
                }
            } else {
                ci.setStatus("other");
            }
            ci.setStatus(eip.getStatus());
            ci.setDesc(eip.getInstanceOperationType().getDescribe());
            ci.setOpen_id(eip.getInstanceId());
            ci.setOpen_name(eip.getInstanceName());
            toCiResCloud(request, ci);
            data.add(ci);
        }

        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_EIP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbEipRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }


    public static Map<Class, List> convertLoadBalancer(BaseCloudRequest request, DescribeElbResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response.body || null == response.body.res || CollUtil.isEmpty(response.body.res.data)) {
            result.put(CmdbLoadbalanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        List<CmdbLoadbalanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        for (DescribeElbResponse.DescribeElbResponseBodyDataAz res : response.body.res.data) {
            CmdbLoadbalanceRes ci = new CmdbLoadbalanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getInstanceId()));
            ci.setOpen_id(res.getInstanceId());
            ci.setOpen_name(res.getInstanceName());
            ci.setAddress(res.getAddress());
            //负载均衡实例状态。取值：
            //inactive：实例已停止，此状态的实例监听不会再转发流量。
            //active：实例运行中，实例创建后，默认状态为 active。
            //locked：实例已锁定，实例已经被锁定。
            ci.setOpen_status(res.getStatus());
            ci.setStatus(res.getStatus());
            ci.setCreate_time(TimeUtils.utcStringToMilliLong(res.getInstanceOperationInfo().createTime));
            toCiResCloud(request, ci);
            data.add(ci);
            //关联vpc
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getVpcId()));
            associations.add(vpc);
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getSubnetId()));
            associations.add(subnet);

        }
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.fromValue(request.getPlugin().getRealm()),
                        ResourceType.CMDB_LOADBALANCE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("RegionId"))
                .getData();
        result.put(CmdbLoadbalanceRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertHost(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbHostRes> hosts = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CmdbHostRes host = new CmdbHostRes();
                host.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), info.getString("hostId")));
                host.setOpen_id(info.getString("hostId"));
                host.setOpen_name(info.getString("hostName"));
                host.setIp(info.getString("outOfBandIp") + "," + info.getString("manageIp"));
                host.setCpu_size(info.getIntValue("hostCpu"));
                host.setMem_size(info.getIntValue("hostRam") * 1024);//GB转MB
                host.setTotal_size(info.getFloatValue("dataDiskSize"));//磁盘总容量
                host.setUsed_size(info.getFloatValue("usedDiskSize"));//磁盘已使用容量
                host.setAllocation_size(info.getFloatValue("usedDiskSize"));
                switch (info.getString("hostState")) {
                    case "available":
                        host.setStatus(InstanceStatus.RUNNING.value());
                        break;
                    case "maintaining":
                        host.setStatus("maintaining");//新增维护中状态
                        break;
                    case "error":
                        host.setStatus(InstanceStatus.ERROR.value());//异常
                        break;
                    default:
                        host.setStatus(InstanceStatus.UNKNOWN.value());//新增维护中状态
                        break;
                }
                host.setOpen_status(info.getString("hostState"));
                host.setDesc(info.getString("hostDesc"));
                host.setCloud_type(request.getPlugin().getRealm());
                host.setAccount_id(request.getBody().getAccess().getCmpId());
                hosts.add(host);
                //关联关系地域
                if (StrUtil.isNotEmpty(info.getString("regionId"))) {
                    TmdbResourceSet regionSet = new TmdbResourceSet();
                    regionSet.setCloud_type(request.getPlugin().getRealm());
                    regionSet.setAccount_id(accessBean.getCmpId());
                    regionSet.setResource_type("cmdb_host_res");
                    regionSet.setResource_id(host.getRes_id());
                    regionSet.setSet_type(DevopsSide.DEVOPS_REGION.value());
                    regionSet.setSet_table("tmdb_devops");
                    regionSet.setSet_id(IdUtils.encryptId(accessBean.getCmpId(), info.getString("regionId")));
                    regionSet.setBiz_id(regionSet.getResource_id() + "_" + regionSet.getSet_id());
                    sets.add(regionSet);
                }
                //关联关系可用区
                if (StrUtil.isNotEmpty(info.getString("zoneId"))) {
                    TmdbResourceSet zoneSet = new TmdbResourceSet();
                    zoneSet.setCloud_type(request.getPlugin().getRealm());
                    zoneSet.setAccount_id(accessBean.getCmpId());
                    zoneSet.setResource_type("cmdb_host_res");
                    zoneSet.setResource_id(host.getRes_id());
                    zoneSet.setSet_type(DevopsSide.DEVOPS_ZONE.value());
                    zoneSet.setSet_table("tmdb_devops");
                    zoneSet.setSet_id(IdUtils.encryptId(accessBean.getCmpId(), info.getString("zoneId")));
                    zoneSet.setBiz_id(zoneSet.getResource_id() + "_" + zoneSet.getSet_id());
                    sets.add(zoneSet);
                }
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbHostRes.class, hosts);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    public static List<ResourcePerfDetail> convertHostPerf(BaseCloudRequest request, JSONArray resultData, JSONArray resultResCpuPerfDataResult, JSONArray resultResMemoryPerfDataResult) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<ResourcePerfDetail> hostPerfs = new ArrayList<>();
        String previousFiveMinuteMark = getPreviousFiveMinuteTimeString();
        Map<String, Double> cpuUsageMap = new HashMap<>();
        if (CollUtil.isNotEmpty(resultResCpuPerfDataResult)) {
            for (int i = 0; i < resultResCpuPerfDataResult.size(); i++) {
                JSONObject info = resultResCpuPerfDataResult.getJSONObject(i);
                JSONObject metric = info.getJSONObject("metric");
                JSONArray values = info.getJSONArray("value");
                if (CollUtil.isNotEmpty(metric) && CollUtil.isNotEmpty(values)) {
                    String name = metric.getString("instance");
                    Double value = values.getDoubleValue(1);
                    cpuUsageMap.put(name, value);
                }
            }
        }
        Map<String, Double> memUsageMap = new HashMap<>();
        if (CollUtil.isNotEmpty(resultResMemoryPerfDataResult)) {
            for (int i = 0; i < resultResMemoryPerfDataResult.size(); i++) {
                JSONObject info = resultResMemoryPerfDataResult.getJSONObject(i);
                JSONObject metric = info.getJSONObject("metric");
                JSONArray values = info.getJSONArray("value");
                if (CollUtil.isNotEmpty(metric) && CollUtil.isNotEmpty(values)) {
                    String name = metric.getString("instance");
                    Double value = values.getDoubleValue(1);
                    memUsageMap.put(name, value);
                }
            }
        }
        Double zero = 0d;
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                ResourcePerfDetail hostPerf = new ResourcePerfDetail();
                hostPerf.setId(info.getString("hostId") + "." + previousFiveMinuteMark);
                hostPerf.setResId(IdUtils.encryptId(accessBean.getCmpId(), info.getString("hostId")));
                hostPerf.setCloudType(request.getPlugin().getRealm());
                hostPerf.setAccountId(accessBean.getCmpId());
                hostPerf.setResourceType(ResourceType.CMDB_HOST_RES.value());
                hostPerf.setOpenId(info.getString("hostId"));
                hostPerf.setOpenName(info.getString("hostName"));
                hostPerf.setCreateTime(previousFiveMinuteMark);
                hostPerf.setCpuSize(info.getDoubleValue("hostCpu"));
                Double cpuUsage = cpuUsageMap.get(info.getString("hostName"));
                hostPerf.setCpuUsage(ObjUtil.defaultIfNull(cpuUsage, zero));
                hostPerf.setMemSize(info.getDoubleValue("hostRam") * 1024);//GB转 MB
                Double memUsage = memUsageMap.get(info.getString("hostName"));
                hostPerf.setMemUsage(ObjUtil.defaultIfNull(memUsage, zero));
                Double dataDiskSize = info.getDoubleValue("dataDiskSize");
                Double usedDiskSize = info.getDoubleValue("usedDiskSize");
                hostPerf.setDiskSize(dataDiskSize);
                hostPerf.setDiskUsage(usedDiskSize);
                if(dataDiskSize==0d||usedDiskSize==0d){
                    hostPerf.setDiskUsage(zero);
                }else {
                    Double diskUsage = NumberUtil.div(usedDiskSize, dataDiskSize);
                    hostPerf.setDiskUsage(diskUsage*100d);
                }
                hostPerfs.add( hostPerf);
            }
        }
        return hostPerfs;
    }

    public static Map<Class, List> convertBms(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || ObjectUtil.isEmpty(response.getJSONObject("body").getJSONArray("list"))) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            result.put(CmdbIpRes.class, null);
            return result;
        }
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(),
                        "cmdb_bms_res"
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("RegionId"));
        List<CmdbBmsRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        JSONArray list = response.getJSONObject("body").getJSONArray("list");
        for (int i = 0; i < list.size(); i++) {
            JSONObject res = list.getJSONObject(i);
            CmdbBmsRes ci = new CmdbBmsRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbBmsRes.class.getSimpleName(),res.getString("instanceId")));
            ci.setCpu_size(res.getInteger("instanceCpu"));
            ci.setMem_size(res.getInteger("instanceMemory") * 1024);
            ci.setOpen_status(res.getString("status"));
            ci.setDesc(res.getString("description"));
            ci.setOpen_id(res.getString("instanceId"));
            ci.setOpen_name(res.getString("instanceName"));
            ci.setSys_disk_size(res.getFloat("sysDisk"));
            ci.setData_disk_size(res.getFloat("dataDisk"));
            ci.setExtend1(res.getString("instanceCode"));
            switch (res.getString("status")) {
                case "CREATING":
                    ci.setStatus(InstanceStatus.BUILDING.value());
                    break;
                case "RUNNING":
                    ci.setStatus(InstanceStatus.RUNNING.value());
                    break;
                case "BOOTING":
                    ci.setStatus(InstanceStatus.STARTING.value());
                    break;
                case "REBOOTING":
                    ci.setStatus(InstanceStatus.RESTARTING.value());
                    break;
                case "SHUTTING_DOWN":
                    ci.setStatus(InstanceStatus.STOPPING.value());
                    break;
                case "DOWN":
                    ci.setStatus(InstanceStatus.STOPPED.value());
                    break;
                case "RESUMING":
                    ci.setStatus(InstanceStatus.RECOVERING.value());
                    break;
                case "ERROR":
                    ci.setStatus(InstanceStatus.ERROR.value());
                    break;
                default:
                    ci.setStatus(InstanceStatus.UNKNOWN.value());
                    break;
            }
            toCiResCloud(request, ci);
            data.add(ci);

            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            builderResourceSet.withDataByDevopsValue(ci.getRes_id(), DevopsSide.DEVOPS_ZONE, res.getString("azoneId"));
            //关联规格
            Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("instanceCode")));
            associations.add(flavor);
            //关联镜像
            Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("imageId")));
            associations.add(image);
            //关联镜像OS
            Association os = AssociationUtils.toAssociation(ci, CmdbOsRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), CmdbOsRes.class.getSimpleName(), res.getString("imageId")));
            associations.add(os);
            //关联网络VPC
            Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("vpcId")));
            associations.add(vpc);
            //关联网络VPC子网
            Association subnet = AssociationUtils.toAssociation(ci, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("subnetId")));
            associations.add(subnet);
            //添加公网弹性IP,关联弹性IP
            if (ObjectUtil.isNotNull(res.getString("eipAddr"))) {
                CmdbIpRes ip = new CmdbIpRes();
                //主键ID=CI名+弹性IP的ID生成，防止与弹性IP的id冲突
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("eipAddr")));
                ip.setType(IpType.PUBLIC_IP.value());
                ip.setAddress(res.getString("eipAddr"));
                ip.setOpen_id(res.getString("eipId"));
                ip.setOpen_name(res.getString("eipId"));
                toCiResCloud(request, ip);
                ips.add(ip);
                //IP关联裸金属
                associations.add(AssociationUtils.toAssociation(ip, ci));
                //裸金属关联EIP
                Association eip = AssociationUtils.toAssociation(ci, CmdbEipRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("eipId")));
                associations.add(eip);
            }
            if (StrUtil.isNotEmpty(res.getString("ip"))) {
                CmdbIpRes ip = new CmdbIpRes();
                ip.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("ip")));
                ip.setType(IpType.PRIVATE_IP.value());
                ip.setAddress(res.getString("ip"));
                ip.setOpen_id(res.getString("ip"));
                ip.setOpen_name(res.getString("ip"));
                toCiResCloud(request, ip);
                ips.add(ip);
                //关联网络VPC
                associations.add(AssociationUtils.toAssociation(ip, CmdbVpcRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("vpcId"))));
                //关联网络VPC子网
                associations.add(AssociationUtils.toAssociation(ip, CmdbSubnetRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), res.getString("subnetId"))));
                //关联云主机
                associations.add(AssociationUtils.toAssociation(ip, ci));
            }
            //关联网卡
            if (ObjectUtil.isNotEmpty(res.getJSONArray("networkInterfaces"))) {
                for (Object networkInterfaces : res.getJSONArray("networkInterfaces")) {
                    JSONObject networkInterface = (JSONObject) networkInterfaces;
                    Association eni = AssociationUtils.toAssociation(ci, CmdbNetcardRes.class, IdUtils.encryptId(request.getBody().getAccess().getCmpId(), networkInterface.getString("eniId")));
                    associations.add(eni);
                }
            }

        }
        result.put(CmdbInstanceRes.class, data);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }


    public static List<ResourcePerfDetail> convertHostPerf(){
        List<ResourcePerfDetail> dataPerf = new ArrayList<>();
        return dataPerf;
    }

    public static Map<String, ResourcePerfDetail> convertBmsPerf(BaseCloudRequest request, List<Map<String, List<OpenRangeResponse.BodyResDataResult>>> metricList,JSONArray bmsInfos) {
        if (CollUtil.isEmpty(metricList)) {
            return null;
        }
        Map<String, ResInstanceDiskApiModel> instanceMap = (Map<String, ResInstanceDiskApiModel>) request.getBody().get("instanceMap");
        Map<String, ResourcePerfDetail> perfMap = new HashMap<>();
        for (Map<String, List<OpenRangeResponse.BodyResDataResult>> stringListMap : metricList) {
            for (String key : stringListMap.keySet()) {
                String instanceId = key.substring(0, key.lastIndexOf("-"));
                String metricName = key.substring(key.lastIndexOf("-") + 1, key.length());
                List<List<Double>> res = stringListMap.get(key).stream()
                        .flatMap(t -> t.getValues().stream())
                        .sorted((list1, list2) -> Double.compare(list2.get(0), list1.get(0)))
                        .limit(3)
                        .collect(Collectors.toList());
                for (List<Double> doubleList : res) {
                    BigDecimal bigDecimalValue = new BigDecimal(doubleList.get(0));
                    Long longValue = bigDecimalValue.longValue();
                    Long timestamp = Long.valueOf(longValue);
                    Instant instant = Instant.ofEpochSecond(timestamp);
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                            .withZone(ZoneId.systemDefault());
                    String createTime = formatter.format(instant) + ":00";
                    Double average = Double.valueOf(doubleList.get(1).toString());
                    String id = instanceId + "_" + timestamp;
                    ResourcePerfDetail perf = perfMap.get(id);
                    if (perf == null) {
                        //生成指标对象
                        ResInstanceDiskApiModel instanceDisk = instanceMap.get(instanceId);
                        perf = new ResourcePerfDetail();//指标对应得资源CI信息
                        perf.setAccountId(instanceDisk.getAccount_id());
                        perf.setCloudType(instanceDisk.getCloud_type());
                        perf.setResId(instanceDisk.getRes_id());
                        perf.setOpenId(instanceDisk.getOpen_id());
                        perf.setOpenName(instanceDisk.getOpen_name());
                        perf.setCpuSize(instanceDisk.getCpu_size().doubleValue());
                        perf.setMemSize(instanceDisk.getMem_size().doubleValue());
                        perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                        perf.setCreateTime(createTime);
                        if (CollUtil.isNotEmpty(instanceDisk.getDisks())) {
                            Double sum = instanceDisk.getDisks().stream().mapToDouble(ResDiskApiModel::getSize).sum();
                            perf.setDiskSize(sum);
                        }
                        perf.setId(id);
                    }
//                    BiConsumer<ResourcePerfDetail, Double> setValue = Converts.bmsPerfMapping.get(metricName);
//                    setValue.accept(perf, average);//设置监控指标值
//                    perfMap.put(perf.getId(), perf);
                }
            }
        }
        request.getBody().remove("instanceMap");
        return perfMap;
    }

    public static String getPreviousFiveMinuteTimeString() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算最近的5分钟整点时间
        int minute = now.getMinute();
        int targetMinute = (minute / 5) * 5;
        LocalDateTime fiveMinuteMark = now.withMinute(targetMinute)
                .withSecond(0)
                .withNano(0);

        // 定义格式化模板
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化为字符串
        return fiveMinuteMark.format(formatter);
    }
}
