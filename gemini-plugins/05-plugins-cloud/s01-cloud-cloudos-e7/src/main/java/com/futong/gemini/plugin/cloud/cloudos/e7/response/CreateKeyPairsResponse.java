package com.futong.gemini.plugin.cloud.cloudos.e7.response;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

@Data
public class CreateKeyPairsResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public CreateKeyPairsResponseBody body;

    @Data
    public static class CreateKeyPairsResponseBody extends BaseResponseBody {
        @NameInMap("instanceId")
        public String instanceId;
        @NameInMap("name")
        public String keyPairName;
        @NameInMap("fingerPrint")
        public String fingerPrint;
        @NameInMap("userId")
        public String userId;
        @NameInMap("regionId")
        public String regionId;
        @NameInMap("status")
        public String status;
        @NameInMap("publicKey")
        public String publicKey;
        @NameInMap("privateKey")
        public String privateKeyBody;
        @NameInMap("createTime")
        public Long createTime;
    }

}
