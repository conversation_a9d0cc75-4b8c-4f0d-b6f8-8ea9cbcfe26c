{"description": "The initial management account only supports the main account for management", "model": [{"type": "main", "name": "Cloud platform operation master account", "description": "Cloud platform operation master account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Cloud Account"}, {"field": "serverIp", "label": "Server Host", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Host"}, {"field": "username", "label": "Access Key", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key"}, {"field": "password", "label": "Access Key Secret", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Access Key Secret"}, {"field": "serverPort", "label": "Server Port", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Port"}, {"field": "protocol", "label": "Protocol", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "http"}, {"label": "HTTPS", "value": "https"}], "required": true, "isUpdate": true, "tips": "Please select your Protocol"}, {"field": "jsonStr.ossEndpoint", "label": "Object Storage Endpoint", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter object storage endpoint, e.g.: oss-cn-north-2.unicloudsrv.com"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}]}