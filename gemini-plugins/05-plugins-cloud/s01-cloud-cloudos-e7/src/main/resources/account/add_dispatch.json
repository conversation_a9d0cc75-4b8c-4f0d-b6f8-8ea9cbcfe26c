{"form": {"region": {"field": "region", "label": "地域", "type": "select", "value": "", "items": [], "required": true, "isUpdate": true, "tips": "请选择地域"}}, "data": [{"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取物理机-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理机-${region.label}", "jobInfo": "{\"action\": \"FetchComputeHost\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"model\": {\"regionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取物理机监控--${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理机监控", "jobInfo": "{\"action\":\"FetchComputeHostPerf\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"model\":{\"regionId\":\"${region.value}\"}},\"async\":false}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取云主机-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云主机-${region.label}", "jobInfo": "{\"action\": \"FetchComputeInstance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取磁盘-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取磁盘-${region.label}", "jobInfo": "{\"action\": \"FetchStorageDisk\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\",\"DiskType\":\"ALL\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取云主机规格-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云主机规格-${region.label}", "jobInfo": "{\"action\": \"FetchComputeFlavor\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\",\"ProductCode\":\"VM\",\"PaymentMethodCode\":\"DAY_MONTH\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取裸金属规格-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取裸金属规格-${region.label}", "jobInfo": "{\"action\": \"FetchBmsFlavor\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\",\"ProductCode\":\"BMS\",\"PaymentMethodCode\":\"DAY_MONTH\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取镜像-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取镜像-${region.label}", "jobInfo": "{\"action\": \"FetchStorageImage\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取安全组-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取安全组-${region.label}", "jobInfo": "{\"action\": \"FetchComputeSecuritygroup\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取VPC-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取VPC-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronVpc\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取密钥对-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取密钥对-${region.label}", "jobInfo": "{\"action\": \"FetchPlatformKeypair\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取弹性IP-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取弹性IP-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronEip\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取NAT网关-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取NAT网关-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronNat\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取磁盘快照-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取磁盘快照-${region.label}", "jobInfo": "{\"action\": \"FetchStorageSnapshot\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取负载均衡-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取阿里云负载均衡-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronLoadbalance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取辅助网卡-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取辅助网卡-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronNic\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\",\"OnlySecondary\":false}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取对象存储-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取对象存储-${region.label}", "jobInfo": "{\"action\": \"FetchStorageBucket\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取地域可用区", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "获取地域可用区", "jobInfo": "{\"action\": \"FetchPlatformRegion\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取云主机监控--${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云主机监控", "jobInfo": "{\"action\":\"FetchComputeInstancePerf\",\"body\":{\"auth\":{\"cmpId\":\"${cmpId}\"},\"cloud\":{\"regionId\":\"${region.value}\"},\"BasePageSortSearchRequest\":{\"searchList\":[{\"key\":\"status\",\"searchClassiy\":\"0\",\"value\":\"running\"},{\"key\":\"account_id\",\"searchClassiy\":\"0\",\"value\":\"${cmpId}\"}]}},\"async\":false}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取告警信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformAlarm\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"regionId\": \"${region.value}\",\"ruleType\":\"monitor\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取事件", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 1, "tscTimeout": 0, "description": "", "jobInfo": "{\"action\": \"FetchPlatformEvent\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"regionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取Dnat-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取Dnat-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronDnatEntry\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": false, "unique": false, "form": ["region"], "dispatcher_info": {"jobName": "获取Snat-${region.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 22 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取Dnat-${region.label}", "jobInfo": "{\"action\": \"FetchNeutronSnatEntry\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"RegionId\": \"${region.value}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每天22点\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":22,\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}