{"description": "初始纳管账号仅支持主账号纳管", "model": [{"type": "main", "name": "云平台运营主账号", "description": "云平台运营主账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入云账号"}, {"field": "serverIp", "label": "服务地址", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入服务地址"}, {"field": "username", "label": "密钥KEY", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥KEY"}, {"field": "password", "label": "密钥", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥"}, {"field": "serverPort", "label": "端口", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入端口"}, {"field": "protocol", "label": "协议类型", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "http"}, {"label": "HTTPS", "value": "https"}], "required": true, "isUpdate": true, "tips": "请选择协议类型"}, {"field": "jsonStr.ossEndpoint", "label": "对象存储域名", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入对象存储域名，如：oss-cn-north-2.unicloudsrv.com"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}, {"scope": "resource", "type": "sub", "name": "云平台运营子账号", "description": "云平台运营子账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入云账号"}, {"field": "serverIp", "label": "服务地址", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入服务地址"}, {"field": "username", "label": "密钥KEY", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥KEY"}, {"field": "password", "label": "密钥", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入密钥"}, {"field": "appId", "label": "应用ID", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入应用ID"}, {"field": "serverPort", "label": "端口", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入端口"}, {"field": "protocol", "label": "协议类型", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "http"}, {"label": "HTTPS", "value": "https"}], "required": true, "isUpdate": true, "tips": "请选择协议类型"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}]}