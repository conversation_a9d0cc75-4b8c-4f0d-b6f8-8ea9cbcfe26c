package com.futong.gemini.plugin.cloud.cloudos.e7111;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.template.PluginTemplate;

public class CloudosPluginTemplate extends PluginTemplate {
    @Override
    public boolean onBeforeProcess(JSONObject arguments, JSONObject context) {
        return false;
    }

    @Override
    public BaseResponse process(JSONObject arguments, JSONObject context) {
        return null;
    }

    @Override
    public void onAfterProcess(JSONObject arguments, JSONObject context, BaseResponse response) {

    }
}
