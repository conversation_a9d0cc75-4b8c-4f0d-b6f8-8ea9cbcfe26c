package com.futong.gemini.plugin.cloud.harmony.client;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.util.Map;

@Slf4j
public class HarmonyClient {
    protected final CloseableHttpClient httpClient;
    protected final ConnectionConfig config;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String X_AUTH_TOKEN = "Authorization";

    public HarmonyClient(ConnectionConfig config) {
        this.httpClient = createHttpClient(config);
        this.config = config;
    }

    private CloseableHttpClient createHttpClient(ConnectionConfig config) {
        RequestConfig.Builder requestConfigBuilder = RequestConfig.custom()
                .setConnectTimeout(config.getConnectTimeout())
                .setSocketTimeout(config.getSocketTimeout());

        // 判断是否配置了代理
        if (config.getProxyHost() != null && !config.getProxyHost().isEmpty() && config.getProxyPort() > 0) {
            requestConfigBuilder.setProxy(new HttpHost(config.getProxyHost(), config.getProxyPort()));
        }

        RequestConfig requestConfig = requestConfigBuilder.build();

        try {
            // 忽略SSL证书验证
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            return HttpClientBuilder.create()
                    .setDefaultRequestConfig(requestConfig)
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client with SSL context", e);
        }
    }

    public String getToken() {
        Object token = null;
        String key = config.getAddress();
        try {
            token = GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, key);
        } catch (Exception e) {
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, key);
        }
        if (ObjectUtil.isNull(token)) {
            try {
                token = doToken();
            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, key, token);
        }
        return token.toString();
    }

    protected String encryptPassword(String password, String publicKey) {
        // 创建RSA对象，仅使用公钥（私钥设为null）
        RSA rsa = new RSA(null, publicKey);
        // 使用公钥加密（Hutool自动处理Base64编解码）
        return rsa.encryptBase64(password, KeyType.PublicKey);
    }

    public synchronized String doToken() throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getAddress() + "/app-management/provider/users/v2/noCode/login";
        String authJson = String.format("{" +
                        "\"username\": \"%s\"," +
                        "\"password\": \"%s\"" +
                        "}",
                auth.getUsername(),
                encryptPassword(auth.getPassword(), auth.getPublicKey()));
        log.info("调用云上接口获取谐云认证信息url  = {},authJson={}", url, authJson);
        HttpPost request = new HttpPost(url);
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        request.setEntity(new StringEntity(authJson));
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("Authentication failed: " +
                        EntityUtils.toString(response.getEntity()));
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != 0) {
                String msg = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, msg);
            }
            return JSONPath.read(responseBody, "$.data.token", String.class);
        }
    }

    // 通用请求执行方法
    private String executeRequest(HttpRequestBase request) {
        String token;
        try {
            token = getToken();
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
        request.setHeader(X_AUTH_TOKEN, token);
        //设置超时时间
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            if (response.getStatusLine().getStatusCode() < 200 ||
                    response.getStatusLine().getStatusCode() >= 300) {
                throw new RuntimeException("API request failed: " + responseBody);
            }

            Boolean success = JSONPath.read(responseBody, "$.success", Boolean.class);
            if (success != null && !success) {
                log.error("请求失败:{}", responseBody);
                String message = JSONPath.read(responseBody, "$.errorMsg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("调用云接口响应错误信息:" + message));
            }
            Integer code = JSONPath.read(responseBody, "$.code", Integer.class);
            if (code != null && !code.equals(0)) {
                log.error("请求失败:{}", responseBody);
                String message = JSONPath.read(responseBody, "$.msg", String.class);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("调用云接口响应错误信息:" + message));
            }
            return responseBody;
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("发送云操作请求信息失败!"), e);
        }
    }

    // GET请求
    public String doGet(String apiPath, Map<String, ?> params) {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String url = config.getAddress() + apiPath;
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(query);
        }
        return executeRequest(new HttpGet(url));
    }

    // GET请求
    public String doDelete(String apiPath, Map<String, ?> params) {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String url = config.getAddress() + apiPath;
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(query);
        }
        return executeRequest(new HttpDelete(url));
    }

    // POST JSON请求
    public String doPost(String apiPath, JSONObject requestBody) {
        String url = config.getAddress() + apiPath;
        HttpPost request = new HttpPost(url);
        String json = requestBody.toJSONString();
        try {
            request.setEntity(new StringEntity(json, "UTF-8"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, "设置POST请求body信息失败");
        }
        return executeRequest(request);
    }

    // POST JSON请求
    public String doPut(String apiPath, JSONObject requestBody) {
        String url = config.getAddress() + apiPath;
        HttpPut request = new HttpPut(url);
        String json = requestBody.toJSONString();
        try {
            request.setEntity(new StringEntity(json, "UTF-8"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, "设置Put请求body信息失败");
        }
        return executeRequest(request);
    }

    public Object doPostDataByType(String apiPath, JSONObject requestBody, String type) {
        StrUtil.emptyToDefault(type, "null");
        String lowerCase = type.toLowerCase();
        switch (lowerCase) {
            case "json":
                return doPostDataJSON(apiPath, requestBody);
            case "string":
                return doPostDataStr(apiPath, requestBody);
            case "jsonarray":
                return doPostDataJSONArray(apiPath, requestBody);
            default:
                return doPostJSON(apiPath, requestBody);
        }
    }

    public JSONObject doPostJSON(String apiPath, JSONObject requestBody) {
        String result = doPost(apiPath, requestBody);
        return JSONObject.parseObject(result);
    }

    public JSONObject doPostDataJSON(String apiPath, JSONObject requestBody) {
        JSONObject result = doPostJSON(apiPath, requestBody);
        return result.getJSONObject("data");
    }

    public String doPostDataStr(String apiPath, JSONObject requestBody) {
        JSONObject result = doPostJSON(apiPath, requestBody);
        return result.getString("data");
    }

    public JSONArray doPostDataJSONArray(String apiPath, JSONObject requestBody) {
        JSONObject result = doPostJSON(apiPath, requestBody);
        return result.getJSONArray("data");
    }

    public JSONObject doPutJSON(String apiPath, JSONObject requestBody) {
        String result = doPut(apiPath, requestBody);
        return JSONObject.parseObject(result);
    }

    public JSONObject doDeleteJSON(String apiPath, Map<String, ?> params) {
        String result = doDelete(apiPath, params);
        return JSONObject.parseObject(result);
    }

    public JSONObject doGetJSON(String apiPath, Map<String, ?> params) {
        String result = doGet(apiPath, params);
        return JSONObject.parseObject(result);
    }

    public Object doGetDataByType(String apiPath, Map<String, ?> params, String type) {
        StrUtil.emptyToDefault(type, "null");
        String lowerCase = type.toLowerCase();
        switch (lowerCase) {
            case "json":
                return doGetDataJSON(apiPath, params);
            case "string":
                return doGetDataStr(apiPath, params);
            case "jsonarray":
                return doGetDataJSONArray(apiPath, params);
            default:
                return doGetJSON(apiPath, params);
        }
    }

    public JSONObject doGetDataJSON(String apiPath, Map<String, ?> params) {
        JSONObject result = doGetJSON(apiPath, params);
        return result.getJSONObject("data");
    }

    public String doGetDataStr(String apiPath, Map<String, ?> params) {
        JSONObject result = doGetJSON(apiPath, params);
        return result.getString("data");
    }

    public JSONArray doGetDataJSONArray(String apiPath, Map<String, ?> params) {
        JSONObject result = doGetJSON(apiPath, params);
        return result.getJSONArray("data");
    }

    public static void main(String[] args) throws Exception {
        ConnectionConfig config = new ConnectionConfig.Builder()
                .protocol("http")
                .host("*************")
                .port("80")
                .authConfig(new AuthConfig.Builder().username("admin").password("Ab123456").build())
                .proxy("***********", 3128)
                .build();
        HarmonyClient client = new HarmonyClient(config);
        JSONObject json = JSONArray.parseObject("{\n" +
                "\t\"name\": \"cmp-test-0717\",\n" +
                "\t\"description\": \"cmp-test-0717描述\",\n" +
                "\t\"annotations\": {\n" +
                "\t\t\"aa\": \"bb\",\n" +
                "\t\t\"cc\": \"dd\"\n" +
                "\t},\n" +
                "\t\"labels\": {\n" +
                "\t\t\"name\": \"zxg\",\n" +
                "\t\t\"age\": \"11\"\n" +
                "\t}\n" +
                "}");
//        JSONObject s = client.doPostJSON("/olympus-core/clusters/yanshi/namespaces", json);
//        JSONObject s = client.doDeleteJSON("/olympus-core/clusters/yanshi/namespaces/cmp-test-0717", null);
        JSONObject s = client.doGetJSON("/zeus-api/V4/middlewares/middlewareList?clusterId=middlewarecluster&namespace=*&type=MySQL", null);
        System.out.println(s);

    }
}
