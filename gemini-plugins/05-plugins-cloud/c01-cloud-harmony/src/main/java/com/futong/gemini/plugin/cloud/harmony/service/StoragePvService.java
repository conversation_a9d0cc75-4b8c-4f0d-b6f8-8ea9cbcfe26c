package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.DataUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class StoragePvService {
    /**
     * 查询pvc详情
     * 文档《olympus-core》
     * operation=null 基本信息20.13
     * operation=describe 描述信息20.7
     * operation=event 事件列表20.8
     * operation=yaml yaml20.11
     *
     * @param request
     * @return
     */
    public static BaseResponse queryStoragePvDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/persistentvolume";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }else{
            apiPath += "/{pv}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 创建pvc
     * 文档《olympus-core》20.2
     *
     * @param request
     * @return
     */
    public static BaseResponse createStoragePv(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/persistentvolume";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        //yaml数据处理
        JSONObject cloudJson = DataUtils.transformJsonLabelsAndAnnotations(request.getBody().getCloud());//转换标签和注释
        JSONObject requests = cloudJson.getJSONObject("spec").getJSONObject("capacity");
        String storage=requests.getString("storage");
        requests.put("storage", storage+"Gi");
        //数据封装
        JSONObject cloud = new JSONObject();
        cloud.put("pv", cloudJson.toJSONString());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除pv
     * 文档《olympus-core》20.14
     *
     * @param request
     * @return
     */
    public static BaseResponse deleteStoragePv(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/persistentvolume/{pv}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }

    /**
     * 只能更新元数据和读写权限
     * 文档《olympus-core》20.4
     *
     * @param request
     * @return
     */
    public static BaseResponse updateStoragePv(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/persistentvolume/{pv}/base";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 修改yaml
     * 文档《olympus-core》20.12
     *
     * @param request
     * @return
     */
    public static BaseResponse updateStoragePvYaml(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/persistentvolume/yaml";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("yaml", request.getBody().getCloud().toJSONString());
        JSONObject ci = request.getBody().getCi();
        cloud.put("pv", ci.getString("name"));
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
