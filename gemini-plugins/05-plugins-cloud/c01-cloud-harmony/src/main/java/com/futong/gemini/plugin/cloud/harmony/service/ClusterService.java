package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class ClusterService {
    public static BaseResponse queryClusterInfo(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = StrUtil.format("/olympus-core/clusters/{}", request.getBody().getModel().getString("cluster"));
        JSONObject result = client.doGetDataJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse createCluster(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        JSONObject result = client.doPostJSON("/olympus-portal/apis/v1/clusters-create", request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse addCluster(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        JSONObject result = client.doPostJSON("/olympus-core/clusters/command", request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse deleteCluster(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String clusterName = request.getBody().getCloud().getString("clusterName");
        client.doDelete("/olympus-core/cluster/" + clusterName, null);
        return BaseResponse.SUCCESS;
    }

    public static BaseResponse validYaml(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/yaml";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("yaml", request.getBody().getCloud().toJSONString());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
