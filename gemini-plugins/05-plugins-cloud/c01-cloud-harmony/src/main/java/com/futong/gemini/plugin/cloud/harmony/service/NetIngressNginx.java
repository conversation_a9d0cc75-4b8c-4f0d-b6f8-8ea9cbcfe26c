package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * ingress路由管理
 */
@Slf4j
public class NetIngressNginx {
    /**
     * 查看内部服务详情
     * 文档《olympus-core》74.22
     * @param request
     * @return
     */
    public static BaseResponse queryNetServiceDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespaces}/{tag}/{internalServices}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 创建内部服务
     * 文档《olympus-core》1.2
     *{
     * "affinityTimeSec" : 0,
     * "annotations" : { },
     * "certificate" : "string",
     * "clusterName" : "string",
     * "cookieName" : "string",
     * "cookiePath" : "string",
     * "createTime" : "string",
     * "domains" : [ {
     * "domainName" : "string",
     * "rules" : [ {
     * "path" : "string",
     * "port" : 0,
     * "portName" : "string",
     * "serviceName" : "string"
     * } ]
     * } ],
     * "esHost" : "string",
     * "esName" : "string",
     * "esPwd" : "string",
     * "icName" : "string",
     * "icPort" : 0,
     * "icType" : "string",
     * "ingressType" : "string",
     * "labels" : { },
     * "logEnable" : true,
     * "name" : "string",
     * "namespace" : "string",
     * "passthrough" : true,
     * "protocol" : "string",
     * "rules" : [ {
     * "backends" : [ {
     * "port" : 0,
     * "serviceName" : "string",
     * "weight" : 0
     * } ],
     * "hosts" : [ { } ],
     * "paths" : [ { } ],
     * "priority" : 0,
     * "ruleName" : "string"
     * } ],
     * "stickySessionsFlag" : "string"
     * }
     * @param request
     * @return
     */
    public static BaseResponse createIngressNginx(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/ingresses";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = request.getBody().getCloud();
        JSONArray selector = cloud.getJSONArray("labels");
        JSONObject obj = new JSONObject();
        for (int i = 0; i < selector.size(); i++) {
            JSONObject jsonObject = selector.getJSONObject(i);
            obj.put(jsonObject.getString("key"),  jsonObject.getString("value"));
        }

        JSONObject ipWhiteList = cloud.getJSONObject("ipWhiteList");
        JSONArray whiteList = ipWhiteList.getJSONArray("whiteList");
        JSONArray whiteListArray = new JSONArray();
        for (int i = 0; i < whiteList.size(); i++) {
            JSONObject jsonObject = whiteList.getJSONObject(i);
            whiteListArray.add(jsonObject.getString("ip"));
        }
        ipWhiteList.put("whiteList", whiteListArray);
        cloud.put("ipWhiteList", ipWhiteList);
        cloud.put("labels", obj);
        log.info("createIngressNginx cloud:{}", cloud);
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除内部服务
     * 文档《olympus-core》1.4
     *
     * @param request
     * @return
     */
    public static BaseResponse deleteIngressNginx(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/ingresses/{name}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }


    /**
     * 修改内部服务
     * 文档《olympus-core》1.12
     *body:
     * {
     * "json" : "string",
     * "namespace" : "string",
     * "resourcesName" : "string"
     * }
     * @param request
     * @return
     */
    public static BaseResponse updateIngressNginx(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/ingresses";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());

        JSONObject cloud = request.getBody().getCloud();
        JSONArray selector = cloud.getJSONArray("labels");
        JSONObject obj = new JSONObject();
        for (int i = 0; i < selector.size(); i++) {
            JSONObject jsonObject = selector.getJSONObject(i);
            obj.put(jsonObject.getString("key"),  jsonObject.getString("value"));
        }

        JSONObject ipWhiteList = cloud.getJSONObject("ipWhiteList");
        JSONArray whiteList = ipWhiteList.getJSONArray("whiteList");
        JSONArray whiteListArray = new JSONArray();
        for (int i = 0; i < whiteList.size(); i++) {
            JSONObject jsonObject = whiteList.getJSONObject(i);
            whiteListArray.add(jsonObject.getString("ip"));
        }
        ipWhiteList.put("whiteList", whiteListArray);
        cloud.put("ipWhiteList", ipWhiteList);
        cloud.put("labels", obj);
        log.info("updateIngressNginx cloud:{}", cloud);
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
