package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class StorageClassService {

    /**
     * 查询存储服务列表
     * 文档《olympus-core》32.1
     *
     * @param request
     * @return
     */
    public static BaseResponse queryStorageClassList(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/storageservices";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSONArray(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 查询pvc详情
     * 文档《olympus-core》
     * operation=info 基本信息19.10
     * operation=pods pod容器组信息19.11
     * operation=describe 描述信息19.7
     * operation=event 事件列表19.8
     * operation=yaml yaml19.13
     *
     * @param request
     * @return
     */
    public static BaseResponse queryStorageClassDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/storageservices/{class}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
}
