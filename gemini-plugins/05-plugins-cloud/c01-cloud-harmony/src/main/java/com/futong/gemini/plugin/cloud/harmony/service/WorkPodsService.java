package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.DataUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class WorkPodsService {
    /**
     * 查询工作节点详情
     * 文档《olympus-core》
     * operation=null 基本信息22.4
     * operation=containers 容器组容器列表22.6
     * operation=containers/{container}/stderrlogs 容器组容器日志22.7
     * operation=describe 描述信息22.8
     * operation=event 事件列表22.9
     * operation=metadata 元数据22.10
     * operation=yaml yaml22.10
     * @param request
     * @return
     */
    public static BaseResponse queryWorkPodsDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/pods/{pods}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 获取容器组监控信息
     * 文档《caas-oam》页面抓取
     * @param request
     * @return
     */
    public static BaseResponse queryWorkPodsMonitor(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/caas-oam/cluster/{cluster}/namespace/{namespace}/getPodResource";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 获取容器组监控信息
     * 文档《caas-core》页面抓取
     * @param request
     * @return
     */
    public static BaseResponse queryWorkPodsContainerFile(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/caas-core/organizations/1/projects/1/clusters/{cluster}/namespace/{namespace}/pod/{pods}/filelist";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSONArray(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除容器组
     * 文档《olympus-core》22.5
     * @param request
     * @return
     */
    public static BaseResponse deleteWorkPods(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/pods/{pods}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }

}
