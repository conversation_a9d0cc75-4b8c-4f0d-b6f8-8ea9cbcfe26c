package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WorkJobService {

    /**
     * 文档《olympus-core》10.7
     * @param request
     * @return
     */
    public static BaseResponse queryWorkJobDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/jobs/{jobs}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        log.info("apiPath: {}", apiPath);
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse createWorkJob(BaseCloudRequest request) {
        ClusterService.validYaml(request);
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/jobs";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("json", request.getBody().getCloud().toJSONString());
        JSONObject metadata = request.getBody().getCloud().getJSONObject("metadata");
        cloud.put("namespace", metadata.getString("namespace"));
        cloud.put("resourcesName", metadata.getString("name"));
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }


    public static BaseResponse deleteWorkJob(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/jobs/{jobs}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }
    public static BaseResponse updateWorkJobYaml(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/jobs/yaml";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("json", request.getBody().getCloud().toJSONString());
        JSONObject ci = request.getBody().getCi();
        cloud.put("namespace", ci.getString("relationNamespaceName"));
        cloud.put("resourcesName", ci.getString("name"));
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
    public static BaseResponse updateWorkJobReplicas(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/jobs/{jobs}/replicas";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
