package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class RegistryService {
    //文档《caas-registry》10.1，用于创建工作负载选择租户镜像
    public static BaseResponse queryTenantProjectRegistries(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/caas-registry/organizations/{tenantId}/project/{projectId}/registries";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSONArray(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
    public static BaseResponse queryTenantProjectRegistriesImages(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/caas-registry/organizations/{tenantId}/project/{projectId}/registries/{registryId}/images";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
}
