package com.futong.gemini.plugin.cloud.harmony.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONPath;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;

import java.util.List;
import java.util.Map;

public class FetchService {
    public static BaseResponse fetchTenant(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            JSONArray resultData = client.doGetDataJSONArray("/olympus-core/core/organs", null);
            Map<Class, List> data = Convert.convertTenant(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取租户失败!"), e);
        }
    }

    public static BaseResponse fetchProject(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            JSONArray resultData = client.doGetDataJSONArray("/olympus-core/core/organs/projects", null);
            Map<Class, List> data = Convert.convertProject(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取集群失败!"), e);
        }
    }

    public static BaseResponse fetchCluster(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            JSONArray resultData = client.doGetDataJSONArray("/olympus-portal/apis/v1/clusters", null);
            Map<Class, List> data = Convert.convertCluster(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取集群失败!"), e);
        }
    }

    /**
     * 获取集群种子任务
     * 用于集群下的资源子任务拆分，可换入集群下资源获取Action
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchClusterSeed(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String childAction = request.getBody().getModel().getString("childAction");
            if (StrUtil.isEmpty(childAction)) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("未指定子任务的childAction！");
            }
            ActionType childActionType;
            try {
                childActionType = ActionType.fromCamelValue(childAction);
            } catch (Exception e) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("childAction不存在未能匹配ActionType！");
            }
            //获取在线的集群
            String result = client.doGet("/olympus-core/clusters/active", null);
            Object rawResult = JSONPath.eval(result, "$.data[*].id");
            if (ObjUtil.isEmpty(rawResult) || !(rawResult instanceof List)) {
                return BaseResponse.FAIL_OP_CLOUD.of("没有在线的集群!");
            }
            List<String> clusterNames = (List<String>) rawResult;
            if (CollUtil.isEmpty(clusterNames)) {
                return BaseResponse.FAIL_OP_CLOUD.of("没有在线的集群!");
            }
            BaseResponse response = BaseResponse.SUCCESS.of("共获取在线集群" + clusterNames.size() + "个!");
            //获取集群resultData的属性clusterName
            return BaseCloudService.toGourdResponse(response, clusterNames, (String t) -> {
                JobInfo jobInfo = new JobInfo();
                request.getBody().getModel().put("cluster", t);
                request.setAction(childActionType);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取集群失败!"), e);
        }
    }

    public static BaseResponse fetchNamespace(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/namespaces", request.getBody().getModel().getString("cluster"));
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNamespace(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取命名空间失败!"), e);
        }
    }

    public static BaseResponse fetchNode(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/nodes", request.getBody().getModel().getString("cluster"));
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNode(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取主机节点失败!"), e);
        }
    }

    public static BaseResponse fetchWorkDeployments(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/deployments", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertDeployments(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-无状态部署失败!"), e);
        }
    }

    public static BaseResponse fetchWorkStatefulsets(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/statefulsets", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertStatefulsets(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-有状态部署失败!"), e);
        }
    }

    /**
     * 获取内部服务olympus-core 22.12
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchWorkPods(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/pods", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertPods(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-pod容器组!"), e);
        }
    }

    /**
     * 获取守护进程olympus-core 8.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchWorkDeamonset(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/daemonsets", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertDeamonset(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-守护进程!"), e);
        }
    }

    /**
     * 获取普通任务 olympus-core 10.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchWorkJob(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/jobs", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertJob(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-普通任务!"), e);
        }
    }


    /**
     * 获取普通任务 olympus-core 7.1
     * @param request
     * @return
     */
    public static BaseResponse fetchWorkCronjob(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/cronjobs", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertCronjob(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取工作负载-普通任务!"), e);
        }
    }

    public static BaseResponse fetchConfigMap(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/configmaps", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertConfigMap(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("配置-配置文件!"), e);
        }
    }

    /**
     * 获取内部服务olympus-core 20.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchStoragePv(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/persistentvolume", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertStoragePv(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("存储-pv!"), e);
        }
    }

    /**
     * 获取内部服务olympus-core 19.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchStoragePvc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/pvc", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertStoragePvc(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("存储-pvc!"), e);
        }
    }

    /**
     * 获取存储服务 olympus-core 32.1
     * @param request
     * @return
     */
    public static BaseResponse fetchStorageClass(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/storageservices", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertStorageClass(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("存储-存储服务class!"), e);
        }
    }

    /**
     * 获取内部服务olympus-core 74.2
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNetServiceInternal(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String apiPath = StrUtil.format("/olympus-core/clusters/namespaces/internalServices");
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNetInternalService(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("网络内部服务!"), e);
        }
    }

    /**
     * 获取外部服务olympus-core 74.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNetServiceExternal(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String apiPath = StrUtil.format("/olympus-core/clusters/namespaces/externalServices");
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNetExternalService(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("网络外部服务!"), e);
        }
    }

    /**
     * 同步Ingress路由olympus-core 1.1
     *
     * @param request
     * @return
     */
    public static BaseResponse fetchNetNginxIngress(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/olympus-core/clusters/{}/ingresses", clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNginxIngress(request, resultData, clusterName);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取网络-ingress失败!"), e);
        }
    }


    /**
     * 获取负载均衡olympus-core 67.1
     * @param request
     * @return
     */
    public static BaseResponse fetchNetNginxIc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String apiPath = StrUtil.format("/olympus-core/clusters/ics");
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertNetNginxIc(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取负载均衡失败!"), e);
        }
    }

    /**
     * 获取中间件服务Mysql《中间件接口文档v4.4.1》2.15
     * @param request
     * @return
     */
    public static BaseResponse fetchMiddlewareMysql(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/zeus-api/V4/middlewares/middlewareList?clusterId={}&namespace=*&type=MySQL",clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertMiddlewareMysql(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取中间件-MySQL失败!"), e);
        }
    }

    /**
     * 获取中间件服务Mysql《中间件接口文档v4.4.1》2.15
     * @param request
     * @return
     */
    public static BaseResponse fetchMiddlewareRedis(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/zeus-api/V4/middlewares/middlewareList?clusterId={}&namespace=*&type=Redis",clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertMiddlewareRedis(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取中间件-Redis失败!"), e);
        }
    }

    /**
     * 获取中间件服务MongoDB《中间件接口文档v4.4.1》2.15
     * @param request
     * @return
     */
    public static BaseResponse fetchMiddlewareMongoDB(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/zeus-api/V4/middlewares/middlewareList?clusterId={}&namespace=*&type=MongoDB",clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertMiddlewareMongoDB(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取中间件-,MongoDB失败!"), e);
        }
    }

    /**
     * 获取中间件服务RocketMQ《中间件接口文档v4.4.1》2.15
     * @param request
     * @return
     */
    public static BaseResponse fetchMiddlewareRocketMQ(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        try {
            String clusterName = request.getBody().getModel().getString("cluster");
            String apiPath = StrUtil.format("/zeus-api/V4/middlewares/middlewareList?clusterId={}&namespace=*&type=RocketMQ",clusterName);
            JSONArray resultData = client.doGetDataJSONArray(apiPath, null);
            Map<Class, List> data = Convert.convertMiddlewareRocketMQ(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取中间件-,RocketMQ失败!"), e);
        }
    }

}
