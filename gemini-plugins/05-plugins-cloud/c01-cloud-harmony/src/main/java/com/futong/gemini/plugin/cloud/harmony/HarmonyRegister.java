package com.futong.gemini.plugin.cloud.harmony;

import com.futong.gemini.plugin.cloud.harmony.sampler.BaseService;
import com.futong.gemini.plugin.cloud.harmony.sampler.FetchService;
import com.futong.gemini.plugin.cloud.harmony.service.*;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class HarmonyRegister extends BaseCloudRegister {
    @Override
    public void load() {
        //资源操作加载
        onAfterLoadPlatform();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadCluster();//加载集群操作
        onAfterLoadPool();//加载资源池操作
        onAfterLoadNode();//加载集群节点操作
        onAfterLoadNamespace();//加载集群节点操作
        onAfterLoadRegistry();//加载镜像仓库操作
        onAfterLoadWorkDeployments();//加载工作负载-无状态部署操作
        onAfterWorkStatefulsets();//加载工作负载-有状态部署操作
        onAfterWorkPods();//加载工作负载-pod容器组操作
        onAfterWorkDeamonset();//加载工作负载-守护进程
        onAfterWorkJob();//加载工作负载-普通任务
        onAfterWorkCronjob();//加载工作负载-定时任务
        onAfterLoadConfigMap();//配置挂载-配置文件
        onAfterLoadStorage();//存储
        onAfterLoadStoragePv();//存储-pv
        onAfterLoadStoragePvc();//存储-pvc
        onAfterLoadStorageClass();//存储-服务class
        onAfterLoadNetIcNginx();//配置网络Nginx负载均衡操作
        onAfterLoadNetServiceInternal();//网络内部服务操作
        onAfterLoadNetServiceExternal();//网络外部服务操作
        onAfterLoadNetIngressNginx();//网络路由服务操作
        onAfterLoadMiddleware();//中间件
    }


    public void onAfterLoadPlatform() {
        //基础操作
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);//添加默认调度任务
        //平台账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, BaseService::authAccount);//认证云账号
        register(ActionType.QUERY_CAAS_GENERAL_GET_DATA,BasicGeneralService::getData);//通用数据获取
        register(ActionType.QUERY_CAAS_GENERAL_POST_DATA,BasicGeneralService::postData);//通用数据获取
    }

    public void onAfterLoadFetch() {
        register(ActionType.FETCH_CAAS_TENANT, FetchService::fetchTenant);//同步租户
        register(ActionType.FETCH_CAAS_PROJECT, FetchService::fetchProject);//同步项目
        register(ActionType.FETCH_CAAS_CLUSTER, FetchService::fetchCluster);//同步集群
        register(ActionType.FETCH_CAAS_CLUSTER_SEED, FetchService::fetchClusterSeed);//同步集群资源种子任务
        register(ActionType.FETCH_CAAS_CLUSTER_NAMESPACE, FetchService::fetchNamespace);//同步集群命名空间
        register(ActionType.FETCH_CAAS_CLUSTER_NODE, FetchService::fetchNode);//同步集群主机节点
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_DEPLOYMENTS, FetchService::fetchWorkDeployments);//同步集群工作负载-无状态部署
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_STATEFULSETS, FetchService::fetchWorkStatefulsets);//同步集群工作负载-有状态部署
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_PODS, FetchService::fetchWorkPods);//同步集群工作负载-pod容器组
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_DEAMONSET, FetchService::fetchWorkDeamonset);//同步集群工作负载-守护进程
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_JOB, FetchService::fetchWorkJob);//同步集群工作负载-普通任务
        register(ActionType.FETCH_CAAS_CLUSTER_WORK_CRONJOB, FetchService::fetchWorkCronjob);//同步集群工作负载-定时任务
        register(ActionType.FETCH_CAAS_CLUSTER_CONFIG_MAP, FetchService::fetchConfigMap);//同步配置-配置文件
        register(ActionType.FETCH_CAAS_CLUSTER_STORAGE_PV, FetchService::fetchStoragePv);//同步存储-pv
        register(ActionType.FETCH_CAAS_CLUSTER_STORAGE_PVC, FetchService::fetchStoragePvc);//同步存储-pvc
        register(ActionType.FETCH_CAAS_CLUSTER_STORAGE_CLASS, FetchService::fetchStorageClass);//同步存储-服务class
        register(ActionType.FETCH_CAAS_CLUSTER_NET_SERVICE_INTERNAL, FetchService::fetchNetServiceInternal);//同步网络内部服务
        register(ActionType.FETCH_CAAS_CLUSTER_NET_SERVICE_EXTERNAL, FetchService::fetchNetServiceExternal);//同步网络外部服务
        register(ActionType.FETCH_CAAS_CLUSTER_NET_INGRESS_NGINX, FetchService::fetchNetNginxIngress);//同步Ingress路由
        register(ActionType.FETCH_CAAS_CLUSTER_NET_IC_NGINX, FetchService::fetchNetNginxIc);//同步nginx负载均衡
        register(ActionType.FETCH_CAAS_CLUSTER_MIDDLEWARE_MYSQL, FetchService::fetchMiddlewareMysql);//同步中间件-mysql
        register(ActionType.FETCH_CAAS_CLUSTER_MIDDLEWARE_REDIS, FetchService::fetchMiddlewareRedis);//同步中间件-redis
        register(ActionType.FETCH_CAAS_CLUSTER_MIDDLEWARE_MONGODB, FetchService::fetchMiddlewareMongoDB);//同步中间件-mongoDB
        register(ActionType.FETCH_CAAS_CLUSTER_MIDDLEWARE_ROCKETMQ, FetchService::fetchMiddlewareRocketMQ);//同步中间件-rocketMQ
    }

    public void onAfterLoadCluster() {
        register(ActionType.QUERY_CAAS_CLUSTER_DETAIL, ClusterService::queryClusterInfo);//查询集群详情
        register(ActionType.CREATE_CAAS_CLUSTER, ClusterService::createCluster);//创建集群
        register(ActionType.ADD_CAAS_CLUSTER, ClusterService::addCluster);//添加集群
        register(ActionType.DELETE_CAAS_CLUSTER, ClusterService::deleteCluster);//删除集群
        register(ActionType.VALID_CAAS_CLUSTER_YAML, ClusterService::validYaml);//校验yaml

    }

    public void onAfterLoadPool() {
        register(ActionType.QUERY_CAAS_CLUSTER_POOL_BY_TENANT_PROJECT, BusinessPoolService::queryPoolByTenantProject);//查询集群详情
    }

    public void onAfterLoadNode() {
        register(ActionType.QUERY_CAAS_CLUSTER_NODE_DETAIL, ClusterNodeService::queryNodeDetail);//查询集群详情
    }

    private void onAfterLoadRegistry() {
        register(ActionType.QUERY_CAAS_CLUSTER_REGISTRY_TENANT_PROJECT_LIST, RegistryService::queryTenantProjectRegistries)//查询集群详情
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.organizationId", "$.body.model.tenantId", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.projectId", "$.body.model.projectId", true, null);
        register(ActionType.QUERY_CAAS_CLUSTER_REGISTRY_IMAGES_TENANT_PROJECT, RegistryService::queryTenantProjectRegistriesImages)//查询集群详情
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.organizationId", "$.body.model.tenantId", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.projectId", "$.body.model.projectId", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.registryId", "$.body.model.registryId", true, null);
    }

    public void onAfterLoadNamespace() {
        register(ActionType.QUERY_CAAS_CLUSTER_NAMESPACE_TENANT_PROJECT_LIST, ClusterNamespaceService::queryTenantProjectNamespaceList)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.organizationId", "$.body.model.tenantId", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.projectId", "$.body.model.projectId", true, null);
        register(ActionType.QUERY_CAAS_CLUSTER_NAMESPACE_DETAIL, ClusterNamespaceService::queryNamespaceDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.namespace", true, null);
        register(ActionType.CREATE_CAAS_CLUSTER_NAMESPACE, ClusterNamespaceService::createNamespace);
        register(ActionType.DELETE_CAAS_CLUSTER_NAMESPACE, ClusterNamespaceService::deleteNamespace)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.namespace", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_NAMESPACE, ClusterNamespaceService::updateNamespace)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.namespace", true, null)
                .addTransferCloud("$.model.description", "$.description", false);
        register(ActionType.UPDATE_CAAS_CLUSTER_NAMESPACE_PROJECT, ClusterNamespaceService::updateNamespaceProject)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.namespace", true, null);
    }

    public void onAfterLoadWorkDeployments() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_DEPLOYMENTS_DETAIL, WorkDeploymentsService::queryWorkDeploymentsDetail);//查询详情
        register(ActionType.CREATE_CAAS_CLUSTER_WORK_DEPLOYMENTS, WorkDeploymentsService::createWorkDeployments);//查询详情
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_DEPLOYMENTS, WorkDeploymentsService::deleteWorkDeployments)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.deployment", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_DEPLOYMENTS_YAML, WorkDeploymentsService::updateWorkDeploymentsYaml)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_DEPLOYMENTS_REPLICAS, WorkDeploymentsService::updateWorkDeploymentsReplicas)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.deployment", true, null);
    }

    public void onAfterWorkStatefulsets() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_STATEFULSETS_DETAIL, WorkStatefulsetsService::queryWorkStatefulsetsDetail);//查询详情
        register(ActionType.CREATE_CAAS_CLUSTER_WORK_STATEFULSETS, WorkStatefulsetsService::createWorkStatefulsets);//查询详情
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_STATEFULSETS, WorkStatefulsetsService::deleteWorkStatefulsets)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.statefulset", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_STATEFULSETS_YAML, WorkStatefulsetsService::updateWorkStatefulsetsYaml)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_STATEFULSETS_REPLICAS, WorkStatefulsetsService::updateWorkStatefulsetsReplicas)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.statefulset", true, null);
    }

    public void onAfterWorkDeamonset() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_DEAMONSET_DETAIL, WorkDeamonsService::queryWorkDeamonsetsDetail)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.model.cluster", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.model.daemonsets", true,null);//查询详情
        register(ActionType.CREATE_CAAS_CLUSTER_WORK_DEAMONSET, WorkDeamonsService::createWorkDeamonsets);//创建
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_DEAMONSET, WorkDeamonsService::deleteWorkDeamonsets)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.daemonsets", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_DEAMONSET_YAML, WorkDeamonsService::updateWorkDeamonsetsYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
    }

    public void onAfterWorkJob() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_JOB_DETAIL, WorkJobService::queryWorkJobDetail)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.model.cluster", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.model.jobs", true,null);//查询详情
        register(ActionType.CREATE_CAAS_CLUSTER_WORK_JOB, WorkJobService::createWorkJob);//创建
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_JOB, WorkJobService::deleteWorkJob)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.jobs", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_JOB_YAML, WorkJobService::updateWorkJobYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
    }


    public void onAfterWorkCronjob() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_CRONJOB_DETAIL, WorkCronjobService::queryWorkCronjobDetail)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.model.cluster", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.model.cronjobs", true,null);//查询详情
        register(ActionType.CREATE_CAAS_CLUSTER_WORK_CRONJOB, WorkCronjobService::createWorkCronjob);//创建
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_CRONJOB, WorkCronjobService::deleteWorkCronjob)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.cronjobs", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_WORK_CRONJOB_YAML, WorkCronjobService::updateWorkCronjobYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
    }


    public void onAfterWorkPods() {
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_PODS_DETAIL, WorkPodsService::queryWorkPodsDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pods", true, null);
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_PODS_MONITOR, WorkPodsService::queryWorkPodsMonitor)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.cloud.podName", true, null);
        register(ActionType.QUERY_CAAS_CLUSTER_WORK_PODS_CONTAINER_FILE, WorkPodsService::queryWorkPodsContainerFile)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pods", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_WORK_PODS, WorkPodsService::deleteWorkPods)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pods", true, null);
    }

    public void onAfterLoadConfigMap() {
        register(ActionType.QUERY_CAAS_CLUSTER_CONFIG_MAP_DETAIL, ConfigMapService::queryConfigMapDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.configMaps", true, null);
        register(ActionType.CREATE_CAAS_CLUSTER_CONFIG_MAP, ConfigMapService::createConfigMap);
        register(ActionType.UPDATE_CAAS_CLUSTER_CONFIG_MAP_YAML, ConfigMapService::updateConfigMapYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_CONFIG_MAP, ConfigMapService::deleteConfigMap)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.configMap", true, null);
    }
    public void onAfterLoadStorage(){
        register(ActionType.QUERY_CAAS_CLUSTER_STORAGE_TYPE, StorageService::queryStorageType);
    }
    public void onAfterLoadStoragePv() {
        register(ActionType.QUERY_CAAS_CLUSTER_STORAGE_PV_DETAIL, StoragePvService::queryStoragePvDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pv", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.cloud.pvName", true, null);
        register(ActionType.CREATE_CAAS_CLUSTER_STORAGE_PV, StoragePvService::createStoragePv);
        register(ActionType.UPDATE_CAAS_CLUSTER_STORAGE_PV_YAML, StoragePvService::updateStoragePvYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_STORAGE_PV, StoragePvService::deleteStoragePv)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pv", true, null);

    }

    public void onAfterLoadStoragePvc() {
        register(ActionType.QUERY_CAAS_CLUSTER_STORAGE_PVC_DETAIL, StoragePvcService::queryStoragePvcDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.cloud.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pvc", true, null);
        register(ActionType.CREATE_CAAS_CLUSTER_STORAGE_PVC, StoragePvcService::createStoragePvc);
        register(ActionType.UPDATE_CAAS_CLUSTER_STORAGE_PVC_YAML, StoragePvcService::updateStoragePvcYaml)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pvc", true, null);
        register(ActionType.UPDATE_CAAS_CLUSTER_STORAGE_PVC_EXPAND, StoragePvcService::updateStorageExpand)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pvc", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.cloud.capacity", "$.body.model.capacity", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_STORAGE_PVC, StoragePvcService::deleteStoragePvc)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.pvc", true, null);

    }

    private void onAfterLoadStorageClass() {
        register(ActionType.QUERY_CAAS_CLUSTER_STORAGE_CLASS_LIST, StorageClassService::queryStorageClassList)//查询列表
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.QUERY_CAAS_CLUSTER_STORAGE_CLASS_DETAIL, StorageClassService::queryStorageClassDetail)//查询详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.class", true, null);
    }

    public void onAfterLoadNetIcNginx() {
        register(ActionType.QUERY_CAAS_CLUSTER_NET_IC_NGINX_DETAIL, NetIcService::queryIcInfoDetail)//查询nginx负载均衡详情
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.type", "$.body.model.type", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);

        register(ActionType.CREATE_CAAS_CLUSTER_NET_IC_NGINX, NetIcService::createNetIcService);//创建
        register(ActionType.UPDATE_CAAS_CLUSTER_NET_IC_NGINX, NetIcService::updateNetIc)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_NET_IC_NGINX, NetIcService::deleteNetIc)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.type", "$.body.model.type", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);
    }

    public void onAfterLoadNetServiceInternal() {
        register(ActionType.QUERY_CAAS_CLUSTER_NET_SERVICE_INTERNAL_DETAIL, NetInternalService::queryNetServiceDetail)//查询网络内部服务操作详情
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.model.cluster", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.model.internalServices", true,null);

        register(ActionType.CREATE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::createNetService);//创建内部服务
        register(ActionType.UPDATE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::updateNetService)//更新内部服务
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::deleteNetService)//删除内部服务
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);

    }


    public void onAfterLoadNetServiceExternal() {
        register(ActionType.QUERY_CAAS_CLUSTER_NET_SERVICE_INTERNAL_DETAIL, NetInternalService::queryNetServiceDetail)//查询网络内部服务操作详情
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.model.cluster", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.model.internalServices", true,null);

        register(ActionType.CREATE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::createNetService);//创建内部服务
        register(ActionType.UPDATE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::updateNetService)//更新内部服务
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationNamespaceName", "$.body.model.namespace", true, null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);
        register(ActionType.DELETE_CAAS_CLUSTER_NET_SERVICE_INTERNAL, NetInternalService::deleteNetService)//删除内部服务
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);

    }
    public void onAfterLoadNetIngressNginx() {
        register(ActionType.CREATE_CAAS_CLUSTER_NET_INGRESS_NGINX, NetIngressNginx::createIngressNginx);//创建ingressNginx
        register(ActionType.UPDATE_CAAS_CLUSTER_NET_INGRESS_NGINX, NetIngressNginx::updateIngressNginx)//更新yaml
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null);
        register(ActionType.DELETE_CAAS_CLUSTER_NET_INGRESS_NGINX, NetIngressNginx::deleteIngressNginx)//删除
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.relationClusterName", "$.body.model.cluster", true, null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.model.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE, "$.body.ci.name", "$.body.model.name", true, null);

    }

    public void onAfterLoadMiddleware() {
        register(ActionType.QUERY_CAAS_CLUSTER_MIDDLEWARE_DETAIL, MiddlewareService::queryMiddlewareDetail)//查询网络内部服务操作详情
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.cloud.clusterId", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.cloud.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.cloud.middlewareName", true,null);
        register(ActionType.DELETE_CAAS_CLUSTER_MIDDLEWARE, MiddlewareService::deleteMiddleware)//查询网络内部服务操作详情
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.cloud.clusterId", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.cloud.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.cloud.middlewareName", true,null);
        register(ActionType.REBOOT_CAAS_CLUSTER_MIDDLEWARE_POD, MiddlewareService::rebootMiddlewarePod)//查询网络内部服务操作详情
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationClusterName", "$.body.cloud.clusterId", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.relationNamespaceName", "$.body.cloud.namespace", true,null)
                .addTransfer(InterceptorType.BEFORE,"$.body.ci.name", "$.body.cloud.middlewareName", true,null);
    }

}
