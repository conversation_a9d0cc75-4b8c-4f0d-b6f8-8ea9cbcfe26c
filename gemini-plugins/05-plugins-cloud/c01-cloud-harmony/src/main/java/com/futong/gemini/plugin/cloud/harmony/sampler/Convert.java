package com.futong.gemini.plugin.cloud.harmony.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.utils.TimeUtils;
import com.futong.gemini.model.atlas.entity.*;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbTenant;
import com.futong.gemini.model.otc.bxc.entity.TmdbTenantLink;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class Convert {
    public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> devops = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                TmdbTenant tenant = new TmdbTenant();
                tenant.setCloud_type(request.getPlugin().getRealm());
                tenant.setOpen_id(info.getString("organId"));
                tenant.setFull_name(info.getString("organName"));
                tenant.setSimpl_name(info.getString("organName"));
                //云上状态： 0正常 1锁定
                tenant.setStatus(info.getIntValue("status") == 0 ? 1 : 2);
                tenant.setAccount_id(accessBean.getCmpId());
                tenant.setDict_code("tenant");
                tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", info.getString("organId")));
                tenant.setInfo_json(info.toJSONString());
                devops.add(tenant);
                TmdbTenantLink link = new TmdbTenantLink();
                link.setParent_tenant_id(null);
                link.setTenant_id(tenant.getBiz_id());
                link.setBiz_id("parent" + link.getTenant_id());
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbDevops.class, devops);
        result.put(TmdbTenantLink.class, links);
        return result;
    }

    public static Map<Class, List> convertProject(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> devops = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject organ = resultData.getJSONObject(i);
                JSONArray projectData = organ.getJSONArray("projects");
                for (int j = 0; j < projectData.size(); j++) {
                    JSONObject info = projectData.getJSONObject(j);
                    TmdbTenant tenant = new TmdbTenant();
                    tenant.setCloud_type(request.getPlugin().getRealm());
                    tenant.setOpen_id(info.getString("projectId"));
                    tenant.setFull_name(info.getString("projectName"));
                    tenant.setSimpl_name(info.getString("projectName"));
                    //云上状态： 0正常 1锁定
                    tenant.setStatus(1);
                    tenant.setAccount_id(accessBean.getCmpId());
                    tenant.setDict_code("project");
                    tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "project", info.getString("projectId")));
                    tenant.setInfo_json(info.toJSONString());
                    devops.add(tenant);
                    TmdbTenantLink link = new TmdbTenantLink();
                    link.setParent_tenant_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", info.getString("organId")));
                    link.setTenant_id(tenant.getBiz_id());
                    link.setBiz_id(link.getParent_tenant_id() + "_" + link.getTenant_id());
                    links.add(link);
                }
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbTenant.class, devops);
        result.put(TmdbTenantLink.class, links);
        return result;
    }

    public static Map<Class, List> convertCluster(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbDevops> devops = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        List<CaCaasCluster> clusters = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                TmdbDevops ops = new TmdbDevops();
                ops.setCloud_type(request.getPlugin().getRealm());
                ops.setDevops_value(info.getString("clusterName"));
                ops.setDevops_name(info.getString("clusterName"));
                ops.setAccount_id(accessBean.getCmpId());
                ops.setDict_code("cluster");
                ops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "cluster", info.getString("clusterName")));
                ops.setInfo_json(info.toJSONString());
                devops.add(ops);
                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setParent_devops_id(null);
                link.setDevops_id(ops.getBiz_id());
                link.setBiz_id("parent" + link.getDevops_id());
                links.add(link);
                CaCaasCluster cluster = new CaCaasCluster();
                cluster.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", info.getString("clusterName")));
                cluster.setOpen_id(info.getString("clusterName"));
                cluster.setName(info.getString("clusterName"));
                cluster.setNode_count(info.getInteger("nodeCount"));
                cluster.setVersion(info.getString("k8sVersion"));
                cluster.setStatus(info.getString("status"));
                cluster.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                cluster.setDescription(info.getString("description"));
                cluster.setCloud_type(request.getPlugin().getRealm());
                cluster.setCloud_account_id(accessBean.getCmpId());
                cluster.setSub_account_id(accessBean.getCmpId());
                cluster.setOpen_json(info.toJSONString());
                clusters.add(cluster);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbDevops.class, devops);
        result.put(TmdbDevopsLink.class, links);
        result.put(CaCaasCluster.class, clusters);
        return result;
    }

    public static Map<Class, List> convertNamespace(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbDevops> devops = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        List<CaCaasClusterNamespace> namespaces = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                TmdbDevops ops = new TmdbDevops();
                ops.setCloud_type(request.getPlugin().getRealm());
                ops.setDevops_value(info.getString("name"));
                ops.setDevops_name(info.getString("name"));
                ops.setAccount_id(accessBean.getCmpId());
                ops.setDict_code("namespace");
                ops.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "namespace", info.getString("clusterName"), info.getString("name")));
                ops.setInfo_json(info.toJSONString());
                devops.add(ops);
                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), "cluster", info.getString("clusterName")));
                link.setDevops_id(ops.getBiz_id());
                link.setBiz_id(link.getParent_devops_id() + "_" + link.getDevops_id());
                links.add(link);
                CaCaasClusterNamespace namespace = new CaCaasClusterNamespace();
                namespace.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", info.getString("clusterName"), info.getString("name")));
                namespace.setOpen_id(info.getString("name"));
                namespace.setName(info.getString("name"));
                namespace.setStatus(info.getString("status"));
                namespace.setCpu_size(info.getFloat("cpu"));
                namespace.setCpu_used(info.getFloat("cpuRequest"));
                namespace.setMemory_size(info.getFloat("memory"));
                namespace.setMemory_used(info.getFloat("memoryRequest"));
                namespace.setGpu_core(info.getFloat("gpuCore"));
                namespace.setGpu_core_used(info.getFloat("gpuCoreRequest"));
                namespace.setGpu_memory(info.getFloat("gpuMemory"));
                namespace.setGpu_memory_used(info.getFloat("gpuMemoryRequest"));
                namespace.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                namespace.setDescription(info.getString("description"));
                namespace.setCloud_type(request.getPlugin().getRealm());
                namespace.setCloud_account_id(accessBean.getCmpId());
                namespace.setSub_account_id(accessBean.getCmpId());
                namespace.setOpen_json(info.toJSONString());
                namespace.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", info.getString("clusterName")));
                namespace.setRelation_cluster_name(info.getString("clusterName"));
                namespace.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", info.getString("clusterName")));
                namespace.setRelation_devops_namespace(ops.getBiz_id());
                JSONObject labels = info.getJSONObject("labels");
                if (CollUtil.isNotEmpty(labels)) {
                    if (StrUtil.isNotEmpty(labels.getString("skyview/organ"))) {
                        namespace.setRelation_tenant_tenant(IdUtils.encryptId(accessBean.getCmpId(), "tenant", labels.getString("skyview/organ")));
                        namespace.setRelation_tenant_id(labels.getString("skyview/organ"));
                    }
                    if (StrUtil.isNotEmpty(labels.getString("skyview/project"))) {
                        namespace.setRelation_tenant_project(IdUtils.encryptId(accessBean.getCmpId(), "project", labels.getString("skyview/project")));
                        namespace.setRelation_project_id(labels.getString("skyview/project"));
                    }
                }
                namespaces.add(namespace);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbDevops.class, devops);
        result.put(TmdbDevopsLink.class, links);
        result.put(CaCaasClusterNamespace.class, namespaces);
        return result;
    }


    public static Map<Class, List> convertNode(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterNode> nodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterNode node = new CaCaasClusterNode();
                node.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_node", info.getString("clusterName"), info.getString("name")));
                node.setOpen_id(info.getString("name"));
                node.setName(info.getString("name"));
                node.setIp(info.getString("ip"));
                node.setType(info.getString("type"));
                node.setStatus(info.getString("status"));
                node.setCpu_size(info.getFloat("cpu"));
                node.setCpu_used(info.getFloat("cpuRequest"));
                node.setMemory_size(info.getFloat("memory"));
                node.setMemory_used(info.getFloat("memoryRequest"));
                node.setGpu_core(info.getFloat("gpuCore"));
                node.setGpu_core_used(info.getFloat("gpuCoreRequest"));
                node.setGpu_memory(info.getFloat("gpuMemory"));
                node.setGpu_memory_used(info.getFloat("gpuMemoryRequest"));
                node.setPod_count(info.getInteger("pods"));
                node.setPod_used(info.getInteger("podUsage"));
                node.setSchedulable(info.getBoolean("schedulable"));
                node.setArchitecture(info.getString("architecture"));
                node.setDescription(info.getString("description"));
                node.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("creationTime")));
                node.setOpen_json(info.toJSONString());
                node.setCloud_type(request.getPlugin().getRealm());
                node.setCloud_account_id(accessBean.getCmpId());
                node.setSub_account_id(accessBean.getCmpId());
                node.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", info.getString("clusterName")));
                node.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", info.getString("clusterName")));
                nodes.add(node);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterNode.class, nodes);
        return result;
    }

    public static Map<Class, List> convertDeployments(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkDeployments> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkDeployments work = new CaCaasClusterWorkDeployments();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_deployments", clusterName, info.getString("deploymentName")));
                work.setOpen_id(info.getString("deploymentName"));
                work.setName(info.getString("deploymentName"));
                work.setStatus(info.getString("status"));
                work.setReady_replicas(info.getInteger("readyReplicas"));
                work.setReplicas(info.getInteger("replicas"));
                work.setImage(info.getString("image"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkDeployments.class, works);
        return result;
    }

    public static Map<Class, List> convertStatefulsets(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkStatefulsets> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkStatefulsets work = new CaCaasClusterWorkStatefulsets();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_statefulsets", clusterName, info.getString("statefulSetName")));
                work.setOpen_id(info.getString("statefulSetName"));
                work.setName(info.getString("statefulSetName"));
                work.setStatus(info.getString("status"));
                work.setReady_replicas(info.getInteger("readyReplicas"));
                work.setReplicas(info.getInteger("replicas"));
                work.setImage(info.getString("image"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkStatefulsets.class, works);
        return result;
    }

    public static Map<Class, List> convertPods(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkPods> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkPods work = new CaCaasClusterWorkPods();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_pods", clusterName, info.getString("podName")));
                work.setOpen_id(info.getString("podName"));
                work.setName(info.getString("podName"));
                work.setStatus(info.getString("phase"));
                work.setIp(info.getString("podIp"));
                work.setContainer_size(info.getInteger("containerSize"));
                work.setCpu_size(info.getFloatValue("cpuLimit"));
                work.setCpu_used(info.getFloatValue("cpuRequest"));
                work.setCpu_usage(info.getFloatValue("cpuUsage"));
                work.setMemory_size(info.getFloatValue("memoryLimit"));
                work.setMemory_used(info.getFloatValue("memoryRequest"));
                work.setMemory_usage(info.getFloatValue("memoryUsage"));
                work.setGpu_core(info.getFloatValue("gpuCoreLimit"));
                work.setGpu_core_used(info.getFloatValue("gpuCoreRequest"));
                work.setGpu_core_usage(info.getFloatValue("gpuCoreUsage"));
                work.setGpu_memory(info.getFloatValue("gpuMemoryLimit"));
                work.setGpu_memory_used(info.getFloatValue("gpuMemoryRequest"));
                work.setGpu_memory_usage(info.getFloatValue("gpuMemoryUsage"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkPods.class, works);
        return result;
    }


    public static Map<Class, List> convertDeamonset(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkDeamonset> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkDeamonset work = new CaCaasClusterWorkDeamonset();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_deamonset", clusterName, info.getString("daemonSetName")));
                work.setOpen_id(info.getString("daemonSetName"));
                work.setName(info.getString("daemonSetName"));
                work.setStatus(info.getString("state"));
                work.setReady_replicas(info.getInteger("readyReplicas"));
                work.setReplicas(info.getInteger("replicas"));
                work.setImage(info.getString("image"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkDeamonset.class, works);
        return result;
    }


    public static Map<Class, List> convertJob(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkJob> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkJob work = new CaCaasClusterWorkJob();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_job", clusterName, info.getString("jobName")));
                work.setOpen_id(info.getString("jobName"));
                work.setName(info.getString("jobName"));
                work.setStatus(info.getString("state"));
                work.setReady_replicas(info.getInteger("succeeded"));
                work.setReplicas(info.getInteger("completions"));
                work.setImage(info.getString("image"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkJob.class, works);
        return result;
    }

    public static Map<Class, List> convertCronjob(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterWorkCronjob> works = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterWorkCronjob work = new CaCaasClusterWorkCronjob();
                work.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_work_cronjob", clusterName, info.getString("cronJobName")));
                work.setOpen_id(info.getString("cronJobName"));
                work.setName(info.getString("cronJobName"));
                work.setStatus(info.getString("state"));
                work.setRunning_count(info.getInteger("runningCount"));
                work.setSchedule(info.getString("schedule"));
                work.setImage(info.getString("image"));
                work.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                work.setOpen_json(info.toJSONString());
                work.setCloud_type(request.getPlugin().getRealm());
                work.setCloud_account_id(accessBean.getCmpId());
                work.setSub_account_id(accessBean.getCmpId());
                work.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                work.setRelation_cluster_name(clusterName);
                work.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                work.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                work.setRelation_namespace_name(info.getString("namespace"));
                work.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                works.add(work);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterWorkCronjob.class, works);
        return result;
    }

    public static Map<Class, List> convertConfigMap(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterConfigMap> configs = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterConfigMap config = new CaCaasClusterConfigMap();
                config.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_config_map", clusterName, info.getString("uid")));
                config.setOpen_id(info.getString("uid"));
                config.setName(info.getString("name"));
                config.setMulti_resource(info.getBoolean("multiResource"));
                //内容过大不记录库
//                config.setData(info.getString("data"));
//                config.setBinary_data(info.getString("binaryData"));
                config.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTimes")));
                //内容过大不记录库
                info.remove("data");
                info.remove("binaryData");
                info.remove("annotations");
                info.remove("labels");
                config.setOpen_json(info.toJSONString());
                config.setCloud_type(request.getPlugin().getRealm());
                config.setCloud_account_id(accessBean.getCmpId());
                config.setSub_account_id(accessBean.getCmpId());
                config.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                config.setRelation_cluster_name(clusterName);
                config.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                config.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                config.setRelation_namespace_name(info.getString("namespace"));
                config.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                configs.add(config);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterConfigMap.class, configs);
        return result;
    }


    public static Map<Class, List> convertStoragePv(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterStoragePv> storages = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterStoragePv storage = new CaCaasClusterStoragePv();
                storage.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_storage_pv", clusterName, info.getString("pvName")));
                storage.setOpen_id(info.getString("pvName"));
                storage.setName(info.getString("pvName"));
                storage.setStatus(info.getString("phase"));
                storage.setAccess_modes(info.getString("accessModes"));
                storage.setRecycle_policy(info.getString("persistentVolumeReclaimPolicy"));
                storage.setSize(info.getFloatValue("storage"));
                storage.setStorage_type(info.getString("storageType"));
                storage.setStorage_type_name(info.getString("storageTypeName"));
                storage.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("creationTimestamp")));
                storage.setOpen_json(info.toJSONString());
                storage.setCloud_type(request.getPlugin().getRealm());
                storage.setCloud_account_id(accessBean.getCmpId());
                storage.setSub_account_id(accessBean.getCmpId());
                storage.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                storage.setRelation_cluster_name(clusterName);
                storage.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                if ("存储服务".equals(info.getString("storageType"))) {
                    storage.setRelation_storage_class(IdUtils.encryptId(accessBean.getCmpId(), "caas_storage_class", info.getString("storageName")));
                    storage.setRelation_storage_class_name(info.getString("storageName"));
                }
                storage.setRelation_cluster_name(clusterName);
                storages.add(storage);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterStoragePv.class, storages);
        return result;
    }

    public static Map<Class, List> convertStoragePvc(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterStoragePvc> storages = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterStoragePvc storage = new CaCaasClusterStoragePvc();
                storage.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_storage_pv", clusterName, info.getString("namespace"),info.getString("name")));
                storage.setOpen_id(info.getString("name"));
                storage.setName(info.getString("name"));
                storage.setStatus(info.getString("status"));
                storage.setAccess_mode(info.getString("accessMode"));
                storage.setAllow_scaling(info.getString("allowVolumeExpansion"));
                storage.setSize(info.getFloatValue("capacity"));
                storage.setDynamic(info.getString("dynamic"));
                storage.setStorage_class_name(info.getString("storageClassName"));
                storage.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                storage.setOpen_json(info.toJSONString());
                storage.setCloud_type(request.getPlugin().getRealm());
                storage.setCloud_account_id(accessBean.getCmpId());
                storage.setSub_account_id(accessBean.getCmpId());
                storage.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                storage.setRelation_cluster_name(clusterName);
                storage.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                storage.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                storage.setRelation_namespace_name(info.getString("namespace"));
                storage.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                if (StrUtil.isNotEmpty(info.getString("pvName"))) {
                    storage.setRelation_storage_pv(IdUtils.encryptId(accessBean.getCmpId(), "caas_storage_pv", info.getString("pvName")));
                    storage.setRelation_storage_pv_name(info.getString("pvName"));
                }
                storage.setRelation_cluster_name(clusterName);
                storages.add(storage);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterStoragePvc.class, storages);
        return result;
    }

    public static Map<Class, List> convertStorageClass(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterStorageClass> storages = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterStorageClass storage = new CaCaasClusterStorageClass();
                storage.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_storage_class", clusterName, info.getString("name")));
                storage.setOpen_id(info.getString("name"));
                storage.setName(info.getString("name"));
                storage.setAllow_scaling(info.getString("allowVolumeExpansion"));
                storage.setSize(info.getFloatValue("storage"));
                storage.setType(info.getString("type"));
                storage.setSupport_auto_provision(info.getBoolean("supportAutoProvision"));
                storage.setSupport_middleware(info.getBoolean("supportMiddleware"));
                storage.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                storage.setOpen_json(info.toJSONString());
                storage.setCloud_type(request.getPlugin().getRealm());
                storage.setCloud_account_id(accessBean.getCmpId());
                storage.setSub_account_id(accessBean.getCmpId());
                storage.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                storage.setRelation_cluster_name(clusterName);
                storage.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                storage.setRelation_cluster_name(clusterName);
                storages.add(storage);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterStorageClass.class, storages);
        return result;
    }

    public static Map<Class, List> convertNetInternalService(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterNetServiceInternal> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            log.info("采集内部服务数量:{}", resultData.size());
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterName");
                CaCaasClusterNetServiceInternal config = new CaCaasClusterNetServiceInternal();
                config.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_net_internal_service", clusterName,info.getString("namespace"), info.getString("internalServicesName")));
                config.setOpen_id(info.getString("internalServicesName"));
                config.setName(info.getString("internalServicesName"));
                config.setType(info.getString("internalServicesType"));
                config.setDescription(info.getString("description"));
                config.setApp_name(info.getString("appName"));
                config.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTimes")));
                config.setOpen_json(info.toJSONString());
                config.setCloud_type(request.getPlugin().getRealm());
                config.setCloud_account_id(accessBean.getCmpId());
                config.setSub_account_id(accessBean.getCmpId());
                config.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                config.setRelation_cluster_name(clusterName);
                config.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                config.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                config.setRelation_namespace_name(info.getString("namespace"));
                config.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(config);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterNetServiceInternal.class, infos);
        return result;
    }

    public static Map<Class, List> convertNetExternalService(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterNetServiceExternal> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            log.info("采集外部服务数量:{}", resultData.size());
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterName");
                CaCaasClusterNetServiceExternal config = new CaCaasClusterNetServiceExternal();
                config.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_net_external_service", clusterName, info.getString("namespace"),info.getString("externalServicesName")));
                config.setOpen_id(info.getString("externalServicesName"));
                config.setName(info.getString("externalServicesName"));
                config.setType(info.getString("externalServicesType"));
                config.setApp_name(info.getString("appName"));
                config.setInfo(info.getJSONArray("externalServiceInfo") == null ? "" : info.getJSONArray("externalServiceInfo").toJSONString());
                config.setDescription(info.getString("description"));
                config.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTimes")));
                config.setOpen_json(info.toJSONString());
                config.setCloud_type(request.getPlugin().getRealm());
                config.setCloud_account_id(accessBean.getCmpId());
                config.setSub_account_id(accessBean.getCmpId());
                config.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                config.setRelation_cluster_name(clusterName);
                config.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                config.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                config.setRelation_namespace_name(info.getString("namespace"));
                config.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(config);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterNetServiceExternal.class, infos);
        return result;
    }


    public static Map<Class, List> convertNginxIngress(BaseCloudRequest request, JSONArray resultData, String clusterName) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterNetNginxIngress> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            log.info("采集ingress服务数量:{}", resultData.size());
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                CaCaasClusterNetNginxIngress ingress = new CaCaasClusterNetNginxIngress();
                ingress.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_net_nginx_ingress", clusterName,info.getString("namespace"), info.getString("name")));
                ingress.setOpen_id(info.getString("name"));
                ingress.setName(info.getString("name"));
                ingress.setDescription(info.getString("description"));
                ingress.setLabel(info.getJSONObject("labels").toJSONString());
                JSONArray routes = info.getJSONArray("routes");
                if (CollUtil.isNotEmpty(routes)) {
                    JSONObject route = routes.getJSONObject(0);
                    ingress.setPath(route.getString("protocol") + "://" + route.getString("ip") + ":" + route.getInteger("icPort"));
                }
                ingress.setIc_name(info.getString("icName"));
                ingress.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                ingress.setOpen_json(info.toJSONString());
                ingress.setCloud_type(request.getPlugin().getRealm());
                ingress.setCloud_account_id(accessBean.getCmpId());
                ingress.setSub_account_id(accessBean.getCmpId());
                ingress.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                ingress.setRelation_cluster_name(clusterName);
                ingress.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                ingress.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                ingress.setRelation_namespace_name(info.getString("namespace"));
                ingress.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(ingress);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterNetNginxIngress.class, infos);
        return result;
    }

    public static Map<Class, List> convertNetNginxIc(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterNetNginxIc> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            log.info("采集负载均衡数量:{}", resultData.size());
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterName");
                CaCaasClusterNetNginxIc config = new CaCaasClusterNetNginxIc();
                config.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_net_nginx_ic", clusterName, info.getString("namespace"),info.getString("name")));
                config.setOpen_id(info.getString("name"));
                config.setName(info.getString("name"));
                config.setType(info.getString("type"));
                config.setDescription(info.getString("description"));
                config.setIp(CollUtil.isNotEmpty(info.getJSONArray("externalIPs")) ? info.getJSONArray("externalIPs").getString(0) : "");
                config.setPort(info.getString("httpPort"));
                config.setStatus(info.getString("status"));
                config.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTimes")));
                config.setOpen_json(info.toJSONString());
                config.setCloud_type(request.getPlugin().getRealm());
                config.setCloud_account_id(accessBean.getCmpId());
                config.setSub_account_id(accessBean.getCmpId());
                config.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                config.setRelation_cluster_name(clusterName);
                config.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                config.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                config.setRelation_namespace_name(info.getString("namespace"));
                config.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(config);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterNetNginxIc.class, infos);
        return result;
    }

    public static Map<Class, List> convertMiddlewareMysql(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterMiddlewareMysql> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterId");
                CaCaasClusterMiddlewareMysql middleware = new CaCaasClusterMiddlewareMysql();
                middleware.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_middleware_mysql", clusterName, info.getString("namespace"),info.getString("name")));
                middleware.setOpen_id(info.getString("name"));
                middleware.setName(info.getString("name"));
                middleware.setStatus(info.getString("status"));
                middleware.setDeploy_mod(info.getString("deployMod"));
                middleware.setLine_name(info.getString("middlewareBaselineName"));
                middleware.setVersion(info.getString("chartVersion"));
                middleware.setDisaster_role(info.getString("disasterRole"));
                middleware.setInstance_count(info.getIntValue("instanceCount"));
                middleware.setDescription(info.getString("description"));
                middleware.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                middleware.setOpen_json(info.toJSONString());
                middleware.setCloud_type(request.getPlugin().getRealm());
                middleware.setCloud_account_id(accessBean.getCmpId());
                middleware.setSub_account_id(accessBean.getCmpId());
                middleware.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                middleware.setRelation_cluster_name(clusterName);
                middleware.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                middleware.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                middleware.setRelation_namespace_name(info.getString("namespace"));
                middleware.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(middleware);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterMiddlewareMysql.class, infos);
        return result;
    }

    public static Map<Class, List> convertMiddlewareRedis(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterMiddlewareRedis> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterId");
                CaCaasClusterMiddlewareRedis middleware = new CaCaasClusterMiddlewareRedis();
                middleware.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_middleware_redis", clusterName, info.getString("namespace"),info.getString("name")));
                middleware.setOpen_id(info.getString("name"));
                middleware.setName(info.getString("name"));
                middleware.setStatus(info.getString("status"));
                middleware.setDeploy_mod(info.getString("deployMod"));
                middleware.setLine_name(info.getString("middlewareBaselineName"));
                middleware.setVersion(info.getString("chartVersion"));
                middleware.setDisaster_role(info.getString("disasterRole"));
                middleware.setInstance_count(info.getIntValue("instanceCount"));
                middleware.setDescription(info.getString("description"));
                middleware.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                middleware.setOpen_json(info.toJSONString());
                middleware.setCloud_type(request.getPlugin().getRealm());
                middleware.setCloud_account_id(accessBean.getCmpId());
                middleware.setSub_account_id(accessBean.getCmpId());
                middleware.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                middleware.setRelation_cluster_name(clusterName);
                middleware.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                middleware.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                middleware.setRelation_namespace_name(info.getString("namespace"));
                middleware.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(middleware);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterMiddlewareRedis.class, infos);
        return result;
    }

    public static Map<Class, List> convertMiddlewareMongoDB(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterMiddlewareMongodb> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterId");
                CaCaasClusterMiddlewareMongodb middleware = new CaCaasClusterMiddlewareMongodb();
                middleware.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_middleware_mongodb", clusterName, info.getString("namespace"),info.getString("name")));
                middleware.setOpen_id(info.getString("name"));
                middleware.setName(info.getString("name"));
                middleware.setStatus(info.getString("status"));
                middleware.setDeploy_mod(info.getString("deployMod"));
                middleware.setLine_name(info.getString("middlewareBaselineName"));
                middleware.setVersion(info.getString("chartVersion"));
                middleware.setDisaster_role(info.getString("disasterRole"));
                middleware.setInstance_count(info.getIntValue("instanceCount"));
                middleware.setDescription(info.getString("description"));
                middleware.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                middleware.setOpen_json(info.toJSONString());
                middleware.setCloud_type(request.getPlugin().getRealm());
                middleware.setCloud_account_id(accessBean.getCmpId());
                middleware.setSub_account_id(accessBean.getCmpId());
                middleware.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                middleware.setRelation_cluster_name(clusterName);
                middleware.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                middleware.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                middleware.setRelation_namespace_name(info.getString("namespace"));
                middleware.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(middleware);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterMiddlewareMongodb.class, infos);
        return result;
    }

    public static Map<Class, List> convertMiddlewareRocketMQ(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CaCaasClusterMiddlewareRocketmq> infos = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                String clusterName = info.getString("clusterId");
                CaCaasClusterMiddlewareRocketmq middleware = new CaCaasClusterMiddlewareRocketmq();
                middleware.setId(IdUtils.encryptId(accessBean.getCmpId(), "caas_middleware_rocketmq", clusterName, info.getString("namespace"),info.getString("name")));
                middleware.setOpen_id(info.getString("name"));
                middleware.setName(info.getString("name"));
                middleware.setStatus(info.getString("status"));
                middleware.setDeploy_mod(info.getString("deployMod"));
                middleware.setLine_name(info.getString("middlewareBaselineName"));
                middleware.setVersion(info.getString("chartVersion"));
                middleware.setDisaster_role(info.getString("disasterRole"));
                middleware.setInstance_count(info.getIntValue("instanceCount"));
                middleware.setDescription(info.getString("description"));
                middleware.setOpen_create_time(TimeUtils.utcStringToMilliLong(info.getString("createTime")));
                middleware.setOpen_json(info.toJSONString());
                middleware.setCloud_type(request.getPlugin().getRealm());
                middleware.setCloud_account_id(accessBean.getCmpId());
                middleware.setSub_account_id(accessBean.getCmpId());
                middleware.setRelation_cluster(IdUtils.encryptId(accessBean.getCmpId(), "caas_cluster", clusterName));
                middleware.setRelation_cluster_name(clusterName);
                middleware.setRelation_devops_cluster(IdUtils.encryptId(accessBean.getCmpId(), "cluster", clusterName));
                middleware.setRelation_namespace(IdUtils.encryptId(accessBean.getCmpId(), "caas_namespace", clusterName, info.getString("namespace")));
                middleware.setRelation_namespace_name(info.getString("namespace"));
                middleware.setRelation_devops_namespace(IdUtils.encryptId(accessBean.getCmpId(), "namespace", clusterName, info.getString("namespace")));
                infos.add(middleware);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CaCaasClusterMiddlewareRocketmq.class, infos);
        return result;
    }
}
