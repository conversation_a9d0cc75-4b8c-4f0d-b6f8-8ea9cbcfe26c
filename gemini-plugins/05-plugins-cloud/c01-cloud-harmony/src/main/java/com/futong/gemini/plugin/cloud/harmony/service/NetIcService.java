package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

/**
 * 负载均衡管理
 */
public class NetIcService {

    /**
     * 查询负载均衡详情
     * 文档《olympus-core》 67.4
     * @param request
     * @return
     */
    public static BaseResponse queryIcInfoDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/icTypes/{type}/ics/{name}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("routeType"))) {
            apiPath += "?routeType={routeType}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }


    /**
     * 创建负载均衡
     * 文档《olympus-core》67.10
     *{
     * "apiSixIngressControllerDTO" : {
     * "containerHttpPort" : 0,
     * "containerHttpsPort" : 0,
     * "etcdEnabled" : true,
     * "etcdHost" : [ { } ],
     * "etcdReplicaCount" : 0,
     * "hostNetworkEnable" : true,
     * "monitorEnable" : true,
     * "password" : "string",
     * "storageClass" : "string",
     * "user" : "string"
     * },
     * "byHelmNginxParams" : {
     * "annotations" : { },
     * "defaultHttpBackend" : "string",
     * "httpPort" : 0,
     * "httpsPort" : 0,
     * "image" : "string",
     * "labels" : { },
     * "nginxTmpl" : true,
     * "params" : { },
     * "type" : "string"
     * },
     * "clusterName" : "string",
     * "createBackend" : true,
     * "createTime" : "string",
     * "defaultServerPort" : 0,
     * "etcd" : {
     * "auth" : {
     * "rbac" : {
     * "password" : "string",
     * "user" : "string"
     * }
     * },
     * "etcdEnabled" : true,
     * "etcdInfoVO" : {
     * "auth" : {
     * "rbac" : {
     * "password" : "string",
     * "user" : "string"
     * }
     * },
     * "enabled" : true,
     * "host" : [ { } ],
     * "persistence" : {
     * "storageClass" : "string"
     * }
     * },
     * "host" : [ { } ],
     * "persistence" : {
     * "storageClass" : "string"
     * }
     * },
     * "externalIPs" : [ { } ],
     * "healthzPort" : 0,
     * "httpPort" : 0,
     * "httpsPort" : 0,
     * "name" : "string",
     * "namespace" : "string",
     * "nodeNames" : [ { } ],
     * "organList" : [ {
     * "code" : "string",
     * "createTime" : "string",
     * "description" : "string",
     * "organId" : "string",
     * "organName" : "string",
     * "status" : 0
     * } ],
     * "params" : { },
     * "portRange" : {
     * "maxPort" : 0,
     * "minPort" : 0
     * },
     * "profilerPort" : 0,
     * "projectList" : [ {
     * "createBy" : "string",
     * "createTime" : "string",
     * "description" : "string",
     * "organId" : "string",
     * "organName" : "string",
     * "projectId" : "string",
     * "projectName" : "string"
     * } ],
     * "shared" : true,
     * "sslPassThroughProxyPort" : 0,
     * "status" : "string",
     * "statusPort" : 0,
     * "streamPort" : 0,
     * "type" : "string"
     * }
     */
    public static BaseResponse createNetIcService(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/ics";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPostJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除负载均衡
     * 文档《olympus-core》67.6
     *
     * @param request
     * @return
     */
    public static BaseResponse deleteNetIc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/icTypes/{type}/ics/{name}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }


    /**
     * 修改负载均衡
     * 文档《olympus-core》67.9
     *body:
     * {
     * "apiSixIngressControllerDTO" : {
     * "containerHttpPort" : 0,
     * "containerHttpsPort" : 0,
     * "etcdEnabled" : true,
     * "etcdHost" : [ { } ],
     * "etcdReplicaCount" : 0,
     * "hostNetworkEnable" : true,
     * "monitorEnable" : true,
     * "password" : "string",
     * "storageClass" : "string",
     * "user" : "string"
     * },
     * "byHelmNginxParams" : {
     * "annotations" : { },
     * "defaultHttpBackend" : "string",
     * "httpPort" : 0,
     * "httpsPort" : 0,
     * "image" : "string",
     * "labels" : { },
     * "nginxTmpl" : true,
     * "params" : { },
     * "type" : "string"
     * },
     * "clusterName" : "string",
     * "createBackend" : true,
     * "createTime" : "string",
     * "defaultServerPort" : 0,
     * "etcd" : {
     * "auth" : {
     * "rbac" : {
     * "password" : "string",
     * "user" : "string"
     * }
     * },
     * "etcdEnabled" : true,
     * "etcdInfoVO" : {
     * "auth" : {
     * "rbac" : {
     * "password" : "string",
     * "user" : "string"
     * }
     * },
     * "enabled" : true,
     * "host" : [ { } ],
     * "persistence" : {
     * "storageClass" : "string"
     * }
     * },
     * "host" : [ { } ],
     * "persistence" : {
     * "storageClass" : "string"
     * }
     * },
     * "externalIPs" : [ { } ],
     * "healthzPort" : 0,
     * "httpPort" : 0,
     * "httpsPort" : 0,
     * "name" : "string",
     * "namespace" : "string",
     * "nodeNames" : [ { } ],
     * "organList" : [ {
     * "code" : "string",
     * "createTime" : "string",
     * "description" : "string",
     * "organId" : "string",
     * "organName" : "string",
     * "status" : 0
     * } ],
     * "params" : { },
     * "portRange" : {
     * "maxPort" : 0,
     * "minPort" : 0
     * },
     * "profilerPort" : 0,
     * "projectList" : [ {
     * "createBy" : "string",
     * "createTime" : "string",
     * "description" : "string",
     * "organId" : "string",
     * "organName" : "string",
     * "projectId" : "string",
     * "projectName" : "string"
     * } ],
     * "shared" : true,
     * "sslPassThroughProxyPort" : 0,
     * "status" : "string",
     * "statusPort" : 0,
     * "streamPort" : 0,
     * "type" : "string"
     * }
     * @param request
     * @return
     */
    public static BaseResponse updateNetIc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/ics";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
