package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class MiddlewareService {
    /**
     * 查询中间件详情（通用）
     * 文档《中间件接口文档v4.4.1》
     * operation=detail (基本信息)事件2.17
     * operation=events (基本信息)事件2.16
     * operation=middlewarePodList (实例详情)实例pod列表2.14
     * operation=topology (实例详情)拓扑信息2.10
     * operation=pvcList (运维操作)pvc列表2.13
     * operation=action/opsAction (运维操作)获取运维Action 1.3
     * operation=resources (运维操作)查询middleware相关资源2.12
     * operation=values (运维操作)获取资源yaml2.9
     * operation=versionList (运维操作)查询中间件版本列表2.8
     * operation=backup/list (数据安全)查询备份任务列表21.20
     * operation=service/internalServices (服务暴露)查询中间件集群内访问列表5.1 pathType=v2
     * operation=ExposeService/list (服务暴露)获取中间件服务暴露列表11.4
     * operation=components/logging (数据监控)？？？
     * operation=monitor/monitorUrl (数据监控)获取中间件数据监控地址9.2
     * operation=parameter/role (参数设置)获取服务可选节点类型16.3
     * operation=parameter/list (参数设置-参数列表)获取参数列表16.4
     * operation=parameter/history (参数设置-参数修改历史)获取参数列表16.5
     *
     * @param request
     * @return
     */
    public static BaseResponse queryMiddlewareDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/zeus-api/V4/middlewares";
        if ("v2".equals(request.getBody().getModel().getString("pathType"))) {
            apiPath = "/zeus-api/V4/middleware";
        }
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result;
        if ("post".equals(request.getBody().getModel().getString("methodType"))) {
            result = client.doPostDataByType(apiPath, request.getBody().getCloud(), dataType);
        } else {
            result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        }
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse deleteMiddleware(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/zeus-api/V4/middlewares/remove";
        String result = client.doDelete(apiPath, request.getBody().getCloud());
        return BaseResponse.SUCCESS.of(result);
    }

    public static BaseResponse rebootMiddlewarePod(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/zeus-api/V4/middlewares/restartPod";
        String result = client.doGet(apiPath, request.getBody().getCloud());
        return BaseResponse.SUCCESS.of(result);
    }

}
