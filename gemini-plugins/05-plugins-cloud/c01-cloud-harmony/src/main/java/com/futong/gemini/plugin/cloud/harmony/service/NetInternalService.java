package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.DataUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 内部网络服务管理
 */
@Slf4j
public class NetInternalService {
    /**
     * 查看内部服务详情
     * 文档《olympus-core》74.22
     * @param request
     * @return
     */
    public static BaseResponse queryNetServiceDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/{tag}/{internalServices}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 创建内部服务
     * 文档《olympus-core》74.17
     *{
     * "clusterIp" : "string",
     * "clusterName" : "string",
     * "description" : "string",
     * "exposeType" : "string",
     * "headless" : true,
     * "labels" : { },
     * "loadBalancerIP" : "string",
     * "namespace" : "string",
     * "selector" : { },
     * "serviceName" : "string",
     * "serviceParamDTOList" : [ {
     * "ip" : "string",
     * "nodePort" : "string",
     * "port" : "string",
     * "portName" : "string",
     * "protocol" : "string",
     * "targetPort" : "string"
     * } ],
     * "serviceType" : "string",
     * "sessionAffinity" : "string",
     * "timeoutSecond" : 0
     * }
     * @param request
     * @return
     */
    public static BaseResponse createNetService(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/internalServices";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = request.getBody().getCloud();
        JSONArray array = new JSONArray();
        array.add(null);
        cloud.put("externalServiceInfo", array);
        JSONArray list = cloud.getJSONArray("serviceParamDTOList");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            if("ClusterIP".equals(cloud.getString("exposeType")))
                jsonObject.remove("nodePort");
            jsonObject.put("portName", jsonObject.getString("protocol").toLowerCase()+"-"+jsonObject.getString("port"));
        }
        cloud.put("serviceParamDTOList", list);
        cloud.put("sessionAffinity", "None");

        JSONArray selector = cloud.getJSONArray("selector");
        JSONObject obj = new JSONObject();
        for (int i = 0; i < selector.size(); i++) {
            JSONObject jsonObject = selector.getJSONObject(i);
            obj.put(jsonObject.getString("key"),  jsonObject.getString("value"));
        }
        cloud.put("selector", obj);
        cloud.put("labels", obj);
        log.info("createNetService cloud:{}", cloud.toJSONString());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除内部服务
     * 文档《olympus-core》74.20
     *
     * @param request
     * @return
     */
    public static BaseResponse deleteNetService(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/internalServices/{name}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }


    /**
     * 修改内部服务
     * 文档《olympus-core》74.4
     *body:
     * {
     * "json" : "string",
     * "namespace" : "string",
     * "resourcesName" : "string"
     * }
     * @param request
     * @return
     */
    public static BaseResponse updateNetService(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/internalServices/{name}";
        log.info("request.getBody().getModel()==="+request.getBody().getModel());
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        log.info("apiPath={}", apiPath);
        JSONArray array = new JSONArray();
        array.add(null);
        JSONObject cloud = request.getBody().getCloud();
        cloud.put("externalServiceInfo", array);
        JSONArray list = cloud.getJSONArray("serviceParamDTOList");
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            if("ClusterIP".equals(cloud.getString("exposeType")))
                jsonObject.remove("nodePort");
            jsonObject.put("portName", jsonObject.getString("protocol").toLowerCase()+"-"+jsonObject.getString("port"));
        }
        cloud.put("serviceParamDTOList", list);
        cloud.put("sessionAffinity", "None");

        JSONArray selector = cloud.getJSONArray("selector");
        JSONObject obj = new JSONObject();
        for (int i = 0; i < selector.size(); i++) {
            JSONObject jsonObject = selector.getJSONObject(i);
            obj.put(jsonObject.getString("key"),  jsonObject.getString("value"));
        }
        cloud.put("selector", obj);
        cloud.put("labels", obj);
        log.info("updateNetService cloud:{}", cloud.toJSONString());
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
