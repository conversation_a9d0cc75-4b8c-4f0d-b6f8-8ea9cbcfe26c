package com.futong.gemini.plugin.cloud.harmony.sampler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;

@Slf4j
public class BaseService {
    public static String accountDispatch;
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
            client.doToken();
            return BaseResponse.SUCCESS;
        }catch (BaseException e){
            log.error("云账号信息认证失败", e);
            throw e;
        }catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }
    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        GourdProxy gourdProxy = SpringUtil.getBean(GourdProxy.class);
        //调用gourd服务-添加调度层级，新增云类型层级
        GourdUtils.addGourdLevel(request.getPlugin().getRealm(), null, "云调度-谐云");
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //删除旧得调度任务
        JSONObject oldWhere = new JSONObject();
        oldWhere.put("jobLevel", request.getBody().getAccess().getCmpId());
        BaseResponse response = gourdProxy.stopAndDeleteDispatcher(oldWhere);
        if (BaseResponse.SUCCESS.equals(response)) {
            log.info("删除旧得调度任务成功!");
        } else {
            log.error("删除旧得调度任务失败{}!", JSON.toJSONString(response));
            return BaseResponse.ERROR_BIZ.of("删除旧得调度任务失败");
        }
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        List<JSONObject> dispatchers = listAllDispatcher(result);
        //调用gourd服务-批量添加调度任务
        return gourdProxy.createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            }
        });
        return dispatchers;
    }
}
