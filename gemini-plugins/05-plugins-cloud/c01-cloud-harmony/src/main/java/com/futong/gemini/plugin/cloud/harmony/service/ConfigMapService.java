package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class ConfigMapService {
    //参考文档《olympus-core》6.12
    public static BaseResponse queryConfigMapDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/configmaps/{configMaps}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    //参考文档《olympus-portal》6.2
    public static BaseResponse createConfigMap(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/configmaps";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("yaml", request.getBody().getCloud().toJSONString());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    //参考文档《olympus-portal》6.11
    public static BaseResponse updateConfigMapYaml(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/configmaps/yaml";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("json", request.getBody().getCloud().toJSONString());
        JSONObject ci = request.getBody().getCi();
        cloud.put("namespace", ci.getString("relationNamespaceName"));
        cloud.put("resourcesName", ci.getString("name"));
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    //参考文档《olympus-portal》6.7
    public static BaseResponse deleteConfigMap(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}/configmaps/{configMap}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doDeleteJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
}
