package com.futong.gemini.plugin.cloud.harmony.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;

public class ClientUtils {
    //获取Client对象
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            //请求Client对象配置信息
            ConnectionConfig.Builder builder = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .username(body.getAccess().getUsername())
                            .password(body.getAccess().getPassword())
                            .build()
                    );
            if (StrUtil.isNotEmpty(body.getAccess().getJsonStr())) {
                String jsonStr = body.getAccess().getJsonStr();
                JSONObject json = JSONObject.parseObject(jsonStr);
                builder.proxy(json.getString("proxyHost"), json.getInteger("proxyPort"));
            }
            ConnectionConfig config = builder.build();
            return clazz.getConstructor(ConnectionConfig.class).newInstance(config);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
}
