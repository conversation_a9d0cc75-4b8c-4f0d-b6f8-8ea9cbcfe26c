package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.DataUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class StoragePvcService {
    /**
     * 查询pvc详情
     * 文档《olympus-core》
     * operation=info 基本信息19.10
     * operation=pods pod容器组信息19.11
     * operation=describe 描述信息19.7
     * operation=event 事件列表19.8
     * operation=yaml yaml19.13
     *
     * @param request
     * @return
     */
    public static BaseResponse queryStoragePvcDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc/{pvc}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 创建pvc
     * 文档《olympus-core》19.2
     *
     * @param request
     * @return
     */
    public static BaseResponse createStoragePvc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        //yaml数据处理
        JSONObject cloudJson = DataUtils.transformJsonLabelsAndAnnotations(request.getBody().getCloud());//转换标签和注释
        JSONObject requests = cloudJson.getJSONObject("spec").getJSONObject("resources").getJSONObject("requests");
        String storage=requests.getString("storage");
        requests.put("storage", storage+"Gi");
        //数据封装
        JSONObject cloud = new JSONObject();
        cloud.put("yaml", cloudJson.toJSONString());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 删除pvc
     * 文档《olympus-core》19.6
     *
     * @param request
     * @return
     */
    public static BaseResponse deleteStoragePvc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc/{pvc}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        client.doDelete(apiPath, null);
        return BaseResponse.SUCCESS;
    }

    /**
     * 只能更新元数据和读写权限
     * 文档《olympus-core》19.5
     *
     * @param request
     * @return
     */
    public static BaseResponse updateStoragePvc(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc/{pvc}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 扩容
     * 文档《olympus-core》19.9
     *
     * @param request
     * @return
     */
    public static BaseResponse updateStorageExpand(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc/{pvc}/expand?namespace={namespace}&capacity={capacity}";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    /**
     * 修改yaml
     * 文档《olympus-core》19.14
     *
     * @param request
     * @return
     */
    public static BaseResponse updateStoragePvcYaml(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/pvc/{pvc}/yaml?namespace={namespace}&withUpdate=true";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        JSONObject cloud = new JSONObject();
        cloud.put("yaml", request.getBody().getCloud().toJSONString());
        JSONObject ci = request.getBody().getCi();
        cloud.put("namespace", ci.getString("relationNamespaceName"));
        cloud.put("pvc", ci.getString("name"));
        JSONObject result = client.doPutJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
}
