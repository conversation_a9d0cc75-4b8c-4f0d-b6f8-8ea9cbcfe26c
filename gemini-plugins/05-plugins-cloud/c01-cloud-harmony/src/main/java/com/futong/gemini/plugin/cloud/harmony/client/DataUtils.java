package com.futong.gemini.plugin.cloud.harmony.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class DataUtils {
    public static JSONObject transformJsonLabelsAndAnnotations(JSONObject input) {

        // 处理 labels 字段
        JSONArray labelsArray = input.getJSONArray("labels");
        if (labelsArray != null) {
            JSONObject labelsMap = new JSONObject();
            for (Object obj : labelsArray) {
                if (obj instanceof JSONObject) {
                    JSONObject item = (JSONObject) obj;
                    String key = item.getString("key");
                    String value = item.getString("value");
                    if (StrUtil.isNotEmpty(key)) {
                        labelsMap.put(key, value);
                    }
                }
            }
            input.put("labels", labelsMap);
        }

        // 处理 annotations 字段
        JSONArray annotationsArray = input.getJSONArray("annotations");
        if (annotationsArray != null) {
            JSONObject annotationsMap = new JSONObject();
            for (Object obj : annotationsArray) {
                if (obj instanceof JSONObject) {
                    JSONObject item = (JSONObject) obj;
                    String key = item.getString("key");
                    String value = item.getString("value");
                    if (StrUtil.isNotEmpty(key)) {
                        annotationsMap.put(key, value);
                    }
                }
            }
            input.put("annotations", annotationsMap);
        }
        return input;
    }


}
