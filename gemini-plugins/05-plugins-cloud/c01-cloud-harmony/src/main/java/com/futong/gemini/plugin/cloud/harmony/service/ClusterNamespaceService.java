package com.futong.gemini.plugin.cloud.harmony.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.harmony.client.ClientUtils;
import com.futong.gemini.plugin.cloud.harmony.client.DataUtils;
import com.futong.gemini.plugin.cloud.harmony.client.HarmonyClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class ClusterNamespaceService {
    //http://*************/olympus-core/organizations/3967954446226358272/projects/1939486835094261760/namespaces?organizationId=3967954446226358272&projectId=1939486835094261760&multiResource=false
    public static BaseResponse queryTenantProjectNamespaceList(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/organizations/{tenantId}/projects/{projectId}/namespaces";
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        Object result = client.doGetDataJSONArray(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse queryNamespaceDetail(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = "/olympus-core/clusters/{cluster}/namespaces/{namespace}";
        if (StrUtil.isNotEmpty(request.getBody().getModel().getString("operation"))) {
            apiPath += "/{operation}";
        }
        apiPath = StrUtil.format(apiPath, request.getBody().getModel());
        String dataType = request.getBody().getModel().getString("dataType");
        Object result = client.doGetDataByType(apiPath, request.getBody().getCloud(), dataType);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse createNamespace(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = StrUtil.format("/olympus-core/clusters/{cluster}/namespaces", request.getBody().getModel());
        JSONObject cloud = DataUtils.transformJsonLabelsAndAnnotations(request.getBody().getCloud());
        JSONObject result = client.doPostJSON(apiPath, cloud);
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse deleteNamespace(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = StrUtil.format("/olympus-core/clusters/{cluster}/namespaces/{namespace}", request.getBody().getModel());
        Object result = client.doDelete(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse updateNamespaceProject(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = StrUtil.format("/olympus-core/clusters/{cluster}/namespaces/{namespace}/allocation", request.getBody().getModel());
        JSONObject result = client.doPostJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

    public static BaseResponse updateNamespace(BaseCloudRequest request) {
        HarmonyClient client = ClientUtils.client(HarmonyClient.class, request.getBody());
        String apiPath = StrUtil.format("/olympus-core/clusters/{cluster}/namespaces/{namespace}/description", request.getBody().getModel());
        JSONObject result = client.doPutJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }

}
