package com.futong.gemini.plugin.cloud.harmony.client;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

// 网络连接配置
@Getter
public class ConnectionConfig {
    private final String protocol;
    private final String host;
    private final String port;
    private final String endpoint;
    private final int connectTimeout;
    private final int socketTimeout;
    private final String proxyHost;
    private final Integer proxyPort;
    private final AuthConfig authConfig;

    private ConnectionConfig(Builder builder) {
        this.protocol = builder.protocol;
        this.host = builder.host;
        this.port = builder.port;
        this.endpoint = builder.endpoint;
        this.connectTimeout = builder.connectTimeout;
        this.socketTimeout = builder.socketTimeout;
        this.proxyHost = builder.proxyHost;
        this.proxyPort = builder.proxyPort;
        this.authConfig = builder.authConfig;
    }


    // Builder 模式
    public static class Builder {
        private String protocol = "http";
        private String host;
        private String port;
        private String endpoint;
        private int connectTimeout = 30000;
        private int socketTimeout = 30000;
        private String proxyHost;
        private Integer proxyPort;
        private AuthConfig authConfig;

        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }

        public Builder host(String host) {
            this.host = host;
            return this;
        }

        public Builder port(String port) {
            this.port = port;
            return this;
        }

        public Builder connectTimeout(int timeout) {
            this.connectTimeout = timeout;
            return this;
        }

        public Builder socketTimeout(int timeout) {
            this.socketTimeout = timeout;
            return this;
        }

        public Builder authConfig(AuthConfig config) {
            this.authConfig = config;
            return this;
        }

        public Builder endpoint(String endpoint) {
            this.endpoint = endpoint;
            return this;
        }

        public Builder proxy(String proxyHost, Integer proxyPort) {
            this.proxyHost = proxyHost;
            this.proxyPort = proxyPort;
            return this;
        }

        public ConnectionConfig build() {
            validate();
            return new ConnectionConfig(this);
        }

        private void validate() {
            if (StrUtil.isEmpty(host) && StrUtil.isEmpty(endpoint)) {
                throw new IllegalArgumentException("Missing required host or endpoint");
            }
        }
    }

    public String getAddress() {
        if (StrUtil.isNotEmpty(endpoint)) {
            return endpoint;
        }
        return protocol + "://" + host + ":" + port;
    }

}

