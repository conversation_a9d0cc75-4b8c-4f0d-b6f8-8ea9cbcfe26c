package com.futong.gemini.plugin.cloud.cloudos.e5.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosPort {

    private List<Object> allowed_address_pairs;

    private List<Object> extra_dhcp_opts;

    private String updated_at;

    private String device_owner;

    private Integer revision_number;

    @JsonProperty("binding:profile")
    private BindingProfile profile;

    private List<Fixed_ips> fixed_ips;

    private String id;

    private List<String> security_groups;

    @JsonProperty("binding:vif_details")
    private BindingVif_details vif_details;

    @JsonProperty("binding:vif_type")
    private String vif_type;

    private String mac_address;

    private String project_id;

    private String status;

    @JsonProperty("binding:host_id")
    private String host_id;

    private String description;

    private List<String> tags;

    private String name;

    private Boolean admin_state_up;

    private String network_id;

    private String tenant_id;

    private String created_at;

    @JsonProperty("binding:vnic_type")
    private String vnic_type;

    private String device_id;

    private String ip_allocation;

    @Data
    public static class BindingProfile {

        private String cvk_host;

        private String update_id;

        private String cvm_host;

    }

    @Data
    public static class Fixed_ips {

        private String subnet_id;

        private String ip_address;

    }

    @Data
    public static class BindingVif_details {

        private String status;

        private Boolean ovs_hybrid_plug;

        private String datapath_type;

        private List<String> secgroupIds;

        private Integer vlanId;

        private Boolean port_filter;

        private String fixed_ips;

    }
}
