package com.futong.gemini.plugin.cloud.cloudos.e5;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.function.FTAction;
import com.futong.gemini.plugin.cloud.cloudos.e5.sampler.FetchSampler;
import com.futong.gemini.plugin.cloud.cloudos.e5.service.*;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;

import java.util.HashMap;
import java.util.Map;

public class CloudosRegister {
    private static Map<String, FTAction> actions = new HashMap<>();


    public static <Q, R, C> void register(String fetchType, FTAction<JSONObject> ftAction) {
        actions.put(fetchType, ftAction);
    }

    public static boolean isNotExists(String action) {
        return !isExists(action);
    }

    public static boolean isExists(String action) {
        return actions.containsKey(action);
    }

    public static FTAction getAction(String action) {
        return actions.get(action);
    }

    public static void onAfterLoadCloudAccount() {
        register(ActionType.AUTH_PLATFORM_ACCOUNT.value(), PlatAuthService::authCloudAccount);//云账号验证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM.value(), AccountService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH.value(), AccountService::createFetchDispatch);//添加默认调度任务
    }
    public static void onAfterLoadFetch() {
        register(ActionType.FETCH_COMPUTE_INSTANCE.value(), FetchSampler::fetchInstance);//同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF.value(), PerfService::fetchInstancePerf);//同步云主机性能数据
        register(ActionType.FETCH_COMPUTE_NODE.value(), FetchSampler::fetchComputenode);//同步计算节点信息
        register(ActionType.FETCH_COMPUTE_HOST_PERF.value(), PerfService::fetchHostPerf);//同步物理机性能数据
        register(ActionType.FETCH_COMPUTE_FLAVOR.value(), FetchSampler::fetchFlavor);//同步规格
        register(ActionType.FETCH_STORAGE_IMAGE.value(), FetchSampler::fetchImage);//同步镜像
        register(ActionType.FETCH_STORAGE_DISK.value(), FetchSampler::fetchDisk);//同步磁盘
        register(ActionType.FETCH_NEUTRON_SWITCH.value(), FetchSampler::fetchNetwork);//同步经典网络
        register(ActionType.FETCH_NEUTRON_VPC.value(), FetchSampler::fetchVpc);//同步VPC
        register(ActionType.FETCH_NEUTRON_SUBNET.value(), FetchSampler::fetchSubnet);//同步子网
        register(ActionType.FETCH_NEUTRON_NIC.value(), FetchSampler::fetchCard);//同步网卡
        register(ActionType.FETCH_PLATFORM_KEYPAIR.value(), FetchSampler::fetchKeypair);//同步密钥对
        register(ActionType.FETCH_NEUTRON_ROUTE.value(), FetchSampler::fetchRouter);//同步路由
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP.value(), FetchSampler::fetchSecurityGroup);//同步安全组
        register(ActionType.FETCH_STORAGE_SNAPSHOT.value(), FetchSampler::fetchSnapshot);//同步快照
        register(ActionType.FETCH_PLATFORM_PROJECT.value(), FetchSampler::fetchProject);//同步组织
        register(ActionType.FETCH_PLATFORM_USER.value(), FetchSampler::fetchUser);//同步用户
        register(ActionType.FETCH_PLATFORM_AZONE.value(), FetchSampler::fetchAzone);//同步可用域
        register(ActionType.FETCH_PLATFORM_ALARM.value(), FetchSampler::fetchAlarm);//获取告警
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL.value(), FetchSampler::queryInstanceTotal);//同步云主机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL.value(), FetchSampler::queryHostTotal);//同步物理机数量
        register(ActionType.QUERY_COMPUTE_INSTANCE_PASSWORD.value(), FetchSampler::queryComputePassword);//获取云主机密码
        register(ActionType.QUERY_NETWORK_OUTPUT.value(), FetchSampler::queryNetworkOutput);//获取网络出口
        register("QueryAzone", FetchSampler::queryAzone);//获取区域
    }
    public static void onAfterLoadOperate() {
        register(ActionType.CREATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::createInstance);//创建云主机
        register(ActionType.START_COMPUTE_INSTANCE.value(), ComputeInstanceService::startInstance);//启动云主机
        register(ActionType.STOP_COMPUTE_INSTANCE.value(), ComputeInstanceService::stopInstance);//关闭云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE.value(), ComputeInstanceService::rebootInstance);//重启云主机
        register(ActionType.RESUME_COMPUTE_INSTANCE.value(), ComputeInstanceService::resumeInstance);//恢复云主机
        register(ActionType.PAUSE_COMPUTE_INSTANCE.value(), ComputeInstanceService::pauseInstance);//暂停云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE.value(), ComputeInstanceService::deleteInstance);//删除云主机
        register(ActionType.CONSOLE_COMPUTE_INSTANCE.value(), ComputeInstanceService::webConsole);//web控制台

        register(ActionType.REFRESH_COMPUTE_INSTANCE.value(), ComputeInstanceService::refleshInstance);//更新云主机

        register(ActionType.UPDATE_COMPUTE_INSTANCE.value(), ComputeInstanceService::updateInstance);//修改虚拟机

        register(ActionType.DELETE_NEUTRON_VPC.value(), NetworkVpcService::deleteVpc);//删除VPC


        register(ActionType.CREATE_STORAGE_DISK.value(), StorageDiskService::addStorageDisk);//创建云盘
        register(ActionType.DELETE_STORAGE_DISK.value(), StorageDiskService::deleteStorageDisk);//删除云盘
        register(ActionType.ATTACH_STORAGE_DISK.value(), StorageDiskService::attachStorageDisk);//挂载云盘
        register(ActionType.DETACH_STORAGE_DISK.value(), StorageDiskService::detachStorageDisk);//卸载云盘

        register(ActionType.CREATE_NEUTRON_VSWITCH.value(), NetworkVswitchService::createNetwork);//创建经典网络
        register(ActionType.DELETE_NEUTRON_VSWITCH.value(), NetworkVswitchService::deleteNetwork);//删除经典网络

        register(ActionType.DELETE_COMPUTE_IMAGE.value(), StorageImageService::deleteImage);//删除镜像


        register(ActionType.CREATE_COMPUTE_KEYPAIR.value(), PlatKeypairService::createKeyPair);//创建秘钥对
        register(ActionType.DELETE_COMPUTE_KEYPAIR.value(), PlatKeypairService::deleteKeyPair);//删除秘钥对


    }
}
