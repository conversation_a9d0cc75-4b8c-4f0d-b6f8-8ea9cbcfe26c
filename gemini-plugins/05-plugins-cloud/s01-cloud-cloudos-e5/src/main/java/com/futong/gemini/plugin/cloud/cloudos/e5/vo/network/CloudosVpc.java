package com.futong.gemini.plugin.cloud.cloudos.e5.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosVpc {

    private String id;

    private String name;

    private String description;

    private String status;

    private List<Object> tags;

    private String project_id;

    private String ipv4_cidr;

    private String external_gateway_info;

    private List<Object> routes;
}
