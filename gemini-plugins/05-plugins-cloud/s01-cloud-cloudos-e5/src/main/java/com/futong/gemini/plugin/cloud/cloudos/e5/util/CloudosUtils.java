package com.futong.gemini.plugin.cloud.cloudos.e5.util;

import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.plugin.cloud.cloudos.e5.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateVmRequest;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Slf4j
public class CloudosUtils {
    public static final CloudosUtils bean = new CloudosUtils();

    /**克隆虚拟机
     * @param request  请求body
     * @throws Exception
     */
    public static List<String> cloneVm(String url, CreateVmRequest request, HttpClientConfig config) throws Exception{
		if(BaseClient.bodys.get().containsKey("biz")) {
			request.setVmNum(1);
		}
		int amount = request.getVmNum();
		List<String> instancsIds = new ArrayList<>();
		if(amount>1) {
			String str = generateRandomString(4);
			String vmName = request.getVmName()+"-"+str+"-";
			for(int i=1;i<=amount;i++) {
				request.setVmName(vmName+i);
				request.setVmalias(vmName+i);
				JSONObject vmBody = getVmBody(request);
				log.info("创建cloudosurl={}",url+"?assign_userid="+request.getUserId());
				log.info("创建cloudos虚拟机参数={}",vmBody.toJSONString());
				String result = HttpClientUtil.post(url + "?assign_userid=" + request.getUserId(), vmBody.toJSONString(), config);
				log.info("创建cloudos虚拟机结果={}",result);
				JSONObject jsonObject = JSON.parseObject(result);
				JSONObject server = jsonObject.getJSONObject("server");
				if(server!=null) {
					String instanceId = server.getString("id");
					instancsIds.add(instanceId);
				}
			}
		}else {
			if(BaseClient.bodys.get().containsKey("biz")) {
				Integer resNum = BaseClient.bodys.get().getJSONObject("biz").getInteger("resNum");
				if(resNum!=0) {
					String vmName = request.getVmName() + "-" + resNum;
					request.setVmName(vmName);
				}
			}
			JSONObject vmBody = getVmBody(request);
			log.info("创建cloudosurl={}",url+"?assign_userid="+request.getUserId());
			log.info("创建cloudos虚拟机参数={}",vmBody.toJSONString());
			String result = HttpClientUtil.post(url+"?assign_userid="+request.getUserId(), vmBody.toJSONString(), config);
			log.info("创建cloudos虚拟机结果={}",result);
			JSONObject jsonObject = JSON.parseObject(result);
			JSONObject server = jsonObject.getJSONObject("server");
			if(server!=null) {
				String instanceId = server.getString("id");
				instancsIds.add(instanceId);
			}
		}
		return instancsIds;
	}

	/**
	 * 获取虚拟机body
	 * @param request
	 * @return
	 */
    public static JSONObject getVmBody(CreateVmRequest request) {
		JSONObject result = new JSONObject();
		JSONObject server = new JSONObject();
		try{
			String name = request.getVmName();
			String flavorRef = request.getFlavorId();
			String imageRef = request.getImageId();
			String zone_uuid = request.getZoneUuid();
			String availability_zone = request.getZoneName();
			String domainName = request.getDomainName();
			String hostName = request.getHostname();
			String hostIp = request.getHostip();
			String serverIp = request.getServerIp();
			String userName = request.getUserName();
			String securityGroups = request.getSecurityGroupData();
			JSONArray networks = request.getNetworks();
			String loginType = request.getLoginType();
			String alias = request.getVmalias();
			String description = request.getDescription();

			String enableAdminPass = "1";
			JSONObject metadata = new JSONObject();
			String adminPass = request.getAdminPass();
			String key_name = request.getKeyName();
			String storageTypeId = request.getStorageTypeId();
			String location = request.getLocation();
			server.put("name", name);
			server.put("flavorRef", flavorRef);
			server.put("imageRef", imageRef);
			JSONArray netArray = new JSONArray();
			if(networks!=null) {
				for (int i = 0; i < networks.size(); i++) {
					JSONObject net = new JSONObject();
					String uuid = networks.getString(i);
					net.put("uuid", uuid);
					net.put("enable_gateway", "true");
					netArray.add(net);
				}
			}
			server.put("networks", netArray);
			JSONArray sgs = new JSONArray();
			JSONObject sg = new JSONObject();
			sg.put("name","default");
			sgs.add(sg);
			server.put("security_groups", sgs);
			if(hostName != null)
				availability_zone = availability_zone + ":" + domainName + ":" + hostName+"(CVM"+serverIp+")";
			server.put("availability_zone",availability_zone);
			switch (loginType){
				case "2":
					server.put("adminPass", adminPass);
					break;
				case "3":
					server.put("key_name", key_name);
					break;
				case "4":
					enableAdminPass = "0";
					break;
				default:
					break;
			}
			metadata.put("enableAdminPass", enableAdminPass);
			metadata.put("user_name", userName);
			metadata.put("alias", alias);
			metadata.put("description", description);
			metadata.put("zone_uuid", zone_uuid);
			metadata.put("storageTypeId", Opt.ofNullable(storageTypeId).orElse(""));//系统盘存储类型
			if (!"".equals(metadata.getString("storageTypeId"))) {
				metadata.put("location", Opt.ofNullable(location).orElse(""));//系统盘存储池
			}
			metadata.put("h3c_extend_api", "true");//是否为h3c扩展api
			if(hostName!=null&&!"".equals(hostName))
				metadata.put("cvk", hostName+"(CVM"+hostIp+")");
			server.put("metadata", metadata);

		}catch (Exception e) {
			e.printStackTrace();
		}
		result.put("server",server);
		return result;

	}

	private static final String ALPHA_NUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
	private static final Random random = new Random();

	public static String generateRandomString(int length) {
		StringBuilder sb = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			int index = random.nextInt(ALPHA_NUMERIC.length());
			sb.append(ALPHA_NUMERIC.charAt(index));
		}
		return sb.toString();
	}


}
