package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosFlavor;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbFlavor;

import java.util.ArrayList;
import java.util.List;

/**
 * ComputeFlavorService类用于从云资源提供商获取计算规格信息，并转换为内部使用的数据格式
 */
public class ComputeFlavorService {

    // 单例实例
    public static final ComputeFlavorService bean = new ComputeFlavorService();

    /**
     * 根据提供的参数获取规格信息，并将其转换后发送消息
     *
     * @param arguments 请求参数，包含获取规格信息所需的必要信息
     */
    public void fetchFlavor(JSONObject arguments) {
        List<CmdbFlavor> flavorList = new ArrayList<>();
        List<CloudosFlavor> flavors = fetchCloudosFlavor(arguments);
        if(ObjectUtil.isNotEmpty(flavors)){
            flavors.forEach(flavor ->{
                // 规格资源数据模型转换
                flavorList.add(Converts.toNxcFlavor(BaseClient.auths.get(), flavor));
            });
        }
        // 推送规格数据
        BaseUtils.sendMessage(flavorList, arguments);
    }

    /**
     * 获取规格资源信息，并将其转换为CloudosFlavor格式
     *
     * @param arguments 请求参数，用于构建获取规格信息的请求
     * @return 返回CloudosFlavor格式的规格信息列表
     */
    public List<CloudosFlavor> fetchCloudosFlavor(JSONObject arguments){
        List<CloudosFlavor> flavors =  new ArrayList<>();
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(),new String[]{request.getAuthToken().getToken().getProject().getId(),"flavors/detail"}),
                ResourceEnum.FLAVOR.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 解析并转换JSON对象为CloudosFlavor对象
                CloudosFlavor flavor = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosFlavor>() {});
                flavors.add(flavor);
            });
        }
        return flavors;
    }
}
