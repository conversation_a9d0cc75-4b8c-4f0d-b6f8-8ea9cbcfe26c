package com.futong.gemini.plugin.cloud.cloudos.e5.vo;

import lombok.Data;

import java.util.Arrays;
import java.util.List;
@Data
public class CloudosTokenParam {

    private Auth auth;
    @Data
    public static class Domain {

        public Domain(){
            super();
        }
        public  Domain(String id){
            this.id = id;
        }
        private String id;
    }
    @Data
    public static class User {

        public User(){
            super();
        }
        public  User(Domain domain,String name,String password){
            this.domain = domain;
            this.name = name;
            this.password = password;
        }
        private Domain domain;
        private String name;
        private String password;
    }
    @Data
    public static class Password {
        private User user;
        public Password(){
            super();
        }
        public Password(User user){
            this.user = user;
        }
    }
    @Data
    public static class Identity {
        private List<String> methods = Arrays.asList("password");
        private Password password;

        public Identity(){
            super();
        }
        public Identity(Password password){
            this.password = password;
        }
    }
    @Data
    public static class Project {
        public Project(){
            super();
        }
        public Project(ScopeDomain domain,String name){
            this.name = name;
            this.domain = domain;
        }
        private String name;
        private ScopeDomain domain;
    }

    @Data
    public static class ScopeDomain {

        public ScopeDomain(){
            super();
        }
        public  ScopeDomain(String id){
            this.id = id;
        }
        private String id;
    }
    @Data
    public static class Scope {
        private Project project;

        public Scope(){
            super();
        }
        public Scope(Project project){
            this.project = project;
        }
    }
    @Data
    public static class Auth {
        private Identity identity;
        private Scope scope;
    }
}
