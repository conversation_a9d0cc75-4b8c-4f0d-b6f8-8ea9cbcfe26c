package com.futong.gemini.plugin.cloud.cloudos.e5.request;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosToken;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建虚拟机请求参数
 */
@Setter
@Getter
public class CreateVmRequest implements Serializable {
    private JSONObject auth;

    //虚拟机名称
    private String vmName;

    private String userId;

    //服务地址
    private String serverIp;
    private String imageId;

    //可用区id
    private String zoneUuid;

    //可用区名称
    private String zoneName;



    //模版id
    private String flavorId;

    private String projectId;

    private JSONArray networks;

    private String securityGroupData;


    //用户名
    private String userName;





    //主机名称
    private String hostname;
    private String hostip;

    //domain名称
    private String domainName;


    private JSONArray systemDisks;
    private JSONArray dataDisks;
    private int vmNum;



    private String loginType;

    private String vmalias;

    private String description;

    private String adminPass;
	private	String keyName;
	private	String storageTypeId;
	private	String location;

    private CloudosToken authToken;


}
