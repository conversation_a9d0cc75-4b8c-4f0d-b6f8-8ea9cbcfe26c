package com.futong.gemini.plugin.cloud.cloudos.e5.common;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.constant.dict.AlarmLevel;
import com.futong.constant.dict.CloudType;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosAlarm;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;

import java.util.List;

public class FetchConverts {
    public static AlarmInfoBean toAlarm(CloudosAlarm info) {
        AlarmInfoBean alarm = new AlarmInfoBean();
        try {
            alarm.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), info.getName(), info.getStart_at()));
            alarm.setAccountId(BaseClient.auths.get().getCmpId());
            alarm.setCloudType(CloudType.PRIVATE_HUASAN.value());
            alarm.setOpenId(info.getName());
            alarm.setOpenName(info.getName());
            alarm.setOpenLevel(info.getLevel());
            alarm.setAlarmId("");
            alarm.setAlarmName(info.getName());
            alarm.setDetail(info.getMsg());
            alarm.setClosedStatus(false);
            alarm.setCount(1);
            alarm.setFirstTime(info.getStart_at().replace("T", " ").replace("Z", ""));
            alarm.setCreateTime(info.getStart_at());
            switch (info.getLevel()) {
                case "紧急":
                    alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                    break;
                case "重要":
                    alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                    break;
                case "一般":
                    alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                    alarm.setClosedStatus(true);
                    break;
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return alarm;
    }

    public static void createInstanceEventJob(JSONObject arguments, String instanceId, String type) {
        try {
            PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
            JSONObject req = new JSONObject();
            JSONObject instance = new JSONObject();
            instance.put("ids", new String[]{instanceId});
            instance.put("resourceType", type);
            instance.put("auth", arguments.getJSONObject("auth"));
            req.put("auth", arguments.getJSONObject("auth"));
            req.put("body", instance);
            req.put("action", ActionType.REFRESH_COMPUTE_INSTANCE.value());
            toJobInfo(plugin.getRealm(), plugin.getVersion(), req);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void toJobInfo(String realm, String version, JSONObject jobRequest) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(realm);
        jobInfo.setVersion(version);
        jobInfo.setCount(1);
        jobInfo.setRequest(jobRequest);
        jobInfo.setTriggerTime(System.currentTimeMillis() + 30000);
        SpringUtil.getBean(GourdProxy.class).createTempEventJob(jobInfo);
    }
}
