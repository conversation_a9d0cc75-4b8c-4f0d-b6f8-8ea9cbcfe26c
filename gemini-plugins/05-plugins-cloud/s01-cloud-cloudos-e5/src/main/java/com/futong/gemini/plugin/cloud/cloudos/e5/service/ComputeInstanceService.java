package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.constant.dict.InstanceStatus;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.cloudos.e5.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CloudosInstancePassRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateVmRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.CloudosUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosFlavor;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosHost;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosInstance;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosImage;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OsType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import com.futong.utils.SecureTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ComputeInstanceService {

    public static final ComputeInstanceService bean = new ComputeInstanceService();

    public static BaseResponse pauseInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject params = new JSONObject();
        params.put("suspend", "1");
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId, "action"});
                HttpClientUtil.post(url, params.toJSONString(), config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
            } catch (Exception e) {
                log.error("删除云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "暂停云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse resumeInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject params = new JSONObject();
        params.put("resume", "1");
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId, "action"});
                HttpClientUtil.post(url, params.toJSONString(), config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
            } catch (Exception e) {
                log.error("删除云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse rebootInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());

        JSONObject params = new JSONObject();
        //重启类型，SOFT为软启动，HARD为硬启动。
        JSONObject type = new JSONObject();
        type.put("type", "SOFT");
        params.put("reboot", type);
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId, "action"});
                HttpClientUtil.post(url, params.toJSONString(), config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
            } catch (Exception e) {
                log.error("重启云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重启云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse startInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject params = new JSONObject();
        params.put("os-start", "1");
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId, "action"});
                HttpClientUtil.post(url, params.toJSONString(), config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
            } catch (Exception e) {
                log.error("开启云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "开启云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse stopInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject params = new JSONObject();
        params.put("os-stop", "1");
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId, "action"});
                HttpClientUtil.post(url, params.toJSONString(), config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
            } catch (Exception e) {
                log.error("关闭云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "关闭云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public void fetchInstance(JSONObject arguments) {
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/detail"}),
                ResourceEnum.SERVER.getValue());
        List<CmdbInstanceRes> instanceList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbDiskRes> diskList = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(array)) {
            Map<String, CloudosImage> imageMap = StorageImageService.bean.fetchCloudosImage(arguments).stream().collect(Collectors.toMap(CloudosImage::getId, Function.identity(), (key1, key2) -> key1));
            Map<String, CloudosFlavor> flavorMap = ComputeFlavorService.bean.fetchCloudosFlavor(arguments).stream().collect(Collectors.toMap(CloudosFlavor::getId, Function.identity(), (key1, key2) -> key1));
            array.forEach(inst -> {
                JSONObject instObj = (JSONObject) inst;
                /**转换json对象为 CloudosInstance*/
                CloudosInstance cloudosInstance = Converts.parseAndConvert(instObj.toJSONString(), new TypeReference<CloudosInstance>() {
                });
                /**云主机资源数据模型转换*/
                CmdbInstanceRes instance = Converts.toNxcInstance(BaseClient.auths.get(), cloudosInstance);
                /**从规格内获取CPU、内存信息补全模型*/
                if (ObjectUtil.isNotNull(cloudosInstance.getFlavor()) && ObjectUtil.isNotNull(flavorMap.get(cloudosInstance.getFlavor().getId()))) {
                    instance.setCpu_size(flavorMap.get(cloudosInstance.getFlavor().getId()).getVcpus());
                    instance.setMem_size(flavorMap.get(cloudosInstance.getFlavor().getId()).getRam());
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), cloudosInstance.getFlavor().getId(), new CmdbFlavor()), instance));
                    // 获取系统盘信息、云主机与磁盘关系
                    CmdbDiskRes disk = Converts.toNxcDisk(BaseClient.auths.get(), flavorMap.get(cloudosInstance.getFlavor().getId()));
                    if (ObjectUtil.isNotNull(disk)) {
                        disk.setOpen_id("fake" + IdUtils.encryptId(instance.getOpen_id(), ResourceType.CMDB_DISK_RES.value()));
                        disk.setOpen_name(instance.getOpen_name() + "_system_disk");
                        disk.setRes_id(IdUtils.encryptId(disk.getAccount_id(), disk.getCloud_type(), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));
                        associations.add(AssociationUtils.toAssociation(instance, disk));
                        diskList.add(disk);
                    }
                }
                /**云主机与镜像关系*/
                if (ObjectUtil.isNotNull(cloudosInstance.getImage()) && ObjectUtil.isNotNull(imageMap.get(cloudosInstance.getImage().getId()))) {
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), cloudosInstance.getImage().getId(), new CmdbImageRes()), instance));
                    // 云主机与操作系统关系
                    CmdbOsRes os = Converts.toNxcOsRes(BaseClient.auths.get(), imageMap.get(cloudosInstance.getImage().getId()));
                    osList.add(os);
                    associations.add(AssociationUtils.toAssociation(instance, os));
                }
                // 获取云主机下密钥对，并绑定云主机与密钥关系
                CmdbKeypairRes keypairRes = Converts.toNxcKeypair(BaseClient.auths.get(), cloudosInstance);
                if (ObjectUtil.isNotNull(keypairRes)) {
                    associations.add(AssociationUtils.toAssociation(keypairRes, instance));
                }
                //北新仓资源关系数据
                sets.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), cloudosInstance));
                instanceList.add(instance);
            });
            imageMap.clear();
            flavorMap.clear();
        }
        /**
         * 推送云主机数据
         */
        BaseUtils.sendMessage(instanceList, arguments);

        /**推送资源关系数据*/
        BaseUtils.sendMessage(associations, arguments);

        //推送操作系统数据
        BaseUtils.sendMessage(osList, arguments);
        //推送系统盘数据
        BaseUtils.sendMessage(diskList, arguments);
        //北新仓资源关系数据
        BaseUtils.sendMessage(sets, arguments);
    }

    /**
     * 创建云主机
     * @param arguments
     * @return
     */
    public static BaseResponse createInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cloudos创建虚拟机接受参数={}", arguments.toString());
        System.out.println(BaseClient.bodys.get());
        CloudAccessBean accessBean = BaseClient.auths.get();
        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers"});
        try {
            List<String> instanceIds = CloudosUtils.cloneVm(url, request, config);
            instanceIds.forEach(instanceId -> {
                FetchConverts.createInstanceEventJob(arguments,instanceId,"update");
                com.alibaba.fastjson.JSONObject biz = arguments.getJSONObject("body").getJSONObject("biz");
                if(biz!=null) {
                    log.info("创建虚拟机发起工单-instanceId--{}--bizid---{}", instanceId,IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instanceId));
                    biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instanceId));
                    FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                }
            });
        } catch (Exception e) {
            log.error("创建云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建云主机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteInstance(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceId});
                log.info("删除云主机url={}", url);
                HttpClientUtil.delete(url, config);
                FetchConverts.createInstanceEventJob(arguments,instanceId,"delete");
            } catch (Exception e) {
                log.error("删除云主机异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除云主机异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse webConsole(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        if(instanceIds==null||instanceIds.size()<=0) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "虚拟机参数为空");
        }
        try {
            JSONObject params = new JSONObject();
            JSONObject b = new JSONObject();
            b.put("type","novnc");
            params.put("os-getVNCConsole",b);
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers", instanceIds.get(0), "action"});
            String result = HttpClientUtil.post(url, params.toJSONString(),config);
            cn.hutool.json.JSONObject entries = JSONUtil.parseObj(result);
            cn.hutool.json.JSONObject console = entries.getJSONObject("console");
            message = console.getStr("url");
        } catch (Exception e) {
            log.error("获取云主机控制台异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机控制台异常");
        }
        return new BaseDataResponse(message);
    }

    public static BaseResponse deleteKeyPair(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs", instanceId});
                HttpClientUtil.delete(url, config);
            } catch (Exception e) {
                log.error("删除秘钥对异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除秘钥对异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    public static BaseResponse updateInstance(JSONObject arguments) {
        String message = "成功发起修改云主机请求.";
        log.info("vmware修改虚拟机接受参数={}", arguments.toString());
        CreateVmRequest request = BaseClient.bodys.get().toJavaObject(CreateVmRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "servers"});
        try {
        } catch (Exception e) {
            log.error("创建云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改云主机异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse refleshInstance(JSONObject arguments) {
        String message = "操作成功.";
        log.info("触发更新虚拟机状态");
        CloudAccessBean accessBean = BaseClient.auths.get();
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/detail"}),
                ResourceEnum.SERVER.getValue());
        List<CmdbInstanceRes> instanceList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<CmdbDiskRes> diskList = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        boolean flag = false;
        if("update".equals(request.getResourceType())) {
            if (ObjectUtil.isNotEmpty(array)) {
                Map<String, CloudosImage> imageMap = StorageImageService.bean.fetchCloudosImage(arguments).stream().collect(Collectors.toMap(CloudosImage::getId, Function.identity(), (key1, key2) -> key1));
                Map<String, CloudosFlavor> flavorMap = ComputeFlavorService.bean.fetchCloudosFlavor(arguments).stream().collect(Collectors.toMap(CloudosFlavor::getId, Function.identity(), (key1, key2) -> key1));
                for(int i = 0; i < array.size(); i++) {
                    JSONObject instObj = array.getJSONObject(i);
                    /**转换json对象为 CloudosInstance*/
                    CloudosInstance cloudosInstance = Converts.parseAndConvert(instObj.toJSONString(), new TypeReference<CloudosInstance>() {
                    });
                    /**云主机资源数据模型转换*/
                    CmdbInstanceRes instance = Converts.toNxcInstance(BaseClient.auths.get(), cloudosInstance);
                    log.info("虚拟机状态={}", instance.getOpen_status());

                    if("BUILD".equals(instance.getOpen_status())) {
                        flag = true;
                    }else {
                        if (ObjectUtil.isNotNull(cloudosInstance.getFlavor()) && ObjectUtil.isNotNull(flavorMap.get(cloudosInstance.getFlavor().getId()))) {
                            instance.setCpu_size(flavorMap.get(cloudosInstance.getFlavor().getId()).getVcpus());
                            instance.setMem_size(flavorMap.get(cloudosInstance.getFlavor().getId()).getRam());
                            associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), cloudosInstance.getFlavor().getId(), new CmdbFlavor()), instance));
                            // 获取系统盘信息、云主机与磁盘关系
                            CmdbDiskRes disk = Converts.toNxcDisk(BaseClient.auths.get(), flavorMap.get(cloudosInstance.getFlavor().getId()));
                            if (ObjectUtil.isNotNull(disk)) {
                                disk.setOpen_id("fake" + IdUtils.encryptId(instance.getOpen_id(), ResourceType.CMDB_DISK_RES.value()));
                                disk.setOpen_name(instance.getOpen_name() + "_system_disk");
                                disk.setRes_id(IdUtils.encryptId(disk.getAccount_id(), disk.getCloud_type(), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));
                                associations.add(AssociationUtils.toAssociation(instance, disk));
                                diskList.add(disk);
                            }
                        }
                        if (ObjectUtil.isNotNull(cloudosInstance.getImage()) && ObjectUtil.isNotNull(imageMap.get(cloudosInstance.getImage().getId()))) {
                            associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), cloudosInstance.getImage().getId(), new CmdbImageRes()), instance));
                            // 云主机与操作系统关系
                            CmdbOsRes os = Converts.toNxcOsRes(BaseClient.auths.get(), imageMap.get(cloudosInstance.getImage().getId()));
                            osList.add(os);
                            associations.add(AssociationUtils.toAssociation(instance, os));
                        }
                        // 获取云主机下密钥对，并绑定云主机与密钥关系
                        CmdbKeypairRes keypairRes = Converts.toNxcKeypair(BaseClient.auths.get(), cloudosInstance);
                        if (ObjectUtil.isNotNull(keypairRes)) {
                            associations.add(AssociationUtils.toAssociation(keypairRes, instance));
                        }
                        //北新仓资源关系数据
                        sets.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), cloudosInstance));
                        if(request.getIds().contains(instance.getOpen_id()))
                            instanceList.add(instance);
                    }
                }
                if(flag) {
                    PluginInfo plugin = arguments.getObject("plugin", PluginInfo.class);
                    JobInfo jobInfo = toJobInfo(plugin.getRealm(), plugin.getVersion(), arguments);
                    return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
                }
                BaseCloudService.toRefreshMessageAndSend("update", instanceList, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", associations, Association.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", osList, CmdbOsRes.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", diskList, CmdbDiskRes.class,accessBean.getCloudType(),accessBean.getCmpId());
                BaseCloudService.toRefreshMessageAndSend("update", sets, TmdbResourceSet.class,accessBean.getCloudType(),accessBean.getCmpId());
            }
        }else {
            List<CmdbInstanceRes> list = new ArrayList<>();
            request.getIds().forEach(vmId -> {
                CmdbInstanceRes ins = new CmdbInstanceRes();
                String resId= IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), vmId);
                ins.setRes_id(resId);
                list.add(ins);
            });
            BaseCloudService.toRefreshMessageAndSend("delete", list, CmdbInstanceRes.class,accessBean.getCloudType(),accessBean.getCmpId());
        }
        return BaseResponse.SUCCESS;
    }

    public static JobInfo toJobInfo(String realm, String version, JSONObject jobRequest) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setRealm(realm);
        jobInfo.setVersion(version);
        jobInfo.setCount(1);
        jobInfo.setRequest(jobRequest);
        jobInfo.setTriggerTime(System.currentTimeMillis() + 10000);
        return jobInfo;
    }

    public int queryInstanceTotal(DescribeCloudosRequest request) {
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/detail"}),
                ResourceEnum.SERVER.getValue());
        int count = 0;
        if(array!=null) {
            count = array.size();
        }
        return count;
    }

    public String queryInstancePass(CloudosInstancePassRequest request) {
        HttpClientConfig config = new HttpClientConfig();
        String pass = "";
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "/servers/",  request.getCi().getOpen_id()});
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.get(url, config);
        if(ObjectUtil.isNotEmpty(responseJson)) {
            JSONObject jsonObject = JSONObject.parseObject(responseJson);
            if (jsonObject.get("server") != null) {
                JSONObject server = jsonObject.getJSONObject("server");
                pass = server.getString("adminPass");
            }
        }
        return pass;
    }

    public List<JSONObject> queryNetworkOutput(CloudosInstancePassRequest request) {
        HttpClientConfig config = new HttpClientConfig();
        List<JSONObject> resultList = new ArrayList<>();
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetsecurityUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "h3c-phycial-mappings"});
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.get(url, config);
        if(ObjectUtil.isNotEmpty(responseJson)) {
            JSONObject obj = JSON.parseObject(responseJson);
            if (null != obj){
                JSONArray list = obj.getJSONArray("phycialmappinglist");
                for (int i = 0; i < list.size(); i++){
                    JSONObject jsonObject = list.getJSONObject(i);
                    JSONArray phycialMappingList = jsonObject.getJSONArray("PhycialMapping_info");
                    for (int y = 0; y < phycialMappingList.size(); y++){
                        JSONObject phycialObj = phycialMappingList.getJSONObject(y);
                        String phycialName = phycialObj.getString("phycial_name");
                        JSONObject result = new JSONObject();
                        result.put("azone",jsonObject.getString("azone"));
                        result.put("encodeName",phycialName);
                        result.put("decodeName",new String(Base64.getDecoder().decode(phycialName)));
                        resultList.add(result);
                    }
                }
            }
        }
        return resultList;
    }

    public List<JSONObject> queryAzone(CloudosInstancePassRequest request) {
        HttpClientConfig config = new HttpClientConfig();
        List<JSONObject> resultList = new ArrayList<>();
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAzoneUrl(), null);
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String responseJson = HttpClientUtil.get(url, config);
        String resourceType = request.getModel().getString("resourceType");
        String azoneName = request.getModel().getString("azoneName");
        String virtType = request.getModel().getString("virtType");
        JSONArray obj = JSON.parseArray(responseJson);
        if (null != obj){
            for (int i = 0; i < obj.size(); i++){
                JSONObject jsonObject = obj.getJSONObject(i);
                String zone = jsonObject.getString("zone");
                String type = jsonObject.getString("resourceType");
                String vir = jsonObject.getString("virtType");
                if (StringUtils.isNotEmpty(resourceType)){
                    if (!type.equals(resourceType)){
                        continue;
                    }
                }
                if(!"CAS".equals(vir))
                    continue;
                if (StringUtils.isNotEmpty(azoneName) && azoneName.equals(zone)){
                    resultList.add(jsonObject);
                }else if (vir.equals(virtType)){
                    resultList.add(jsonObject);
                }else if(StringUtils.isEmpty(virtType) && StringUtils.isEmpty(azoneName)) {
                    resultList.add(jsonObject);
                }
            }
        }
        return resultList;
    }

}
