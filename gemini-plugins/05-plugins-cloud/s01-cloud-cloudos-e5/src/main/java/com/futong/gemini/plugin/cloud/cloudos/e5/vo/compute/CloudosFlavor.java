package com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosFlavor {

    private Integer ram;
    @JsonProperty("OS-FLV-DISABLED:disabled")
    private Boolean disabled;

    @JsonProperty("os-flavor-access:is_public")
    private Boolean is_public;

    private Integer rxtx_factor;

    private Float disk;

    private String id;

    private String name;

    private Integer vcpus;

    private String swap;
    @JsonProperty("OS-FLV-EXT-DATA:ephemeral")
    private Integer ephemeral;

}

