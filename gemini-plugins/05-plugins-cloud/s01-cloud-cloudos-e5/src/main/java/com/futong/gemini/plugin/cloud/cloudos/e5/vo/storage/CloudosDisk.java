package com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosDisk {

    private String migration_status;

    private List<Attachments> attachments;

    private List<Links> links;

    private String availability_zone;

    @JsonProperty("os-vol-host-attr:host")
    private String host;

    private Boolean encrypted;

    private String updated_at;

    private String replication_status;

    private String snapshot_id;

    private String id;

    private Float size;

    private String user_id;

    @JsonProperty("os-vol-tenant-attr:tenant_id")
    private String tenant_id;
    @JsonProperty("os-vol-mig-status-attr:migstat")
    private String migstat;

    private Metadata metadata;

    private String status;

    private String description;

    private Boolean multiattach;

    private String source_volid;

    private String consistencygroup_id;
    @JsonProperty("os-vol-mig-status-attr:name_id")
    private String name_id;

    private String name;

    private String bootable;

    private String created_at;

    private String volume_type;



    @Data
    public static class Attachments {

        private String server_id;

        private String attachment_id;

        private String attached_at;

        private String host_name;

        private String volume_id;

        private String device;

        private String id;

    }

    @Data
    public static class Links {

        private String href;

        private String rel;

    }

    @Data
    public static class Metadata {

        private String azone_label;

        private String azone_uuid;

        private String storageTypeId;

        private String attached_mode;

        private String user_name;

        private String storage_type_name;

    }
}
