
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosSecurityGroup;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSecuritygroupRule;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class NetworkSecurityGroupService {

    public static final NetworkSecurityGroupService bean = new NetworkSecurityGroupService();

    public void fetchSecurityGroup(JSONObject arguments) {
        List<CloudosSecurityGroup> cloudosSecurityGroups = fetchCloudosSecurityGroup(arguments);
        List<CmdbSecuritygroupRes> list = new ArrayList<>();
        List<CmdbSecuritygroupRule> rules = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(cloudosSecurityGroups)){
            cloudosSecurityGroups.forEach(securityGroup ->{
                /**转换安全组*/
                CmdbSecuritygroupRes group = Converts.toNxcSecurityGroup(BaseClient.auths.get(), securityGroup);
                list.add(group);
                /**转换安全组规则*/
                List<CmdbSecuritygroupRule> ruleRes = new ArrayList<>();
                Converts.toNxcSecurityGroupRule(ruleRes,BaseClient.auths.get(), securityGroup);
                if(ObjectUtil.isNotEmpty(ruleRes)){
                    rules.addAll(ruleRes);
                    /**安全组与安全组规则数据*/
                    associations.add(AssociationUtils.toAssociation(group,ruleRes.stream().collect(Collectors.toList())));
                }
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), securityGroup));
            });
        }
        /**
         * 推送安全组数据
         */
        BaseUtils.sendMessage(list, arguments);

        /**推送安全组规则数据*/
        BaseUtils.sendMessage(rules, arguments);

        /**推送安全组与安全组规则关系数据*/
        BaseUtils.sendMessage(associations, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    private List<CloudosSecurityGroup> fetchCloudosSecurityGroup(JSONObject arguments){
        List<CloudosSecurityGroup> groups =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getSecurityGroupListUrl(),null),
                ResourceEnum.SECURITY_GROUP.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosSecurityGroup group = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosSecurityGroup>() {});
                groups.add(group);
            });
        }
        return groups;
    }
}
