package com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosCluster {
    private String name;
    private String morId;
    private String type;
    private String subType;
    private List<Children> children;
    @Data
    public static class Children {
        private String name;
        private String morId;
        private String type;
    }
}
