
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.TenantSide;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.DateUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbUser;
import com.futong.gemini.model.otc.bxc.entity.TmdbUserTenant;
import com.futong.gemini.model.otc.common.utils.IdUtils;

import java.util.ArrayList;
import java.util.List;

public class PlatProjectService {

    public static final PlatProjectService bean = new PlatProjectService();

    public void fetchProject(JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> linkList = new ArrayList<>();
        fetchCloudosProject(devopsList, linkList, arguments);

        /** 推送组织数据 */
        BaseUtils.sendMessage(devopsList, arguments);

        /** 推送组织关系数据 */
        BaseUtils.sendMessage(linkList, arguments);
    }

    public void fetchUser(JSONObject arguments) {
        List<TmdbUser> userList = new ArrayList<>();
        List<TmdbUserTenant> linkList = new ArrayList<>();
        fetchCloudosUser(userList, linkList, arguments);

        /** 推送组织数据 */
        BaseUtils.sendMessage(userList, arguments);

        /** 推送组织关系数据 */
        BaseUtils.sendMessage(linkList, arguments);
    }

    private void fetchCloudosProject(List<TmdbDevops> tenantList, List<TmdbDevopsLink> links, JSONObject arguments) {
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        //获取项目列表
        String response = HttpClientUtil.get(URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getProjectUrl(), null), config);
        if (ObjectUtil.isNotEmpty(response) && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONArray("res"))) {
            recuProject(tenantList, links, JSONObject.parseObject(response).getJSONArray("res"), BaseClient.auths.get());
        }
    }
    private void recuProject(List<TmdbDevops> tenantList, List<TmdbDevopsLink> links, JSONArray array, CloudAccessBean bean) {
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                TmdbDevops tenant = new TmdbDevops();
                tenant.setAccount_id(bean.getCmpId());
                tenant.setDevops_value(tempObj.getString("id"));
                tenant.setDevops_name(tempObj.getString("name"));
                tenant.setDict_code(TenantSide.TENANT_PROJECT.value());
                tenant.setCloud_type(bean.getCloudType());
                /*if(null!=tempObj.get("status"))
                    tenant.setStatus(tempObj.getBoolean("status")?1:2);
                else
                    tenant.setStatus(1);*/
                tenant.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), tenant.getDevops_value()}));
                tenant.setInfo_json(tempObj.toString());
                TmdbDevopsLink link = new TmdbDevopsLink();
                String parentId = IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), tempObj.getString("parent_id")});
                link.setParent_devops_id(tempObj.getString("parent_id").equals("default") ? null : parentId);
                link.setDevops_id(tenant.getBiz_id());
                link.setBiz_id(IdUtils.encryptId(new String[]{parentId, link.getDevops_id()}));
                links.add(link);
                tenantList.add(tenant);
                if (ObjectUtil.isNotEmpty(tempObj.getJSONArray("children"))) {
                    recuProject(tenantList, links, tempObj.getJSONArray("children"), bean);
                }
            });
        }
    }

    private void fetchCloudosUser(List<TmdbUser> tenantList, List<TmdbUserTenant> links, JSONObject arguments) {
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        //获取用户列表
        String response = HttpClientUtil.get(URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getUserUrl(), null), config);
        if (ObjectUtil.isNotEmpty(response)
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"))) {
            fetchUser(tenantList, links, JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"), BaseClient.auths.get());
        }
    }

    private void fetchUser(List<TmdbUser> userList, List<TmdbUserTenant> links, JSONArray array, CloudAccessBean bean) {
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                TmdbUser user = new TmdbUser();
                user.setUser_code(tempObj.getString("name"));
                user.setOpen_id(tempObj.getString("id"));
                user.setUser_email(tempObj.getString("email"));
                user.setStatus(tempObj.getBoolean("enabled")?1:0);
                user.setLogin_name(tempObj.getString("name"));
                user.setUser_name(tempObj.getString("name"));
                user.setUser_phone(tempObj.getString("phone"));
                user.setAccount_id(bean.getCmpId());
                user.setCreate_time(DateUtils.utcToTimestamp(tempObj.getString("created_at").replace(" ", "T")+"Z"));
                user.setBiz_id(IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_TENANT.value(), user.getOpen_id()}));
                TmdbUserTenant link = new TmdbUserTenant();
                String parentId = IdUtils.encryptId(new String[]{bean.getCmpId(), bean.getCloudType(), TenantSide.TENANT_PROJECT.value(), tempObj.getString("default_project_id")});
                link.setTenant_id(parentId);
                link.setUser_id(user.getBiz_id());
                link.setBiz_id(IdUtils.encryptId(new String[]{parentId, link.getTenant_id()}));
                links.add(link);
                userList.add(user);
            });
        }
    }
}
