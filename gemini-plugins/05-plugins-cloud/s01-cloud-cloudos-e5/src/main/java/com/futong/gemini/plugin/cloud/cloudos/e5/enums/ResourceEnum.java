package com.futong.gemini.plugin.cloud.cloudos.e5.enums;

public enum ResourceEnum {

    /** cloudos云主机 */
    SERVER("servers"),

    VM("vm"),

    /** cloudos规格*/
    FLAVOR("flavors"),

    /** cloudos镜像*/
    IMAGE("images"),

    /** cloudos镜像*/
    SNAPSHOT("snapshots"),


    /** cloudos 集群*/
    CLUSTER("clusters"),

    /** cloudos 宿主机*/
    HOST("host"),

    /** cloudos 存储池*/
    STORAGEPOOL("storagePool"),

    /** cloudos 宿主机关联云主机*/
    HOST_VM("vms"),

    /** cloudos 云盘*/
    VOLUME("volumes"),

    /** cloudos vpc*/
    VPC("vpcs"),

    /** cloudos router*/
    ROUTER("routers"),

    /** cloudos 子网*/
    SUBNET("subnets"),

    /** cloudos 网卡*/
    PORT("ports"),

    /** cloudos 经典网络*/
    NETWORRK("networks"),

    /** cloudos 安全组*/
    SECURITY_GROUP("security_groups"),

    /** cloudos ip*/
    IP("ip"),

    PROJECT("project"),
    // ssh密钥
    KEYPAIR("keypairs"),
    AZONE("azone"),
    // 安全组规则
    SECURITY_GROUP_RULES("security_group_rules");

    private String value;

    private ResourceEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
