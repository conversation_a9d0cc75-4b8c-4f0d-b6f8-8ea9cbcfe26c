package com.futong.gemini.plugin.cloud.cloudos.e5;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.function.FTAction;
import com.futong.common.log.DynamicLoggerConfigurator;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.service.AccountService;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosToken;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.sdk.template.PluginTemplate;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Locale;

@Slf4j
public class CloudosPluginTemplate extends PluginTemplate {

    @Override
    public void init(String key) {
        super.init(key);
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.h3c.cloudos","/cloud/cloudos");
        loadAccountForm();
        loadAccountDispatch();
        CloudosRegister.onAfterLoadCloudAccount();//加载云账号调度信息
        CloudosRegister.onAfterLoadFetch();//加载同步调度信息
        CloudosRegister.onAfterLoadOperate();//加载操作调度信息
    }

    private static String[] files = {
            "add_form_en-US.json",
            "add_form_zh-CN.json"
    };

    public void loadAccountDispatch() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("account/add_dispatch.json");
            String text = IoUtil.readUtf8(is);
            String[] split = key.split(":");
            //替换参数插件作用域
            text = StrUtil.replace(text, "#{plugin_realm}", split[0]);
            //替换参数插件版本
            text = StrUtil.replace(text, "#{plugin_version}", split[1]);
            AccountService.accountDispatch = text;
        } catch (Exception e) {
            log.error("加载"+key+"插件账号调度任务信息失败!", e);
        }
    }
    public void loadAccountForm() {
        try {
            for (String file : files) {
                InputStream is = getClass().getClassLoader().getResourceAsStream("account/" + file);
                String text = IoUtil.readUtf8(is);
                JSONObject json = JSON.parseObject(text);
                String lang = extractLocaleFromFilename(file);
                Locale locale = Locale.forLanguageTag(lang);
                AccountService.accountForm.put(locale, json);
            }
        } catch (Exception e) {
            log.error("加载插件账号表单信息失败!", e);
        }
    }

    public static String extractLocaleFromFilename(String filename) {
        // 假设文件名格式为 "add_form_<locale>.json"
        // 找到最后一个 "_" 的位置
        int lastUnderscoreIndex = filename.lastIndexOf('_');
        if (lastUnderscoreIndex == -1 || !filename.endsWith(".json")) {
            throw new IllegalArgumentException("Invalid filename format: " + filename);
        }
        // 提取下划线之后、".json"之前的部分
        return filename.substring(lastUnderscoreIndex + 1, filename.length() - 5);
    }
    @Override
    public boolean onBeforeProcess(JSONObject arguments, JSONObject context) {
        log.info("onBeforeProcess:cloudos私有云收到请求信息:" + arguments.toJSONString());
        //获取账号表单信息,不做云账号验证
        if (ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM.value().equals(arguments.getString("action"))) {
            return true;
        }
        //临时替换兼容，后续待v4.0.3发版本后删除
        if (ActionType.QUERY_CLOUD_ACCOUNT_MODEL.value().equals(arguments.getString("action"))) {
            return true;
        }
        BaseClient.load(arguments);
        if(!ActionType.AUTH_PLATFORM_ACCOUNT.value().equals(arguments.getString("action"))){
            CloudosToken token = HttpClientUtil.getToken(BaseClient.auths.get());
            arguments.getJSONObject("body").put("authToken",token);
        }
        return true;
    }
    @Override
    public BaseResponse process(JSONObject arguments, JSONObject context) {
        log.info("process:收到请求信息:" + arguments.toJSONString());
        String action = arguments.getString("action");
        //临时替换兼容，后续待v4.0.3发版本后删除
        if("QueryCloudAccountModel".equals(action)){
            action="GetPlatformAccountAddForm";
        }else if("AuthCloudAccount".equals(action)){
            action="AuthPlatformAccount";
        }
        if (CloudosRegister.isNotExists(action)) {
            return BaseResponse.FAIL_PARAM_EMPTY.of("未找Action:" + action + "对应得处理方法");
        }
        FTAction ftAction = CloudosRegister.getAction(action);
        return ftAction.doAction(arguments);
    }
    @Override
    public void onAfterProcess(JSONObject arguments, JSONObject context, BaseResponse response) {
        log.info("onAfterProcess:cloudos私有云对返回结果处理");
        BaseClient.auths.remove();
        BaseClient.bodys.remove();
    }
}
