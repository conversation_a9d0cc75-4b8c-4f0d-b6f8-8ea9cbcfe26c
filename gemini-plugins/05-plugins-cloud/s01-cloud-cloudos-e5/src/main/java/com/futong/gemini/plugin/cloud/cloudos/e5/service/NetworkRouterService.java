
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosRouter;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbRouteRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkRouterService {

    public static final NetworkRouterService bean = new NetworkRouterService();

    public void fetchRouter(JSONObject arguments) {
        List<CmdbRouteRes> routerList = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CloudosRouter> cloudosRouterList = fetchCloudosRouter(arguments);
        if(ObjectUtil.isNotEmpty(cloudosRouterList)){
            cloudosRouterList.forEach(d ->{
                /** router资源数据模型转换*/
                CmdbRouteRes route = Converts.toNxcRoute(BaseClient.auths.get(), d);
                routerList.add(route);
                // router标签数据
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /** 推送路由数据 */
        BaseUtils.sendMessage(routerList, arguments);
        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    private   List<CloudosRouter>  fetchCloudosRouter(JSONObject arguments){
        List<CloudosRouter> routers =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getRouterUrl(),null),
                ResourceEnum.ROUTER.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosRouter router = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosRouter>() {});
                routers.add(router);
            });
        }
        return routers;
    }
}
