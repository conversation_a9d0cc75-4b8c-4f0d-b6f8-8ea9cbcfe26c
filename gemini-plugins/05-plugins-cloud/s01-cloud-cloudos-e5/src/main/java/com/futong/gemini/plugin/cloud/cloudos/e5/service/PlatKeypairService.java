
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateKeypairRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateNetworkRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosKeypair;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbKeypairRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PlatKeypairService {

    public static final PlatKeypairService bean = new PlatKeypairService();

    public void fetchKeypair(JSONObject arguments) {
        List<CmdbKeypairRes> keypairList = new ArrayList<>();
        List<CloudosKeypair> cloudosKeypairList = fetchCloudosKeypair(arguments);
        if(ObjectUtil.isNotEmpty(cloudosKeypairList)){
            cloudosKeypairList.forEach(d ->{
                /** 秘钥对数据模型转换*/
                CmdbKeypairRes keypair = Converts.toNxcKeypair(BaseClient.auths.get(), d);
                keypairList.add(keypair);
            });
        }
        /** 推送秘钥对数据 */
        BaseUtils.sendMessage(keypairList, arguments);
    }

    /**
     * 获取秘钥对
     * @param arguments
     * @return
     */
    private   List<CloudosKeypair>  fetchCloudosKeypair(JSONObject arguments){
        List<CloudosKeypair> keypairs =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(),new String[]{request.getAuthToken().getToken().getProject().getId(),"os-keypairs"}),
                ResourceEnum.KEYPAIR.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosKeypair keypair = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosKeypair>() {});
                keypairs.add(keypair);
            });
        }
        return keypairs;
    }

    public static BaseResponse createKeyPair(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cloudos创建密钥对接受参数={}", arguments.toString());
        CreateKeypairRequest request = BaseClient.bodys.get().toJavaObject(CreateKeypairRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs"});
        try {
            JSONObject keypair = new JSONObject();
            keypair.put("keypair",request.getCloud());
            HttpClientUtil.post(url, keypair.toString(), config);
        } catch (Exception e) {
            log.error("创建密钥对异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建密钥对异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteKeyPair(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cloudos删除秘钥对接受参数={}", arguments.toString());
        CreateKeypairRequest request = BaseClient.bodys.get().toJavaObject(CreateKeypairRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        JSONObject body = BaseClient.bodys.get();
        List<String> keypairIds = getInstanceList(body);
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        for (String keyId : keypairIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "os-keypairs",keyId});
                HttpClientUtil.delete(url, config);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
