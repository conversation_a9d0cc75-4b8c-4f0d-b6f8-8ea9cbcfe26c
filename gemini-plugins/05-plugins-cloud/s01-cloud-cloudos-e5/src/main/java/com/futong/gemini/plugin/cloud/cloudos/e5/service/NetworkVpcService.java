
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosVpc;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbVpcRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkVpcService {

    public static final NetworkVpcService bean = new NetworkVpcService();

    public void fetchVpc(JSONObject arguments) {
        List<CmdbVpcRes> vpcList = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CloudosVpc> cloudosVpcList = fetchCloudosVpc(arguments);
        if(ObjectUtil.isNotEmpty(cloudosVpcList)){
            cloudosVpcList.forEach(d ->{
                /** VPC资源数据模型转换*/
                CmdbVpcRes vpc = Converts.toNxcVpc(BaseClient.auths.get(), d);
                vpcList.add(vpc);
                // vpc标签数据
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /** 推送vpc数据 */
        BaseUtils.sendMessage(vpcList, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    private   List<CloudosVpc>  fetchCloudosVpc(JSONObject arguments){
        List<CloudosVpc> vpcs =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVpcUrl(),null),
                ResourceEnum.VPC.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosVpc vpc = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosVpc>() {});
                vpcs.add(vpc);
            });
        }
        return vpcs;
    }


    public static BaseResponse deleteVpc(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getVpcUrl(), new String[]{"/"+instanceId});
                HttpClientUtil.delete(url, config);
            } catch (Exception e) {
                log.error("删除vpc异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除vpc异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }
}
