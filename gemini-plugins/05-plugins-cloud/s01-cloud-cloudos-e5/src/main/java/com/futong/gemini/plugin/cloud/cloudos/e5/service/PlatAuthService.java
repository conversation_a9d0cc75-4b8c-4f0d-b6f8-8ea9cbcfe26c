
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PlatAuthService {

    public static final PlatAuthService bean = new PlatAuthService();

    //账号认证
    public static BaseResponse authCloudAccount(JSONObject arguments) {
        try {
            HttpClientUtil.getCloudosToken(BaseClient.auths.get());
        } catch (Exception e) {
            log.error("账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
        return BaseResponse.SUCCESS.ofI18n("gemini.public.auth.success");
    }
}
