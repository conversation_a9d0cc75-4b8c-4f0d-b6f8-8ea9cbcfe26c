package com.futong.gemini.plugin.cloud.cloudos.e5.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosAlarm {

    private String end_at;

    private String from;

    private String level;

    private String name;

    private String start_at;

    private Attr attr;

    private String msg;

    @Data
    public static class Attr {

        private String hostname;

    }

}

