package com.futong.gemini.plugin.cloud.cloudos.e5.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;

public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取token信息*/
    private String[] tokenUrl = { "/sys/oapi/keystone/v3/auth/tokens" };

    public String[] getTokenUrl() {
        return tokenUrl.clone();
    }

    /** 获取计算资源URL */
    private String[] computeUrl = { "/os/compute/v1/v2" };

    public String[] getComputeUrl() {
        return computeUrl.clone();
    }
    /** 获取镜像资源URL */
    private String[] imageUrl = { "/os/image/v1/v2/images" };

    public String[] getImageUrl() {
        return imageUrl.clone();
    }

    /** 获取计算节点URL */
    private String[] computeNodeUrl = {"/os/compute/v1/h3cloudos/computenode"};

    public String[] getComputeNodeUrl() {
        return computeNodeUrl.clone();
    }

    /** 获取主机URL */
    private String[] hostUrl = {"/os/compute/v1/cloudos"};

    public String[] getHostUrl() {
        return hostUrl.clone();
    }


    /** 获取存储资源接口URL */
    private String[] storageUrl = {"/os/storage/v1/v2"};

    public String[] getStorageUrl() {
        return storageUrl.clone();
    }
    /** 获取经典网络资源接口URL */
    private String[] networkUrl = {"/os/netsecurity/v1/v2.0/networks"};

    public String[] getNetworkUrl() {
        return networkUrl.clone();
    }

    /** 获取vpc资源接口URL */
    private String[] vpcUrl = { "/os/netsecurity/v1/v1.0/vpcs" };

    public String[] getVpcUrl() {
        return vpcUrl.clone();
    }
    /** 获取子网资源接口URL */
    private String[] subnetUrl = { "/os/netsecurity/v1/v2.0/subnets" };

    public String[] getSubnetUrl() {
        return subnetUrl.clone();
    }
    /** 获取网卡资源接口URL */
    private String[] portUrl = { "/os/netsecurity/v1/v2.0/ports" };

    public String[] getPortUrl() {
        return portUrl.clone();
    }

    private String[] netsecurityUrl = {"/os/netsecurity/v1/v2.1"};

    public String[] getNetsecurityUrl() {
        return netsecurityUrl.clone();
    }


    // 安全组
    private String[] securityGroupListUrl = new String[]{"/os/netsecurity/v1/v2.0/security-groups"};

    public String[] getSecurityGroupListUrl() {
        return this.securityGroupListUrl.clone();
    }

    // 安全组规则
    private String[] securityGroupRuleListUrl = new String[]{"/os/netsecurity/v1/v2.0/security-group-rules"};

    public String[] getSecurityGroupRuleListUrl() {
        return this.securityGroupRuleListUrl.clone();
    }

    //获取组织资源URL
    private String[] projectUrl = { "/sys/oapi/v1/getProjects" };
    public String[] getProjectUrl() {
        return projectUrl.clone();
    }

    private String[] userUrl = { "/sys/oapi/v1/users" };
    public String[] getUserUrl() {
        return userUrl.clone();
    }

    private String[] azoneUrl = { "/os/compute/v1/cloudos/azones" };
    public String[] getAzoneUrl() {
        return azoneUrl.clone();
    }

    private String[] instancePerfUrl = { "/os/compute/v1/v1/resource/generic" };

    public String[] getInstancePerfUrl() {
        return instancePerfUrl.clone();
    }

    private String[] routerUrl = { "/os/netsecurity/v1/v2.0/routers" };

    public String[] getRouterUrl() {
        return routerUrl.clone();
    }

    private String[] alarmUrl = { "/sys/alertmgt/api/v1/alert","page","pagesize" };

	public String[] getAlarmUrl() {
		return alarmUrl.clone();
	}

    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

}
