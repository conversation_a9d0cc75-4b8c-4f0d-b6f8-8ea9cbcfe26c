
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosSubnet;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.entity.CmdbSubnetRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkSubnetService {

    public static final NetworkSubnetService bean = new NetworkSubnetService();

    public void fetchSubnet(JSONObject arguments) {
        List<CmdbSubnetRes> subnetList = new ArrayList<>();
        List<TmdbResourceSet> sets = new ArrayList<>();
        List<CloudosSubnet> cloudosSubnetList = fetchCloudosSubnet(arguments);
        if(ObjectUtil.isNotEmpty(cloudosSubnetList)){
            cloudosSubnetList.forEach(d ->{
                /** 子网资源数据模型转换*/
                CmdbSubnetRes subnet = Converts.toNxcSubnet(BaseClient.auths.get(), d);
                subnetList.add(subnet);
                // 子网标签数据
                sets.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /** 推送子网数据 */
        BaseUtils.sendMessage(subnetList, arguments);
        //北新仓资源关系数据
        BaseUtils.sendMessage(sets, arguments);
    }

    private List<CloudosSubnet>  fetchCloudosSubnet(JSONObject arguments){
        List<CloudosSubnet> subnets =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getSubnetUrl(),null),
                ResourceEnum.SUBNET.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosSubnet subnet = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosSubnet>() {});
                subnets.add(subnet);
            });
        }
        return subnets;
    }
}
