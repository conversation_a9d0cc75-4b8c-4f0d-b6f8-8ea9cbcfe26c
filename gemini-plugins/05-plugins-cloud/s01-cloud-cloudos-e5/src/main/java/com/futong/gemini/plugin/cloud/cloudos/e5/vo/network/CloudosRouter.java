package com.futong.gemini.plugin.cloud.cloudos.e5.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosRouter {

    private String status;

    private String external_gateway_info;

    private List<Object> availability_zone_hints;

    private List<Object> availability_zones;

    private String description;

    private List<Object> tags;

    private String tenant_id;

    private String created_at;

    private Boolean admin_state_up;

    private Boolean distributed;

    private String updated_at;

    private String project_id;

    private String flavor_id;

    private Integer revision_number;

    private List<Object> routes;

    private Boolean ha;

    private String id;

    private String name;
}
