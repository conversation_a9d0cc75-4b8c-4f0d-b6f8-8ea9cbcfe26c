
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.nxc.entity.CmdbStoragePoolRes;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosHost;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosStoragePool;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ComputeHostService {

    public static final ComputeHostService bean = new ComputeHostService();

    public void fetchHost(DescribeCloudosRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<CmdbHostRes> hosts = new ArrayList<>();
        try {
            request.getIds().forEach(i -> {
                String json = HttpClientUtil.get(BaseClient.auths.get(), request, URLUtils.bean.getHostUrl(), new String[]{"crm", request.getAuthToken().getToken().getProject().getId(), request.getComputeNode(), "hostDetail", i});
                CloudosHost host = Converts.parseAndConvert(json, new TypeReference<CloudosHost>() {
                });
                //转换宿主机信息
                CmdbHostRes hostRes = Converts.toNxcHost(BaseClient.auths.get(), host);
                //缓存宿主机所属计算节点名称，为后续性能数据同步时使用
                hostRes.setExtend1(request.getComputeNode());
                hosts.add(hostRes);
            });
        } catch (Exception e) {
            log.error("同步宿主机资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.host.fail"), e);
        }
        /**
         * 推送宿主机数据
         */
        BaseUtils.sendMessage(hosts, arguments);
    }

    public void fetchHostVmRelation(DescribeCloudosRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.public.param.ids.notnull"));
        }
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(hostId -> {
                JSONArray array = Converts.fetchResourceToJsonArray(request,
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(), new String[]{"crm", request.getAuthToken().getToken().getProject().getId(), request.getComputeNode(), "vms-host-simple", hostId}),
                        ResourceEnum.HOST_VM.getValue());
                List<CmdbInstanceRes> instanceResList = new ArrayList<>();
                array.forEach(pool -> {
                    JSONObject json = (JSONObject) pool;
                    CmdbInstanceRes instance = new CmdbInstanceRes();
                    instance.setRes_id(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), BaseClient.auths.get().getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), json.getString("uuid")));
                    instance.setAccount_id(BaseClient.auths.get().getCmpId());
                    instance.setOpen_id(json.getString("uuid"));
                    instance.setCloud_type(BaseClient.auths.get().getCloudType());
                    instanceResList.add(instance);
                });
                if (ObjectUtil.isNotEmpty(instanceResList)) {
                    /**宿主机与云主机关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(), hostId, new CmdbHostRes()), instanceResList.stream().collect(Collectors.toList())));
                }
            });
        } catch (Exception e) {
            log.error("同步宿主机与云主机资源关系数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.h3c.cloudos.fetch.host.instance.relation.fail"), e);
        }
        /**
         * 推送宿主机与云主机关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }
}
