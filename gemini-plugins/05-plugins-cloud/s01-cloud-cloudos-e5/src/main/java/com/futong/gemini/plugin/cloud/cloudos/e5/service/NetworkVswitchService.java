
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateNetworkRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosNetwork;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbVswitchRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkVswitchService {

    public static final NetworkVswitchService bean = new NetworkVswitchService();

    public void fetchNetwork(JSONObject arguments) {
        List<CmdbVswitchRes> vswitchList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CloudosNetwork> cloudosNetworks = fetchCloudosNetwork(arguments);
        if(ObjectUtil.isNotEmpty(cloudosNetworks)){
            cloudosNetworks.forEach(d ->{
                /** 经典网络资源数据模型转换*/
                CmdbVswitchRes vswitchRes = Converts.toNxcVswitch(BaseClient.auths.get(), d);
                vswitchList.add(vswitchRes);
                /**经典网络与子网关系数据*/
                List<Association> associationList = Converts.toAssociation(BaseClient.auths.get(), d, vswitchRes);
                if(ObjectUtil.isNotEmpty(associationList))
                    associations.addAll(associationList);
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /**
         * 推送经典网络数据
         */
        BaseUtils.sendMessage(vswitchList, arguments);

        /**推送资源关系(经典网络与子网)数据*/
        BaseUtils.sendMessage(associations, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }
    private   List<CloudosNetwork>  fetchCloudosNetwork(JSONObject arguments){
        List<CloudosNetwork> nets =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetworkUrl(),null),
                ResourceEnum.NETWORRK.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosNetwork net = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosNetwork>() {});
                nets.add(net);
            });
        }
        return nets;
    }

    public static BaseResponse createNetwork(JSONObject arguments) {
        String message = "操作成功.";
        log.info("cloudos创建经典网络接受参数={}", arguments.toString());
        CreateNetworkRequest request = BaseClient.bodys.get().toJavaObject(CreateNetworkRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetworkUrl(), null);
        try {
            JSONObject network = new JSONObject();
            JSONObject cloud = request.getCloud();
            cloud.put("tenant_id",request.getAuthToken().getToken().getProject().getId());
            cloud.put("provider:segmentation_id", cloud.getString("segmentation_id"));
            cloud.remove("segmentation_id");
            network.put("network",request.getCloud());
            log.info("创建经典网络url={}", url);
            log.info("创建经典网络参数={}", network.toString());
            HttpClientUtil.post(url, network.toString(), config);
        } catch (Exception e) {
            log.error("创建经典网络异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建经典网络异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteNetwork(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getNetworkUrl(), new String[]{ "/"+instanceId});
                HttpClientUtil.delete(url, config);
            } catch (Exception e) {
                log.error("删除经典网络异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除经典网络异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }
}
