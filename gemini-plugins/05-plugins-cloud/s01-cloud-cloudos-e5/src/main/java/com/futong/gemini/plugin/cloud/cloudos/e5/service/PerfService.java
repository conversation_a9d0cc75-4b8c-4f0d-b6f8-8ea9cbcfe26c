

package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.*;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.InstancePerfKpi;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.QueryInstancePerfReq;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.DateUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.JobUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosPerf;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.compute.CloudosHost;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.PageSortSearchRequest;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PerfService {

    public static final PerfService bean = new PerfService();

    //同步物理机性能数据
    public static BaseResponse fetchHostPerf(JSONObject arguments) {
        String message = "成功获取物理机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取物理机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取物理机列表
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> response = ApiFactory.Api.res.listHostStoragePool(searchRequest);
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        long current = DateUtil.current();
        fetchAndSendIHostPerf(response.getData().getList(), arguments, current);

        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            log.info("执行拆分性能数据任务");
            return new GourdJobResponse(JobUtils.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }

    //同步云主机性能数据
    public static BaseResponse fetchInstancePerf(JSONObject arguments) {
        String message = "成功获取云主机性能信息.";
        PageSortSearchRequest request = BaseClient.bodys.get().toJavaObject(PageSortSearchRequest.class);
        //转换分页获取云主机列表查询入参
        BasePageSortSearchRequest searchRequest = Converts.toBasePageSortSearchRequest(request);
        //根据入参分页获取云主机列表
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> response = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        long current = DateUtil.current();
        //同步性能数据并推送到数据队列
        fetchAndSendInstancePerf(response.getData().getList(), arguments, current);
        //若有下一页，则拆分dataJob分页获取性能数据
        if ((ObjectUtil.isNull(request) || ObjectUtil.isNull(request.getCurrent())) && response.getData().getCount() > searchRequest.getCurrent() * searchRequest.getSize()) {
            log.info("执行拆分性能数据任务");
            return new GourdJobResponse(JobUtils.splitPerfJob(response, searchRequest, arguments), message);
        }
        return BaseResponse.SUCCESS.of(message);
    }

    private static void fetchAndSendIHostPerf(List<ResHostStoragePoolApiModel> list, JSONObject arguments, long current) {
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        list.forEach(res -> {
            //获取物理机详情，详情内包含CPU、内存使用率
            String json = HttpClientUtil.get(BaseClient.auths.get(), request, URLUtils.bean.getHostUrl(), new String[]{"crm", request.getAuthToken().getToken().getProject().getId(), res.getExtend1(), "hostDetail", res.getOpen_id()});
            CloudosHost host = Converts.parseAndConvert(json, new TypeReference<CloudosHost>() {
            });
            ResourcePerfDetail perf = initResourcePerfDetail(BaseClient.auths.get(), res, current);
            perf.setCpuUsage(host.getCpuRate() == null ? 0 : host.getCpuRate().doubleValue());
            perf.setMemUsage(host.getMemRate() == null ? 0 : host.getMemRate().doubleValue());
            if (ObjectUtil.isNotEmpty(res.getStorages())) {
                Float size = 0.0f;
                Float usedSize = 0.0f;
                for (int i = 0; i < res.getStorages().size(); i++) {
                    ResStoragePoolApiModel storage = res.getStorages().get(i);
                    if ("dir".equals(storage.getType()))
                        continue;
                    size += storage.getTotal_size() == null ? 0 : storage.getTotal_size();
                    usedSize += storage.getUsed_size() == null ? 0 : storage.getUsed_size();
                }
                perf.setDiskSize(size.doubleValue());
                if (size > 0 && size >= usedSize)
                    perf.setDiskUsage(Double.valueOf(String.format("%.2f", 100 * usedSize / size)));
            }
            perf.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRes_id(), current + ""));
            perfList.add(perf);
        });
        //推送性能到数据队列
        BaseUtils.sendMessage(perfList, arguments);
    }

    private static void fetchAndSendInstancePerf(List<ResInstanceDiskApiModel> list, JSONObject arguments, long current) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        QueryInstancePerfReq req = new QueryInstancePerfReq();
        req.setTimestamp(DateUtils.longToUtc(current-60*1000*60*4));
        list.forEach(res -> {
            if ("ACTIVE".equals(res.getOpen_status())) {
                req.setInstanceId(res.getOpen_id());
                //采集CPU使用率数据
                req.setKpi(InstancePerfKpi.CpuUsage.getKey());
                List<CloudosPerf> cpuPerfs = fetchPerf(request, req, res);
                //若未获取到数据，则跳过该条数据
                if (ObjectUtil.isEmpty(cpuPerfs))
                    return;
                //采集内存使用率数据
                req.setKpi(InstancePerfKpi.MemUsage.getKey());
                List<CloudosPerf> memPerfs = fetchPerf(request, req, res);

                //采集磁盘读速率数据
                req.setKpi(InstancePerfKpi.diskRead.getKey());
                List<CloudosPerf> diskReadPerfs = fetchPerf(request, req, res);

                //采集磁盘写速率数据
                req.setKpi(InstancePerfKpi.diskWrite.getKey());
                List<CloudosPerf> diskWritePerfs = fetchPerf(request, req, res);

                //采集网络流入速率数据
                //req.setKpi(InstancePerfKpi.netIn.getKey());
                // List<CloudosPerf> netInPerfs  = fetchPerf(request,req,res);
                //采集网络流出速率数据
                //req.setKpi(InstancePerfKpi.netOut.getKey());
                // List<CloudosPerf> netOutPerfs  = fetchPerf(request,req,res);

                Float diskSize = 0f;
                if (ObjectUtil.isNotEmpty(res.getDisks())) {
                    for (ResDiskApiModel disk : res.getDisks()) {
                        diskSize += disk.getSize() == null ? 0 : disk.getSize();
                    }
                }
                for (int i = 0; i < cpuPerfs.size(); i++) {
                    CloudosPerf perf = cpuPerfs.get(i);
                    ResourcePerfDetail resourcePerf = initResourcePerfDetail(BaseClient.auths.get(), res, current);
                    //设置磁盘大小
                    resourcePerf.setDiskSize(diskSize.doubleValue());
                    //设置cpu使用率
                    resourcePerf.setCpuUsage(perf.getValue() == null ? 0 : perf.getValue().doubleValue());
                    resourcePerf.setCreateTime(DateUtils.utcToStr(perf.getTimestamp()));
                    //设置内存使用率
                    if (ObjectUtil.isNotEmpty(memPerfs) && i < memPerfs.size() && resourcePerf.getMemSize() > 0) {
                        resourcePerf.setMemUsage(memPerfs.get(i).getValue() == null ? 0 : 100 * memPerfs.get(i).getValue() / resourcePerf.getMemSize());
                        if (resourcePerf.getMemUsage() > 100)
                            resourcePerf.setMemUsage(100d);
                    }
                    //设置磁盘读速率
                    if (ObjectUtil.isNotEmpty(diskReadPerfs) && i < diskReadPerfs.size())
                        resourcePerf.setDiskRead(diskReadPerfs.get(i).getValue() == null ? 0 : diskReadPerfs.get(i).getValue().doubleValue() / 1024);
                    //设置磁盘写速率
                    if (ObjectUtil.isNotEmpty(diskWritePerfs) && i < diskWritePerfs.size())
                        resourcePerf.setDiskWrite(diskWritePerfs.get(i).getValue() == null ? 0 : diskWritePerfs.get(i).getValue().doubleValue() / 1024);
                    //设置网络流入速率
                    //if (ObjectUtil.isNotEmpty(netInPerfs) && i<netInPerfs.size())
                    //    resourcePerf.setNetIn(netInPerfs.get(i).getValue()==null?0:netInPerfs.get(i).getValue().doubleValue()/1024);
                    //设置网络流出速率
                    //if (ObjectUtil.isNotEmpty(netOutPerfs) && i<netOutPerfs.size())
                    //    resourcePerf.setNetOut(netOutPerfs.get(i).getValue()==null?0:netOutPerfs.get(i).getValue().doubleValue()/1024);
                    //计算磁盘io
                    resourcePerf.setDiskIo(resourcePerf.getDiskRead() + resourcePerf.getDiskWrite());
                    //计算网络io
                    resourcePerf.setNetIo(resourcePerf.getNetIn() + resourcePerf.getNetOut());
                    resourcePerf.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRes_id(), resourcePerf.getCreateTime()));
                    perfList.add(resourcePerf);
                    //特殊处理，新增五分钟一个点的批次数据
                    ResourcePerfDetail perfDetail = new ResourcePerfDetail();
                    BeanUtils.copyProperties(resourcePerf, perfDetail);
                    String createTime = resourcePerf.getCreateTime();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime dateTime = LocalDateTime.parse(createTime, formatter);
                    LocalDateTime minusTime = dateTime.minusMinutes(5);
                    perfDetail.setCreateTime(formatter.format(minusTime));
                    perfDetail.setId(IdUtils.encryptId(BaseClient.auths.get().getCmpId(), res.getRes_id(), perfDetail.getCreateTime()));
                    perfList.add(perfDetail);
                }
            }
        });
        //推送性能到数据队列
        BaseUtils.sendMessage(perfList, arguments);
    }


    /**
     * 调用cloudos云上接口获取性能数据
     * 调用接口失败：捕获异常，跳过该条数据
     *
     * @param request
     * @param req
     * @param res
     * @return
     */
    private static List<CloudosPerf> fetchPerf(DescribeCloudosRequest request, QueryInstancePerfReq req, ResInstanceDiskApiModel res) {
        List<CloudosPerf> perfs = null;
        try {
            String json = HttpClientUtil.get(BaseClient.auths.get(), request, URLUtils.bean.getInstancePerfUrl(), new String[]{res.getOpen_id(), "metric", req.getKpi(), "measures?start=" + req.getTimestamp()});
            perfs = JSON.parseObject(json, new TypeReference<List<CloudosPerf>>() {
            });
        } catch (Exception e) {
            log.error("获取性能数据失败", e);
        }
        return perfs;
    }

    private static <T> ResourcePerfDetail initResourcePerfDetail(CloudAccessBean accessBean, T info, long writeTime) {
        ResourcePerfDetail perf = new ResourcePerfDetail();
        if (info instanceof ResInstanceDiskApiModel) {
            ResInstanceDiskApiModel vminfo = (ResInstanceDiskApiModel) info;
            perf.setResId(vminfo.getRes_id());
            perf.setOpenId(vminfo.getOpen_id());
            perf.setOpenName(vminfo.getOpen_name());
            perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
            perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
        } else if (info instanceof ResHostStoragePoolApiModel) {
            ResHostStoragePoolApiModel host = (ResHostStoragePoolApiModel) info;
            perf.setResId(host.getRes_id());
            perf.setOpenId(host.getOpen_id());
            perf.setOpenName(host.getOpen_name());
            perf.setCpuSize(host.getCpu_size() == null ? 0 : Double.valueOf(host.getCpu_size()));
            perf.setMemSize(host.getMem_size() == null ? 0 : Double.valueOf(host.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
        }
        perf.setCloudType(accessBean.getCloudType());
        perf.setCreateTime(DateUtils.timestampToStr(writeTime));
        perf.setAccountId(accessBean.getCmpId());
        perf.setDiskSize(0d);
        perf.setCpuUsage(0d);
        perf.setMemUsage(0d);
        perf.setDiskUsage(0d);
        perf.setNetIn(0d);
        perf.setNetOut(0d);
        perf.setNetIo(0d);
        perf.setDiskIo(0d);
        perf.setDiskRead(0d);
        perf.setDiskWrite(0d);
        return perf;
    }
}
