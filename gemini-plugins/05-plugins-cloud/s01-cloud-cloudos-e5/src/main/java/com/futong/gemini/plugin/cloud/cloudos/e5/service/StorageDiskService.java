
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.CreateNetworkRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosDisk;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class StorageDiskService {

    public static final StorageDiskService bean = new StorageDiskService();


    public void fetchDisk(JSONObject arguments) {
        List<CmdbDiskRes> diskList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CloudosDisk> cloudosDisks = fetchCloudosDisk(arguments);
        if(ObjectUtil.isNotEmpty(cloudosDisks)){
            cloudosDisks.forEach(d ->{
                /** 云盘资源数据模型转换*/
                CmdbDiskRes diskRes = Converts.toNxcDisk(BaseClient.auths.get(), d);
                diskList.add(diskRes);
                List<Association> associationList = Converts.toAssociation(BaseClient.auths.get(), d, diskRes);
                if(ObjectUtil.isNotEmpty(associationList))
                    associations.addAll(associationList);

                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /**
         * 推送云盘数据
         */
        BaseUtils.sendMessage(diskList, arguments);

        /**推送资源关系(云主机与云盘)数据*/
        BaseUtils.sendMessage(associations, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    /***
     * 获取规格资源信息，并将其转换为CloudosFlavor
     * @param arguments
     * @return
     */
    private   List<CloudosDisk>  fetchCloudosDisk(JSONObject arguments){
        List<CloudosDisk> disks =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageUrl(),new String[]{request.getAuthToken().getToken().getProject().getId(),"volumes/detail"}),
                ResourceEnum.VOLUME.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosDisk disk = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosDisk>() {});
                disks.add(disk);
            });
        }
        return disks;
    }


    public static BaseResponse addStorageDisk(JSONObject disk) {
        String message = "操作成功.";
        log.info("cloudos创建云盘接受参数={}", disk.toString());
        CreateNetworkRequest request = BaseClient.bodys.get().toJavaObject(CreateNetworkRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(),"volumes"});
        try {
            JSONObject params = new JSONObject();
            JSONObject diskObj = new JSONObject();
            diskObj.put("name", disk.getString("name"));
            if(disk.getString("description")!=null)
                diskObj.put("description", disk.getString("description"));
            diskObj.put("size", disk.getInteger("size"));
            diskObj.put("multiattach", false);
            diskObj.put("attach_status", disk.getString("attach_status"));
            if(disk.getString("volume_type_name")!=null&&!"".equals(disk.getString("volume_type_name")))
                diskObj.put("volume_type", disk.getString("volume_type_name"));
            JSONObject metadata = new JSONObject();
            JSONObject diskmetadata = disk.getJSONObject("metadata");
            if(diskmetadata!=null){
                metadata.put("storage_type_name", diskmetadata.getString("storage_type_name"));
                metadata.put("storageTypeId", diskmetadata.getString("storageTypeId"));
                metadata.put("location", diskmetadata.getString("location"));
                metadata.put("user_name", diskmetadata.getString("user_name"));
                metadata.put("azone_uuid", diskmetadata.getString("azone_uuid"));
                metadata.put("azone_label", diskmetadata.getString("azone_label"));
                diskObj.put("availability_zone", diskmetadata.getString("azone_name"));
                diskObj.put("metadata", metadata);
                url = url + "?assign_userid=" + diskmetadata.getString("user_id");
            }
            if("从镜像创建硬盘".equals(diskObj.getString("createType")))
                diskObj.put("imageRef", disk.getString("imageRef"));
            params.put("volume",diskObj);
            JSONObject cloud = request.getCloud();
            cloud.put("tenant_id",request.getAuthToken().getToken().getProject().getId());
            cloud.put("provider:segmentation_id", cloud.getString("segmentation_id"));
            cloud.remove("segmentation_id");
            params.put("network",request.getCloud());
            log.info("创建云盘url={}", url);
            log.info("创建云盘参数={}", params.toString());
            HttpClientUtil.post(url, params.toString(), config);
        } catch (Exception e) {
            log.error("创建云盘异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建云盘异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteStorageDisk(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());

        JSONObject body = BaseClient.bodys.get();
        List<String> diskIds = getInstanceList(body);
        for (String diskId : diskIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(), "volumes", diskId});
                HttpClientUtil.delete(url,  config);
            } catch (Exception e) {
                log.error("删除云盘异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除云盘异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse attachStorageDisk(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());

        JSONObject body = BaseClient.bodys.get();
        String diskId = getCiId(body);
        String instanceId = getInstance(body);
        try {
            JSONObject params = new JSONObject();
            JSONObject disk = new JSONObject();
            disk.put("volumeId",diskId);
            params.put("volumeAttachment",disk);
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(),"servers",instanceId, "os-volume_attachments"});
            HttpClientUtil.post(url,params.toJSONString(), config);
        } catch (Exception e) {
            log.error("删除云主机异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "绑定云盘异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse detachStorageDisk(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());

        JSONObject body = BaseClient.bodys.get();
        String diskId = getCiId(body);
        String instanceId = getDetachInstance(body);
        try {
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getComputeUrl(), new String[]{request.getAuthToken().getToken().getProject().getId(),"servers",instanceId, "os-volume_attachments", diskId});
            HttpClientUtil.delete(url, config);
        } catch (Exception e) {
            log.error("解绑云盘异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "解绑云盘异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

    public static String getCiId(JSONObject body) {
        JSONObject ci = body.getJSONObject("ci");
        String ciId = ci.getString("openId");
        return ciId;
    }

    public static String getInstance(JSONObject body) {
        JSONObject ci = body.getJSONObject("model");
        String instanceId = ci.getString("instanceId");
        return instanceId;
    }

    public static String getDetachInstance(JSONObject body) {
        JSONObject ci = body.getJSONObject("ci");
        JSONObject instance = ci.getJSONObject("relationInstance");
        String openId = instance.getString("relationOpenId");
        return openId;
    }
}
