package com.futong.gemini.plugin.cloud.cloudos.e5.enums;

public enum InstancePerfKpi {

    CpuUsage("cpu_util","cpuUsage","CPU使用率"),MemUsage("memory.usage","memUsage","内存使用率"),diskRead("disk.read.bytes.rate","diskRead","磁盘读速率大小")
    ,diskWrite("disk.write.bytes.rate","diskWrite","磁盘写速率大小")
    ,netIn("network.incoming.bytes.rate","netIn","网络流入字节速率")
    ,netOut("network.outgoing.bytes.rate","netOut","网络流出字节速率");

    private String label;

    private String key;

    private String kpi;
    private InstancePerfKpi(String key,String kpi,String label) {
        this.label = label;
        this.key= key;
        this.kpi = kpi;
    }

    public String getKpi() {
        return kpi;
    }

    public void setKpi(String kpi) {
        this.kpi = kpi;
    }
    public String getLabel() {
        return label;
    }
    public void setLabel(String label) {
        this.label = label;
    }
    public String getKey() {
        return key;
    }
    public void setKey(String key) {
        this.key = key;
    }
}
