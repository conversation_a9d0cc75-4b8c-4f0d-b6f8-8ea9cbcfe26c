package com.futong.gemini.plugin.cloud.cloudos.e5.request;

import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosToken;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DescribeCloudosRequest {

    /**
     * 待同步的资源ID
     */
    private List<String> ids;

    /**
     * 每次同步的条数
     */
    private Integer fecthSize;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * cloudos token信息
     */
    private CloudosToken authToken;

    /** 计算节点名称 */
    private String computeNode;

}
