package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.bean.CloudAccessBean;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientConfig;
import com.futong.gemini.plugin.cloud.cloudos.e5.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosImage;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbUser;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbImageRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存储服务类，负责从云资源提供商获取镜像信息并转换、推送数据队列
 */
@Slf4j
public class StorageImageService {

    // 存储镜像服务实例
    public static final StorageImageService bean = new StorageImageService();

    /**
     * 获取并推送镜像信息
     *
     * @param arguments 请求参数，包含获取镜像信息所需的信息
     */
    public void fetchImage(JSONObject arguments) {
        List<CmdbImageRes> imageList = new ArrayList<>();
        List<CmdbSnapshotRes> snapshotList = new ArrayList<>();
        List<CloudosImage> images = fetchCloudosImage(arguments);
        Map<String, String> userMap = fetchCloudosUser(arguments);
        List<Association> associations = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(images)) {
            images.forEach(image -> {
                // 镜像资源数据模型转换
                CmdbImageRes imageRes = Converts.toNxImage(BaseClient.auths.get(), image);
                if (imageRes != null){
                    String userId = userMap.get(image.getUser_name()) == null ? "" : userMap.get(image.getUser_name());
                    associations.add(AssociationUtils.toAssociation(imageRes, TmdbUser.class, userId));
                    if (ObjectUtil.isNotNull(imageRes))
                        imageList.add(imageRes);

                    // 快照资源数据模型转换
                    CmdbSnapshotRes snapshot = Converts.toNxSnapshot(BaseClient.auths.get(), image);
                    if (ObjectUtil.isNotNull(snapshot))
                        snapshotList.add(snapshot);
                }
            });
        }

        // 推送镜像数据
        BaseUtils.sendMessage(imageList, arguments);

        // 推送快照数据
        BaseUtils.sendMessage(snapshotList, arguments);

        // 推送镜像和用户关系表
        BaseUtils.sendMessage(associations, arguments);
    }

    /**
     * 从云资源提供商获取镜像信息
     *
     * @param arguments 请求参数，包含获取镜像信息所需的信息
     * @return 镜像信息列表
     */
    public List<CloudosImage> fetchCloudosImage(JSONObject arguments) {
        List<CloudosImage> images = new ArrayList<>();
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageUrl(), null),
                ResourceEnum.IMAGE.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosImage image = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosImage>() {});
                images.add(image);
            });
        }
        return images;
    }

    private Map<String, String> fetchCloudosUser(JSONObject arguments) {
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        //获取用户列表
        String response = HttpClientUtil.get(URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getUserUrl(), null), config);
        if (ObjectUtil.isNotEmpty(response)
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body"))
                && ObjectUtil.isNotEmpty(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"))) {
            return fetchUser(JSONObject.parseObject(response).getJSONObject("res").getJSONObject("body").getJSONArray("users"), BaseClient.auths.get());
        }
        return new HashMap<>();
    }

    private Map<String, String> fetchUser(JSONArray array, CloudAccessBean bean) {
        Map<String, String> map = new HashMap<>();
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                map.put(tempObj.getString("name"),tempObj.getString("id"));
            });
        }
        return map;
    }


    public static BaseResponse deleteImage(JSONObject arguments) {
        String message = "操作成功.";
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getAuthToken());
        JSONObject body = BaseClient.bodys.get();
        List<String> instanceIds = getInstanceList(body);
        for (String instanceId : instanceIds) {
            try {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageUrl(), new String[]{"/"+instanceId});
                HttpClientUtil.delete(url, config);
            } catch (Exception e) {
                log.error("删除镜像异常{}", e);
//                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除镜像异常");
            }
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
