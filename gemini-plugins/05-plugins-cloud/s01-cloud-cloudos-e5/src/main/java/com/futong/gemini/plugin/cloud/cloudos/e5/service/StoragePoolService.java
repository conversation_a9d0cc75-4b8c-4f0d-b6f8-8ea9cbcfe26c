
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.CiResCloudUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosStoragePool;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbHostRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbStoragePoolRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
public class StoragePoolService {

    public static final StoragePoolService bean = new StoragePoolService();

    public void fetchStoragePool(DescribeCloudosRequest request, JSONObject arguments) {
        if (ObjectUtil.isEmpty(request.getIds())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "参数ids不能为空.");
        }
        List<CmdbStoragePoolRes> pools = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        try {
            request.getIds().forEach(hostId -> {
                JSONArray array = Converts.fetchResourceToJsonArray(request,
                        URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getHostUrl(),new String[]{"crm",request.getAuthToken().getToken().getProject().getId(),request.getComputeNode(),"storagePoolForHost",hostId}),
                        ResourceEnum.STORAGEPOOL.getValue());
                List<CmdbStoragePoolRes> poolResList = new ArrayList<>();
                array.forEach(pool->{
                    JSONObject json = (JSONObject)pool;
                    CloudosStoragePool storage = Converts.parseAndConvert(json.toJSONString(), new TypeReference<CloudosStoragePool>() {});
                    //转换存储池信息
                    CmdbStoragePoolRes hostRes = Converts.toNxcStoragePool(BaseClient.auths.get(), storage,hostId);
                    poolResList.add(hostRes);
                });
                if(ObjectUtil.isNotEmpty(poolResList)){
                    /**宿主机与存储池关系数据*/
                    associations.add(AssociationUtils.toAssociation(CiResCloudUtils.toCiResCloud(BaseClient.auths.get(),hostId,new CmdbHostRes()),poolResList.stream().collect(Collectors.toList())));
                    pools.addAll(poolResList);
                }
            });
        } catch (Exception e) {
            log.error("同步存储池资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.fetch.storagePool.fail"), e);
        }
        /**
         * 推送存储池数据
         */
        BaseUtils.sendMessage(pools, arguments);

        /**
         * 推送宿主机与存储池关系数据
         */
        BaseUtils.sendMessage(associations, arguments);
    }
}
