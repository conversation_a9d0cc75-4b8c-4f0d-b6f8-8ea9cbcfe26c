package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.i18n.FTI18nUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;

public class AccountService {

    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(JSONObject arguments) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }

    public static BaseResponse createFetchDispatch(JSONObject arguments) {
        arguments.put("auth", arguments.getJSONObject("body").getJSONObject("auth"));
        BaseCloudRequest request = new BaseCloudRequest(arguments);
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        List<JSONObject> dispatchers = listAllDispatcher(result);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            }
        });
        return dispatchers;
    }
}
