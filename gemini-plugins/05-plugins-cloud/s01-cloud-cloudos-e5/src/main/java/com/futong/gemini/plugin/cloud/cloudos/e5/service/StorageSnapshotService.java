
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.storage.CloudosSnapshot;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbDiskRes;
import com.futong.gemini.model.otc.nxc.entity.CmdbSnapshotRes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class StorageSnapshotService {

    public static final StorageSnapshotService bean = new StorageSnapshotService();


    public void fetchSnapshot(JSONObject arguments) {
        List<CmdbSnapshotRes> snapshotList = new ArrayList<>();
        List<Association> associationList = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CloudosSnapshot> cloudosSnapshotList = fetchCloudosSnapshot(arguments);
        if(ObjectUtil.isNotEmpty(cloudosSnapshotList)){
            cloudosSnapshotList.forEach(d ->{
                /** 磁盘快照资源数据模型转换*/
                CmdbSnapshotRes snapshot = Converts.toNxSnapshot(BaseClient.auths.get(), d);
                snapshotList.add(snapshot);
                //快照和磁盘的关系数据
                CmdbDiskRes disk =Converts.toNxDisk(BaseClient.auths.get(), d);
                associationList.add(AssociationUtils.toAssociation(disk, snapshot));
                // 快照
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));
            });
        }
        /** 推送磁盘快照数据 */
        BaseUtils.sendMessage(snapshotList, arguments);

        // 推送磁盘和快照关系数据
        BaseUtils.sendMessage(associationList, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    private   List<CloudosSnapshot>  fetchCloudosSnapshot(JSONObject arguments){
        List<CloudosSnapshot> snapshots =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getStorageUrl(),new String[]{request.getAuthToken().getToken().getProject().getId(),"snapshots/detail"}),
                ResourceEnum.SNAPSHOT.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosSnapshot snapshot = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosSnapshot>() {});
                log.info("snapshot ={}",snapshot);
                snapshots.add(snapshot);
            });
        }
        return snapshots;
    }
}
