
package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.ResourceEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.network.CloudosPort;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class NetworkNicService {

    public static final NetworkNicService bean = new NetworkNicService();

    public void fetchNic(JSONObject arguments) {
        List<CmdbNetcardRes> cardList = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<TmdbResourceSet> tags = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<CloudosPort> cloudosCardList = fetchPort(arguments);
        if(ObjectUtil.isNotEmpty(cloudosCardList)){
            cloudosCardList.forEach(d ->{
                /** 网卡资源数据模型转换*/
                CmdbNetcardRes card = Converts.toNxcNetcard(BaseClient.auths.get(), d);
                cardList.add(card);
                /** 获取网卡关联的子网信息 */
                CmdbSubnetRes subnet =Converts.toNxcSubnet(BaseClient.auths.get(), d);
                if(ObjectUtil.isNotNull(subnet))
                    associations.add(AssociationUtils.toAssociation(subnet,card));
                /** 网卡关联的经典网络信息 */
                CmdbVswitchRes vswitch =Converts.toNxcVswitch(BaseClient.auths.get(), d);
                if(ObjectUtil.isNotNull(vswitch))
                    associations.add(AssociationUtils.toAssociation(vswitch,card));
                /** 网卡关联的云主机信息 */
                CmdbInstanceRes instance =Converts.toNxcInstance(BaseClient.auths.get(), d);
                if(ObjectUtil.isNotNull(instance))
                    associations.add(AssociationUtils.toAssociation(instance,card));
                /** 网卡关联的IP信息 */
                CmdbIpRes ip =Converts.toNxcIp(BaseClient.auths.get(), d);
                if(ObjectUtil.isNotNull(ip)){
                    associations.add(AssociationUtils.toAssociation(card,ip));
                    ips.add(ip);
                }
                if(ObjectUtil.isNotNull(ip) && ObjectUtil.isNotNull(instance))
                    associations.add(AssociationUtils.toAssociation(instance,ip));
                /** 网卡关联的安全组信息 */
                Converts.toNxcSecuritygroup(associations,BaseClient.auths.get(), d,instance,card);
                // 网卡标签数据
                tags.add(Converts.toTmdbResourceSet(BaseClient.auths.get(), d));

            });
        }
        /** 推送网卡数据 */
        BaseUtils.sendMessage(cardList, arguments);

        /** 推送IP数据 */
        BaseUtils.sendMessage(ips, arguments);

        /** 推送网卡关联数据数据 */
        BaseUtils.sendMessage(associations, arguments);

        //北新仓资源关系数据
        BaseUtils.sendMessage(tags, arguments);
    }

    private  List<CloudosPort>  fetchPort(JSONObject arguments){
        List<CloudosPort> ports =  new ArrayList<>();
        DescribeCloudosRequest request =   BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPortUrl(),null),
                ResourceEnum.PORT.getValue());
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CloudosPort port = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosPort>() {});
                ports.add(port);
            });
        }
        return ports;
    }
}
