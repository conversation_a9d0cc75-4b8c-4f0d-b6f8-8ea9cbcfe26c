package com.futong.gemini.plugin.cloud.cloudos.e5.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.route.RouteFactory;
import com.futong.gemini.plugin.cloud.cloudos.e5.common.FetchConverts;
import com.futong.gemini.plugin.cloud.cloudos.e5.convert.Converts;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosAlarm;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.model.otc.common.model.OtcAetMessage;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

;

@Slf4j
public class AlarmService {

    public static final AlarmService bean = new AlarmService();

    // 获取事件
    public String fetchAlarm(JSONObject arguments) throws Exception{
        List<CloudosAlarm> alarms = fetchCloudosAlarm(arguments);
        List<AlarmInfoBean> beans = alarms.stream()
                .map(t -> FetchConverts.toAlarm(t)).collect(Collectors.toList());
        toAetMessageAndSend(beans, "alarm");
        String message = StrUtil.format("本次获取告警信息条数：{}", beans.size());
        return message;
    }

    public List<CloudosAlarm> fetchCloudosAlarm(JSONObject arguments){
        List<CloudosAlarm> alarms =  new ArrayList<>();
        DescribeCloudosRequest request = BaseClient.bodys.get().toJavaObject(DescribeCloudosRequest.class);
        String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlarmUrl(),new String[]{"?","1","100"});
        JSONArray array = Converts.fetchResourceToJsonArray(request,url,
                "data");
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 解析并转换JSON对象为CloudosFlavor对象
                CloudosAlarm flavor = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<CloudosAlarm>() {});
                alarms.add(flavor);
            });
        }
        return alarms;
    }

    public static <T> OtcAetMessage<T> toAetMessageAndSend(List<T> res, String type) {
        OtcAetMessage<T> message = new OtcAetMessage<>();
        message.setType(type);
        message.setBody(res);
        BaseResponse baseResponse = RouteFactory.routeAetMessage(message);
        if (BaseResponse.SUCCESS.isNotEquals(baseResponse)) {
            log.error("发送消息到数据底座失败,{}", baseResponse.getMessage());
            throw new BaseException(baseResponse, "发送消息到数据底座失败!");
        }
        return message;
    }
}
