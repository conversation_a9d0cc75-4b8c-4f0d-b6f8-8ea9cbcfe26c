package com.futong.gemini.plugin.cloud.cloudos.e5.vo.network;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudosSecurityGroup {

    private String description;

    private List<Object> tags;

    private String tenant_id;

    private String created_at;

    private String updated_at;

    private List<Security_group_rules> security_group_rules;

    private Integer revision_number;

    private String project_id;

    private String id;

    private String name;



    @Data
    public static class Security_group_rules {

        private String direction;

        private String description;

        private List<Object> tags;

        private String updated_at;

        private Integer revision_number;

        private String id;

        private String remote_ip_prefix;

        private String created_at;

        private String security_group_id;

        private String tenant_id;

        private String ethertype;

        private String project_id;

    }
}
