package com.futong.gemini.plugin.cloud.cloudos.e5.http;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.cloudos.e5.enums.TokenEnum;
import com.futong.gemini.plugin.cloud.cloudos.e5.request.DescribeCloudosRequest;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosToken;
import com.futong.gemini.plugin.cloud.cloudos.e5.vo.CloudosTokenParam;
import com.futong.gemini.plugin.cloud.cloudos.e5.util.URLUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
@Slf4j
public class HttpClientUtil {
    private static final String HTTPS = "https";

    /***
     * 获取token
     * @param bean
     * @return
     */
    public  static CloudosToken getToken(CloudAccessBean bean){
        CloudosToken token =null;
        try {
            token = (CloudosToken) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId());
        } catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId());
        }
        if(ObjectUtil.isNull(token)){
            token = getCloudosToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}",JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId(),token);
        }
        return token;
    }
    public  static CloudosToken getCloudosToken(CloudAccessBean bean) {
        CloudosToken token =new CloudosToken();
        /**拼接获取 token url */
        String  url  = URLUtils.bean.makeUrl(bean, URLUtils.bean.getTokenUrl(),null);
        CloudosTokenParam param = new CloudosTokenParam();
        CloudosTokenParam.Auth auth = new CloudosTokenParam.Auth();
        CloudosTokenParam.User user = new CloudosTokenParam.User(new CloudosTokenParam.Domain(bean.getDomain()),bean.getUsername(),bean.getPassword());
        CloudosTokenParam.Identity identity = new CloudosTokenParam.Identity(new CloudosTokenParam.Password(user));
        CloudosTokenParam.Scope scope = new CloudosTokenParam.Scope(new CloudosTokenParam.Project(new CloudosTokenParam.ScopeDomain(bean.getDomain()), bean.getProjectId()));
        auth.setIdentity(identity);
        auth.setScope(scope);
        param.setAuth(auth);
        log.info("认证url---{}", url);
        log.info("认证body---{}",JSON.toJSONString(param));
        try {
            token = fetchToken(url, param,HttpClientConfig.class.newInstance(),bean);
        } catch (Exception e) {
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
        return token;
    }
    private static CloudosToken fetchToken(String url, CloudosTokenParam tokenParam, HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            String json = "{\"auth\": {\"identity\": {\"methods\": [\"password\"],\"password\": {\"user\": {\"domain\": {\"id\": \""+tokenParam.getAuth().getScope().getProject().getDomain().getId()+"\"},\"name\": \""+tokenParam.getAuth().getIdentity().getPassword().getUser().getName()+"\",\"password\": \""+tokenParam.getAuth().getIdentity().getPassword().getUser().getPassword()+"\"}}},\"scope\": {\"project\": {\"domain\": {\"id\": \""+tokenParam.getAuth().getScope().getProject().getDomain().getId()+"\"},\"name\": \""+tokenParam.getAuth().getScope().getProject().getName()+"\"}}}}";
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            log.info("开始调用----"+System.currentTimeMillis());
            HttpResponse response = httpClient.execute(httpPost);
            log.info("结束调用----"+System.currentTimeMillis());
            String result = getResponseStr(response, config);
            log.info("得到结果：{}", result);
            CloudosToken token = JSON.parseObject(result, CloudosToken.class);
            Header[] headers = response.getHeaders(TokenEnum.AUBJECT_TOKEN.getValue());
            if(ObjectUtil.isNotEmpty(headers)){
                token.setAuthToken(headers[0].getValue());
            }
            return token;
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public  static String get(CloudAccessBean bean, DescribeCloudosRequest request, String[] url, String[] args){
       String  newUrl  = URLUtils.bean.makeUrl(bean, url,args);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(),request.getAuthToken().getAuthToken());
       return  get(newUrl,config);
    }
    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @return
     */
    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            URL urlObj = new URL(url);
            if (url.startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }
    /**
     * Get http请求
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @return 响应结果字符串
     */
    public static String get(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpGet httpGet = new HttpGet(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            httpGet.addHeader("Content-Type","application/json");
            httpGet.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpGet);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    public static boolean isJsonarray(String jsonString){
        try {
            new JSONArray(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    private static String getResponseStr(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        if(response.getEntity()==null) {
            return "";
        }
        return EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");
    }

    /**
     * 删除
     * @param url
     * @param config
     * @return
     */
    public static String delete(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpDelete httpDelete = new HttpDelete(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
}
