{"title": "华为云账号配置", "description": "请填写华为云账号信息", "fields": [{"name": "accessKeyId", "label": "Access Key ID", "type": "text", "required": true, "placeholder": "请输入华为云Access Key ID", "description": "华为云账号的Access Key ID，用于API认证"}, {"name": "accessKeySecret", "label": "Access Key Secret", "type": "password", "required": true, "placeholder": "请输入华为云Access Key Secret", "description": "华为云账号的Access Key Secret，用于API认证"}, {"name": "region", "label": "区域", "type": "select", "required": true, "placeholder": "请选择华为云区域", "description": "华为云服务区域，如：cn-north-4", "options": [{"value": "cn-north-1", "label": "华北-北京一"}, {"value": "cn-north-4", "label": "华北-北京四"}, {"value": "cn-east-2", "label": "华东-上海二"}, {"value": "cn-east-3", "label": "华东-上海一"}, {"value": "cn-south-1", "label": "华南-广州"}, {"value": "cn-southwest-2", "label": "西南-贵阳一"}, {"value": "ap-southeast-1", "label": "中国-香港"}, {"value": "ap-southeast-2", "label": "亚太-曼谷"}, {"value": "ap-southeast-3", "label": "亚太-新加坡"}, {"value": "af-south-1", "label": "非洲-约翰内斯堡"}, {"value": "na-mexico-1", "label": "拉美-墨西哥城一"}, {"value": "la-north-2", "label": "拉美-墨西哥城二"}, {"value": "sa-brazil-1", "label": "拉美-圣保罗一"}, {"value": "la-south-2", "label": "拉美-圣地亚哥"}, {"value": "cn-northeast-1", "label": "东北-大连"}, {"value": "cn-north-9", "label": "华北-乌兰察布一"}, {"value": "cn-north-2", "label": "华北-北京二"}, {"value": "cn-south-2", "label": "华南-广州-友好用户环境"}, {"value": "cn-south-4", "label": "华南-广州-羊城一"}, {"value": "cn-south-6", "label": "华南-广州-羊城二"}, {"value": "cn-south-8", "label": "华南-广州-羊城三"}, {"value": "cn-south-9", "label": "华南-广州-羊城四"}, {"value": "cn-south-10", "label": "华南-广州-羊城五"}, {"value": "cn-south-11", "label": "华南-广州-羊城六"}, {"value": "cn-south-12", "label": "华南-广州-羊城七"}, {"value": "cn-south-13", "label": "华南-广州-羊城八"}, {"value": "cn-south-14", "label": "华南-广州-羊城九"}, {"value": "cn-south-15", "label": "华南-广州-羊城十"}, {"value": "cn-south-16", "label": "华南-广州-羊城十一"}, {"value": "cn-south-17", "label": "华南-广州-羊城十二"}, {"value": "cn-south-18", "label": "华南-广州-羊城十三"}, {"value": "cn-south-19", "label": "华南-广州-羊城十四"}, {"value": "cn-south-20", "label": "华南-广州-羊城十五"}, {"value": "cn-south-21", "label": "华南-广州-羊城十六"}, {"value": "cn-south-22", "label": "华南-广州-羊城十七"}, {"value": "cn-south-23", "label": "华南-广州-羊城十八"}, {"value": "cn-south-24", "label": "华南-广州-羊城十九"}, {"value": "cn-south-25", "label": "华南-广州-羊城二十"}, {"value": "cn-south-26", "label": "华南-广州-羊城二十一"}, {"value": "cn-south-27", "label": "华南-广州-羊城二十二"}, {"value": "cn-south-28", "label": "华南-广州-羊城二十三"}, {"value": "cn-south-29", "label": "华南-广州-羊城二十四"}, {"value": "cn-south-30", "label": "华南-广州-羊城二十五"}, {"value": "cn-south-31", "label": "华南-广州-羊城二十六"}, {"value": "cn-south-32", "label": "华南-广州-羊城二十七"}, {"value": "cn-south-33", "label": "华南-广州-羊城二十八"}, {"value": "cn-south-34", "label": "华南-广州-羊城二十九"}, {"value": "cn-south-35", "label": "华南-广州-羊城三十"}, {"value": "cn-south-36", "label": "华南-广州-羊城三十一"}, {"value": "cn-south-37", "label": "华南-广州-羊城三十二"}, {"value": "cn-south-38", "label": "华南-广州-羊城三十三"}, {"value": "cn-south-39", "label": "华南-广州-羊城三十四"}, {"value": "cn-south-40", "label": "华南-广州-羊城三十五"}, {"value": "cn-south-41", "label": "华南-广州-羊城三十六"}, {"value": "cn-south-42", "label": "华南-广州-羊城三十七"}, {"value": "cn-south-43", "label": "华南-广州-羊城三十八"}, {"value": "cn-south-44", "label": "华南-广州-羊城三十九"}, {"value": "cn-south-45", "label": "华南-广州-羊城四十"}, {"value": "cn-south-46", "label": "华南-广州-羊城四十一"}, {"value": "cn-south-47", "label": "华南-广州-羊城四十二"}, {"value": "cn-south-48", "label": "华南-广州-羊城四十三"}, {"value": "cn-south-49", "label": "华南-广州-羊城四十四"}, {"value": "cn-south-50", "label": "华南-广州-羊城四十五"}, {"value": "cn-south-51", "label": "华南-广州-羊城四十六"}, {"value": "cn-south-52", "label": "华南-广州-羊城四十七"}, {"value": "cn-south-53", "label": "华南-广州-羊城四十八"}, {"value": "cn-south-54", "label": "华南-广州-羊城四十九"}, {"value": "cn-south-55", "label": "华南-广州-羊城五十"}, {"value": "cn-south-56", "label": "华南-广州-羊城五十一"}, {"value": "cn-south-57", "label": "华南-广州-羊城五十二"}, {"value": "cn-south-58", "label": "华南-广州-羊城五十三"}, {"value": "cn-south-59", "label": "华南-广州-羊城五十四"}, {"value": "cn-south-60", "label": "华南-广州-羊城五十五"}, {"value": "cn-south-61", "label": "华南-广州-羊城五十六"}, {"value": "cn-south-62", "label": "华南-广州-羊城五十七"}, {"value": "cn-south-63", "label": "华南-广州-羊城五十八"}, {"value": "cn-south-64", "label": "华南-广州-羊城五十九"}, {"value": "cn-south-65", "label": "华南-广州-羊城六十"}, {"value": "cn-south-66", "label": "华南-广州-羊城六十一"}, {"value": "cn-south-67", "label": "华南-广州-羊城六十二"}, {"value": "cn-south-68", "label": "华南-广州-羊城六十三"}, {"value": "cn-south-69", "label": "华南-广州-羊城六十四"}, {"value": "cn-south-70", "label": "华南-广州-羊城六十五"}, {"value": "cn-south-71", "label": "华南-广州-羊城六十六"}, {"value": "cn-south-72", "label": "华南-广州-羊城六十七"}, {"value": "cn-south-73", "label": "华南-广州-羊城六十八"}, {"value": "cn-south-74", "label": "华南-广州-羊城六十九"}, {"value": "cn-south-75", "label": "华南-广州-羊城七十"}, {"value": "cn-south-76", "label": "华南-广州-羊城七十一"}, {"value": "cn-south-77", "label": "华南-广州-羊城七十二"}, {"value": "cn-south-78", "label": "华南-广州-羊城七十三"}, {"value": "cn-south-79", "label": "华南-广州-羊城七十四"}, {"value": "cn-south-80", "label": "华南-广州-羊城七十五"}, {"value": "cn-south-81", "label": "华南-广州-羊城七十六"}, {"value": "cn-south-82", "label": "华南-广州-羊城七十七"}, {"value": "cn-south-83", "label": "华南-广州-羊城七十八"}, {"value": "cn-south-84", "label": "华南-广州-羊城七十九"}, {"value": "cn-south-85", "label": "华南-广州-羊城八十"}, {"value": "cn-south-86", "label": "华南-广州-羊城八十一"}, {"value": "cn-south-87", "label": "华南-广州-羊城八十二"}, {"value": "cn-south-88", "label": "华南-广州-羊城八十三"}, {"value": "cn-south-89", "label": "华南-广州-羊城八十四"}, {"value": "cn-south-90", "label": "华南-广州-羊城八十五"}, {"value": "cn-south-91", "label": "华南-广州-羊城八十六"}, {"value": "cn-south-92", "label": "华南-广州-羊城八十七"}, {"value": "cn-south-93", "label": "华南-广州-羊城八十八"}, {"value": "cn-south-94", "label": "华南-广州-羊城八十九"}, {"value": "cn-south-95", "label": "华南-广州-羊城九十"}, {"value": "cn-south-96", "label": "华南-广州-羊城九十一"}, {"value": "cn-south-97", "label": "华南-广州-羊城九十二"}, {"value": "cn-south-98", "label": "华南-广州-羊城九十三"}, {"value": "cn-south-99", "label": "华南-广州-羊城九十四"}, {"value": "cn-south-100", "label": "华南-广州-羊城九十五"}]}, {"name": "description", "label": "描述", "type": "textarea", "required": false, "placeholder": "请输入账号描述信息", "description": "账号的备注描述信息"}], "validation": {"accessKeyId": {"pattern": "^[A-Z0-9]{20}$", "message": "Access Key ID必须是20位大写字母和数字的组合"}, "accessKeySecret": {"minLength": 32, "maxLength": 64, "message": "Access Key Secret长度必须在32-64位之间"}}}