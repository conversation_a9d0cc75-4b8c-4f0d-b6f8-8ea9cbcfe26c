{"title": "Huawei Cloud Account Configuration", "description": "Please fill in your Huawei Cloud account information", "fields": [{"name": "accessKeyId", "label": "Access Key ID", "type": "text", "required": true, "placeholder": "Enter Huawei Cloud Access Key ID", "description": "Huawei Cloud account Access Key ID for API authentication"}, {"name": "accessKeySecret", "label": "Access Key Secret", "type": "password", "required": true, "placeholder": "Enter Huawei Cloud Access Key Secret", "description": "Huawei Cloud account Access Key Secret for API authentication"}, {"name": "region", "label": "Region", "type": "select", "required": true, "placeholder": "Select Huawei Cloud region", "description": "Huawei Cloud service region, e.g.: cn-north-4", "options": [{"value": "cn-north-1", "label": "China North-Beijing1"}, {"value": "cn-north-4", "label": "China North-Beijing4"}, {"value": "cn-east-2", "label": "China East-Shanghai2"}, {"value": "cn-east-3", "label": "China East-Shanghai1"}, {"value": "cn-south-1", "label": "China South-Guangzhou"}, {"value": "cn-southwest-2", "label": "China Southwest-Guiyang1"}, {"value": "ap-southeast-1", "label": "China-Hong Kong"}, {"value": "ap-southeast-2", "label": "Asia Pacific-Bangkok"}, {"value": "ap-southeast-3", "label": "Asia Pacific-Singapore"}, {"value": "af-south-1", "label": "Africa-Johannesburg"}, {"value": "na-mexico-1", "label": "Latin America-Mexico City1"}, {"value": "la-north-2", "label": "Latin America-Mexico City2"}, {"value": "sa-brazil-1", "label": "Latin America-Sao Paulo1"}, {"value": "la-south-2", "label": "Latin America-Santiago"}, {"value": "cn-northeast-1", "label": "China Northeast-Dalian"}, {"value": "cn-north-9", "label": "China North-Ulanqab1"}, {"value": "cn-north-2", "label": "China North-Beijing2"}]}, {"name": "description", "label": "Description", "type": "textarea", "required": false, "placeholder": "Enter account description", "description": "Account description and notes"}], "validation": {"accessKeyId": {"pattern": "^[A-Z0-9]{20}$", "message": "Access Key ID must be 20 characters of uppercase letters and numbers"}, "accessKeySecret": {"minLength": 32, "maxLength": 64, "message": "Access Key Secret length must be between 32-64 characters"}}}