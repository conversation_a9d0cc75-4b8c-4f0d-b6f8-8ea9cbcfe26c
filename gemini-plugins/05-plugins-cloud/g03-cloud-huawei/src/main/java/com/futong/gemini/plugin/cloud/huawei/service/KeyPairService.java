package com.futong.gemini.plugin.cloud.huawei.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.common.CloudClient;
import com.futong.gemini.plugin.sdk.model.PluginRequest;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.ecs.v2.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 华为云密钥对服务类
 * 负责密钥对的管理操作
 */
@Slf4j
public class KeyPairService {

    /**
     * 查询密钥对列表
     */
    public BaseResponse listKeypairs(PluginRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAuth().getUsername();
            String accessKeySecret = request.getBody().getAuth().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.createEcsClient(accessKeyId, accessKeySecret, regionId);

            // TODO: 华为云SDK 3.1.155版本中NovaListKeypairsRequest的API不兼容，临时禁用此功能
            log.warn("华为云SDK 3.1.155版本中NovaListKeypairsRequest的API不兼容，暂不支持查询密钥对功能");
            return BaseResponse.FAIL_OP.of("华为云SDK版本不支持此功能");
        } catch (Exception e) {
            log.error("查询密钥对列表异常", e);
            return BaseResponse.FAIL_OP.of("查询密钥对列表异常: " + e.getMessage());
        }
    }





} 