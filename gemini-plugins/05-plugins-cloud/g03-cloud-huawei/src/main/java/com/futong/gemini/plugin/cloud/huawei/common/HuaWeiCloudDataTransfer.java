package com.futong.gemini.plugin.cloud.huawei.common;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.common.model.BaseDataResponse;
import com.huaweicloud.sdk.ecs.v2.model.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 华为云基础数据传输类 - 简化版
 * 仅保留被注册的操作方法所需的转换功能
 */
@Slf4j
public class HuaWeiCloudDataTransfer {

    /**
     * 转换单个实例（从ShowServerResponse）
     * 被RefreshService使用
     */
    public static JSONObject convertInstance(ShowServerResponse response) {
        if (response == null || response.getServer() == null) {
            return null;
        }
        return convertInstance(response.getServer());
    }

    /**
     * 转换单个实例
     * 被RefreshService使用
     */
    public static JSONObject convertInstance(ServerDetail server) {
        if (server == null) {
            return null;
        }
        
        JSONObject instance = new JSONObject();
        instance.put("id", server.getId());
        instance.put("name", server.getName());
        instance.put("status", server.getStatus());
        instance.put("flavor", server.getFlavor() != null ? server.getFlavor().getId() : null);
        instance.put("image", server.getImage() != null ? server.getImage().getId() : null);
        instance.put("created", server.getCreated());
        instance.put("updated", server.getUpdated());
        instance.put("cloudType", "huawei");
        return instance;
    }

    /**
     * 转换创建服务器响应
     * 被ComputeInstanceService.createInstance使用
     */
    public static JSONObject convertCreateServersResponse(CreateServersResponse response) {
        if (response == null) {
            return null;
        }
        
        JSONObject result = new JSONObject();
        result.put("job_id", response.getJobId());
        result.put("cloudType", "huawei");
        
        if (response.getServerIds() != null) {
            result.put("server_ids", response.getServerIds());
        }
        
        return result;
    }

    /**
     * 创建成功响应
     */
    public static BaseResponse successResponse(Object data) {
        return new BaseDataResponse<>(data);
    }

    /**
     * 创建失败响应
     */
    public static BaseResponse errorResponse(String message) {
        return BaseResponse.FAIL_OP.of(message);
    }
} 