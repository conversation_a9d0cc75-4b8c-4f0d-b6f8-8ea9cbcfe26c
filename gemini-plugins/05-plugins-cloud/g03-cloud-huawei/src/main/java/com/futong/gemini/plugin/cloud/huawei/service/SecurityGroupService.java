package com.futong.gemini.plugin.cloud.huawei.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 华为云安全组服务类
 * 负责安全组和安全组规则的管理操作
 */
@Slf4j
public class SecurityGroupService {

    // 所有查询方法已删除，因为数据同步由FetchService统一处理
    // 如需要添加安全组操作方法（创建、删除、修改规则等），请在此处实现

} 