package com.futong.gemini.plugin.cloud.huawei.common;

import cn.hutool.core.util.StrUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.ecs.v2.region.EcsRegion;
import com.huaweicloud.sdk.evs.v2.EvsClient;
import com.huaweicloud.sdk.evs.v2.region.EvsRegion;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.region.IamRegion;
import com.huaweicloud.sdk.ims.v2.ImsClient;
import com.huaweicloud.sdk.ims.v2.region.ImsRegion;
import com.huaweicloud.sdk.vpc.v3.VpcClient;
import com.huaweicloud.sdk.vpc.v3.region.VpcRegion;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;

/**
 * 华为云客户端工厂类
 * 负责创建各种华为云服务的客户端实例
 */
@Slf4j
public class CloudClient extends BaseCloudClient {
    public static final CloudClient client = new CloudClient();

    //获取Client对象
    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            //创建华为云认证信息
            ICredential credentials = new BasicCredentials()
                    .withAk(body.getAccess().getUsername())
                    .withSk(body.getAccess().getPassword());

            //获取区域信息
            String regionId = body.getCloud().getString("regionId");
            
            //创建HTTP配置
            HttpConfig config = HttpConfig.getDefaultHttpConfig();
            
            //设置代理
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                try {
                    URL url = new URL(body.getAuth().getString("proxyAddr"));
                    config.withProxyHost(url.getHost())
                          .withProxyPort(url.getPort());
                } catch (Exception e) {
                    log.warn("Failed to parse proxy address: {}", body.getAuth().getString("proxyAddr"));
                }
            }

            //根据客户端类型创建对应的华为云客户端
            if (clazz == EcsClient.class) {
                return (C) EcsClient.newBuilder()
                        .withHttpConfig(config)
                        .withCredential(credentials)
                        .withRegion(EcsRegion.valueOf(regionId))
                        .build();
            } else if (clazz == IamClient.class) {
                return (C) IamClient.newBuilder()
                        .withHttpConfig(config)
                        .withCredential(credentials)
                        .withRegion(IamRegion.valueOf(regionId))
                        .build();
            } else if (clazz == VpcClient.class) {
                return (C) VpcClient.newBuilder()
                        .withHttpConfig(config)
                        .withCredential(credentials)
                        .withRegion(VpcRegion.valueOf(regionId))
                        .build();
            } else if (clazz == EvsClient.class) {
                return (C) EvsClient.newBuilder()
                        .withHttpConfig(config)
                        .withCredential(credentials)
                        .withRegion(EvsRegion.valueOf(regionId))
                        .build();
            } else if (clazz == ImsClient.class) {
                return (C) ImsClient.newBuilder()
                        .withHttpConfig(config)
                        .withCredential(credentials)
                        .withRegion(ImsRegion.valueOf(regionId))
                        .build();
            } else {
                throw new BaseException(BaseResponse.ERROR_BIZ, "Unsupported client type: " + clazz.getName());
            }
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 创建ECS客户端 - 保持兼容性
     */
    public static EcsClient createEcsClient(String accessKeyId, String accessKeySecret, String regionId) {
        try {
            ICredential credentials = new BasicCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            HttpConfig config = HttpConfig.getDefaultHttpConfig();
            config.withTimeout(60);

            return EcsClient.newBuilder()
                    .withHttpConfig(config)
                    .withCredential(credentials)
                    .withRegion(EcsRegion.valueOf(regionId))
                    .build();
        } catch (Exception e) {
            log.error("创建ECS客户端失败", e);
            throw new RuntimeException("创建ECS客户端失败: " + e.getMessage());
        }
    }

    /**
     * 创建VPC客户端 - 保持兼容性
     */
    public static VpcClient createVpcClient(String accessKeyId, String accessKeySecret, String regionId) {
        try {
            ICredential credentials = new BasicCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            HttpConfig config = HttpConfig.getDefaultHttpConfig();
            config.withTimeout(60);

            return VpcClient.newBuilder()
                    .withHttpConfig(config)
                    .withCredential(credentials)
                    .withRegion(VpcRegion.valueOf(regionId))
                    .build();
        } catch (Exception e) {
            log.error("创建VPC客户端失败", e);
            throw new RuntimeException("创建VPC客户端失败: " + e.getMessage());
        }
    }

    /**
     * 创建EVS客户端 - 保持兼容性
     */
    public static EvsClient createEvsClient(String accessKeyId, String accessKeySecret, String regionId) {
        try {
            ICredential credentials = new BasicCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            HttpConfig config = HttpConfig.getDefaultHttpConfig();
            config.withTimeout(60);

            return EvsClient.newBuilder()
                    .withHttpConfig(config)
                    .withCredential(credentials)
                    .withRegion(EvsRegion.valueOf(regionId))
                    .build();
        } catch (Exception e) {
            log.error("创建EVS客户端失败", e);
            throw new RuntimeException("创建EVS客户端失败: " + e.getMessage());
        }
    }

    /**
     * 创建IMS客户端 - 保持兼容性
     */
    public static ImsClient createImsClient(String accessKeyId, String accessKeySecret, String regionId) {
        try {
            ICredential credentials = new BasicCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            HttpConfig config = HttpConfig.getDefaultHttpConfig();
            config.withTimeout(60);

            return ImsClient.newBuilder()
                    .withHttpConfig(config)
                    .withCredential(credentials)
                    .withRegion(ImsRegion.valueOf(regionId))
                    .build();
        } catch (Exception e) {
            log.error("创建IMS客户端失败", e);
            throw new RuntimeException("创建IMS客户端失败: " + e.getMessage());
        }
    }
} 