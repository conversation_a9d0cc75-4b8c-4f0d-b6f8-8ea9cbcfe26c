package com.futong.gemini.plugin.cloud.huawei.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 华为云网络服务类
 * 负责VPC、子网等网络资源的管理操作
 */
@Slf4j
public class NetworkService {

    // 所有查询方法已删除，因为数据同步由FetchService统一处理
    // 如需要添加网络操作方法（创建VPC、创建子网等），请在此处实现

} 