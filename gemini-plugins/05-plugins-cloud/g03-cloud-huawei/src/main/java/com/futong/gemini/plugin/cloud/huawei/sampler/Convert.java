package com.futong.gemini.plugin.cloud.huawei.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.futong.common.utils.TimeUtils;
import com.futong.constant.dict.CloudType;
import com.futong.constant.dict.DevopsSide;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.huaweicloud.sdk.ecs.v2.model.*;
import com.huaweicloud.sdk.evs.v2.model.ListVolumesResponse;
import com.huaweicloud.sdk.evs.v2.model.VolumeDetail;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsResponse;
import com.huaweicloud.sdk.iam.v3.model.Region;
import com.huaweicloud.sdk.ims.v2.model.GlanceListImagesResponse;
import com.huaweicloud.sdk.vpc.v2.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 华为云数据转换器
 * 遵循项目标准模式，用于BaseCloudService.fetch系列方法的方法引用
 */
@Slf4j
public class Convert {

    /**
     * 华为云通用属性设置
     * @param request 请求对象
     * @param t CI资源对象
     * @param <T> CI资源类型
     */
    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(CloudType.PUBLIC_HUAWEI.value());
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    /**
     * 华为云状态转换为标准状态
     * @param huaweiStatus 华为云状态
     * @return 标准状态
     */
    private static String convertStatus(String huaweiStatus) {
        if (StrUtil.isEmpty(huaweiStatus)) {
            return "unknown";
        }
        switch (huaweiStatus.toLowerCase()) {
            case "active":
            case "available":
            case "in-use":
                return "available";
            case "build":
            case "building":
            case "creating":
                return "creating";
            case "shutoff":
            case "stopped":
                return "stopped";
            case "error":
            case "failed":
                return "error";
            case "deleting":
                return "deleting";
            default:
                return "other";
        }
    }

    /**
     * 转换地域信息
     */
    public static Map<Class, List> convertRegion(BaseCloudRequest request, KeystoneListRegionsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getRegions() || CollUtil.isEmpty(response.getRegions())) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        
        // 将获取到的地域信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_HUAWEI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                response.getRegions(),
                Region::getId,  // 华为云用id作为区域名称
                Region::getId   // 华为云用id作为区域标识
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 转换可用区信息
     */
    public static Map<Class, List> convertZone(BaseCloudRequest request, NovaListAvailabilityZonesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getAvailabilityZoneInfo() || CollUtil.isEmpty(response.getAvailabilityZoneInfo())) {
            result.put(TmdbDevops.class, null);
            result.put(TmdbDevopsLink.class, null);
            return result;
        }
        
        // 将获取到的可用区信息转换为CI模型
        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_HUAWEI.value(),
                DevopsSide.DEVOPS_ZONE.value(),
                response.getAvailabilityZoneInfo(),
                zone -> zone.getZoneName(),
                zone -> zone.getZoneName()
        );
        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 转换云服务器信息
     */
    public static Map<Class, List> convertServer(BaseCloudRequest request, ListServersDetailsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getServers() || CollUtil.isEmpty(response.getServers())) {
            result.put(CmdbInstanceRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (ServerDetail server : response.getServers()) {
            CmdbInstanceRes ci = new CmdbInstanceRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), server.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(server.getId());
            ci.setOpen_name(server.getName());
            ci.setStatus(convertStatus(server.getStatus()));
            ci.setOpen_status(server.getStatus());
            
            // 设置创建时间
            if (StrUtil.isNotEmpty(server.getCreated())) {
                try {
                    ci.setCreate_time(TimeUtils.utcStringToMilliLong(server.getCreated()));
                } catch (Exception e) {
                    log.warn("解析华为云服务器创建时间失败: {}", server.getCreated());
                }
            }
            
            // 设置更新时间
            if (StrUtil.isNotEmpty(server.getUpdated())) {
                try {
                    ci.setUpdate_time(TimeUtils.utcStringToMilliLong(server.getUpdated()));
                } catch (Exception e) {
                    log.warn("解析华为云服务器更新时间失败: {}", server.getUpdated());
                }
            }
            
            // 设置规格信息
            if (server.getFlavor() != null && StrUtil.isNotEmpty(server.getFlavor().getId())) {
                // 关联规格
                Association flavor = AssociationUtils.toAssociation(ci, CmdbFlavor.class, 
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), server.getFlavor().getId(), request.getBody().getCloud().getString("regionId")));
                associations.add(flavor);
            }
            
            // 设置镜像信息
            if (server.getImage() != null && StrUtil.isNotEmpty(server.getImage().getId())) {
                // 关联镜像
                Association image = AssociationUtils.toAssociation(ci, CmdbImageRes.class,
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), server.getImage().getId(), request.getBody().getCloud().getString("regionId")));
                associations.add(image);
            }
            
            // 设置网络信息 - 暂时简化处理
            if (server.getAddresses() != null && !server.getAddresses().isEmpty()) {
                StringBuilder allIps = new StringBuilder();
                
                for (Map.Entry<String, List<ServerAddress>> entry : server.getAddresses().entrySet()) {
                    List<ServerAddress> addresses = entry.getValue();
                    if (addresses != null) {
                        for (ServerAddress addr : addresses) {
                            if (allIps.length() > 0) allIps.append(",");
                            allIps.append(addr.getAddr());
                        }
                    }
                }
            }
            
            // 设置密钥对信息
            if (StrUtil.isNotEmpty(server.getKeyName())) {
                // 关联密钥对
                Association keypair = AssociationUtils.toAssociation(ci, CmdbKeypairRes.class,
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), server.getKeyName()));
                associations.add(keypair);
            }
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_INSTANCE_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbInstanceRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换规格信息
     */
    public static Map<Class, List> convertFlavor(BaseCloudRequest request, ListFlavorsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getFlavors() || CollUtil.isEmpty(response.getFlavors())) {
            result.put(CmdbFlavor.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        
        List<CmdbFlavor> data = new ArrayList<>();
        
        for (Flavor flavor : response.getFlavors()) {
            CmdbFlavor ci = new CmdbFlavor();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), flavor.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(flavor.getId());
            ci.setOpen_name(flavor.getName());
            
            // 设置CPU核数
            if (StrUtil.isNotEmpty(flavor.getVcpus())) {
                try {
                    ci.setCpu_size(Integer.parseInt(flavor.getVcpus()));
                } catch (NumberFormatException e) {
                    log.warn("解析华为云规格CPU核数失败: {}", flavor.getVcpus());
                }
            }
            
            // 设置内存大小（华为云以MB为单位）
            if (flavor.getRam() != null) {
                ci.setMem_size(flavor.getRam()); // 华为云已经是MB单位
            }
            
            // 设置磁盘大小（华为云规格可能有本地存储）
            if (StrUtil.isNotEmpty(flavor.getDisk())) {
                try {
                    ci.setMin_sysdisk_size(Float.parseFloat(flavor.getDisk()));
                } catch (NumberFormatException e) {
                    log.warn("解析华为云规格磁盘大小失败: {}", flavor.getDisk());
                }
            }
            
            // 设置规格族信息
            if (StrUtil.isNotEmpty(flavor.getName())) {
                // 从规格名称中提取规格族，华为云规格名通常以规格族开头
                String[] parts = flavor.getName().split("\\.");
                if (parts.length > 0) {
                    ci.setSpecification_class_code(parts[0]);
                    ci.setSpecification_class_name(parts[0]);
                }
            }
            
            // 设置分类
            ci.setCategory("CPU"); // 默认为CPU类型，GPU等特殊类型需要进一步判断
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_FLAVOR
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbFlavor.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 转换镜像信息
     */
    public static Map<Class, List> convertImage(BaseCloudRequest request, GlanceListImagesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getImages() || CollUtil.isEmpty(response.getImages())) {
            result.put(CmdbImageRes.class, null);
            result.put(CmdbOsRes.class, null);
            result.put(Association.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        
        List<CmdbImageRes> dataImage = new ArrayList<>();
        List<CmdbOsRes> dataOs = new ArrayList<>();
        List<Association> dataAss = new ArrayList<>();
        
        for (Object imageObj : response.getImages()) {
            // 华为云镜像对象结构，需要使用反射或转换
            if (imageObj instanceof Map) {
                Map<String, Object> image = (Map<String, Object>) imageObj;
                
                CmdbImageRes ci = new CmdbImageRes();
                CmdbOsRes os = new CmdbOsRes();
                
                String imageId = (String) image.get("id");
                String imageName = (String) image.get("name");
                
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), imageId, request.getBody().getCloud().getString("regionId")));
                ci.setOpen_id(imageId);
                ci.setOpen_name(imageName);
                
                // 设置镜像状态
                String status = (String) image.get("status");
                ci.setStatus(convertStatus(status));
                ci.setOpen_status(status);
                
                // 设置镜像类型和可见性
                String visibility = (String) image.get("visibility");
                if ("public".equals(visibility)) {
                    ci.setVisibility("public");
                } else {
                    ci.setVisibility("private");
                }
                
                // 设置镜像大小
                Object sizeObj = image.get("size");
                if (sizeObj != null) {
                    try {
                        Long size = Long.parseLong(sizeObj.toString());
                        ci.setSize((float) (size / 1024 / 1024 / 1024)); // 转换为GB
                    } catch (NumberFormatException e) {
                        log.warn("解析华为云镜像大小失败: {}", sizeObj);
                    }
                }
                
                // 设置操作系统信息
                String osType = (String) image.get("os_type");
                String osVersion = (String) image.get("os_version");
                
                if (StrUtil.isNotEmpty(osType)) {
                    ci.setType(osType);
                    
                    // 暂时简化操作系统对象的处理
                    // TODO: 需要确认CmdbOsRes的正确方法名后再完善
                }
                
                // 设置描述信息
                String description = (String) image.get("description");
                if (StrUtil.isNotEmpty(description)) {
                    ci.setDesc(description);
                }
                
                // 设置创建时间
                String createdAt = (String) image.get("created_at");
                if (StrUtil.isNotEmpty(createdAt)) {
                    try {
                        ci.setCreate_time(TimeUtils.utcStringToMilliLong(createdAt));
                    } catch (Exception e) {
                        log.warn("解析华为云镜像创建时间失败: {}", createdAt);
                    }
                }
                
                toCiResCloud(request, ci);
                dataImage.add(ci);
            }
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_IMAGE_RES
                ).withDataByDevopsValue(dataImage,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbImageRes.class, dataImage);
        result.put(CmdbOsRes.class, dataOs);
        result.put(Association.class, dataAss);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 转换磁盘信息
     */
    public static Map<Class, List> convertVolume(BaseCloudRequest request, ListVolumesResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getVolumes() || CollUtil.isEmpty(response.getVolumes())) {
            result.put(CmdbDiskRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbDiskRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (VolumeDetail volume : response.getVolumes()) {
            CmdbDiskRes ci = new CmdbDiskRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), volume.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(volume.getId());
            ci.setOpen_name(volume.getName());
            
            // 设置磁盘状态
            ci.setStatus(convertStatus(volume.getStatus()));
            ci.setOpen_status(volume.getStatus());
            
            // 设置磁盘大小（华为云以GB为单位）
            if (volume.getSize() != null) {
                ci.setSize(volume.getSize().floatValue());
            }
            
            // 设置磁盘类型
            if (StrUtil.isNotEmpty(volume.getVolumeType())) {
                ci.setType(volume.getVolumeType());
            }
            
            // 设置描述信息
            if (StrUtil.isNotEmpty(volume.getDescription())) {
                ci.setDesc(volume.getDescription());
            }
            
            // 设置创建时间
            if (StrUtil.isNotEmpty(volume.getCreatedAt())) {
                try {
                    ci.setCreate_time(TimeUtils.utcStringToMilliLong(volume.getCreatedAt()));
                } catch (Exception e) {
                    log.warn("解析华为云磁盘创建时间失败: {}", volume.getCreatedAt());
                }
            }
            
            // 设置可用区
            if (StrUtil.isNotEmpty(volume.getAvailabilityZone())) {
                // 暂时跳过可用区设置，需要确认正确的方法名
            }
            
            // 设置是否为启动盘
            String bootableStr = volume.getBootable();
            if ("true".equals(bootableStr)) {
                // 暂时跳过启动盘设置，需要确认正确的方法名
            }
            
            // 关联实例（如果磁盘已挂载）
            if (volume.getAttachments() != null && !volume.getAttachments().isEmpty()) {
                for (Object attachment : volume.getAttachments()) {
                    if (attachment instanceof Map) {
                        Map<String, Object> attachMap = (Map<String, Object>) attachment;
                        String serverId = (String) attachMap.get("server_id");
                        if (StrUtil.isNotEmpty(serverId)) {
                            Association instance = AssociationUtils.toAssociation(ci, CmdbInstanceRes.class,
                                IdUtils.encryptId(request.getBody().getAccess().getCmpId(), serverId, request.getBody().getCloud().getString("regionId")));
                            associations.add(instance);
                        }
                    }
                }
            }
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_DISK_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbDiskRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换VPC信息
     */
    public static Map<Class, List> convertVpc(BaseCloudRequest request, ListVpcsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getVpcs() || CollUtil.isEmpty(response.getVpcs())) {
            result.put(CmdbVpcRes.class, null);
            result.put(TmdbResourceSet.class, null);
            return result;
        }
        
        List<CmdbVpcRes> data = new ArrayList<>();
        
        for (Vpc vpc : response.getVpcs()) {
            CmdbVpcRes ci = new CmdbVpcRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vpc.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(vpc.getId());
            ci.setOpen_name(vpc.getName());
            
            // 设置VPC状态
            if (vpc.getStatus() != null) {
                String statusStr = vpc.getStatus().toString();
                ci.setStatus(convertStatus(statusStr));
                ci.setOpen_status(statusStr);
            }
            
            // 设置CIDR
            if (StrUtil.isNotEmpty(vpc.getCidr())) {
                ci.setCidr(vpc.getCidr());
            }
            
            // 设置描述信息
            if (StrUtil.isNotEmpty(vpc.getDescription())) {
                ci.setDesc(vpc.getDescription());
            }
            
            // 设置创建时间
            if (vpc.getCreatedAt() != null) {
                try {
                    String createdAtStr = vpc.getCreatedAt().toString();
                    ci.setCreate_time(TimeUtils.utcStringToMilliLong(createdAtStr));
                } catch (Exception e) {
                    log.warn("解析华为云VPC创建时间失败: {}", vpc.getCreatedAt());
                }
            }
            
            // 设置更新时间
            if (vpc.getUpdatedAt() != null) {
                try {
                    String updatedAtStr = vpc.getUpdatedAt().toString();
                    ci.setUpdate_time(TimeUtils.utcStringToMilliLong(updatedAtStr));
                } catch (Exception e) {
                    log.warn("解析华为云VPC更新时间失败: {}", vpc.getUpdatedAt());
                }
            }
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_VPC_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbVpcRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        return result;
    }

    /**
     * 转换安全组信息
     */
    public static Map<Class, List> convertSecurityGroup(BaseCloudRequest request, ListSecurityGroupsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getSecurityGroups() || CollUtil.isEmpty(response.getSecurityGroups())) {
            result.put(CmdbSecuritygroupRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        List<CmdbSecuritygroupRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (com.huaweicloud.sdk.vpc.v2.model.SecurityGroup securityGroup : response.getSecurityGroups()) {
            CmdbSecuritygroupRes ci = new CmdbSecuritygroupRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroup.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(securityGroup.getId());
            ci.setOpen_name(securityGroup.getName());
            
            // 设置描述信息
            if (StrUtil.isNotEmpty(securityGroup.getDescription())) {
                ci.setDesc(securityGroup.getDescription());
            }
            
            // 华为云安全组对象暂时没有提供创建时间和更新时间字段
            
            // 关联VPC
            if (StrUtil.isNotEmpty(securityGroup.getVpcId())) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class,
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), securityGroup.getVpcId(), request.getBody().getCloud().getString("regionId")));
                associations.add(vpc);
            }
            
            toCiResCloud(request, ci);
            data.add(ci);
        }
        
        // 创建资源集合
        List<TmdbResourceSet> sets = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_SECURITYGROUP_RES
                ).withDataByDevopsValue(data,
                        DevopsSide.DEVOPS_REGION,
                        request.getBody().getCloud().getString("regionId"))
                .getData();
        
        result.put(CmdbSecuritygroupRes.class, data);
        result.put(TmdbResourceSet.class, sets);
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换子网信息
     */
    public static Map<Class, List> convertSubnet(BaseCloudRequest request, ListSubnetsResponse response) {
        Map<Class, List> result = new HashMap<>();
        if (null == response || null == response.getSubnets() || CollUtil.isEmpty(response.getSubnets())) {
            result.put(CmdbSubnetRes.class, null);
            result.put(TmdbResourceSet.class, null);
            result.put(Association.class, null);
            return result;
        }
        
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfo(request.getBody().getAccess().getCmpId(),
                        CloudType.PUBLIC_HUAWEI,
                        ResourceType.CMDB_SUBNET_RES
                );
        String regionResId = BuilderDevops.builderId(request.getBody().getAccess().getCmpId(),
                CloudType.PUBLIC_HUAWEI.value(),
                DevopsSide.DEVOPS_REGION.value(),
                request.getBody().getCloud().getString("regionId"));
        
        List<CmdbSubnetRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        
        for (Subnet subnet : response.getSubnets()) {
            CmdbSubnetRes ci = new CmdbSubnetRes();
            ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnet.getId(), request.getBody().getCloud().getString("regionId")));
            ci.setOpen_id(subnet.getId());
            ci.setOpen_name(subnet.getName());
            
            // 设置状态
            if (subnet.getStatus() != null) {
                String statusStr = subnet.getStatus().toString();
                ci.setStatus(convertStatus(statusStr));
                ci.setOpen_status(statusStr);
            }
            
            // 设置CIDR
            if (StrUtil.isNotEmpty(subnet.getCidr())) {
                ci.setCidr_ipv4(subnet.getCidr());
            }
            
            // 设置描述信息
            if (StrUtil.isNotEmpty(subnet.getDescription())) {
                ci.setDesc(subnet.getDescription());
            }
            
            // 设置网关IP
            if (StrUtil.isNotEmpty(subnet.getGatewayIp())) {
                ci.setGateway_ipv4(subnet.getGatewayIp());
            }
            
            // 设置是否启用DHCP
            Boolean dhcpEnable = subnet.getDhcpEnable();
            if (dhcpEnable != null) {
                ci.setDhcp_enable(dhcpEnable ? 1 : 0);
            }
            
            toCiResCloud(request, ci);
            data.add(ci);
            
            // 关联地域
            builderResourceSet.withData(ci.getRes_id(), DevopsSide.DEVOPS_REGION, regionResId);
            
            // 关联VPC
            if (StrUtil.isNotEmpty(subnet.getVpcId())) {
                Association vpc = AssociationUtils.toAssociation(ci, CmdbVpcRes.class,
                    IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnet.getVpcId(), request.getBody().getCloud().getString("regionId")));
                associations.add(vpc);
            }
        }
        
        result.put(CmdbSubnetRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换密钥对信息 - 暂时注释，等待确认正确的响应类型
     */
    public static Map<Class, List> convertKeyPair(BaseCloudRequest request, Object response) {
        Map<Class, List> result = new HashMap<>();
        // 暂时返回空结果，确保编译通过
        result.put(TmdbResourceSet.class, null);
        return result;
    }
} 