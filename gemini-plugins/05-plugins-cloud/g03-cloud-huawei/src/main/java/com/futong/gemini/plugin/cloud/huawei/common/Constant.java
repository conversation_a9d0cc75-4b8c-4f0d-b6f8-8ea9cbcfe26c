package com.futong.gemini.plugin.cloud.huawei.common;

import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.futong.common.utils.Entry.E2;
import static com.futong.common.utils.Entry.E3;

public class Constant {

    public static Map<String, Entry.E3<String, Entry.E2<String, String>[], String>> metrics = new HashMap<>();
    private static E2<String, String>[] metricType = new E2[]{
            new E2("Average", "average"),
            new E2("Maximum", "max"),
            new E2("Minimum", "min"),
            new E2("Value", "value"),
            new E2("Sum", "sum"),
    };

    static {
        // 华为云ECS实例性能指标
        metrics.put("cpu_util", new E3("cpuUsage", metricType, "%"));
        metrics.put("mem_util", new E3("memUsage", metricType, "%"));
        metrics.put("disk_util", new E3("diskUsage", metricType, "%"));
        metrics.put("disk_read_bytes_rate", new E3("diskRead", metricType, "Bytes/s"));
        metrics.put("disk_write_bytes_rate", new E3("diskWrite", metricType, "Bytes/s"));
        metrics.put("network_incoming_bytes_rate", new E3("netIn", metricType, "Bytes/s"));
        metrics.put("network_outgoing_bytes_rate", new E3("netOut", metricType, "Bytes/s"));
    }

    public static Map<String, BiConsumer<PerfInfoBean, Double>> perfMapping = new HashMap<>();

    static {
        perfMapping.put("cpu_util", PerfInfoBean::setCpuUsage);
        perfMapping.put("mem_util", PerfInfoBean::setMemUsage);
        perfMapping.put("disk_util", PerfInfoBean::setDiskUsage);
        perfMapping.put("disk_read_bytes_rate", PerfInfoBean::setDiskRead);
        perfMapping.put("disk_write_bytes_rate", PerfInfoBean::setDiskWrite);
        perfMapping.put("network_incoming_bytes_rate", PerfInfoBean::setNetIn);
        perfMapping.put("network_outgoing_bytes_rate", PerfInfoBean::setNetOut);
    }

    // 华为云区域常量
    public static final String REGION_CN_NORTH_1 = "cn-north-1";
    public static final String REGION_CN_NORTH_4 = "cn-north-4";
    public static final String REGION_CN_EAST_2 = "cn-east-2";
    public static final String REGION_CN_EAST_3 = "cn-east-3";
    public static final String REGION_CN_SOUTH_1 = "cn-south-1";
    public static final String REGION_CN_SOUTHWEST_2 = "cn-southwest-2";
    public static final String REGION_AP_SOUTHEAST_1 = "ap-southeast-1";
    public static final String REGION_AP_SOUTHEAST_2 = "ap-southeast-2";
    public static final String REGION_AP_SOUTHEAST_3 = "ap-southeast-3";
    public static final String REGION_AF_SOUTH_1 = "af-south-1";
    public static final String REGION_NA_MEXICO_1 = "na-mexico-1";
    public static final String REGION_LA_NORTH_2 = "la-north-2";
    public static final String REGION_SA_BRAZIL_1 = "sa-brazil-1";
    public static final String REGION_LA_SOUTH_2 = "la-south-2";
    public static final String REGION_CN_NORTH_9 = "cn-north-9";
    public static final String REGION_CN_NORTH_2 = "cn-north-2";
    public static final String REGION_CN_NORTH_11 = "cn-north-11";
    public static final String REGION_CN_EAST_5 = "cn-east-5";
    public static final String REGION_CN_SOUTH_2 = "cn-south-2";
    public static final String REGION_CN_SOUTH_4 = "cn-south-4";
    public static final String REGION_CN_SOUTHWEST_1 = "cn-southwest-1";
    public static final String REGION_AP_SOUTHEAST_4 = "ap-southeast-4";
    public static final String REGION_ME_EAST_1 = "me-east-1";
    public static final String REGION_EU_WEST_101 = "eu-west-101";
    public static final String REGION_EU_WEST_0 = "eu-west-0";
    public static final String REGION_EU_WEST_200 = "eu-west-200";
    public static final String REGION_TR_WEST_1 = "tr-west-1";
    public static final String REGION_AE_AD_1 = "ae-ad-1";
    public static final String REGION_AP_SOUTHEAST_5 = "ap-southeast-5";
    public static final String REGION_AP_SOUTHEAST_6 = "ap-southeast-6";
    public static final String REGION_AP_SOUTHEAST_7 = "ap-southeast-7";
    public static final String REGION_AP_SOUTHEAST_8 = "ap-southeast-8";
    public static final String REGION_AP_SOUTHEAST_9 = "ap-southeast-9";
    public static final String REGION_AP_SOUTHEAST_10 = "ap-southeast-10";
    public static final String REGION_AP_SOUTHEAST_11 = "ap-southeast-11";
    public static final String REGION_AP_SOUTHEAST_12 = "ap-southeast-12";
    public static final String REGION_AP_SOUTHEAST_13 = "ap-southeast-13";
    public static final String REGION_AP_SOUTHEAST_14 = "ap-southeast-14";
    public static final String REGION_AP_SOUTHEAST_15 = "ap-southeast-15";
    public static final String REGION_AP_SOUTHEAST_16 = "ap-southeast-16";
    public static final String REGION_AP_SOUTHEAST_17 = "ap-southeast-17";
    public static final String REGION_AP_SOUTHEAST_18 = "ap-southeast-18";
    public static final String REGION_AP_SOUTHEAST_19 = "ap-southeast-19";
    public static final String REGION_AP_SOUTHEAST_20 = "ap-southeast-20";
    public static final String REGION_AP_SOUTHEAST_21 = "ap-southeast-21";
    public static final String REGION_AP_SOUTHEAST_22 = "ap-southeast-22";
    public static final String REGION_AP_SOUTHEAST_23 = "ap-southeast-23";
    public static final String REGION_AP_SOUTHEAST_24 = "ap-southeast-24";
    public static final String REGION_AP_SOUTHEAST_25 = "ap-southeast-25";
    public static final String REGION_AP_SOUTHEAST_26 = "ap-southeast-26";
    public static final String REGION_AP_SOUTHEAST_27 = "ap-southeast-27";
    public static final String REGION_AP_SOUTHEAST_28 = "ap-southeast-28";
    public static final String REGION_AP_SOUTHEAST_29 = "ap-southeast-29";
    public static final String REGION_AP_SOUTHEAST_30 = "ap-southeast-30";
    public static final String REGION_AP_SOUTHEAST_31 = "ap-southeast-31";
    public static final String REGION_AP_SOUTHEAST_32 = "ap-southeast-32";
    public static final String REGION_AP_SOUTHEAST_33 = "ap-southeast-33";
    public static final String REGION_AP_SOUTHEAST_34 = "ap-southeast-34";
    public static final String REGION_AP_SOUTHEAST_35 = "ap-southeast-35";
    public static final String REGION_AP_SOUTHEAST_36 = "ap-southeast-36";
    public static final String REGION_AP_SOUTHEAST_37 = "ap-southeast-37";
    public static final String REGION_AP_SOUTHEAST_38 = "ap-southeast-38";
    public static final String REGION_AP_SOUTHEAST_39 = "ap-southeast-39";
    public static final String REGION_AP_SOUTHEAST_40 = "ap-southeast-40";
    public static final String REGION_AP_SOUTHEAST_41 = "ap-southeast-41";
    public static final String REGION_AP_SOUTHEAST_42 = "ap-southeast-42";
    public static final String REGION_AP_SOUTHEAST_43 = "ap-southeast-43";
    public static final String REGION_AP_SOUTHEAST_44 = "ap-southeast-44";
    public static final String REGION_AP_SOUTHEAST_45 = "ap-southeast-45";
    public static final String REGION_AP_SOUTHEAST_46 = "ap-southeast-46";
    public static final String REGION_AP_SOUTHEAST_47 = "ap-southeast-47";
    public static final String REGION_AP_SOUTHEAST_48 = "ap-southeast-48";
    public static final String REGION_AP_SOUTHEAST_49 = "ap-southeast-49";
    public static final String REGION_AP_SOUTHEAST_50 = "ap-southeast-50";
    public static final String REGION_AP_SOUTHEAST_51 = "ap-southeast-51";
    public static final String REGION_AP_SOUTHEAST_52 = "ap-southeast-52";
    public static final String REGION_AP_SOUTHEAST_53 = "ap-southeast-53";
    public static final String REGION_AP_SOUTHEAST_54 = "ap-southeast-54";
    public static final String REGION_AP_SOUTHEAST_55 = "ap-southeast-55";
    public static final String REGION_AP_SOUTHEAST_56 = "ap-southeast-56";
    public static final String REGION_AP_SOUTHEAST_57 = "ap-southeast-57";
    public static final String REGION_AP_SOUTHEAST_58 = "ap-southeast-58";
    public static final String REGION_AP_SOUTHEAST_59 = "ap-southeast-59";
    public static final String REGION_AP_SOUTHEAST_60 = "ap-southeast-60";
    public static final String REGION_AP_SOUTHEAST_61 = "ap-southeast-61";
    public static final String REGION_AP_SOUTHEAST_62 = "ap-southeast-62";
    public static final String REGION_AP_SOUTHEAST_63 = "ap-southeast-63";
    public static final String REGION_AP_SOUTHEAST_64 = "ap-southeast-64";
    public static final String REGION_AP_SOUTHEAST_65 = "ap-southeast-65";
    public static final String REGION_AP_SOUTHEAST_66 = "ap-southeast-66";
    public static final String REGION_AP_SOUTHEAST_67 = "ap-southeast-67";
    public static final String REGION_AP_SOUTHEAST_68 = "ap-southeast-68";
    public static final String REGION_AP_SOUTHEAST_69 = "ap-southeast-69";
    public static final String REGION_AP_SOUTHEAST_70 = "ap-southeast-70";
    public static final String REGION_AP_SOUTHEAST_71 = "ap-southeast-71";
    public static final String REGION_AP_SOUTHEAST_72 = "ap-southeast-72";
    public static final String REGION_AP_SOUTHEAST_73 = "ap-southeast-73";
    public static final String REGION_AP_SOUTHEAST_74 = "ap-southeast-74";
    public static final String REGION_AP_SOUTHEAST_75 = "ap-southeast-75";
    public static final String REGION_AP_SOUTHEAST_76 = "ap-southeast-76";
    public static final String REGION_AP_SOUTHEAST_77 = "ap-southeast-77";
    public static final String REGION_AP_SOUTHEAST_78 = "ap-southeast-78";
    public static final String REGION_AP_SOUTHEAST_79 = "ap-southeast-79";
    public static final String REGION_AP_SOUTHEAST_80 = "ap-southeast-80";
    public static final String REGION_AP_SOUTHEAST_81 = "ap-southeast-81";
    public static final String REGION_AP_SOUTHEAST_82 = "ap-southeast-82";
    public static final String REGION_AP_SOUTHEAST_83 = "ap-southeast-83";
    public static final String REGION_AP_SOUTHEAST_84 = "ap-southeast-84";
    public static final String REGION_AP_SOUTHEAST_85 = "ap-southeast-85";
    public static final String REGION_AP_SOUTHEAST_86 = "ap-southeast-86";
    public static final String REGION_AP_SOUTHEAST_87 = "ap-southeast-87";
    public static final String REGION_AP_SOUTHEAST_88 = "ap-southeast-88";
    public static final String REGION_AP_SOUTHEAST_89 = "ap-southeast-89";
    public static final String REGION_AP_SOUTHEAST_90 = "ap-southeast-90";
    public static final String REGION_AP_SOUTHEAST_91 = "ap-southeast-91";
    public static final String REGION_AP_SOUTHEAST_92 = "ap-southeast-92";
    public static final String REGION_AP_SOUTHEAST_93 = "ap-southeast-93";
    public static final String REGION_AP_SOUTHEAST_94 = "ap-southeast-94";
    public static final String REGION_AP_SOUTHEAST_95 = "ap-southeast-95";
    public static final String REGION_AP_SOUTHEAST_96 = "ap-southeast-96";
    public static final String REGION_AP_SOUTHEAST_97 = "ap-southeast-97";
    public static final String REGION_AP_SOUTHEAST_98 = "ap-southeast-98";
    public static final String REGION_AP_SOUTHEAST_99 = "ap-southeast-99";
    public static final String REGION_AP_SOUTHEAST_100 = "ap-southeast-100";
} 