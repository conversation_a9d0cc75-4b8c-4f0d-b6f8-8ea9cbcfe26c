package com.futong.gemini.plugin.cloud.huawei.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.common.CloudClient;
import com.futong.gemini.plugin.cloud.huawei.sampler.FetchService;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.ecs.v2.model.ListServersDetailsRequest;
import com.huaweicloud.sdk.ecs.v2.model.ListServersDetailsResponse;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsResponse;
import com.huaweicloud.sdk.iam.v3.model.Region;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;


@Slf4j
public class AccountService {

    public static Map<Locale, JSONObject> accountForm = new HashMap<>();
    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }

    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            // 使用ECS API验证账号
            CloudClient.client.execute(request.getBody(), EcsClient::listServersDetails);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("华为云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        //替换参数云账号ID
        String text = StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId());
        JSONObject result = JSON.parseObject(text);
        
        // 获取华为云区域列表 - 动态获取而不是硬编码
        List<HashMap<String, String>> regions = getHuaweiRegionsDynamic(request);
        
        if (request.getBody().containsKey("all")) {
            //根据地域生成全量调度任务
            return new BaseDataResponse<>(listAllDispatcher(result, regions));
        } else {
            //获取region信息
            result.getJSONObject("form").getJSONObject("region").put("items", regions);
            return new BaseDataResponse<>(result);
        }
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        List<HashMap<String, String>> regions = getHuaweiRegionsDynamic(request);
        List<JSONObject> dispatchers = listAllDispatcher(result, regions);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, List<HashMap<String, String>> regions) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                for (HashMap<String, String> region : regions) {
                    String itemStrRegion = StrUtil.replace(itemStr, "${region.label}", region.get("label"));
                    itemStrRegion = StrUtil.replace(itemStrRegion, "${region.value}", region.get("value"));
                    dispatchers.add(JSON.parseObject(itemStrRegion));
                }
            }
        });
        return dispatchers;
    }

    /**
     * 动态获取华为云region列表
     * 使用华为云IAM API获取真实的region信息
     */
    private static List<HashMap<String, String>> getHuaweiRegionsDynamic(BaseCloudRequest request) {
        try {
            log.info("开始动态获取华为云region列表");
            
            // 使用默认region（cn-north-1）来调用IAM API获取所有region列表
            BaseCloudRequestBody tempBody = new BaseCloudRequestBody(request.getBody());
            tempBody.getCloud().put("regionId", "cn-north-1");
            
            // 创建IAM客户端
            IamClient iamClient = CloudClient.client.client(IamClient.class, tempBody);
            
            // 调用KeystoneListRegions API获取region列表
            KeystoneListRegionsRequest regionRequest = new KeystoneListRegionsRequest();
            KeystoneListRegionsResponse response = iamClient.keystoneListRegions(regionRequest);
            
            List<HashMap<String, String>> regions = new ArrayList<>();
            if (response != null && response.getRegions() != null) {
                for (Object regionObj : response.getRegions()) {
                    if (regionObj instanceof Region) {
                        Region region = (Region) regionObj;
                        HashMap<String, String> regionMap = new HashMap<>();
                        regionMap.put("value", region.getId());
                        // 直接使用region ID作为显示名称，简化逻辑
                        regionMap.put("label", region.getId());
                        regions.add(regionMap);
                    }
                }
            }
            
            log.info("成功获取到{}个华为云region", regions.size());
            return regions.isEmpty() ? getDefaultRegions() : regions;
            
        } catch (Exception e) {
            log.warn("动态获取华为云region列表失败，使用默认列表", e);
            return getDefaultRegions();
        }
    }

    /**
     * 获取默认的华为云region列表（fallback方案）
     */
    private static List<HashMap<String, String>> getDefaultRegions() {
        List<HashMap<String, String>> defaultRegions = new ArrayList<>();
        
        // 提供几个常用的华为云region作为默认值
        String[] commonRegions = {
            "cn-north-1",    // 华北-北京一
            "cn-north-4",    // 华北-北京四  
            "cn-east-2",     // 华东-上海二
            "cn-east-3",     // 华东-上海一
            "cn-south-1",    // 华南-广州
            "ap-southeast-1" // 亚太-香港
        };
        
        for (String regionId : commonRegions) {
            HashMap<String, String> regionMap = new HashMap<>();
            regionMap.put("value", regionId);
            regionMap.put("label", regionId);
            defaultRegions.add(regionMap);
        }
        
        log.info("使用默认华为云region列表，共{}个region", defaultRegions.size());
        return defaultRegions;
    }
} 