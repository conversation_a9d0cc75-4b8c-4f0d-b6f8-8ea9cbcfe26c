package com.futong.gemini.plugin.cloud.huawei.sampler;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.common.CloudClient;
import com.futong.gemini.plugin.cloud.huawei.common.HuaWeiCloudDataTransfer;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.ecs.v2.model.ShowServerRequest;
import com.huaweicloud.sdk.ecs.v2.model.ShowServerResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 华为云数据刷新服务
 * 负责刷新华为云资源的状态信息
 */
@Slf4j
public class RefreshService extends BaseCloudService {

    /**
     * 刷新云主机信息
     */
    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        try {
            EcsClient client = CloudClient.client.client(EcsClient.class, request.getBody());
            
            // 从刷新配置中获取实例ID列表
            List<String> instanceIds = getInstanceIdsFromRefreshConfig(request);
            
            if (instanceIds == null || instanceIds.isEmpty()) {
                log.warn("刷新云主机信息失败：未找到实例ID");
                return BaseResponse.FAIL_OP.of("未找到需要刷新的实例ID");
            }
            
            List<JSONObject> refreshedInstances = new java.util.ArrayList<>();
            
            for (String instanceId : instanceIds) {
                try {
                    ShowServerRequest showRequest = new ShowServerRequest();
                    showRequest.setServerId(instanceId);
                    ShowServerResponse response = client.showServer(showRequest);
                    
                    JSONObject instance = HuaWeiCloudDataTransfer.convertInstance(response);
                    if (instance != null) {
                        refreshedInstances.add(instance);
                    }
                } catch (Exception e) {
                    log.warn("刷新实例 {} 信息失败: {}", instanceId, e.getMessage());
                }
            }
            
            if (!refreshedInstances.isEmpty()) {
                toMessageAndSend(request.getBody(), refreshedInstances, JSONObject.class);
            }
            
            return new BaseDataResponse<>(refreshedInstances);
        } catch (Exception e) {
            log.error("刷新华为云主机信息失败", e);
            return BaseResponse.FAIL_OP.of("刷新云主机信息失败: " + e.getMessage());
        }
    }

    /**
     * 从刷新配置中获取实例ID列表
     */
    private static List<String> getInstanceIdsFromRefreshConfig(BaseCloudRequest request) {
        try {
            JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
            if (refreshConfig != null) {
                Object data = refreshConfig.get("data");
                if (data instanceof List) {
                    return (List<String>) data;
                } else if (data instanceof String) {
                    return java.util.Arrays.asList((String) data);
                }
            }
        } catch (Exception e) {
            log.warn("解析刷新配置中的实例ID失败", e);
        }
        return null;
    }
} 