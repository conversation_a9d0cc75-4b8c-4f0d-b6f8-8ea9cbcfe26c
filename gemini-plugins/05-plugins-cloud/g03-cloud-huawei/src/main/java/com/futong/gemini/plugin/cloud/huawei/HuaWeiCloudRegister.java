package com.futong.gemini.plugin.cloud.huawei;

import com.futong.common.function.FTExecute;
import com.futong.gemini.plugin.cloud.huawei.sampler.FetchService;
import com.futong.gemini.plugin.cloud.huawei.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.huawei.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

/**
 * 华为云插件注册类
 * 负责注册所有华为云相关的操作类型
 */
@Slf4j
public class HuaWeiCloudRegister extends BaseCloudRegister {
    
    public <Q, R, C> Builder registerHuawei(ActionType actionType, FTExecute<Q, R, C> execute) {
        return register(actionType, execute, CloudService::toFTAction);
    }

    @Override
    public void load() {
        //资源操作加载
        onAfterLoadPlatform();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadCompute();//加载云主机操作
        onAfterLoadComputeImage();//加载镜像操作
        onAfterLoadComputeSecurityGroup();//加载云主机安全组操作
        onAfterLoadStorageDisk();//加载存储云硬盘操作
        onAfterLoadNeutronVpc();//加载网络VPC操作
    }

    public void onAfterLoadPlatform() {
        //平台账号
        registerBefore(CloudService::defaultRegion,
                ActionType.AUTH_PLATFORM_ACCOUNT,
                ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL,
                ActionType.CREATE_PLATFORM_FETCH_DISPATCH,
                ActionType.QUERY_PLATFORM_BILL_BALANCE);//认证云账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount);//认证云账号
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);//获取调度添加模型
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);//添加默认调度任务
        register(ActionType.QUERY_PLATFORM_BILL_BALANCE, PlatformService::queryBillBalance);//平台账单
    }

    public void onAfterLoadFetch() {
        registerBefore(CloudService::defaultPage50,
                ActionType.FETCH_PLATFORM_KEYPAIR,
                ActionType.FETCH_COMPUTE_SECURITYGROUP
        );//默认查询页码及条数50
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_STORAGE_IMAGE,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_NEUTRON_EIP
        );//默认查询页码及条数100
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion);//同步地域
        register(ActionType.FETCH_PLATFORM_AZONE, FetchService::fetchZone);//同步可用区
        // register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeyPair);//获取密钥对 - 暂时注释，待实现
        // register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm)//获取告警信息 - 暂时注释，待实现
        //         .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
        // register(ActionType.FETCH_PLATFORM_BILL, FetchService::fetchBill);//获取账单信息 - 暂时注释，待实现
        // register(ActionType.FETCH_PLATFORM_BILL_DAY, FetchService::fetchBillDay);//获取日账单信息 - 暂时注释，待实现
        // register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent)//获取事件 - 暂时注释，待实现
        //         .addBefore(CloudService::defaultStartEndTimeOneDay);//默认查询时间区间一天
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchEcs);//获取云主机
        // register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsMonitor);//获取云主机监控 - 暂时注释，待实现
        // register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor);//获取规格 - 暂时注释，待实现
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup);//获取安全组
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage);//获取镜像
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk);//获取磁盘
        register(ActionType.FETCH_NEUTRON_VPC, FetchService::fetchVpc);//获取VPC
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);//获取Subnet
        // register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip);//获取EIP - 暂时注释，待实现
    }

    public void onAfterLoadCompute() {
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)//刷新云主机
                .addTransferCloud("$.refreshConfig.data", "$.instanceIds", BaseUtils::formatStrArray);//从刷新配置中获取实例ID
        registerHuawei(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance);//创建云主机
        registerHuawei(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstances)//批量删除云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceId")//批量操作基于请求分割刷新任务
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")//基于请求设置刷新请求的cloud信息
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerHuawei(ActionType.UPDATE_COMPUTE_INSTANCE, ComputeInstanceService::updateInstance)//修改云主机
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.resourceName", "$.instanceName", false)
                .addTransferCloud("$.model.description", "$.description", false)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                .addSetRefreshData("request","$.body.cloud.instanceId")//单资源操作,直接设置刷新请求的data信息
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerHuawei(ActionType.UPDATE_COMPUTE_INSTANCE_FLAVOR, ComputeInstanceService::updateInstanceFlavor)//修改云主机规格
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 5000, 5000)
                .addSetRefreshData("request","$.body.cloud.instanceId")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
  /*      registerHuawei(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::batchStartServers)//批量开启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceId")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerHuawei(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::batchStopServers)//批量停止云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceId")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        registerHuawei(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::batchRebootServers)//批量重启云主机
                .addTransferCloud("$.cis.sourceJson.devopsLevel01Value", "$.regionId", BaseUtils::formatSingle)
                .addTransferCloud("$.cis.openId", "$.instanceId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(30, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceId")
                .addSetRefreshCloud("request","$.body.cloud.regionId","$.regionId")
                .addAfter(BaseCloudService::addRefreshGourdJob);*/
        registerHuawei(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, ComputeInstanceService::updateInstanceVnc)//修改云主机VNC登录密码
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.model.Password", "$.vncPassword");
    }

    public void onAfterLoadComputeImage() {
        // registerHuawei(ActionType.QUERY_COMPUTE_IMAGE, ImageService::queryImages);//查询镜像 - 已删除stub方法
 /*       registerHuawei(ActionType.CREATE_COMPUTE_IMAGE, ImageService::createImage);//创建自定义镜像
        registerHuawei(ActionType.UPDATE_COMPUTE_IMAGE, ImageService::updateImage)//修改自定义镜像
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId")
                .addTransferCloud("$.model.resourceName", "$.imageName", false)
                .addTransferCloud("$.model.description", "$.description", false);*/
        // registerHuawei(ActionType.COPY_COMPUTE_IMAGE, ImageService::copyImage)//复制一个地域下的自定义镜像到其他地域 - 已删除stub方法
        //         .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
        //         .addTransferCloud("$.ci.openId", "$.imageId");
        // registerHuawei(ActionType.EXPORT_COMPUTE_IMAGE, ImageService::exportImage)//导出一份自定义镜像到OBS - 已删除stub方法
        //         .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
        //         .addTransferCloud("$.ci.openId", "$.imageId");
/*        registerHuawei(ActionType.UPDATE_COMPUTE_IMAGE_PERMISSION, ImageService::updateImagePermission)//管理镜像共享权限
                .addTransferCloud("$.ci.sourceJson.devopsLevel01Value", "$.regionId")
                .addTransferCloud("$.ci.openId", "$.imageId");*/
    }

    public void onAfterLoadComputeSecurityGroup() {
       // registerHuawei(ActionType.CREATE_COMPUTE_SECURITYGROUP, SecurityGroupService::createSecurityGroup);//创建安全组
       // registerHuawei(ActionType.DELETE_COMPUTE_SECURITYGROUP, SecurityGroupService::deleteSecurityGroup);//删除安全组
       // registerHuawei(ActionType.UPDATE_COMPUTE_SECURITYGROUP, SecurityGroupService::updateSecurityGroup);//修改安全组
       // registerHuawei(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::createSecurityGroupRule);//创建安全组规则
//registerHuawei(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::deleteSecurityGroupRule);//删除安全组规则
    }

    public void onAfterLoadStorageDisk() {
        // registerHuawei(ActionType.CREATE_STORAGE_DISK, DiskService::createDisk);//创建磁盘 - 已删除stub方法
        // registerHuawei(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisk);//删除磁盘 - 已删除stub方法
        // registerHuawei(ActionType.UPDATE_STORAGE_DISK, DiskService::updateDisk);//修改磁盘 - 已删除stub方法
        // registerHuawei(ActionType.ATTACH_STORAGE_DISK, DiskService::attachDisk);//挂载磁盘 - 已删除stub方法
        // registerHuawei(ActionType.DETACH_STORAGE_DISK, DiskService::detachDisk);//卸载磁盘 - 已删除stub方法
    }

    public void onAfterLoadNeutronVpc() {
        //registerHuawei(ActionType.CREATE_NEUTRON_VPC, NetworkService::createVpc);//创建VPC
      //  registerHuawei(ActionType.DELETE_NEUTRON_VPC, NetworkService::deleteVpc);//删除VPC
        // registerHuawei(ActionType.UPDATE_NEUTRON_VPC, NetworkService::updateVpc);//修改VPC - 已删除stub方法
        // registerHuawei(ActionType.CREATE_NEUTRON_SUBNET, NetworkService::createSubnet);//创建子网 - 已删除stub方法
        // registerHuawei(ActionType.DELETE_NEUTRON_SUBNET, NetworkService::deleteSubnet);//删除子网 - 已删除stub方法
        // registerHuawei(ActionType.UPDATE_NEUTRON_SUBNET, NetworkService::updateSubnet);//修改子网 - 已删除stub方法
    }
} 