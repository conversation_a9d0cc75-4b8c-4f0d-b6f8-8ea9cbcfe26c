package com.futong.gemini.plugin.cloud.huawei.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.huawei.common.CloudClient;
import com.futong.gemini.plugin.cloud.huawei.common.HuaWeiCloudDataTransfer;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.ecs.v2.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 华为云计算实例服务类
 * 负责云服务器的管理操作
 */
@Slf4j
public class ComputeInstanceService {

    /**
     * 创建云服务器实例
     */
    public BaseResponse createInstance(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            CreateServersRequest createRequest = new CreateServersRequest();
            CreateServersRequestBody body = new CreateServersRequestBody();
            
            // 设置服务器配置
            PrePaidServer server = new PrePaidServer();
            
            // 设置基本参数
            if (request.getBody().containsKey("name")) {
                server.setName(request.getBody().getString("name"));
            }
            if (request.getBody().containsKey("flavorRef")) {
                server.setFlavorRef(request.getBody().getString("flavorRef"));
            }
            if (request.getBody().containsKey("imageRef")) {
                server.setImageRef(request.getBody().getString("imageRef"));
            }

            body.setServer(server);
            createRequest.setBody(body);

            // 调用API
            CreateServersResponse response = ecsClient.createServers(createRequest);

            if (response != null) {
                log.info("创建云服务器实例成功");
                JSONObject result = HuaWeiCloudDataTransfer.convertCreateServersResponse(response);
                return new BaseDataResponse<>(result);
            } else {
                return BaseResponse.FAIL_OP.of("创建云服务器实例失败");
            }
        } catch (Exception e) {
            log.error("创建云服务器实例异常", e);
            return BaseResponse.FAIL_OP.of("创建云服务器实例异常: " + e.getMessage());
        }
    }

    /**
     * 删除云服务器实例
     */
    public BaseResponse deleteInstances(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 获取服务器ID
            String serverId = request.getBody().getString("serverId");
            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 构建请求参数
            DeleteServersRequest deleteRequest = new DeleteServersRequest();
            DeleteServersRequestBody body = new DeleteServersRequestBody();
            
            // 设置服务器列表
            List<ServerId> serverList = new ArrayList<>();
            ServerId serverIdObj = new ServerId();
            serverIdObj.setId(serverId);
            serverList.add(serverIdObj);
            
            body.setServers(serverList);
            body.setDeletePublicip(false); // 是否删除弹性IP
            body.setDeleteVolume(false); // 是否删除磁盘
            deleteRequest.setBody(body);

            // 调用API
            DeleteServersResponse response = ecsClient.deleteServers(deleteRequest);

            log.info("删除云服务器实例成功，服务器ID: {}", serverId);
            JSONObject result = new JSONObject();
            result.put("serverId", serverId);
            result.put("cloudType", "huawei");
            result.put("status", "deleting");
            
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("删除云服务器实例异常", e);
            return BaseResponse.FAIL_OP.of("删除云服务器实例异常: " + e.getMessage());
        }
    }

    /**
     * 更新云服务器
     */
    public BaseResponse updateServer(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            UpdateServerRequest updateRequest = new UpdateServerRequest();
            updateRequest.setServerId(serverId);
            
            UpdateServerRequestBody body = new UpdateServerRequestBody();
            UpdateServerOption option = new UpdateServerOption();
            
            if (request.getBody().containsKey("name")) {
                option.setName(request.getBody().getString("name"));
            }
            if (request.getBody().containsKey("description")) {
                option.setDescription(request.getBody().getString("description"));
            }
            
            body.setServer(option);
            updateRequest.setBody(body);

            // 调用API
            UpdateServerResponse response = ecsClient.updateServer(updateRequest);

            if (response != null && response.getServer() != null) {
                log.info("更新云服务器成功，服务器ID: {}", serverId);
                return new BaseDataResponse<>(response.getServer());
            } else {
                log.warn("更新云服务器返回空结果");
                return BaseResponse.FAIL_OP.of("更新云服务器失败");
            }
        } catch (Exception e) {
            log.error("更新云服务器异常", e);
            return BaseResponse.FAIL_OP.of("更新云服务器异常: " + e.getMessage());
        }
    }

    /**
     * 更新云服务器
     */
    public BaseResponse updateInstance(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 获取服务器ID
            String serverId = request.getBody().getString("serverId");
            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 构建请求参数
            UpdateServerRequest updateRequest = new UpdateServerRequest();
            updateRequest.setServerId(serverId);
            
            UpdateServerRequestBody body = new UpdateServerRequestBody();
            UpdateServerOption server = new UpdateServerOption();
            
            // 设置更新参数
            if (request.getBody().containsKey("name")) {
                server.setName(request.getBody().getString("name"));
            }
            if (request.getBody().containsKey("description")) {
                server.setDescription(request.getBody().getString("description"));
            }

            body.setServer(server);
            updateRequest.setBody(body);

            // 调用API
            UpdateServerResponse response = ecsClient.updateServer(updateRequest);

            if (response != null && response.getServer() != null) {
                log.info("修改云服务器实例成功，服务器ID: {}", serverId);
                // UpdateServerResult中包含修改后的服务器信息，直接构建返回结果
                JSONObject result = new JSONObject();
                result.put("serverId", serverId);
                result.put("status", "updated");
                result.put("cloudType", "huawei");
                return new BaseDataResponse<>(result);
            } else {
                return BaseResponse.FAIL_OP.of("修改云服务器实例失败");
            }
        } catch (Exception e) {
            log.error("修改云服务器实例异常", e);
            return BaseResponse.FAIL_OP.of("修改云服务器实例异常: " + e.getMessage());
        }
    }

    /**
     * 变更云服务器规格
     */
    public BaseResponse resizeServer(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");
            String flavorRef = request.getBody().getString("flavorRef");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }
            if (flavorRef == null || flavorRef.isEmpty()) {
                return BaseResponse.FAIL_OP.of("规格ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // TODO: 华为云SDK 3.1.155版本中没有ResizeServerOption类，需要使用正确的API类名
            // 暂时返回未实现错误
            return BaseResponse.FAIL_OP.of("变更云服务器规格功能暂未实现，等待SDK版本更新");
        } catch (Exception e) {
            log.error("变更云服务器规格异常", e);
            return BaseResponse.FAIL_OP.of("变更云服务器规格异常: " + e.getMessage());
        }
    }

    /**
     * 变更云服务器规格
     */
    public BaseResponse updateInstanceFlavor(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 获取服务器ID
            String serverId = request.getBody().getString("serverId");
            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 获取新规格ID
            String flavorRef = request.getBody().getString("flavorRef");
            if (flavorRef == null || flavorRef.isEmpty()) {
                return BaseResponse.FAIL_OP.of("新规格ID不能为空");
            }

            // 构建请求参数
            ResizeServerRequest resizeRequest = new ResizeServerRequest();
            resizeRequest.setServerId(serverId);
            
            ResizeServerRequestBody body = new ResizeServerRequestBody();
            ResizePrePaidServerOption resize = new ResizePrePaidServerOption();
            resize.setFlavorRef(flavorRef);

            body.setResize(resize);
            resizeRequest.setBody(body);

            // 调用API
            ResizeServerResponse response = ecsClient.resizeServer(resizeRequest);

            log.info("修改云服务器规格成功，服务器ID: {}, 新规格: {}", serverId, flavorRef);
            JSONObject result = new JSONObject();
            result.put("serverId", serverId);
            result.put("flavorRef", flavorRef);
            result.put("cloudType", "huawei");
            result.put("status", "resizing");
            
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("修改云服务器规格异常", e);
            return BaseResponse.FAIL_OP.of("修改云服务器规格异常: " + e.getMessage());
        }
    }

    /**
     * 挂载云硬盘
     */
    public BaseResponse attachServerVolume(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");
            String volumeId = request.getBody().getString("volumeId");
            String device = request.getBody().getString("device");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }
            if (volumeId == null || volumeId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("云硬盘ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            AttachServerVolumeRequest attachRequest = new AttachServerVolumeRequest();
            attachRequest.setServerId(serverId);
            
            AttachServerVolumeRequestBody body = new AttachServerVolumeRequestBody();
            AttachServerVolumeOption option = new AttachServerVolumeOption();
            option.setVolumeId(volumeId);
            if (device != null && !device.isEmpty()) {
                option.setDevice(device);
            }
            body.setVolumeAttachment(option);
            attachRequest.setBody(body);

            // 调用API
            AttachServerVolumeResponse response = ecsClient.attachServerVolume(attachRequest);

            if (response != null && response.getJobId() != null) {
                log.info("挂载云硬盘成功，服务器ID: {}, 云硬盘ID: {}, 任务ID: {}", serverId, volumeId, response.getJobId());
                return new BaseDataResponse<>(response);
            } else {
                log.warn("挂载云硬盘返回空结果");
                return BaseResponse.FAIL_OP.of("挂载云硬盘失败");
            }
        } catch (Exception e) {
            log.error("挂载云硬盘异常", e);
            return BaseResponse.FAIL_OP.of("挂载云硬盘异常: " + e.getMessage());
        }
    }

    /**
     * 卸载云硬盘
     */
    public BaseResponse detachServerVolume(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");
            String volumeId = request.getBody().getString("volumeId");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }
            if (volumeId == null || volumeId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("云硬盘ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            DetachServerVolumeRequest detachRequest = new DetachServerVolumeRequest();
            detachRequest.setServerId(serverId);
            detachRequest.setVolumeId(volumeId);

            // 调用API
            DetachServerVolumeResponse response = ecsClient.detachServerVolume(detachRequest);

            if (response != null && response.getJobId() != null) {
                log.info("卸载云硬盘成功，服务器ID: {}, 云硬盘ID: {}, 任务ID: {}", serverId, volumeId, response.getJobId());
                return new BaseDataResponse<>(response);
            } else {
                log.warn("卸载云硬盘返回空结果");
                return BaseResponse.FAIL_OP.of("卸载云硬盘失败");
            }
        } catch (Exception e) {
            log.error("卸载云硬盘异常", e);
            return BaseResponse.FAIL_OP.of("卸载云硬盘异常: " + e.getMessage());
        }
    }

    /**
     * 添加安全组
     */
    public BaseResponse novaAssociateSecurityGroup(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");
            String securityGroupId = request.getBody().getString("securityGroupId");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }
            if (securityGroupId == null || securityGroupId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("安全组ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            NovaAssociateSecurityGroupRequest associateRequest = new NovaAssociateSecurityGroupRequest();
            associateRequest.setServerId(serverId);
            
            NovaAssociateSecurityGroupRequestBody body = new NovaAssociateSecurityGroupRequestBody();
            NovaAddSecurityGroupOption option = new NovaAddSecurityGroupOption();
            option.setName(securityGroupId);
            body.setAddSecurityGroup(option);
            associateRequest.setBody(body);

            // 调用API
            NovaAssociateSecurityGroupResponse response = ecsClient.novaAssociateSecurityGroup(associateRequest);

            if (response != null) {
                log.info("添加安全组成功，服务器ID: {}, 安全组ID: {}", serverId, securityGroupId);
                return new BaseDataResponse<>(response);
            } else {
                log.warn("添加安全组返回空结果");
                return BaseResponse.FAIL_OP.of("添加安全组失败");
            }
        } catch (Exception e) {
            log.error("添加安全组异常", e);
            return BaseResponse.FAIL_OP.of("添加安全组异常: " + e.getMessage());
        }
    }

    /**
     * 移除安全组
     */
    public BaseResponse novaDisassociateSecurityGroup(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");
            String securityGroupId = request.getBody().getString("securityGroupId");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }
            if (securityGroupId == null || securityGroupId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("安全组ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            NovaDisassociateSecurityGroupRequest disassociateRequest = new NovaDisassociateSecurityGroupRequest();
            disassociateRequest.setServerId(serverId);
            
            NovaDisassociateSecurityGroupRequestBody body = new NovaDisassociateSecurityGroupRequestBody();
            NovaRemoveSecurityGroupOption option = new NovaRemoveSecurityGroupOption();
            option.setName(securityGroupId);
            body.setRemoveSecurityGroup(option);
            disassociateRequest.setBody(body);

            // 调用API
            NovaDisassociateSecurityGroupResponse response = ecsClient.novaDisassociateSecurityGroup(disassociateRequest);

            if (response != null) {
                log.info("移除安全组成功，服务器ID: {}, 安全组ID: {}", serverId, securityGroupId);
                return new BaseDataResponse<>(response);
            } else {
                log.warn("移除安全组返回空结果");
                return BaseResponse.FAIL_OP.of("移除安全组失败");
            }
        } catch (Exception e) {
            log.error("移除安全组异常", e);
            return BaseResponse.FAIL_OP.of("移除安全组异常: " + e.getMessage());
        }
    }

    /**
     * 获取远程登录地址
     */
    public BaseResponse showServerRemoteConsole(BaseCloudRequest request) {
        try {
            // 获取认证信息
            String accessKeyId = request.getBody().getAccess().getUsername();
            String accessKeySecret = request.getBody().getAccess().getPassword();
            String regionId = request.getBody().getJSONObject("cloud").getString("regionId");
            String serverId = request.getBody().getString("serverId");

            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数
            ShowServerRemoteConsoleRequest consoleRequest = new ShowServerRemoteConsoleRequest();
            consoleRequest.setServerId(serverId);
            
            ShowServerRemoteConsoleRequestBody body = new ShowServerRemoteConsoleRequestBody();
            GetServerRemoteConsoleOption option = new GetServerRemoteConsoleOption();
            option.setProtocol(GetServerRemoteConsoleOption.ProtocolEnum.VNC);
            body.setRemoteConsole(option);
            consoleRequest.setBody(body);

            // 调用API
            ShowServerRemoteConsoleResponse response = ecsClient.showServerRemoteConsole(consoleRequest);

            if (response != null && response.getRemoteConsole() != null) {
                log.info("获取远程登录地址成功，服务器ID: {}", serverId);
                return new BaseDataResponse<>(response.getRemoteConsole());
            } else {
                log.warn("获取远程登录地址返回空结果");
                return BaseResponse.FAIL_OP.of("获取远程登录地址失败");
            }
        } catch (Exception e) {
            log.error("获取远程登录地址异常", e);
            return BaseResponse.FAIL_OP.of("获取远程登录地址异常: " + e.getMessage());
        }
    }

    /**
     * 获取远程登录地址
     */
    public BaseResponse updateInstanceVnc(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 获取服务器ID
            String serverId = request.getBody().getString("serverId");
            if (serverId == null || serverId.isEmpty()) {
                return BaseResponse.FAIL_OP.of("服务器ID不能为空");
            }

            // 获取VNC密码
            String vncPassword = request.getBody().getString("vncPassword");
            if (vncPassword == null || vncPassword.isEmpty()) {
                return BaseResponse.FAIL_OP.of("VNC密码不能为空");
            }

            // TODO: 华为云SDK 3.1.155版本中VNC密码相关API类名需要确认
            // 暂时返回未实现错误，等待确认正确的API类名
            log.warn("修改VNC密码功能暂未实现，服务器ID: {}", serverId);
            JSONObject result = new JSONObject();
            result.put("serverId", serverId);
            result.put("cloudType", "huawei");
            result.put("message", "VNC密码修改功能暂未实现，等待SDK版本确认");
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("修改云服务器VNC登录密码异常", e);
            return BaseResponse.FAIL_OP.of("修改云服务器VNC登录密码异常: " + e.getMessage());
        }
    }

    /**
     * 查询云服务器总数统计
     */
    public BaseResponse queryInstanceTotal(BaseCloudRequest request) {
        try {
            // 创建ECS客户端
            EcsClient ecsClient = CloudClient.client.client(EcsClient.class, request.getBody());

            // 构建请求参数，查询所有实例
            ListServersDetailsRequest listRequest = new ListServersDetailsRequest();
            listRequest.setLimit(1); // 只需要获取总数，设置limit为1

            // 调用API
            ListServersDetailsResponse response = ecsClient.listServersDetails(listRequest);

            if (response != null) {
                // 华为云API可能不直接返回总数，这里需要根据实际API响应进行调整
                int total = response.getServers() != null ? response.getServers().size() : 0;
                log.info("查询云服务器总数成功，共{}个实例", total);
                
                JSONObject result = new JSONObject();
                result.put("total", total);
                result.put("cloudType", "huawei");
                
                return new BaseDataResponse<>(result);
            } else {
                log.warn("查询云服务器总数返回空结果");
                return new BaseDataResponse<>(0);
            }
        } catch (Exception e) {
            log.error("查询云服务器总数异常", e);
            return BaseResponse.FAIL_OP.of("查询云服务器总数异常: " + e.getMessage());
        }
    }


} 