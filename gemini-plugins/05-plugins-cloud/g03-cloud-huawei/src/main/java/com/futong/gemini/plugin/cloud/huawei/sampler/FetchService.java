package com.futong.gemini.plugin.cloud.huawei.sampler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.plugin.cloud.huawei.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.huaweicloud.sdk.ecs.v2.EcsClient;
import com.huaweicloud.sdk.evs.v2.EvsClient;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.ims.v2.ImsClient;
import com.huaweicloud.sdk.vpc.v2.VpcClient;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * 华为云数据获取服务
 * 使用BaseCloudService.fetch标准模式，支持分页拆分任务
 */
@Slf4j
public class FetchService {

    /**
     * 华为云分页拆分响应处理
     * 华为云使用marker+limit分页模式，根据返回的marker继续查询下一页
     */
    public static BaseResponse toHuaweiPageGourdResponse(BaseCloudRequest request, BaseResponse response, 
                                                        String nextMarker, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) return response;
        
        // 如果有下一页marker，创建子任务
        if (nextMarker != null && !nextMarker.isEmpty()) {
            response.of("。云上数据分页查询中，继续获取下一页数据");
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("marker", nextMarker);
            request.getBody().getCloud().put("limit", pageSize);
            jobInfo.setRequest(request.cloneJSONObject());
            return BaseCloudService.toGourdResponse(response, CollUtil.newArrayList(jobInfo), (JobInfo t) -> t);
        }
        return response;
    }

    /**
     * 获取华为云地域信息
     */
    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        Map<Class, List> map = BaseCloudService.fetch(request,
                CloudClient.client,
                IamClient::keystoneListRegions,
                Convert::convertRegion);
        BaseResponse response = BaseCloudService.fetchSend(request, map);
        if (CollUtil.isEmpty(map.get(TmdbDevops.class))) {
            return response;
        }
        return BaseCloudService.toGourdResponse(response, map.get(TmdbDevops.class), (TmdbDevops t) -> {
            JobInfo jobInfo = new JobInfo();
            request.setAction(ActionType.FETCH_PLATFORM_AZONE);
            request.getBody().getCloud().put("regionId", t.getDevops_value());
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    /**
     * 获取华为云可用区信息
     */
    public static BaseResponse fetchZone(BaseCloudRequest request) {
        return BaseCloudService.fetchAndSend(request,
                CloudClient.client,
                EcsClient::novaListAvailabilityZones,
                Convert::convertZone);
    }

    /**
     * 获取华为云云主机信息 - 支持分页拆分
     */
    public static BaseResponse fetchEcs(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    EcsClient::listServersDetails,
                    Convert::convertServer);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云ECS分页处理 - 检查响应中是否有下一页标记
            String nextMarker = extractNextMarker(response, "servers_links");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云主机信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云主机信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云云盘信息 - 支持分页拆分
     */
    public static BaseResponse fetchDisk(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    EvsClient::listVolumes,
                    Convert::convertVolume);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云EVS分页处理
            String nextMarker = extractNextMarker(response, "volumes_links");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云盘信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云盘信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云镜像信息 - 支持分页拆分
     */
    public static BaseResponse fetchImage(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    ImsClient::glanceListImages,
                    Convert::convertImage);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云IMS分页处理
            String nextMarker = extractNextMarker(response, "next");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云镜像信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云镜像信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云VPC信息 - 支持分页拆分
     */
    public static BaseResponse fetchVpc(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    VpcClient::listVpcs,
                    Convert::convertVpc);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云VPC分页处理 - VPC通常数量较少，一般不需要分页
            // 但为了统一，仍然检查分页信息
            String nextMarker = extractNextMarker(response, "links");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云VPC信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云VPC信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云子网信息 - 支持分页拆分
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    VpcClient::listSubnets,
                    Convert::convertSubnet);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云Subnet分页处理
            String nextMarker = extractNextMarker(response, "links");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云子网信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云子网信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云安全组信息 - 支持分页拆分
     */
    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request) {
        try {
            // 设置默认分页参数
            if (!request.getBody().getCloud().containsKey("limit")) {
                request.getBody().getCloud().put("limit", 100);
            }
            
            Map<Class, List> map = BaseCloudService.fetch(request,
                    CloudClient.client,
                    VpcClient::listSecurityGroups,
                    Convert::convertSecurityGroup);
            BaseResponse response = BaseCloudService.fetchSend(request, map);
            
            // 华为云SecurityGroup分页处理
            String nextMarker = extractNextMarker(response, "links");
            if (nextMarker != null) {
                int limit = request.getBody().getCloud().getIntValue("limit");
                return toHuaweiPageGourdResponse(request, response, nextMarker, limit);
            }
            
            return response;
        } catch (Exception e) {
            log.error("获取华为云安全组信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云安全组信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取华为云密钥对信息
     * 注意: 华为云SDK 3.1.155版本中密钥对API不兼容，临时禁用此功能
     */
    public static BaseResponse fetchKeyPair(BaseCloudRequest request) {
        try {
            log.warn("华为云SDK 3.1.155版本中密钥对API不兼容，暂不支持查询密钥对功能");
            return BaseResponse.SUCCESS.of("华为云SDK版本暂不支持密钥对查询功能");
        } catch (Exception e) {
            log.error("获取华为云密钥对信息失败", e);
            return BaseResponse.FAIL_OP.of("获取华为云密钥对信息失败: " + e.getMessage());
        }
    }

    /**
     * 从响应中提取下一页标记
     * 华为云分页响应通常在links字段中包含next链接
     * 
     * @param response 华为云API响应对象
     * @param linksField links字段名称，如"servers_links"、"volumes_links"等
     * @return 下一页的marker参数，如果没有下一页则返回null
     */
    private static String extractNextMarker(BaseResponse response, String linksField) {
        try {
            // 使用反射获取响应的HttpResponse内容
            Field httpResponseField = response.getClass().getDeclaredField("httpResponse");
            httpResponseField.setAccessible(true);
            Object httpResponseObj = httpResponseField.get(response);
            
            if (httpResponseObj == null) {
                log.debug("响应中未找到httpResponse字段");
                return null;
            }
            
            // 获取响应体内容
            String responseBody = null;
            if (httpResponseObj.getClass().getName().contains("HttpResponse")) {
                Field bodyField = httpResponseObj.getClass().getDeclaredField("body");
                bodyField.setAccessible(true);
                Object bodyObj = bodyField.get(httpResponseObj);
                if (bodyObj != null) {
                    responseBody = bodyObj.toString();
                }
            }
            
            if (responseBody == null || responseBody.isEmpty()) {
                log.debug("响应体为空，无法提取分页信息");
                return null;
            }
            
            // 解析JSON响应体
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);
            if (!jsonResponse.containsKey(linksField)) {
                log.debug("响应中未找到分页字段: " + linksField);
                return null;
            }
            
            // 获取links数组
            JSONArray linksArray = jsonResponse.getJSONArray(linksField);
            if (linksArray == null || linksArray.isEmpty()) {
                log.debug("分页字段为空: " + linksField);
                return null;
            }
            
            // 查找rel为"next"的链接
            for (int i = 0; i < linksArray.size(); i++) {
                JSONObject linkObj = linksArray.getJSONObject(i);
                if (linkObj != null && "next".equals(linkObj.getString("rel"))) {
                    String nextUrl = linkObj.getString("href");
                    if (nextUrl != null && !nextUrl.isEmpty()) {
                        String marker = extractMarkerFromUrl(nextUrl);
                        log.debug("成功提取下一页marker: " + marker);
                        return marker;
                    }
                }
            }
            
            log.debug("在分页链接中未找到next链接");
            return null;
            
        } catch (Exception e) {
            log.warn("提取分页标记失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从URL中提取marker参数
     */
    private static String extractMarkerFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        try {
            String[] parts = url.split("[?&]");
            for (String part : parts) {
                if (part.startsWith("marker=")) {
                    return part.substring("marker=".length());
                }
            }
        } catch (Exception e) {
            log.debug("从URL提取marker失败: " + e.getMessage());
        }
        return null;
    }
} 