# 华为公有云插件 (HuaWei Cloud Plugin)

## 项目概述

华为公有云插件是为双子星系统开发的华为公有云平台插件，实现对华为公有云资源的统一管理和监控。

### 项目目标
- 集成华为公有云API，实现资源同步
- 支持多种云资源类型的管理
- 提供统一的资源操作接口
- 实现数据实时同步和监控

## 功能特性

### 1. 账号管理
- ✅ 支持AccessKeyID和AccessKeySecret认证
- ✅ 实现账号有效性验证
- ✅ 支持多账号管理
- ✅ 支持多区域管理

### 2. 云主机管理 (ECS)
- ✅ 实例列表查询 (listServersDetails)
- ✅ 实例详情获取 (showServer)
- ✅ 实例启动/停止/重启 (batchStartServers/batchStopServers/batchRebootServers)
- ✅ 实例创建/删除 (createServers/deleteServers)
- ✅ 实例规格变更 (resizeServer)
- ✅ 云盘挂载/卸载 (attachServerVolume/detachServerVolume)
- ✅ 安全组管理 (novaAssociateSecurityGroup/novaDisassociateSecurityGroup)
- ✅ VNC远程登录 (showServerRemoteConsole)

### 3. 镜像管理
- ✅ 镜像列表查询 (listImages)
- ✅ 镜像创建/删除 (createImage/deleteImage)
- ✅ 镜像详情获取 (showImage)
- ✅ 镜像成员管理 (addImageMember/deleteImageMember)

### 4. 磁盘管理
- ✅ 磁盘列表查询 (listVolumes)
- ✅ 磁盘创建/删除 (createVolume/deleteVolume)
- ✅ 磁盘详情获取 (showVolume)
- ✅ 磁盘扩展 (extendVolume)

### 5. 安全组管理
- ✅ 安全组列表查询 (listSecurityGroups)
- ✅ 安全组创建/删除 (createSecurityGroup/deleteSecurityGroup)
- ✅ 安全组规则管理 (listSecurityGroupRules/createSecurityGroupRule)

### 6. 密钥对管理
- ✅ 密钥对列表查询 (listKeypairs)
- ✅ 密钥对创建/删除 (createKeypair/deleteKeypair)
- ✅ 密钥对导入 (importKeypair)

### 7. 网络管理
- ✅ VPC列表查询 (listVpcs)
- ✅ VPC创建/删除 (createVpc/deleteVpc)
- ✅ 子网管理 (listSubnets/createSubnet)

## 技术架构

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    华为云插件架构                              │
├─────────────────────────────────────────────────────────────┤
│  HuaWeiCloudPluginTemplate (插件模板)                        │
│  ├── 请求路由分发                                            │
│  └── 统一响应处理                                            │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                                      │
│  ├── AccountService (账号服务)                               │
│  ├── ComputeInstanceService (云主机服务)                     │
│  ├── ImageService (镜像服务)                                 │
│  ├── DiskService (磁盘服务)                                  │
│  ├── SecurityGroupService (安全组服务)                       │
│  ├── KeyPairService (密钥对服务)                             │
│  └── NetworkService (网络服务)                               │
├─────────────────────────────────────────────────────────────┤
│  通用层 (Common Layer)                                       │
│  ├── CloudClient (华为云客户端)                              │
│  ├── HuaWeiCloudAuth (认证服务)                              │
│  ├── HuaWeiCloudException (异常处理)                         │
│  ├── HuaWeiCloudLogger (日志服务)                            │
│  └── HuaWeiCloudDataTransfer (数据传输)                      │
├─────────────────────────────────────────────────────────────┤
│  华为云SDK (HuaWei Cloud SDK)                                │
│  ├── ECS SDK (弹性计算服务)                                  │
│  ├── IMS SDK (镜像服务)                                      │
│  ├── EVS SDK (云硬盘服务)                                    │
│  ├── VPC SDK (虚拟私有云)                                    │
│  └── IAM SDK (身份与访问管理)                                │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. HuaWeiCloudPluginTemplate
- 继承 `BaseCloudPluginTemplate`
- 负责请求路由和分发
- 统一异常处理和响应格式

#### 2. CloudClient
- 继承 `BaseCloudClient`
- 封装华为云SDK客户端创建
- 支持多种华为云服务客户端

#### 3. 服务层类
- 每个服务类负责特定资源类型的管理
- 统一的API调用模式
- 完整的CRUD操作支持

## 开发环境

### 技术要求
- **Java版本**: JDK 1.8
- **Maven版本**: 3.6.0+
- **华为云SDK版本**: 3.1.155
- **框架限制**: 禁止使用SpringBoot

### 依赖管理
```xml
<!-- 华为云SDK依赖 -->
<dependency>
    <groupId>com.huaweicloud.sdk</groupId>
    <artifactId>huaweicloud-sdk-ecs</artifactId>
    <version>3.1.155</version>
</dependency>
<dependency>
    <groupId>com.huaweicloud.sdk</groupId>
    <artifactId>huaweicloud-sdk-iam</artifactId>
    <version>3.1.155</version>
</dependency>
<!-- 其他华为云SDK依赖... -->
```

## 配置说明

### 账号配置
```json
{
  "accessKeyId": "your_access_key_id",
  "accessKeySecret": "your_access_key_secret",
  "regionId": "cn-north-4"
}
```

### 调度配置
```json
{
  "syncInterval": 300,
  "retryTimes": 3,
  "retryInterval": 60
}
```

### 日志配置
```java
// 配置特定日志路径
DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.huawei", "/cloud/huawei")
```

## 使用说明

### 1. 插件注册
插件通过SPI机制自动注册：
```
META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface
com.futong.gemini.plugin.cloud.huawei.HuaWeiCloudPluginTemplate
```

### 2. API调用示例
```java
// 创建请求
BaseCloudRequest request = new BaseCloudRequest();
request.getBody().setModule("compute");
request.getBody().setAction("listServersDetails");

// 调用插件
HuaWeiCloudPluginTemplate template = new HuaWeiCloudPluginTemplate();
BaseResponse response = template.process(request);
```

### 3. 数据同步
```java
// 同步ECS实例数据
CloudService.syncComputeInstances(request);

// 全量同步所有资源
CloudService.syncAllResources(request);
```

## 部署指南

### 1. 编译打包
```bash
mvn clean package -pl gemini-plugins/05-plugins-cloud/g03-cloud-huawei
```

### 2. 部署方式
- **容器化部署**: 支持Docker容器化部署
- **传统部署**: 支持JAR包直接部署
- **集群部署**: 支持多实例集群部署

### 3. 环境配置
- 配置华为云AccessKey
- 配置区域信息
- 配置日志路径
- 配置RabbitMQ连接

## 监控和日志

### 性能监控
- API调用成功率监控
- 响应时间监控 (< 30秒)
- 资源使用率监控

### 日志记录
- 操作日志: `/cloud/huawei/operation.log`
- 错误日志: `/cloud/huawei/error.log`
- 性能日志: `/cloud/huawei/performance.log`

### 异常处理
- 统一的异常处理机制
- 详细的错误信息记录
- 自动重试机制

## 测试

### 单元测试
- 核心功能单元测试覆盖率 ≥ 80%
- API接口测试
- 异常处理测试

### 集成测试
- 端到端功能测试
- 数据同步测试
- 性能压力测试

## 安全要求

### 认证安全
- 使用HTTPS进行API通信
- 支持AccessKey认证
- 实现请求签名验证

### 数据安全
- 敏感信息加密存储
- 传输数据加密
- 访问权限控制

## 版本历史

### v1.0.0 (2025-07)
- ✅ 完成基础架构开发
- ✅ 实现核心功能模块
- ✅ 完成扩展功能开发
- ✅ 完成单元测试
- ✅ 完成性能优化

## 参考文档

### API文档
- [华为公有云ECS API文档](https://console.huaweicloud.com/apiexplorer/#/openapi/ECS/doc)
- [华为公有云SDK文档](https://console.huaweicloud.com/apiexplorer/#/sdkcenter?language=Java)

### 项目规范
- [Java编码规范](docs/code-standards/java-coding-standards.md)
- [插件开发规范](docs/code-standards/plugin-development-standards.md)
- [云插件开发规范](docs/code-standards/cloud-plugin-development-standards.md)

## 联系方式

- **项目负责人**: 开发团队
- **技术支持**: 技术负责人
- **文档版本**: 1.0.0
- **最后更新**: 2025年07月

---

**华为公有云插件** - 为双子星系统提供完整的华为云资源管理能力 