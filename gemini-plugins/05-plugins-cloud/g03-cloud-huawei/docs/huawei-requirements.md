# 华为公有云云插件需求文档

## 此文档变量说明
- {XXXCloud} 的实际值为 HuaWeiCloud
## 华为公有云插件开发指南

### 参考实现
主要参考阿里云插件 [gemini-plugins/05-plugins-cloud/g01-cloud-ali/](mdc:gemini-plugins/05-plugins-cloud/g01-cloud-ali) 的实现：
- **CloudClient**: [gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/common/](mdc:gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/common) 目录下的各种Client类
- **注册类**: [gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/AliCloudRegister.java](mdc:gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/AliCloudRegister.java)
- **服务层**: [gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/service/](mdc:gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/service) 目录下的各种服务类
- **模板类**: [gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/AliCloudPluginTemplate.java](mdc:gemini-plugins/05-plugins-cloud/g01-cloud-ali/src/main/java/com/futong/gemini/plugin/cloud/ali/AliCloudPluginTemplate.java)

## 项目概述

### 项目名称
华为公有云插件 (huawei Cloud Plugin)

### 项目描述
为双子星系统开发华为公有云平台插件，实现对华为公有云资源的统一管理和监控。

### 项目目标
- 集成华为公有云API，实现资源同步
- 支持多种云资源类型的管理
- 提供统一的资源操作接口
- 实现数据实时同步和监控

## 功能需求

### 1. 账号管理
- **功能描述**: 支持华为公有云账号的添加、验证和管理
- **技术要求**: 
  - 支持AccessKeyID和AccessKeySecret认证
  - 实现账号有效性验证
  - 支持多账号管理
- **API接口**: 
  - 账号添加接口
  - 账号验证接口
  - 账号列表查询接口

### 2. 云主机管理 (ECS)
- **功能描述**: 管理华为公有云弹性计算服务(ECS)实例
- **支持操作**:
  - 实例列表查询
  - 实例详情获取
  - 实例启动/停止/重启
  - 实例规格查询
  - 实例删除
- **API接口**:
  - `listServersDetails` - 查询实例列表
  - `showServer` - 获取实例详情
  - `batchStartServers` - 启动实例
  - `batchStopServers` - 停止实例
  - `batchRebootServers` - 重启实例
  - `deleteServers` - 删除实例
  - `ListFlavorSpecs` - 查询规格列表
  - `createServers` - 创建实例
  - `updateServer` - 修改虚拟机
  - `resizeServer` - 变更规格
  - `attachServerVolume` - 挂载云盘
  - `detachServerVolume` - 卸载硬盘
  - `novaDisassociateSecurityGroup` - 移除安全组
  - `novaAssociateSecurityGroup` - 添加安全组
  - `showServerRemoteConsole` - VNC远程调用
### 3. 镜像管理
- **功能描述**: 管理系统镜像和自定义镜像
- **支持操作**:
  - 镜像列表查询
  - 镜像详情获取
  - 镜像类型分类
- **API接口**:
  - `listImages` - 查询镜像列表
  - `createImage` - 创建镜像

### 4. 云盘管理
- **功能描述**: 管理云硬盘资源
- **支持操作**:
  - 磁盘列表查询
  - 磁盘详情获取
  - 磁盘状态监控
- **API接口**:
  - `listVolumes` - 查询云盘列表
  - `createVolume` - 创建云盘

### 5. 安全组管理
- **功能描述**: 管理网络安全组
- **支持操作**:
  - 安全组列表查询
  - 安全组详情获取
  - 安全组规则查看
- **API接口**:
  - `listSecurityGroups` - 查询安全组列表
  - `createSecurityGroup` - 创建安全组
  - `listSecurityGroupRules` - 查询安全组规则
## 技术需求

### 1. 开发环境

- **Java版本**: JDK 1.8
- **Maven版本**: 3.6.0+
- **华为公有云SDK**: 3.1.155
- **框架限制**: 禁止使用SpringBoot
- **依赖**: [gemini-plugins/05-plugins-cloud/g03-cloud-huawei/pom.xml](mdc:gemini-plugins/05-plugins-cloud/g03-cloud-huawei/pom.xml) 中的 SDK版本需要为3.1.155版本，禁止修改除此POM文件之外的其他POM文件

### 2. 架构要求
- **插件架构**: 采用标准插件架构
- **服务注册**: 使用SPI机制进行服务注册
- **异常处理**: 统一的异常处理机制
- **日志记录**: 使用项目统一日志框架

### 3. 数据同步
- **同步方式**: 定时任务 + 事件触发
- **数据格式**: 统一的数据模型格式
- **消息队列**: 集成RabbitMQ进行数据传递
- **重试机制**: 支持失败重试和错误恢复

### 4. 性能要求
- **响应时间**: API调用响应时间 < 30秒
- **并发处理**: 支持多线程并发处理
- **内存使用**: 合理控制内存占用
- **连接池**: 使用连接池管理API连接

## 配置需求

### 1. 账号配置
```json
{
  "accessKeyId": "your_access_key_id",
  "accessKeySecret": "your_access_key_secret"
}
```

### 2. 调度配置
```json
{
  "syncInterval": 300,
  "retryTimes": 3,
  "retryInterval": 60
}
```

### 3. 表单配置
- **中文表单**: `add_form_zh-CN.json`
- **英文表单**: `add_form_en-US.json`
- **调度配置**: `add_dispatch.json`

## 安全需求

### 1. 认证安全
- 使用HTTPS进行API通信
- 支持AccessKey认证
- 实现请求签名验证

### 2. 数据安全
- 敏感信息加密存储
- 传输数据加密
- 访问权限控制

### 3. 操作安全
- 操作日志记录
- 异常操作告警
- 权限验证机制

## 监控需求

### 1. 性能监控
- API调用成功率监控
- 响应时间监控
- 资源使用率监控

### 2. 业务监控
- 资源同步状态监控
- 异常事件监控
- 数据一致性监控

### 3. 日志监控
- 操作日志记录
- 错误日志告警
- 性能日志分析

## 测试需求

### 1. 单元测试
- 核心功能单元测试
- API接口测试
- 异常处理测试

### 2. 集成测试
- 端到端功能测试
- 数据同步测试
- 性能压力测试

### 3. 验收测试
- 功能完整性测试
- 性能指标测试
- 安全合规测试

## 部署需求

### 1. 打包要求
- 生成轻量级JAR包
- 包含所有依赖
- 支持热部署

### 2. 部署方式
- 支持容器化部署
- 支持传统部署
- 支持集群部署

### 3. 配置管理
- 支持环境变量配置
- 支持配置文件配置
- 支持动态配置更新

## 文档需求

### 1. 技术文档
- API接口文档
- 架构设计文档
- 部署指南文档

### 2. 用户文档
- 使用说明文档
- 配置指南文档
- 故障排除文档

### 3. 运维文档
- 监控配置文档
- 日志分析文档
- 性能调优文档

## 验收标准

### 1. 功能验收
- [ ] 所有功能需求实现完成
- [ ] 所有API接口正常工作
- [ ] 数据同步功能正常
- [ ] 异常处理机制完善

### 2. 性能验收
- [ ] 响应时间满足要求
- [ ] 并发处理能力达标
- [ ] 内存使用合理
- [ ] 资源占用可控

### 3. 质量验收
- [ ] 代码质量达标
- [ ] 测试覆盖率 ≥ 80%
- [ ] 文档完整性 100%
- [ ] 安全合规性通过

## 项目计划

### 阶段一：基础架构 (1周)
- [] 项目环境搭建
- [] 基础类实现
- [] 客户端封装

### 阶段二：核心功能 (2周)
- [] 云主机管理功能
- [] 账号管理功能
- [] 数据同步功能

### 阶段三：扩展功能 (1周)
- [] 镜像管理功能
- [] 磁盘管理功能
- [] 密钥对管理功能

### 阶段四：测试优化 (1周)
- [] 单元测试编写
- [] 集成测试执行
- [] 性能优化调整

### 阶段五：部署上线 (1周)
- [] 文档完善
- [] 部署配置
- [] 上线验证

## 风险评估

### 1. 技术风险
- **百度云SDK兼容性**: 需要验证SDK版本兼容性
- **API限制**: 需要了解API调用频率限制
- **数据格式**: 需要确保数据格式转换正确

### 2. 项目风险
- **时间风险**: 开发周期可能延长
- **质量风险**: 代码质量可能不达标
- **集成风险**: 与现有系统集成可能有问题

### 3. 运维风险
- **监控风险**: 监控覆盖可能不全面
- **安全风险**: 可能存在安全漏洞
- **性能风险**: 性能可能不满足要求

## 附录

### 1. 华为公有云API文档
- [华为公有云ECS API文档](https://console.huaweicloud.com/apiexplorer/#/openapi/ECS/doc)
- [华为公有云SDK文档](https://console.huaweicloud.com/apiexplorer/#/sdkcenter?language=Java)
- [华为公有云SDK使用说明文档](https://github.com/huaweicloud/huaweicloud-sdk-java-v3/blob/master/README_CN.md)
### 2. 项目规范
- [Java编码规范](docs/code-standards/java-coding-standards.md)
- [插件开发规范](docs/code-standards/plugin-development-standards.md)
- [云插件开发规范](docs/code-standards/cloud-plugin-development-standards.md)

### 3. 相关文档
- [项目架构文档](docs/architecture/overview.md)
- [部署指南文档](docs/architecture/deployment-guide.md)
- [API接口文档](docs/api/cloud-plugin-api.md)

---
### API 参考文档
- [华为公有云ECS API文档](https://console.huaweicloud.com/apiexplorer/#/openapi/ECS/doc)
- [华为公有云SDK文档](https://console.huaweicloud.com/apiexplorer/#/sdkcenter?language=Java)
- [华为公有云SDK使用说明文档](https://github.com/huaweicloud/huaweicloud-sdk-java-v3/blob/master/README_CN.md)

### 开发流程

#### 1. 客户端创建
```java
1. 参考阿里云构建 HuaWeiCloudClient 类
2. 通过 AccessKeyID、AccessKeySecret 和平台SDK创建客户端
3. 获取特定资源的 Client 对象（EcsClient、BosClient、等等）
```

#### 2. 数据查询
```java
使用华为 SDK 查询资源数据
平台URL: https://{}.{}.myhuaweicloud.com
```

#### 3. 数据格式化
```java
请求访问接口时参数保持和阿里云参数处理和校验逻辑一致
统一返回数据格式，参考 com.futong.gemini.model.otc.nxc.entity.* 和 com.futong.gemini.model.otc.bxc.entity.* 和 com.futong.gemini.model.otc.gjc.entity.*
```

#### 4. 消息发送
```java
通过 BaseCloudService.toMessageAndSend 方法发送数据至 RabbitMQ
```

### 配置文件
- **账号配置**: [gemini-plugins/05-plugins-cloud/g03-cloud-huawei/src/main/resources/account/](mdc:gemini-plugins/05-plugins-cloud/g03-cloud-huawei/src/main/resources/account)
- **服务注册**: [gemini-plugins/05-plugins-cloud/s07-cloud-baidu/src/main/resources/META-INF/services/](mdc:gemini-plugins/05-plugins-cloud/g03-cloud-huawei/src/main/resources/META-INF/services)
**文档版本**: 1.0.0  
**创建时间**: 2025年07月
**最后更新**: 2025年07月
**负责人**: 开发团队  
**审核人**: 技术负责人 