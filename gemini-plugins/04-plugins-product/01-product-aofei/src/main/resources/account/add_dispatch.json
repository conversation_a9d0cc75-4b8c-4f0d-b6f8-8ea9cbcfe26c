{"data": [{"required": true, "unique": true, "dispatcher_info": {"jobName": "采集资源组和资产", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "采集资源组和资产", "jobInfo": "{\"action\":\"FetchResourceGroup\",\"async\":false,\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步区域", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步区域", "jobInfo": "{\"action\":\"FetchPlatformAzone\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步任务", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步任务", "jobInfo": "{\"action\":\"FetchTask\",\"async\":false,\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步推理任务性能", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步推理任务性能", "jobInfo": "{\"action\":\"FetchTaskPerf\",\"async\":false,\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步调优任务", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步调优任务", "jobInfo": "{\"action\":\"FetchFineTask\",\"async\":false,\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步调优任务性能", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步调优任务性能", "jobInfo": "{\"action\":\"FetchFineTaskPerf\",\"async\":false,\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步镜像", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步镜像", "jobInfo": "{\"action\":\"FetchImage\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步算法", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步算法", "jobInfo": "{\"action\":\"<PERSON>tch<PERSON>lgo\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步数据集", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步数据集", "jobInfo": "{\"action\":\"FetchDataset\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步开发环境", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步开发环境", "jobInfo": "{\"action\":\"FetchDevEnv\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步开发环境性能", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步开发环境", "jobInfo": "{\"action\":\"FetchDevEnvPerf\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步模型", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步模型", "jobInfo": "{\"action\":\"FetchModel\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步标签", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": "cmp_atlas_exchange", "mqRoutingKey": "cmp_atlas_resource_routing", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步模型", "jobInfo": "{\"action\":\"FetchTag\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}