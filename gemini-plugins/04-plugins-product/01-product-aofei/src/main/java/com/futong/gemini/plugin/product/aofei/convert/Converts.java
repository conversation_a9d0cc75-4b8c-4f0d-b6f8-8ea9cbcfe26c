package com.futong.gemini.plugin.product.aofei.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.atlas.entity.*;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.TimeUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.ComputeUtil;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiTokenParam;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class Converts {

    public static <T> T parseAndConvert(String jsonString, TypeReference<T> typeRef) {
        return JSON.parseObject(jsonString, typeRef);
    }

    public static JSONArray fetchResourceToJsonArray(DescribeAofeiRequest request, String url, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(), request.getAuthToken().getData().getToken());
        String responseJson = HttpClientUtil.get(url, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (ObjectUtil.isEmpty(key)) {
            array = JSONArray.parseArray(responseJson);
        } else if (HttpClientUtil.isJsonarray(JSONObject.parseObject(responseJson).get(key).toString())) {
            array = JSONObject.parseObject(responseJson).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(responseJson).getJSONObject(key));
        }
        return array;
    }

    public static JSONArray fetchResourceToJsonArray(AofeiToken token, String url, String json, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + token.getData().getToken());

        String responseJson = HttpClientUtil.post(url, json, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (JSONObject.parseObject(responseJson).getJSONObject("data") != null) {
            JSONObject data = JSONObject.parseObject(responseJson).getJSONObject("data");
            if (data.getJSONArray(key) != null) {
                array = data.getJSONArray(key);
            } else {
                array.add(data);
            }
        } else {
            if (JSONObject.parseObject(responseJson) != null) {
                JSONObject data = JSONObject.parseObject(responseJson);
                if (data.getJSONArray(key) != null) {
                    array = data.getJSONArray(key);
                }
            }
        }
        return array;
    }

    public static JSONArray fetchListResourceToJsonArray(AofeiToken token, String url, String json, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + token.getData().getToken());

        String responseJson = HttpClientUtil.post(url, json, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (JSONObject.parseObject(responseJson).getJSONArray(key) != null) {
            array= JSONObject.parseObject(responseJson).getJSONArray(key);
        }
        return array;
    }

    public static JSONArray fetchResourceResultToJsonArray(AofeiToken token, String url, String json, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + token.getData().getToken());

        String responseJson = HttpClientUtil.post(url, json, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (JSONObject.parseObject(responseJson).getJSONArray("result") != null) {
            array = JSONObject.parseObject(responseJson).getJSONArray("result");

        }
        return array;
    }

    public static JSONObject fetchResourceResultToJSONObject(AofeiToken token, String url, String json, String key) {
        JSONObject obj = new JSONObject();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + token.getData().getToken());
        String responseJson = HttpClientUtil.post(url, json, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return obj;
        if (JSONObject.parseObject(responseJson).getJSONObject(key) != null) {
            obj = JSONObject.parseObject(responseJson).getJSONObject(key);

        }
        return obj;
    }

    public static JSONArray getResourceToJsonArray(AofeiToken token, String url, String json, String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + token.getData().getToken());

        String responseJson = HttpClientUtil.get(url, config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (JSONObject.parseObject(responseJson).getJSONObject("data") != null) {
            JSONObject data = JSONObject.parseObject(responseJson).getJSONObject("data");
            if (data.getJSONArray(key) != null) {
                array = data.getJSONArray(key);
            }
        }
        return array;
    }

    public static JSONArray getResourceToJsonObject(AofeiToken token, String url, String json,String key) {
        JSONArray array = new JSONArray();
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+token.getData().getToken());

        String responseJson = HttpClientUtil.get(url,config);
        if (ObjectUtil.isEmpty(responseJson) || "null".equals(responseJson))
            return array;
        if (JSONObject.parseObject(responseJson).getJSONArray("data")!=null) {
            array = JSONObject.parseObject(responseJson).getJSONArray("data");
        }
        return array;
    }

    public static JSONArray covertJsonArray(String json, String key) {
        JSONArray array = new JSONArray();
        if (ObjectUtil.isEmpty(json) || "null".equals(json))
            return array;
        if (HttpClientUtil.isJsonarray(JSONObject.parseObject(json).get(key).toString())) {
            array = JSONObject.parseObject(json).getJSONArray(key);
        } else {
            array.add(JSONObject.parseObject(json).getJSONObject(key));
        }
        return array;
    }

    public static List<AofeiToken> getToken(List<AofeiUser> list, CloudAccessBean cloudAccessBean) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        List<AofeiToken> tokenList = new ArrayList<>();
        list.forEach(user -> {
            String id = user.getUserId();
            AofeiTokenParam param = new AofeiTokenParam();
            param.setUsername(cloudAccessBean.getUsername());
            param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
            param.setUserid(id);
            try {
                AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
                tokenList.add(aofeiToken);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return tokenList;
    }


    /**
     * 获取数据通用方法
     *
     * @param list
     * @param cloudAccessBean
     * @param json
     * @param propertyUrl
     * @param protocol
     * @param key
     * @return
     */
    public static JSONArray convertData(List<AofeiUser> list, CloudAccessBean cloudAccessBean, String json, String propertyUrl, String protocol, String key) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        JSONArray result = new JSONArray();
        list.forEach(user -> {
            String userId = user.getUserId();
            AofeiTokenParam param = new AofeiTokenParam();
            param.setUsername(cloudAccessBean.getUsername());
            param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
            param.setUserid(userId);
            try {
                AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
                if (TypeEnum.GET.getValue().equals(protocol)) {
                    JSONArray array = Converts.getResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                } else {
                    JSONArray array = Converts.fetchResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                }

            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
        });
        // 推送规格数据
        return result;
    }

    public static JSONArray convertDataPerf(List<AofeiUser> list, CloudAccessBean cloudAccessBean, String json, String propertyUrl, String protocol, String key) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        JSONArray result = new JSONArray();
        list.forEach(user -> {
            String userId = user.getUserId();
            AofeiTokenParam param = new AofeiTokenParam();
            param.setUsername(cloudAccessBean.getUsername());
            param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
            param.setUserid(userId);
            try {
                AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
                if (TypeEnum.GET.getValue().equals(protocol)) {
                    JSONArray array = Converts.getResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                } else {
                    JSONArray array = Converts.fetchListResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                }

            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
        });
        // 推送规格数据
        return result;
    }

    /**
     * 获取数据通用方法
     *
     * @param list
     * @param cloudAccessBean
     * @param json
     * @param propertyUrl
     * @param protocol
     * @param key
     * @return
     */
    public static JSONArray convertListData(List<AofeiUser> list, CloudAccessBean cloudAccessBean, String json, String propertyUrl, String protocol, String key) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        JSONArray result = new JSONArray();
        list.forEach(user -> {
            String userId = user.getUserId();
            AofeiTokenParam param = new AofeiTokenParam();
            param.setUsername(cloudAccessBean.getUsername());
            param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
            param.setUserid(userId);
            try {
                AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
                if (TypeEnum.GET.getValue().equals(protocol)) {
                    JSONArray array = Converts.getResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                } else {
                    JSONArray array = Converts.fetchListResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                    result.addAll(array);
                }

            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
        });
        // 推送规格数据
        return result;
    }
    /**
     * 获取数据通用方法
     *
     * @param cloudAccessBean
     * @param json
     * @param propertyUrl
     * @param protocol
     * @param key
     * @return
     */
    public static JSONArray convertDataByUser(AofeiUser user, CloudAccessBean cloudAccessBean, String json, String propertyUrl, String protocol, String key) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        JSONArray result = new JSONArray();
        String userId = user.getUserId();
        AofeiTokenParam param = new AofeiTokenParam();
        param.setUsername(cloudAccessBean.getUsername());
        param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
        param.setUserid(userId);
        try {
            AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
            if (TypeEnum.GET.getValue().equals(protocol)) {
                JSONArray array = Converts.getResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                result.addAll(array);
            } else {
                JSONArray array = Converts.fetchResourceToJsonArray(aofeiToken, propertyUrl, json, key);
                result.addAll(array);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
        // 推送规格数据
        return result;
    }

    /**
     * 资产转换
     *
     * @param bean
     * @param info
     * @return
     */
    public static CaDatahubProperty toProperty(CloudAccessBean bean, JSONObject info, String type, List<CaDatahubPropertyVersion> versionList, Map<String, JSONObject> versionMap, List<CaDatahubPropertyTag> propertyTagList, String subCmpId) {
        CaDatahubProperty property = new CaDatahubProperty();
        property.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, info.getString("id"), subCmpId));
        property.setOpen_id(info.getString("id"));
        property.setName(info.getString("name"));
        property.setCloud_account_id(bean.getCmpId());
        property.setOpen_create_user_id(info.getString("creator"));
        property.setOpen_json(info.toJSONString());
        property.setIs_preset(info.getString("isPreset"));
        property.setVersion_id(info.getString("versionId"));
        property.setProperty_type(type);
        property.setSub_account_id(subCmpId);
        property.setCloud_type(bean.getCloudType());
        property.setCollections(info.getBoolean("isCollected")?1:0);
        JSONArray taskTags = info.getJSONArray("taskTags");
//        JSONArray versions = info.getJSONArray("versions");
//        if (ObjectUtil.isNotEmpty(versions)) {
//            versions.forEach(temp -> {
//                JSONObject tempObj = (JSONObject) temp;
//                CaDatahubPropertyVersion version = Converts.toPropertyVersion(BaseClient.auths.get(), tempObj, type, property.getOpen_id(), versionMap, subCmpId);
//
//                if (ObjectUtil.isNotNull(version))
//                    versionList.add(version);
//            });
//        }
        if (ObjectUtil.isNotEmpty(taskTags)) {
            taskTags.forEach(temp -> {
                String tagName = (String) temp;
                CaDatahubPropertyTag tag = new CaDatahubPropertyTag();
                tag.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, property.getOpen_id(), getTagId(tagName), subCmpId));
                tag.setOpen_id(getTagId(tagName));
                tag.setPropery_id(property.getOpen_id());
                tag.setCloud_type(bean.getCloudType());
                tag.setTag(tagName);
                tag.setSub_account_id(subCmpId);
                tag.setCloud_account_id(bean.getCmpId());
                tag.setProperty_type(type);
                propertyTagList.add(tag);
            });
        }
        return property;
    }

    public static CaDatahubPropertyConfig toPropertyConfig(CloudAccessBean bean, JSONObject info, String type, String propertyId, String versionId, String subCmpId) {
        CaDatahubPropertyConfig config = new CaDatahubPropertyConfig();
        config.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, propertyId,versionId,info.getString("id"),subCmpId));
        config.setOpen_id(info.getString("id"));
        config.setName(info.getString("name"));
        config.setCloud_account_id(bean.getCmpId());
        config.setOpen_json(info.toJSONString());
        config.setCloud_type(bean.getCloudType());
        config.setOpen_update_time(info.getLong("updateTime"));
        config.setProperty_type(type);
        config.setStart_file(info.getString("bootFile"));
        config.setSub_account_id(subCmpId);
        config.setPropery_id(propertyId);
        return config;
    }

    public static CaDatahubPropertyInfo toPropertyInfo(CloudAccessBean bean, JSONObject info, String type, String propertyId, String versionId, String subCmpId) {
        CaDatahubPropertyInfo propertyInfo = new CaDatahubPropertyInfo();
        propertyInfo.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, propertyId, versionId, info.getString("id"), subCmpId));
        propertyInfo.setOpen_id(info.getString("id"));
        propertyInfo.setName(info.getString("name"));
        propertyInfo.setCloud_account_id(bean.getCmpId());
        propertyInfo.setOpen_json(info.toJSONString());
        propertyInfo.setContent(info.getString("content"));
        propertyInfo.setVersion_id(versionId);
        propertyInfo.setCloud_type(bean.getCloudType());
        propertyInfo.setOpen_update_time(info.getLong("updateTime"));
        propertyInfo.setProperty_type(type);
        propertyInfo.setSub_account_id(subCmpId);
        propertyInfo.setPropery_id(propertyId);
        return propertyInfo;
    }

    public static CaDatahubPropertyDatafile toPropertyDataFile(CloudAccessBean bean, JSONObject info, String type, String propertyId, String versionId, String parentId) {
        CaDatahubPropertyDatafile dataFile = new CaDatahubPropertyDatafile();
        dataFile.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, propertyId, versionId, info.getString("fileType"), info.getString("filePath"), info.getString("fileName")));
        dataFile.setFile_name(info.getString("fileName"));
        dataFile.setFile_type(info.getString("fileType"));
        dataFile.setFile_size(info.getDouble("fileSize"));
        dataFile.setOpen_update_time(info.getLong("updateTime"));
        dataFile.setProperty_type(type);
        dataFile.setPropery_id(propertyId);
        dataFile.setVersion_id(versionId);
        dataFile.setOpen_parent_id(null);
        dataFile.setCloud_account_id(bean.getCmpId());
        dataFile.setCloud_type(bean.getCloudType());
        dataFile.setOpen_json(info.toJSONString());
        dataFile.setOpen_parent_id(parentId);
        dataFile.setFile_path(info.getString("filePath"));
        dataFile.setCurrent_path(info.getString("currentPath"));
        return dataFile;
    }

    public static CaDatahubPropertyVersion toPropertyVersion(CloudAccessBean bean, JSONObject info, String type, String propertyId, Map<String, JSONObject> versionMap, String subCmpId,String userId) {
        CaDatahubPropertyVersion version = new CaDatahubPropertyVersion();
        version.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, propertyId, info.getString("id"), subCmpId));
        version.setOpen_id(info.getString("id"));
        version.setVersion_desc(info.getString("description"));
        version.setOpen_create_time(info.getLong("createdAt"));
        version.setOpen_update_time(info.getLong("updatedAt"));
        version.setVersion_status(info.getInteger("createStatus") == 1 ? "创建完成" : "创建失败");
        version.setOpen_json(info.toJSONString());
        version.setVersion_name(info.getString("name"));
        version.setProperty_type(type);
        version.setSub_account_id(subCmpId);
        version.setCloud_account_id(bean.getCmpId());
        version.setCloud_type(bean.getCloudType());
        version.setPropery_id(info.getString("dataId"));
        version.setPath("/private/datahub/"+userId+"/"+info.getString("dataId")+"/"+info.getString("id")+"/");
        if("dataset".equals(type)) {
            version.setPath("/private/datahub/"+userId+"/"+info.getString("dataId")+"/"+info.getString("id")+"/data");
        }

        return version;
    }

    public static CaDevEnv toDevenv(CloudAccessBean bean, JSONObject info, String type, String subCmpId) {
        CaDevEnv devenv = new CaDevEnv();
        devenv.setId(IdUtils.encryptId(subCmpId, bean.getCloudType(), type, info.getString("name")));
        devenv.setOpen_id(info.getString("id"));
        devenv.setName(info.getString("name"));
        devenv.setCpu(info.getInteger("cpuLimit"));
        devenv.setMemory(info.getDouble("memoryLimit"));
        devenv.setWeb_path(info.getString("webPath"));
        devenv.setResource_group_id(info.getString("resourceGroupId"));
        devenv.setSsh_service_ip(info.getString("nodeIp"));
        devenv.setStatus(getStatus(info.getInteger("status")));
        devenv.setOpen_status(info.getString("status"));
        devenv.setImage_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceEnum.IMAGE.getValue(), info.getString("imageId")));
        devenv.setImage_version_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceEnum.VERSION.getValue(), info.getString("imageVersionId")));
        devenv.setImage_type(info.getString("imageType"));
        devenv.setCloud_account_id(bean.getCmpId());
        devenv.setOpen_create_user_id(info.getString("author"));
        devenv.setOpen_create_time(info.getLong("createdTime"));
        devenv.setEnable_shared_memory(info.getBoolean("enableSharedMemory") ? "1" : "0");
        devenv.setSsh_username(info.getString("sshUsername"));
        devenv.setSsh_password(info.getString("sshPassword"));
        devenv.setSsh_port(info.getString("sshPort"));
        devenv.setSsh_service_ip(info.getString("serviceClusterIp"));
        devenv.setEnable_ssh(info.getBoolean("enableSsh") ? "1" : "0");
        devenv.setEnable_shared_memory(info.getBoolean("enableSharedMemory") ? "1" : "0");
        devenv.setAuth_type(info.getString("sshAuthType"));
        devenv.setOpen_create_user_id(info.getString("creator"));
        devenv.setKey_pair_id(info.getString("keyPairId"));
        if (ObjectUtil.isNotEmpty(info.getJSONObject("gpuResource"))) {
            JSONObject gpuResource = info.getJSONObject("gpuResource");
            devenv.setGpu(gpuResource.getInteger("gpuLimit"));
            devenv.setVgpu(gpuResource.getInteger("vGpu"));
            devenv.setVgpu_mem(gpuResource.getInteger("vGpuMem"));
            devenv.setVgpu_cores(gpuResource.getString("vGpuCores"));
        }
        devenv.setSub_account_id(subCmpId);
        devenv.setGpu(info.getInteger("gpu"));
        devenv.setOpen_json(info.toJSONString());
        devenv.setCloud_type(bean.getCloudType());
        return devenv;
    }

    /**
     * 任务转换方法
     *
     * @param bean
     * @param info
     * @param type
     * @return
     */
    public static CaLargeModelFineTuneTask toFineTask(CloudAccessBean bean, JSONObject info, String type, String subCmpId) {
        CaLargeModelFineTuneTask task = new CaLargeModelFineTuneTask();
        // 基本信息
        task.setId(IdUtils.encryptId(subCmpId, bean.getCloudType(), type, info.getString("taskName")));
        task.setOpen_id(info.getString("id"));
        task.setTask_name(info.getString("taskName"));
        task.setOpen_status(info.getString("status"));
        task.setCloud_account_id(bean.getCmpId());
        task.setCloud_type(bean.getCloudType());
        task.setOpen_json(info.toJSONString());

        // 运行时信息
        task.setRun_time(info.getString("runTime"));
        task.setOpen_create_time(TimeUtils.stringToLongTime(info.getString("createTime")));

        // 资源配置信息
        //task.setImage(info.getString("image"));
        task.setImage_id(info.getString("imageId"));
        task.setImage_name(info.getString("imageName"));
        task.setImage_version_name(info.getString("imageVersion"));
        task.setImage_version_id(info.getString("imageVersionId"));

        // 模型信息
        task.setModel_id(info.getString("modelId"));
        task.setModel_name(info.getString("modelName"));
        // task.setModel_version(info.getString("modelVersion"));
        task.setModel_version_id(info.getString("modelVersionId"));
        task.setModel_version_name(info.getString("modelVersion"));
        //task.setModel_origin_type(info.getString("modelOriginType"));
        //task.setOrigin_model_dir(info.getString("originModelDir"));

        // 数据集信息
        task.setDataset_id(info.getString("datasetId"));
        task.setDataset_name(info.getString("dataSetName"));
        // task.setDataset_version(info.getString("datasetVersion"));
        // task.setDataset_version_id(info.getString("datasetVersionId"));

        // 算法信息
        task.setAlgorithm_id(info.getString("algorithmId"));
        task.setAlgorithm_name(info.getString("algorithmName"));
        task.setAlgorithm_version_name(info.getString("algorithmVersion"));
        task.setAlgorithm_version_id(info.getString("algorithmVersionId"));
        // task.setAlgorithm_origin_type(info.getString("algorithmOriginType"));
        task.setAlgorithm_path(info.getString("algorithmPath"));
        task.setStartup_file(info.getString("startupFile"));

        // 训练框架和策略
        task.setTask_type(info.getString("llmTrainFramework"));
        task.setFramework(info.getString("llmTrainFramework"));
        //  task.setFine_tune_strategy(info.getString("fineTuneStrategy"));
        // task.setFine_tune_strategy_id(info.getString("fineTuneStrategyId"));
        task.setIs_pretrain(info.getString("isPretrain"));
        task.setTask_scene(info.getInteger("isPretrain") == 0 ? "微调" : "预训练");

        // 资源配置
        task.setCommand(info.getString("command"));
        //    task.setInput_dir(info.getString("inputDir"));
        // task.setInput_type(info.getString("inputType"));
        task.setOut_put_dir(info.getString("outputDir"));
        task.setWorking_dir(info.getString("workingDir"));
        // task.setRdma(info.getString("rdma"));
        task.setTask_desc(info.getString("description"));
        // task.setCreate_way(info.getString("createWay"));

        // 分区信息
        String partitions = info.getString("partitions");
        if (ObjectUtil.isNotEmpty(partitions)) {
            JSONObject partitionsObj = JSONObject.parseObject(partitions);
            task.setResource_group_id(partitionsObj.getString("zoneId"));
            task.setResource_group_name(partitionsObj.getString("zoneName"));
            // task.setResource_group_key(partitionsObj.getString("resourceGroupKey"));
        }

        // 资源配置
        // JSONArray resources = info.getJSONArray("resources");
        task.setResource_config_json(info.getString("resources"));

      /*  if (ObjectUtil.isNotEmpty(resources)) {
            JSONObject resource = resources.getJSONObject(0);
            task.setNode_type(resource.getString("nodeType"));
            task.setNode_numbers(resource.getInteger("numbers"));
            task.setCpu_number(resource.getInteger("cpuNumber"));
            task.setMemory(resource.getInteger("memory"));

            String gpuResource = resource.getString("gpuResource");
            if (ObjectUtil.isNotEmpty(gpuResource)) {
                JSONObject gpuObj = JSONObject.parseObject(gpuResource);
                task.setGpu_num(gpuObj.getInteger("gpuNum"));
                task.setGpu_share(gpuObj.getString("gpuShare"));
            }
        }*/

        // 超参数
        // task.setCommon_hyper_parameters(info.getString("commonHyperParameters"));
        task.setHyper_params(info.getString("fineTuneHyperParameters"));

        //task.setFine_tune_hyper_parameters(info.getString("fineTuneHyperParameters"));
        task.setEnv_params(info.getString("variable"));
        task.setSub_account_id(subCmpId);

        return task;
    }

    /**
     * 推理任务方法
     *
     * @param bean 云账号信息
     * @param info 任务信息
     * @param type 资源类型
     * @return CaLargeModelReasoningTask
     */
    public static CaLargeModelReasoningTask toReasonTask(CloudAccessBean bean, JSONObject info, String type, String subCmpId) {
        CaLargeModelReasoningTask task = new CaLargeModelReasoningTask();
        // 基本信息
        task.setId(IdUtils.encryptId(subCmpId, bean.getCloudType(), type, info.getString("name")));
        task.setCloud_account_id(bean.getCmpId());
        task.setCloud_type(bean.getCloudType());
        task.setOpen_json(info.toJSONString());
        task.setCreate_user_id(info.getString("createUser"));

        task.setOpen_id(info.getString("id"));
        task.setReasoning_id(info.getString("id"));
        task.setCreate_user_name(info.getString("userName"));
        task.setOpen_create_user_id(info.getString("createUser"));
        task.setOpen_status(info.getString("status"));
        task.setOpen_update_time(info.getLong("modifiedTime"));
        task.setFramwork(info.getString("framework"));
        task.setTask_name(info.getString("name"));
        task.setTask_desc(info.getString("description"));
        task.setOpen_create_time(info.getLong("createTime"));
        task.setIs_large_model(info.getInteger("isLargeModel"));
        task.setSub_account_id(subCmpId);
        // 版本信息
        JSONArray versions = info.getJSONArray("modelServerVersions");
        if (ObjectUtil.isNotEmpty(versions)) {
            JSONObject version = versions.getJSONObject(0);
            task.setMemory(version.getDouble("memory"));
            task.setModel_id(version.getString("modelId"));
            task.setModel_version_Id(version.getString("modelVersionId"));
            task.setImage(version.getString("image"));
            task.setResource_group_id(version.getString("resourceGroupId"));
            task.setResource_group_name(version.getString("resourceGroupName"));
            task.setLog_dir(version.getString("logDir"));
            task.setRequest_method(version.getString("reqMethod"));
            task.setVolume_path(version.getString("volumePath"));
            task.setMemory(version.getDouble("memory"));
            task.setInstance_sum(version.getInteger("replica"));
            task.setCpu(version.getInteger("cpu"));
            task.setGpu(version.getInteger("gpu"));
            task.setMin_instance_sum(version.getInteger("predictMinReplicas"));
            task.setMax_instance_sum(version.getInteger("predictMaxReplicas"));
            task.setHyper_params(version.getString("hyperParameterList"));
            task.setEnv_params(version.getString("envs"));
            task.setRate(version.getInteger("rate"));
            task.setMetric(version.getInteger("metric"));
            task.setModel_float(version.getString("modelFloat"));
        }

        return task;
    }

    /**
     * 获取推理任务状态
     */
    private static String getReasonTaskStatus(String status) {
        if (ObjectUtil.isEmpty(status)) {
            return "未知";
        }
        switch (status) {
            case "1":
                return "运行中";
            case "2":
                return "启动中";
            case "3":
                return "就绪";
            case "4":
                return "失败";
            case "5":
                return "停止中";
            case "6":
                return "停止";
            case "7":
                return "待创建";
            case "10":
                return "恢复中";
            case "11":
                return "被动停止中";
            default:
                return "未知";
        }
    }

    public static CaKeyPair toKeyPair(CloudAccessBean bean, JSONObject info, String type, String subCmpId) {
        CaKeyPair keypair = new CaKeyPair();
        keypair.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, info.getString("id"), subCmpId));
        keypair.setOpen_id(info.getString("id"));
        keypair.setName(info.getString("name"));
        keypair.setAlgorithm(info.getString("algorithm"));
        keypair.setFingerprint(info.getString("fingerprint"));
        keypair.setCloud_account_id(bean.getCmpId());
        keypair.setCloud_type(bean.getCloudType());
        keypair.setSub_account_id(subCmpId);
        return keypair;
    }

    public static String getStatus(int status) {
        switch (status) {
            case 1:
                return "停止";
            case 2:
                return "停止中";
            case 3:
                return "运行中";
            case 4:
                return "启动中";
            case 5:
                return "删除中";
            case 6:
                return "保存中";
            case 7:
                return "失败";
            case 8:
                return "创建中";
            default:
                return "未知";
        }
    }

    public static CaDevEnvEvent toDevEvent(CloudAccessBean bean, JSONObject info, String type, String envId, String subCmpId) {
        CaDevEnvEvent event = new CaDevEnvEvent();
        event.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), envId, type, info.getString("id"), subCmpId));
        event.setEvent_message(info.getString("message"));
        event.setEvent_name(info.getString("podName"));
        event.setEvent_time(info.getLong("createdTime"));
        event.setDev_env_id(envId);
        event.setOpen_create_time(info.getLong("eventCreatedTime"));
        event.setOpen_id(info.getString("id"));
        event.setOpen_json(info.toJSONString());
        event.setEvent_type(info.getString("type"));
        event.setCloud_account_id(bean.getCmpId());
        event.setCloud_type(bean.getCloudType());
        event.setEvent_reason(info.getString("reason"));
        event.setSub_account_id(subCmpId);
        return event;
    }

    public static CaDatahubFile toFile(CloudAccessBean bean, JSONObject info, String type, String parentId) {
        CaDatahubFile file = new CaDatahubFile();
        file.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, info.getString("isDirectory"), info.getString("filePath"), info.getString("parentPath"), info.getString("fileName")));
        file.setFile_name(info.getString("fileName"));
        file.setFile_size(info.getDouble("fileSizeBytes"));
        file.setIs_directory(info.getString("isDirectory"));
        file.setFile_path(info.getString("filePath"));
        file.setOpen_json(info.toJSONString());
        file.setType("1");
        file.setOpen_parent_id(parentId);
        file.setCloud_account_id(bean.getCmpId());
        file.setCloud_type(bean.getCloudType());
        file.setFile_type(info.getString("isDirectory"));
        file.setIs_directory(info.getString("isDirectory"));
        return file;
    }

    public static CaDevEnvVariable toDevVariable(CloudAccessBean bean, JSONObject info, String type, String envId, String subCmpId) {
        CaDevEnvVariable var = new CaDevEnvVariable();
        var.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), envId, type, info.getString("id"), subCmpId));
        var.setEnv_key(info.getString("key"));
        var.setEnv_value(info.getString("value"));
        var.setDev_env_id(envId);
        var.setOpen_id(info.getString("id"));
        var.setOpen_json(info.toJSONString());
        var.setCloud_account_id(bean.getCmpId());
        var.setCloud_type(bean.getCloudType());
        var.setSub_account_id(subCmpId);
        return var;
    }

    public static CaDevEnvPort toDevPort(CloudAccessBean bean, JSONObject info, String type, String envId, String subCmpId) {
        CaDevEnvPort port = new CaDevEnvPort();
        port.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), envId, type, info.getString("id"), subCmpId));
        port.setPort(info.getString("port"));
        port.setNode_port(info.getString("nodePort"));
        port.setDescription(info.getString("description"));
        port.setService_name(info.getString("serviceName"));
        port.setDev_env_id(envId);
        port.setOpen_id(info.getString("id"));
        port.setOpen_json(info.toJSONString());
        port.setCloud_account_id(bean.getCmpId());
        port.setCloud_type(bean.getCloudType());
        port.setSub_account_id(subCmpId);
        return port;
    }

    public static CaResourceGroup toResourceGroup(CloudAccessBean bean, JSONObject info, String subCmpId) {
        CaResourceGroup rg = new CaResourceGroup();
        rg.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), "ca_resource_group", info.getString("id"),subCmpId));
        rg.setOpen_id(info.getString("id"));
        rg.setOpen_group_name(info.getString("name"));
        rg.setCloud_account_id(bean.getCmpId());
        rg.setOpen_json(info.toJSONString());
        rg.setOpen_card_total(info.getInteger("gpuTotal"));
        rg.setOpen_card_usable(info.getInteger("gpuSurplus"));
        rg.setOpen_cpu_total(info.getInteger("cpuTotal"));
        rg.setOpen_cpu_usable(info.getInteger("cpuSurplus"));
        rg.setOpen_ram_total(info.getDouble("memTotal"));
        rg.setOpen_ram_usable(info.getDouble("memSurplus"));
        rg.setOpen_status(info.getString("status"));
        rg.setOpen_queue_num(info.getInteger("queuePodNum"));
        rg.setCloud_type(bean.getCloudType());
        rg.setSub_account_id(subCmpId);
        return rg;
    }

    public static CaResourceGroupNode toResourceGroupNode(CloudAccessBean bean, JSONObject info, String groupId, String subCmpId) {
        CaResourceGroupNode rgNode = new CaResourceGroupNode();
        rgNode.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), "ca_resource_group_node", info.getString("nodeId"),subCmpId));
        rgNode.setOpen_id(info.getString("nodeId"));
        rgNode.setOpen_node_name(info.getString("nodeName"));
        rgNode.setOpen_node_ip(info.getString("nodeIp"));
        rgNode.setOpen_node_status(info.getString("nodeStatus"));
        rgNode.setOpen_group_id(info.getString("clusterId"));
        rgNode.setOpen_cpu_total(info.getInteger("cpuTotal"));
        rgNode.setOpen_cpu_usable(info.getInteger("cpuSurplus"));
        rgNode.setOpen_card_total(info.getInteger("gpuTotal"));
        rgNode.setOpen_card_usable(info.getInteger("gpuSurplus"));
        rgNode.setOpen_ram_total(info.getDouble("memTotal"));
        rgNode.setOpen_ram_usable(info.getDouble("memSurplus"));
        rgNode.setOpne_node_gpu_ts(info.getString("gpuType"));
        rgNode.setOpen_video_ram_total(info.getInteger("gpuMemTotal"));
        rgNode.setOpen_video_ram_usable(info.getInteger("gpuMemSurplus"));
        rgNode.setOpen_group_id(groupId);
        rgNode.setCloud_account_id(bean.getCmpId());
        rgNode.setOpen_json(info.toJSONString());
        rgNode.setCloud_type(bean.getCloudType());
        rgNode.setSub_account_id(subCmpId);
        return rgNode;
    }

    public static AofeiToken getUserToken(CloudAccessBean cloudAccessBean) {
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        String userId = AofeiConstants.USER_ID;
        AofeiTokenParam param = new AofeiTokenParam();
        String jsonStr = cloudAccessBean.getJsonStr();
        log.info("jsonStr==="+ jsonStr);
        if(jsonStr!=null) {
            JSONObject obj = JSONObject.parseObject(jsonStr);
            userId  = obj.getString("userId")==null?userId:obj.getString("userId");
        }
        log.info("userId==="+ userId);
        param.setUsername(cloudAccessBean.getUsername());
        log.info("param.getUsername==="+ param.getUsername());
        param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
        log.info("param.getPassword()==="+ param.getPassword());
        param.setUserid(userId);
        try {
            AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
            log.info("aofeiToken==="+ JSON.toJSONString(aofeiToken));
            return aofeiToken;
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
    }

    public static String getTagId(String name) {
        String[] tags = new String[]{"", "图像检测", "图像分类", "图像分割", "文本分类", "智能对话", "机器翻译", "文本生成",
                "代码生成", "图像生成", "深度学习", "机器学习", "推理", "Jupyter", "微调", "预训练", "人脸检测", "scikit_learn", "奖励模型", "强化学习", "直接偏好优化", "SFT", "EMBEDDING", "ReRanker"};
        for (int i = 0; i < tags.length; i++) {
            if (tags[i].equals(name)) {
                return String.valueOf(i);
            }
        }
        return "";
    }

    /**
     * 转换大模型调优任务实例
     *
     * @param bean   云账号信息
     * @param info   实例信息
     * @param taskId 任务ID
     * @param type   资源类型
     * @return CaLargeModelFineTuneTaskPod
     */
    public static CaTaskInstance toFineTuneTaskPod(CloudAccessBean bean, JSONObject info, String taskId, String type, String subCmpId) {
        CaTaskInstance pod = new CaTaskInstance();
        pod.setId(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), type, info.getString("id"),subCmpId));
        pod.setOpen_id(info.getString("id"));
        pod.setTask_id(taskId);
        pod.setName(info.getString("name"));
        pod.setOpen_status(info.getString("status"));
        pod.setType(info.getString("type"));
        pod.setStart_time(TimeUtils.stringToLongTime(info.getString("startTime")));
        pod.setEnd_time(TimeUtils.stringToLongTime(info.getString("endTime")));
        pod.setRun_time(info.getString("runTime"));
        pod.setIp(info.getString("ip"));
        pod.setCloud_type(bean.getCloudType());
        pod.setCloud_account_id(bean.getCmpId());
        pod.setSub_account_id(subCmpId);
        pod.setOpen_json(info.toJSONString());
        return pod;
    }

    /**
     * 转换推理调用统计数据
     * @param bean 云账号信息
     * @param statObj 统计数据JSON对象
     * @param reasonTaskId 推理任务ID
     * @param subCmpId 子账号ID
     * @return 转换后的调用统计对象
     */
    public static CaLargeModelReasoningCallNum toReasonCallNum(CloudAccessBean bean, JSONObject statObj, String reasonTaskId, String subCmpId) {
        CaLargeModelReasoningCallNum callNum = new CaLargeModelReasoningCallNum();
        callNum.setId(IdUtils.encryptId(subCmpId, bean.getCloudType(), ResourceEnum.REASON.getValue(), reasonTaskId, statObj.getString("date")));
        callNum.setOpen_id(reasonTaskId);
        callNum.setCloud_account_id(bean.getCmpId());
        callNum.setCloud_type(bean.getCloudType());
        callNum.setSub_account_id(subCmpId);
        callNum.setOpen_json(statObj.toJSONString());
        callNum.setTotal(statObj.getString("total"));
        callNum.setDay_date(statObj.getString("date"));
        callNum.setSuccess(statObj.getString("success"));
        callNum.setFail(statObj.getString("fail"));
        callNum.setTask_id(reasonTaskId);
        return callNum;
    }

    public static void toCaTaskTag(List<CaTaskTag> tagList,CloudAccessBean bean, JSONObject tagObj,String parentId,String type) {
        JSONArray children = tagObj.getJSONArray("children");
        CaTaskTag tag = new CaTaskTag();
        tag.setId(IdUtils.encryptId(bean.getCloudType(), ResourceEnum.TAG.getValue(), type,tagObj.getString("id")));
        tag.setTag_name(tagObj.getString("name"));
        tag.setParent_id(parentId);
        tag.setOpen_id(tagObj.getString("id"));
        tag.setSort(tagObj.getInteger("tagSort"));
        tag.setCloud_account_id(bean.getCmpId());
        tag.setCloud_type(bean.getCloudType());
        tag.setOpen_json(tagObj.toJSONString());
        tag.setProperty_type(type);
        if (ObjectUtil.isNotEmpty(children)) {
            children.forEach(temp -> {
                        JSONObject child = (JSONObject) temp;
                toCaTaskTag(tagList,bean,  child, tag.getId(),type);
            });
        }
        tagList.add(tag);
    }


    public static List<ResourcePerfDetail> toPerfDetail(CloudAccessBean bean,JSONObject task,String type,String podName,JSONArray list,long time, String subCmpId) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        String resId = "";
        if("fine".equals(type)) {
            resId = IdUtils.encryptId(subCmpId, bean.getCloudType(), type, task.getString("taskName"));
        }else {
            resId = IdUtils.encryptId(subCmpId, bean.getCloudType(), type, task.getString("name"));
        }

        if(CollUtil.isNotEmpty(list)) {
            JSONObject dataList = list.getJSONObject(0);
            JSONObject cpuList = dataList.getJSONObject(TypeEnum.CPU.getValue());
            JSONObject memList = dataList.getJSONObject(TypeEnum.MEM.getValue());
            JSONObject gpuList = dataList.getJSONObject(TypeEnum.GPU.getValue());
            JSONObject gpuCoreList = dataList.getJSONObject(TypeEnum.GPUCORE.getValue());

            ResourcePerfDetail perf = new ResourcePerfDetail();
            perf.setAccountId(bean.getCmpId());
            perf.setResId(resId);
            perf.setOpenId(task.getString("id"));
            perf.setOpenName(podName);
            perf.setCloudType(bean.getCloudType());
            perf.setResourceType(type);
            perf.setGpu(0d);
            perf.setGpuCore(0d);
            perf.setCpuUsage(cpuList.getJSONArray("values").size()==0?0d:cpuList.getJSONArray("values").getDouble(cpuList.getJSONArray("values").size()-1));
            perf.setMemUsage(memList.getJSONArray("values").size()==0?0d:memList.getJSONArray("values").getDouble(memList.getJSONArray("values").size()-1));
            JSONObject gpuListJSONObject = gpuList.getJSONObject("values");
            try {
                for (String key : gpuListJSONObject.keySet()) {
                    perf.setGpu(gpuListJSONObject.getJSONArray(key).size() == 0 ? 0d : gpuListJSONObject.getJSONArray(key).getDouble(gpuListJSONObject.getJSONArray(key).size() - 1));
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
            JSONObject gpuCoreListJSONObject = gpuCoreList.getJSONObject("values");
            try {
                for (String key : gpuCoreListJSONObject.keySet()) {
                    perf.setGpuCore(gpuCoreListJSONObject.getJSONArray(key).size()==0?0d:gpuCoreListJSONObject.getJSONArray(key).getDouble(gpuCoreListJSONObject.getJSONArray(key).size()-1));
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
            perf.setCreateTime(DateUtil.formatDateTime(new Date(time)));
            perfList.add(perf);
        }
        return perfList;
    }

}
