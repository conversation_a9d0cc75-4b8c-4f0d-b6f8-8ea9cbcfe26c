package com.futong.gemini.plugin.product.aofei.request;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CreateLlmFineTuneRequest extends BaseRequest{
    private String name;
    private String type;
    private String zoneId;
    private String zoneName;
    private String resourceGroupKey;
    private String image;
    private String imageId;
    private String imageName;
    private String imageVersion;
    private String imageVersionId;
    private List<ResourceConfig> resource;

    private String resourceType;
    private String inputDir = "";
    private String inputType;
    private String outputDir;
    private String isUrgentTask;
    private String describe;
    private String userId;
    private Integer isPretrain;
    private String createWay;
    private String llmTrainFramework;
    private List<HyperParameter> commonHyperParameters;
    private List<Variable> variable = new ArrayList<>();
    private String fineTuneStrategy;
    private String fineTuneStrategyId;
    private String datasetName;
    private String datasetId;
    private String datasetVersion;
    private String datasetVersionId;
    private String algorithmId;
    private String algorithmName;
    private String algorithmVersion;
    private String algorithmVersionId;
    private String algorithmPath = "";
    private String algorithmOriginType;
    private String startupFile;
    private String modelId;
    private String modelName;
    private String modelVersion;
    private String modelVersionId;
    private String originModelDir = "";
    private String modelOriginType;
    private Boolean rdma = false;
    private String resourceName;
    private String command;
    private String storageMedium;
    private String workingDir;
    private String userName;

    @Data
    public static class ResourceConfig {
        private String nodeType = "Node";
        private Integer numbers;
        private GpuResource gpuResource;
        private Integer cpuNumber;
        private Integer memory;
    }

    @Data
    public static class GpuResource {
        private String gpuShare = "none";
        private Integer gpuNum;
        private Integer gpuCores;
        private Integer gpuMem;
        private String gpuResourceType;
        private String migRes;
    }

    @Data
    public static class HyperParameter {
        private String name;
        private String value;
        private String description;
        private Boolean custom;
    }

    @Data
    public static class Variable {
        private String key;
        private String value;
        private String description;
        private Boolean custom;
    }

    private String scenario = "算力任务（调优）";

    private boolean eventNotification = false;

    private Integer autoRecovery = 0;

    private ExtraModelParamsBo extraModelParamsBo;

    private JSONArray fineTuneHyperParameters = new JSONArray();

    @Data
    public static class ExtraModelParamsBo {
        private ExtraSftModelParamsBo extraSftModelParamsBo;
        private ExtraRewardModelParamsBo extraRewardModelParamsBo;
    }

    @Data
    public static class ExtraSftModelParamsBo {
        private String sftModelId;
        private String sftModelName;
        private String sftModelVersion;
        private String sftModelVersionId;
        private String sftOriginModelDir;
        private String sftModelOriginType = "aiAssets";
    }

    @Data
    public static class ExtraRewardModelParamsBo {
        private String rewardModelId;
        private String rewardModelName;
        private String rewardModelVersion;
        private String rewardModelVersionId;
        private String rewardOriginModelDir;
        private String rewardModelOriginType;
    }

}
