
package com.futong.gemini.plugin.product.aofei.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.constant.dict.DevopsSide;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;

import java.util.ArrayList;
import java.util.List;

public class PlatAZoneService {

    public static final PlatAZoneService bean = new PlatAZoneService();

    public void fetchRegionAndZone(JSONObject arguments) {
        List<TmdbDevops> devopsList = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        TmdbDevops zone = new TmdbDevops();
        zone.setAccount_id(BaseClient.auths.get().getCmpId());
        zone.setDevops_name("默认可用区");
        zone.setDevops_value("默认可用区");
        zone.setCloud_type(BaseClient.auths.get().getCloudType());
        zone.setDict_code(DevopsSide.DEVOPS_ZONE.value());
        zone.setBiz_id(IdUtils.encryptId(new String[]{zone.getAccount_id(), zone.getCloud_type(), zone.getDict_code(), zone.getDevops_value()}));
        zone.setInfo_json(JSON.toJSONString(zone));
        devopsList.add(zone);

        TmdbDevops region = new TmdbDevops();
        region.setAccount_id(BaseClient.auths.get().getCmpId());
        region.setDevops_name("默认区域");
        region.setDevops_value("默认区域");
        region.setCloud_type(BaseClient.auths.get().getCloudType());
        region.setDict_code(DevopsSide.DEVOPS_REGION.value());
        region.setBiz_id(IdUtils.encryptId(new String[]{region.getAccount_id(), region.getCloud_type(), region.getDict_code(), region.getDevops_value()}));
        region.setInfo_json(JSON.toJSONString(region));
        devopsList.add(region);

        TmdbDevopsLink link1 = new TmdbDevopsLink();
        String parentId = IdUtils.encryptId(new String[]{zone.getAccount_id(), zone.getCloud_type()});
        link1.setParent_devops_id(region.getBiz_id());
        link1.setDevops_id(zone.getBiz_id());
        link1.setBiz_id(IdUtils.encryptId(new String[]{parentId, link1.getDevops_id()}));
        links.add(link1);

        TmdbDevopsLink link2 = new TmdbDevopsLink();
        link2.setParent_devops_id(null);
        link2.setDevops_id(region.getBiz_id());
        link2.setBiz_id(IdUtils.encryptId(new String[]{parentId, link2.getDevops_id()}));
        links.add(link2);

        //发送消息
        BaseUtils.sendMessage(devopsList, arguments);

        BaseUtils.sendMessage(links, arguments);
    }

}
