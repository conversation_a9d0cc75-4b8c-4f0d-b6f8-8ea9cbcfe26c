package com.futong.gemini.plugin.product.aofei.request;

import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DescribeAofeiRequest {

    /**
     * 待同步的资源ID
     */
    private List<AofeiUser> ids;

    /**
     * 每次同步的条数
     */
    private Integer fecthSize;

    private Integer taskTag;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * cloudos token信息
     */
    private AofeiToken authToken;

    /** 计算节点名称 */
    private String computeNode;

}
