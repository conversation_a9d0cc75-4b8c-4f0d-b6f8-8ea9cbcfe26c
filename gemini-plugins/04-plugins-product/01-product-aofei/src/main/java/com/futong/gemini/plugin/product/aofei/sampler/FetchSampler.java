package com.futong.gemini.plugin.product.aofei.sampler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.route.RouteInfo;
import com.futong.gemini.model.route.mq.RabbitRoute;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.service.*;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.TscInfo;
import com.futong.sniffgourd.sdk.model.TscStatus;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FetchSampler {

    public static BaseResponse fetchUser(JSONObject arguments) {
        String message = "成功获取用户资源信息.";
        try{
            UserService.bean.fetchUser(arguments);
        }  catch (Exception e) {
            log.error("同步用户资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步用户资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchImage(JSONObject arguments) {
        String message = "成功获取资产信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                PropertyService.bean.fetchProperty(request,arguments,request.getResourceType());
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitPropertyDataJob(request,arguments,new String[]{ResourceEnum.IMAGE.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资产数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资产数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchModel(JSONObject arguments) {
        String message = "成功获取资产信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                PropertyService.bean.fetchProperty(request,arguments,request.getResourceType());
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitPropertyDataJob(request,arguments,new String[]{ResourceEnum.MODEL.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资产数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资产数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchAlgo(JSONObject arguments) {
        String message = "成功获取资产信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                PropertyService.bean.fetchProperty(request,arguments,request.getResourceType());
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitPropertyDataJob(request,arguments,new String[]{ResourceEnum.ALGO.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资产数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资产数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDataset(JSONObject arguments) {
        String message = "成功获取资产信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                PropertyService.bean.fetchProperty(request,arguments,request.getResourceType());
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitPropertyDataJob(request,arguments,new String[]{ResourceEnum.DATASET.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资产数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资产数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchResourceGroup(JSONObject arguments) {
        String message = "成功获取资源组信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                ResourceGroupService.bean.fetchResourceGroup(request,arguments);

            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitUserDataJob(request,arguments,new String[]{ResourceEnum.RESOURPCEGROUP.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资源组数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资源组数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDevEnv(JSONObject arguments) {
        String message = "成功获取开发环境信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "dev_env"://同步开发环境
                        DevEnvService.bean.fetchDevEnv(request,arguments);
                        break;
                    case "keypair"://同步密钥对
                        DevEnvService.bean.fetchKeyPair(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitDevEnvDataJob(request,arguments,new String[]{ResourceEnum.DEV_ENV.getValue(),ResourceEnum.KEYPAIR.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步开发环境数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步开发环境数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchDevEnvPerf(JSONObject arguments) {
        String message = "成功获取开发环境性能信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "dev_env"://同步开发环境
                        DevEnvService.bean.fetchDevEnvPerf(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitDevEnvDataJob(request,arguments,new String[]{ResourceEnum.DEV_ENV.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步开发环境性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步开发环境性能失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchFile(JSONObject arguments) {
        String message = "成功获取文件信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "file"://同步开发环境
                        FileService.bean.fetchFile(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitUserDataJob(request,arguments,new String[]{ResourceEnum.FILE.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步资产数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步资产数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse test(JSONObject arguments) {
        String message1 = "发送测试.";
        try {
            TscStatus tscStatus = TscStatus.FINISH;
            RouteInfo routeInfo = new RouteInfo("cmp_atlas_exchange", "cmp_atlas_resource");
            TscInfo.Message message = new TscInfo.Message();
            message.setDispatchId("D-********-5");
            message.setBatchId("D-********-50000");
            message.setCount(1);//数据标记版本最大刻度
            message.setEndStatus(tscStatus);
            RabbitRoute.routeMessage(message, routeInfo);
        }  catch (Exception e) {
            log.error("发送测试失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "发送测试失败");
        }
        return BaseResponse.SUCCESS.of(message1);
    }

    public static BaseResponse fetchReasonTask(JSONObject arguments) {
        String message = "成功获取任务信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "reason"://同步大模型推理
                        TaskService.bean.fetchReasonTask(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitTaskDataJob(request,arguments,new String[]{ResourceEnum.REASON.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步任务失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchReasonTaskPerf(JSONObject arguments) {
        String message = "成功获取任务信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "reasonPerf"://同步大模型推理
                        TaskService.bean.fetchReasonTaskPerf(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitTaskDataJob(request,arguments,new String[]{ResourceEnum.REASON_PERF.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步任务失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchFineTask(JSONObject arguments) {
        String message = "成功获取任务信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "fine"://同步大模型调优
                        TaskService.bean.fetchFineTask(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitFineTaskDataJob(request,arguments,new String[]{ResourceEnum.FINE.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步任务失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchFineTaskPerf(JSONObject arguments) {
        String message = "成功获取任务信息.";
        try {
            DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
            if(ObjectUtil.isNotNull(request) && ObjectUtil.isNotEmpty(request.getIds())) {//若ids不为空时，获取指定资源，否则获取列表并拆分dataJob
                switch (request.getResourceType()) {
                    case "finePerf"://同步大模型调优
                        TaskService.bean.fetchFineTaskPerf(request,arguments);
                        break;
                    default:
                        return BaseResponse.FAIL_PARAM_EMPTY.of("未匹配到resourceType:" + request.getResourceType() + "的处理方法.");
                }
            }else{
                //获取云主机列表，并拆分dataJob
                return new GourdJobResponse(AccountService.bean.splitFineTaskDataJob(request,arguments,new String[]{ResourceEnum.FINE_PERF.getValue()}),message);
            }
        }  catch (Exception e) {
            log.error("同步任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步任务失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchAzone(JSONObject arguments) {
        String message = "成功获取可用域资源信息.";
        try{
            PlatAZoneService.bean.fetchRegionAndZone(arguments);
        }  catch (Exception e) {
            log.error("同步可用域资源数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步可用域资源数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse fetchTag(JSONObject arguments) {
        String message = "成功获取标签信息.";
        try {
            TagService.bean.fetchTag(arguments);
        }  catch (Exception e) {
            log.error("同步标签数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "同步标签数据失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse consume(JSONObject arguments) {
        String message = "消费";
        try{

        }  catch (Exception e) {
            log.error("消费失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "消费失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


}
