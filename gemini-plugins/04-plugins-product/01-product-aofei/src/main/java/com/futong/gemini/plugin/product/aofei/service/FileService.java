
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.model.atlas.entity.CaDatahubFile;
import com.futong.gemini.model.atlas.entity.CaDatahubProperty;
import com.futong.gemini.model.atlas.entity.CaDatahubPropertyDatafile;
import com.futong.gemini.model.atlas.entity.CaDatahubPropertyVersion;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.DownloadTypeEnum;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.*;
import com.futong.gemini.plugin.product.aofei.util.JobUtils;
import com.futong.gemini.plugin.product.aofei.util.JsonFieldNameConverter;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.temporal.ValueRange;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class FileService {

    public static final FileService bean = new FileService();

    public void fetchFile(DescribeAofeiRequest request, JSONObject arguments) {
        List<CaDatahubFile> fileList = new ArrayList<>();
        List<CaDatahubFile> folderList = new ArrayList<>();
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String fileUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFileUrl(cloudAccessBean.getScvmmRole()),null);
        String json = "{ \"fileName\": \"\",\"path\": \"\",\"requestBegin\": 1,\"requestFileAccount\":100,\"userId\": 12}";
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, json, fileUrl, TypeEnum.POST.getValue(), TypeEnum.RESULT.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDatahubFile file = Converts.toFile(BaseClient.auths.get(), tempObj,ResourceEnum.FILE.getValue(),null);
                if (ObjectUtil.isNotNull(file)) {
                    fileList.add(file);
                    if("1".equals(file.getIs_directory()))
                        folderList.add(file);
                }
            });
        }
        getFile(fileList,folderList,request, arguments);
        // 推送文件数据
        log.info("推送文件数量：{}",fileList.size());
        BaseUtils.sendAtlasMessage(fileList, arguments,"",CaDatahubFile.class);
    }

    public void getFile(List<CaDatahubFile> fileList,List<CaDatahubFile> subfileList,DescribeAofeiRequest request, JSONObject arguments) {
        try {
            List<CaDatahubFile> folderList = new ArrayList<>();
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String fileUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFileUrl(cloudAccessBean.getScvmmRole()),null);
            for(CaDatahubFile file : subfileList) {
                String json = "{ \"fileName\": \"\",\"path\": \"/"+file.getFile_name()+"\",\"requestBegin\": 1,\"requestFileAccount\":100,\"userId\": 12}";
                JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, json, fileUrl, TypeEnum.POST.getValue(), TypeEnum.RESULT.getValue());
                if (ObjectUtil.isNotEmpty(array)) {
                    array.forEach(temp -> {
                        JSONObject tempObj = (JSONObject) temp;
                        CaDatahubFile subfile = Converts.toFile(BaseClient.auths.get(), tempObj,ResourceEnum.FILE.getValue(),file.getId());
                        if (ObjectUtil.isNotNull(subfile)) {
                            fileList.add(subfile);
                            if("1".equals(subfile.getIs_directory()))
                                folderList.add(subfile);
                        }
                    });
                }
            }
//            getFile(fileList, folderList, request, arguments);
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static BaseResponse fetchFolder(JSONObject arguments){
        List<CaDatahubFile> fileList = new ArrayList<>();
        try {
            List<CaDatahubFile> files = new ArrayList<>();
            log.info("获取文件夹接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            FileFolderRequest request = BaseClient.bodys.get().toJavaObject(FileFolderRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            String fileUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFileUrl(cloudAccessBean.getScvmmRole()),null);
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String userId = null;
            String jsonStr = cloudAccessBean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                if("1".equals(request.getType())) {
                    userId = jsonObject.getString("userId");
                }
            }
            String json = "{ \"fileName\": \""+request.getFileName()+"\",\"path\": \""+request.getPath()+"\",\"requestBegin\": 1,\"requestFileAccount\":100,\"userId\": "+userId+"}";
            AofeiToken userToken = Converts.getUserToken(cloudAccessBean);
            JSONArray array = Converts.fetchResourceToJsonArray(userToken, fileUrl,json,TypeEnum.RESULT.getValue());

            if (ObjectUtil.isNotEmpty(array)) {
                array.forEach(temp -> {
                    JSONObject tempObj = (JSONObject) temp;
                    CaDatahubFile file = Converts.toFile(BaseClient.auths.get(), tempObj,ResourceEnum.FILE.getValue(),null);
                    files.add(file);
                });
            }
            fileList = files.stream().filter(file -> StringUtils.isEmpty(request.getFileType())||request.getFileType().equals(file.getIs_directory())).collect(Collectors.toList());
            //fileList转成JSONarray

        }  catch (Exception e) {
            log.error("获取文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取文件夹失败");
        }
        JSONArray jsonArray = new JSONArray();
        for (CaDatahubFile file : fileList) {
            jsonArray.add(file);
        }

        JSONArray camelCaseArray = JsonFieldNameConverter.convertToCamelCase(jsonArray);
        return new BaseDataResponse<>(camelCaseArray);
    }

    public static BaseResponse fetchPropertyFolder(JSONObject arguments){
        String message = "获取文件夹.";
        List<CaDatahubPropertyDatafile> fileList = new ArrayList<>();
        try {
            List<CaDatahubPropertyDatafile> files = new ArrayList<>();
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            PropertyFileFolderRequest request = BaseClient.bodys.get().toJavaObject(PropertyFileFolderRequest.class);
            String frl = "/data/queryFileList";
            if("image".equals(request.getType())) {
                frl = "/image/queryImageFileList";
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String filejson = "{\"current\": 1, \"currentPath\": \""+request.getCurrentPath()+"\", \"id\": \"" + request.getId()+ "\", \"size\": 100, \"versionId\": \"" + request.getVersionId() + "\"}";
            AofeiToken userToken = Converts.getUserToken(cloudAccessBean);
            JSONArray array = Converts.fetchResourceToJsonArray(userToken, URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDatahubUrl(cloudAccessBean.getScvmmRole()), new String[]{frl}), filejson, TypeEnum.ROWS.getValue());

            if (ObjectUtil.isNotEmpty(array)) {
                array.forEach(temp -> {
                    JSONObject tempObj = (JSONObject) temp;
                    CaDatahubPropertyDatafile file = Converts.toPropertyDataFile(BaseClient.auths.get(), tempObj, request.getType(),request.getId(),request.getVersionId(),request.getParentId());
                    if (ObjectUtil.isNotNull(file))
                        files.add(file);
                });
                fileList = files.stream().filter(file -> StringUtils.isEmpty(request.getFileType())||request.getFileType().equals(file.getFile_type())).collect(Collectors.toList());
            }
        }  catch (Exception e) {
            log.error("获取文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取文件夹失败");
        }

        JSONArray jsonArray = new JSONArray();
        for (CaDatahubPropertyDatafile file : fileList) {
            jsonArray.add(file);
        }
        JSONArray camelCaseArray = JsonFieldNameConverter.convertToCamelCase(jsonArray);
        return new BaseDataResponse<>(camelCaseArray);
    }

    /**
     * 创建文件夹：镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse createPropertyFolder(JSONObject arguments){
        try {
            log.info("创建文件夹接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreatePropertyFolderRequest request = BaseClient.bodys.get().toJavaObject(CreatePropertyFolderRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFolderCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建文件夹失败");
        }
    }

    /**
     * 上传文件（夹）镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse uploadPropertyFile(JSONObject arguments){
        String message = "文件（夹）镜像、数据集、模型、算法上传成功";
        try {
            log.info("上传文件（夹）镜像、数据集、模型、算法接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileUploadFile(cloudAccessBean.getScvmmRole()), null);
            HttpClientUtil.postFormData(url, arguments, config);
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("上传文件（夹）镜像、数据集、模型、算法失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "上传文件（夹）镜像、数据集、模型、算法失败");
        }
    }

    /**
     * 上传文件（夹）
     * @param arguments
     * @return
     */
    public static BaseResponse uploadSmallFile(JSONObject arguments){
        try {
            log.info("上传文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFileUploadUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postFormData(url, arguments, config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("上传文件（夹）失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "上传文件（夹）失败");
        }
    }

    /**
     * 上传大文件
     * @param arguments
     * @return
     */
    public static BaseResponse uploadLargeFile(JSONObject arguments){
        String message = "大文件上传成功";
        try {
            log.info("上传大文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getLargeFileUploadUrl(cloudAccessBean.getScvmmRole()), null);
            HttpClientUtil.postFormData(url, arguments, config);
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("上传大文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "上传大文件失败");
        }
    }

    /**
     * 文件下载
     * @param arguments
     * @return
     */
    public static BaseResponse downloadFile(JSONObject arguments){
        String message = "文件下载成功.";
        try {
            log.info("下载文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DownloadFileRequest request = BaseClient.bodys.get().toJavaObject(DownloadFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            if(DownloadTypeEnum.PROPERTY_DOWNLOAD.getCode().equals(request.getDownloadType())){
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), new String[]{"/"+request.getPath()}, null);
                HttpClientUtil.getFile(url,config);
            }else{
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFileDownloadUrl(cloudAccessBean.getScvmmRole()), null);
                HttpClientUtil.postFile(url,JSON.toJSONString(request),config,request.getFileName());
            }
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("下载文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "下载文件失败");
        }
    }

    /**
     * 文件夹下载
     * @param arguments
     * @return
     */
    public static BaseResponse downloadFolder(JSONObject arguments){
        String message = "文件夹下载成功.";
        try {
            log.info("下载文件夹接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DownloadFileRequest request = BaseClient.bodys.get().toJavaObject(DownloadFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFolderDownloadUrl(cloudAccessBean.getScvmmRole()), null);
            HttpClientUtil.postFile(url,JSON.toJSONString(request),config,request.getFileName()+".zip");
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("下载文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "下载文件夹失败");
        }
    }

    /**
     * 文件下载（公共文件）
     * @param arguments
     * @return
     */
    public static BaseResponse downloadPublicFile(JSONObject arguments){
        String message = "文件批量下载成功.";
        try {
            log.info("批量下载文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DownloadPublicFileRequest request = BaseClient.bodys.get().toJavaObject(DownloadPublicFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPublicFilesDownloadUrl(cloudAccessBean.getScvmmRole()), null);
            if(BooleanUtil.isFalse(request.getIsBatch())){
                url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPublicFileDownloadUrl(cloudAccessBean.getScvmmRole()), null);
            }
            HttpClientUtil.postFile(url,JSON.toJSONString(request),config,request.getFileName()==null?"PublicFile": request.getFileName());
            return BaseResponse.SUCCESS.of(message);
        }catch (Exception e){
            log.error("下载文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "下载文件失败");
        }
    }

    /**
     * 读取文件：镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse readPropertyFile(JSONObject arguments){
        try {
            log.info("读取文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            ReadPropertyFileRequest request = BaseClient.bodys.get().toJavaObject(ReadPropertyFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileReadUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("读取文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "读取文件失败");
        }
    }

    /**
     * 修改文件：镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse updatePropertyFile(JSONObject arguments){
        try {
            log.info("修改文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UpdatePropertyFile request = BaseClient.bodys.get().toJavaObject(UpdatePropertyFile.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("修改文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改文件失败");
        }
    }

    /**
     * 获取文件Url：镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse queryPropertyFileUrl(JSONObject arguments){
        try {
            log.info("获取文件Url接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            ReadPropertyFileRequest request = BaseClient.bodys.get().toJavaObject(ReadPropertyFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileDownloadUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("获取文件Url失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取文件Url失败");
        }
    }

    /**
     * 删除文件：镜像、数据集、模型、算法
     * @param arguments
     * @return
     */
    public static BaseResponse deletePropertyFile(JSONObject arguments){
        try {
            log.info("删除文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeletePropertyFileRequest request = BaseClient.bodys.get().toJavaObject(DeletePropertyFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除文件失败");
        }
    }


    /**
     * 创建文件夹
     * @param arguments
     * @return
     */
    public static BaseResponse createFolder(JSONObject arguments){
        try {
            log.info("创建文件夹接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateFolderRequest request = BaseClient.bodys.get().toJavaObject(CreateFolderRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFolderCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建文件夹失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建文件夹失败");
        }
    }

    /**
     * 文件（夹）是否被共享
     * @param arguments
     * @return
     */
    public static BaseResponse findShareFile(JSONObject arguments){
        try {
            log.info("查询文件（夹）是否被共享接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            FileMergeFileRequest request = BaseClient.bodys.get().toJavaObject(FileMergeFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFindShareFileUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("查询文件（夹）是否被共享失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询文件（夹）是否被共享失败");
        }
    }


    /**
     * 重命名文件
     * @param arguments
     * @return
     */
    public static BaseResponse renameFile(JSONObject arguments){
        try {
            log.info("重命名文件（夹）接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            RenameFileRequest request = BaseClient.bodys.get().toJavaObject(RenameFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFileRenameUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("重命名文件（夹）失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重命名文件（夹）失败");
        }
    }

    /**
     * 删除文件
     * @param arguments
     * @return
     */
    public static BaseResponse deleteFile(JSONObject arguments){
        try {
            log.info("删除文件接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeleteFileRequest request = BaseClient.bodys.get().toJavaObject(DeleteFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFileDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除文件失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除文件失败");
        }
    }

    /**
     * 资产文件上传分片合并
     * @param arguments
     * @return
     */
    public static BaseResponse mergePropertyFile(JSONObject arguments){
        try {
            log.info("资产文件上传分片合并接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            MergePropertyFileRequest request = BaseClient.bodys.get().toJavaObject(MergePropertyFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getPropertyFileMergeFile(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url,JSONObject.toJSONString(request),config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("资产文件上传分片合并失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "资产文件上传分片合并失败");
        }
    }

    /**
     * 大文件上传分片合并
     * @param arguments
     * @return
     */
    public static BaseResponse mergeLargeFile(JSONObject arguments){
        try {
            log.info("大文件上传分片合并接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            FileMergeFileRequest request = BaseClient.bodys.get().toJavaObject(FileMergeFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getLargeFileMergeUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url,JSON.toJSONString(request),config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("大文件上传分片合并失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "大文件上传分片合并失败");
        }
    }

    /**
     * 检查文件是否存在
     * @param arguments
     * @return
     */
    public static BaseResponse checkFileExist(JSONObject arguments){
        try {
            log.info("检查文件是否存在接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CheckFileRequest request = BaseClient.bodys.get().toJavaObject(CheckFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getFileCheckExistUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url,JSON.toJSONString(request),config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("检查文件是否存在失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "检查文件是否存在失败");
        }
    }

    public static BaseResponse getPod(JSONObject arguments){
        JSONArray podList = new JSONArray();
        try {
            log.info("获取pod接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            String fileUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneTaskPodListUrl(cloudAccessBean.getScvmmRole()),null);
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            AofeiToken userToken = Converts.getUserToken(cloudAccessBean);
            podList = Converts.fetchResourceToJsonArray(userToken, fileUrl,BaseClient.bodys.get().toString(),TypeEnum.ROWS.getValue());

        }  catch (Exception e) {
            log.error("获取pod失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取pod失败");
        }
        return new BaseDataResponse<>(podList);
    }

    public static BaseResponse getLog(JSONObject arguments){
        JSONArray logList = new JSONArray();
        try {
            log.info("获取日志接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            String fileUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getLogUrl(cloudAccessBean.getScvmmRole()),null);
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            AofeiToken userToken = Converts.getUserToken(cloudAccessBean);
            try {
                logList = Converts.fetchResourceToJsonArray(userToken, fileUrl, BaseClient.bodys.get().toString(), TypeEnum.LOGCONTENT.getValue());
            }catch (Exception e) {
                e.printStackTrace();
            }

        }  catch (Exception e) {
            log.error("获取日志失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取日志失败");
        }
        return new BaseDataResponse<>(logList);
    }
}
