package com.futong.gemini.plugin.product.aofei.enums;

public enum DownloadTypeEnum {

    PROPERTY_DOWNLOAD("propertyModule","资产管理文件下载"),

    FILE_DOWNLOAD("fileModule","文件管理文件下载");

    private String code;

    private String name;

    private DownloadTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
