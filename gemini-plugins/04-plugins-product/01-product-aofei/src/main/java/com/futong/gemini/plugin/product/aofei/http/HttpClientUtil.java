package com.futong.gemini.plugin.product.aofei.http;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTServletUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.ComputeUtil;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.HttpResult;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiTokenParam;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.futong.gemini.plugin.sdk.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.*;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class HttpClientUtil {
    private static final String HTTPS = "https";

    /***
     * 获取token
     * @param bean
     * @return
     */
    public  static AofeiToken getToken(CloudAccessBean bean){
        AofeiToken token =null;
        try {
            token = (AofeiToken) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId());
        } catch (Exception e){
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId());
        }
        if(ObjectUtil.isNull(token)){
            token = getAofeiToken(bean);
            log.info("调用云上接口获取token并存入缓存内  = {}",JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, BaseClient.auths.get().getCmpId(),token);
        }
        return token;
    }
    public  static AofeiToken getAofeiToken(CloudAccessBean bean) {
        AofeiToken token =null;
        /**拼接获取 token url */
        String url = URLUtils.bean.makeUrl(bean, URLUtils.bean.getLoginUrl(bean.getScvmmRole()),null);
        AofeiTokenParam param = new AofeiTokenParam();
        param.setUsername(bean.getUsername());
        param.setPassword(ComputeUtil.getEncryptPwd(bean.getPassword(),null));
        String jsonStr = bean.getJsonStr();
        if(jsonStr!=null) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            param.setUserid(jsonObject.getString("userId"));
        }

        try {
            log.info("开始调用接口:{}", System.currentTimeMillis());
            if("sub".equals(bean.getAccountType())) {
                url = URLUtils.bean.makeUrl(bean, URLUtils.bean.getTokenUrl(bean.getScvmmRole()),null);;
                log.info("调用子账号云上url:{},用户名:{},密码:{},加密密码:{}",url,bean.getUsername(),bean.getPassword(),param.getPassword());
                token = fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(),bean);
            }else {
                log.info("调用云上url:{},用户名:{},密码:{},加密密码:{}",url,bean.getUsername(),bean.getPassword(),param.getPassword());
                token = fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(),bean);
            }
            log.info("结束调用接口:{}", System.currentTimeMillis());
        } catch (Exception e) {
            throw  new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        }
        return token;
    }
    public static AofeiToken fetchToken(String url, String json, HttpClientConfig config, CloudAccessBean param) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Plat-Route-id", param.getScvmmPort());
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            String result = getResponseStr(response, config);
            AofeiToken token = JSON.parseObject(result, AofeiToken.class);
            if(token.getToken()!=null) {
                AofeiToken.DataToken data = new AofeiToken.DataToken();
                data.setToken(token.getToken());
                token.setData(data);
            }
            return token;
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public  static String get(CloudAccessBean bean, DescribeAofeiRequest request, String[] url, String[] args){
       String  newUrl  = URLUtils.bean.makeUrl(bean, url,args);
        //设置请求头，添加token
        HttpClientConfig config = new HttpClientConfig();
        config.addHeader(TokenEnum.AUTH_TOKEN.getValue(),request.getAuthToken().getData().getToken());
       return  get(newUrl,config);
    }
    /**
     *  根据摘要认证 url构建HttpClient
     * @param url
     * @return
     */
    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            URL urlObj = new URL(url);
            if (url.toLowerCase().startsWith(HTTPS)) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }
    /**
     * Get http请求
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @return 响应结果字符串
     */
    public static String get(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpGet httpGet = new HttpGet(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            httpGet.addHeader("Content-Type","application/json");
            httpGet.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpGet);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
    public static boolean isJsonarray(String jsonString){
        try {
            new JSONArray(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String post(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            int connectTimeout = 60000; // 连接超时时间（5秒）
            int socketTimeout = 60000;  // 读取超时时间（5秒）
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * Post请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String postByCode(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            int connectTimeout = 5000; // 连接超时时间（5秒）
            int socketTimeout = 5000;  // 读取超时时间（5秒）
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStrByCode(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    public static String postStream(String url, String json, HttpClientConfig config) {
        url = "https://60.191.123.122:40302/inference/cce-pb5p2t34e50f/v1/chat/completions";
        HttpPost httpPost = new HttpPost("https://60.191.123.122:40302/inference/cce-pb5p2t34e50f/v1/chat/completions");
        CloseableHttpClient  httpClient = buildHttpClient(url);
        // 设置SSE必需的请求头
        httpPost.setHeader("Accept", "text/event-stream");
        httpPost.setHeader("Cache-Control", "no-cache");
        httpPost.setHeader("Connection", "keep-alive");
        httpPost.setHeader("Authorization", "Bearer eyJhbGciOiJIUzI1NiJ9.eyJjbHVzdGVyVHlwZSI6ImNsb3VkU2NlbmFyaW8iLCJzeXNVc2VyTmFtZSI6InVuaWNsb3VkdXNlcjAyNjgzOTIyNjUiLCJuaWNrTmFtZSI6InRlc3R1c2VyIiwicm9sZUlkIjoiNDAwNyIsImdyb3VwSWQiOiIxMDEiLCJzeXNHcnBJZCI6IjIwMDAiLCJpc3MiOiJpc3N1ZXJfY29tbW9uX3VzZXIiLCJ0ZW5hbnRHaWQiOjIwMDAsInVzZXJOYW1lIjoidW5pY2xvdWR1c2VyMDI2ODM5MjI2NSIsInN5c0dycE5hbWUiOiJ1bmljbG91ZGdyb3VwODUzMTIwNTk4MiIsInVzZXJJZCI6IjEyIiwiZ3JvdXBOYW1lIjoiSDNDIiwidGVuYW50TmFtZSI6InVuaWNsb3VkZ3JvdXA4NTMxMjA1OTgyIiwidGVuYW50SWQiOjEwMSwic3lzVXNlcklkIjoiMjAwOSIsImV4cCI6MTc0Mjk2ODIwNCwibG9naW5fdXNlcl9rZXkiOiI3Yzk0MDljZi0xMzUwLTQ4YzItYTlkNS1lNzM4ZDdlMmI5N2MifQ.qrlws7BRpE5hZ-fX0O2gwOj_-mTfgjuPrWZAN3Z19dA");
        httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
        try {
            HttpResponse response = httpClient.execute(httpPost);
            InputStream inputStream = response.getEntity().getContent();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.isEmpty()) {
                    System.out.println("Received: " + line);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    /**
     * Put请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String put(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPut httpPost = new HttpPut(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }


    /**
     * Put请求，请求内容必须为JSON格式的字符串
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String putByCode(String url, String json, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPut httpPost = new HttpPut(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/json");
            EntityBuilder entityBuilder = EntityBuilder.create();
            entityBuilder.setText(json);
            entityBuilder.setContentType(ContentType.APPLICATION_JSON);
            entityBuilder.setContentEncoding(config.getCharset());
            HttpEntity requestEntity = entityBuilder.build();
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            return getResponseStrByCode(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    private static String getResponseStr(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        String entity = EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");
//        log.info("httpResult:{}",entity);
//        HttpResult httpResult = JSON.parseObject(entity,HttpResult.class);
//        httpResult.setCode(httpResult.getErrorCode()==null?httpResult.getCode():httpResult.getErrorCode());
//        if(httpResult.getCode() != 200){
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,httpResult.getMessage());
//        }
        return entity;
    }

    private static String getResponseStrByCode(HttpResponse response, HttpClientConfig config) throws Exception{
        if(response.getStatusLine().getStatusCode() >= 400){
            String msg = EntityUtils.toString(response.getEntity(), config.getCharset());
            if(StringUtils.isEmpty(msg)){
                msg = "StatusCode: " + response.getStatusLine().getStatusCode();
            }
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,msg);
        }
        String entity = EntityUtils.toString(response.getEntity(), config.getCharset()).replace("@type", "type");
//        log.info("httpResult:{}",entity);
        HttpResult httpResult = JSON.parseObject(entity,HttpResult.class);
        httpResult.setCode(httpResult.getErrorCode()==null?httpResult.getCode():httpResult.getErrorCode());
        if(httpResult.getCode() != 200){
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD ,httpResult.getMessage());
        }
        return entity;
    }

    /**
     * 删除
     * @param url
     * @param config
     * @return
     */
    public static String delete(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpDelete httpDelete = new HttpDelete(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * 删除
     * @param url
     * @param config
     * @return
     */
    public static String deletePost(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpPost httpDelete = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStr(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * 删除
     * @param url
     * @param config
     * @return
     */
    public static String deletePostByCode(String url, HttpClientConfig config) {
        CloseableHttpClient httpClient = buildHttpClient(url);
        HttpPost httpDelete = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpDelete.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpDelete.addHeader(key, header.get(key));
            }
            httpDelete.addHeader("Content-Type","application/json");
            httpDelete.addHeader("Accept","application/json");
            HttpResponse response = httpClient.execute(httpDelete);
            return getResponseStrByCode(response, config);
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * Post请求，请求格式必须为formData
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param arguments   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static String postFormData(String url, JSONObject arguments, HttpClientConfig config) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            if(FileService.get().length > 0){
                for (InputStreamSource inputStreamSource : FileService.get()) {
                    MultipartFile multipartFile = (MultipartFile) inputStreamSource;
                    MultipartEntityBuilder builder = MultipartEntityBuilder.create().setCharset(StandardCharsets.UTF_8);
                    JSONObject fileObject = arguments.getJSONObject("body").getJSONObject("file");
                    for (String key : fileObject.keySet()) {
                        if("chunk".equals(key) || "file".equals(key)){
                            builder.addBinaryBody(key, multipartFile.getBytes(), ContentType.create("multipart/form-data", Consts.UTF_8), multipartFile.getOriginalFilename());
                        }else {
                            builder.addTextBody(key,fileObject.getString(key),ContentType.create("text/plain", Consts.UTF_8));
                        }
                    }
                    httpPost.setEntity(builder.build());
                    HttpResponse response = httpClient.execute(httpPost);
                    String entity =  getResponseStr(response, config);
                    return entity;
                }
            }else{
                return "附件为空";
            }
            return null;
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * Post请求，下载文件
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @param json   JSON格式的字符串
     * @return 响应结果字符串
     */
    public static void postFile(String url, String json, HttpClientConfig config,String downFileName) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
//            httpPost.addHeader("Content-Type","application/json");
//            httpPost.addHeader("Accept","application/octet-stream");
            StringEntity requestEntity = new StringEntity(json, StandardCharsets.UTF_8);
            requestEntity.setContentType("application/json");
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                byte[] fileBytes = EntityUtils.toByteArray(entity);
                InputStream inputStream = new ByteArrayInputStream(fileBytes);
                FTServletUtils.download(inputStream,URLEncoder.encode(downFileName, StandardCharsets.UTF_8.toString()));
//                InputStreamResource resource = new InputStreamResource(inputStream);
//                HttpHeaders headers = new HttpHeaders();
//                headers.add("Content-Disposition", "attachment; filename="+URLEncoder.encode(downFileName, StandardCharsets.UTF_8.toString()));
//                return ResponseEntity.ok()
//                        .headers(headers)
//                        .contentLength(fileBytes.length)
//                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
//                        .body(resource);
            }
//            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * post请求，下载zip包
     * @param url
     * @param json
     * @param config
     * @param downFolderName
     * @return
     */
    public static ResponseEntity<InputStreamResource> postFolder(String url, String json, HttpClientConfig config,String downFolderName) {
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpPost.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpPost.addHeader(key, header.get(key));
            }
            httpPost.addHeader("Content-Type","application/json");
            httpPost.addHeader("Accept","application/zip");
            StringEntity requestEntity = new StringEntity(json, StandardCharsets.UTF_8);
            requestEntity.setContentType("application/json");
            httpPost.setEntity(requestEntity);
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream inputStream = entity.getContent();
                HttpHeaders headers = new HttpHeaders();
                headers.add("Content-Disposition", "attachment;filename="+URLEncoder.encode(downFolderName, StandardCharsets.UTF_8.toString()));
                headers.add("Content-Type", "application/octet-stream");
                return new ResponseEntity<>(new InputStreamResource(inputStream),headers, HttpStatus.OK);
            }
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }

    /**
     * Post请求
     * @param url    请求地址
     * @param config 配置项，如果null则使用默认配置
     * @return 响应结果字符串
     */
    public static void getFile(String url, HttpClientConfig config) {
        String downFileName = url.substring(url.lastIndexOf("/")+1);
        CloseableHttpClient  httpClient = buildHttpClient(url);
        HttpGet httpGet = new HttpGet(url);
        if (config == null) {
            config = new HttpClientConfig();
        }
        try {
            httpGet.setConfig(config.buildRequestConfig());
            Map<String, String> header = config.getHeader();
            for (String key : header.keySet()) {
                httpGet.addHeader(key, header.get(key));
            }
            httpGet.addHeader("Content-Type","application/json");
            httpGet.addHeader("Accept","application/octet-stream");
            HttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                byte[] fileBytes = EntityUtils.toByteArray(entity);
                InputStream inputStream = new ByteArrayInputStream(fileBytes);
                FTServletUtils.download(inputStream,URLEncoder.encode(downFileName, StandardCharsets.UTF_8.toString()));
            }
        } catch (Exception e) {
            log.error("HttpClient查询失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error("HttpClient关闭连接失败", e);
            }
        }
    }
}
