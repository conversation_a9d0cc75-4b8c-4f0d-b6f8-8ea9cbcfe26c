
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.model.atlas.entity.*;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.CreateDevEnvRequest;
import com.futong.gemini.plugin.product.aofei.request.CreateFolderRequest;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.request.OpenDevEnvRequest;
import com.futong.gemini.plugin.product.aofei.util.AofeiUtils;
import com.futong.gemini.plugin.product.aofei.util.ComputeUtil;
import com.futong.gemini.plugin.product.aofei.util.JobUtils;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiTokenParam;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class DevEnvService {

    public static final DevEnvService bean = new DevEnvService();

    public void fetchDevEnv(DescribeAofeiRequest request, JSONObject arguments) {
        final String subCmpId = request.getIds()==null?"":request.getIds().get(0).getCmpId();
        Integer fecthSize = request.getFecthSize()-1;
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        List<CaDevEnv> envList = new ArrayList<>();
        List<CaDevEnvEvent> eventList = new ArrayList<>();
        List<CaDevEnvPort> portList = new ArrayList<>();
        List<CaDevEnvVariable> varList = new ArrayList<>();
        // 推送开发环境数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()),null);
        devEnvUrl+="?page="+fecthSize+"&size="+count;
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDevEnv env = Converts.toDevenv(BaseClient.auths.get(), tempObj,ResourceEnum.DEV_ENV.getValue(),subCmpId);
                fetchEvent(env,request,arguments,eventList,subCmpId);
                fetchCustom(env,request,arguments,portList,varList,subCmpId);
                if (ObjectUtil.isNotNull(env))
                    envList.add(env);
            });
        }
        // 推送开发环境数据
        log.info("推送开发环境数量：{}",envList.size());
        BaseUtils.sendAtlasMessage(envList, arguments,subCmpId,CaDevEnv.class);
        log.info("推送端口数量：{}",portList.size());
        BaseUtils.sendAtlasMessage(portList, arguments,subCmpId,CaDevEnvPort.class);
        //推送变量数据
        log.info("推送变量数量：{}",varList.size());
        BaseUtils.sendAtlasMessage(varList, arguments,subCmpId,CaDevEnvVariable.class);
        // 推送事件数据
        log.info("推送事件数量：{}",eventList.size());
        BaseUtils.sendAtlasMessage(eventList, arguments,subCmpId,CaDevEnvEvent.class);
        //推送端口数据
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void fetchDevEnvPerf(DescribeAofeiRequest request, JSONObject arguments) {
        final String subCmpId = request.getIds()==null?"":request.getIds().get(0).getCmpId();
        Integer fecthSize = request.getFecthSize()-1;
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        long time = System.currentTimeMillis();
        // 推送开发环境数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()),null);
        devEnvUrl+="?page="+fecthSize+"&size="+count;
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDevEnv env = Converts.toDevenv(BaseClient.auths.get(), tempObj,ResourceEnum.DEV_ENV.getValue(),subCmpId);
                JSONObject jsonObject = JSON.parseObject(env.getOpen_json());
                JSONObject obj = new JSONObject();
                obj.put("podNames", new String[]{jsonObject.getString("vcJobName")+"-pod-0"});
                obj.put("startTime",System.currentTimeMillis()-5*60*1000l);
                obj.put("endTime", System.currentTimeMillis());
                String devEnvPerfUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvPerfUrl(cloudAccessBean.getScvmmRole()),null);
                JSONArray list = Converts.convertData(request.getIds(), cloudAccessBean, obj.toString(),devEnvPerfUrl, TypeEnum.POST.getValue(), "");
                perfList.addAll(Converts.toPerfDetail(cloudAccessBean,tempObj,ResourceEnum.DEV_ENV.getValue(),jsonObject.getString("vcJobName")+"-pod-0",list,time,subCmpId));
            });
        }
        log.info("推送开发环境性能数量：{}", perfList.size());
        BaseUtils.sendAtlasPerfMessage(perfList, arguments,subCmpId,ResourcePerfDetail.class);
    }

    public void fetchKeyPair(DescribeAofeiRequest request, JSONObject arguments) {
        final String subCmpId = request.getIds()==null?"":request.getIds().get(0).getCmpId();
        List<CaKeyPair> keypairList = new ArrayList<>();
        // 推送开发环境数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String keyPairUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getKeyPairUrl(cloudAccessBean.getScvmmRole()),null);
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null,keyPairUrl+"?page=0&size=100", TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaKeyPair keypair = Converts.toKeyPair(BaseClient.auths.get(), tempObj,ResourceEnum.KEYPAIR.getValue(),subCmpId);
                if (ObjectUtil.isNotNull(keypair))
                    keypairList.add(keypair);
            });
        }
        // 推送密钥对数据
        log.info("推送密钥对数量：{}",keypairList.size());
        BaseUtils.sendAtlasMessage(keypairList, arguments,subCmpId,CaKeyPair.class);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void fetchEvent(CaDevEnv env,DescribeAofeiRequest request, JSONObject arguments,List<CaDevEnvEvent> eventList, String subCmpId) {
        // 推送开发环境数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()),new String[]{env.getOpen_id(),"events"});
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDevEnvEvent event = Converts.toDevEvent(BaseClient.auths.get(), tempObj,ResourceEnum.EVENT.getValue(),env.getId(),subCmpId);
                if (ObjectUtil.isNotNull(event))
                    eventList.add(event);
            });
        }
        // 推送镜像数据

    }

    public void fetchCustom(CaDevEnv env,DescribeAofeiRequest request, JSONObject arguments,List<CaDevEnvPort> portList,List<CaDevEnvVariable> varList, String subCmpId) {
        // 推送开发环境数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()),new String[]{"/"+env.getOpen_id()});
        JSONArray vars = Converts.convertData(request.getIds(), cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.CUSTOMENVS.getValue());
        JSONArray ports = Converts.convertData(request.getIds(), cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.CUSTOMPORTS.getValue());
        if (ObjectUtil.isNotEmpty(vars)) {
            vars.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDevEnvVariable var = Converts.toDevVariable(BaseClient.auths.get(), tempObj,ResourceEnum.EVENT.getValue(),env.getId(),subCmpId);
                if (ObjectUtil.isNotNull(var))
                    varList.add(var);
            });
        }

        if (ObjectUtil.isNotEmpty(ports)) {
            ports.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaDevEnvPort port = Converts.toDevPort(BaseClient.auths.get(), tempObj,ResourceEnum.EVENT.getValue(),env.getId(),subCmpId);
                if (ObjectUtil.isNotNull(port))
                    portList.add(port);
            });
        }

    }

    public static BaseResponse createDevEnv(JSONObject arguments) {
        String message = "成功创建开发环境.";
        try {
            log.info("创建开发环境接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateDevEnvRequest request = BaseClient.bodys.get().toJavaObject(CreateDevEnvRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()), null);
            String msg = AofeiUtils.createDevEnv(arguments, cloudAccessBean, url, request, config);
            if (!"OK".equals(msg)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, msg);
            }
        }  catch (Exception e) {
            log.error("创建开发环境失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建开发环境失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse startDevEnv(JSONObject arguments) {
        String message = "成功开启开发环境.";
        try {
            log.info("开启开发环境接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                try {
                    String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()), new String[]{"/"+instanceId});
                    HttpClientUtil.post(url+":start", "", config);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }  catch (Exception e) {
            log.error("开启开发环境失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "开启开发环境失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    public static BaseResponse openDevEnv(JSONObject arguments) {
        String message = "成功打开开发环境.";
        String openUrl = "";
        try {
            log.info("打开开发环境接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String token = Converts.getUserToken(cloudAccessBean).getData().getToken();
            OpenDevEnvRequest request = BaseClient.bodys.get().toJavaObject(OpenDevEnvRequest.class);
            String url = request.getUrl();
            String openId = request.getOpenId();
            openUrl = url+"/platform?platform=futong&access_token="+token+"&api="+url+"/devolopenv/notebook/"+openId+"/web/lab";
        }  catch (Exception e) {
            log.error("打开开发环境失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "打开开发环境失败");
        }
        return new BaseDataResponse<>(openUrl);
    }

    public static BaseResponse stopDevEnv(JSONObject arguments) {
        String message = "成功停止开发环境.";
        try {
            log.info("停止开发环境接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                try {
                    String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()), new String[]{"/"+instanceId});
                    HttpClientUtil.post(url+":close", "", config);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }  catch (Exception e) {
            log.error("停止开发环境失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "停止开发环境失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static BaseResponse deleteDevEnv(JSONObject arguments) {
        String message = "成功删除开发环境.";
        try {
            log.info("删除开发环境接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                try {
                    String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()), new String[]{"/"+instanceId});
                    HttpClientUtil.delete(url, config);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }  catch (Exception e) {
            log.error("删除开发环境失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除开发环境失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }


}
