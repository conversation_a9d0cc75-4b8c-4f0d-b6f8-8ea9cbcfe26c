
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.model.atlas.entity.CaDatahubProperty;
import com.futong.gemini.model.atlas.entity.CaResourceGroup;
import com.futong.gemini.model.atlas.entity.CaResourceGroupNode;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.ComputeUtil;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiTokenParam;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ResourceGroupService {

    public static final ResourceGroupService bean = new ResourceGroupService();

    public void fetchResourceGroup(DescribeAofeiRequest request, JSONObject arguments) {
        String subCmpId = request.getIds()==null?"":request.getIds().get(0).getCmpId();
        List<CaResourceGroup> rgList = new ArrayList<>();
        List<CaResourceGroupNode> rgNodeList = new ArrayList<>();
        fetchResourceGroupAndNode(request.getIds(),arguments,rgList,rgNodeList,subCmpId);
        // 推送资源组数据
        log.info("推送资源组数量：{}",rgList.size());
        BaseUtils.sendAtlasMessage(rgList, arguments,subCmpId,CaResourceGroup.class);
        // 推送资源组节点数据
        log.info("推送资源节点数量：{}",rgNodeList.size());
        BaseUtils.sendAtlasMessage(rgNodeList, arguments,subCmpId, CaResourceGroupNode.class);
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void fetchResourceGroupAndNode(List<AofeiUser> list, JSONObject arguments, List<CaResourceGroup> rgList, List<CaResourceGroupNode> rgNodeList, String subCmpId) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String rgUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getResourceGroupUrl(cloudAccessBean.getScvmmRole()),null);
        AofeiUser aofeiUser = null;
        if(list!=null) {
            aofeiUser = list.get(0);
        }
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTokenUrl(cloudAccessBean.getScvmmRole()), null);
        Integer deptId = 101;
        if(aofeiUser!=null) {
            String userId = aofeiUser.getUserId();
            AofeiTokenParam param = new AofeiTokenParam();
            param.setUsername(cloudAccessBean.getUsername());
            param.setPassword(ComputeUtil.getEncryptPwd(cloudAccessBean.getPassword(), null));
            param.setUserid(userId);
            try {
                AofeiToken aofeiToken = HttpClientUtil.fetchToken(url, JSON.toJSONString(param), HttpClientConfig.class.newInstance(), cloudAccessBean);
                String infoUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getInfoUrl(cloudAccessBean.getScvmmRole()), null);
                HttpClientConfig config = new HttpClientConfig();
                config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + aofeiToken.getData().getToken());
                String s = HttpClientUtil.get(infoUrl, config);
                cn.hutool.json.JSONObject entries = JSONUtil.parseObj(s);
                cn.hutool.json.JSONObject user = entries.getJSONObject("user");
                deptId = user.getInt("deptId");
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        String json = "{\"sysGroupId\": \""+deptId+"\"}";
        DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
        JSONArray array = Converts.fetchResourceResultToJsonArray(request.getAuthToken(), rgUrl,json, TypeEnum.RECORDS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                CaResourceGroup rg = Converts.toResourceGroup(BaseClient.auths.get(), tempObj,subCmpId);
                if (ObjectUtil.isNotNull(rg))
                    rgList.add(rg);
                JSONArray nodes = tempObj.getJSONArray(TypeEnum.NODElIST.getValue());
                if (ObjectUtil.isNotEmpty(nodes)) {
                    nodes.forEach(node -> {
                        JSONObject nodeObj = (JSONObject) node;
                        CaResourceGroupNode n = Converts.toResourceGroupNode(BaseClient.auths.get(),nodeObj,rg.getOpen_id(),subCmpId);
                        if (ObjectUtil.isNotNull(n))
                            rgNodeList.add(n);
                    });
                }
            });
        }
    }
}
