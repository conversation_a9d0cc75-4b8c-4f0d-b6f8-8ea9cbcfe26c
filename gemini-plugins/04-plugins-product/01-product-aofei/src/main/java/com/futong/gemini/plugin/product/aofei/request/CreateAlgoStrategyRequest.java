package com.futong.gemini.plugin.product.aofei.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CreateAlgoStrategyRequest implements Serializable {
    private String id;
    private String algoId;
    private String algoVersionId;
    private String bootFile;
    private String imageId;
    private String imageVersionId;
    private String modelId;
    private String modelVersionId;
    private String modelPath;
    private int taskType;
    private List<EnvironmentParameter> trainEnvParams;
    private List<HyperParameter> trainHyperParams;
    private String trainStrategy;


    @Data
    public static class EnvironmentParameter {
        private String name;
        private String value;
        private String description;
    }

    @Data
    public static class HyperParameter {
        private String name;
        private String value;
        private String description;
    }

}
