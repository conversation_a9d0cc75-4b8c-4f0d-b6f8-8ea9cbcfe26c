package com.futong.gemini.plugin.product.aofei.vo.token;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AofeiToken implements Serializable {

    private final long serialVersionUID = 1L;

    private Integer code;

    private boolean success;

    private String message;

    private DataToken data;

    private String token;

    @Data
    public static class DataToken {
        private String token;
        private boolean changePasswd;
    }
}
