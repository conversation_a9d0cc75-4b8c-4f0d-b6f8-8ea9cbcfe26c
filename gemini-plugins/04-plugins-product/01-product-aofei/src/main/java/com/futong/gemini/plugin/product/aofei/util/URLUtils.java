package com.futong.gemini.plugin.product.aofei.util;

import cn.hutool.core.util.ObjectUtil;
import com.futong.bean.CloudAccessBean;

public  class URLUtils {

    public static final URLUtils bean = new URLUtils();

    /**获取token信息*/
    private String[] loginUrl = { "/prod-api/login" };

    public String[] getLoginUrl(String prefix) {
        return getCommonUrl(prefix,loginUrl);
    }

    private String[] tokenUrl = { "/prod-api/token" };

    public String[] getTokenUrl(String prefix) {
        return getCommonUrl(prefix,tokenUrl);
    }

    private String[] infoUrl = { "/prod-api/getInfo" };

    public String[] getInfoUrl(String prefix) {
        return getCommonUrl(prefix,infoUrl);
    }

    private String[] userUrl = { "/prod-api/system/user/list" };

    public String[] getUserUrl(String prefix) {
        return getCommonUrl(prefix,userUrl);
    }

    private String[] modelUrl = { "/api/market/datahub/model/queryList" };

    private String[] devEnvUrl = { "/api/kernel/notebooks" };

    public String[] getDevEnvUrl(String prefix) {
        return getCommonUrl(prefix,devEnvUrl);
    }

    private String[] devEnvPerfUrl = { "/api/monitor/monitor/getPodsAvgMonitoringInformation" };

    public String[] getDevEnvPerfUrl(String prefix) {
        return getCommonUrl(prefix,devEnvPerfUrl);
    }

    private String[] completionUrl = { "/inference/cce-pb5p2t34e50f/v1/chat/completions"};

    public String[] getCompletionUrl(String prefix) {
        return getCommonUrl(prefix,completionUrl);
    }

    private String[] keyPairUrl = { "/api/kernel/notebooks/keypairs" };

    public String[] getKeyPairUrl(String prefix) {
        return getCommonUrl(prefix,keyPairUrl);
    }

    private String[] evaluateTaskUrl = { "/api/model/evaluation/manage/findEvaluationPodPageList" };

    public String[] getEvaluateTaskUrl(String prefix) {
        return getCommonUrl(prefix,evaluateTaskUrl);
    }

    private String[] reasonTaskUrl = { "/api/model/modelServer/findModelServerPageList" };

    public String[] getReasonTaskUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskUrl);
    }

    private String[] reasonTaskCreateUrl = { "/api/model/modelServer/createUnionModelServer" };

    public String[] getReasonTaskCreateUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskCreateUrl);
    }

    private String[] reasonTaskDetailUrl = { "/api/model/modelServer/findModelServerById" };

    public String[] getReasonTaskDetailUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskDetailUrl);
    }

    private String[] modelServerPodUrl = { "/api/model/modelServerPod/findModelServerPodList" };

    public String[] getMoelServerPodUrl(String prefix) {
        return getCommonUrl(prefix,modelServerPodUrl);
    }

    private String[] reasonPerfUrl = { "/api/monitor/monitor/getPodsAvgMonitoringInformation" };

    public String[] getReasonPerfUrl(String prefix) {
        return getCommonUrl(prefix,reasonPerfUrl);
    }

    private String[] reasonTaskStopUrl = { "/api/model/modelServerVersion/stop" };

    public String[] getReasonTaskStopUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskStopUrl);
    }

    private String[] reasonTaskStartUrl = { "/api/model/modelServerVersion/start" };

    public String[] getReasonTaskStartUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskStartUrl);
    }

    private String[] reasonTaskVersionDetailUrl = { "/api/model/modelServerVersion/findModelServerVersionById" };

    public String[] getReasonTaskVersionDetailUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskVersionDetailUrl);
    }

    private String[] reasonTaskPodLogUrl = { "/api/model/modelServerVersion/getModelServerPodLog" };

    public String[] getReasonTaskPodLogUrl(String prefix) {
        return getCommonUrl(prefix,reasonTaskPodLogUrl);
    }

    private String[] reasonTaskCallStatisticsUrl = { "/api/model/modelServer/modelServerCallStatistics" };

    public String[] getReasonTaskCallStatisticsUrl(String prefix) {
        return getCommonUrl(prefix, reasonTaskCallStatisticsUrl);
    }

    private String[] fineTuneTaskUrl = { "/api/kernel/fineTune/listLlmFineTuneTask" };

    public String[] getFineTaskUrl(String prefix) {
        return getCommonUrl(prefix,fineTuneTaskUrl);
    }

    private String[] fineTuneTaskDetailUrl = { "/api/kernel/fineTune/queryLlmFineTuneTaskDetail" };

    public String[] getFineTaskDetailUrl(String prefix) {
        return getCommonUrl(prefix,fineTuneTaskDetailUrl);
    }

    private String[] fineTuneTaskPodListUrl = { "/api/kernel/trainOperatorPod/getPodList" };

    public String[] getFineTuneTaskPodListUrl(String prefix) {
        return getCommonUrl(prefix,fineTuneTaskPodListUrl);
    }

    private String[] fineTuneTaskPodLogUrl = { "/api/kernel/trainOperatorPod/viewPodLogs" };

    public String[] getFineTuneTaskPodLogUrl(String prefix) {
        return getCommonUrl(prefix,fineTuneTaskPodLogUrl);
    }

    private String[] resourceGroupUrl = { "/api/system/aiResourceGroup/findResourceGroupBySysGroupId" };

    public String[] getResourceGroupUrl(String prefix) {
        return getCommonUrl(prefix,resourceGroupUrl);
    }

    private String[] algoUrl = { "/api/market/datahub/algo/queryList" };

    private String[] imageUrl = { "/api/market/datahub/image/queryList" };

    public String[] getPropertyUrl(String prefix,String type) {
        if(ObjectUtil.isNotEmpty(type)){
            if(type.equals("dataset")){
                return getCommonUrl(prefix,datasetUrl);
            }else if(type.equals("model")){
                return getCommonUrl(prefix,modelUrl);
            }else if(type.equals("image")){
                return getCommonUrl(prefix,imageUrl);
            }else if(type.equals("algo")){
                return getCommonUrl(prefix,algoUrl);
            }
        }
        return null;
    }

    private String[] fileUrl = { "/api/file/file/getFolderInfo" };

    public String[] getFileUrl(String prefix) {
        return getCommonUrl(prefix,fileUrl);
    }

    private String[] logUrl = { "/api/kernel/trainOperatorPod/viewPodLogs" };

    public String[] getLogUrl(String prefix) {
        return getCommonUrl(prefix,logUrl);
    }

    private String[] tagUrl = { "/api/market/datahub/tag/queryCurTenantDataHubTag" };

    public String[] getTagUrl(String prefix) {
        return getCommonUrl(prefix,tagUrl);
    }

    private String[] datasetUrl = { "/api/market/datahub/dataset/queryList" };

    public String[] getDatasetUrl(String prefix) {
        return getCommonUrl(prefix,datasetUrl);
    }

    private String[] datahubUrl = { "/api/market/datahub" };

    public String[] getDatahubUrl(String prefix) {
        return getCommonUrl(prefix,datahubUrl);
    }

    private String[] configUrl = { "/api/market/datahub/algo/getAlgoParamConfigList?","algoId","versionId"};

    public String[] getConfigUrl(String prefix) {
        return getCommonUrl(prefix,configUrl);
    }



    /**创建模型*/
    private String[] modelCreateUrl = {"/api/market/datahub/model/create"};
    public String[] getModelCreateUrl(String prefix) {
        return getCommonUrl(prefix,modelCreateUrl);
    }
    /**删除模型*/
    private String[] modelDeleteUrl = {"/api/market/datahub/model/delete?id="};
    public String[] getModelDeleteUrl(String prefix) {
        return getCommonUrl(prefix,modelDeleteUrl);
    }
    /**创建模型版本*/
    private String[] modelVersionCreateUrl = {"/api/market/datahub/model/createVersion"};
    public String[] getModelVersionCreateUrl(String prefix){
        return getCommonUrl(prefix,modelVersionCreateUrl);
    }
    /**修改模型版本*/
    private String[] modelVersionUpdateUrl = {"/api/market/datahub/model/modifyVersion"};
    public String[] getModelVersionUpdateUrl(String prefix){
        return getCommonUrl(prefix,modelVersionUpdateUrl);
    }
    /**删除模型版本*/
    private String[] modelVersionDeleteUrl = {"/api/market/datahub/model/deleteVersion"};
    public String[] getModelVersionDeleteUrl(String prefix){
        return getCommonUrl(prefix,modelVersionDeleteUrl);
    }
    /**创建数据集*/
    private String[] dataSetCreateUrl = {"/api/market/datahub/dataset/create"};
    public String[] getDataSetCreateUrl(String prefix) {
        return getCommonUrl(prefix,dataSetCreateUrl);
    }
    /**删除数据集*/
    private String[] dataSetDeleteUrl = {"/api/market/datahub/dataset/delete?id="};
    public String[] getDataSetDeleteUrl(String prefix) {
        return getCommonUrl(prefix,dataSetDeleteUrl);
    }
    /**创建数据集版本*/
    private String[] dataSetVersionCreateUrl = {"/api/market/datahub/dataset/createVersion"};
    public String[] getDataSetVersionCreateUrl(String prefix){
        return getCommonUrl(prefix,dataSetVersionCreateUrl);
    }
    /**修改数据集版本*/
    private String[] dataSetVersionUpdateUrl = {"/api/market/datahub/dataset/modifyVersion"};
    public String[] getDataSetVersionUpdateUrl(String prefix){
        return getCommonUrl(prefix,dataSetVersionUpdateUrl);
    }
    /**删除数据集版本*/
    private String[] dataSetVersionDeleteUrl = {"/api/market/datahub/dataset/deleteVersion"};
    public String[] getDataSetVersionDeleteUrl(String prefix){
        return getCommonUrl(prefix,dataSetVersionDeleteUrl);
    }
    /**创建镜像*/
    private String[] imageCreateUrl = {"/api/market/datahub/image/create"};
    public String[] getImageCreateUrl(String prefix) {
        return getCommonUrl(prefix,imageCreateUrl);
    }
    /**删除镜像*/
    private String[] imageDeleteUrl = {"/api/market/datahub/image/delete?id="};
    public String[] getImageDeleteUrl(String prefix) {
        return getCommonUrl(prefix,imageDeleteUrl);
    }
    /**创建镜像版本*/
    private String[] imageVersionCreateUrl = {"/api/market/datahub/image/createVersion"};
    public String[] getImageVersionCreateUrl(String prefix){
        return getCommonUrl(prefix,imageVersionCreateUrl);
    }
    /**修改镜像版本*/
    private String[] imageVersionUpdateUrl = {"/api/market/datahub/image/modifyVersion"};
    public String[] getImageVersionUpdateUrl(String prefix){
        return getCommonUrl(prefix,imageVersionUpdateUrl);
    }
    /**删除镜像版本*/
    private String[] imageVersionDeleteUrl = {"/api/market/datahub/image/deleteVersion"};
    public String[] getImageVersionDeleteUrl(String prefix){
        return getCommonUrl(prefix,imageVersionDeleteUrl);
    }
    /**创建算法*/
    private String[] algorithmCreateUrl = {"/api/market/datahub/algo/create"};
    public String[] getAlgorithmCreateUrl(String prefix) {
        return getCommonUrl(prefix,algorithmCreateUrl);
    }
    /**删除算法*/
    private String[] algorithmDeleteUrl = {"/api/market/datahub/algo/delete?id="};
    public String[] getAlgorithmDeleteUrl(String prefix) {
        return getCommonUrl(prefix,algorithmDeleteUrl);
    }
    /**创建算法版本*/
    private String[] algorithmVersionCreateUrl = {"/api/market/datahub/algo/createVersion"};
    public String[] getAlgorithmVersionCreateUrl(String prefix){
        return getCommonUrl(prefix,algorithmVersionCreateUrl);
    }
    /**修改算法版本*/
    private String[] algorithmVersionUpdateUrl = {"/api/market/datahub/algo/modifyVersion"};
    public String[] getAlgorithmVersionUpdateUrl(String prefix){
        return getCommonUrl(prefix,algorithmVersionUpdateUrl);
    }
    /**删除算法版本*/
    private String[] algorithmVersionDeleteUrl = {"/api/market/datahub/algo/deleteVersion"};
    public String[] getAlgorithmVersionDeleteUrl(String prefix){
        return getCommonUrl(prefix,algorithmVersionDeleteUrl);
    }
    /**创建算法策略*/
    private String[] algorithmStrategyCreateUrl = {"/api/market/datahub/algo/addAlgoParamConfig"};
    public String[] getAlgorithmStrategyCreateUrl(String prefix){
        return getCommonUrl(prefix,algorithmStrategyCreateUrl);
    }
    /**修改算法策略*/
    private String[] algorithmStrategyUpdateUrl = {"/api/market/datahub/algo/updateAlgoParamConfig"};
    public String[] getAlgorithmStrategyUpdateUrl(String prefix){
        return getCommonUrl(prefix,algorithmStrategyUpdateUrl);
    }
    /**删除算法策略*/
    private String[] algorithmStrategyDeleteUrl = {"/api/market/datahub/algo/removeAlgoParamConfig?id="};
    public String[] getAlgorithmStrategyDeleteUrl(String prefix){
        return getCommonUrl(prefix,algorithmStrategyDeleteUrl);
    }
    /**创建文件夹：模型、数据集、镜像、算法*/
    private String[] propertyFolderCreateUrl = {"/api/market/datahub/data/createFolder"};
    public String[] getPropertyFolderCreateUrl(String prefix){
        return getCommonUrl(prefix,propertyFolderCreateUrl);
    }
    /**获取文件：模型、数据集、镜像、算法*/
    private String[] propertyFileReadUrl = {"/api/market/datahub/data/readFile"};
    public String[] getPropertyFileReadUrl(String prefix){
        return getCommonUrl(prefix,propertyFileReadUrl);
    }
    /**修改文件：模型、数据集、镜像、算法*/
    private String[] propertyFileUpdateUrl = {"/api/market/datahub/data/editFile"};
    public String[] getPropertyFileUpdateUrl(String prefix){
        return getCommonUrl(prefix,propertyFileUpdateUrl);
    }
    /**删除文件：模型、数据集、镜像、算法*/
    private String[] propertyFileDeleteUrl = {"/api/market/datahub/data/deleteFiles"};
    public String[] getPropertyFileDeleteUrl(String prefix){
        return getCommonUrl(prefix,propertyFileDeleteUrl);
    }
    /**上传文件：模型、数据集、镜像、算法*/
    private String[] propertyFileUploadFile = {"/api/market/datahub/data/uploadFile"};
    public String[] getPropertyFileUploadFile(String prefix){
        return getCommonUrl(prefix,propertyFileUploadFile);
    }
    /**上传文件分片合并：模型、数据集、镜像、算法*/
    private String[] propertyFileMergeFile = {"/api/market/datahub/data/mergeFile"};
    public String[] getPropertyFileMergeFile(String prefix){
        return getCommonUrl(prefix,propertyFileMergeFile);
    }
    /**获取文件URL：模型、数据集、镜像、算法*/
    private String [] propertyFileDownloadUrl = {"/api/market/datahub/data/queryFileDownloadUrl"};
    public String[] getPropertyFileDownloadUrl(String prefix){
        return getCommonUrl(prefix,propertyFileDownloadUrl);
    }
    /**下载文件：模型、数据集、镜像、算法*/
    private String[] propertyFileDownload = {"/api/market/datahub/data/queryFileDownloadUrl"};
    public String[] getPropertyFileDownload(String prefix){
        return getCommonUrl(prefix,propertyFileDownload);
    }

    /**上传文件(夹)*/
    public String[] fileUploadUrl = {"/api/file/file/uploadFolder"};
    public String[] getFileUploadUrl(String prefix){
        return getCommonUrl(prefix,fileUploadUrl);
    }
    /**上传大文件*/
    public String[] largeFileUploadUrl = {"/api/file/file/uploadFile"};
    public String[] getLargeFileUploadUrl(String prefix){
        return getCommonUrl(prefix,largeFileUploadUrl);
    }
    /**上传大文件：分片合并*/
    public String[] largeFileMergeUrl = {"/api/file/file/mergeFile"};
    public String[] getLargeFileMergeUrl(String prefix){
        return getCommonUrl(prefix,largeFileMergeUrl);
    }
    /**创建文件夹*/
    private String[] folderCreateUrl = {"/api/file/file/addFolder"};
    public String[] getFolderCreateUrl(String prefix){
        return getCommonUrl(prefix,folderCreateUrl);
    }
    /**删除文件（夹）*/
    private String[] fileDeleteUrl = {"/api/file/file/deleteFileOrDictory"};
    public String[] getFileDeleteUrl(String prefix){
        return getCommonUrl(prefix,fileDeleteUrl);
    }
    /**重命名文件（夹）*/
    private String[] fileRenameUrl = {"/api/file/file/fileReName"};
    public String[] getFileRenameUrl(String prefix){
        return getCommonUrl(prefix,fileRenameUrl);
    }
    /**文件（夹）是否被共享*/
    private String[] findShareFileUrl = {"/api/file/fileShare/findShareFile"};
    public String[] getFindShareFileUrl(String prefix){
        return getCommonUrl(prefix,findShareFileUrl);
    }
    /**文件下载*/
    private String[] fileDownloadUrl = {"/api/file/file/downloadFile1"};
    public String[] getFileDownloadUrl(String prefix){
        return getCommonUrl(prefix,fileDownloadUrl);
    }
    /**文件夹下载*/
    private String[] folderDownloadUrl = {"/api/file/file/downloadFile"};
    public String[] getFolderDownloadUrl(String prefix){
        return getCommonUrl(prefix,folderDownloadUrl);
    }
    /**文件是否存在*/
    private String[] fileCheckExistUrl = {"/api/file/file/checkFileExist"};
    public String[] getFileCheckExistUrl(String prefix){
        return getCommonUrl(prefix,fileCheckExistUrl);
    }
    /**公有文件批量下载*/
    private String[] publicFilesDownloadUrl = {"/api/file/file/downloadPublicFiles"};
    public String[] getPublicFilesDownloadUrl(String prefix){
        return getCommonUrl(prefix,publicFilesDownloadUrl);
    }
    /**公有文件单个下载*/
    private String[] publicFileDownloadUrl = {"/api/file/file/downloadPublicFile"};
    public String[] getPublicFileDownloadUrl(String prefix){
        return getCommonUrl(prefix,publicFileDownloadUrl);
    }

    private String[] fineTuneCreateUrl = {"/api/kernel/computationTask/train"};

    public String[] getFineTuneCreateUrl(String prefix) {
        return getCommonUrl(prefix, fineTuneCreateUrl);
    }


    private String[] fineTuneStopUrl = {"/api/kernel/fineTune/stopLlmFineTuneTask"};

    public String[] getFineTuneStopUrl(String prefix) {
        return getCommonUrl(prefix, fineTuneStopUrl);
    }

    private String[] fineTuneStartUrl = {"/api/kernel/fineTune/startLlmFineTuneTask"};

    public String[] getFineTuneStartUrl(String prefix) {
        return getCommonUrl(prefix, fineTuneStartUrl);
    }

    private String[] fineTuneDeleteUrl = {"/api/kernel/computationTask/deleteTask"};

    public String[] getFineTuneDeleteUrl(String prefix) {
        return getCommonUrl(prefix, fineTuneDeleteUrl);
    }

    private String[] fineReasonDeleteUrl = {"/api/model/modelServerVersion/deleteModelServerVersionIntegrally"};

    public String[] getFineReasonDeleteUrl(String prefix) {
        return getCommonUrl(prefix, fineReasonDeleteUrl);
    }



    private String[] generateCommandUrl = {"/api/market/datahub/algo/generateCommand"};

    public String[] getGenerateCommandUrl(String prefix) {
        return getCommonUrl(prefix, generateCommandUrl);
    }

    public  String makeUrl(CloudAccessBean param, String[] paths, String[] args) {
        String url = param.getProtocol() +"://" + param.getServerIp() + ":"+ param.getServerPort();
        if (paths.length > 1) {
            return configArgs(url, paths, args);
        } else if(null != args && args.length>1){
            url = url + paths[0];
            for (int i = 0; i < args.length; i++) {
                url = url +"/"+ args[i];
            }
            return url;
        }else if (null != args && args.length > 0 && null != args[0]) {
            return url +  paths[0] + args[0] ;
        } else {
            return url + paths[0];
        }
    }
    /**
     *
     * 配置url参数
     * @param url ip和端口信息
     * @param paths 拼接参数名
     * @param args 拼接参数值
     * @return {@code String}
     */
    private static String configArgs(String url, String[] paths, String[] args) {
        if (null == args || args.length == 0 || null == url) {
            return url;
        }
        String resp = url;
        StringBuffer buf = new StringBuffer(resp);
        for (int i = 0; i < paths.length; i++) {
            if (null != args[i]) {
                if (paths[i].contains("?")) {
                    buf.append(args[i]).append(paths[i]);
                } else if (ObjectUtil.isEmpty(paths[i])) {
                    buf.append(args[i]);
                }else if (paths[i].contains("/")) {
                    buf.append(paths[i]).append(args[i]);
                } else {
                    buf.append(paths[i]).append("=").append(args[i]).append("&");
                }
            } else if (paths[i].contains("?") || paths[i].contains("/")) {
                buf.append(paths[i]);
            }
        }
        resp = buf.toString();
        if (resp.endsWith("&") || resp.endsWith("/") || resp.endsWith("?")) {
            resp = resp.substring(0, resp.length() - 1);
        }
        return resp;
    }

    public String[] getCommonUrl(String prefix,String[] url) {
        if(prefix==null||"/".equals(prefix)) {
            return url.clone();
        }else {
            String s = url[0];
            if (!s.startsWith(prefix)) {
                if (prefix != null && prefix.length() > 0) {
                    s = prefix + s;
                }
                url[0] = s;
            }
        }
        return url.clone();
    }

}
