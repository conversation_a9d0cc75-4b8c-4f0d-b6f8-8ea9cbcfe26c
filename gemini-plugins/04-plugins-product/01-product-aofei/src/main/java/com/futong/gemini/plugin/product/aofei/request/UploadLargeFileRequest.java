package com.futong.gemini.plugin.product.aofei.request;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
public class UploadLargeFileRequest implements Serializable {

    private String cmpId;

    private String userId;

    private Boolean isPublic;

    private String currentPath;

    private MultipartFile chunk;

    private Long chunkSize;

    private String fileName;

    private String chunkMd5;

    private String fileMd5;

    private int index;

    private int total;

}
