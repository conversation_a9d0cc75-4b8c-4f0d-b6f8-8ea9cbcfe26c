
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.model.atlas.entity.*;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PropertyService {

    public static final PropertyService bean = new PropertyService();

    public void fetchProperty(DescribeAofeiRequest request, JSONObject arguments, String type) {
        String subCmpId = request.getIds()==null?"":request.getIds().get(0).getCmpId();
        Integer fecthSize = request.getFecthSize();
        List<CaDatahubProperty> propertyList = new ArrayList<>();
        List<CaDatahubPropertyTag> propertyTagList = new ArrayList<>();
        List<CaDatahubPropertyVersion> versionList = new ArrayList<>();
        List<CaDatahubPropertyInfo> infoList = new ArrayList<>();
        List<CaDatahubPropertyConfig> configList = new ArrayList<>();

        Map<String, JSONObject> versionMap = new HashMap();
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String modelUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getPropertyUrl(cloudAccessBean.getScvmmRole(),type), null);
        int key = 1;
        int count = AofeiConstants.FETCH_SIZE;
        String json = "{\"current\": "+fecthSize+",\"size\": "+count+",  \"scope\": "+key+",\"taskTags\": []}";
        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, json, modelUrl,TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                String infojson = "{\"id\": \""+tempObj.getString("id")+"\", \"current\": 1, \"size\": 100, \"full\": false}";
                JSONArray verArray = Converts.convertData(request.getIds(), cloudAccessBean, infojson, URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDatahubUrl(cloudAccessBean.getScvmmRole()), new String[]{"/"+type+"/queryVersionList"}),TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
                if (ObjectUtil.isNotEmpty(verArray)) {
                    verArray.forEach(ver -> {
                        JSONObject tempVer = (JSONObject) ver;
                        CaDatahubPropertyVersion version = Converts.toPropertyVersion(BaseClient.auths.get(), tempVer, type, tempObj.getString("id"), versionMap, subCmpId,tempObj.getString("creator"));
                        versionList.add(version);
                    });
                }
                CaDatahubProperty property = Converts.toProperty(BaseClient.auths.get(), tempObj, type,versionList,versionMap,propertyTagList,subCmpId);
                if (ObjectUtil.isNotNull(property))
                    propertyList.add(property);
            });
        }
        if (ObjectUtil.isNotEmpty(versionList)) {
            versionList.forEach(temp -> {
                String infojson = "{\"id\": \"" + temp.getPropery_id() + "\", \"versionId\": \"" + temp.getOpen_id() + "\"}";
                List<AofeiToken> tokens = Converts.getToken(request.getIds(), cloudAccessBean);
                if (ObjectUtil.isNotNull(tokens))
                    tokens.forEach(token -> {
                        try {
                            JSONObject imageInfo = Converts.fetchResourceResultToJSONObject(token, URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDatahubUrl(cloudAccessBean.getScvmmRole()), new String[]{"/" + type + "/queryInfo"}), infojson, TypeEnum.DATA.getValue());
                            CaDatahubPropertyInfo info = Converts.toPropertyInfo(BaseClient.auths.get(), imageInfo, type, temp.getPropery_id(), temp.getOpen_id(), subCmpId);
                            infoList.add(info);
                        }catch (Exception e) {
                            e.printStackTrace();
                        }
                        if("algo".equals(type)) {
                            String configUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getConfigUrl(cloudAccessBean.getScvmmRole()), new String[]{"",temp.getPropery_id(), temp.getOpen_id()});
                            JSONArray configInfoList = Converts.getResourceToJsonObject(token, configUrl, infojson, TypeEnum.DATA.getValue());
                            if (ObjectUtil.isNotEmpty(configInfoList)) {
                                configInfoList.forEach(t -> {
                                    JSONObject tempVer = (JSONObject) t;
                                    CaDatahubPropertyConfig con = Converts.toPropertyConfig(BaseClient.auths.get(), tempVer, type, temp.getPropery_id(), temp.getOpen_id(), subCmpId);
                                    configList.add(con);
                                });
                            }
                        }
                    });

            });
        }
        // 推送镜像数据
        log.info("推送"+type+"数量-------：{}",propertyList.size());
        BaseUtils.sendAtlasMessage(propertyList, arguments,subCmpId,CaDatahubProperty.class);
        log.info("推送"+type+"版本数量-------：{}",versionList.size());
        BaseUtils.sendAtlasMessage(versionList, arguments,subCmpId,CaDatahubPropertyVersion.class);
        log.info("推送"+type+"介绍数量--------：{}",infoList.size());
        BaseUtils.sendAtlasMessage(infoList, arguments,subCmpId,CaDatahubPropertyInfo.class);
        log.info("推送"+type+"tag数量：{}",propertyTagList.size());
        BaseUtils.sendAtlasMessage(propertyTagList, arguments,subCmpId,CaDatahubPropertyTag.class);
        log.info("推送"+type+"算法config数量：{}",configList.size());
        BaseUtils.sendAtlasMessage(configList, arguments,subCmpId,CaDatahubPropertyConfig.class);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
