package com.futong.gemini.plugin.product.aofei.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.UploadPropertyFileRequest;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UploadFileService {

    /**
     * 私有文件上传
     * @param arguments
     * @return
     */
    public static BaseResponse uploadPrivateFile(JSONObject arguments){
        String message = "私有文件上传成功.";
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UploadPropertyFileRequest request = BaseClient.bodys.get().toJavaObject(UploadPropertyFileRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "");
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageCreateUrl(cloudAccessBean.getScvmmRole()), null);
            HttpClientUtil.post(url, JSONObject.toJSONString(request), config);
        }catch (Exception e){
            log.error("私有文件上传失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "私有文件上传失败");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    /**
     * 公有文件上传
     * @param arguments
     * @return
     */
    public static BaseResponse uploadPublicFile(JSONObject arguments){
        return BaseResponse.SUCCESS.of("");
    }
}
