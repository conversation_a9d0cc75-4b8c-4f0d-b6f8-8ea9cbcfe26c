package com.futong.gemini.plugin.product.aofei.vo.image;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AofeiImage {

    private String userId;

    private String userName;

    private String deptId;

    private String email;

    private String password;

    private String roleId;
    private List<Role> roles;
    private Dept dept;

    @Data
    public static class Role {
        private String roleId;
        private String roleName;
        private String roleKey;
    }
    @Data
    public static class Dept {
        private String deptId;
        private String deptName;
    }
}
