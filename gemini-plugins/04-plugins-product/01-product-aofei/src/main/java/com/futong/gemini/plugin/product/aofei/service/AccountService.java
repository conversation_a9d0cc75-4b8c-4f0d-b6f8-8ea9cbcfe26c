package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.JobUtils;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Configuration
public class AccountService {

    public static final AccountService bean = new AccountService();

    public static String accountDispatch;
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    public static BaseResponse getAccountAddForm(JSONObject arguments) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    public static BaseResponse authAccount(JSONObject arguments) {
        try {
            log.info("---------傲飞账号开始成功------------");
            HttpClientUtil.getAofeiToken(BaseClient.auths.get());
        } catch (Exception e) {
            log.error("账号认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.auth.fail"), e);
        }
        log.info("---------傲飞账号验证成功------------");
        return BaseResponse.SUCCESS.ofI18n("gemini.public.auth.success");
    }

    public static BaseResponse createFetchDispatch(JSONObject arguments) {
        arguments.put("auth", arguments.getJSONObject("body").getJSONObject("auth"));
        BaseCloudRequest request = new BaseCloudRequest(arguments);
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        List<JSONObject> dispatchers = listAllDispatcher(result);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            }
        });
        return dispatchers;
    }


    public List<JobInfo> splitUserDataJob(DescribeAofeiRequest request, JSONObject arguments,String[] resourceTypes) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<AofeiUser> userList = new ArrayList<>();
        List<CloudAccessBean> subList = cloudAccessBean.getSubList();
        for(CloudAccessBean bean : subList) {
            AofeiUser user = new AofeiUser();
            user.setUserName(bean.getUsername());
            String jsonStr = bean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject obj = JSONObject.parseObject(jsonStr);
                String userId  = obj.getString("userId");
                user.setUserId(userId);
                user.setCmpId(bean.getCmpId());
                user.setPassword(bean.getPassword());
                userList.add(user);
            }
        }
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            /** 拆分用户dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitDataJob(request, userList, resourceTypes,
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取用户数据异常");
        }
        return jobs;
    }

    public List<JobInfo> splitPropertyDataJob(DescribeAofeiRequest request, JSONObject arguments,String[] resourceTypes) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<AofeiUser> userList = new ArrayList<>();
        List<CloudAccessBean> subList = cloudAccessBean.getSubList();
        for(CloudAccessBean bean : subList) {
            AofeiUser user = new AofeiUser();
            user.setUserName(bean.getUsername());
            String jsonStr = bean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject obj = JSONObject.parseObject(jsonStr);
                String userId  = obj.getString("userId");
                user.setUserId(userId);
                user.setCmpId(bean.getCmpId());
                user.setPassword(bean.getPassword());
                userList.add(user);
            }
        }
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            /** 拆分用户dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitPropertyDataJob(request, userList, resourceTypes,
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取用户数据异常");
        }
        return jobs;
    }

    public List<JobInfo> splitTaskDataJob(DescribeAofeiRequest request, JSONObject arguments,String[] resourceTypes) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<AofeiUser> userList = new ArrayList<>();
        List<CloudAccessBean> subList = cloudAccessBean.getSubList();
        for(CloudAccessBean bean : subList) {
            AofeiUser user = new AofeiUser();
            user.setUserName(bean.getUsername());
            String jsonStr = bean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject obj = JSONObject.parseObject(jsonStr);
                String userId  = obj.getString("userId");
                user.setUserId(userId);
                user.setCmpId(bean.getCmpId());
                user.setPassword(bean.getPassword());
                userList.add(user);
            }
        }
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            /** 拆分用户dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitTaskDataJob(request, userList, resourceTypes,
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取用户数据异常");
        }
        return jobs;
    }

    public List<JobInfo> splitFineTaskDataJob(DescribeAofeiRequest request, JSONObject arguments,String[] resourceTypes) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<AofeiUser> userList = new ArrayList<>();
        List<CloudAccessBean> subList = cloudAccessBean.getSubList();
        for(CloudAccessBean bean : subList) {
            AofeiUser user = new AofeiUser();
            user.setUserName(bean.getUsername());
            String jsonStr = bean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject obj = JSONObject.parseObject(jsonStr);
                String userId  = obj.getString("userId");
                user.setUserId(userId);
                user.setCmpId(bean.getCmpId());
                user.setPassword(bean.getPassword());
                userList.add(user);
            }
        }
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            /** 拆分用户dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitFineTaskDataJob(request, userList, resourceTypes,
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取用户数据异常");
        }
        return jobs;
    }


    public List<JobInfo> splitDevEnvDataJob(DescribeAofeiRequest request, JSONObject arguments,String[] resourceTypes) {
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<AofeiUser> userList = new ArrayList<>();
        List<CloudAccessBean> subList = cloudAccessBean.getSubList();
        for(CloudAccessBean bean : subList) {
            AofeiUser user = new AofeiUser();
            user.setUserName(bean.getUsername());
            String jsonStr = bean.getJsonStr();
            if(jsonStr!=null) {
                JSONObject obj = JSONObject.parseObject(jsonStr);
                String userId  = obj.getString("userId");
                user.setUserId(userId);
                user.setCmpId(bean.getCmpId());
                user.setPassword(bean.getPassword());
                userList.add(user);
            }
        }
        /** 拆分dataJob，推送到gourd */
        List<JobInfo> jobs = new ArrayList<>();
        try {
            /** 拆分用户dataJob */
            jobs = Stream.of(jobs,
                            JobUtils.bean.splitDevEnvDataJob(request, userList, resourceTypes,
                                    arguments))
                    .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取用户数据异常");
        }
        return jobs;
    }
}
