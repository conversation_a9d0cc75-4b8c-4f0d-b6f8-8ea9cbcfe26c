package com.futong.gemini.plugin.product.aofei.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AofeiUtils {
    public static final AofeiUtils bean = new AofeiUtils();

    /**
     * 创建开发环境
     *
     * @param request 请求body
     * @throws Exception
     */
    public static String createDevEnv(JSONObject arguments, CloudAccessBean accessBean, String url, CreateDevEnvRequest request, HttpClientConfig config) throws Exception {
        CreateDevEnvRequest body = getDevEnvBody(request);
        log.info("创建开发环境参数={}", JSONObject.toJSONString(body).toString());
        String message = HttpClientUtil.post(url, JSONObject.toJSONString(body), config);
        log.info("创建开发环境返回={}", message);
        return sendMq(BaseClient.bodys.get(), accessBean, message, request.getName());
    }

    public static String completion(JSONObject arguments, CloudAccessBean accessBean, String url, CompletionRequest request, HttpClientConfig config) throws Exception {
        log.info("对话参数={}", JSONObject.toJSONString(request).toString());
        String message = HttpClientUtil.postStream(url, JSONObject.toJSONString(request), config);
        log.info("对话返回={}", message);
        return message;
    }

    public static String sendMq(JSONObject arguments, CloudAccessBean accessBean, String message, String name) {
        JSONObject obj = JSONObject.parseObject(message);
        String result = "OK";
        log.info("发送消息队列开始={}", message);
        if (obj.getInteger("code") == 200) {
            JSONObject biz = arguments.getJSONObject("biz");
            log.info("biz信息={}", biz.toJSONString());
            if (ObjectUtil.isNotEmpty(biz)) {
                biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.DEV_ENV.getValue(), name));
                FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
                log.info("发送消息队列成功");
            }
        } else {
            result = "开发环境创建失败!失败原因:"+obj.getString("message");
            JSONObject biz = arguments.getJSONObject("biz");
            biz.put("error", "开发环境创建失败!失败原因:"+obj.getString("message"));
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return result;
    }

    public static void main(String[] args) {
        System.out.println(IdUtils.encryptId("c063bad17f46a84b755f49aa954734e3", "cpp_ampha", ResourceEnum.DEV_ENV.getValue(), "test0317_2","c063bad17f46a84b755f49aa954734e3"));
    }

    /**
     * 获取虚拟机body
     *
     * @param request
     * @return
     */
    public static CreateDevEnvRequest getDevEnvBody(CreateDevEnvRequest request) {
        if (request.getSshPassword() != null) {
            request.setSshPassword(ComputeUtil.getBase64Code(request.getSshPassword()));
        }
        return request;

    }

    /**
     * 创建大模型调优任务
     *
     * @param arguments  请求参数
     * @param accessBean 云账号信息
     * @param url        请求URL
     * @param request    请求体
     * @param config     HTTP配置
     */
    public static String createFineTuneTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                            CreateLlmFineTuneRequest request, HttpClientConfig config) {
        log.info("创建大模型调优任务参数={}", JSONObject.toJSONString(request));
        String response = HttpClientUtil.post(url, JSONObject.toJSONString(request), config);
        log.info("创建大模型调优任务返回={}", response);
        return sendFineTuneMq(arguments, accessBean, response, request.getName());
    }

    /**
     * 停止大模型调优任务
     *
     * @param arguments  请求参数
     * @param accessBean 云账号信息
     * @param url        请求URL
     * @param taskId     任务ID
     * @param config     HTTP配置
     */
    public static String stopFineTuneTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                          String taskId, HttpClientConfig config) {
        log.info("停止大模型调优任务参数, taskId={}", taskId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("taskId", taskId);
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        log.info("停止大模型调优任务返回={}", response);
        return sendFineTuneStopMq(BaseClient.bodys.get(), accessBean, response, taskId);
    }

    public static String deleteFineTuneTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                          String taskId, HttpClientConfig config) {
        log.info("删除大模型调优任务参数, taskId={}", taskId);
        JSONObject requestBody = new JSONObject();
        List<String> ids = new ArrayList<>();
        ids.add(taskId);
        requestBody.put("taskIds", ids);
        requestBody.put("createWay", "custom");
        log.info("删除大模型调优任务参数, url={}", url);
        log.info("删除大模型调优任务参数, body={}", requestBody.toJSONString());
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        JSONObject jsonObject = JSONObject.parseObject(response);
        String message = "删除大模型任务成功";
        if (jsonObject.getInteger("code") != 200) {
            message = jsonObject.getString("message");
        }
        log.info("删除大模型调优任务返回={}", response);
        return message;
    }

    public static String deleteReasonTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                            String taskId, HttpClientConfig config) {
        log.info("删除大模型推理任务参数, taskId={}", taskId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("id", taskId);
        log.info("删除大模型推理任务参数, url={}", url);
        log.info("删除大模型推理任务参数, body={}", requestBody.toJSONString());
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        log.info("删除大模型推理任务返回={}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        String message = "删除大模型任务成功";
        if (jsonObject.getInteger("code") != 200) {
            message = jsonObject.getString("message");
        }
        log.info("删除大模型调优任务返回={}", response);
        return message;
    }

    /**
     * 发送大模型调优任务停止消息队列
     */
    private static String sendFineTuneStopMq(JSONObject arguments, CloudAccessBean accessBean, String response, String taskId) {
        String message = "成功停止大模型调优任务";
        JSONObject responseObj = JSONObject.parseObject(response);
        log.info("发送消息队列开始={}", response);
        if (responseObj.containsKey("returnCode") && responseObj.getInteger("returnCode") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.FINE.getValue(), taskId));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");
        } else {
            message = "大模型调优任务停止失败!" + responseObj.getString("returnMessage");
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("error", "大模型调优任务停止失败!");
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return message;
    }

    public static String startFineTuneTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                          String taskId, HttpClientConfig config) {
        log.info("启动大模型调优任务参数, taskId={}", taskId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("taskId", taskId);
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        log.info("启动大模型调优任务返回={}", response);
        return sendFineTuneStartMq(BaseClient.bodys.get(), accessBean, response, taskId);
    }

    /**
     * 发送大模型调优任务停止消息队列
     */
    private static String sendFineTuneStartMq(JSONObject arguments, CloudAccessBean accessBean, String response, String taskId) {
        String message = "成功启动大模型调优任务";
        JSONObject responseObj = JSONObject.parseObject(response);
        log.info("发送消息队列开始={}", response);
        if (responseObj.containsKey("returnCode") && responseObj.getInteger("returnCode") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.FINE.getValue(), taskId));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");
        } else {
            message = "大模型调优任务启动失败!" + responseObj.getString("returnMessage");
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("error", "大模型调优任务启动失败!");
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return message;
    }

    /**
     * 发送大模型调优任务消息队列
     */
    private static String sendFineTuneMq(JSONObject arguments, CloudAccessBean accessBean, String response, String name) {
        String message = "成功创建大模型调优任务";
        JSONObject responseObj = JSONObject.parseObject(response);
        log.info("发送消息队列开始={}", response);
        if (responseObj.containsKey("returnCode") && responseObj.getInteger("returnCode") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            log.info("biz信息={}", biz.toJSONString());
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.FINE.getValue(), name));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");
        } else {
            message = "创建大模型调优任务失败!" + responseObj.getString("message");
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("error", "大模型调优任务创建失败!" + responseObj.getString("message"));
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return message;
    }

    private static JSONObject checkBiz(JSONObject arguments) {
        if (!arguments.containsKey("biz")) {
            log.info("biz参数为空，跳过消息队列发送");
            return null;
        }
        JSONObject biz = arguments.getJSONObject("biz");
        if (ObjectUtil.isEmpty(biz)) {
            log.info("biz对象为空，跳过消息队列发送");
            return null;
        }
        return biz;
    }

    /**
     * 创建推理服务
     *
     * @param arguments  请求参数
     * @param accessBean 云账号信息
     * @param url        请求URL
     * @param request    请求体
     * @param config     HTTP配置
     */
    public static String createReasonTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                        CreateReasonRequest request, HttpClientConfig config) {
        log.info("创建推理服务参数={}", JSONObject.toJSONString(request));
        String response = "{\"success\":true,\"code\":200}";
        try {
            response = HttpClientUtil.post(url, JSONObject.toJSONString(request), config);
            log.info("创建推理服务返回={}", response);
        }catch (Exception e) {
            e.printStackTrace();
        }

       return sendReasonCreateMq(arguments, accessBean, response, request.getModelServer().getName());
    }

    /**
     * 发送推理服务创建消息队列
     */
    private static String sendReasonCreateMq(JSONObject arguments, CloudAccessBean accessBean, String response, String name) {
        JSONObject responseObj = JSONObject.parseObject(response);
        String message = "成功创建推理服务";
        log.info("发送消息队列开始={}", response);
        if (responseObj.getBoolean("success") && responseObj.getInteger("code") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.REASON.getValue(), name));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");
            log.info("biz信息={}", biz.toJSONString());
        } else {
            JSONObject biz = checkBiz(arguments);
            message = "推理服务创建失败!" + responseObj.getString("message");
            if (biz == null) return message;
            biz.put("error", "推理服务创建失败!");
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }

        return message;
    }

    /**
     * 停止推理服务
     *
     * @param arguments  请求参数
     * @param accessBean 云账号信息
     * @param url        请求URL
     * @param versionId  版本ID
     * @param config     HTTP配置
     */
    public static String stopReasonTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                        String versionId, HttpClientConfig config) {
        log.info("停止推理服务参数, versionId={}", versionId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("id", versionId);
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        log.info("停止推理服务返回={}", response);
        return sendReasonStopMq(BaseClient.bodys.get(), accessBean, response, versionId);
    }

    public static String startReasonTask(JSONObject arguments, CloudAccessBean accessBean, String url,
                                        String versionId, HttpClientConfig config) {
        log.info("启动推理服务参数, versionId={}", versionId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("id", versionId);
        String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
        log.info("启动推理服务返回={}", response);
        return sendReasonStartMq(BaseClient.bodys.get(), accessBean, response, versionId);
    }

    private static String sendReasonStartMq(JSONObject arguments, CloudAccessBean accessBean, String response, String versionId) {
        String message = "成功启动推理服务";
        JSONObject responseObj = JSONObject.parseObject(response);
        log.info("发送消息队列开始={}", response);
        if (responseObj.getBoolean("success") && responseObj.getInteger("code") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.REASON.getValue(), versionId));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");

        } else {
            JSONObject biz = checkBiz(arguments);
            message = "推理服务启动失败!" + responseObj.getString("message");
            if (biz == null) return message;
            biz.put("error", "推理服务启动失败!");
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return message;
    }

    /**
     * 发送推理服务停止消息队列
     */
    private static String sendReasonStopMq(JSONObject arguments, CloudAccessBean accessBean, String response, String versionId) {
        String message = "成功停止推理服务";
        JSONObject responseObj = JSONObject.parseObject(response);
        log.info("发送消息队列开始={}", response);
        if (responseObj.getBoolean("success") && responseObj.getInteger("code") == 200) {
            JSONObject biz = checkBiz(arguments);
            if (biz == null) return message;
            biz.put("resId", IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceEnum.REASON.getValue(), versionId));
            FTRabbitUtils.sendMessage("CMP_EXCHANGE", "cmp_resource_routing", biz);
            log.info("发送消息队列成功");

        } else {
            JSONObject biz = checkBiz(arguments);
            message = "推理服务停止失败!" + responseObj.getString("message");
            if (biz == null) return message;
            biz.put("error", "推理服务停止失败!");
            FTRabbitUtils.sendMessage("cmp_atlas_exchange", "cmp_atlas_resource", biz);
        }
        return message;
    }

    /**
     * 生成命令
     *
     * @param cloudAccessBean 云账号信息
     * @param request         请求参数
     * @param config          HTTP配置
     * @return 生成的命令
     */
    public static String generateCommand(CloudAccessBean cloudAccessBean, GenerateCommandRequest request, HttpClientConfig config) {
        log.info("生成命令参数={}", JSONObject.toJSONString(request));
        String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getGenerateCommandUrl(cloudAccessBean.getScvmmRole()), null);
        String response = HttpClientUtil.post(url, JSONObject.toJSONString(request), config);
        log.info("生成命令返回={}", response);

        JSONObject result = JSONObject.parseObject(response);
        if (!result.getBoolean("success") || result.getInteger("code") != 200) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "命令生成失败: " + result.getString("message"));
        }
        return result.getString("data");
    }

    /**
     * 获取大模型调优任务日志
     *
     * @param arguments       请求参数
     * @param cloudAccessBean 云访问对象
     * @param url             请求URL
     * @param requestBody     请求体
     * @param config          HTTP客户端配置
     * @return 日志数据
     */
    public static JSONObject getFineTuneTaskPodLog(JSONObject arguments, CloudAccessBean cloudAccessBean, String url, JSONObject requestBody, HttpClientConfig config) {
        try {
            String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
            log.info("获取大模型调优任务日志返回={}", response);

            JSONObject result = JSONObject.parseObject(response);
            if (!result.getBoolean("success") || result.getInteger("returnCode") != 200) {
                if (result.getInteger("returnCode") == 500 && result.getString("message").equals("文件错误")){
                    return null;
                }
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "获取任务日志失败: " + result.getString("message"));
            }

            return result.getJSONObject("data");
        } catch (Exception e) {
            log.error("获取大模型调优任务日志失败, url={}, requestBody={}", url, requestBody, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取大模型调优任务日志失败");
        }
    }

    /**
     * 获取推理服务日志
     *
     * @param arguments       请求参数
     * @param cloudAccessBean 云访问对象
     * @param url             请求URL
     * @param requestBody     请求体
     * @param config          HTTP客户端配置
     * @return 日志数据
     */
    public static String getReasonTaskPodLog(JSONObject arguments, CloudAccessBean cloudAccessBean, String url, JSONObject requestBody, HttpClientConfig config) {
        try {
            String response = HttpClientUtil.post(url, requestBody.toJSONString(), config);
            log.info("获取推理服务日志返回={}", response);

            JSONObject result = JSONObject.parseObject(response);
            if (!result.getBoolean("success") || result.getInteger("code") != 200) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "获取推理服务日志失败: " + result.getString("message"));
            }

            return result.getString("data");
        } catch (Exception e) {
            log.error("获取推理服务日志失败, url={}, requestBody={}", url, requestBody, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取推理服务日志失败");
        }
    }

}
