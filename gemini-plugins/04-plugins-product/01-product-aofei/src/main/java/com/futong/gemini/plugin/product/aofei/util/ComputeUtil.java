package com.futong.gemini.plugin.product.aofei.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class ComputeUtil {

    public static void main(String[] args) {
        System.out.println(getEncryptPwd("admin@000","h3cAmpha_license"));
    }

    public static String getEncryptPwd(String password,String key) {
        String pwd = "";
        key = key==null?"h3cAmpha_license":key;
        try {
            IvParameterSpec iv = new IvParameterSpec(key.getBytes("UTF-8"));
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted = cipher.doFinal(password.getBytes());
            pwd = toHexString(encrypted);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return pwd;
    }
    private static final char[] HEX_CHAR = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};
    public static String toHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        int num;
        for(byte b : bytes) {
            num = b<0?256+b:b;
            sb.append(HEX_CHAR[num/16]).append(HEX_CHAR[num%16]);
        }
        return sb.toString().toUpperCase();
    }

    public static String getBase64Code(String password) {
        String pwd = "";
        try {
            pwd = new String(Base64.getEncoder().encode(password.getBytes()));
        }catch (Exception e) {
            e.printStackTrace();
        }
        return pwd;
    }
}
