package com.futong.gemini.plugin.product.aofei.enums;

public enum TypeEnum {

    ITEMS("items"),

    ROWS("rows"),
    CPU("cpuUtilizationRate"),
    MEM("memoryUtilizationRate"),
    GPUCORE("gpuCoreUtilizationRate"),
    GPU("gpuUtilizationRate"),

    RESULT("result"),
    LOGCONTENT("logContent"),

    RECORDS("records"),
    GET("get"),
    NODES("nodes"),
    NODElIST("nodeList"),
    CUSTOMENVS("customEnvs"),
    CUSTOMPORTS("customPorts"),
    IMAGETAGlIST("imageTagList"),
    OTHERTAGLIST("otherTagList"),
    DATA("data"),
    POST("post");


    private String value;

    private TypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
