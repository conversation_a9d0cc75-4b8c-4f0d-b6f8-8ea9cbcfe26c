package com.futong.gemini.plugin.product.aofei.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

public class JsonFieldNameConverter {

    /**
     * 将JSONArray中的JSON对象的字段名转换为驼峰命名
     *
     * @param jsonArray 待转换的JSONArray
     * @return 转换后的JSONArray
     */
    public static JSONArray convertToCamelCase(JSONArray jsonArray) {
        JSONArray resultArray = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.put("format_json", JSONUtil.parseObj(jsonObject.get("open_json")));
            jsonObject.put("file_path_with_slash","/"+jsonObject.getString("file_path"));
            JSONObject convertedObject = convertToCamelCase(jsonObject);
            resultArray.add(convertedObject);
        }
        return resultArray;
    }

    /**
     * 将JSONObject的字段名转换为驼峰命名
     *
     * @param jsonObject 待转换的JSONObject
     * @return 转换后的JSONObject
     */
    public static JSONObject convertToCamelCase(JSONObject jsonObject) {
        JSONObject resultObject = new JSONObject();
        Map<String, Object> map = jsonObject.getInnerMap();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String originalKey = entry.getKey();
            String camelCaseKey = toCamelCase(originalKey);
            Object value = entry.getValue();
            if (value instanceof JSONObject) {
                // 递归处理嵌套的JSONObject
                value = convertToCamelCase((JSONObject) value);
            } else if (value instanceof JSONArray) {
                // 递归处理嵌套的JSONArray
                value = convertToCamelCase((JSONArray) value);
            }
            resultObject.put(camelCaseKey, value);
        }
        return resultObject;
    }

    /**
     * 将下划线命名的字符串转换为驼峰命名
     *
     * @param underscoreString 下划线命名的字符串
     * @return 驼峰命名的字符串
     */
    public static String toCamelCase(String underscoreString) {
        if (underscoreString == null || underscoreString.isEmpty()) {
            return underscoreString;
        }
        StringBuilder camelCaseString = new StringBuilder();
        boolean nextIsUpperCase = false;
        for (char c : underscoreString.toCharArray()) {
            if (c == '_') {
                nextIsUpperCase = true;
            } else {
                if (nextIsUpperCase) {
                    camelCaseString.append(Character.toUpperCase(c));
                    nextIsUpperCase = false;
                } else {
                    camelCaseString.append(Character.toLowerCase(c));
                }
            }
        }
        return camelCaseString.toString();
    }

    public static void main(String[] args) {
        String jsonString = "[{\"user_name\":\"JohnDoe\",\"age\":30},{\"full_name\":\"Jane_Smith\",\"age\":25}]";
        JSONArray jsonArray = JSONArray.parseArray(jsonString);
        JSONArray camelCaseArray = convertToCamelCase(jsonArray);
        System.out.println(camelCaseArray.toJSONString());
    }
}
