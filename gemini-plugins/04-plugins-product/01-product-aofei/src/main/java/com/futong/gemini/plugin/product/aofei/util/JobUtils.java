package com.futong.gemini.plugin.product.aofei.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class JobUtils {

    public static final JobUtils bean = new JobUtils();

    //拆分配置数据采集dataJob
    public  List<JobInfo> splitDataJob(DescribeAofeiRequest request , List<AofeiUser> userList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(userList) || ObjectUtil.isEmpty(userList)){
            return null;
        }
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {
            List<AofeiUser> ids = new ArrayList<>();
            Integer size = request.getFecthSize() ==null? 1: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, userList.size(), (int)Math.ceil((float)userList.size()/size)));
                for (int i = 1; i <= userList.size(); i++) {
                    ids.add(userList.get(i - 1));
                    if (i % size == 0 || i == userList.size()) {
                        JSONObject cloneArguments = arguments.clone();
                        JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                        cloneBody.put("ids", ids);
                        cloneBody.put("resourceType", resourceType);
                        cloneBody.put("fecthSize", request.getFecthSize());
                        cloneBody.put("condition", resourceTypes[0]);
                        cloneArguments.put("body", cloneBody);
                        JobInfo jobInfo = new JobInfo();
                        jobInfo.setRequest(cloneArguments);
                        jobs.add(jobInfo);
                        ids = new ArrayList<>();
                    }
                }
        });
        return jobs;
    }

    public  List<JobInfo> splitPropertyDataJob(DescribeAofeiRequest request , List<AofeiUser> userList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(userList) || ObjectUtil.isEmpty(userList)){
            return null;
        }
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {

            Integer size = request.getFecthSize() ==null? 1: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, userList.size(), (int)Math.ceil((float)userList.size()/size)));
            for (int i = 1; i <= userList.size(); i++) {
                List<AofeiUser> ids = new ArrayList<>();
                ids.add(userList.get(i - 1));
                String modelUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getPropertyUrl(cloudAccessBean.getScvmmRole(),resourceType), null);
                int key = 1;
                int count = 1000;
                int total = 0;
                String json = "{\"current\": 1,\"size\": "+count+",  \"scope\": "+key+",\"taskTags\": []}";
                if (ObjectUtil.isNotEmpty(ids)) {
                    JSONArray propertyArray = Converts.convertData(ids, cloudAccessBean, json, modelUrl,TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
                    if (ObjectUtil.isNotEmpty(propertyArray)) {
                        total = propertyArray.size();
                    }
                }
                int page = total%AofeiConstants.FETCH_SIZE==0?total/ AofeiConstants.FETCH_SIZE:total/AofeiConstants.FETCH_SIZE+1;
                for(int j = 1; j <= page; j++) {
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("resourceType", resourceType);
                    cloneBody.put("fecthSize", j);
                    cloneBody.put("condition", resourceTypes[0]);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                }
            }
        });
        return jobs;
    }

    public  List<JobInfo> splitDevEnvDataJob(DescribeAofeiRequest request , List<AofeiUser> userList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(userList) || ObjectUtil.isEmpty(userList)){
            return null;
        }
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {

            Integer size = request.getFecthSize() ==null? 1: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, userList.size(), (int)Math.ceil((float)userList.size()/size)));
            for (int i = 1; i <= userList.size(); i++) {
                List<AofeiUser> ids = new ArrayList<>();
                ids.add(userList.get(i - 1));
                String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getDevEnvUrl(cloudAccessBean.getScvmmRole()),null);
                JSONArray array = Converts.convertData(ids, cloudAccessBean, null,devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
                int total = 0;
                if (ObjectUtil.isNotEmpty(array)) {
                    total = array.size();
                }
                int page = total%AofeiConstants.DEV_EVN_FETCH_SIZE==0?total/ AofeiConstants.DEV_EVN_FETCH_SIZE:total/AofeiConstants.DEV_EVN_FETCH_SIZE+1;
                for(int j = 1; j <= page; j++) {
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("resourceType", resourceType);
                    cloneBody.put("fecthSize", j);
                    cloneBody.put("condition", resourceTypes[0]);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                }
            }
        });
        return jobs;
    }

    public  List<JobInfo> splitTaskDataJob(DescribeAofeiRequest request , List<AofeiUser> userList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(userList) || ObjectUtil.isEmpty(userList)){
            return null;
        }
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {

            Integer size = request.getFecthSize() ==null? 1: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, userList.size(), (int)Math.ceil((float)userList.size()/size)));
            for (int i = 1; i <= userList.size(); i++) {
                List<AofeiUser> ids = new ArrayList<>();
                ids.add(userList.get(i - 1));

                String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskUrl(cloudAccessBean.getScvmmRole()), null);
                JSONObject config = new JSONObject();
                config.put("pageNum", 1);
                config.put("pageSize", 1000);
                JSONObject queryCriteria = new JSONObject();
                config.put("queryCriteria", queryCriteria);

                queryCriteria.put("createUser", userList.get(i - 1).getUserId());
                queryCriteria.put("isLargeModel", 1);
                JSONArray objects = Converts.convertDataByUser(userList.get(i - 1), cloudAccessBean, config.toJSONString(), devEnvUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
                int total = 0;
                if (ObjectUtil.isNotEmpty(objects)) {
                    total = objects.size();
                }
                int page = total%AofeiConstants.DEV_EVN_FETCH_SIZE==0?total/ AofeiConstants.DEV_EVN_FETCH_SIZE:total/AofeiConstants.DEV_EVN_FETCH_SIZE+1;
                for(int j = 1; j <= page; j++) {
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("resourceType", resourceType);
                    cloneBody.put("fecthSize", j);
                    cloneBody.put("condition", resourceTypes[0]);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                }
            }
        });
        return jobs;
    }

    public  List<JobInfo> splitFineTaskDataJob(DescribeAofeiRequest request , List<AofeiUser> userList, String[] resourceTypes, JSONObject arguments){
        if(ObjectUtil.isNull(userList) || ObjectUtil.isEmpty(userList)){
            return null;
        }
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        List<JobInfo> jobs = new ArrayList<>();
        Arrays.stream(resourceTypes).collect(Collectors.toList()).forEach(resourceType -> {

            Integer size = request.getFecthSize() ==null? 1: request.getFecthSize();
            log.info(StrUtil.format("资源类型 ={},共有{}条,分: {} 次同步.",resourceType, userList.size(), (int)Math.ceil((float)userList.size()/size)));
            for (int i = 1; i <= userList.size(); i++) {
                List<AofeiUser> ids = new ArrayList<>();
                ids.add(userList.get(i - 1));

                String fineUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTaskUrl(cloudAccessBean.getScvmmRole()), null);
                JSONObject config = new JSONObject();
                config.put("pageNum", 1);
                config.put("pageSize", 1000);
                config.put("userId", userList.get(i - 1).getUserId());
                JSONArray objects = Converts.convertDataByUser(userList.get(i - 1), cloudAccessBean, config.toJSONString(), fineUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());

                int total = 0;
                if (ObjectUtil.isNotEmpty(objects)) {
                    total = objects.size();
                }
                int page = total%AofeiConstants.DEV_EVN_FETCH_SIZE==0?total/ AofeiConstants.DEV_EVN_FETCH_SIZE:total/AofeiConstants.DEV_EVN_FETCH_SIZE+1;
                for(int j = 1; j <= page; j++) {
                    JSONObject cloneArguments = arguments.clone();
                    JSONObject cloneBody = cloneArguments.getJSONObject("body").clone();
                    cloneBody.put("ids", ids);
                    cloneBody.put("resourceType", resourceType);
                    cloneBody.put("fecthSize", j);
                    cloneBody.put("condition", resourceTypes[0]);
                    cloneArguments.put("body", cloneBody);
                    JobInfo jobInfo = new JobInfo();
                    jobInfo.setRequest(cloneArguments);
                    jobs.add(jobInfo);
                }
            }
        });
        return jobs;
    }
}
