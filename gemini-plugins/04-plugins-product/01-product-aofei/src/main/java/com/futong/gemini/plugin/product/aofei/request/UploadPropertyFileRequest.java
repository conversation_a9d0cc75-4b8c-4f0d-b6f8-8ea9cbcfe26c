package com.futong.gemini.plugin.product.aofei.request;

import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
public class UploadPropertyFileRequest implements Serializable {

    private String cmpId;

    private String id;

    private String versionId;

    private String currentPath;

    private String filePath;

    private MultipartFile chunk;

    private Long chunkSize;

    private Long index;

    private Long total;

    private String submitId;
}
