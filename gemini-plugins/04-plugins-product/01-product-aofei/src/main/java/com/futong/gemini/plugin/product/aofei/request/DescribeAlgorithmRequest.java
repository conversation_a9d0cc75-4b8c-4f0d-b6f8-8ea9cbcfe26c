package com.futong.gemini.plugin.product.aofei.request;

import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DescribeAlgorithmRequest implements Serializable {

    private String name;
    private String description;
    private List<String> taskTags;
    private Integer authority;
    private ShareInfo shareInfo;
    private AofeiToken authToken;

    @Data
    public static class ShareInfo {
        private List<String> users;
        private List<String> groups;
    }
}
