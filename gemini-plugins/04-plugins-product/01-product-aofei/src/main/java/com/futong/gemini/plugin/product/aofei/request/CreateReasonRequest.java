package com.futong.gemini.plugin.product.aofei.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CreateReasonRequest {
    private ModelServer modelServer;
    private List<ModelServerVersion> modelServerVersions;

    @Data
    public static class ModelServer {
        private String name;
        private String description;
        private String createUser;
        private String userName;
        private String path = "";
        private Integer isLargeModel;
    }

    @Data
    public static class ModelServerVersion {
        private String createUser;
        private String userName;
        private Integer cpu;
        private Integer memory;
        private String command;
        private String envs;
        private List<Map<String, String>> envsList;
        private Integer rate;
        private String modelVersionId;
        private String modelName;
        private String modelVersionName;
        private String modelId;
        private String modelInfo;
        private Integer customImageFlag;
        private Integer autoRecovery=0;
        private Integer framework;
        private List<Object> rates = new ArrayList<>();
        private String reqMethod;
        private String resourceGroupName;
        private String resourceGroupId;
        private String resourceGroupKey;
        private String gpuShare;
        private Integer gpu;
        private Integer predictMaxReplicas;
        private Integer predictMinReplicas;
        private Integer predictReplicas;
        private Integer metric;
        private Integer monitorThreshold;
        private String volumes ="[]";
        private String hyperParameterList="[]";
        private String volumePath;
        private String modelFloat;
        private Integer deploymentMode = 1;
        private String scenario = "算力任务（在线服务）";
        private Integer isLargeModel = 1;
    }
}
