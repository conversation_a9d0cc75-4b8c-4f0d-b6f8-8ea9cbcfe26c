package com.futong.gemini.plugin.product.aofei.request;

import com.alibaba.fastjson.JSONArray;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 创建开发环境请求参数
 */
@Setter
@Getter
public class CreateDevEnvRequest implements Serializable {

    private String name;

    private String image;

    private String imageId;

    private String imageVersionId;

    private Integer cpuLimit;

    private Integer memoryLimit;

    private Boolean enableSsh;

    private Integer sshAuthType;

    private String sshPassword;

    private Boolean enableSharedMemory;

    private String description;

    private String resourceGroupId;

    private String workingDir;

    private Integer imageType;

    private boolean start;

    private AofeiToken authToken;
}
