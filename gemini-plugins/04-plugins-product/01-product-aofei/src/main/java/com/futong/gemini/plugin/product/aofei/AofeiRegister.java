package com.futong.gemini.plugin.product.aofei;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.function.FTAction;
import com.futong.gemini.plugin.product.aofei.enums.ActionType;
import com.futong.gemini.plugin.product.aofei.sampler.FetchSampler;
import com.futong.gemini.plugin.product.aofei.service.*;

import java.util.HashMap;
import java.util.Map;

public class AofeiRegister {
    private static Map<String, FTAction> actions = new HashMap<>();


    public static <Q, R, C> void register(String fetchType, FTAction<JSONObject> ftAction) {
        actions.put(fetchType, ftAction);
    }

    public static boolean isNotExists(String action) {
        return !isExists(action);
    }

    public static boolean isExists(String action) {
        return actions.containsKey(action);
    }

    public static FTAction getAction(String action) {
        return actions.get(action);
    }

    public static void onAfterLoadCloudAccount() {
        register(ActionType.AUTH_PLATFORM_ACCOUNT.value(), AccountService::authAccount);//云账号验证
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM.value(), AccountService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH.value(), AccountService::createFetchDispatch);//添加默认调度任务
    }
    public static void onAfterLoadFetch() {
        register(ActionType.FETCH_IMAGE.value(), FetchSampler::fetchImage);//同步镜像
        register(ActionType.FETCH_MODEL.value(), FetchSampler::fetchModel);//同步模型
        register(ActionType.FETCH_ALGO.value(), FetchSampler::fetchAlgo);//同步算法
        register(ActionType.FETCH_DATASET.value(), FetchSampler::fetchDataset);//同步数据集
        register(ActionType.FETCH_RESOURCE_GROUP.value(), FetchSampler::fetchResourceGroup);//同步资源组
        register(ActionType.FETCH_DEV_ENV.value(), FetchSampler::fetchDevEnv);//同步开发机环境
        register(ActionType.FETCH_DEV_ENV_PERF.value(), FetchSampler::fetchDevEnvPerf);//同步开发机环境性能
//        register(ActionType.FETCH_File.value(), FetchSampler::fetchFile);//同步文件
        register(ActionType.FETCH_TASK.value(), FetchSampler::fetchReasonTask);//同步推理任务
        register(ActionType.FETCH_TASK_PERF.value(), FetchSampler::fetchReasonTaskPerf);//同步推理任务性能
        register(ActionType.FETCH_FINE_TASK.value(), FetchSampler::fetchFineTask);//同步调优任务
        register(ActionType.FETCH_FINE_TASK_PERF.value(), FetchSampler::fetchFineTaskPerf);//同步调优任务性能
        register(ActionType.FETCH_PLATFORM_AZONE.value(), FetchSampler::fetchAzone);//同步可用域
        register(ActionType.FETCH_TAG.value(), FetchSampler::fetchTag);//同步标签
    }
    public static void onAfterLoadOperate() {
        register(ActionType.CREATE_DEV_ENV.value(), DevEnvService::createDevEnv);//创建开发环境
        register(ActionType.START_DEV_ENV.value(), DevEnvService::startDevEnv);//启动开发环境
        register(ActionType.STOP_DEV_ENV.value(), DevEnvService::stopDevEnv);//停止开发环境
        register(ActionType.DELETE_DEV_ENV.value(), DevEnvService::deleteDevEnv);//删除开发环境
        register(ActionType.FETCH_FOLDER.value(), FileService::fetchFolder);//获取文件夹下文件
        register(ActionType.FETCH_PROPERTY_FOLDER.value(), FileService::fetchPropertyFolder);//获取文件夹下文件
        register(ActionType.OPEN_DEV_ENV.value(), DevEnvService::openDevEnv);//打开开发环境
        register(ActionType.GET_LOG.value(), FileService::getLog);//查看日志
        register(ActionType.GET_POD.value(), FileService::getPod);//查看容器
    }

    public static void onAfterLoadModel() {
        //创建模型
        register(ActionType.CREATE_MODEL.value(), DataHubPropertyService::createModel);
        //删除模型
        register(ActionType.DELETE_MODEL.value(), DataHubPropertyService::deleteModel);
        //创建模型版本
        register(ActionType.CREATE_MODEL_VERSION.value(), DataHubPropertyService::createModelVersion);
        //修改模型版本
        register(ActionType.UPDATE_MODEL_VERSION.value(), DataHubPropertyService::updateModelVersion);
        //删除模型版本
        register(ActionType.DELETE_MODEL_VERSION.value(), DataHubPropertyService::deleteModelVersion);
    }

    public static void onAfterLoadDataSet() {
        //创建数据集
        register(ActionType.CREATE_DATASET.value(), DataHubPropertyService::createDataSet);
        //删除数据集
        register(ActionType.DELETE_DATASET.value(), DataHubPropertyService::deleteDataSet);
        //创建数据集版本
        register(ActionType.CREATE_DATASET_VERSION.value(), DataHubPropertyService::createDataSetVersion);
        //修改数据集版本
        register(ActionType.UPDATE_DATASET_VERSION.value(), DataHubPropertyService::updateDataSetVersion);
        //删除数据集版本
        register(ActionType.DELETE_DATASET_VERSION.value(), DataHubPropertyService::deleteDataSetVersion);
    }

    public static void onAfterLoadImage() {
        //创建镜像
        register(ActionType.CREATE_IMAGE.value(), DataHubPropertyService::createImage);
        //删除镜像
        register(ActionType.DELETE_IMAGE.value(), DataHubPropertyService::deleteImage);
        //创建镜像版本
        register(ActionType.CREATE_IMAGE_VERSION.value(), DataHubPropertyService::createImageVersion);
        //修改镜像版本
        register(ActionType.UPDATE_IMAGE_VERSION.value(), DataHubPropertyService::updateImageVersion);
        //删除镜像版本
        register(ActionType.DELETE_IMAGE_VERSION.value(), DataHubPropertyService::deleteImageVersion);
    }

    public static void onAfterLoadAlgorithm() {
        //创建算法
        register(ActionType.CREATE_ALGORITHM.value(), DataHubPropertyService::createAlgorithm);
        //删除算法
        register(ActionType.DELETE_ALGORITHM.value(), DataHubPropertyService::deleteAlgorithm);
        //创建算法版本
        register(ActionType.CREATE_ALGORITHM_VERSION.value(), DataHubPropertyService::createAlgorithmVersion);
        //修改算法版本
        register(ActionType.UPDATE_ALGORITHM_VERSION.value(), DataHubPropertyService::updateAlgorithmVersion);
        //删除算法版本
        register(ActionType.DELETE_ALGORITHM_VERSION.value(), DataHubPropertyService::deleteAlgorithmVersion);
    }

    public static void onAfterLoadPropertyFile(){
        //创建文件夹：镜像、数据集、模型、算法
        register(ActionType.CREATE_PROPERTY_FOLDER.value(), FileService::createPropertyFolder);
        //上传文件（夹）：镜像、数据集、模型、算法
        register(ActionType.UPLOAD_PROPERTY_FILE.value(), FileService::uploadPropertyFile);
        //读取文件：镜像、数据集、模型、算法
        register(ActionType.READ_PROPERTY_FILE.value(), FileService::readPropertyFile);
        //编辑文件：镜像、数据集、模型、算法
        register(ActionType.UPDATE_PROPERTY_FILE.value(), FileService::updatePropertyFile);
        //获取文件URL：镜像、数据集、模型、算法
        register(ActionType.QUERY_PROPERTY_FILE_URL.value(), FileService::queryPropertyFileUrl);
        //删除文件：镜像、数据集、模型、算法
        register(ActionType.DELETE_PROPERTY_FILE.value(), FileService::deletePropertyFile);
        //创建算法策略
        register(ActionType.CREATE_ALGORITHM_STRATEGY.value(), DataHubPropertyService::createAlgorithmStrategy);
        //修改算法策略
        register(ActionType.UPDATE_ALGORITHM_STRATEGY.value(), DataHubPropertyService::updateAlgorithmStrategy);
        //删除算法策略
        register(ActionType.DELETE_ALGORITHM_STRATEGY.value(), DataHubPropertyService::deleteAlgorithmStrategy);
    }

    public static void onAfterLoadFile(){
        //创建文件夹
        register(ActionType.CREATE_FOLDER.value(), FileService::createFolder);
        //删除文件（夹）
        register(ActionType.DELETE_FILE.value(), FileService::deleteFile);
        //文件（夹）是否被共享
        register(ActionType.FIND_SHARE_FILE.value(), FileService::findShareFile);
        //重命名文件（夹）
        register(ActionType.RENAME_FILE.value(), FileService::renameFile);
        //资产文件上传分片合并
        register(ActionType.MERGE_PROPERTY_FILE.value(), FileService::mergePropertyFile);
        //大文件上传分片合并
        register(ActionType.MERGE_LARGE_FILE.value(), FileService::mergeLargeFile);
        //文件是否存在
        register(ActionType.CHECK_FILE_EXIST.value(), FileService::checkFileExist);
        //小文件上传
        register(ActionType.UPLOAD_SMALL_FILE.value(), FileService::uploadSmallFile);
        //大文件上传
        register(ActionType.UPLOAD_LARGE_FILE.value(), FileService::uploadLargeFile);
        //文件下载
        register(ActionType.DOWNLOAD_FILE.value(), FileService::downloadFile);
        //文件夹下载
        register(ActionType.DOWNLOAD_FOLDER.value(), FileService::downloadFolder);
        //公有文件下载
        register(ActionType.DOWNLOAD_PUBLIC_FILE.value(), FileService::downloadPublicFile);
    }

    public static void onAfterLoadTask(){
        //停止推理服务
        register(ActionType.STOP_FINE_TUNE_TASK.value(), TaskService::stopFineTuneTask);
        register(ActionType.START_FINE_TUNE_TASK.value(), TaskService::startFineTuneTask);
        //创建调优服务
        register(ActionType.CREATE_FINE_TUNE_TASK.value(), TaskService::createFineTuneTask);
        //删除调优服务
        register(ActionType.DELETE_MODEL_TASK.value(), TaskService::deleteFineTuneTask);
        register(ActionType.DELETE_REASON_TASK.value(), TaskService::deleteResonTask);
        //停止推理服务
        register(ActionType.STOP_REASON_TASK.value(), TaskService::stopReasonTask);
        //启动推理服务
        register(ActionType.START_REASON_TASK.value(), TaskService::startReasonTask);
        //创建推理服务
        register(ActionType.CREATE_REASON_TASK.value(), TaskService::createReasonTask);
        //获取大模型调优任务日志
        register(ActionType.GET_FINE_TUNE_TASK_POD_LOG.value(), TaskService::getFineTuneTaskPodLog);
        //获取推理服务日志
        register(ActionType.GET_REASON_TASK_POD_LOG.value(), TaskService::getReasonTaskPodLog);

        register(ActionType.COMPLETION.value(), TaskService::completion);//创建对话

    }
}
