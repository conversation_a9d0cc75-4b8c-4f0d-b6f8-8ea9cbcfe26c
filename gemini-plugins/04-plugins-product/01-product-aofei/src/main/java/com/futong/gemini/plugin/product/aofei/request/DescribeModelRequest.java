package com.futong.gemini.plugin.product.aofei.request;

import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DescribeModelRequest implements Serializable {


    private Application application;
    private List<AiApplicationVersionList> aiApplicationVersionList;
    private CreateDTO createDTO;
    private AofeiToken authToken;

    @Data
    public static class Application {
        private String name;
        private String createUser;
        private String description;
        private String userName;
        private String type;
        private String developer;
        private Integer loginUser;
    }

    @Data
    public static class AiApplicationVersionList {
        private String applicationName;
        private String createUser;
        private String userName;
        private String description;
        private String version;
        private Integer source;
        private String trainTaskVersionId;
        private String llmFineTuneTaskId;
        private String image;
        private String imageType;
        private Integer framework;
        private Integer saveType;
        private String savePath;
        private Integer configSaveType;
        private String label;
        private String scale;
        private String modelStructure;
        private Integer loginUser;
    }

    @Data
    public static class CreateDTO {
        private String name;
        private String description;
        private List<String> taskTags;
        private Integer authority;
        private ShareInfo shareInfo;
    }

    @Data
    public static class ShareInfo {
        private List<String> users;
        private List<String> groups;
    }
}
