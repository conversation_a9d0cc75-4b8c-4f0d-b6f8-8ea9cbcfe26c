package com.futong.gemini.plugin.product.aofei.enums;

import com.futong.constant.dict.ResourceType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ModuleType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OperationType;
import com.futong.gemini.plugin.product.aofei.service.FileService;
import com.google.common.base.CaseFormat;

public enum ActionType {

    //账号验证
    AUTH_PLATFORM_ACCOUNT("账号校验", ModuleType.PLATFORM, null, OperationType.QUERY),
    FETCH_PLATFORM_USER("同步用户", ModuleType.PLATFORM, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PROPERTY_IMAGE("同步镜像", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PROPERTY_ALGORITHM("同步算法", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PROPERTY_DATASET("同步数据集", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PROPERTY("同步模型", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_IMAGE("同步镜像", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_TAG("同步标签", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_ALGO("同步算法", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_MODEL("同步模型", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_DATASET("同步数据集", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_RESOURCE_GROUP("同步资源组", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_DEV_ENV("同步开发环境", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_DEV_ENV_PERF("同步开发环境性能", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_TASK("同步任务", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_TASK_PERF("同步任务性能", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_FINE_TASK("同步调优任务", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_FINE_TASK_PERF("同步调优任务性能", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_PLATFORM_AZONE("同步可用区", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_File("同步文件", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    FETCH_KEYPAIR("同步密钥对", ModuleType.COMPUTE, ResourceType.CMDB_KEYPAIR_RES, OperationType.FETCH),
    GET_PLATFORM_ACCOUNT_ADD_FORM("查询账号添加表单", ModuleType.PLATFORM, null, OperationType.QUERY),
    CREATE_PLATFORM_FETCH_DISPATCH("添加默认平台调度任务", ModuleType.PLATFORM, null, OperationType.CREATE),

    /***********开发机操作接口************************/
    CREATE_DEV_ENV("创建开发环境", ModuleType.PLATFORM, null, OperationType.CREATE),
    START_DEV_ENV("启动开发环境", ModuleType.PLATFORM, null, OperationType.START),
    OPEN_DEV_ENV("打开开发环境", ModuleType.PLATFORM, null, OperationType.START),
    GET_LOG("查看日志", ModuleType.PLATFORM, null, OperationType.START),
    GET_POD("查看容器", ModuleType.PLATFORM, null, OperationType.START),
    STOP_DEV_ENV("停止开发环境", ModuleType.PLATFORM, null, OperationType.STOP),
    DELETE_DEV_ENV("删除开发环境", ModuleType.PLATFORM, null, OperationType.DELETE),
    FETCH_FOLDER("获取文件夹下文件", ModuleType.PLATFORM, null, OperationType.FETCH),
    FETCH_PROPERTY_FOLDER("获取资产下文件夹下文件", ModuleType.PLATFORM, null, OperationType.FETCH),
    COMPLETION("对话", ModuleType.PLATFORM, null, OperationType.CREATE),


    /***********模型操作接口************************/
    CREATE_MODEL("创建模型", ModuleType.PLATFORM, null, OperationType.CREATE),
    DELETE_MODEL("删除模型", ModuleType.PLATFORM, null, OperationType.DELETE),
    CREATE_MODEL_VERSION("创建模型版本", ModuleType.PLATFORM, null, OperationType.CREATE),
    UPDATE_MODEL_VERSION("修改模型版本", ModuleType.PLATFORM, null, OperationType.UPDATE),
    DELETE_MODEL_VERSION("删除模型版本", ModuleType.PLATFORM, null, OperationType.DELETE),

    /***********数据集操作接口************************/
    CREATE_DATASET("创建数据集", ModuleType.PLATFORM, null, OperationType.CREATE),
    DELETE_DATASET("删除数据集", ModuleType.PLATFORM, null, OperationType.DELETE),
    CREATE_DATASET_VERSION("创建数据集版本", ModuleType.PLATFORM, null, OperationType.CREATE),
    UPDATE_DATASET_VERSION("修改数据集版本", ModuleType.PLATFORM, null, OperationType.UPDATE),
    DELETE_DATASET_VERSION("删除数据集版本", ModuleType.PLATFORM, null, OperationType.DELETE),

    /***********镜像操作接口************************/
    CREATE_IMAGE("创建镜像", ModuleType.PLATFORM, null, OperationType.CREATE),
    DELETE_IMAGE("删除镜像", ModuleType.PLATFORM, null, OperationType.DELETE),
    CREATE_IMAGE_VERSION("创建镜像版本", ModuleType.PLATFORM, null, OperationType.CREATE),
    UPDATE_IMAGE_VERSION("修改镜像版本", ModuleType.PLATFORM, null, OperationType.UPDATE),
    DELETE_IMAGE_VERSION("删除镜像版本", ModuleType.PLATFORM, null, OperationType.DELETE),

    /***********算法操作接口************************/
    CREATE_ALGORITHM("创建算法", ModuleType.PLATFORM, null, OperationType.CREATE),
    DELETE_ALGORITHM("删除算法", ModuleType.PLATFORM, null, OperationType.DELETE),
    CREATE_ALGORITHM_VERSION("创建算法版本", ModuleType.PLATFORM, null, OperationType.CREATE),
    UPDATE_ALGORITHM_VERSION("修改算法版本", ModuleType.PLATFORM, null, OperationType.UPDATE),
    DELETE_ALGORITHM_VERSION("删除算法版本", ModuleType.PLATFORM, null, OperationType.DELETE),
    CREATE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.PLATFORM, null, OperationType.CREATE),
    UPDATE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.PLATFORM, null, OperationType.UPDATE),
    DELETE_ALGORITHM_STRATEGY("创建算法策略", ModuleType.PLATFORM, null, OperationType.DELETE),

    /***********资产文件操作接口************************/
    CREATE_PROPERTY_FOLDER("创建文件夹", ModuleType.STORAGE, null, OperationType.CREATE),
    DELETE_PROPERTY_FILE("删除文件", ModuleType.STORAGE, null, OperationType.DELETE),
    READ_PROPERTY_FILE("读取文件内容", ModuleType.STORAGE, null, OperationType.QUERY),
    UPDATE_PROPERTY_FILE("修改文件内容", ModuleType.STORAGE, null, OperationType.UPDATE),
    QUERY_PROPERTY_FILE_URL("查询文件URL", ModuleType.STORAGE, null, OperationType.QUERY),
    DOWNLOAD_PROPERTY_FILE("下载文件", ModuleType.STORAGE, null, OperationType.GET),
    UPLOAD_PROPERTY_FILE("上传文件（夹）", ModuleType.STORAGE, null, OperationType.CREATE),
    CHECK_FILE_EXIST("文件（夹）是否存在", ModuleType.STORAGE, null, OperationType.QUERY),

    /***********文件操作接口************************/
    CREATE_FOLDER("创建文件夹", ModuleType.STORAGE, null, OperationType.CREATE),
    DELETE_FILE("删除文件（夹）", ModuleType.STORAGE, null, OperationType.DELETE),
    FIND_SHARE_FILE("文件（夹）是否被共享", ModuleType.STORAGE, null, OperationType.QUERY),
    RENAME_FILE("重命名文件（夹）", ModuleType.STORAGE, null, OperationType.UPDATE),
    DOWNLOAD_FILE("下载文件", ModuleType.STORAGE, null, OperationType.GET),
    DOWNLOAD_FOLDER("下载文件夹", ModuleType.STORAGE, null, OperationType.GET),
    UPLOAD_SMALL_FILE("小文件上传", ModuleType.STORAGE, null, OperationType.CREATE),
    UPLOAD_LARGE_FILE("大文件上传", ModuleType.STORAGE, null, OperationType.CREATE),
    MERGE_PROPERTY_FILE("资产文件上传合并", ModuleType.STORAGE, null, OperationType.CREATE),
    MERGE_LARGE_FILE("大文件上传合并", ModuleType.STORAGE, null, OperationType.CREATE),
    DOWNLOAD_PUBLIC_FILE("下载文件（公共文件）", ModuleType.STORAGE, null, OperationType.GET),

    //大模型调优任务
    CREATE_FINE_TUNE_TASK("创建大模型调优任务", ModuleType.COMPUTE, null, OperationType.CREATE),
    STOP_FINE_TUNE_TASK("停止大模型调优任务", ModuleType.COMPUTE, null, OperationType.STOP),
    START_FINE_TUNE_TASK("启动大模型调优任务", ModuleType.COMPUTE, null, OperationType.START),
    DELETE_MODEL_TASK("删除大模型调优任务", ModuleType.COMPUTE, null, OperationType.DELETE),

    //推理服务
    STOP_REASON_TASK("停止推理服务", ModuleType.COMPUTE, null, OperationType.STOP),
    START_REASON_TASK("启动推理服务", ModuleType.COMPUTE, null, OperationType.START),
    CREATE_REASON_TASK("创建推理服务", ModuleType.COMPUTE, null, OperationType.CREATE),
    DELETE_REASON_TASK("删除推理服务", ModuleType.COMPUTE, null, OperationType.DELETE),
    GET_FINE_TUNE_TASK_POD_LOG("获取大模型调优任务日志", ModuleType.COMPUTE, null, OperationType.QUERY),
    GET_REASON_TASK_POD_LOG("获取推理服务日志", ModuleType.COMPUTE, null, OperationType.QUERY);
    /**
     * 资源同步
     */

    String cname;
    ModuleType moduleType;
    ResourceType resourceType;
    OperationType operationType;

    ActionType(String cname, ModuleType moduleType, ResourceType resourceType, OperationType operationType) {
        this.cname = cname;
        this.moduleType = moduleType;
        this.resourceType = resourceType;
        this.operationType = operationType;
    }

    public String cname() {
        return cname;
    }

    public ModuleType moduleType() {
        return moduleType;
    }

    public ResourceType resourceType() {
        return resourceType;
    }

    public OperationType operationType() {
        return operationType;
    }

    public String value() {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, this.name());
    }

}
