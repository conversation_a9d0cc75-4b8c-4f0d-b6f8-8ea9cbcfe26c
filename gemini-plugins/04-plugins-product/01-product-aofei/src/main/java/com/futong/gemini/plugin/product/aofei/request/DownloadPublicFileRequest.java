package com.futong.gemini.plugin.product.aofei.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DownloadPublicFileRequest implements Serializable {

    private String parentPath;

    private List<String> sPath;

    private String userId;

    private String fileName;

    private String path;

    private String publicUserId;

    private Boolean isBatch;
}
