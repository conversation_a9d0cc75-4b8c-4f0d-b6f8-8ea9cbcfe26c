
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.model.atlas.entity.CaDatahubProperty;
import com.futong.gemini.model.atlas.entity.CaDevEnv;
import com.futong.gemini.model.atlas.entity.CaTaskTag;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TagService {

    public static final TagService bean = new TagService();

    public void fetchTag(JSONObject arguments) {
        List<CaTaskTag> tagList = new ArrayList<>();
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        AofeiToken token = HttpClientUtil.getToken(BaseClient.auths.get());
        String tagUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getTagUrl(cloudAccessBean.getScvmmRole()), null);
        JSONArray imageList = Converts.getResourceToJsonArray(token,tagUrl, "", TypeEnum.IMAGETAGlIST.getValue());
        JSONArray otherList = Converts.getResourceToJsonArray(token,tagUrl, "", TypeEnum.OTHERTAGLIST.getValue());
        if (ObjectUtil.isNotEmpty(imageList)) {
            imageList.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                Converts.toCaTaskTag(tagList,BaseClient.auths.get(), tempObj,null,"image");

            });
        }
        if (ObjectUtil.isNotEmpty(otherList)) {
            otherList.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                Converts.toCaTaskTag(tagList,BaseClient.auths.get(), tempObj,null,"other");
            });
        }
        //发送消息
        log.info("推送标签数量：{}", tagList.size());
        BaseUtils.sendAtlasMessage(tagList, arguments,BaseClient.auths.get().getCmpId(), CaTaskTag.class);
    }

}
