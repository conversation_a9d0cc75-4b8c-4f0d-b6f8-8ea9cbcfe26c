
package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.futong.gemini.model.otc.nxc.entity.CmdbFlavor;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.request.DescribeAofeiRequest;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;

import java.util.ArrayList;
import java.util.List;

public class UserService {

    public static final UserService bean = new UserService();

    public void fetchUser(JSONObject arguments) {
        List<CmdbFlavor> userList = new ArrayList<>();
        List<AofeiUser> users = fetchAofeiUser(arguments);
        if(ObjectUtil.isNotEmpty(users)){
            users.forEach(user ->{
                // 规格资源数据模型转换
//                userList.add(Converts.toUser(BaseClient.auths.get(), user));
            });
        }
        // 推送规格数据
        BaseUtils.sendMessage(userList, arguments);
    }

    public List<AofeiUser> fetchAofeiUser(JSONObject arguments){
        List<AofeiUser> users =  new ArrayList<>();
        DescribeAofeiRequest request = BaseClient.bodys.get().toJavaObject(DescribeAofeiRequest.class);
        JSONArray array = Converts.fetchResourceToJsonArray(request,
                URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getUserUrl(""),new String[]{request.getAuthToken().getData().getToken()}),
                "");
        if(ObjectUtil.isNotEmpty(array)){
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 解析并转换JSON对象为CloudosFlavor对象
                AofeiUser user = Converts.parseAndConvert(tempObj.toJSONString(), new TypeReference<AofeiUser>() {});
                users.add(user);
            });
        }
        return users;
    }
}
