package com.futong.gemini.plugin.product.aofei.enums;

public enum ResourceEnum {


    MODEL("model"),

    // 云主机
    DATASET("dataset"),

    ALGO("algo"),
    FILE("file"),
    DEV_ENV("dev_env"),
    KEYPAIR("keypair"),
    VERSION("version"),
    EVENT("event"),
    FINE("fine"),
    FINE_PERF("finePerf"),
    EVALUATE("evaluate"),
    REASON("reason"),
    REASON_PERF("reasonPerf"),
    AI("ai"),
    IMAGE("image"),
    RESOURPCEGROUP("resourceGroup"),
    TAG("tag"),
    TASK_INSTANCE("task_instance");


    private String value;

    private ResourceEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
