package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ActionType;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.http.HttpClientUtil;
import com.futong.gemini.plugin.product.aofei.request.*;
import com.futong.gemini.plugin.product.aofei.util.ComputeUtil;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import com.futong.gemini.plugin.product.aofei.vo.HttpResult;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiToken;
import com.futong.gemini.plugin.product.aofei.vo.token.AofeiTokenParam;
import com.futong.gemini.plugin.product.aofei.vo.user.AofeiUser;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class DataHubPropertyService {

    public static final DataHubPropertyService bean = new DataHubPropertyService();


    /**
     * 创建模型
     * @param arguments
     * @return
     */
    public static BaseResponse createModel(JSONObject arguments){
        try {
            log.info("创建模型接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeModelRequest request = BaseClient.bodys.get().toJavaObject(DescribeModelRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+ Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getModelCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建模型失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建模型失败");
        }
    }

    /**
     * 删除模型
     * @param arguments
     * @return
     */
    public static BaseResponse deleteModel(JSONObject arguments){
        String message = "成功删除模型.";
        try {
            log.info("删除模型接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeModelRequest request = BaseClient.bodys.get().toJavaObject(DescribeModelRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getModelDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{instanceId});
                log.info("删除模型Url={}", url);
                HttpClientUtil.deletePost(url,config);
            }
            return BaseResponse.SUCCESS.of(message);
        }  catch (Exception e) {
            log.error("删除模型失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除模型失败");
        }
    }

    /**
     * 创建模型版本
     * @param arguments
     * @return
     */
    public static BaseResponse createModelVersion(JSONObject arguments){
        try {
            log.info("创建模型版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateVersionRequest request = BaseClient.bodys.get().toJavaObject(CreateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getModelVersionCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建模型版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建模型版本失败");
        }
    }

    /**
     * 修改模型版本
     * @param arguments
     * @return
     */
    public static BaseResponse updateModelVersion(JSONObject arguments){
        try {
            log.info("修改模型版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UpdateVersionRequest request = BaseClient.bodys.get().toJavaObject(UpdateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getModelVersionUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.putByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("修改模型版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改模型版本失败");
        }
    }

    /**
     * 删除模型版本
     * @param arguments
     * @return
     */
    public static BaseResponse deleteModelVersion(JSONObject arguments){
        try {
            log.info("删除模型版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeleteVersionRequest request = BaseClient.bodys.get().toJavaObject(DeleteVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getModelVersionDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除模型版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除模型版本失败");
        }
    }

    /**
     * 创建数据集
     * @param arguments
     * @return
     */
    public static BaseResponse createDataSet(JSONObject arguments){
        try {
            log.info("创建数据集接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeDataSetRequest request = BaseClient.bodys.get().toJavaObject(DescribeDataSetRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDataSetCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建数据集失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建数据集失败");
        }
    }

    /**
     * 删除数据集
     * @param arguments
     * @return
     */
    public static BaseResponse deleteDataSet(JSONObject arguments){
        String message = "成功删除数据集.";
        try {
            log.info("删除数据集接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeDataSetRequest request = BaseClient.bodys.get().toJavaObject(DescribeDataSetRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDataSetDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{instanceId});
                HttpClientUtil.deletePost(url, config);
            }
            return BaseResponse.SUCCESS.of(message);
        }  catch (Exception e) {
            log.error("删除数据集失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除数据集失败");
        }
    }

    /**
     * 创建数据集版本
     * @param arguments
     * @return
     */
    public static BaseResponse createDataSetVersion(JSONObject arguments){
        try {
            log.info("创建数据集版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateVersionRequest request = BaseClient.bodys.get().toJavaObject(CreateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDataSetVersionCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建数据集版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建数据集版本失败");
        }
    }

    /**
     * 修改数据集版本
     * @param arguments
     * @return
     */
    public static BaseResponse updateDataSetVersion(JSONObject arguments){
        try {
            log.info("修改数据集版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UpdateVersionRequest request = BaseClient.bodys.get().toJavaObject(UpdateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDataSetVersionUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.putByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("修改数据集版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改数据集版本失败");
        }
    }

    /**
     * 删除数据集版本
     * @param arguments
     * @return
     */
    public static BaseResponse deleteDataSetVersion(JSONObject arguments){
        try {
            log.info("删除数据集版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeleteVersionRequest request = BaseClient.bodys.get().toJavaObject(DeleteVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getDataSetVersionDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除模型版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除模型版本失败");
        }
    }

    /**
     * 创建镜像
     * @param arguments
     * @return
     */
    public static BaseResponse createImage(JSONObject arguments){
        try {
            log.info("创建镜像接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeImageRequest request = BaseClient.bodys.get().toJavaObject(DescribeImageRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建镜像失败");
        }
    }

    /**
     * 删除镜像
     * @param arguments
     * @return
     */
    public static BaseResponse deleteImage(JSONObject arguments){
        String message = "成功删除镜像.";
        try {
            log.info("删除镜像接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeImageRequest request = BaseClient.bodys.get().toJavaObject(DescribeImageRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{instanceId});
                HttpClientUtil.deletePost(url, config);
            }
            return BaseResponse.SUCCESS.of(message);
        }  catch (Exception e) {
            log.error("删除镜像失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除镜像失败");
        }
    }

    /**
     * 创建镜像版本
     * @param arguments
     * @return
     */
    public static BaseResponse createImageVersion(JSONObject arguments){
        try {
            log.info("创建镜像版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateVersionRequest request = BaseClient.bodys.get().toJavaObject(CreateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageVersionCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建镜像版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建镜像版本失败");
        }
    }

    /**
     * 修改镜像版本
     * @param arguments
     * @return
     */
    public static BaseResponse updateImageVersion(JSONObject arguments){
        try {
            log.info("修改镜像版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UpdateVersionRequest request = BaseClient.bodys.get().toJavaObject(UpdateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageVersionUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.putByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("修改镜像版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改镜像版本失败");
        }
    }

    /**
     * 删除镜像版本
     * @param arguments
     * @return
     */
    public static BaseResponse deleteImageVersion(JSONObject arguments){
        try {
            log.info("删除镜像版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeleteVersionRequest request = BaseClient.bodys.get().toJavaObject(DeleteVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getImageVersionDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除镜像版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除镜像版本失败");
        }
    }

    /**
     * 创建算法
     * @param arguments
     * @return
     */
    public static BaseResponse createAlgorithm(JSONObject arguments){
        try {
            log.info("创建算法接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeAlgorithmRequest request = BaseClient.bodys.get().toJavaObject(DescribeAlgorithmRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建算法失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建算法失败");
        }
    }

    /**
     * 删除算法
     * @param arguments
     * @return
     */
    public static BaseResponse deleteAlgorithm(JSONObject arguments){
        String message = "成功删除算法.";
        try {
            log.info("删除算法接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DescribeAlgorithmRequest request = BaseClient.bodys.get().toJavaObject(DescribeAlgorithmRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{instanceId});
                HttpClientUtil.deletePost(url, config);
            }
            return BaseResponse.SUCCESS.of(message);
        }  catch (Exception e) {
            log.error("删除算法失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除算法失败");
        }
    }

    /**
     * 创建算法版本
     * @param arguments
     * @return
     */
    public static BaseResponse createAlgorithmVersion(JSONObject arguments){
        try {
            log.info("创建算法版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateVersionRequest request = BaseClient.bodys.get().toJavaObject(CreateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmVersionCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("创建算法版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建算法版本失败");
        }
    }

    /**
     * 修改算法版本
     * @param arguments
     * @return
     */
    public static BaseResponse updateAlgorithmVersion(JSONObject arguments){
        try {
            log.info("修改算法版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            UpdateVersionRequest request = BaseClient.bodys.get().toJavaObject(UpdateVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmVersionUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.putByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("修改算法版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改算法版本失败");
        }
    }

    /**
     * 删除算法版本
     * @param arguments
     * @return
     */
    public static BaseResponse deleteAlgorithmVersion(JSONObject arguments){
        try {
            log.info("删除算法版本接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            DeleteVersionRequest request = BaseClient.bodys.get().toJavaObject(DeleteVersionRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmVersionDeleteUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }  catch (Exception e) {
            log.error("删除算法版本失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除算法版本失败");
        }
    }

    /**
     * 创建算法策略
     * @param arguments
     * @return
     */
    public static  BaseResponse createAlgorithmStrategy(JSONObject arguments){
        try {
            log.info("创建算法策略接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateAlgoStrategyRequest request = BaseClient.bodys.get().toJavaObject(CreateAlgoStrategyRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmStrategyCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.postByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("创建算法策略失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建算法策略失败");
        }
    }

    /**
     * 修改算法策略
     * @param arguments
     * @return
     */
    public static  BaseResponse updateAlgorithmStrategy(JSONObject arguments){
        try {
            log.info("修改算法策略接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CreateAlgoStrategyRequest request = BaseClient.bodys.get().toJavaObject(CreateAlgoStrategyRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmStrategyUpdateUrl(cloudAccessBean.getScvmmRole()), null);
            String response = HttpClientUtil.putByCode(url, JSONObject.toJSONString(request), config);
            return new BaseDataResponse<>(response);
        }catch (Exception e){
            log.error("修改算法策略失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "修改算法策略失败");
        }
    }

    /**
     * 删除算法策略
     * @param arguments
     * @return
     */
    public static BaseResponse deleteAlgorithmStrategy(JSONObject arguments){
        String message = "成功删除算法策略.";
        try {
            log.info("删除算法策略接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            JSONObject body = BaseClient.bodys.get();
            List<String> instanceIds = getInstanceList(body);
            for (String instanceId : instanceIds) {
                String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getAlgorithmStrategyDeleteUrl(cloudAccessBean.getScvmmRole()), new String[]{instanceId});
                HttpClientUtil.deletePost(url, config);
            }
            return BaseResponse.SUCCESS.of(message);
        }  catch (Exception e) {
            log.error("删除算法策略失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除算法策略失败");
        }
    }



    public static void main(String[] args) {
        System.out.println(ActionType.CREATE_MODEL.value());
        String json = "{\"name\":\"imageTest11\",\"description\":\"imageTest11\",\"taskTags\":[\"12\",\"10\",\"11\",\"13\"],\"authority\":3,\"schemaType\":0,\"shareInfo\":{\"users\":[\"3\",\"16\"],\"groups\":[\"202\"]}}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        DescribeImageRequest request = jsonObject.toJavaObject(DescribeImageRequest.class);
        System.out.println(request);
    }

    public static List<String> getInstanceList(JSONObject body) {
        List<String> instanceIds = new ArrayList<>();
        if(body.containsKey("cis")) {
            JSONArray cis = body.getJSONArray("cis");
            for (int i = 0; i < cis.size(); i++) {
                JSONObject ci = cis.getJSONObject(i);
                String instanceId = ci.getString("openId");
                instanceIds.add(instanceId);
            }
        }else if(body.containsKey("ci")){
            JSONObject ci = body.getJSONObject("ci");
            String instanceId = ci.getString("openId");
            instanceIds.add(instanceId);
        }
        return instanceIds;
    }

}
