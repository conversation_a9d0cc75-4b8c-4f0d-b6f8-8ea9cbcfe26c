package com.futong.gemini.plugin.product.aofei.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.gemini.model.atlas.entity.CaLargeModelFineTuneTask;
import com.futong.gemini.model.atlas.entity.CaLargeModelReasoningCallNum;
import com.futong.gemini.model.atlas.entity.CaLargeModelReasoningTask;
import com.futong.gemini.model.atlas.entity.CaTaskInstance;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.product.aofei.constants.AofeiConstants;
import com.futong.gemini.plugin.product.aofei.convert.Converts;
import com.futong.gemini.plugin.product.aofei.enums.ResourceEnum;
import com.futong.gemini.plugin.product.aofei.enums.TokenEnum;
import com.futong.gemini.plugin.product.aofei.enums.TypeEnum;
import com.futong.gemini.plugin.product.aofei.http.HttpClientConfig;
import com.futong.gemini.plugin.product.aofei.request.*;
import com.futong.gemini.plugin.product.aofei.util.AofeiUtils;
import com.futong.gemini.plugin.product.aofei.util.URLUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TaskService {

    public static final TaskService bean = new TaskService();

    /**
     * 获取fine任务数据
     *
     * @param request
     * @param arguments
     */
    public void fetchFineTask(DescribeAofeiRequest request, JSONObject arguments) {
        String subCmpId = request.getIds() == null ? "" : request.getIds().get(0).getCmpId();
        List<CaLargeModelFineTuneTask> taskList = new ArrayList<>();
        List<CaTaskInstance> podList = new ArrayList<>();
        Integer fecthSize =request.getFecthSize();
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        // 推送任务数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String fineUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTaskUrl(cloudAccessBean.getScvmmRole()), null);
        JSONObject config = new JSONObject();
        config.put("pageNum", fecthSize);
        config.put("pageSize", count);
        JSONArray array = new JSONArray();
        request.getIds().forEach(user -> {
            config.put("userId", user.getUserId());
            JSONArray list = Converts.convertDataByUser(user, cloudAccessBean, config.toJSONString(), fineUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
                array.addAll(list);
        });
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                String taskId = tempObj.getString("id");

                try {
                    // 获取任务详情
                    String detailUrl = URLUtils.bean.makeUrl(
                            cloudAccessBean,
                            URLUtils.bean.getFineTaskDetailUrl(cloudAccessBean.getScvmmRole()),
                            new String[]{"?taskId=" + taskId}
                    );

                    JSONObject detailConfig = new JSONObject();
                    detailConfig.put("taskId", taskId);

                    JSONArray detailArray = Converts.convertData(
                            request.getIds(),
                            cloudAccessBean,
                            detailConfig.toJSONString(),
                            detailUrl,
                            TypeEnum.POST.getValue(),
                            TypeEnum.DATA.getValue()
                    );

                    if (ObjectUtil.isNotEmpty(detailArray)) {
                        JSONObject detailData = detailArray.getJSONObject(0);
                        // 合并基础信息和详情信息
                        tempObj.putAll(detailData);

                        // 获取任务实例列表
                        String podListUrl = URLUtils.bean.makeUrl(
                                cloudAccessBean,
                                URLUtils.bean.getFineTuneTaskPodListUrl(cloudAccessBean.getScvmmRole()),
                                null
                        );

                        JSONObject podListConfig = new JSONObject();
                        podListConfig.put("taskId", taskId);
                        podListConfig.put("pageNum", 1);
                        podListConfig.put("pageSize", 10);

                        JSONArray podListArray = Converts.convertData(
                                request.getIds(),
                                cloudAccessBean,
                                podListConfig.toJSONString(),
                                podListUrl,
                                TypeEnum.POST.getValue(),
                                TypeEnum.ROWS.getValue()
                        );

                        if (ObjectUtil.isNotEmpty(podListArray)) {
                            // 将实例信息转换为实体对象并添加到实例列表中
                            podListArray.forEach(pod -> {
                                JSONObject podObj = (JSONObject) pod;
                                CaTaskInstance taskPod = Converts.toFineTuneTaskPod(BaseClient.auths.get(), podObj, taskId, ResourceEnum.TASK_INSTANCE.getValue(), subCmpId);
                                if (ObjectUtil.isNotNull(taskPod)) {
                                    podList.add(taskPod);
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    log.error("获取任务详情失败, taskId={}", taskId, e);
                    log.error("获取任务详情或实例列表失败, taskId={}", taskId, e);
                }

                CaLargeModelFineTuneTask fine = Converts.toFineTask(BaseClient.auths.get(), tempObj, ResourceEnum.FINE.getValue(), subCmpId);
                if (ObjectUtil.isNotNull(fine)) {
                    taskList.add(fine);
                }
            });
        }
        // 推送任务数据
        log.info("推送调优任务数量：{}", taskList.size());
        log.info("推送调优任务实例数量：{}", podList.size());
        BaseUtils.sendAtlasMessage(taskList, arguments,subCmpId,CaLargeModelFineTuneTask.class);
        BaseUtils.sendAtlasMessage(podList, arguments,subCmpId, CaTaskInstance.class);
    }

    public void fetchFineTaskPerf(DescribeAofeiRequest request, JSONObject arguments) {
        long time = System.currentTimeMillis();
        String subCmpId = request.getIds() == null ? "" : request.getIds().get(0).getCmpId();
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        Integer fecthSize =request.getFecthSize();
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        // 推送任务数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String fineUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTaskUrl(cloudAccessBean.getScvmmRole()), null);
        JSONObject config = new JSONObject();
        config.put("pageNum", fecthSize);
        config.put("pageSize", count);
        JSONArray array = new JSONArray();
        request.getIds().forEach(user -> {
            config.put("userId", user.getUserId());
            JSONArray list = Converts.convertDataByUser(user, cloudAccessBean, config.toJSONString(), fineUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue());
            array.addAll(list);
        });
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                String taskId = tempObj.getString("id");

                try {
                    String podListUrl = URLUtils.bean.makeUrl(
                            cloudAccessBean,
                            URLUtils.bean.getFineTuneTaskPodListUrl(cloudAccessBean.getScvmmRole()),
                            null
                    );
                    JSONObject podListConfig = new JSONObject();
                    podListConfig.put("taskId", taskId);
                    podListConfig.put("pageNum", 1);
                    podListConfig.put("pageSize", 10);

                    JSONArray podListArray = Converts.convertData(
                            request.getIds(),
                            cloudAccessBean,
                            podListConfig.toJSONString(),
                            podListUrl,
                            TypeEnum.POST.getValue(),
                            TypeEnum.ROWS.getValue()
                    );

                    for(int i = 0 ; i < podListArray.size(); i++) {
                        JSONObject podObj = (JSONObject) podListArray.get(i);
                        System.out.println("flag---"+"llm-unicloudusykhrldddddddddo-worker-0".equals(podObj.getString("name")));
                        System.out.println("pdoname---"+podObj.getString("name"));
                        JSONObject perfConfig = new JSONObject();
                        perfConfig.put("startTime",System.currentTimeMillis()-5*60*1000l);
                        perfConfig.put("endTime", System.currentTimeMillis());
                        perfConfig.put("podNames", new String[]{podObj.getString("name")});

                        String perfUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonPerfUrl(cloudAccessBean.getScvmmRole()), null);
                        JSONArray list = Converts.convertData(request.getIds(), cloudAccessBean, perfConfig.toString(),perfUrl, TypeEnum.POST.getValue(), "");
                        perfList.addAll(Converts.toPerfDetail(cloudAccessBean,tempObj,ResourceEnum.FINE.getValue(),podObj.getString("name"),list,time,subCmpId));
                    }
                } catch (Exception e) {
                    log.error("获取任务详情失败, taskId={}", taskId, e);
                    log.error("获取任务详情或实例列表失败, taskId={}", taskId, e);
                }
            });
        }
        log.info("推送调优性能数量：{}", perfList.size());
        BaseUtils.sendAtlasPerfMessage(perfList, arguments, subCmpId, ResourcePerfDetail.class);
    }

    public static void main(String[] args) {
        String str = "/src/train.p";
        System.out.println(str.substring(1,  str.length()));
    }

    /**
     * 创建大模型调优任务
     *
     * @param arguments 请求参数。
     * @return BaseResponse
     */
    public static BaseResponse createFineTuneTask(JSONObject arguments) {

        try {
            log.info("创建大模型调优任务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            JSONObject Json = BaseClient.bodys.get();

            log.info("转换请求体，body={}", Json);

            CreateLlmFineTuneRequest request = Json.toJavaObject(CreateLlmFineTuneRequest.class);

            // 调用命令生成接口
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());

            // 构造命令生成请求
            GenerateCommandRequest commandRequest = new GenerateCommandRequest();
            commandRequest.setBootFile(request.getStartupFile().substring(1,  request.getStartupFile().length()));
            commandRequest.setTrainTaskType("PyTorch".equals(request.getType()) ? 0 :
                    "ScikitLearn".equals(request.getType()) ? 1 :
                            "TensorFlow".equals(request.getType()) ? 2 : 3);
            commandRequest.setTrainEnvParams(CollUtil.isNotEmpty(request.getVariable()) ? request.getVariable() : new ArrayList<>());
            commandRequest.setTrainHyperParams(CollUtil.isNotEmpty(request.getCommonHyperParameters()) ? request.getCommonHyperParameters() : new ArrayList<>());
            commandRequest.setNgpus(request.getResource().get(0).getGpuResource().getGpuNum());
            commandRequest.setNnodes(request.getResource().get(0).getNumbers());
            // 生成命令
            String command = AofeiUtils.generateCommand(cloudAccessBean, commandRequest, config);
            if(StringUtils.isNotEmpty(request.getOutputDir())) {
                request.setOutputDir("/" + request.getOutputDir());
            }else {
                request.setOutputDir("");
            }
            request.setImage("ampha-registry.h3c.com:8099/ampha/"+request.getImageVersionId()+":latest");
            log.info("生成命令:{}",command);
            request.setCommand(command);
            // 创建任务
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneCreateUrl(cloudAccessBean.getScvmmRole()), null);
            String message = AofeiUtils.createFineTuneTask(Json, cloudAccessBean, url, request, config);
            return message.contains("成功")?BaseResponse.SUCCESS.of(message):BaseResponse.ERROR.of(message);
        } catch (Exception e) {
            log.error("创建大模型调优任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建大模型调优任务失败");
        }
    }

    /**
     * 停止大模型调优任务
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse stopFineTuneTask(JSONObject arguments) {
        try {
            log.info("停止大模型调优任务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String taskId = BaseClient.bodys.get().getString("taskId");

            if (ObjectUtil.isEmpty(taskId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "任务ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneStopUrl(cloudAccessBean.getScvmmRole()), new String[]{"?taskId=" + taskId});

            String message = AofeiUtils.stopFineTuneTask(BaseClient.bodys.get(), cloudAccessBean, url, taskId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("停止大模型调优任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "停止大模型调优任务失败");
        }
    }

    /**
     * 删除大模型调优任务
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse deleteFineTuneTask(JSONObject arguments) {
        try {
            log.info("删除大模型调优任务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String taskId = BaseClient.bodys.get().getString("taskId");

            if (ObjectUtil.isEmpty(taskId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "任务ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneDeleteUrl(cloudAccessBean.getScvmmRole()), null);

            String message = AofeiUtils.deleteFineTuneTask(BaseClient.bodys.get(), cloudAccessBean, url, taskId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("删除大模型调优任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除大模型调优任务失败");
        }
    }

    public static BaseResponse deleteResonTask(JSONObject arguments) {
        try {
            log.info("删除大模型推理任务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String taskId = BaseClient.bodys.get().getString("taskId");

            if (ObjectUtil.isEmpty(taskId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "任务ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineReasonDeleteUrl(cloudAccessBean.getScvmmRole()), null);

            String message = AofeiUtils.deleteReasonTask(BaseClient.bodys.get(), cloudAccessBean, url, taskId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("删除大模型推理任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除大模型推理任务失败");
        }
    }

    /**
     * 启动
     * @param arguments
     * @return
     */
    public static BaseResponse startFineTuneTask(JSONObject arguments) {
        try {
            log.info("启动大模型调优任务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String taskId = BaseClient.bodys.get().getString("taskId");

            if (ObjectUtil.isEmpty(taskId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "任务ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneStartUrl(cloudAccessBean.getScvmmRole()), new String[]{"?taskId=" + taskId});

            String message = AofeiUtils.startFineTuneTask(BaseClient.bodys.get(), cloudAccessBean, url, taskId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("启动大模型调优任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动大模型调优任务失败");
        }
    }

    public static JSONObject toTaskJSON(JSONObject json) {
        JSONObject result = new JSONObject();
        JSONObject modelServer = json.getJSONObject("modelServer");
        result.put("modelServer", modelServer);
        JSONObject jsonObject = json.getJSONObject("modelServerVersions");
        JSONArray newVersions = new JSONArray();
        Boolean aBoolean = jsonObject.getBoolean("inferenceAcceleration");
        String modelId = jsonObject.getString("modelId");
        String modelVersionId = jsonObject.getString("modelVersionId");
        String openCreateUserId = jsonObject.getString("openCreateUserId");
        String volumePath = "/private/datahub/"+openCreateUserId+"/"+modelId+"/"+modelVersionId+"/";
        jsonObject.put("volumePath",volumePath);
        jsonObject.put("framework",aBoolean?13:12);
        jsonObject.put("customImageFlag",aBoolean?13:12);
        jsonObject.put("autoRecovery", 0);
        newVersions.add(jsonObject);
        result.put("modelServerVersions", newVersions);
        return result;
    }

    public void fetchEvaluateTask(DescribeAofeiRequest request, JSONObject arguments) {
//        List<CaDevEnv> envList = new ArrayList<>();
//        // 推送开发环境数据
//        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
//        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getEvaluateTaskUrl(cloudAccessBean.getScvmmRole()), null);
//        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null, devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
//        if (ObjectUtil.isNotEmpty(array)) {
//            array.forEach(temp -> {
//                JSONObject tempObj = (JSONObject) temp;
//                CaDevEnv property = Converts.toDevenv(BaseClient.auths.get(), tempObj, ResourceEnum.DEV_ENV.getValue());
//                if (ObjectUtil.isNotNull(property))
//                    envList.add(property);
//            });
//        }
//        // 推送镜像数据
//        BaseUtils.sendAtlasMessage(envList, arguments);
    }



    public void fetchReasonTask(DescribeAofeiRequest request, JSONObject arguments) {
        String subCmpId = request.getIds() == null ? "" : request.getIds().get(0).getCmpId();
        List<CaLargeModelReasoningTask> envList = new ArrayList<>();
        List<CaLargeModelReasoningCallNum> callNumList = new ArrayList<>();
        Integer fecthSize =request.getFecthSize();
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        // 推送推理任务数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskUrl(cloudAccessBean.getScvmmRole()), null);
        JSONObject config = new JSONObject();
        config.put("pageNum", fecthSize);
        config.put("pageSize", count);
        JSONObject queryCriteria = new JSONObject();
        config.put("queryCriteria", queryCriteria);
        JSONArray array = new JSONArray();
        request.getIds().forEach(user -> {
            queryCriteria.put("createUser", user.getUserId());
            queryCriteria.put("isLargeModel", 1);
            array.addAll(Converts.convertDataByUser(user, cloudAccessBean, config.toJSONString(), devEnvUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue()));
        });
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 获取任务详情
                String detailUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskDetailUrl(cloudAccessBean.getScvmmRole()), null);
                JSONObject detailConfig = new JSONObject();
                detailConfig.put("id", tempObj.getString("id"));
                JSONObject detailData = Converts.convertData(request.getIds(), cloudAccessBean, detailConfig.toJSONString(), detailUrl, TypeEnum.POST.getValue(), TypeEnum.DATA.getValue()).getJSONObject(0);
                if (ObjectUtil.isNotEmpty(detailData)) {
                    // 合并基础信息和详情信息
                    tempObj.putAll(detailData);
                    // 获取版本详情
                    JSONArray versions = detailData.getJSONArray("modelServerVersions");
                    if (ObjectUtil.isNotEmpty(versions)) {
                        JSONObject version = versions.getJSONObject(0);
                        // 获取版本详情
                        String versionDetailUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskVersionDetailUrl(cloudAccessBean.getScvmmRole()), null);
                        JSONObject versionConfig = new JSONObject();
                        versionConfig.put("id", version.getString("modelServerVersionId"));
                        JSONObject versionDetailData = Converts.convertData(request.getIds(), cloudAccessBean, versionConfig.toJSONString(), versionDetailUrl, TypeEnum.POST.getValue(), TypeEnum.DATA.getValue()).getJSONObject(0);
                        if (ObjectUtil.isNotEmpty(versionDetailData)) {
                            // 将版本详情数据合并到版本信息中
                            version.putAll(versionDetailData);
                        }
                    }
                }
                CaLargeModelReasoningTask property = Converts.toReasonTask(BaseClient.auths.get(), tempObj, ResourceEnum.REASON.getValue(), subCmpId);
                if (ObjectUtil.isNotNull(property)) {
                    envList.add(property);
                }

                // 获取调用统计数据
                String statisticsUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskCallStatisticsUrl(cloudAccessBean.getScvmmRole()), null);
                JSONObject statisticsConfig = new JSONObject();
                statisticsConfig.put("id", tempObj.getString("id"));
                JSONArray statisticsData = Converts.convertListData(request.getIds(), cloudAccessBean, statisticsConfig.toJSONString(), statisticsUrl, TypeEnum.POST.getValue(), TypeEnum.DATA.getValue());

                if (ObjectUtil.isNotEmpty(statisticsData)) {
                    statisticsData.forEach(stat -> {
                        JSONObject statObj = (JSONObject) stat;
                        CaLargeModelReasoningCallNum callNum = Converts.toReasonCallNum(cloudAccessBean, statObj, tempObj.getString("id"), subCmpId);
                        callNumList.add(callNum);
                    });
                }
            });
        }
        // 推送推理任务数据
        log.info("推送reason任务数量：{}", envList.size());
        log.info("推送reason任务信息：{}", JSONArray.toJSONString(envList));
        log.info("推送reason调用统计数量：{}", callNumList.size());
        BaseUtils.sendAtlasMessage(envList, arguments, subCmpId, CaLargeModelReasoningTask.class);
        BaseUtils.sendAtlasMessage(callNumList, arguments, subCmpId, CaLargeModelReasoningCallNum.class);
    }

    public void fetchReasonTaskPerf(DescribeAofeiRequest request, JSONObject arguments) {
        long time = System.currentTimeMillis();
        String subCmpId = request.getIds() == null ? "" : request.getIds().get(0).getCmpId();
        Integer fecthSize =request.getFecthSize();
        Integer count = AofeiConstants.DEV_EVN_FETCH_SIZE;
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        // 推送推理任务数据
        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskUrl(cloudAccessBean.getScvmmRole()), null);
        JSONObject config = new JSONObject();
        config.put("pageNum", fecthSize);
        config.put("pageSize", count);
        JSONObject queryCriteria = new JSONObject();
        config.put("queryCriteria", queryCriteria);
        JSONArray array = new JSONArray();
        request.getIds().forEach(user -> {
            queryCriteria.put("createUser", user.getUserId());
            queryCriteria.put("isLargeModel", 1);
            array.addAll(Converts.convertDataByUser(user, cloudAccessBean, config.toJSONString(), devEnvUrl, TypeEnum.POST.getValue(), TypeEnum.ROWS.getValue()));
        });
        if (ObjectUtil.isNotEmpty(array)) {
            array.forEach(temp -> {
                JSONObject tempObj = (JSONObject) temp;
                // 获取任务详情
                String detailUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getMoelServerPodUrl(cloudAccessBean.getScvmmRole()), null);
                JSONObject detailConfig = new JSONObject();
                detailConfig.put("modelServerId", tempObj.getString("id"));
                detailConfig.put("status", 1);

                JSONArray detailData = Converts.convertDataPerf(request.getIds(), cloudAccessBean, detailConfig.toJSONString(), detailUrl, TypeEnum.POST.getValue(), TypeEnum.DATA.getValue());
                for(int i=0;i<detailData.size();i++) {
                    JSONObject podNames = new JSONObject();
                    podNames.put("podNames", new String[]{detailData.getString(i)});
                    podNames.put("startTime",System.currentTimeMillis()-5*60*1000l);
                    podNames.put("endTime", System.currentTimeMillis());
                    String perfUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonPerfUrl(cloudAccessBean.getScvmmRole()), null);
                    JSONArray list = Converts.convertData(request.getIds(), cloudAccessBean, podNames.toString(),perfUrl, TypeEnum.POST.getValue(), "");
                    perfList.addAll(Converts.toPerfDetail(cloudAccessBean,tempObj,ResourceEnum.REASON.getValue(),detailData.getString(i),list,time,subCmpId));
                }
            });
        }
        log.info("推送推理性能数量：{}", perfList.size());
        BaseUtils.sendAtlasPerfMessage(perfList, arguments, subCmpId, ResourcePerfDetail.class);
    }

    public void fetchAITask(DescribeAofeiRequest request, JSONObject arguments) {
//        List<CaDevEnv> envList = new ArrayList<>();
//        // 推送开发环境数据
//        CloudAccessBean cloudAccessBean = BaseClient.auths.get();
//        String devEnvUrl = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskUrl(cloudAccessBean.getScvmmRole()), null);
//        JSONArray array = Converts.convertData(request.getIds(), cloudAccessBean, null, devEnvUrl, TypeEnum.GET.getValue(), TypeEnum.ROWS.getValue());
//        if (ObjectUtil.isNotEmpty(array)) {
//            array.forEach(temp -> {
//                JSONObject tempObj = (JSONObject) temp;
//                CaDevEnv property = Converts.toDevenv(BaseClient.auths.get(), tempObj, ResourceEnum.DEV_ENV.getValue());
//                if (ObjectUtil.isNotNull(property))
//                    envList.add(property);
//            });
//        }
//        // 推送镜像数据
//        BaseUtils.sendAtlasMessage(envList, arguments);
    }

    /**
     * 停止推理服务
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse stopReasonTask(JSONObject arguments) {
        try {
            log.info("启动推理服务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String versionId = BaseClient.bodys.get().getString("versionId");

            if (ObjectUtil.isEmpty(versionId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "版本ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskStopUrl(cloudAccessBean.getScvmmRole()), null);

            String message = AofeiUtils.stopReasonTask(BaseClient.bodys.get(), cloudAccessBean, url, versionId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("启动推理服务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动推理服务失败");
        }
    }

    /**
     * 启动推理服务
     * @param arguments
     * @return
     */
    public static BaseResponse startReasonTask(JSONObject arguments) {
        try {
            log.info("启动推理服务，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            String versionId = BaseClient.bodys.get().getString("versionId");

            if (ObjectUtil.isEmpty(versionId)) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "版本ID不能为空");
            }
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskStartUrl(cloudAccessBean.getScvmmRole()), null);

            String message = AofeiUtils.startReasonTask(BaseClient.bodys.get(), cloudAccessBean, url, versionId, config);
            return BaseResponse.SUCCESS.of(message);
        } catch (Exception e) {
            log.error("启动推理服务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "启动推理服务失败");
        }
    }

    /**
     * 创建推理服务
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse createReasonTask(JSONObject arguments) {
        try {
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            JSONObject Json = BaseClient.bodys.get();
            log.info("接收前端参数----{}",Json);
            JSONObject bodyJson = toTaskJSON(Json);
            CreateReasonRequest request = JSONObject.parseObject(bodyJson.toJSONString(), CreateReasonRequest.class);
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskCreateUrl(cloudAccessBean.getScvmmRole()), null);
            request.getModelServerVersions().forEach(temp -> {
                temp.setModelInfo(temp.getModelName() + "/" + temp.getModelVersionName());
                if (ObjectUtil.isEmpty(temp.getPredictMinReplicas())) {
                    temp.setPredictMinReplicas(temp.getPredictReplicas());
                }
                if (ObjectUtil.isEmpty(temp.getPredictMaxReplicas())) {
                    temp.setPredictMaxReplicas(temp.getPredictReplicas());
                }
                List<JSONObject> envList = new ArrayList<>();
                if (CollUtil.isNotEmpty(temp.getEnvsList())) {
                    temp.getEnvsList().forEach(env -> {
                        JSONObject newEnv = new JSONObject();
                        String name = env.get("name");
                        String value = env.get("value");
                        if (name != null) {
                            newEnv.put(name, value);
                            envList.add(newEnv);
                        }
                    });
                }
                temp.setEnvs(JSONObject.toJSONString(envList));
            });

            log.info("推理body---{}",JSONObject.toJSONString(request));
            String message =AofeiUtils.createReasonTask(BaseClient.bodys.get(), cloudAccessBean, url, request, config);
            log.info("创建推理服务返回={}", message);
            return message.contains("成功")?BaseResponse.SUCCESS.of(message):BaseResponse.ERROR.of(message);
        } catch (Exception e) {
            log.error("创建推理服务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建推理服务失败");
        }
    }

    /**
     * 获取大模型调优任务日志
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse getFineTuneTaskPodLog(JSONObject arguments) {
        try {
            log.info("获取大模型调优任务日志，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            JSONObject bodyJson = BaseClient.bodys.get();

            // 构造请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("id", bodyJson.getString("id"));
            // requestBody.put("taskId", bodyJson.getString("taskId"));
            requestBody.put("offset", bodyJson.getLongValue("offset"));
            requestBody.put("startReadLines", bodyJson.getLong("startReadLines"));
            requestBody.put("recerseRead", bodyJson.getBooleanValue("recerseRead"));

            // 调用接口获取日志
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getFineTuneTaskPodLogUrl(cloudAccessBean.getScvmmRole()), null);

            JSONObject result = AofeiUtils.getFineTuneTaskPodLog(arguments, cloudAccessBean, url, requestBody, config);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("获取大模型调优任务日志失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取大模型调优任务日志失败");
        }
    }

    /**
     * 获取推理服务日志
     *
     * @param arguments 请求参数
     * @return BaseResponse
     */
    public static BaseResponse getReasonTaskPodLog(JSONObject arguments) {
        try {
            log.info("获取推理服务日志，接收参数={}", arguments);
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            JSONObject bodyJson = BaseClient.bodys.get();

            // 构造请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("id", bodyJson.getString("modelServerVersionId"));

            // 调用接口获取日志
            HttpClientConfig config = new HttpClientConfig();
            config.addHeader(TokenEnum.AUTHORIZATION.getValue(), "Bearer " + Converts.getUserToken(cloudAccessBean).getData().getToken());
            String url = URLUtils.bean.makeUrl(cloudAccessBean, URLUtils.bean.getReasonTaskPodLogUrl(cloudAccessBean.getScvmmRole()), null);

            String result = AofeiUtils.getReasonTaskPodLog(arguments, cloudAccessBean, url, requestBody, config);
            return new BaseDataResponse<>(result);
        } catch (Exception e) {
            log.error("获取推理服务日志失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取推理服务日志失败");
        }
    }

    /**
     * 对话
     * @param arguments
     * @return
     */
    public static BaseResponse completion(JSONObject arguments) {
        JSONObject result = new JSONObject();
        try {
            log.info("对话接受参数={}", arguments.toString());
            CloudAccessBean cloudAccessBean = BaseClient.auths.get();
            CompletionRequest request = BaseClient.bodys.get().toJavaObject(CompletionRequest.class);
            String url = URLUtils.bean.makeUrl(BaseClient.auths.get(), URLUtils.bean.getCompletionUrl(cloudAccessBean.getScvmmRole()), null);
            result.put("url",url);
            result.put("token", "Bearer "+Converts.getUserToken(cloudAccessBean).getData().getToken());
            result.put("json",JSONObject.toJSONString(request));
        }  catch (Exception e) {
            log.error("对话失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "对话失败");
        }
        return new BaseDataResponse<>().withData(result);
    }

}
