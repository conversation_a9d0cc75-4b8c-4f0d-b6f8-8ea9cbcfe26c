<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gemini</artifactId>
        <groupId>com.futong.gemini</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>gemini-plugins</artifactId>
    <properties>
        <output.directory>/cloudJars</output.directory>
        <plugin.version>1</plugin.version>
        <plugin.name>${project.artifactId}-${project.version}</plugin.name>
    </properties>
    <modules>
        <module>01-plugin-sdk</module>
        <!--业务插件 -->
        <module>02-plugins-biz</module>
        <!--项目插件 -->
        <module>03-plugins-project</module>
        <!--产品插件 -->
        <module>04-plugins-product</module>
        <!--云插件 -->
        <module>05-plugins-cloud</module>
    </modules>
    <!--以下引包server服务已依赖，此处只作为plugins插件服务的编译依赖，所有scope均为provided-->
    <dependencies>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>2.4.3</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>com.battcn</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <!-- amqp-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <!-- redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--ES-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--minio-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>7.1.4</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--数据库连接-->
        <!--多数据源-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${baomidou-dynamic-datasource.version}</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>${baomidou-mybatis-plus.version}</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--mybatisplus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${baomidou-mybatis-plus.version}</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--数据库连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${alibaba-druid.version}</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <!--mysql连接驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.22</version>
<!--            <scope>provided</scope>-->
        </dependency>
        <!-- Apache CXF Spring Boot Starter for JAX-WS (SOAP) -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.5.5</version> <!-- 请检查并使用最新稳定版本 -->
        </dependency>

    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.futong.gemini</groupId>
                <artifactId>01-plugin-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <!-- 配置 maven-jar-plugin 以禁用生成原始的 JAR 文件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.4</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <!-- 可选：重新定义输出文件名，避免与原始jar冲突 -->
                                <finalName>${plugin.name}</finalName>
                                <!-- 指定输出目录 -->
                                <outputDirectory>${output.directory}</outputDirectory>
                                <minimizeJar>true</minimizeJar>
                                <createDependencyReducedPom>false</createDependencyReducedPom>
                                <artifactSet>
                                    <excludes>
                                        <!-- 排除掉gemini-server里已经存在的引用 -->
<!--                                        <exclude>com.futong.gemini:plugin-sdk</exclude>-->
<!--                                        <exclude>com.futong.gemini:gemini-model</exclude>-->
                                        <exclude>com.futong:public-common</exclude>
<!--                                        <exclude>com.futong:public-constant</exclude>-->
<!--                                        <exclude>com.futong.otc:otc-sdk</exclude>-->
<!--                                        <exclude>com.futong.otc:bxc-sdk</exclude>-->
<!--                                        <exclude>com.futong.otc:common</exclude>-->
<!--                                        <exclude>com.futong.otc:nxc-sdk</exclude>-->
<!--                                        <exclude>com.futong.otc:gjc-sdk</exclude>-->
<!--                                        <exclude>com.futong.otc:dth-sdk</exclude>-->
<!--                                        <exclude>com.futong.otc:atlas-sdk</exclude>-->
                                        <exclude>com.github.spullara.mustache.java:compiler</exclude>
                                        <exclude>org.apache.cxf:cxf-spring-boot-starter-jaxws</exclude>
                                        <exclude>org.apache.cxf:cxf-spring-boot-autoconfigure</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-transports-http</exclude>
                                        <exclude>org.apache.cxf:cxf-core</exclude>
                                        <exclude>org.glassfish.jaxb:jaxb-runtime</exclude>
                                        <exclude>org.glassfish.jaxb:txw2</exclude>
                                        <exclude>com.sun.istack:istack-commons-runtime</exclude>
                                        <exclude>com.sun.activation:jakarta.activation</exclude>
                                        <exclude>com.fasterxml.woodstox:woodstox-core</exclude>
                                        <exclude>org.codehaus.woodstox:stax2-api</exclude>
                                        <exclude>org.apache.ws.xmlschema:xmlschema-core</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-frontend-jaxws</exclude>
                                        <exclude>xml-resolver:xml-resolver</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-bindings-soap</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-wsdl</exclude>
                                        <exclude>wsdl4j:wsdl4j</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-databinding-jaxb</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-bindings-xml</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-frontend-simple</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-ws-addr</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-ws-policy</exclude>
                                        <exclude>org.apache.neethi:neethi</exclude>
                                        <exclude>org.apache.cxf:cxf-rt-features-metrics</exclude>
                                        <exclude>com.google.guava:guava</exclude>
                                        <exclude>com.google.guava:failureaccess</exclude>
                                        <exclude>com.google.guava:listenablefuture</exclude>
                                        <exclude>org.checkerframework:checker-qual</exclude>
                                        <exclude>com.google.errorprone:error_prone_annotations</exclude>
                                        <exclude>com.google.j2objc:j2objc-annotations</exclude>
                                        <exclude>com.squareup.okio:okio</exclude>
                                        <exclude>org.jetbrains.kotlin:kotlin-stdlib</exclude>
                                        <exclude>org.jetbrains:annotations</exclude>
                                        <exclude>org.jetbrains.kotlin:kotlin-stdlib-common</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-web</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter</exclude>
                                        <exclude>org.springframework.boot:spring-boot</exclude>
                                        <exclude>jakarta.annotation:jakarta.annotation-api</exclude>
                                        <exclude>org.yaml:snakeyaml</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-json</exclude>
                                        <exclude>com.fasterxml.jackson.datatype:jackson-datatype-jdk8</exclude>
                                        <exclude>com.fasterxml.jackson.datatype:jackson-datatype-jsr310</exclude>
                                        <exclude>com.fasterxml.jackson.module:jackson-module-parameter-names
                                        </exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-tomcat</exclude>
                                        <exclude>org.apache.tomcat.embed:tomcat-embed-core</exclude>
                                        <exclude>org.glassfish:jakarta.el</exclude>
                                        <exclude>org.apache.tomcat.embed:tomcat-embed-websocket</exclude>
                                        <exclude>org.springframework:spring-web</exclude>
                                        <exclude>org.springframework:spring-beans</exclude>
                                        <exclude>org.springframework:spring-webmvc</exclude>
                                        <exclude>org.springframework:spring-aop</exclude>
                                        <exclude>org.springframework:spring-context</exclude>
                                        <exclude>org.springframework:spring-expression</exclude>
                                        <exclude>com.battcn:swagger-spring-boot-starter</exclude>
                                        <exclude>com.battcn:swagger-spring-boot-autoconfigure</exclude>
                                        <exclude>io.springfox:springfox-swagger2</exclude>
                                        <exclude>io.swagger:swagger-annotations</exclude>
                                        <exclude>io.swagger:swagger-models</exclude>
                                        <exclude>io.springfox:springfox-spi</exclude>
                                        <exclude>io.springfox:springfox-core</exclude>
                                        <exclude>io.springfox:springfox-schema</exclude>
                                        <exclude>io.springfox:springfox-swagger-common</exclude>
                                        <exclude>io.springfox:springfox-spring-web</exclude>
                                        <exclude>org.reflections:reflections</exclude>
                                        <exclude>org.javassist:javassist</exclude>
                                        <exclude>com.fasterxml:classmate</exclude>
                                        <exclude>org.springframework.plugin:spring-plugin-core</exclude>
                                        <exclude>org.springframework.plugin:spring-plugin-metadata</exclude>
                                        <exclude>org.mapstruct:mapstruct</exclude>
                                        <exclude>io.springfox:springfox-bean-validators</exclude>
                                        <exclude>com.battcn:swagger-vue-ui</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-actuator</exclude>
                                        <exclude>org.springframework.boot:spring-boot-actuator-autoconfigure
                                        </exclude>
                                        <exclude>org.springframework.boot:spring-boot-actuator</exclude>
                                        <exclude>io.micrometer:micrometer-core</exclude>
                                        <exclude>org.hdrhistogram:HdrHistogram</exclude>
                                        <exclude>org.latencyutils:LatencyUtils</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-consul-discovery
                                        </exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-consul</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-consul-core</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-validation</exclude>
                                        <exclude>org.hibernate.validator:hibernate-validator</exclude>
                                        <exclude>jakarta.validation:jakarta.validation-api</exclude>
                                        <exclude>org.jboss.logging:jboss-logging</exclude>
                                        <exclude>com.ecwid.consul:consul-api</exclude>
                                        <exclude>com.google.code.gson:gson</exclude>
                                        <exclude>org.apache.httpcomponents:httpclient</exclude>
                                        <exclude>commons-codec:commons-codec</exclude>
                                        <exclude>org.apache.httpcomponents:httpcore</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-consul-discovery</exclude>
                                        <exclude>commons-configuration:commons-configuration</exclude>
                                        <exclude>commons-lang:commons-lang</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-netflix-hystrix</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-netflix-ribbon
                                        </exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-netflix-ribbon</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-netflix-archaius</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-netflix-archaius
                                        </exclude>
                                        <exclude>com.netflix.ribbon:ribbon</exclude>
                                        <exclude>com.netflix.ribbon:ribbon-transport</exclude>
                                        <exclude>io.reactivex:rxnetty-contexts</exclude>
                                        <exclude>io.reactivex:rxnetty-servo</exclude>
                                        <exclude>javax.inject:javax.inject</exclude>
                                        <exclude>io.reactivex:rxnetty</exclude>
                                        <exclude>com.netflix.ribbon:ribbon-core</exclude>
                                        <exclude>com.netflix.ribbon:ribbon-httpclient</exclude>
                                        <exclude>commons-collections:commons-collections</exclude>
                                        <exclude>com.sun.jersey:jersey-client</exclude>
                                        <exclude>com.sun.jersey:jersey-core</exclude>
                                        <exclude>javax.ws.rs:jsr311-api</exclude>
                                        <exclude>com.sun.jersey.contribs:jersey-apache-client4</exclude>
                                        <exclude>com.netflix.servo:servo-core</exclude>
                                        <exclude>com.netflix.netflix-commons:netflix-commons-util</exclude>
                                        <exclude>com.netflix.ribbon:ribbon-loadbalancer</exclude>
                                        <exclude>com.netflix.netflix-commons:netflix-statistics</exclude>
                                        <exclude>io.reactivex:rxjava</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-loadbalancer
                                        </exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-loadbalancer</exclude>
                                        <exclude>io.projectreactor.addons:reactor-extra</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-cache</exclude>
                                        <exclude>com.stoyanr:evictor</exclude>
                                        <exclude>joda-time:joda-time</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter-openfeign</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-starter</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-context</exclude>
                                        <exclude>org.springframework.security:spring-security-rsa</exclude>
                                        <exclude>org.bouncycastle:bcpkix-jdk15on</exclude>
                                        <exclude>org.bouncycastle:bcprov-jdk15on</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-openfeign-core</exclude>
                                        <exclude>io.github.openfeign.form:feign-form-spring</exclude>
                                        <exclude>io.github.openfeign.form:feign-form</exclude>
                                        <exclude>commons-fileupload:commons-fileupload</exclude>
                                        <exclude>commons-io:commons-io</exclude>
                                        <exclude>org.springframework.cloud:spring-cloud-commons</exclude>
                                        <exclude>org.springframework.security:spring-security-crypto</exclude>
                                        <exclude>io.github.openfeign:feign-core</exclude>
                                        <exclude>io.github.openfeign:feign-slf4j</exclude>
                                        <exclude>io.github.openfeign:feign-hystrix</exclude>
                                        <exclude>com.netflix.archaius:archaius-core</exclude>
                                        <exclude>com.netflix.hystrix:hystrix-core</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-amqp</exclude>
                                        <exclude>org.springframework:spring-messaging</exclude>
                                        <exclude>org.springframework.amqp:spring-rabbit</exclude>
                                        <exclude>com.rabbitmq:amqp-client</exclude>
                                        <exclude>org.springframework.amqp:spring-amqp</exclude>
                                        <exclude>org.springframework.retry:spring-retry</exclude>
                                        <exclude>org.springframework:spring-tx</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-data-redis</exclude>
                                        <exclude>org.springframework.data:spring-data-redis</exclude>
                                        <exclude>org.springframework.data:spring-data-keyvalue</exclude>
                                        <exclude>org.springframework:spring-oxm</exclude>
                                        <exclude>org.springframework:spring-context-support</exclude>
                                        <exclude>io.lettuce:lettuce-core</exclude>
                                        <exclude>io.netty:netty-common</exclude>
                                        <exclude>io.netty:netty-handler</exclude>
                                        <exclude>io.netty:netty-resolver</exclude>
                                        <exclude>io.netty:netty-buffer</exclude>
                                        <exclude>io.netty:netty-codec</exclude>
                                        <exclude>io.netty:netty-transport</exclude>
                                        <exclude>io.projectreactor:reactor-core</exclude>
                                        <exclude>org.reactivestreams:reactive-streams</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-data-elasticsearch
                                        </exclude>
                                        <exclude>org.springframework.data:spring-data-elasticsearch</exclude>
                                        <exclude>org.springframework.data:spring-data-commons</exclude>
                                        <exclude>org.elasticsearch.plugin:transport-netty4-client</exclude>
                                        <exclude>io.netty:netty-codec-http</exclude>
                                        <exclude>org.elasticsearch.client:elasticsearch-rest-high-level-client
                                        </exclude>
                                        <exclude>org.elasticsearch:elasticsearch</exclude>
                                        <exclude>org.elasticsearch:elasticsearch-core</exclude>
                                        <exclude>org.elasticsearch:elasticsearch-secure-sm</exclude>
                                        <exclude>org.elasticsearch:elasticsearch-x-content</exclude>
                                        <exclude>com.fasterxml.jackson.dataformat:jackson-dataformat-smile</exclude>
                                        <exclude>com.fasterxml.jackson.dataformat:jackson-dataformat-yaml</exclude>
                                        <exclude>com.fasterxml.jackson.dataformat:jackson-dataformat-cbor</exclude>
                                        <exclude>org.elasticsearch:elasticsearch-geo</exclude>
                                        <exclude>org.apache.lucene:lucene-core</exclude>
                                        <exclude>org.apache.lucene:lucene-analyzers-common</exclude>
                                        <exclude>org.apache.lucene:lucene-backward-codecs</exclude>
                                        <exclude>org.apache.lucene:lucene-grouping</exclude>
                                        <exclude>org.apache.lucene:lucene-highlighter</exclude>
                                        <exclude>org.apache.lucene:lucene-join</exclude>
                                        <exclude>org.apache.lucene:lucene-memory</exclude>
                                        <exclude>org.apache.lucene:lucene-misc</exclude>
                                        <exclude>org.apache.lucene:lucene-queries</exclude>
                                        <exclude>org.apache.lucene:lucene-queryparser</exclude>
                                        <exclude>org.apache.lucene:lucene-sandbox</exclude>
                                        <exclude>org.apache.lucene:lucene-spatial</exclude>
                                        <exclude>org.apache.lucene:lucene-spatial-extras</exclude>
                                        <exclude>org.apache.lucene:lucene-spatial3d</exclude>
                                        <exclude>org.apache.lucene:lucene-suggest</exclude>
                                        <exclude>org.elasticsearch:elasticsearch-cli</exclude>
                                        <exclude>net.sf.jopt-simple:jopt-simple</exclude>
                                        <exclude>com.carrotsearch:hppc</exclude>
                                        <exclude>com.tdunning:t-digest</exclude>
                                        <exclude>org.elasticsearch:jna</exclude>
                                        <exclude>org.elasticsearch.client:elasticsearch-rest-client</exclude>
                                        <exclude>org.apache.httpcomponents:httpasyncclient</exclude>
                                        <exclude>org.apache.httpcomponents:httpcore-nio</exclude>
                                        <exclude>org.elasticsearch.plugin:mapper-extras-client</exclude>
                                        <exclude>org.elasticsearch.plugin:parent-join-client</exclude>
                                        <exclude>org.elasticsearch.plugin:aggs-matrix-stats-client</exclude>
                                        <exclude>org.elasticsearch.plugin:rank-eval-client</exclude>
                                        <exclude>org.elasticsearch.plugin:lang-mustache-client</exclude>
                                        <exclude>com.github.spullara.mustache.javar</exclude>
                                        <exclude>io.minio:minio</exclude>
                                        <exclude>com.carrotsearch.thirdparty:simple-xml-safe</exclude>
                                        <exclude>com.squareup.okhttp3:okhttp</exclude>
                                        <exclude>com.fasterxml.jackson.core:jackson-annotations</exclude>
                                        <exclude>com.fasterxml.jackson.core:jackson-core</exclude>
                                        <exclude>com.fasterxml.jackson.core:jackson-databind</exclude>
                                        <exclude>com.github.spotbugs:spotbugs-annotations</exclude>
                                        <exclude>net.jcip:jcip-annotations</exclude>
                                        <exclude>com.google.code.findbugs:jsr305</exclude>
                                        <exclude>com.baomidou:dynamic-datasource-spring-boot-starter</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-jdbc</exclude>
                                        <exclude>com.zaxxer:HikariCP</exclude>
                                        <exclude>org.springframework:spring-jdbc</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-aop</exclude>
                                        <exclude>org.aspectj:aspectjweaver</exclude>
                                        <exclude>com.baomidou:mybatis-plus-annotation</exclude>
                                        <exclude>com.baomidou:mybatis-plus-boot-starter</exclude>
                                        <exclude>com.baomidou:mybatis-plus</exclude>
                                        <exclude>com.baomidou:mybatis-plus-extension</exclude>
                                        <exclude>com.baomidou:mybatis-plus-core</exclude>
                                        <exclude>com.github.jsqlparser:jsqlparser</exclude>
                                        <exclude>org.mybatis:mybatis</exclude>
                                        <exclude>org.mybatis:mybatis-spring</exclude>
                                        <exclude>org.springframework.boot:spring-boot-autoconfigure</exclude>
                                        <exclude>com.alibaba:druid-spring-boot-starter</exclude>
                                        <exclude>com.alibaba:druid</exclude>
                                        <exclude>org.slf4j:slf4j-api</exclude>
                                        <exclude>mysql:mysql-connector-java</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-logging</exclude>
                                        <exclude>ch.qos.logback:logback-classic</exclude>
                                        <exclude>ch.qos.logback:logback-core</exclude>
                                        <exclude>org.apache.logging.log4j:log4j-to-slf4j</exclude>
                                        <exclude>org.apache.logging.log4j:log4j-api</exclude>
                                        <exclude>org.slf4j:jul-to-slf4j</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-test</exclude>
                                        <exclude>org.springframework.boot:spring-boot-test</exclude>
                                        <exclude>org.springframework.boot:spring-boot-test-autoconfigure</exclude>
                                        <exclude>com.jayway.jsonpath:json-path</exclude>
                                        <exclude>net.minidev:json-smart</exclude>
                                        <exclude>net.minidev:accessors-smart</exclude>
                                        <exclude>org.ow2.asm:asm</exclude>
                                        <exclude>jakarta.xml.bind:jakarta.xml.bind-api</exclude>
                                        <exclude>jakarta.activation:jakarta.activation-api</exclude>
                                        <exclude>org.assertj:assertj-core</exclude>
                                        <exclude>org.hamcrest:hamcrest</exclude>
                                        <exclude>org.junit.jupiter:junit-jupiter</exclude>
                                        <exclude>org.junit.jupiter:junit-jupiter-api</exclude>
                                        <exclude>org.opentest4j:opentest4j</exclude>
                                        <exclude>org.junit.platform:junit-platform-commons</exclude>
                                        <exclude>org.junit.jupiter:junit-jupiter-params</exclude>
                                        <exclude>org.junit.jupiter:junit-jupiter-engine</exclude>
                                        <exclude>org.junit.vintage:junit-vintage-engine</exclude>
                                        <exclude>org.apiguardian:apiguardian-api</exclude>
                                        <exclude>org.junit.platform:junit-platform-engine</exclude>
                                        <exclude>junit:junit</exclude>
                                        <exclude>org.mockito:mockito-core</exclude>
                                        <exclude>net.bytebuddy:byte-buddy</exclude>
                                        <exclude>net.bytebuddy:byte-buddy-agent</exclude>
                                        <exclude>org.objenesis:objenesis</exclude>
                                        <exclude>org.mockito:mockito-junit-jupiter</exclude>
                                        <exclude>org.skyscreamer:jsonassert</exclude>
                                        <exclude>com.vaadin.external.google:android-json</exclude>
                                        <exclude>org.springframework:spring-core</exclude>
                                        <exclude>org.springframework:spring-jcl</exclude>
                                        <exclude>org.springframework:spring-test</exclude>
                                        <exclude>org.xmlunit:xmlunit-core</exclude>
                                        <exclude>org.projectlombok:lombok</exclude>
                                        <exclude>com.alibaba:fastjson</exclude>
                                        <exclude>cn.hutool:hutool-all</exclude>
                                        <exclude>org.apache.commons:commons-lang3</exclude>
                                        <exclude>com.futong:futong-cmp-core</exclude>
                                        <exclude>com.github.ulisesbocchio:jasypt-spring-boot-starter</exclude>
                                        <exclude>com.github.ulisesbocchio:jasypt-spring-boot</exclude>
                                        <exclude>org.jasypt:jasypt</exclude>
                                    </excludes>
                                </artifactSet>
                                <filters>
                                    <filter>
                                        <artifact>ch.qos.logback:*</artifact>
                                        <includes>
                                            <include>**</include>
                                        </includes>
                                    </filter>
                                    <filter>
                                        <artifact>*:*</artifact>
                                        <excludes>
                                            <exclude>META-INF/*.SF</exclude>
                                            <exclude>META-INF/*.DSA</exclude>
                                            <exclude>META-INF/*.RSA</exclude>
                                            <exclude>META-INF/NOTICE.txt</exclude>
                                            <exclude>META-INF/LICENSE.txt</exclude>
                                        </excludes>
                                    </filter>
                                </filters>
                                <transformers>
                                    <!-- 这里不包含ManifestResourceTransformer，因为我们没有主类 -->
                                    <!-- 但保持这个结构，你可以根据需要添加其他转换器 -->
                                </transformers>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
