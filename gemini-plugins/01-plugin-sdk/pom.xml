<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gemini-plugins</artifactId>
        <groupId>com.futong.gemini</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>01-plugin-sdk</artifactId>
    <name>01-plugin-sdk</name>
    <dependencies>
        <!--富通公共包-工具-->
        <dependency>
            <groupId>com.futong</groupId>
            <artifactId>public-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--富通公共包-常量-->
        <dependency>
            <groupId>com.futong</groupId>
            <artifactId>public-constant</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--富通公共包-数据底座-->
        <dependency>
            <groupId>com.futong.gemini</groupId>
            <artifactId>gemini-model</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--GuavaCacheUtils 使用，用于本地缓存-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>
        <!-- 调度采集器SDK -->
        <dependency>
            <groupId>com.futong.sniffgourd</groupId>
            <artifactId>sniffgourd-sdk</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>