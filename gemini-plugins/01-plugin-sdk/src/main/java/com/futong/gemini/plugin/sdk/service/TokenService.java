package com.futong.gemini.plugin.sdk.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.redis.FTRedisUtils;
import com.futong.common.utils.FTEncryptUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
public class TokenService {
    //token样式
    public static final String TOKEN = "token:{}";
    //缓存时效
    private static final Integer VALIDITY = 60 * 5;

    //将信息缓存
    public static BaseResponse token(JSONObject request) {
        //将认证信息缓存到 Redis
        String key = StrUtil.format(TOKEN, UUID.randomUUID().toString());
        //设置缓存信息
        FTRedisUtils.set(key, request.toJSONString(), VALIDITY);
        //使用 base64 对 Redis 的 key 进行加密生成 token
        return new BaseDataResponse<>(FTEncryptUtils.Base64Encode(key));
    }

    //检查token信息并返回缓存信息
    public static JSONObject checkToken(JSONObject request) {
        //校验是否传入 key
        if (StrUtil.isEmpty(request.getString("token"))) {
            log.info("缺少参数token信息!");
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "缺少参数token信息!");
        }
        //对 token 进行解密
        String key = FTEncryptUtils.Base64Decode(request.getString("token"));
        //校验 key 是否存在，以及是否过期
        if (!FTRedisUtils.hasKey(key)) {
            log.info("检查到token对应的key:{}不存在,或已过期!", key);
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "检查到token对应的key不存在,或已过期!!");
        }
        return FTRedisUtils.getJson(key);
    }

}
