package com.futong.gemini.plugin.sdk.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.feign.FTFeignUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.common.OperationFeign;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class AuthService {

    public static CloudAccessBean getAuthAccess(CloudAccessBean access) {
        if (access == null || StrUtil.isEmpty(access.getCmpId())) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.of("无Auth认证对象cmpId信息!"));
        }
        if (StrUtil.isNotEmpty(access.getUsername())) return access;
        //通过接口获取信息
        try {
            OperationFeign proxy = FTFeignUtils.target(OperationFeign.class, "futong-public-operation:50012");
            OperationFeign.AccessListRequest accessListRequest = new OperationFeign.AccessListRequest();
            accessListRequest.setCmpId(access.getCmpId());
            BaseDataResponse<List<CloudAccessBean>> accessList = proxy.getAllAccessList(accessListRequest);
            if (CollUtil.isEmpty(accessList.getData())) {
                throw new BaseException(BaseResponse.ERROR_BIZ_DATA_EMPTY, StrUtil.format("云账号{}无法获取账号信息", access.getCmpId()));
            }
            return accessList.getData().get(0);
        } catch (Exception e) {
            log.error(StrUtil.format("云账号{}无法获取账号信息", access.getCmpId()), e);
            throw new BaseException(BaseResponse.FAIL_PARAM, e, StrUtil.format("云账号{}无法获取账号信息", access.getCmpId()));
        }
    }

}
