package com.futong.gemini.plugin.sdk.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.futong.common.model.BaseResponse;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class GourdService {
    public static <T> BaseResponse toGourdResponse(BaseResponse response, List<T> data, Function<T, JobInfo> convert) {
        if (CollUtil.isEmpty(data)) return response;
        List<JobInfo> jobInfos = data.stream().map(convert).filter(ObjUtil::isNotNull).collect(Collectors.toList());
        if (response instanceof GourdJobResponse) {
            return ((GourdJobResponse) response).withAddJob(jobInfos).of("。拆分" + jobInfos.size() + "子任务");
        }
        return new GourdJobResponse(jobInfos, response.getMessage() + "。拆分" + jobInfos.size() + "子任务");
    }

    public static <T> BaseResponse toflatMapGourdResponse(BaseResponse response, List<T> data, Function<T, Stream<JobInfo>> convert) {
        if (CollUtil.isEmpty(data)) return response;
        List<JobInfo> jobInfos = data.stream().flatMap(convert).filter(ObjUtil::isNotNull).collect(Collectors.toList());
        if (response instanceof GourdJobResponse) {
            return ((GourdJobResponse) response).withAddJob(jobInfos).of("。拆分" + jobInfos.size() + "子任务");
        }
        return new GourdJobResponse(jobInfos, response.getMessage() + "。拆分" + jobInfos.size() + "子任务");
    }
}
