package com.futong.gemini.plugin.sdk.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.gemini.plugin.sdk.service.AuthService;
import lombok.Data;

@Data
public class BodyAuth extends JSONObject {
    //认证信息
    private CloudAccessBean access;

    private String proxyAddr;

    public BodyAuth(JSONObject json) {
        super(json);
        this.access = json.toJavaObject(CloudAccessBean.class);
        loadExt();
    }

    public BodyAuth(JSONObject json, CloudAccessBean access) {
        super(json);
        this.access = access;
        loadExt();
    }

    public void loadExt() {
        if (StrUtil.isNotEmpty(access.getJsonStr())) {
            JSONObject accessExt = JSON.parseObject(access.getJsonStr());
            proxyAddr = accessExt.getString("proxyAddr");
        }
    }

    public void loadProxyAuthAccess() {
        CloudAccessBean bean = AuthService.getAuthAccess(access);
        this.access = bean;
        loadExt();
    }
}
