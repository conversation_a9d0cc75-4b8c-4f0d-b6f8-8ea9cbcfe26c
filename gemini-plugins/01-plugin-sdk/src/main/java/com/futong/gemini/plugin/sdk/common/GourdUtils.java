package com.futong.gemini.plugin.sdk.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;

public class GourdUtils {
    public static BaseResponse addGourdLevel(String id, String parentId, String name) {
        GourdProxy proxy = SpringUtil.getBean(GourdProxy.class);
        JSONObject level = new JSONObject();
        level.put("id", id);
        if (StrUtil.isNotEmpty(parentId)) {
            level.put("parentId", parentId);
        }
        level.put("name", name);
        return proxy.createLevel(level);
    }
}
