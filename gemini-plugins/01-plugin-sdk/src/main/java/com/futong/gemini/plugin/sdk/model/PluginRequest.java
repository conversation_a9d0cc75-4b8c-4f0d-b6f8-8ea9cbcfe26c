package com.futong.gemini.plugin.sdk.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;

@Slf4j
@Data
public class PluginRequest extends JSONObject {

    //请求信息
    private String action;
    private PluginInfo plugin;
    private boolean async;
    private PluginRequestBody body;

    public PluginRequest(JSONObject json) {
        super(json);
        this.action = json.getString("action");
        this.plugin = json.getObject("plugin", PluginInfo.class);
        if (json.containsKey("async")) {
            this.async = json.getBoolean("async");
        }
        if (json.containsKey("body")) {
            this.body = new PluginRequestBody(json.getJSONObject("body"));
        }
    }

    public void setAction(String action) {
        put("action", action);
        this.action = action;
    }

    public <T extends PluginRequestBody> T builderBody(Class<T> clazz) {
        try {
            T t = clazz.getConstructor(JSONObject.class).newInstance(this.body);
            t.cache = body.cache;
            this.body = t;
            return t;
        } catch (Exception e) {
            log.error("构建{}body对象失败！", e, clazz.getSimpleName());
            throw new BaseException(BaseResponse.ERROR.of("构建" + clazz.getSimpleName() + "body对象失败！"), e);
        }
    }

    public JSONObject cloneJSONObject() {
        return JSON.parseObject(this.toJSONString());
    }
}
