package com.futong.gemini.plugin.sdk.template;

import cn.hutool.core.collection.CollUtil;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTAfter;
import com.futong.common.function.FTBefore;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.model.PluginRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public abstract class ActionRegister {
    Map<Object, FTAction<PluginRequest>> actionRegister = new HashMap<>();
    Map<Object, List<FTBefore<PluginRequest>>> beforeRegister = new HashMap<>();
    Map<Object, List<FTAfter<PluginRequest, BaseResponse>>> afterRegister = new HashMap<>();

    public abstract void load();

    public <Q, R, C> void register(Object key, FTExecute<Q, R, C> execute, Function<FTExecute<Q, R, C>, FTAction<PluginRequest>> function) {
        actionRegister.put(key, function.apply(execute));
    }

    public void register(Object key, FTAction<PluginRequest> ftAction) {

        actionRegister.put(key, ftAction);
    }

    public void registerBefore(FTBefore<PluginRequest> ftBefore, Object... keys) {
        for (Object key : keys) {
            if (!beforeRegister.containsKey(key)) {
                beforeRegister.put(key, new ArrayList<>());
            }
            beforeRegister.get(key).add(ftBefore);
        }
    }

    public void registerBefore(Object key, FTBefore<PluginRequest>... ftBefore) {
        if (!beforeRegister.containsKey(key)) {
            beforeRegister.put(key, new ArrayList<>());
        }
        CollUtil.addAll(beforeRegister.get(key), ftBefore);
    }

    public void registerAfter(Object key, FTAfter<PluginRequest, BaseResponse>... ftAfter) {
        if (!afterRegister.containsKey(key)) {
            afterRegister.put(key, new ArrayList<>());
        }
        CollUtil.addAll(afterRegister.get(key), ftAfter);
    }

    public FTAction<PluginRequest> getAction(Object key) {
        return actionRegister.get(key);
    }

    public List<FTBefore<PluginRequest>> getBefore(Object key) {
        return beforeRegister.get(key);
    }

    public List<FTAfter<PluginRequest, BaseResponse>> getAfter(Object key) {
        return afterRegister.get(key);
    }

    public boolean isNotExists(Object key) {
        return !isExists(key);
    }

    public boolean isExists(Object key) {
        return actionRegister.containsKey(key);
    }

}
