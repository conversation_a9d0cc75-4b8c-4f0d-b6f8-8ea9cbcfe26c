package com.futong.gemini.plugin.sdk.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTAfter;
import com.futong.common.function.FTBefore;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.model.PluginRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class PluginTemplateProcessRegister extends PluginTemplateProcess<PluginRequest> {
    public ActionRegister register;

    public abstract ActionRegister getRegister();

    @Override
    public void init(String key) {
        super.init(key);
        register = getRegister();
        register.load();
    }

    @Override
    public PluginRequest toRequest(JSONObject arguments) {
        if (!arguments.containsKey("action")) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.of("无action请求信息!"));
        }
        return new PluginRequest(arguments);
    }

    @Override
    public boolean onBeforeProcess(PluginRequest request, JSONObject context) {
        log.info("插件服务[" + key + "]收到请求信息:" + request.toJSONString());
        List<FTBefore<PluginRequest>> ftBefore = register.getBefore(request.getAction());
        if (null == ftBefore) return true;
        for (FTBefore<PluginRequest> before : ftBefore) {
            if (!before.before(request)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public BaseResponse process(PluginRequest request, JSONObject context) {
        if (register.isNotExists(request.getAction())) {
            return BaseResponse.FAIL_PARAM_EMPTY.of("未找Action:" + request.getAction() + "对应得处理方法");
        }
        FTAction<PluginRequest> ftAction = register.getAction(request.getAction());
        return ftAction.doAction(request);
    }

    @Override
    public void onAfterProcess(PluginRequest request, JSONObject context, BaseResponse response) {
        log.info("插件服务[" + key + "]处理结束响应结果信息:" + JSON.toJSONString(response));
        List<FTAfter<PluginRequest, BaseResponse>> ftAfters = register.getAfter(request.getAction());
        if (null == ftAfters) return;
        for (FTAfter<PluginRequest, BaseResponse> after : ftAfters) {
            after.after(request, response);
        }
    }


}
