package com.futong.gemini.plugin.sdk.template;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;

public interface ProcessInterface<T> {
    default T toRequest(JSONObject arguments) {
        return (T) arguments;
    }
    boolean onBeforeProcess(T request, JSONObject context);

    BaseResponse process(T request, JSONObject context);

    void onAfterProcess(T request, JSONObject context, BaseResponse response);
}
