package com.futong.gemini.plugin.sdk.common;

import com.futong.bean.CloudAccessBean;
import com.futong.common.model.BaseDataResponse;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface OperationFeign {
    @Data
    class AccessListRequest {
        private String cmpId;
    }
    @PostMapping("/access/getAllAccessList")
    BaseDataResponse<List<CloudAccessBean>> getAllAccessList(@RequestBody AccessListRequest request);
}
