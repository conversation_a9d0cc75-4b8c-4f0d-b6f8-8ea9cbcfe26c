package com.futong.gemini.plugin.sdk.service;

import org.springframework.core.io.InputStreamSource;

public class FileService {
    private static ThreadLocal<InputStreamSource[]> files = new ThreadLocal<>();

    public static InputStreamSource[] get() {
        return files.get();
    }

    public static void set(InputStreamSource[] file) {
        files.set(file);
    }

    public static void remove() {
        files.remove();
    }

}
