package com.futong.gemini.plugin.sdk.template;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.sdk.PluginInterface;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class PluginTemplateProcess<T> implements PluginInterface<JSONObject>, ProcessInterface<T> {
    public String key;

    //插件加载初始方法:key=realm:version
    @Override
    public void init(String key) {
        log.info("插件[" + key + "]已完成加载!");//子类可重写
        log.info("插件[" + key + "]初始化......");//子类可重写
        this.key = key;
    }

    @Override
    public void destroy() {
        log.info("插件[" + key + "]销毁中......");//子类可重写
    }

    @Override
    public final BaseResponse execute(JSONObject arguments) {
        //定义请求信息上下文
        JSONObject context = new JSONObject();
        T request = toRequest(arguments);
        //执行前置请求预处理
        try {
            boolean bool = onBeforeProcess(request, context);
            if (!bool) return BaseResponse.FAIL_OP.of("前置请求预处理不通过！");
        } catch (BaseException e) {
            log.error("前置请求预处理失败!", e);
            return e.response;
        }
        //执行请求处理
        BaseResponse response = process(request, context);
        //执行请求后置处理
        onAfterProcess(request, context, response);
        return response;
    }

}
