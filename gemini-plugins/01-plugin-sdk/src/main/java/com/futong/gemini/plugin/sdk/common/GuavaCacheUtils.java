package com.futong.gemini.plugin.sdk.common;

import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
public class GuavaCacheUtils {
    public enum Mode {
        //自动续期,固定时长
        AUTO, FIXED
    }

    //这种策略适用于需要回收那些可能很长时间都不会被再次使用的条目
    //当有get动作时则会自动续期1小时
    public static Cache<String, Object> autoHourCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterAccess(1, TimeUnit.HOURS)//方法设置的缓存条目在给定时间内没有被读取或写入，则会被回收
            .build();
    //这种策略适用于固定时间有效期,除非重新写入
    public static Cache<String, Object> fixedHourCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.HOURS)//方法设置的缓存条目在给定时间内没有被写访问（创建或覆盖）,则会被回收
            .build();

    public static Cache<String, Object> choose(GuavaCacheUtils.Mode mode) {
        switch (mode) {
            case AUTO:
                return autoHourCache;
            case FIXED:
                return fixedHourCache;
            default:
                throw new BaseException(BaseResponse.ERROR_SYS, "不支持此模式的缓存信息!");
        }
    }

    public static String get(GuavaCacheUtils.Mode mode, String key, Callable<String> callable) {
        Object s = getObj(mode, key, callable);
        return null == s ? null : s.toString();
    }

    public static Object getObj(GuavaCacheUtils.Mode mode, String key, Callable<String> callable) {
        Cache<String, Object> cache = choose(mode);
        try {
            return cache.get(key, callable);
        } catch (Exception e) {
            log.error("获取缓存信息失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "获取缓存信息失败!");
        }
    }

    public static String get(GuavaCacheUtils.Mode mode, String key) {
        Object s = getObj(mode, key);
        return null == s ? null : s.toString();
    }

    public static Object getObj(GuavaCacheUtils.Mode mode, String key) {
        Cache<String, Object> cache = choose(mode);
        try {
            return cache.getIfPresent(key);
        } catch (Exception e) {
            log.error("获取缓存信息失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "获取缓存信息失败!");
        }
    }

    public static void put(GuavaCacheUtils.Mode mode, String key, Object value) {
        Cache<String, Object> cache = choose(mode);
        try {
            cache.put(key, value);
        } catch (Exception e) {
            log.error("获取缓存信息失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "设置缓存信息失败!");
        }
    }

    public static String del(GuavaCacheUtils.Mode mode, String key) {
        Object s = delObj(mode, key);
        return null == s ? null : s.toString();
    }

    public static Object delObj(GuavaCacheUtils.Mode mode, String key) {
        Cache<String, Object> cache = choose(mode);
        try {
            return cache.asMap().remove(key);
        } catch (Exception e) {
            log.error("移除缓存信息失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "移除缓存信息失败!");
        }
    }

    public static void main(String[] args) throws ExecutionException, InterruptedException {
//        Cache<Object, Object> build = CacheBuilder.newBuilder().expireAfterAccess(5, TimeUnit.SECONDS).build();
//
//        String value = (String) build.get("aaaa", (Callable<String>) () -> "test");
//        System.out.println(value);
//        Thread.sleep(1000);
//        value = (String) build.get("aaaa", (Callable<String>) () -> "test1");
//        System.out.println(value);
//        Thread.sleep(4000);
//        value = (String) build.get("aaaa", (Callable<String>) () -> "test2");
//        System.out.println(value);
//        Thread.sleep(6000);
//        value = (String) build.get("aaaa", (Callable<String>) () -> "test3");
//        System.out.println(value);
        put(Mode.AUTO, "cas-cmpid-1234566", "token-7823213113");
        String v = get(Mode.AUTO, "cas-cmpid-1234566");
        System.out.println(v);
        //获取一个不存在的key信息
        Callable<String> fetchToken = () -> {
            System.out.println("尝试获取新的token");
            return "new-token-456789";
        };
        String v1 = get(Mode.AUTO, "openstack-cmpid-1234", fetchToken);
        System.out.println(v1);
        //再次调用因为再有效期内的缓存信息还再，则不会触发fetchToken
        String v2 = get(Mode.AUTO, "openstack-cmpid-1234", fetchToken);
        System.out.println(v2);
    }
}
