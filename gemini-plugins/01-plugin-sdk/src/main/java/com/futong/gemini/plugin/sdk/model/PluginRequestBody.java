package com.futong.gemini.plugin.sdk.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.sniffgourd.sdk.model.GourdInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * body类，提供一些公共参数获取，及参数获取方法。简化参数获取方式及转换过程
 * 参数有两种：
 * 元对象：就是参数对应的原始对象
 * body封装对象：基于元参数
 */
@Slf4j
@Data
@NoArgsConstructor
public class PluginRequestBody extends JSONObject {
    public Map<String, Object> cache = new HashMap<>();

    public PluginRequestBody(JSONObject json) {
        super(json);
    }

    //通用参数认证对象，
    public CloudAccessBean getAuth() {
        return get("auth", CloudAccessBean.class);
    }
    public BodyAuth getBodyAuth() {
        return getBody("auth", BodyAuth.class);
    }

    public GourdInfo getGourdInfo() {
        return get("gourd", GourdInfo.class);
    }

    public <T> T getCI(Class<T> clazz) {
        return get("ci", clazz);
    }

    public <T extends JSONObject> T getBodyCI(Class<T> clazz) {
        return getBody("ci", clazz);
    }

    public <T> List<T> getCIS(Class<T> clazz) {
        return getList("cis", clazz);
    }

    public <T extends JSONObject> List<T> getBodyCIS(Class<T> clazz) {
        return getBodyList("cis", clazz);
    }

    public <T> T get(String key, Class<T> clazz) {
        if (cache.containsKey(key)) return (T) cache.get(key);
        if (!this.containsKey(key)) return null;
        T obj = this.getJSONObject(key).toJavaObject(clazz);
        cache.put(key, obj);
        return obj;
    }

    public <T> List<T> getList(String key, Class<T> clazz) {
        if (cache.containsKey(key)) return (List<T>) cache.get(key);
        if (!this.containsKey(key)) return null;
        JSONArray jsonArray = this.getJSONArray(key);
        List<T> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getObject(i, clazz));
        }
        cache.put(key, list);
        return list;
    }

    public <T extends JSONObject> T getBody(String key, Class<T> clazz) {
        String cacheKey = "body_" + key;
        if (cache.containsKey(cacheKey)) return (T) cache.get(cacheKey);
        if (!this.containsKey(key)) return null;
        try {
            Constructor<T> constructor;
            Object meta = cache.get(key);//原始信息
            T obj;
            if (meta != null && (constructor = clazz.getConstructor(JSONObject.class, meta.getClass())) != null) {
                obj = constructor.newInstance(this.getJSONObject(key), meta);
            } else {
                obj = clazz.getConstructor(JSONObject.class).newInstance(this.getJSONObject(key));
            }
            cache.put(cacheKey, obj);
            return obj;
        } catch (Exception e) {
            log.error("无法构造{}对象,该类没有实现了JSONObject参数的构造函数", e, clazz.getSimpleName());
            throw new BaseException(BaseResponse.ERROR, "无法构造" + clazz.getSimpleName() + "对象,该类没有实现了JSONObject参数的构造函数");
        }
    }

    public <T extends JSONObject> List<T> getBodyList(String key, Class<T> clazz) {
        String cacheKey = "body_" + key;
        if (cache.containsKey(key)) return (List<T>) cache.get(key);
        if (!this.containsKey(key)) return null;
        JSONArray jsonArray = this.getJSONArray(key);
        List<T> list = new ArrayList<>();
        try {
            Constructor<T> constructor = clazz.getConstructor(JSONObject.class);
            for (int i = 0; i < jsonArray.size(); i++) {
                list.add(constructor.newInstance(jsonArray.getJSONObject(i)));
            }
            cache.put(cacheKey, list);
            return list;
        } catch (Exception e) {
            log.error("无法构造{}对象,该类没有实现了JSONObject参数的构造函数", e, clazz.getSimpleName());
            throw new BaseException(BaseResponse.ERROR, "无法构造" + clazz.getSimpleName() + "对象,该类没有实现了JSONObject参数的构造函数");
        }
    }
}
