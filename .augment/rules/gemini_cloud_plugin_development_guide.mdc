# Gemini 云平台插件开发指南

基于华为云插件 `c01-cloud-harmony` 的实际实现标准制定。

## 1. 项目概述

### 1.1 背景
Gemini 是一个基于 Java 的云平台插件系统，用于统一管理不同类型的云平台资源：
- **公有云**：阿里云、腾讯云、华为云等 IaaS 资源
- **私有云**：OpenStack、百度云等私有化部署
- **虚拟化平台**：VMware vSphere 等虚拟化资源
- **超融合平台**：Nutanix 等超融合基础设施
- **容器云平台**：Kubernetes、谐云等容器编排平台

插件负责采集资源数据，转换为统一的 CI 模型，通过 RabbitMQ 发送到消息队列，供后续服务处理。

### 1.2 目标
根据不同插件类型实现相应的资源管理：
- **IaaS 插件**：云主机、实例规格、镜像、磁盘、密钥对、安全组等
- **容器云插件**：集群、命名空间、工作负载、存储、网络服务等
- **虚拟化插件**：虚拟机、数据存储、网络、资源池等

核心要求：
- 遵循 Gemini 插件框架，参考标准实现（如 `c01-cloud-harmony`）
- 数据转换为 CI 模型并通过 MQ 发送
- 支持新增云平台和 API 版本升级
- 符合 [编码规范](mdc:docs/code-standards)

### 1.3 插件分类和命名规范
- **公有云**：`gXX-cloud-{platform}`（如 `g01-cloud-ali`、`g03-cloud-huawei`）
- **私有云**：`sXX-cloud-{platform}`（如 `s07-cloud-baidu`）
- **虚拟化平台**：`vXX-cloud-{platform}`（如 `v01-cloud-vmware`）
- **超融合平台**：`hXX-cloud-{platform}`（如 `h01-cloud-nutanix`）
- **容器云平台**：`cXX-cloud-{platform}`（如 `c01-cloud-harmony`）

## 2. 项目结构

### 2.1 核心模块
- **gemini-model**: CI 模型定义，参考 [gemini-model/pom.xml](mdc:gemini-model/pom.xml)
- **gemini-sdk**: 基础 SDK，参考 [gemini-sdk/pom.xml](mdc:gemini-sdk/pom.xml)
- **gemini-server**: 服务端应用，参考 [gemini-server/pom.xml](mdc:gemini-server/pom.xml)
- **gemini-plugins**: 云平台插件模块

### 2.2 插件模块结构
```
gemini-plugins/
├── 01-plugin-sdk/               # 插件 SDK 框架
├── 05-plugins-cloud/            # 云平台插件
│   ├── 01-cloud-sdk/            # 云插件基础 SDK
│   ├── g01-cloud-ali/           # 阿里云插件（IaaS 参考）
│   ├── g03-cloud-huawei/        # 华为云插件（IaaS）
│   ├── c01-cloud-harmony/       # 谐云插件（容器云标准实现）
│   ├── s07-cloud-baidu/         # 百度云插件（私有云）
│   └── ...其他插件
```

### 2.3 标准插件包结构（基于华为云插件）
```
05-plugins-cloud/{prefix}-cloud-{platform}/
├── src/main/java/com/futong/gemini/plugin/cloud/{platform}/
│   ├── client/                  # 客户端类
│   │   ├── {Platform}Client.java        # 主客户端类
│   │   ├── ClientUtils.java             # 客户端工具类
│   │   ├── ConnectionConfig.java        # 连接配置类
│   │   └── AuthConfig.java              # 认证配置类
│   ├── service/                 # 业务服务层
│   │   ├── BaseService.java             # 基础服务类
│   │   ├── PlatformAccountService.java  # 平台账号服务
│   │   ├── ClusterService.java          # 集群服务（容器云）
│   │   ├── ComputeInstanceService.java  # 云主机服务（IaaS）
│   │   └── ...其他资源服务
│   ├── sampler/                 # 数据采集器
│   │   ├── FetchService.java            # 资源获取服务
│   │   ├── Convert.java                 # 数据转换服务
│   │   └── RefreshService.java          # 刷新服务
│   ├── {Platform}Register.java          # 插件注册类
│   └── {Platform}PluginTemplate.java    # 插件模板类
├── src/main/resources/
│   ├── account/                 # 账号配置文件
│   │   ├── add_dispatch.json            # 调度配置
│   │   ├── add_form_zh-CN.json          # 中文表单
│   │   └── add_form_en-US.json          # 英文表单
│   └── META-INF/services/       # 服务注册文件
├── docs/requirements.md         # 需求文档
└── pom.xml                      # Maven 配置文件
```

## 3. 开发要求

### 3.1 资源类型（按插件分类）

#### 3.1.1 IaaS 插件资源（如阿里云、华为云）
- **云主机（ECS/BCC）**: 查询、创建、删除、电源管理（启动/停止/重启）
- **实例规格**: 查询实例类型（CPU、内存配置）
- **镜像**: 查询、创建、删除
- **磁盘**: 查询、创建、挂载、卸载
- **密钥对**: 查询、创建、删除
- **安全组**: 查询、创建、更新、删除规则
- **VPC 网络**: 查询、创建、删除

#### 3.1.2 容器云插件资源（如谐云）
- **租户（Tenant）**: 组织管理
- **项目（Project）**: 项目管理
- **集群（Cluster）**: Kubernetes 集群管理
- **命名空间（Namespace）**: 资源隔离
- **工作负载**: Deployments、StatefulSets、Pods
- **存储**: PV、PVC、StorageClass
- **网络服务**: Service、Ingress

### 3.2 核心类实现（基于华为云插件标准）

#### 3.2.1 客户端类实现
```java
// 参考 HarmonyClient 实现
public class {Platform}Client {
    private ConnectionConfig config;
    private CloseableHttpClient httpClient;

    public {Platform}Client(ConnectionConfig config) {
        this.config = config;
        this.httpClient = createHttpClient();
    }

    // 认证方法
    public synchronized String doToken() throws Exception {
        // 实现具体的认证逻辑
        // 支持用户名密码、AccessKey/SecretKey 等方式
    }

    // HTTP 请求方法
    public String doGet(String apiPath, Map<String, ?> params) { }
    public String doPost(String apiPath, JSONObject requestBody) { }
    public String doPut(String apiPath, JSONObject requestBody) { }
    public String doDelete(String apiPath) { }

    // 数据解析方法
    public JSONObject doGetDataJSON(String apiPath, Map<String, ?> params) { }
    public JSONArray doGetDataJSONArray(String apiPath, Map<String, ?> params) { }
}
```

#### 3.2.2 注册类实现
```java
// 参考 HarmonyRegister 实现
public class {Platform}Register extends BaseCloudRegister {
    @Override
    public void load() {
        onAfterLoadPlatform();    // 加载云平台操作
        onAfterLoadFetch();       // 加载同步调度信息
        // 根据插件类型加载对应资源操作
        onAfterLoadCompute();     // IaaS: 云主机操作
        onAfterLoadCluster();     // 容器云: 集群操作
    }

    public void onAfterLoadPlatform() {
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatformAccountService::authAccount);
    }

    public void onAfterLoadFetch() {
        // 注册资源同步任务
        register(ActionType.FETCH_CAAS_TENANT, FetchService::fetchTenant);
        register(ActionType.FETCH_CAAS_CLUSTER, FetchService::fetchCluster);
        // 更多同步任务...
    }
}
```

#### 3.2.3 模板类实现
```java
// 参考 HarmonyPluginTemplate 实现
@Slf4j
public class {Platform}PluginTemplate extends BaseCloudPluginTemplate {
    @Override
    public void init(String key) {
        super.init(key);
        loadAccountForm();
        loadAccountDispatch();
        // 指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.{platform}", "/cloud/{platform}");
    }

    @Override
    public BaseCloudRegister getRegister() {
        return new {Platform}Register();
    }
}
```

#### 3.2.4 服务层实现
**目录**: `com.futong.gemini.plugin.cloud.{platform}.service`

**通用服务类**:
- `BaseService`: 基础服务，处理账号表单、调度任务等
- `PlatformAccountService`: 平台账号认证服务

**IaaS 插件服务类**:
- `ComputeInstanceService`: 云主机操作
- `DiskService`: 磁盘操作
- `ImageService`: 镜像操作
- `SecurityGroupService`: 安全组操作
- `VpcService`: VPC 操作

**容器云插件服务类**:
- `ClusterService`: 集群管理
- `NodeService`: 节点管理
- `NamespaceService`: 命名空间管理
- `WorkDeploymentsService`: 工作负载管理

**示例实现**:
```java
// 参考 HarmonyRegister 中的服务调用方式
public class ClusterService {
    public static BaseResponse queryClusterInfo(BaseCloudRequest request) {
        {Platform}Client client = ClientUtils.client({Platform}Client.class, request.getBody());
        String apiPath = StrUtil.format("/api/clusters/{}", request.getBody().getModel().getString("cluster"));
        JSONObject result = client.doGetDataJSON(apiPath, request.getBody().getCloud());
        return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
    }
}
```

#### 3.2.5 数据采集和转换
**目录**: `com.futong.gemini.plugin.cloud.{platform}.sampler`

**核心类**:
- `FetchService`: 定时资源同步
- `Convert`: 数据转换为 CI 模型
- `RefreshService`: 异步回调处理

**示例实现**:
```java
// 参考 HarmonyFetchService 实现
public class FetchService {
    public static BaseResponse fetchTenant(BaseCloudRequest request) {
        {Platform}Client client = ClientUtils.client({Platform}Client.class, request.getBody());
        try {
            JSONArray resultData = client.doGetDataJSONArray("/api/tenants", null);
            Map<Class, List> data = Convert.convertTenant(request, resultData);
            return BaseCloudService.fetchSend(request, data);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取租户失败!"), e);
        }
    }
}

// 参考 HarmonyConvert 实现
public class Convert {
    public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONArray resultData) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> tenants = new ArrayList<>();

        if (CollUtil.isNotEmpty(resultData)) {
            for (int i = 0; i < resultData.size(); i++) {
                JSONObject info = resultData.getJSONObject(i);
                TmdbTenant tenant = new TmdbTenant();
                tenant.setCloud_type(request.getPlugin().getRealm()); // 重要：设置云类型
                tenant.setOpen_id(info.getString("id"));
                tenant.setFull_name(info.getString("name"));
                tenant.setAccount_id(accessBean.getCmpId());
                // 更多字段设置...
                tenants.add(tenant);
            }
        }

        Map<Class, List> result = new HashMap<>();
        result.put(TmdbTenant.class, tenants);
        return result;
    }
}
```

### 3.3 包命名规范
```java
com.futong.gemini.plugin.cloud.{platform}.client     // 客户端类
com.futong.gemini.plugin.cloud.{platform}.service    // 业务服务层
com.futong.gemini.plugin.cloud.{platform}.sampler    // 数据采集器
com.futong.gemini.plugin.cloud.{platform}            // 注册和模板类
```

### 3.4 插件命名规则
- **公有云**：`gXX-cloud-{platform}`（如 `g01-cloud-ali`、`g03-cloud-huawei`）
- **私有云**：`sXX-cloud-{platform}`（如 `s07-cloud-baidu`）
- **虚拟化平台**：`vXX-cloud-{platform}`（如 `v01-cloud-vmware`）
- **超融合平台**：`hXX-cloud-{platform}`（如 `h01-cloud-nutanix`）
- **容器云平台**：`cXX-cloud-{platform}`（如 `c01-cloud-harmony`）

### 3.5 技术要求
- **Java**: JDK 1.8
- **Maven**: 3.6.0+
- **依赖管理**: 通过父 `POM`（`05-plugins-cloud`）继承，插件模块不得单独引用依赖
  - **JSON 处理**: 使用 fastjson（继承自父 POM）
  - **HTTP 客户端**: Apache HttpClient（继承自父 POM）
  - **日志框架**: SLF4J/Logback（继承自父 POM）
- **禁止**: Spring Boot
- **POM 示例**:
  ```xml
  <parent>
      <groupId>com.futong.gemini</groupId>
      <artifactId>05-plugins-cloud</artifactId>
      <version>1.0.0</version>
  </parent>
  <artifactId>{prefix}-cloud-{platform}</artifactId>
  ```
- **日志配置**:
  ```java
  // 在 PluginTemplate 的 init 方法中配置
  DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.{platform}", "/cloud/{platform}");
  ```

### 3.6 认证配置
根据不同云平台类型配置相应的认证方式：

#### 3.6.1 IaaS 插件认证（AccessKey 方式）
```java
// 连接配置
ConnectionConfig config = new ConnectionConfig.Builder()
    .accessKeyId(accessBean.getAccessKeyId())
    .accessKeySecret(accessBean.getAccessKeySecret())
    .regionId(accessBean.getRegionId())
    .build();
```

#### 3.6.2 容器云插件认证（用户名密码方式）
```java
// 参考 HarmonyClient 认证配置
ConnectionConfig config = new ConnectionConfig.Builder()
    .protocol(accessBean.getProtocol())
    .host(accessBean.getServerIp())
    .port(accessBean.getServerPort())
    .authConfig(new AuthConfig.Builder()
        .username(accessBean.getUsername())
        .password(accessBean.getPassword())
        .build())
    .proxy(accessBean.getProxyHost(), accessBean.getProxyPort())
    .build();
```

## 4. 开发规范

### 4.1 方法签名规范

#### 4.1.1 默认参数设置方法
所有默认参数设置方法必须遵循以下规范：

```java
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

// 默认区域设置
public static boolean defaultRegion(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("region")) {
        request.getBody().getCloud().put("region", "default-region"); // 根据云平台设置默认区域
    }
    return true;
}

// 默认分页设置
public static boolean defaultPage50(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("pageNumber")) {
        request.getBody().getCloud().put("pageNumber", 1); // 默认第1页
    }
    if (!request.getBody().getCloud().containsKey("pageSize")) {
        request.getBody().getCloud().put("pageSize", 50); // 默认50条
    }
    return true;
}

// 默认时间范围设置（一天）
public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("startTime")) {
        request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 一天前
    }
    if (!request.getBody().getCloud().containsKey("endTime")) {
        request.getBody().getCloud().put("endTime", System.currentTimeMillis()); // 当前时间
    }
    return true;
}
```

#### 4.1.2 关键要求
1. **方法签名**：必须接收 `BaseCloudRequest request` 参数
2. **返回类型**：必须返回 `boolean` 类型
3. **参数设置**：通过 `request.getBody().getCloud().put(key, value)` 设置参数
4. **参数检查**：在设置参数前检查是否已存在，避免覆盖用户设置
5. **导入语句**：使用正确的包路径 `com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest`

#### 4.1.3 CI 模型云类型参数设置
**重要**：在数据转换时必须使用 `request.getPlugin().getRealm()` 获取云类型参数

```java
// 参考 HarmonyConvert 实现
public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONArray resultData) {
    CloudAccessBean accessBean = request.getBody().getAccess();
    List<TmdbTenant> tenants = new ArrayList<>();
    List<TmdbTenantLink> links = new ArrayList<>();

    if (CollUtil.isNotEmpty(resultData)) {
        for (int i = 0; i < resultData.size(); i++) {
            JSONObject info = resultData.getJSONObject(i);
            TmdbTenant tenant = new TmdbTenant();

            // 重要：设置云类型
            tenant.setCloud_type(request.getPlugin().getRealm());

            tenant.setOpen_id(info.getString("organId"));
            tenant.setFull_name(info.getString("organName"));
            tenant.setSimpl_name(info.getString("organName"));
            // 云上状态： 0正常 1锁定
            tenant.setStatus(info.getIntValue("status") == 0 ? 1 : 2);
            tenant.setAccount_id(accessBean.getCmpId());
            tenant.setDict_code("tenant");
            tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", info.getString("organId")));
            tenant.setInfo_json(info.toJSONString());
            tenants.add(tenant);

            // 创建关联关系
            TmdbTenantLink link = new TmdbTenantLink();
            link.setParent_tenant_id(null);
            link.setTenant_id(tenant.getBiz_id());
            link.setBiz_id("parent" + link.getTenant_id());
            links.add(link);
        }
    }

    Map<Class, List> result = new HashMap<>();
    result.put(TmdbTenant.class, tenants);
    result.put(TmdbTenantLink.class, links);
    return result;
}
```

### 4.2 数据格式化规范
- **输入**: 遵循云平台 API 参数格式
- **消息发送**: 使用 `BaseCloudService.fetchSend` 发送到 RabbitMQ
- **JSON 处理**: 使用 fastjson（继承自父 POM），禁止单独引入依赖

### 4.3 配置文件规范

#### 4.3.1 账号配置文件
**目录**: `src/main/resources/account/`

**文件列表**:
- `add_dispatch.json`: 调度配置
- `add_form_zh-CN.json`: 中文表单配置
- `add_form_en-US.json`: 英文表单配置

**表单配置示例**（参考华为云插件）:
```json
{
  "description": "初始纳管账号仅支持主账号纳管",
  "model": [
    {
      "type": "main",
      "name": "云平台运营主账号",
      "description": "云平台运营主账号,可用于云资源获取!",
      "form": [
        {
          "field": "cloudAccount",
          "label": "云账号",
          "type": "input",
          "required": true,
          "tips": "请输入云账号"
        },
        {
          "field": "username",
          "label": "用户名",
          "type": "input",
          "required": true,
          "tips": "请输入用户名"
        },
        {
          "field": "password",
          "label": "密码",
          "type": "password",
          "required": true,
          "tips": "请输入密码"
        },
        {
          "field": "serverIp",
          "label": "服务地址",
          "type": "input",
          "required": true,
          "tips": "请输入服务地址"
        }
      ]
    }
  ]
}
```

#### 4.3.2 服务注册文件
**文件路径**: `src/main/resources/META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`

**文件内容**:
```text
com.futong.gemini.plugin.cloud.{platform}.{Platform}PluginTemplate
```

## 5. 开发流程

### 5.1 客户端创建
1. **创建客户端类**：参考 `HarmonyClient` 实现
   ```java
   public class {Platform}Client {
       private ConnectionConfig config;
       private CloseableHttpClient httpClient;

       public {Platform}Client(ConnectionConfig config) {
           this.config = config;
           this.httpClient = createHttpClient();
       }
   }
   ```

2. **创建工具类**：参考 `ClientUtils` 实现
   ```java
   public class ClientUtils {
       public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
           body.loadAccess();
           ConnectionConfig config = buildConfig(body.getAccess());
           return (C) new {Platform}Client(config);
       }
   }
   ```

### 5.2 服务实现
1. **实现服务层**：调用云平台 API
   ```java
   public class ClusterService {
       public static BaseResponse queryClusterInfo(BaseCloudRequest request) {
           {Platform}Client client = ClientUtils.client({Platform}Client.class, request.getBody());
           JSONObject result = client.doGetDataJSON("/api/clusters", request.getBody().getCloud());
           return new BaseDataResponse<>(BaseResponse.SUCCESS, result);
       }
   }
   ```

2. **实现数据采集**：转换响应为 CI 模型
   ```java
   public class FetchService {
       public static BaseResponse fetchCluster(BaseCloudRequest request) {
           {Platform}Client client = ClientUtils.client({Platform}Client.class, request.getBody());
           JSONArray resultData = client.doGetDataJSONArray("/api/clusters", null);
           Map<Class, List> data = Convert.convertCluster(request, resultData);
           return BaseCloudService.fetchSend(request, data);
       }
   }
   ```

3. **发送数据到 RabbitMQ**：使用 `BaseCloudService.fetchSend`

### 5.3 注册和配置
1. **在 Register 中注册操作**：
   ```java
   public void onAfterLoadFetch() {
       register(ActionType.FETCH_CAAS_CLUSTER, FetchService::fetchCluster);
   }
   ```

2. **配置调度任务**：在 `add_dispatch.json` 中配置定时任务

3. **配置账号表单**：在 `add_form_zh-CN.json` 中配置认证表单

### 5.4 测试
1. **编写单元测试**：覆盖核心业务逻辑
2. **验证 CI 模型**：确保数据转换正确
3. **验证 MQ 消息**：确保消息发送成功
4. **测试示例**：
   ```java
   @Test
   public void testFetchCluster() {
       BaseCloudRequest request = createTestRequest();
       BaseResponse response = FetchService.fetchCluster(request);
       assertNotNull(response);
       assertEquals(BaseResponse.SUCCESS, response.getCode());
   }
   ```

## 6. 注意事项

### 6.1 编码规范
- 遵循包命名和插件命名规则
- **参考标准实现**：华为云插件（`c01-cloud-harmony`）作为标准实现
- 遵循 [docs/code-standards/](mdc:docs/code-standards)

### 6.2 错误处理
使用项目统一的异常处理机制：

```java
// 参考 HarmonyFetchService 的错误处理
public static BaseResponse fetchTenant(BaseCloudRequest request) {
    {Platform}Client client = ClientUtils.client({Platform}Client.class, request.getBody());
    try {
        JSONArray resultData = client.doGetDataJSONArray("/api/tenants", null);
        Map<Class, List> data = Convert.convertTenant(request, resultData);
        return BaseCloudService.fetchSend(request, data);
    } catch (Exception e) {
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取租户失败!"), e);
    }
}

// HTTP 错误处理
try {
    CloseableHttpResponse response = httpClient.execute(request);
    if (response.getStatusLine().getStatusCode() != 200) {
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("API调用失败"));
    }
} catch (BaseException e) {
    throw e;
} catch (Exception e) {
    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("发送云操作请求信息失败!"), e);
}
```

### 6.3 依赖约束
- **禁止单独引入依赖**：所有依赖必须通过父 `POM`（`05-plugins-cloud`）继承
- **JSON 处理**：使用 fastjson（继承自父 POM）
- **HTTP 客户端**：使用 Apache HttpClient（继承自父 POM）
- **日志框架**：使用 SLF4J/Logback（继承自父 POM）

### 6.4 业务说明
- **插件职责**：采集资源数据，转换为 CI 模型，通过 MQ 发送
- **数据流向**：云平台 API → 插件采集 → CI 模型转换 → RabbitMQ → 后续服务处理
- **云类型设置**：必须使用 `request.getPlugin().getRealm()` 设置云类型

### 6.5 性能和安全
- **连接池管理**：合理配置 HTTP 连接池
- **认证信息**：敏感信息不要硬编码，通过配置文件管理
- **代理支持**：支持 HTTP 代理配置
- **超时设置**：合理设置请求超时时间

## 7. 交付物清单

### 7.1 核心代码
- **客户端类**：`{Platform}Client`、`ClientUtils`、连接配置类
- **注册类**：`{Platform}Register` 继承 `BaseCloudRegister`
- **模板类**：`{Platform}PluginTemplate` 继承 `BaseCloudPluginTemplate`
- **服务层代码**：各种资源操作服务类
- **数据采集器**：`FetchService`、`Convert`、`RefreshService`

### 7.2 配置文件
- **账号配置**：`add_form_zh-CN.json`、`add_form_en-US.json`
- **调度配置**：`add_dispatch.json`
- **服务注册**：`META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`

### 7.3 文档
- **需求文档**：`docs/requirements.md`
- **API 说明**：支持的资源类型和接口
- **配置示例**：账号配置和使用说明

### 7.4 测试
- **单元测试**：覆盖核心业务逻辑
- **集成测试**：验证 API 调用和数据转换
- **测试报告**：功能和性能测试结果

## 8. 参考实现

### 8.1 标准实现
- **华为云插件**：`c01-cloud-harmony`（容器云标准实现）
- **阿里云插件**：`g01-cloud-ali`（IaaS 参考实现）
- **华为云插件**：`g03-cloud-huawei`（IaaS 实现）

### 8.2 参考文档
- [docs/api/cloud-plugin-api.md](mdc:docs/api/cloud-plugin-api.md)
- [docs/code-standards/cloud-plugin-development-standards.md](mdc:docs/code-standards/cloud-plugin-development-standards.md)
- [docs/architecture/deployment-guide.md](mdc:docs/architecture/deployment-guide.md)
- [docs/requirements/plugin-development-guide.md](mdc:docs/requirements/plugin-development-guide.md)

## 9. 重要提醒

1. **严格遵循华为云插件的实现模式**
2. **确保所有资源操作都有对应的数据采集机制**
3. **统一错误处理和日志记录**
4. **方法签名必须符合规范要求**
5. **导入语句使用正确的包路径**
6. **参数设置前检查是否已存在**
7. **必须使用 `request.getPlugin().getRealm()` 设置云类型**