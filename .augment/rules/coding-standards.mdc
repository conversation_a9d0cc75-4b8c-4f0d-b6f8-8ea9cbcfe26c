---
description:
globs: *.java
alwaysApply: true
---
# Futong Gemini 项目编码标准

基于华为云插件 `c01-cloud-harmony` 的实际实现标准制定。

## 通用编码规范

### Java 编码标准
参考 [docs/code-standards/java-coding-standards.md](mdc:docs/code-standards/java-coding-standards.md) 进行 Java 代码编写。

### 云插件开发标准
严格遵循 [docs/code-standards/cloud-plugin-development-standards.md](mdc:docs/code-standards/cloud-plugin-development-standards.md) 中定义的云插件开发标准。

### 插件开发标准
按照 [docs/code-standards/plugin-development-standards.md](mdc:docs/code-standards/plugin-development-standards.md) 执行插件开发。

## 项目结构约定

### Maven 项目结构
- 所有模块都应该有正确的 `pom.xml` 配置
- 父模块：主项目根目录的 [pom.xml](mdc:pom.xml)
- 依赖版本管理集中在父 POM 中

### 插件分类和命名规范
```
gemini-plugins/05-plugins-cloud/
├── g01-cloud-ali/           # 公有云：阿里云
├── g02-cloud-tencent/       # 公有云：腾讯云
├── g03-cloud-huawei/        # 公有云：华为云
├── s07-cloud-baidu/         # 私有云：百度云
├── c01-cloud-harmony/       # 容器云：谐云平台
├── v01-cloud-vmware/        # 虚拟化：VMware
└── h01-cloud-nutanix/       # 超融合：Nutanix
```

### 包命名约定
```java
com.futong.gemini.model.*           // 数据模型
com.futong.gemini.sdk.*             // SDK 相关
com.futong.gemini.plugin.*          // 插件相关
com.futong.gemini.plugin.cloud.*    // 云插件特定
com.futong.gemini.server.*          // 服务端代码

// 具体插件包命名
com.futong.gemini.plugin.cloud.{vendor}.client     // 客户端类
com.futong.gemini.plugin.cloud.{vendor}.service    // 业务服务层
com.futong.gemini.plugin.cloud.{vendor}.sampler    // 数据采集器
com.futong.gemini.plugin.cloud.{vendor}            // 注册和模板类
```

### 目录结构规范
```
src/main/java/com/futong/gemini/plugin/cloud/{vendor}/
├── client/          # 客户端类（如 HarmonyClient、ClientUtils）
├── service/         # 业务服务层（资源操作代码）
├── sampler/         # 数据采集器（采集任务代码、数据转换）
├── {Vendor}Register.java           # 注册类
└── {Vendor}PluginTemplate.java     # 模板类
```

## 技术约束

### 开发环境限制
- **Java 版本**: 严格使用 JDK 1.8
- **Maven 版本**: 3.6.0
- **禁止框架**: 不允许使用 SpringBoot
- **日志框架**: 使用项目统一的日志配置

### 依赖管理
- 云服务商 SDK 版本在各自模块的 `pom.xml` 中指定
- 公共依赖在父 POM 中管理
- 避免依赖冲突，使用 Maven Shade Plugin 进行打包

## 核心类实现规范

### 客户端类实现
```java
// 参考 HarmonyClient 实现
public class {Vendor}Client {
    private ConnectionConfig config;
    private CloseableHttpClient httpClient;

    // 认证方法
    public synchronized String doToken() throws Exception {
        // 实现认证逻辑
    }

    // HTTP 请求方法
    public String doGet(String apiPath, Map<String, ?> params) { }
    public String doPost(String apiPath, JSONObject requestBody) { }
    public String doPut(String apiPath, JSONObject requestBody) { }
    public String doDelete(String apiPath) { }

    // 数据解析方法
    public JSONObject doGetDataJSON(String apiPath, Map<String, ?> params) { }
    public JSONArray doGetDataJSONArray(String apiPath, Map<String, ?> params) { }
}
```

### 注册类实现
```java
// 参考 HarmonyRegister 实现
public class {Vendor}Register extends BaseCloudRegister {
    @Override
    public void load() {
        onAfterLoadPlatform();    // 加载云平台操作
        onAfterLoadFetch();       // 加载同步调度信息
        // 根据插件类型加载对应资源操作
    }

    public void onAfterLoadPlatform() {
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatformAccountService::authAccount);
    }
}
```

### 模板类实现
```java
// 参考 HarmonyPluginTemplate 实现
@Slf4j
public class {Vendor}PluginTemplate extends BaseCloudPluginTemplate {
    @Override
    public void init(String key) {
        super.init(key);
        loadAccountForm();
        loadAccountDispatch();
        // 指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.{vendor}", "/cloud/{vendor}");
    }

    @Override
    public BaseCloudRegister getRegister() {
        return new {Vendor}Register();
    }
}
```

## 代码质量要求

### 异常处理
```java
// 使用项目统一的异常处理
throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作失败!"), e);
throw new BaseException(BaseResponse.ERROR_BIZ.of("业务错误"), e);
```

### 日志记录
```java
// 使用 Lombok 的 @Slf4j 注解
@Slf4j
public class ExampleService {
    // 配置特定日志路径
    DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.{vendor}", "/cloud/{vendor}");
}
```

## 插件开发模式

### 服务注册
所有插件服务必须在 `META-INF/services/` 目录下正确注册：
- 文件路径：`src/main/resources/META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`
- 内容：插件模板类的完整类名

### 账号配置
插件账号相关配置放在 `src/main/resources/account/` 目录：
- `add_dispatch.json`: 调度配置
- `add_form_zh-CN.json`: 中文表单配置
- `add_form_en-US.json`: 英文表单配置

### 认证配置示例
```json
// add_form_zh-CN.json 示例（参考华为云插件）
{
  "description": "初始纳管账号仅支持主账号纳管",
  "model": [
    {
      "type": "main",
      "name": "云平台运营主账号",
      "form": [
        {
          "field": "cloudAccount",
          "label": "云账号",
          "type": "input",
          "required": true
        },
        {
          "field": "username",
          "label": "用户名",
          "type": "input",
          "required": true
        },
        {
          "field": "password",
          "label": "密码",
          "type": "password",
          "required": true
        },
        {
          "field": "serverIp",
          "label": "服务地址",
          "type": "input",
          "required": true
        }
      ]
    }
  ]
}
```

### 资源同步模式
```java
// 1. 实现 FetchService 类进行资源采集
public class FetchService {
    public static BaseResponse fetchTenant(BaseCloudRequest request) {
        {Vendor}Client client = ClientUtils.client({Vendor}Client.class, request.getBody());
        JSONArray resultData = client.doGetDataJSONArray("/api/path", null);
        Map<Class, List> data = Convert.convertTenant(request, resultData);
        return BaseCloudService.fetchSend(request, data);
    }
}

// 2. 实现 Convert 类进行数据转换
public class Convert {
    public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONArray resultData) {
        // 转换为 CI 模型
        // 使用 request.getPlugin().getRealm() 获取云类型
    }
}
```

## 文档和测试

### 文档要求
- 每个插件必须有 `docs/requirements.md` 文档
- 说明支持的资源类型和 API 接口
- 提供配置示例和使用说明

### 测试标准
- 单元测试覆盖核心业务逻辑
- 集成测试验证 API 调用
- 错误场景处理测试

## 部署和构建

### 构建配置
参考 [docs/architecture/deployment-guide.md](mdc:docs/architecture/deployment-guide.md) 进行部署配置。

### CI/CD 流程
遵循项目的 [Jenkinsfile](mdc:Jenkinsfile) 定义的构建流程。

### 容器化
服务端应用使用 [gemini-server/Dockerfile](mdc:gemini-server/Dockerfile) 进行容器化部署。

## 数据库和配置

### 数据库脚本
- 初始化脚本：[gemini-server/src/main/resources/db/init.sql](mdc:gemini-server/src/main/resources/db/init.sql)
- 更新脚本：[gemini-server/src/main/resources/db/update.sql](mdc:gemini-server/src/main/resources/db/update.sql)

### 配置文件
- 启动配置：[gemini-server/src/main/resources/bootstrap.yml](mdc:gemini-server/src/main/resources/bootstrap.yml)
- 国际化：[gemini-server/src/main/resources/i18n/](mdc:gemini-server/src/main/resources/i18n)

## 代码审查要点

1. **包命名是否符合规范**
2. **是否正确继承基础类**
3. **异常处理是否统一**
4. **日志配置是否正确**
5. **资源清理是否完整**
6. **测试覆盖是否充分**
7. **文档是否完整**
8. **配置文件是否正确**

## 性能和安全

### 性能要求
- 合理使用连接池和缓存
- 避免内存泄漏
- 异步处理长时间操作

### 安全要求
- 敏感信息不要硬编码
- 使用安全的认证方式
- 输入参数验证和过滤


