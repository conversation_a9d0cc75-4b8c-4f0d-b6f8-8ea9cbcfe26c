pipeline { //定义一个流水线脚本
	agent {
	    label 'oper'
	}

    //参数化构建
    parameters {
        booleanParam(name: 'SEND_MAIL', defaultValue: false, description: '是否发送邮件通知')
        // booleanParam(name: 'SEND_SONAR', defaultValue: false, description: '是否进行代码质量检测')
        string(name: 'TAG_NAME', defaultValue: 'v4.0.6.D', description: '请输入要构建的Tag')
    }

    //环境变量
    environment {
        OUT_DIR = '/work/futong-cm-standard/futong-gemini-maven-jar-406D'
        DOCKER_REGISTRY_HOST = 'harbor.futongcloud.com.cn:15005'
        DOCKER_TEMP_PATH = 'cmp/public'
        ENVIRONMENT_NAME = '开发环境'
    }

	stages { //所有工作的开始
		stage('拉取git代码') { //一项工作的开始，括号里的是该工作的名称
			steps { //声明式脚本往这里面写
			    git branch: 'dev-4.0.6', credentialsId: 'lipp', url: 'http://gitlab.futongcloud.com.cn:82/cmp/java/futong-public-geimini.git'
				echo '拉取完成' //控制台打印
			}
		}

// 		stage('代码质量检测') {
//             steps {
//                 // withSonarQubeEnv('sonarqube') 表示在SonarQube环境中运行
//                 // 在系统管理-系统配置中配置 SonarQube servers
//                 // 1. 选中 environment variables
//                 // 2. Nmae 输入 sonarqube，表示SonarQube服务器的名称
//                 withSonarQubeEnv('sonarqube'){
//                     sh '''
//                     echo "Java 版本:"
//                     java -version
//                     /data/jenkins/tools/hudson.tasks.Maven_MavenInstallation/maven/bin/mvn findbugs:findbugs
//                     /data/jenkins/tools/hudson.tasks.Maven_MavenInstallation/maven/bin/mvn -am clean verify sonar:sonar -Dsonar.branch.name=dev-4.0.6
//                     '''
//                 }
//             }
//         }
//
//         stage('发送sonarqube报告') {
//           steps {
//             script {
//               echo "sonarqube report "
//               sh 'docker pull harbor.futongcloud.com.cn:15005/cmp/sonarqube-report:v1.0'
//               sh 'docker run --rm -i harbor.futongcloud.com.cn:15005/cmp/sonarqube-report:v1.0 bash -c "cd /code && python product_quality_report.py futong-public-gemini http://************:9000 cmp_java_futong-public-geimini_AZelwcYAnd7tZRKdkl1z::dev-4.0.6 squ_e90ce2b1b59703a1872a6504dfbfc087457538da http://************:82/api/v4 sdVG8GRuGqm3xiNCFs-W 182 dev-4.0.6 <EMAIL>" '
//             }
//           }
//         }

		stage('编译代码') {
			steps {
			    sh """
			    export JAVA_HOME=/usr/local/java
			    /data/jenkins/tools/hudson.tasks.Maven_MavenInstallation/maven/bin/mvn clean package -pl gemini-server -am
			    """
				echo '构建完成'
			}
		}

        stage('构建Docker镜像') {
            steps {
                script {

                    echo "构建futong-public-gemini服务镜像开始."
                    // 使用 sh 命令执行 Docker 构建
                    sh """
                    cd ${WORKSPACE}/gemini-server
                    pwd
                    mkdir -p ${OUT_DIR}/futong-public-gemini/
                    rm -rf ${OUT_DIR}/futong-public-gemini/*
                    cp Dockerfile ${OUT_DIR}/futong-public-gemini/
                    cp ${OUT_DIR}/futong-public-gemini.jar ${OUT_DIR}/futong-public-gemini/
                    cd ${OUT_DIR}/futong-public-gemini/
                    docker build -t ${DOCKER_REGISTRY_HOST}/${DOCKER_TEMP_PATH}/futong-public-gemini:${TAG_NAME} .
                    docker push ${DOCKER_REGISTRY_HOST}/${DOCKER_TEMP_PATH}/futong-public-gemini:${TAG_NAME}
                    docker rmi ${DOCKER_REGISTRY_HOST}/${DOCKER_TEMP_PATH}/futong-public-gemini:${TAG_NAME}
                    """
                    echo "构建futong-public-gemini服务镜像成功."
                }
            }
        }
	}



	post{
	    always{
            script {// 确认是否发邮件
                  if (params.SEND_MAIL) {
                      emailext body: '${DEFAULT_CONTENT}',
                            subject: '${DEFAULT_SUBJECT}',
                                to: '${DEFAULT_RECIPIENTS}'
                  }
            }
	    }
	}
}
