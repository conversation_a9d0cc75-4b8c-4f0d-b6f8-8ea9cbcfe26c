# 插件构建指南

## 1. 插件名称规则

插件名称遵循以下格式：`模块-插件名-插件版本-cmp版本-插件版本`

- **模块**：可选值为 [product|biz|project|cloud]。
- **插件名**：云插件通常取云类型，例如阿里 `public_ali`。
- **cmp版本**：此信息来自 `gemini.pom` 文件中的 `<yunjing.version>v4.0.3</yunjing.version>`，无需修改。
- **插件版本**：通常是数字标识，如 [1,2,3...]。

**示例**：
cloud-public_ali-4.5.10-v4.0.1-1
## 2. 构建命令
### 构建概述

使用Maven命令进行插件的构建，无需手动部署依赖包。

### 基本构建命令

```bash
mvn -pl 插件目录 -am clean package
参数-CMP版本(可选)：默认值“v4.0.3”,来自 `gemini.pom` 文件的 `<yunjing.version>v4.0.3</yunjing.version>
如需指定则在命令后增加参数 -Dyunjing.version=CMP版本
参数-插件版本(可选)：默认值“1”,来自 `gemini-plugins.pom` 文件的 `<plugin.version>1</plugin.version>`
如需指定则在命令后增加参数 -Dplugin.version=版本号
参数-输出路径(可选)：默认值"/cloudJars",来自gemini-plugins.pom 文件义的 <output.directory>/cloudJars</output.directory>
如需指定则在命令后增加参数 -Doutput.directory=输出目录

云插件：
mvn -pl gemini-plugins/05-plugins-cloud/g01-cloud-ali -am clean package
示例：指定插件版本
mvn -pl gemini-plugins/05-plugins-cloud/g01-cloud-ali -am clean package -Dplugin.version=1
mvn -pl gemini-plugins/05-plugins-cloud/g02-cloud-tencent -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/g03-cloud-huawei -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/g04-cloud-aws -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s01-cloud-cloudos-e5 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s01-cloud-cloudos-e7 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s01-cloud-cloudos-e7111 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s02-cloud-ziguang-e7 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/v01-cloud-vmware -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/v02-cloud-cas-e7 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/v03-cloud-fusionCompute-v8.0 -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s05-cloud-manageOne -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s06-cloud-UCloud -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/h01-cloud-uis -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/h02-cloud-nutanix -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/c01-cloud-harmony -am clean package
mvn -pl gemini-plugins/05-plugins-cloud/s07-cloud-baidu -am clean package -D'yunjing.version=v4.0.6'
产品插件：
mvn -pl gemini-plugins/04-plugins-product/01-product-aofei -am clean package
项目插件：
mvn -pl gemini-plugins/03-plugins-project/01-project-gk -am clean package
业务插件：
mvn -pl gemini-plugins/02-plugins-biz/01-biz-custom -am clean package
mvn -pl gemini-plugins/02-plugins-biz/02-biz-dth-dws -am clean package



mvn -pl gemini-plugins/04-plugins-product/01-product-aofei -am clean package
