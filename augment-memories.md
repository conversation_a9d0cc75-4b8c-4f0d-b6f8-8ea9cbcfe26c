# Futong Gemini 项目开发规范与指南

## 项目概述
Futong Gemini 是一个基于 Java 的云平台插件系统，用于统一管理不同云平台的资源。项目采用模块化架构，支持多云平台资源管理。

## 核心技术约束

### 开发环境限制
- **Java 版本**: 严格使用 JDK 1.8
- **Maven 版本**: 3.6.0
- **禁止框架**: 不允许使用 SpringBoot
- **日志框架**: 使用项目统一的日志配置

### 项目结构
```
gemini-model/           # 数据模型定义
gemini-sdk/            # SDK 模块
gemini-server/         # 服务端应用
gemini-plugins/        # 插件模块
├── 01-plugin-sdk/     # 插件 SDK 基础框架
├── 05-plugins-cloud/  # 云平台插件（主要开发区域）
    ├── 01-cloud-sdk/      # 云插件基础 SDK
    ├── g01-cloud-ali/     # 阿里云插件（参考实现）
    ├── g02-cloud-tencent/ # 腾讯云插件
    └── s07-cloud-baidu/   # 百度云插件（当前开发目标）
```

## 编码规范

### 包命名约定
```java
com.futong.gemini.model.*           // 数据模型
com.futong.gemini.sdk.*             // SDK 相关
com.futong.gemini.plugin.*          // 插件相关
com.futong.gemini.plugin.cloud.*    // 云插件特定
com.futong.gemini.server.*          // 服务端代码

// 百度云插件包命名
com.futong.gemini.plugin.cloud.baidu.client     // 通用Client类
com.futong.gemini.plugin.cloud.baidu.common     // 通用工具类
com.futong.gemini.plugin.cloud.baidu.service    // 服务层
com.futong.gemini.plugin.cloud.baidu            // 主要类（Register、Template）
```

### 目录结构规范
```
src/main/java/com/futong/gemini/plugin/cloud/{vendor}/
├── common/          # 通用工具类和客户端
├── service/         # 业务服务层
├── sampler/         # 数据采集器（如果需要）
├── {Vendor}CloudRegister.java        # 注册类
└── {Vendor}CloudPluginTemplate.java  # 模板类
```

### 异常处理
```java
// 使用项目统一的异常处理
throw new BaseException(BaseResponse.ERROR_BIZ, e);
```

### 日志记录
```java
// 使用 Lombok 的 @Slf4j 注解
@Slf4j
public class ExampleService {
    // 配置特定日志路径
    DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.{vendor}", "/cloud/{vendor}")
}

// 百度云插件日志配置
DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.baidu", "/cloud/baidu")
```

## 云插件开发指南

### 核心类实现要求

#### 1. 基础类继承
```java
// 客户端类
public class CloudClient extends BaseCloudClient {
    // 实现统一的客户端接口
}

// 注册类
public class CloudRegister extends BaseCloudRegister {
    // 实现统一的注册接口
}

// 模板类
public class CloudPluginTemplate extends BaseCloudPluginTemplate {
    // 实现所有操作类型
}
```

#### 2. 必需实现的服务类
- `AccountService`: 账号服务
- `CloudService`: 云服务基础类
- `ComputeInstanceService`: 云主机服务
- `DiskService`: 磁盘服务
- `ImageService`: 镜像服务
- `PlatformService`: 平台服务
- `SecurityGroupService`: 安全组服务
- `SnapshotService`: 快照服务
- `VpcService`: VPC服务

#### 3. 辅助类
- `Constant`: 常量服务
- `Convert`: 数据统一服务
- `FetchService`: 定时任务服务
- `RefreshService`: 回调服务

### 方法签名规范

#### 默认参数设置方法
所有默认参数设置方法必须遵循以下规范：

```java
// 正确的方法签名
public static boolean defaultRegion(BaseCloudRequest request) {
    request.getBody().getCloud().put("region", "bj"); // 默认使用北京地域
    return true;
}

public static boolean defaultPage50(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("pageNumber")) {
        request.getBody().getCloud().put("pageNumber", 1);//默认1
    }
    if (!request.getBody().getCloud().containsKey("pageSize")) {
        request.getBody().getCloud().put("pageSize", 50);//默认50
    }
    return true;
}

public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
    if (!request.getBody().getCloud().containsKey("startTime")) {
        request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 24 * 60 * 60 * 1000);//一天前
    }
    if (!request.getBody().getCloud().containsKey("endTime")) {
        request.getBody().getCloud().put("endTime", System.currentTimeMillis());//当前时间
    }
    return true;
}
```

#### 关键要求
1. **方法签名**：必须接收 `BaseCloudRequest request` 参数
2. **返回类型**：必须返回 `boolean` 类型
3. **参数设置**：通过 `request.getBody().getCloud().put(key, value)` 设置参数
4. **参数检查**：在设置参数前检查是否已存在，避免覆盖用户设置

#### 导入语句规范
```java
// ✅ 正确的导入语句
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

// ❌ 错误的导入语句
import com.futong.common.response.BaseResponse;  // 错误的包路径
```

### 插件开发模式

#### 服务注册
所有插件服务必须在 `META-INF/services/` 目录下正确注册：
- 文件路径：`src/main/resources/META-INF/services/com.futong.gemini.plugin.sdk.PluginInterface`
- 内容：插件模板类的完整类名

#### 账号配置
插件账号相关配置放在 `src/main/resources/account/` 目录：
- `add_dispatch.json`: 调度配置
- `add_form_zh-CN.json`: 中文表单配置  
- `add_form_en-US.json`: 英文表单配置

#### 资源同步模式
```java
// 1. 继承 BaseCloudService
// 2. 实现资源获取方法
// 3. 使用 toMessageAndSend 发送到 RabbitMQ
// 4. 配置刷新任务和重试机制
```

### 参考实现
主要参考阿里云插件 `g01-cloud-ali` 的实现模式。

### 技术限制
- **数据格式**: 统一返回数据格式，参考 `com.futong.gemini.model.otc.*` 包下的实体类

### 开发流程
1. **客户端创建**: 
2. **数据查询**:
3. **数据格式化**: 保持与阿里云参数处理和校验逻辑一致
4. **消息发送**: 通过 `BaseCloudService.toMessageAndSend` 方法发送数据至 RabbitMQ

## 代码质量要求

### 性能要求
- 合理使用连接池和缓存
- 避免内存泄漏
- 异步处理长时间操作

### 安全要求
- 敏感信息不要硬编码
- 使用安全的认证方式
- 输入参数验证和过滤

### 测试标准
- 单元测试覆盖核心业务逻辑
- 集成测试验证 API 调用
- 错误场景处理测试

## 代码审查要点

1. **包命名是否符合规范**
2. **是否正确继承基础类**
3. **异常处理是否统一**
4. **日志配置是否正确**
5. **资源清理是否完整**
6. **测试覆盖是否充分**
7. **文档是否完整**
8. **配置文件是否正确**

## 依赖管理

### Maven 配置
- 父模块：主项目根目录的 `pom.xml`
- 依赖版本管理集中在父 POM 中
- 云服务商 SDK 版本在各自模块的 `pom.xml` 中指定
- 使用 Maven Shade Plugin 进行打包，避免依赖冲突

### 文档要求
- 每个插件必须有 `docs/requirements.md` 文档
- 说明支持的资源类型和 API 接口
- 提供配置示例和使用说明

## 重要提醒

1. **严格遵循包命名规范**
2. **参考阿里云插件的实现模式**
3. **确保所有资源操作都有对应的刷新机制**
4. **统一错误处理和日志记录**
5. **方法签名必须符合规范要求**
6. **导入语句使用正确的包路径**
7. **参数设置前检查是否已存在**
