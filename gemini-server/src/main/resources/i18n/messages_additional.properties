gemini.proxy.notfound.realm=缺少参数realm信息
gemini.proxy.notfound.template=未找到该云类型所支持的模板
gemini.load.notfound.jar=未能找到jar包!
gemini.load.success=加载云类型{0},新的JAR包{1}已装载成功!
gemini.load.fail.jar=无法加载JAR包
gemini.load.notfound.template=未检测到JAR包里模板信息!请确保JAR有继承PluginInterface接口
gemini.unload.notfound=云类型{0}没有相关jar包被加载
gemini.unload.success=卸载云类型{0},JAR包:{1}已卸载成功!
gemini.unload.fail.jar=无法卸载JAR包

gemini.public.http.fail=HttpClient构建失败
gemini.public.param.ids.notnull=参数ids不能为空.
gemini.public.token.fail=获取token失败
gemini.public.auth.fail=账号认证失败
gemini.public.auth.success=账号认证通过.
gemini.public.send.message.fail=发送MQ消息到数据底座失败!
gemini.public.fetch.host.fail=同步宿主机资源数据失败
gemini.public.fetch.host.success=成功获取宿主机及其相关联资源信息.
gemini.public.fetch.instance.fail=同步云主机资源数据失败
gemini.public.fetch.disk.fail=同步磁盘资源数据失败
gemini.public.fetch.nic.fail=同步虚拟网卡资源数据失败
gemini.public.fetch.storagePool.fail=同步存储池资源数据失败
gemini.public.fetch.snapshot.fail=同步快照资源数据失败
gemini.public.fetch.template.fail=同步模版资源数据失败
gemini.public.fetch.template.success=同步模版完成.
gemini.public.fetch.hostpool.fail=同步主机池资源数据失败
gemini.public.fetch.hostpool.success=同步主机池完成.
gemini.public.fetch.cluster.fail=同步集群资源数据失败
gemini.public.fetch.cluster.success=同步集群完成.
gemini.public.fetch.securityGroup.fail=同步安全组资源数据失败
gemini.public.fetch.securityGroup.success=同步安全组完成.

gemini.h3c.cas.host.instance.relation.fail=获取宿主机与云主机关系异常
gemini.h3c.cas.fetch.instance.disk.relation.fail=同步云主机磁盘网卡资源数据失败
gemini.h3c.cloudos.fetch.host.instance.relation.fail=同步宿主机与云主机资源关系数据失败
