#Translated properties file
#Tue Sep 03 10:32:17 CST 2024
gemini.proxy.notfound.realm=Parameter realm information is missing
gemini.proxy.notfound.template=No template supported for this cloud type was found
gemini.load.notfound.jar=jar package not found!
gemini.load.success=Load cloud type {0}, new JAR package {1} successfully loaded!
gemini.load.fail.jar=Unable to load the JAR package
gemini.load.notfound.template=Template information not detected in JAR package! Make sure the JAR inherits the PluginInterface interface
gemini.unload.notfound=Cloud type {0} No associated jar package was loaded
gemini.unload.success=Uninstall cloud type {0},JAR package :{1} Uninstall successfully!
gemini.unload.fail.jar=Unable to uninstall the JAR package
