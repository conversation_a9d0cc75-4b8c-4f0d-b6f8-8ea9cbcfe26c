server:
  port: 30078
  max-http-header-size: 5MB
spring:
  servlet:
    multipart:
      max-file-size: 5120MB
      max-request-size: 5120MB
  application:
    name: futong-public-gemini-local
  cloud:
    consul:
      discovery:
        instance-id: ${spring.application.name}:${server.port}
        prefer-ip-address: true #true以IP地址注册到服务中心，相互注册使用IP地址,false服务名
        health-check-interval: 10s
        hostname: ${spring.application.name}
        service-name: ${spring.application.name}
        enabled: true
      #        ip-address: **************
      enabled: false #本地测试不用注册中心
      host: ftcloud-consul
      port: 8500
  datasource:
    druid:
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
        url-pattern: /druid/*
        enabled: true
    dynamic:
      primary: nxc #设置默认的数据源或者数据源组,默认值即为cmp
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      druid:
        maxActive: 20
        initialSize: 5
        maxWait: 60000
        minIdle: 5
        timeBetweenEvictionRunsMillis: 60000 #设置执行一次连接回收器的时间
        minEvictableIdleTimeMillis: 300000 #设置时间： 该时间内没有任何操作的空闲连接会被回收
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true # 空闲时校验
        testOnBorrow: false # 使用中是否校验有效性
        testOnReturn: false # 归还连接池时是否校验
        poolPreparedStatements: false  # mysql 不推荐打开预处理连接池
        filters: slf4j # 注意这个值和druid原生不一致，默认启动了stat,wall #stat,logback
        useGlobalDataSourceStat: true  # 统计所有数据源状态
        connectionProperties: druid.stat.mergSql=true;druid.stat.slowSqlMillis=500  # sql合并统计 设置慢sql时间为500，超过500 会有记录提示
      datasource:
        nxc:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************
          username: root
          password: L7bzd1gmm+db
          schema: db/init.sql
          data: db/update.sql
          separator: $
        bxc:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************
          username: root
          password: L7bzd1gmm+db
          separator: $
  elasticsearch:
    rest:
      uris: ************:9200
      connection-timeout: 10000    # 连接超时(ms)
      read-timeout: 60000         # 读取超时(ms) 建议30-60秒
  rabbitmq:
    enabled: true
    host: ************
    port: 5672
    username: user
    password: L7bzd1gmm+mq
  redis:
    host: ************
    port: 6379
    password: L7bzd1gmm+redis
    timeout: 10000
    max_wait: 10000
    max_idle: 5
    max_active: 500
management:
  endpoint:
    health:
      show-details: always  #显示健康具体信息，默认不会显示详细信息
minio:
  mioHost: ************
  mioPort: 9000
  ak: admin
  sk: L7bzd1gmm+minio
  bucket: scaffold
  path: plugin/
gemini:
  async:
    corePoolSize: 6
#  endpoints:
#    web:
#      exposure:
#        include: '*'    # 暴露所有节点
#开启打印sql
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
