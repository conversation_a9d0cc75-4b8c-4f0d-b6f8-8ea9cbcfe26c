package com.futong.gemini.common.proxy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "futong-public-scaffold", url = "futong-public-scaffold:30079")
public interface ScaffoldProxy {
    @PostMapping("/scaffold/v1/plugin/list")
    BaseDataResponse<JSONArray> list(@RequestBody JSONObject request);
    @PutMapping("/scaffold/v1/plugin")
    BaseResponse updatePlugin(@RequestBody JSONObject request);
}
