package com.futong.gemini.common.utils;

import cn.hutool.core.lang.Opt;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class DynamicClassLoader {

    private ClassLoader classLoader;

    private Map<String, Entry.E2<String, URLClassLoader>> classLoaderMap = new ConcurrentHashMap<>();

    {
        ClassLoader thisClassLoader = DynamicClassLoader.class.getClassLoader();
        classLoader = thisClassLoader != null ? thisClassLoader : ClassLoader.getSystemClassLoader();
    }

    public boolean existsJar(String key) {
        return classLoaderMap.containsKey(key);
    }

    public String getJarPath(String key) {
        return Opt.ofNullable(classLoaderMap.get(key)).map(Entry.E2::getV1).orElse(null);
    }

    public URLClassLoader loadJar(String jarPath, String key) {
        unloadJar(key);
        try {
            URL url = new File(jarPath).toURI().toURL();
            URLClassLoader classLoader = new URLClassLoader(new URL[]{url}, this.classLoader);
            classLoaderMap.put(key, new Entry.E2<>(jarPath, classLoader));
            return classLoader;
        } catch (Exception e) {
            log.error("云类型:" + key + ",加载JAR包:" + jarPath + "失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "云类型:" + key + ",加载JAR包:" + jarPath + "失败!");
        }
    }


    public void unloadJar(String key) {
        Entry.E2<String, URLClassLoader> urlClassLoader = classLoaderMap.remove(key);
        try {
            if (urlClassLoader != null && urlClassLoader.v2 != null) {
                urlClassLoader.v2.close();
            }
        } catch (Exception e) {
            log.error("卸载云类型:" + key + ",JAR包:" + urlClassLoader.v1 + "失败!", e);
            throw new BaseException(BaseResponse.ERROR_SYS, "卸载云类型:" + key + ",JAR包:" + urlClassLoader.v1 + "失败!");
        }
    }

}
