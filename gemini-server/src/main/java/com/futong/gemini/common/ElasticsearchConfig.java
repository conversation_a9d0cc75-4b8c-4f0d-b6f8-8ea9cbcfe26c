package com.futong.gemini.common;

import org.apache.http.HttpHost;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;

@Configuration
public class ElasticsearchConfig {

    private final ElasticsearchRestClientProperties properties;

    public ElasticsearchConfig(ElasticsearchRestClientProperties properties) {
        this.properties = properties;
    }

    @Bean(destroyMethod = "close")
    public RestHighLevelClient restHighLevelClient() {
        // 1. 创建基础Builder
        RestClientBuilder builder = RestClient.builder(createHosts());

        // 2. 应用标准配置
        builder = applyStandardConfig(builder);

        // 3. 应用高级配置（修复重试配置）
        builder = applyAdvancedConfig(builder);

        return new RestHighLevelClient(builder);
    }

    private HttpHost[] createHosts() {
        return properties.getUris().stream().map(this::createHttpHost).toArray(HttpHost[]::new);

    }

    private HttpHost createHttpHost(String uri) {
        try {
            return createHttpHost(URI.create(uri));
        } catch (IllegalArgumentException ex) {
            return HttpHost.create(uri);
        }
    }

    private HttpHost createHttpHost(URI uri) {
        if (!StringUtils.hasLength(uri.getUserInfo())) {
            return HttpHost.create(uri.toString());
        }
        try {
            return HttpHost.create(new URI(uri.getScheme(), null, uri.getHost(), uri.getPort(), uri.getPath(),
                    uri.getQuery(), uri.getFragment()).toString());
        } catch (URISyntaxException ex) {
            throw new IllegalStateException(ex);
        }
    }

    private RestClientBuilder applyStandardConfig(RestClientBuilder builder) {
        return builder.setRequestConfigCallback(requestConfigBuilder ->
                requestConfigBuilder
                        .setConnectTimeout((int) properties.getConnectionTimeout().toMillis())
                        .setSocketTimeout((int) properties.getReadTimeout().toMillis())
        );
    }

    private RestClientBuilder applyAdvancedConfig(RestClientBuilder builder) {
        // 使用新版的重试配置API
        return builder
                .setHttpClientConfigCallback(this::configureHttpClient);
    }

    private HttpAsyncClientBuilder configureHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
        // 配置Keep-Alive
        httpClientBuilder.setKeepAliveStrategy((response, context) -> 30 * 1000);

        // 配置连接池
        httpClientBuilder.setMaxConnTotal(50);
        httpClientBuilder.setMaxConnPerRoute(10);

        return httpClientBuilder;
    }


}