package com.futong.gemini.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.minio.FTMinioUtils;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.SingletonHolder;
import com.futong.gemini.common.utils.DynamicClassLoader;
import com.futong.gemini.plugin.sdk.PluginInterface;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.gemini.plugin.sdk.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.*;
import java.net.URL;
import java.net.URLClassLoader;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;
import java.util.concurrent.ExecutorService;

@Slf4j
public class GeminiFramework {
    //分隔符
    public static final String CONJUNCTION = ":";

    public static Map<String, PluginInterface<?>> plugins = new HashMap<>();

    public static BaseResponse onProxy(JSONObject arguments, String realm, String version) {
        log.info("Gemini接收到请求信息:{}", arguments.toJSONString());
        if (StrUtil.isEmpty(realm)) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.proxy.notfound.realm"));
        }
        //获取模板信息
        PluginInterface plugin = getPlugin(realm, version);
        if (plugin == null) {
            throw new BaseException(BaseResponse.ERROR_BIZ.ofI18n("gemini.proxy.notfound.template"));
        }
        log.info("Gemini调用模板{}进行处理", plugin.getClass().getSimpleName());
        arguments.put("plugin", new PluginInfo(realm, version));
        //如果是token请求则在此处进行统一拦截
        if (arguments.containsKey("cache_token") && arguments.getBoolean("cache_token")) {
            arguments.remove("cache_token");
            return TokenService.token(arguments);
        }
        //判断如果是异常任务则发起异常调用
        if (arguments.containsKey("async") && arguments.getBoolean("async")) {
            String traceId = MDC.get("TRACE_ID");
            AsyncConfiguration.Task.task.execute(() ->
                    {
                        try {
                            MDC.put("TRACE_ID", traceId);
                            plugin.execute(arguments);
                        } catch (Exception e) {
                            log.error("异步线程发生异常:", e);
                        }
                    }
            );
            return BaseResponse.SUCCESS.of("异步调用发起成功!");
        }
        return plugin.execute(arguments);
    }

    public static BaseResponse loadPluginInterface(JSONObject plugin) {
        //获取模板key
        String key = getPluginKey(plugin);
        log.info("云类型({}),加载plugin:{}", key, plugin.toJSONString());
        //获取记录插件在minio中的路径，并记录到本地
        String jarFilePath = FTMinioUtils.loadLocal(plugin.getString("path"));
        // 创建File对象
        File jarFile = new File(jarFilePath);
        if (!jarFile.exists()) {
            throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY.ofI18n("gemini.load.notfound.jar"));
        }
        loadJar(plugin, jarFile, key);
        return BaseResponse.SUCCESS.ofI18n("gemini.load.success", key, jarFilePath);
    }

    public static BaseResponse unloadPlugin(JSONObject plugin) {
        //获取模板key
        String key = getPluginKey(plugin);
        log.info("云类型({}),卸载plugin:{}", key, plugin.toJSONString());
        //获取类加载管理器单例
        DynamicClassLoader dynamicClassLoader = SingletonHolder.getInstance(DynamicClassLoader.class);
        //判断是否已加载，无则返回
        if (!dynamicClassLoader.existsJar(key))
            BaseResponse.SUCCESS.ofI18n("gemini.unload.notfound", key);
        String jarFilePath = dynamicClassLoader.getJarPath(key);
        //执行卸载JAR包
        unloadJar(dynamicClassLoader, plugin, key, jarFilePath);
        return BaseResponse.SUCCESS.ofI18n("gemini.unload.success", key, jarFilePath);
    }

    public static void loadJar(JSONObject plugin, File jarFile, String key) {
        log.info("云类型({}),加载JAR:{}", key, jarFile.getPath());
        try {
            //获取类加载管理器单例
            DynamicClassLoader dynamicClassLoader = SingletonHolder.getInstance(DynamicClassLoader.class);
            //加载JAR包
            URLClassLoader classLoader = dynamicClassLoader.loadJar(jarFile.getPath(), key);
            //加载模板
            loadPluginInterface(key, classLoader);
        } catch (Exception e) {
            log.error("无法加载jar包" + plugin.toJSONString(), e);
            throw new BaseException(BaseResponse.ERROR_SYS.ofI18n("gemini.load.fail.jar"), e, plugin.toJSONString());
        }
    }

    public static void unloadJar(DynamicClassLoader dynamicClassLoader, JSONObject plugin, String key, String jarFilePath) {
        log.info("云类型({}),卸载JAR:{}", key, jarFilePath);
        try {
            //卸载JAR类加载器
            dynamicClassLoader.unloadJar(key);
            //卸载模板
            PluginInterface remove = plugins.remove(key);
            log.info("云类型({}),卸载Plugin:{}", key, remove.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("无法卸载jar包" + plugin.toJSONString(), e);
            throw new BaseException(BaseResponse.ERROR_SYS.ofI18n("gemini.unload.fail.jar"), e, plugin.toJSONString());
        }
    }

    public static void loadPluginInterface(String key, ClassLoader classLoader) {
        ServiceLoader<PluginInterface> serviceLoader = ServiceLoader.load(PluginInterface.class, classLoader);
        if (!serviceLoader.iterator().hasNext()) {
            log.error("未检测到JAR包里模板信息!请确保JAR有继承PluginInterface接口");
            throw new BaseException(BaseResponse.ERROR_BIZ.ofI18n("gemini.load.notfound.template"));
        }
        int count = 0;
        for (PluginInterface plugin : serviceLoader) {
            if (plugin instanceof PluginInterface) {
                //避免多版本冲突
                String cloudKey = (count++ > 0) ? key + "_" + count : key;
                plugins.put(cloudKey, plugin);
                plugin.init(cloudKey);
                log.info("云类型({}),加载Template:{}", cloudKey, plugin.getClass().getSimpleName());
            }
        }
    }

    public static String getPluginKey(JSONObject plugin) {
        String cloudType = plugin.getString("realm");
        String version = plugin.getString("version");
        String key = StrUtil.join(CONJUNCTION, cloudType, version);
        return key;
    }

    public static PluginInterface<?> getPlugin(String realm, String version) {
        PluginInterface<?> template;
        //无认证对象
        if (null != (template = getPluginByKeys(realm, version))) return template;
        if (null != (template = getPluginByKeys(realm))) return template;
        return null;
    }

    public static PluginInterface<?> getPluginByKeys(String... keys) {
        String key = StrUtil.join(CONJUNCTION, keys);
        return plugins.get(key);
    }

    /**
     * 处理sse数据
     * @param result
     * @return
     */
    public static SseEmitter onsse(BaseDataResponse result,ExecutorService executor,SseEmitter emitter) {
        try {
            executor.execute(() -> {
                JSONObject data = (JSONObject)result.getData();
                String url = data.getString("url");
                String token = data.getString("token");
                String json = data.getString("json");
                CloseableHttpClient httpClient = buildHttpClient(url);
                try {

                    HttpPost httpPost = new HttpPost(url);
                    // 设置SSE必需的请求头
                    httpPost.setHeader("Accept", "text/event-stream");
                    httpPost.setHeader("Cache-Control", "no-cache");
                    httpPost.setHeader("Connection", "keep-alive");
                    httpPost.setHeader("Authorization", token);
                    httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
                    HttpResponse response = httpClient.execute(httpPost);
                    InputStream inputStream = response.getEntity().getContent();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                    int i=0;
                    String line = "";
                    while ((line = reader.readLine()) != null) {
                        if (!line.isEmpty()) {
                            emitter.send(SseEmitter.event()
                                    .data(line)
                                    .id(String.valueOf(i))
                                    .reconnectTime(5000L));
                            i++;
                        }
                    }
                    emitter.complete(); // 完成发送
                } catch (Exception e) {
                    emitter.completeWithError(e); // 发送错误
                }finally {
                    try {
                        httpClient.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            });
        }  catch (Exception e) {
            e.printStackTrace();
        }
        return emitter;
    }

    private static CloseableHttpClient buildHttpClient(String url) {
        try {
            URL urlObj = new URL(url);
            if (url.toLowerCase().startsWith("https")) {
                SSLContextBuilder builder = new SSLContextBuilder();
                builder.loadTrustMaterial(null, (X509Certificate[] x509Certificates, String s) -> true);
                SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(builder.build(), new String[]{"TLSv1.1", "TLSv1.2", "SSLv3"}, null, NoopHostnameVerifier.INSTANCE);
                Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", new PlainConnectionSocketFactory())
                        .register("https", socketFactory).build();
                HttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
                CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build();
                return httpClient;
            } else {
                return HttpClientBuilder.create().build();
            }
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.http.fail"), e);
        }
    }

}
