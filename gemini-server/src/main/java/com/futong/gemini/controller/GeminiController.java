package com.futong.gemini.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTJsonUtils;
import com.futong.gemini.common.GeminiFramework;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.plugin.sdk.model.PluginInfo;
import com.futong.gemini.plugin.sdk.service.FileService;
import com.futong.gemini.plugin.sdk.service.TokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Api(tags = "双子星服务")
@RestController
public class GeminiController {

    @PostMapping("/proxy/{realm}/{version}")
    @ApiOperation(value = "双子星代理调用", notes = "双子星代理调用")
    public BaseResponse proxy(@PathVariable("realm") String realm, @PathVariable("version") String version, @RequestBody JSONObject request) {
        log.info("[双子星代理接口]信息为realm:{},version:{},request:{}", realm, version, request);
        try {
            String traceId = UUID.randomUUID().toString().replace("-", "");
            if (request.containsKey("requestId")) {
                traceId = "requestId_" + request.getString("requestId");
            } else if (request.containsKey("batchId")) {
                traceId = "gourd_" + request.getString("batchId");
            }
            MDC.put("TRACE_ID", traceId);
            return GeminiFramework.onProxy(request, realm, version);
        } catch (BaseException e) {
            log.error("内部错误!error:", e);
            return e.response;
        } catch (Exception e) {
            log.error("系统错误!error:", e);
            return BaseResponse.ERROR_SYS.of(e);
        } finally {
            MDC.remove("TRACE_ID");
        }
    }

    @GetMapping("/listRealmVersion")
    @ApiOperation(value = "双子星加载域及版本信息", notes = "双子星加载域及版本信息")
    public BaseResponse listRealmVersion() {
        log.info("调用请求获取双子星加载域及版本信息");
        try {
            return new BaseDataResponse<>().withData(GeminiFramework.plugins.keySet());
        } catch (BaseException e) {
            log.error("内部错误!error:", e);
            return e.response;
        } catch (Exception e) {
            log.error("系统错误!error:", e);
            return BaseResponse.ERROR_SYS.of(e);
        }
    }

    @PostMapping("/load")
    @ApiOperation(value = "双子星加载插件", notes = "双子星加载插件")
    public BaseResponse load(@RequestBody JSONObject request) {
        log.info("request:" + request);
        try {
            return GeminiFramework.loadPluginInterface(request);
        } catch (BaseException e) {
            log.error("内部错误!error:", e);
            return e.response;
        } catch (Exception e) {
            log.error("系统错误!error:", e);
            return BaseResponse.ERROR_SYS.of(e);
        }
    }

    @PostMapping("/unload")
    @ApiOperation(value = "双子星卸载插件", notes = "双子星卸载插件")
    public BaseResponse unload(@RequestBody JSONObject request) {
        log.info("request:" + request);
        try {
            return GeminiFramework.unloadPlugin(request);
        } catch (BaseException e) {
            log.error("内部错误!error:", e);
            return e.response;
        } catch (Exception e) {
            log.error("系统错误!error:", e);
            return BaseResponse.ERROR_SYS.of(e);
        }
    }

    @PostMapping("/upload")
    @ApiOperation(value = "上传文件", notes = "上传文件")
    public BaseResponse upload(String request, MultipartFile[] files) {
        log.info("request:" + request + ";files" + Arrays.toString(files));
        try {
            JSONObject target = JSONObject.parseObject(request);
            //检查token信息,并获取原请求信息
            JSONObject source = TokenService.checkToken(target);
            //合并当前信息至原信息，相同替换
            FTJsonUtils.deepMerge(source, target);
            log.info("请求信息合并后为:{}", source.toJSONString());
//            FileService.set(files);
            if (!source.containsKey("plugin")) {
                return BaseResponse.ERROR.of("未检测到插件信息");
            }
            //文件信息缓存至当前线程
            FileService.set(files);
            PluginInfo pluginInfo = source.getObject("plugin", PluginInfo.class);
            return proxy(pluginInfo.getRealm(), pluginInfo.getVersion(), source);
        } finally {
            FileService.remove();
        }
    }

    @PostMapping("/download")
    @ApiOperation(value = "下载文件", notes = "下载文件")
    public BaseResponse download(@RequestBody JSONObject request) {
        //检查token信息,并获取原请求信息
        JSONObject source = TokenService.checkToken(request);
        //合并当前信息至原信息，相同替换
        FTJsonUtils.deepMerge(source, request);
        log.info("请求信息合并后为:{}", source.toJSONString());
        if (!source.containsKey("plugin")) {
            return BaseResponse.ERROR.of("未检测到插件信息");
        }
        //文件信息缓存至当前线程
        PluginInfo pluginInfo = source.getObject("plugin", PluginInfo.class);
        return proxy(pluginInfo.getRealm(), pluginInfo.getVersion(), source);
    }

    @GetMapping("/test")
    public BaseResponse test() {
        BasePageSortSearchRequest request = new BasePageSortSearchRequest();
        request.setCurrent(1);
        request.setSize(10000);
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> response = ApiFactory.Api.res.listInstanceDisk(request);
        System.out.println(JSON.toJSONString(response));
        return BaseResponse.SUCCESS;
    }

    @PostMapping(path = "/sse/{realm}/{version}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter handlePostSse(@PathVariable("realm") String realm, @PathVariable("version") String version, @RequestBody JSONObject request, HttpServletResponse response) {
        response.setHeader("Accept-Encoding", "identity");
        String traceId = UUID.randomUUID().toString().replace("-", "");
        if (request.containsKey("requestId")) {
            traceId = "requestId_" + request.getString("requestId");
        } else if (request.containsKey("batchId")) {
            traceId = "gourd_" + request.getString("batchId");
        }
        MDC.put("TRACE_ID", traceId);
        BaseDataResponse result = (BaseDataResponse) GeminiFramework.onProxy(request, realm, version);
        SseEmitter emitter = new SseEmitter(0L); // 0表示不超时
        ExecutorService executor = Executors.newCachedThreadPool();
        GeminiFramework.onsse(result, executor, emitter);
        return emitter;
    }

}
