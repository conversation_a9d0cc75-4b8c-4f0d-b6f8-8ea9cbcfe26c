# 基于java:8镜像进行扩展
FROM harbor.futongcloud.com.cn:15005/basic/jre:8.202
#定义一个构建参数并设置默认值
ARG BUILD_TIME
# 将构建參数的值赋给环境变量
ENV BUILD_TIME=${BUILD_TIME}
# 指定制作该镜像的作者和联系方式
MAINTAINER lipengpeng

# 指定容器内进程对外开放的端口
EXPOSE 50014

# 创建配置文件和日志文件需要的文件夹
RUN mkdir -p /etc/ftcloud/gemini /var/log/gemini /etc/ftcloud/public/database_config

# 将maven打成的jar包复制到镜像目录中（相对路径---默认"."就是Dockerfile所在目录）
ADD futong-public-gemini.jar futong-public-gemini.jar

# 指定容器启动时，需要执行的命令(运行该jar程序)
ENTRYPOINT java -server \
-Dfile.encoding=UTF-8 \
-Djasypt.encryptor.password=futong \
-Duser.timezone=GMT+08 \
-Xms256m \
-XX:NewSize=256m \
-Xmn1g \
-XX:+UseG1GC \
-Xmx1g \
#-XX:+PrintGCDetails \
#-XX:+PrintGCTimeStamps \
#-XX:+PrintGCDateStamps \
#-Xloggc:/var/log/obs/gc.$$.log \
#-XX:+HeapDumpOnOutOfMemoryError \
#-XX:HeapDumpPath=/var/log/obs/gc.hprof \
-jar futong-public-gemini.jar \
--spring.profiles.active=data \
--spring.config.location=/etc/ftcloud/gemini/bootstrap.yml \
--logging.config=/etc/ftcloud/gemini/logback-spring.xml \
