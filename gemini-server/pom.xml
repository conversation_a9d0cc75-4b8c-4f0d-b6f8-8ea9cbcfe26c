<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.futong.gemini</groupId>
        <artifactId>gemini</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>gemini-server</artifactId>
    <properties>
        <output.directory>/work/futong-cm-standard/futong-gemini-maven-jar-406D</output.directory>
        <cxf.version>3.5.5</cxf.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.futong.gemini</groupId>
            <artifactId>01-plugin-sdk</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>2.4.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.battcn</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- amqp-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!-- redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!--ES-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <!-- Apache CXF Spring Boot Starter for JAX-WS (SOAP) -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.5.5</version> <!-- 请检查并使用最新稳定版本 -->
        </dependency>

        <!--minio-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>7.1.4</version>
        </dependency>
        <!--数据库连接-->
        <!--多数据源-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${baomidou-dynamic-datasource.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>${baomidou-mybatis-plus.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--mybatisplus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${baomidou-mybatis-plus.version}</version>
        </dependency>
        <!--数据库连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${alibaba-druid.version}</version>
        </dependency>
        <!--mysql连接驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.22</version>
        </dependency>

    </dependencies>
    <profiles>
        <profile>
            <id>cloud_ali</id>
            <properties>
                <project>cloud_ali</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>g01-cloud-ali</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_tencent</id>
            <properties>
                <project>cloud_tencent</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>g02-cloud-tencent</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_cloudos_e7</id>
            <properties>
                <project>cloud_cloudos_e7</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s01-cloud-cloudos-e7</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>biz_dth_dws</id>
            <properties>
                <project>biz_dth_dws</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>02-biz-dth-dws</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>biz_dth_dws_atlas</id>
            <properties>
                <project>biz_dth_dws_atlas</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>02-biz-dth-dws-atlas</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>product_aofei</id>
            <properties>
                <project>product_aofei</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>01-product-aofei</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_vmware</id>
            <properties>
                <project>cloud_vmware</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>v01-cloud-vmware</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_cloudos</id>
            <properties>
                <project>cloud_cloudos</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s01-cloud-cloudos-e5</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_cas</id>
            <properties>
                <project>cloud_cas</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>v02-cloud-cas-e7</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_uis</id>
            <properties>
                <project>cloud_uis</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>h01-cloud-uis</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_scp</id>
            <properties>
                <project>cloud_scp</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s03-cloud-scp-68</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_scp_610</id>
            <properties>
                <project>cloud_scp_610</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s03-cloud-scp-610</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_manageone</id>
            <properties>
                <project>cloud_manageone</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s05-cloud-manageOne</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_baidu</id>
            <properties>
                <project>cloud_baidu</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s07-cloud-baidu</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_fusioncompute</id>
            <properties>
                <project>cloud_fusioncompute</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>v03-cloud-fusionCompute-v8.0</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_harmony</id>
            <properties>
                <project>cloud_harmony</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>c01-cloud-harmony</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_ali_316</id>
            <properties>
                <project>cloud_ali_316</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s08-cloud-ali-316</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_nutanix</id>
            <properties>
                <project>cloud_nutanix</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>h02-cloud-nutanix</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_ucloud</id>
            <properties>
                <project>cloud_ucloud</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>s06-cloud-UCloud</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>cloud_cce</id>
            <properties>
                <project>cloud_cce</project>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.futong.gemini</groupId>
                    <artifactId>c02-cloud-cce</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
    <build>
        <finalName>futong-public-gemini</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--<fork>true</fork> &lt;!&ndash; 如果没有该配置，devtools不会生效 &ndash;&gt;-->
                    <outputDirectory>${output.directory}</outputDirectory>
                    <includeSystemScope>true</includeSystemScope><!--外部进行打包-->
                </configuration>
            </plugin>
            <!--使单元测试不影响项目的编译-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip><!--跳过单元测试-->
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
